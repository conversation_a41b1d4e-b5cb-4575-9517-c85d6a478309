﻿// Decompiled with JetBrains decompiler
// Type: HDSplash.Properties.Settings
// Assembly: HDSplash, Version=1.0.1.0, Culture=neutral, PublicKeyToken=null
// MVID: 57BF8E78-0319-4ECF-A8F5-B637E39756A4
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDSplash.dll

using System.CodeDom.Compiler;
using System.Configuration;
using System.Runtime.CompilerServices;

namespace HDSplash.Properties
{
  [CompilerGenerated]
  [GeneratedCode("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "14.0.0.0")]
  internal sealed class Settings : ApplicationSettingsBase
  {
    private static Settings defaultInstance = (Settings) SettingsBase.Synchronized((SettingsBase) new Settings());

    public static Settings Default => Settings.defaultInstance;
  }
}
