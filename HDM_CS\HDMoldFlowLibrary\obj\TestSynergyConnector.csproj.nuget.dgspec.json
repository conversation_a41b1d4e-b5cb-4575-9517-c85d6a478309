{"format": 1, "restore": {"c:\\Moldflow\\HDM_CS\\HDMoldFlowLibrary\\TestSynergyConnector.csproj": {}}, "projects": {"c:\\Moldflow\\HDM_CS\\HDMoldFlowLibrary\\TestSynergyConnector.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "c:\\Moldflow\\HDM_CS\\HDMoldFlowLibrary\\TestSynergyConnector.csproj", "projectName": "TestSynergyConnector", "projectPath": "c:\\Moldflow\\HDM_CS\\HDMoldFlowLibrary\\TestSynergyConnector.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "c:\\Moldflow\\HDM_CS\\HDMoldFlowLibrary\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.204\\RuntimeIdentifierGraph.json"}}}}}