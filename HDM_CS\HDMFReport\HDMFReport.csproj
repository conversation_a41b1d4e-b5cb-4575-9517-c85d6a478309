﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!--Project was exported from assembly: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll-->
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{B43243CA-9F4C-4E80-92A8-E53557A443DC}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AssemblyName>HDMFReport</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <ApplicationVersion>1.0.0.0</ApplicationVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ArSetting.Net4">
      <HintPath>lib\ArSetting.Net4.dll</HintPath>
    </Reference>
    <Reference Include="HDLocale">
      <HintPath>lib\HDLocale.dll</HintPath>
    </Reference>
    <Reference Include="HDLog4Net">
      <HintPath>lib\HDLog4Net.dll</HintPath>
    </Reference>
    <Reference Include="HDMFUserControl">
      <HintPath>lib\HDMFUserControl.dll</HintPath>
    </Reference>
    <Reference Include="HDMoldFlowLibrary">
      <HintPath>lib\HDMoldFlowLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Newtonsoft.Json">
      <HintPath>lib\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Base\clsBase.cs" />
    <Compile Include="Changsung\clsChangsung.cs" />
    <Compile Include="Changsung\clsChangsungData.cs" />
    <Compile Include="clsInjCondTable.cs" />
    <Compile Include="clsReport.cs" />
    <Compile Include="clsReportData.cs" />
    <Compile Include="CMPRData.cs" />
    <Compile Include="clsReportDefine.cs" />
    <Compile Include="Base\frmBase.cs" />
    <Compile Include="Kumnung\clsKumnungData.cs" />
    <Compile Include="Kumnung\clsKumnung.cs" />
    <Compile Include="HDSolutions\clsHDSolutions_CMPR.cs" />
    <Compile Include="HDSolutions\clsHDSolutions.cs" />
    <Compile Include="clsReportUtill.cs" />
    <Compile Include="HDSolutions\clsHDSolutionData.cs" />
    <Compile Include="HDSolutions\frmHDSolutions.cs" />
    <Compile Include="frmReportSetting.cs" />
    <Compile Include="Hyundai\clsHyundai_CMPR.cs" />
    <Compile Include="Hyundai\clsHyundai.cs" />
    <Compile Include="Hyundai\clsHyundaiData.cs" />
    <Compile Include="Hyundai\frmHyundai.cs" />
    <Compile Include="SL\clsSL.cs" />
    <Compile Include="SL\clsSLData.cs" />
    <Compile Include="SL\frmSL.cs" />
    <Compile Include="Properties\Resources.Designer.cs" />
    <Compile Include="Properties\Settings.Designer.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\AppEvents.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\AppEvents_Event.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\Application.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\DocEvents.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\DocEvents_Event.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\Range.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\Shape.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\Shapes.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\Sheets.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\Workbook.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\WorkbookEvents.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\WorkbookEvents_Event.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\Workbooks.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\Worksheet.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\XlCopyPictureFormat.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\XlFixedFormatType.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\XlPictureAppearance.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\_Application.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\_Workbook.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\_Worksheet.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\Application.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\Cell.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\CellRange.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\Collection.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\ColorFormat.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\EApplication.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\EApplication_Event.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\Font.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\ParagraphFormat.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\PpPlaceholderType.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\PpSaveAsFileType.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\PresEvents.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\PresEvents_Event.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\Presentation.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\Presentations.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\Row.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\Rows.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\Shape.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\Shapes.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\SldEvents.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\SldEvents_Event.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\Slide.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\Slides.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\Table.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\TextFrame.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\TextRange.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\_Application.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\_Presentation.cs" />
    <Compile Include="Microsoft\Office\Interop\PowerPoint\_Slide.cs" />
    <Compile Include="Microsoft\Office\Core\MsoShapeType.cs" />
    <Compile Include="Microsoft\Office\Core\MsoTextOrientation.cs" />
    <Compile Include="Microsoft\Office\Core\MsoTriState.cs" />
    <Compile Include="Microsoft\Office\Core\MsoZOrderCmd.cs" />
    <Compile Include="AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="HDMFReport\frmBase.resx" />
    <EmbeddedResource Include="HDMFReport\frmHDSolutions.resx" />
    <EmbeddedResource Include="HDMFReport\frmHyundai.resx" />
    <EmbeddedResource Include="HDMFReport\frmReportSetting.resx" />
    <EmbeddedResource Include="HDMFReport\frmSL.resx" />
    <EmbeddedResource Include="HDMFReport\Properties\Resources.resx" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>