﻿// Decompiled with JetBrains decompiler
// Type: HDMFReport.frmHDSolutions
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using HDLocale;
using HDLog4Net;
using HDMFReport.Properties;
using HDMFUserControl;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace HDMFReport
{
  internal class frmHDSolutions : frmBase
  {
    private FileInfo m_fiReportUser;
    private FileInfo m_fiReportSystem = new FileInfo(clsReportDefine.g_diCfg.FullName + "\\HDSolutions.ini");
    private IContainer components;
    private TabControl tabControl_Main;
    private TabPage tabPage_Input;
    private TabPage tabPage_View;
    private NewButton newButton_View;
    private NewButton newButton_Input;
    private Panel panel1;
    private NewComboBox newComboBox_Input_DefZ;
    private NewComboBox newComboBox_Input_DefY;
    private NewComboBox newComboBox_Input_DefX;
    private NewComboBox newComboBox_Input_DefAll;
    private NewComboBox newComboBox_Input_Cooling;
    private NewComboBox newComboBox_Input_ShrinkageSink;
    private NewComboBox newComboBox_Input_AirTrap;
    private NewComboBox newComboBox_Input_WeldLine;
    private Label label29;
    private Label label28;
    private Label label32;
    private Label label26;
    private Label label27;
    private NewTextBox newTextBox_Input_Schizonepeta;
    private NewTextBox newTextBox_Input_Manager;
    private NewTextBox newTextBox_Input_CoolingTime;
    private NewTextBox newTextBox_Input_Engineer;
    private NewTextBox newTextBox_Input_PackingTime;
    private NewTextBox newTextBox_Input_Sequence;
    private NewTextBox newTextBox_Input_Countermeasure;
    private NewTextBox newTextBox_Input_DefZ;
    private NewTextBox newTextBox_Input_DefY;
    private NewTextBox newTextBox_Input_DefX;
    private NewTextBox newTextBox_Input_DefAll;
    private NewTextBox newTextBox_Input_Cooling;
    private NewTextBox newTextBox_Input_ShrinkageSink;
    private NewTextBox newTextBox_Input_AirTrap;
    private NewTextBox newTextBox_Input_WeldLine;
    private NewTextBox newTextBox_Input_FllingTime;
    private NewTextBox newTextBox_Input_Item;
    private Label label_Input_InjRangeVP;
    private Label label_Input_InjRange4;
    private Label label_Input_InjRange3;
    private Label label_Input_MoldOpen;
    private Label label10;
    private Label label35;
    private Label label11;
    private Label label_Input_InjRange2;
    private Label label34;
    private Label label53;
    private Label label38;
    private Label label43;
    private Label label_Input_Filling8;
    private Label label_Input_Frozen4;
    private Label label_Input_Filling4;
    private Label label_Input_Filling7;
    private Label label_Input_Frozen3;
    private Label label_Input_Filling3;
    private Label label_Input_Filling6;
    private Label label_Input_Frozen2;
    private Label label_Input_Filling2;
    private Label label_Input_Filling5;
    private Label label_Input_Frozen1;
    private Label label_Input_Filling1;
    private Label label_Input_ShrinkageMax;
    private Label label67;
    private Label label66;
    private Label label64;
    private Label label60;
    private Label label_Input_SurfaceMaxTemp;
    private Label label_Input_ShrinkageMin;
    private Label label_Input_SurfaceMinTemp;
    private Label label33;
    private Label label13;
    private Label label14;
    private Label label_Input_InjRange1;
    private UnitTextBox unitTextBox_Input_InjPreRatio;
    private UnitTextBox unitTextBox_Input_InjMaxClamp;
    private UnitTextBox unitTextBox_Input_InjMaxPressure;
    private UnitTextBox unitTextBox_Input_InjRangeVP;
    private UnitTextBox unitTextBox_Input_InjRange4;
    private UnitTextBox unitTextBox_Input_InjRange3;
    private UnitTextBox unitTextBox_Input_InjScrewDia;
    private UnitTextBox unitTextBox_Input_InjRange2;
    private UnitTextBox unitTextBox_Input_Filling8;
    private UnitTextBox unitTextBox_Input_InjMaxRate;
    private UnitTextBox unitTextBox_Input_Filling7;
    private UnitTextBox unitTextBox_Input_Frozen4;
    private UnitTextBox unitTextBox_Input_Filling4;
    private UnitTextBox unitTextBox_Input_Filling6;
    private UnitTextBox unitTextBox_Input_Frozen3;
    private UnitTextBox unitTextBox_Input_ShrinkageMax;
    private UnitTextBox unitTextBox_Input_Filling3;
    private UnitTextBox unitTextBox_Input_ShrinkageMin;
    private UnitTextBox unitTextBox_Input_MoldTempMax;
    private UnitTextBox unitTextBox_Input_MoldTempMin;
    private UnitTextBox unitTextBox_Input_Filling5;
    private UnitTextBox unitTextBox_Input_Frozen2;
    private UnitTextBox unitTextBox_Input_Filling2;
    private UnitTextBox unitTextBox_Input_Frozen1;
    private UnitTextBox unitTextBox_Input_Filling1;
    private UnitTextBox unitTextBox_Input_InjRange1;
    private UnitTextBox unitTextBox_Input_InjMaxStroke;
    private Label label_Input_InjPreRatio;
    private Label label_Input_InjScrewDia;
    private Label label_Input_InjMaxClamp;
    private Label label_Input_InjMaxRate;
    private Label label_Input_InjMaxPressure;
    private Label label_Input_InjMaxStroke;
    private NewComboBox newComboBox_Input_InjData;
    private Label label_Input_Deflection;
    private Label label_Input_SurfShrinkage;
    private Label label48;
    private Label label37;
    private Label label30;
    private Label label_Input_Result_Comment;
    private Label label_Input_Sign;
    private Label label_Input_Range;
    private Label label_Input_Inj;
    private NewButton newButton_View_Select;
    private NewButton newButton_View_All;
    private NewTextBox newTextBox_View_Z;
    private NewTextBox newTextBox_View_Y;
    private NewTextBox newTextBox_View_X;
    private NewComboBox newComboBox_View_Type;
    private Label label2;
    private Label label3;
    private Label label4;
    private Label label5;
    private Label label_View_Model;
    private DataGridView dataGridView_View;
    private Label label_View_Item;
    private NewTextBox newTextBox_Input_FillingFrame;
    private NewButton newButton_Apply;
    private NewComboBox newComboBox_Input_Balance;
    private Label label31;
    private NewTextBox newTextBox_Input_Balance;
    private CheckBox checkBox_Input_InjCond;
    private Label label61;
    private Label label_Input_ShrinkageOffset1;
    private Label label_Input_SinkMarkMin;
    private Label label_Input_ScaleValue;
    private Label label_Input_ShrinkageOffset2;
    private NewTextBox newTextBox_Input_ShrinkageOffset1;
    private NewTextBox newTextBox_Input_ShrinkageOffset2;
    private NewTextBox newTextBox_Input_DefScale;
    private NewTextBox newTextBox_Input_SinkMarkMin;
    private Label label_Input_DefScale;
    private NewTextBox newTextBox_Input_SinkMarkMax;
    private Label label_Input_SinkMarkMax;
    private Panel panel2;
    private RadioButton radioButton_MoldData;
    private RadioButton radioButton_ProductData;
    private Panel panel3;
    private CheckBox checkBox_AllCheck;
    private DataGridViewTextBoxColumn Column6;
    private DataGridViewCheckBoxColumn Column5;
    private DataGridViewTextBoxColumn Column_Item;
    private DataGridViewTextBoxColumn Column2;
    private DataGridViewTextBoxColumn Column3;
    private DataGridViewTextBoxColumn Column4;
    private DataGridViewTextBoxColumn Column_SlideID;

    protected override CreateParams CreateParams
    {
      get
      {
        CreateParams createParams = base.CreateParams;
        createParams.ExStyle |= 33554432;
        return createParams;
      }
    }

    public frmHDSolutions()
    {
      this.InitializeComponent();
      this.newButton_Apply.Image = clsReportUtill.ChangeSyncBackgroundImage(this.newButton_Apply.Image);
      this.label_Input_Inj.Text = LocaleControl.getInstance().GetString("IDS_INJ_INFO");
      this.label_Input_InjMaxStroke.Text = LocaleControl.getInstance().GetString("IDS_INJ_MAXSTROKE");
      this.label_Input_InjMaxRate.Text = LocaleControl.getInstance().GetString("IDS_INJ_MAXRATE");
      this.label_Input_InjScrewDia.Text = LocaleControl.getInstance().GetString("IDS_INJ_SCREWDIA");
      this.label_Input_InjMaxPressure.Text = LocaleControl.getInstance().GetString("IDS_INJ_MAXPRESSURE");
      this.label_Input_InjMaxClamp.Text = LocaleControl.getInstance().GetString("IDS_INJ_MAXCLAMP");
      this.label_Input_InjPreRatio.Text = LocaleControl.getInstance().GetString("IDS_INJ_PRERATIO");
      this.label_Input_Range.Text = LocaleControl.getInstance().GetString("IDS_INJ_RANGE");
      this.label_Input_InjRange1.Text = "1" + LocaleControl.getInstance().GetString("IDS_STEP");
      this.label_Input_InjRange2.Text = "2" + LocaleControl.getInstance().GetString("IDS_STEP");
      this.label_Input_InjRange3.Text = "3" + LocaleControl.getInstance().GetString("IDS_STEP");
      this.label_Input_InjRange4.Text = "4" + LocaleControl.getInstance().GetString("IDS_STEP") + "(" + LocaleControl.getInstance().GetString("IDS_CHANGE_VP") + ")";
      this.label_Input_InjRangeVP.Text = LocaleControl.getInstance().GetString("IDS_VP_CUSHION") + "(Cushion)";
      this.radioButton_ProductData.Text = LocaleControl.getInstance().GetString("IDS_PRODUCT_DATA");
      this.radioButton_MoldData.Text = LocaleControl.getInstance().GetString("IDS_MOLD_DATA");
      this.label_Input_Sign.Text = LocaleControl.getInstance().GetString("IDS_SIGN");
      this.label_Input_Result_Comment.Text = LocaleControl.getInstance().GetString("IDS_RESULT_COMMENT");
      this.label_Input_MoldOpen.Text = LocaleControl.getInstance().GetString("IDS_MOLD_OPEN") + "(S)";
      this.label_Input_Filling1.Text = "1" + LocaleControl.getInstance().GetString("IDS_PAGE");
      this.label_Input_Filling2.Text = "2" + LocaleControl.getInstance().GetString("IDS_PAGE");
      this.label_Input_Filling3.Text = "3" + LocaleControl.getInstance().GetString("IDS_PAGE");
      this.label_Input_Filling4.Text = "4" + LocaleControl.getInstance().GetString("IDS_PAGE");
      this.label_Input_Filling5.Text = "5" + LocaleControl.getInstance().GetString("IDS_PAGE");
      this.label_Input_Filling6.Text = "6" + LocaleControl.getInstance().GetString("IDS_PAGE");
      this.label_Input_Filling7.Text = "7" + LocaleControl.getInstance().GetString("IDS_PAGE");
      this.label_Input_Filling8.Text = "8" + LocaleControl.getInstance().GetString("IDS_PAGE");
      this.label_Input_Frozen1.Text = "1" + LocaleControl.getInstance().GetString("IDS_PAGE");
      this.label_Input_Frozen2.Text = "2" + LocaleControl.getInstance().GetString("IDS_PAGE");
      this.label_Input_Frozen3.Text = "3" + LocaleControl.getInstance().GetString("IDS_PAGE");
      this.label_Input_Frozen4.Text = "4" + LocaleControl.getInstance().GetString("IDS_PAGE");
      this.label_Input_SurfShrinkage.Text = LocaleControl.getInstance().GetString("IDS_SURF_SHRINKAGE");
      this.label_Input_SurfaceMinTemp.Text = LocaleControl.getInstance().GetString("IDS_SURF_MIN_TEMP");
      this.label_Input_SurfaceMaxTemp.Text = LocaleControl.getInstance().GetString("IDS_SURF_MAX_TEMP");
      this.label_Input_ShrinkageMin.Text = LocaleControl.getInstance().GetString("IDS_SHRINKAGE_MIN");
      this.label_Input_ShrinkageMax.Text = LocaleControl.getInstance().GetString("IDS_SHRINKAGE_MAX");
      this.label_Input_SinkMarkMin.Text = "SinkMark " + LocaleControl.getInstance().GetString("IDS_MIN");
      this.label_Input_SinkMarkMax.Text = "SinkMark " + LocaleControl.getInstance().GetString("IDS_MAX");
      this.label_Input_DefScale.Text = "Deflection " + LocaleControl.getInstance().GetString("IDS_OFFSET");
      this.label_Input_ShrinkageOffset1.Text = LocaleControl.getInstance().GetString("IDS_SHRINKAGE_OFFSET") + " - 1";
      this.label_Input_ShrinkageOffset2.Text = LocaleControl.getInstance().GetString("IDS_SHRINKAGE_OFFSET") + " - 2";
      this.label_Input_Deflection.Text = LocaleControl.getInstance().GetString("IDS_WARP");
      this.checkBox_Input_InjCond.Text = LocaleControl.getInstance().GetString("IDS_INJ_CONDITION");
      this.newButton_Input.ButtonText = LocaleControl.getInstance().GetString("IDS_INPUT_VALUE");
      this.newButton_View.ButtonText = LocaleControl.getInstance().GetString("IDS_VIEW_SETTING");
      this.label_View_Model.Text = LocaleControl.getInstance().GetString("IDS_MODEL_VIEW_ROTATE_ANGLE");
      this.label_View_Item.Text = LocaleControl.getInstance().GetString("IDS_ITEM_VIEW_ROTATE_ANGLE");
      this.newButton_View_All.ButtonText = LocaleControl.getInstance().GetString("IDS_APPLY_ALL");
      this.newButton_View_Select.ButtonText = LocaleControl.getInstance().GetString("IDS_APPLY_SELECT");
      this.Column_Item.HeaderText = LocaleControl.getInstance().GetString("IDS_ITEM");
    }

    private void frmHDSolutions_Load(object sender, EventArgs e)
    {
      this.Text = LocaleControl.getInstance().GetString("IDS_WRITE_REPORT") + " [" + LocaleControl.getInstance().GetString("IDS_STUDY") + ": " + this.m_drStudy["Name"].ToString() + "]";
      this.m_fiReportSystem = new FileInfo(clsReportDefine.g_diCfg.FullName + "\\" + this.m_enumCompany.ToString() + ".ini");
      this.SetDefault();
      this.SetLoad();
      if (clsReportDefine.enumLicLevel != clsReportDefine.LicLevel.Standard)
        return;
      this.checkBox_Input_InjCond.Checked = false;
      this.newComboBox_Input_InjData.Enabled = false;
      this.unitTextBox_Input_InjMaxClamp.Enabled = false;
      this.unitTextBox_Input_InjMaxPressure.Enabled = false;
      this.unitTextBox_Input_InjMaxRate.Enabled = false;
      this.unitTextBox_Input_InjMaxStroke.Enabled = false;
      this.unitTextBox_Input_InjPreRatio.Enabled = false;
      this.unitTextBox_Input_InjRange1.Enabled = false;
      this.unitTextBox_Input_InjRange2.Enabled = false;
      this.unitTextBox_Input_InjRange3.Enabled = false;
      this.unitTextBox_Input_InjRange4.Enabled = false;
      this.unitTextBox_Input_InjRangeVP.Enabled = false;
      this.unitTextBox_Input_InjScrewDia.Enabled = false;
    }

    private void SetDefault()
    {
      string empty = string.Empty;
      try
      {
        if (clsReportDefine.enumLicLevel < clsReportDefine.LicLevel.Premium)
          this.checkBox_Input_InjCond.Enabled = false;
        this.checkBox_Input_InjCond.Checked = false;
        this.newComboBox_Input_InjData.Items.Add((object) LocaleControl.getInstance().GetString("IDS_STUDY"));
        foreach (DataRow dataRow in clsReportDefine.g_dtInjectionDB.AsEnumerable())
          this.newComboBox_Input_InjData.Items.Add((object) dataRow["FileName"].ToString());
        this.newComboBox_Input_InjData.SelectedIndex = 0;
        this.radioButton_ProductData.Checked = true;
        this.unitTextBox_Input_InjRange1.Value = "5";
        this.unitTextBox_Input_InjRange2.Value = "25";
        this.unitTextBox_Input_InjRange3.Value = "80";
        this.unitTextBox_Input_InjRange4.Value = "99";
        this.unitTextBox_Input_InjRangeVP.Value = "7";
        string str = this.m_drStudy.Table.TableName;
        if (this.m_drStudy.Table.TableName.ToLower().Contains("past"))
          str = ((IEnumerable<string>) this.m_drStudy["Name"].ToString().Split('_')).Where<string>((System.Func<string, bool>) (Temp => Temp.ToLower().Contains("past"))).FirstOrDefault<string>() + "_Case";
        this.newTextBox_Input_Item.Value = str;
        this.newTextBox_Input_Sequence.Value = this.m_drStudy["Sequence"].ToString();
        this.newTextBox_Input_Engineer.Value = "";
        this.newTextBox_Input_Manager.Value = "";
        this.newComboBox_Input_WeldLine.Items.AddRange((object[]) new string[3]
        {
          "OK",
          "Discussion",
          "NG"
        });
        this.newComboBox_Input_WeldLine.SelectedIndex = 1;
        this.newComboBox_Input_AirTrap.Items.AddRange((object[]) new string[3]
        {
          "OK",
          "Discussion",
          "NG"
        });
        this.newComboBox_Input_AirTrap.SelectedIndex = 1;
        this.newComboBox_Input_ShrinkageSink.Items.AddRange((object[]) new string[3]
        {
          "OK",
          "Discussion",
          "NG"
        });
        this.newComboBox_Input_ShrinkageSink.SelectedIndex = 1;
        this.newComboBox_Input_Cooling.Items.AddRange((object[]) new string[3]
        {
          "OK",
          "Discussion",
          "NG"
        });
        this.newComboBox_Input_Cooling.SelectedIndex = 1;
        this.newComboBox_Input_Balance.Items.AddRange((object[]) new string[3]
        {
          "OK",
          "Discussion",
          "NG"
        });
        this.newComboBox_Input_Balance.SelectedIndex = 1;
        this.newTextBox_Input_Countermeasure.Value = "To improve air trap, you should make a flow leader and increase thickness. + 0.5mm" + Environment.NewLine + "Almost all areas are cooled in 24 seconds" + Environment.NewLine + "Use of Mold inserts to improve deformation area";
        this.newTextBox_Input_FllingTime.Value = "0";
        if (this.m_dicReport != null && this.m_dicReport.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillingTime")))
          this.newTextBox_Input_FllingTime.Value = this.m_dicReport["FillingTime"];
        this.newTextBox_Input_PackingTime.Value = "0";
        if (this.m_dicReport != null && this.m_dicReport.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PackingTime")))
          this.newTextBox_Input_PackingTime.Value = this.m_dicReport["PackingTime"];
        this.newTextBox_Input_CoolingTime.Value = "0";
        if (this.m_dicReport != null && this.m_dicReport.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CoolingTime")))
          this.newTextBox_Input_CoolingTime.Value = this.m_dicReport["CoolingTime"];
        this.newTextBox_Input_Schizonepeta.Value = "0";
        if (this.m_dicReport != null && this.m_dicReport.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Schizonepeta")))
          this.newTextBox_Input_Schizonepeta.Value = this.m_dicReport["Schizonepeta"];
        this.unitTextBox_Input_Filling1.Value = "10";
        this.unitTextBox_Input_Filling2.Value = "30";
        this.unitTextBox_Input_Filling3.Value = "45";
        this.unitTextBox_Input_Filling4.Value = "55";
        this.unitTextBox_Input_Filling5.Value = "70";
        this.unitTextBox_Input_Filling6.Value = "80";
        this.unitTextBox_Input_Filling7.Value = "95";
        this.unitTextBox_Input_Filling8.Value = "100";
        this.newTextBox_Input_FillingFrame.Value = "150";
        this.unitTextBox_Input_Frozen1.Value = "30";
        this.unitTextBox_Input_Frozen2.Value = "60";
        this.unitTextBox_Input_Frozen3.Value = "80";
        this.unitTextBox_Input_Frozen4.Value = "95";
        if (this.m_dicReport != null && this.m_dicReport.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MoldTemperature")))
        {
          string[] strArray = this.m_dicReport["MoldTemperature"].Split('|');
          this.unitTextBox_Input_MoldTempMin.Value = strArray[0];
          this.unitTextBox_Input_MoldTempMax.Value = strArray[1];
        }
        else
        {
          this.unitTextBox_Input_MoldTempMin.Value = "0";
          this.unitTextBox_Input_MoldTempMax.Value = "0";
        }
        if (this.m_dicReport != null && this.m_dicReport.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VolumetricShrinkage")))
        {
          string[] strArray = this.m_dicReport["VolumetricShrinkage"].Split('|');
          this.unitTextBox_Input_ShrinkageMin.Value = strArray[0];
          this.unitTextBox_Input_ShrinkageMax.Value = strArray[1];
        }
        else
        {
          this.unitTextBox_Input_ShrinkageMin.Value = "0";
          this.unitTextBox_Input_ShrinkageMax.Value = "0";
        }
        this.newTextBox_Input_SinkMarkMin.Value = "0.01";
        this.newTextBox_Input_SinkMarkMax.Value = "1";
        this.newTextBox_Input_DefScale.Value = "5";
        this.newTextBox_Input_ShrinkageOffset1.Value = "0";
        this.newTextBox_Input_ShrinkageOffset2.Value = "3";
        this.newComboBox_Input_DefAll.Items.AddRange((object[]) new string[2]
        {
          "Anchor",
          "Best fit"
        });
        this.newComboBox_Input_DefAll.SelectedIndex = 1;
        this.newComboBox_Input_DefX.Items.AddRange((object[]) new string[2]
        {
          "Anchor",
          "Best fit"
        });
        this.newComboBox_Input_DefX.SelectedIndex = 1;
        this.newComboBox_Input_DefY.Items.AddRange((object[]) new string[2]
        {
          "Anchor",
          "Best fit"
        });
        this.newComboBox_Input_DefY.SelectedIndex = 1;
        this.newComboBox_Input_DefZ.Items.AddRange((object[]) new string[2]
        {
          "Anchor",
          "Best fit"
        });
        this.newComboBox_Input_DefZ.SelectedIndex = 1;
        this.newComboBox_View_Type.Items.AddRange((object[]) new string[14]
        {
          "TOP",
          "FRONT",
          "LEFT",
          "RIGHT",
          "BACK",
          "BOTTOM",
          "LEFT-FRONT-BOT",
          "BOT-FRONT-RIGHT",
          "RIGHT-FRONT-TOP",
          "TOP-FRONT-LEFT",
          "LEFT-BACK-TOP",
          "TOP-BACK-RIGHT",
          "RIGHT-BACK-BOT",
          "BOT-BACK-LEFT"
        });
        this.newComboBox_View_Type.SelectedIndex = this.newComboBox_View_Type.Items.IndexOf((object) "RIGHT-FRONT-TOP");
        string[] strArray1 = new string[32]
        {
          "Title|1장 - 표지|Title",
          "P&R|4장 - 제품 & 런너|Product_1",
          "CoolSystem|4장 - 냉각 시스템|Product_1",
          "P&R&C 1|4장 - 제품 & 런너 & 냉각 1|Product_1",
          "P&R&C 2|4장 - 제품 & 런너 & 냉각 2|Product_1",
          "Mesh|6장 - 제품(Mesh)|Mesh_1",
          "Cool|7장 - 냉각|Mesh_2",
          "FillTime|17,18장 - Fill time|FillTime",
          "TemperatureAtFlowFront|19장 - Temperature at flow front|TemperatureAtFlowFront",
          "Pressure|20장 - Pressure|Pressure",
          "ClampForce|21장 - Clamp force|ClampForce",
          "FrozenLayerFraction|22장 - 시간에 따른 고화|FrozenLayerFraction",
          "Shrinkage1|23장 - 수축률1|Shrinkage",
          "Shrinkage2|23장 - 수축률2|Shrinkage",
          "Shrinkage3|23장 - 수축률3|Shrinkage",
          "SinkMark|24장 - Sink Mark|SinkMark",
          "WeldLine|25장 - Weld Line|WeldLine",
          "AirTrap|26장 - Air Traps|AirTrap",
          "CircuitCoolantTemp|28장 - 냉각 수 온도|CircuitCoolantTemp",
          "MoldTemp1|29장 - 금형 표면 온도1|MoldTemp",
          "MoldTemp2|29장 - 금형 표면 온도2|MoldTemp",
          "MoldTemp3|29장 - 금형 표면 온도3|MoldTemp",
          "MoldTemp4|29장 - 금형 표면 온도4|MoldTemp",
          "MoldTemp5|29장 - 금형 표면 온도5|MoldTemp",
          "TimeToReachEjectionTemp1|30장 - 취출 시간1|TimeToReachEjectionTemp",
          "TimeToReachEjectionTemp2|30장 - 취출 시간2|TimeToReachEjectionTemp",
          "CoolingWater|31장 - 냉각수 유동률, 압력|CoolingWater",
          "DefAll|33장 - 변형 All|DefAll",
          "DefISO|34장 - 변형(Isolate cause of warpage)|DefISO",
          "DefX|35장 - 변형 X|DefX",
          "DefY|36장 - 변형 Y|DefY",
          "DefZ|37장 - 변형 Z|DefZ"
        };
        if (this.m_dtReportView != null)
        {
          foreach (DataRow dataRow in this.m_dtReportView.AsEnumerable())
          {
            DataGridViewRow row = this.dataGridView_View.Rows[this.dataGridView_View.Rows.Add()];
            row.Cells[0].Value = dataRow["Section"];
            row.Cells[2].Value = (object) (dataRow["Slide"].ToString() + LocaleControl.getInstance().GetString("IDS_PAGE") + " - " + dataRow["name"].ToString());
            row.Cells[3].Value = (object) this.newTextBox_View_X.Value;
            row.Cells[4].Value = (object) this.newTextBox_View_Y.Value;
            row.Cells[5].Value = (object) this.newTextBox_View_Z.Value;
            row.Cells[6].Value = dataRow["SlideID"];
          }
          this.dataGridView_View.ClearSelection();
        }
        else
        {
          for (int index = 0; index < strArray1.Length; ++index)
          {
            string[] strArray2 = strArray1[index].Split('|');
            DataGridViewRow row = this.dataGridView_View.Rows[this.dataGridView_View.Rows.Add()];
            row.Cells[0].Value = (object) strArray2[0];
            row.Cells[2].Value = (object) strArray2[1];
            row.Cells[3].Value = (object) this.newTextBox_View_X.Value;
            row.Cells[4].Value = (object) this.newTextBox_View_Y.Value;
            row.Cells[5].Value = (object) this.newTextBox_View_Z.Value;
            row.Cells[6].Value = (object) strArray2[2];
          }
          this.dataGridView_View.ClearSelection();
        }
        this.checkBox_AllCheck.Checked = true;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][frmHDSolutions]SetDefault):" + ex.Message));
      }
    }

    private void SetLoad()
    {
      this.m_fiReportUser = new FileInfo(clsReportDefine.g_diTmpReport.FullName + "\\" + this.m_drStudy["Name"].ToString() + "\\Report.ini");
      if (this.m_fiReportUser.Exists)
      {
        this.LoadUser();
      }
      else
      {
        if (!this.m_fiReportSystem.Exists)
          return;
        this.LoadSystem();
      }
    }

    private void LoadUser()
    {
      try
      {
        string str1 = clsReportUtill.ReadINI("Input", "InjCond", this.m_fiReportUser.FullName);
        if (str1 != "" && str1 == "1")
          this.checkBox_Input_InjCond.Checked = true;
        string str2 = clsReportUtill.ReadINI("Input", "InjName", this.m_fiReportUser.FullName);
        if (str2 != "")
          this.newComboBox_Input_InjData.SelectedIndex = this.newComboBox_Input_InjData.Items.IndexOf((object) str2);
        string str3 = clsReportUtill.ReadINI("Input", "InjMaxStroke", this.m_fiReportUser.FullName);
        if (str3 != "")
          this.unitTextBox_Input_InjMaxStroke.Value = str3;
        string str4 = clsReportUtill.ReadINI("Input", "InjMaxRate", this.m_fiReportUser.FullName);
        if (str4 != "")
          this.unitTextBox_Input_InjMaxRate.Value = str4;
        string str5 = clsReportUtill.ReadINI("Input", "InjScrewDia", this.m_fiReportUser.FullName);
        if (str5 != "")
          this.unitTextBox_Input_InjScrewDia.Value = str5;
        string str6 = clsReportUtill.ReadINI("Input", "InjMaxPressure", this.m_fiReportUser.FullName);
        if (str6 != "")
          this.unitTextBox_Input_InjMaxPressure.Value = str6;
        string str7 = clsReportUtill.ReadINI("Input", "InjMaxClamp", this.m_fiReportUser.FullName);
        if (str7 != "")
          this.unitTextBox_Input_InjMaxClamp.Value = str7;
        string str8 = clsReportUtill.ReadINI("Input", "InjPreRatio", this.m_fiReportUser.FullName);
        if (str8 != "")
          this.unitTextBox_Input_InjPreRatio.Value = str8;
        if (clsReportUtill.ReadINI("Input", "InjType", this.m_fiReportUser.FullName) == "1")
          this.radioButton_MoldData.Checked = true;
        else
          this.radioButton_ProductData.Checked = true;
        string str9 = clsReportUtill.ReadINI("Input", "InjRange1", this.m_fiReportUser.FullName);
        if (str9 != "")
          this.unitTextBox_Input_InjRange1.Value = str9;
        string str10 = clsReportUtill.ReadINI("Input", "InjRange2", this.m_fiReportUser.FullName);
        if (str10 != "")
          this.unitTextBox_Input_InjRange2.Value = str10;
        string str11 = clsReportUtill.ReadINI("Input", "InjRange3", this.m_fiReportUser.FullName);
        if (str11 != "")
          this.unitTextBox_Input_InjRange3.Value = str11;
        string str12 = clsReportUtill.ReadINI("Input", "InjRange4", this.m_fiReportUser.FullName);
        if (str12 != "")
          this.unitTextBox_Input_InjRange4.Value = str12;
        string str13 = clsReportUtill.ReadINI("Input", "InjRangeVP", this.m_fiReportUser.FullName);
        if (str13 != "")
          this.unitTextBox_Input_InjRangeVP.Value = str13;
        string str14 = clsReportUtill.ReadINI("Input", "Item", this.m_fiReportUser.FullName);
        if (str14 != "")
          this.newTextBox_Input_Item.Value = str14;
        string str15 = clsReportUtill.ReadINI("Input", "Sequence", this.m_fiReportUser.FullName);
        if (str15 != "")
          this.newTextBox_Input_Sequence.Value = str15;
        string str16 = clsReportUtill.ReadINI("Input", "Engineer", this.m_fiReportUser.FullName);
        if (str16 != "")
          this.newTextBox_Input_Engineer.Value = str16;
        string str17 = clsReportUtill.ReadINI("Input", "Manager", this.m_fiReportUser.FullName);
        if (str17 != "")
          this.newTextBox_Input_Manager.Value = str17;
        string str18 = clsReportUtill.ReadINI("Input", "WeldLine", this.m_fiReportUser.FullName);
        if (str18 != "")
          this.newComboBox_Input_WeldLine.SelectedIndex = this.newComboBox_Input_WeldLine.Items.IndexOf((object) str18.Split('|')[0]);
        string str19 = clsReportUtill.ReadINI("Input", "AirTrap", this.m_fiReportUser.FullName);
        if (str19 != "")
          this.newComboBox_Input_AirTrap.SelectedIndex = this.newComboBox_Input_AirTrap.Items.IndexOf((object) str19.Split('|')[0]);
        string str20 = clsReportUtill.ReadINI("Input", "ShrinkageSink", this.m_fiReportUser.FullName);
        if (str20 != "")
          this.newComboBox_Input_ShrinkageSink.SelectedIndex = this.newComboBox_Input_ShrinkageSink.Items.IndexOf((object) str20.Split('|')[0]);
        string str21 = clsReportUtill.ReadINI("Input", "Cooling", this.m_fiReportUser.FullName);
        if (str21 != "")
          this.newComboBox_Input_Cooling.SelectedIndex = this.newComboBox_Input_Cooling.Items.IndexOf((object) str21.Split('|')[0]);
        string str22 = clsReportUtill.ReadINI("Input", "Balance", this.m_fiReportUser.FullName);
        if (str22 != "")
          this.newComboBox_Input_Balance.SelectedIndex = this.newComboBox_Input_Balance.Items.IndexOf((object) str22.Split('|')[0]);
        string str23 = clsReportUtill.ReadINI("Input", "Countermeasure", this.m_fiReportUser.FullName);
        if (str23 != "")
        {
          string[] strArray = str23.Split('/');
          this.newTextBox_Input_Countermeasure.Value = "";
          for (int index = 0; index < strArray.Length; ++index)
          {
            if (this.newTextBox_Input_Countermeasure.Value == "")
            {
              this.newTextBox_Input_Countermeasure.Value += strArray[index];
            }
            else
            {
              NewTextBox inputCountermeasure = this.newTextBox_Input_Countermeasure;
              inputCountermeasure.Value = inputCountermeasure.Value + Environment.NewLine + strArray[index];
            }
          }
        }
        string str24 = clsReportUtill.ReadINI("Input", "FillingTime", this.m_fiReportUser.FullName);
        if (str24 != "")
          this.newTextBox_Input_FllingTime.Value = str24;
        string str25 = clsReportUtill.ReadINI("Input", "PackingTime", this.m_fiReportUser.FullName);
        if (str25 != "")
          this.newTextBox_Input_PackingTime.Value = str25;
        string str26 = clsReportUtill.ReadINI("Input", "CoolingTime", this.m_fiReportUser.FullName);
        if (str26 != "")
          this.newTextBox_Input_CoolingTime.Value = str26;
        string str27 = clsReportUtill.ReadINI("Input", "Schizonepeta", this.m_fiReportUser.FullName);
        if (str27 != "")
          this.newTextBox_Input_Schizonepeta.Value = str27;
        string str28 = clsReportUtill.ReadINI("Input", "Filling1", this.m_fiReportUser.FullName);
        if (str28 != "")
          this.unitTextBox_Input_Filling1.Value = str28;
        string str29 = clsReportUtill.ReadINI("Input", "Filling2", this.m_fiReportUser.FullName);
        if (str29 != "")
          this.unitTextBox_Input_Filling2.Value = str29;
        string str30 = clsReportUtill.ReadINI("Input", "Filling3", this.m_fiReportUser.FullName);
        if (str30 != "")
          this.unitTextBox_Input_Filling3.Value = str30;
        string str31 = clsReportUtill.ReadINI("Input", "Filling4", this.m_fiReportUser.FullName);
        if (str31 != "")
          this.unitTextBox_Input_Filling4.Value = str31;
        string str32 = clsReportUtill.ReadINI("Input", "Filling5", this.m_fiReportUser.FullName);
        if (str32 != "")
          this.unitTextBox_Input_Filling5.Value = str32;
        string str33 = clsReportUtill.ReadINI("Input", "Filling6", this.m_fiReportUser.FullName);
        if (str33 != "")
          this.unitTextBox_Input_Filling6.Value = str33;
        string str34 = clsReportUtill.ReadINI("Input", "Filling7", this.m_fiReportUser.FullName);
        if (str34 != "")
          this.unitTextBox_Input_Filling7.Value = str34;
        string str35 = clsReportUtill.ReadINI("Input", "Filling8", this.m_fiReportUser.FullName);
        if (str35 != "")
          this.unitTextBox_Input_Filling8.Value = str35;
        string str36 = clsReportUtill.ReadINI("Input", "FillingFrame", this.m_fiReportUser.FullName);
        if (str36 != "")
          this.newTextBox_Input_FillingFrame.Value = str36;
        string str37 = clsReportUtill.ReadINI("Input", "Frozen1", this.m_fiReportUser.FullName);
        if (str37 != "")
          this.unitTextBox_Input_Frozen1.Value = str37;
        string str38 = clsReportUtill.ReadINI("Input", "Frozen2", this.m_fiReportUser.FullName);
        if (str38 != "")
          this.unitTextBox_Input_Frozen2.Value = str38;
        string str39 = clsReportUtill.ReadINI("Input", "Frozen3", this.m_fiReportUser.FullName);
        if (str39 != "")
          this.unitTextBox_Input_Frozen3.Value = str39;
        string str40 = clsReportUtill.ReadINI("Input", "Frozen4", this.m_fiReportUser.FullName);
        if (str40 != "")
          this.unitTextBox_Input_Frozen4.Value = str40;
        string str41 = clsReportUtill.ReadINI("Input", "MoldTempMin", this.m_fiReportUser.FullName);
        if (str41 != "")
          this.unitTextBox_Input_MoldTempMin.Value = str41;
        string str42 = clsReportUtill.ReadINI("Input", "MoldTempMax", this.m_fiReportUser.FullName);
        if (str42 != "")
          this.unitTextBox_Input_MoldTempMax.Value = str42;
        string str43 = clsReportUtill.ReadINI("Input", "ShrinkageMin", this.m_fiReportUser.FullName);
        if (str43 != "")
          this.unitTextBox_Input_ShrinkageMin.Value = str43;
        string str44 = clsReportUtill.ReadINI("Input", "ShrinkageMax", this.m_fiReportUser.FullName);
        if (str44 != "")
          this.unitTextBox_Input_ShrinkageMax.Value = str44;
        string str45 = clsReportUtill.ReadINI("Input", "SinkMarkMin", this.m_fiReportUser.FullName);
        if (str45 != "")
          this.newTextBox_Input_SinkMarkMin.Value = str45;
        string str46 = clsReportUtill.ReadINI("Input", "SinkMarkMax", this.m_fiReportUser.FullName);
        if (str46 != "")
          this.newTextBox_Input_SinkMarkMax.Value = str46;
        string str47 = clsReportUtill.ReadINI("Input", "DefScale", this.m_fiReportUser.FullName);
        if (str47 != "")
          this.newTextBox_Input_DefScale.Value = str47;
        string str48 = clsReportUtill.ReadINI("Input", "ShrinkageOffset1", this.m_fiReportUser.FullName);
        if (str48 != "")
          this.newTextBox_Input_ShrinkageOffset1.Value = str48;
        string str49 = clsReportUtill.ReadINI("Input", "ShrinkageOffset2", this.m_fiReportUser.FullName);
        if (str49 != "")
          this.newTextBox_Input_ShrinkageOffset2.Value = str49;
        string str50 = clsReportUtill.ReadINI("Input", "DefAll1", this.m_fiReportUser.FullName);
        if (str50 != "")
          this.newComboBox_Input_DefAll.SelectedIndex = this.newComboBox_Input_DefAll.Items.IndexOf((object) str50);
        string str51 = clsReportUtill.ReadINI("Input", "DefAll2", this.m_fiReportUser.FullName);
        if (str51 != "")
          this.newTextBox_Input_DefAll.Value = str51;
        string str52 = clsReportUtill.ReadINI("Input", "DefX1", this.m_fiReportUser.FullName);
        if (str52 != "")
          this.newComboBox_Input_DefX.SelectedIndex = this.newComboBox_Input_DefX.Items.IndexOf((object) str52);
        string str53 = clsReportUtill.ReadINI("Input", "DefX2", this.m_fiReportUser.FullName);
        if (str53 != "")
          this.newTextBox_Input_DefX.Value = str53;
        string str54 = clsReportUtill.ReadINI("Input", "DefY1", this.m_fiReportUser.FullName);
        if (str54 != "")
          this.newComboBox_Input_DefY.SelectedIndex = this.newComboBox_Input_DefY.Items.IndexOf((object) str54);
        string str55 = clsReportUtill.ReadINI("Input", "DefY2", this.m_fiReportUser.FullName);
        if (str55 != "")
          this.newTextBox_Input_DefY.Value = str55;
        string str56 = clsReportUtill.ReadINI("Input", "DefZ1", this.m_fiReportUser.FullName);
        if (str56 != "")
          this.newComboBox_Input_DefZ.SelectedIndex = this.newComboBox_Input_DefZ.Items.IndexOf((object) str56);
        string str57 = clsReportUtill.ReadINI("Input", "DefZ2", this.m_fiReportUser.FullName);
        if (str57 != "")
          this.newTextBox_Input_DefZ.Value = str57;
        string str58 = clsReportUtill.ReadINI("View", "ViewType", this.m_fiReportUser.FullName);
        if (str58 != "")
          this.newComboBox_View_Type.SelectedIndex = this.newComboBox_View_Type.Items.IndexOf((object) str58);
        for (int index = 0; index < this.dataGridView_View.Rows.Count; ++index)
        {
          string[] strArray = clsReportUtill.ReadINI("View", this.dataGridView_View.Rows[index].Cells[0].Value.ToString(), this.m_fiReportUser.FullName).Split('|');
          this.dataGridView_View.Rows[index].Cells[3].Value = (object) strArray[0];
          this.dataGridView_View.Rows[index].Cells[4].Value = (object) strArray[1];
          this.dataGridView_View.Rows[index].Cells[5].Value = (object) strArray[2];
        }
        string[] arr_strUse = clsReportUtill.GetINIAllKeys(this.m_fiReportUser.FullName, "Use");
        for (int i = 0; i < arr_strUse.Length; i++)
        {
          string p_strValue = clsReportUtill.ReadINI("Use", arr_strUse[i], this.m_fiReportUser.FullName);
          foreach (DataGridViewRow dataGridViewRow in this.dataGridView_View.Rows.Cast<DataGridViewRow>().Where<DataGridViewRow>((System.Func<DataGridViewRow, bool>) (Temp => Temp.Cells[6].Value.ToString() == arr_strUse[i])).ToArray<DataGridViewRow>())
            dataGridViewRow.Cells[1].Value = (object) clsReportUtill.ConvertToBoolean(p_strValue);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][frmHDSolutions]LoadUser):" + ex.Message));
      }
    }

    private void LoadSystem()
    {
      try
      {
        string str1 = clsReportUtill.ReadINI("Input", "InjCond", this.m_fiReportSystem.FullName);
        if (str1 != "" && str1 == "1")
          this.checkBox_Input_InjCond.Checked = true;
        if (clsReportUtill.ReadINI("Input", "InjType", this.m_fiReportSystem.FullName) == "1")
          this.radioButton_MoldData.Checked = true;
        else
          this.radioButton_ProductData.Checked = true;
        string str2 = clsReportUtill.ReadINI("Input", "InjRange1", this.m_fiReportSystem.FullName);
        if (str2 != "")
          this.unitTextBox_Input_InjRange1.Value = str2;
        string str3 = clsReportUtill.ReadINI("Input", "InjRange2", this.m_fiReportSystem.FullName);
        if (str3 != "")
          this.unitTextBox_Input_InjRange2.Value = str3;
        string str4 = clsReportUtill.ReadINI("Input", "InjRange3", this.m_fiReportSystem.FullName);
        if (str4 != "")
          this.unitTextBox_Input_InjRange3.Value = str4;
        string str5 = clsReportUtill.ReadINI("Input", "InjRange4", this.m_fiReportSystem.FullName);
        if (str5 != "")
          this.unitTextBox_Input_InjRange4.Value = str5;
        string str6 = clsReportUtill.ReadINI("Input", "InjRangeVP", this.m_fiReportSystem.FullName);
        if (str6 != "")
          this.unitTextBox_Input_InjRangeVP.Value = str6;
        string str7 = clsReportUtill.ReadINI("Input", "Engineer", this.m_fiReportSystem.FullName);
        if (str7 != "")
          this.newTextBox_Input_Engineer.Value = str7;
        string str8 = clsReportUtill.ReadINI("Input", "Manager", this.m_fiReportSystem.FullName);
        if (str8 != "")
          this.newTextBox_Input_Manager.Value = str8;
        string str9 = clsReportUtill.ReadINI("Input", "WeldLine", this.m_fiReportSystem.FullName);
        if (str9 != "")
          this.newComboBox_Input_WeldLine.SelectedIndex = this.newComboBox_Input_WeldLine.Items.IndexOf((object) str9.Split('|')[0]);
        string str10 = clsReportUtill.ReadINI("Input", "AirTrap", this.m_fiReportSystem.FullName);
        if (str10 != "")
          this.newComboBox_Input_AirTrap.SelectedIndex = this.newComboBox_Input_AirTrap.Items.IndexOf((object) str10.Split('|')[0]);
        string str11 = clsReportUtill.ReadINI("Input", "ShrinkageSink", this.m_fiReportSystem.FullName);
        if (str11 != "")
          this.newComboBox_Input_ShrinkageSink.SelectedIndex = this.newComboBox_Input_ShrinkageSink.Items.IndexOf((object) str11.Split('|')[0]);
        string str12 = clsReportUtill.ReadINI("Input", "Cooling", this.m_fiReportSystem.FullName);
        if (str12 != "")
          this.newComboBox_Input_Cooling.SelectedIndex = this.newComboBox_Input_Cooling.Items.IndexOf((object) str12.Split('|')[0]);
        string str13 = clsReportUtill.ReadINI("Input", "Balance", this.m_fiReportSystem.FullName);
        if (str13 != "")
          this.newComboBox_Input_Balance.SelectedIndex = this.newComboBox_Input_Balance.Items.IndexOf((object) str13.Split('|')[0]);
        string str14 = clsReportUtill.ReadINI("Input", "Countermeasure", this.m_fiReportSystem.FullName);
        if (str14 != "")
        {
          string[] strArray = str14.Split('/');
          this.newTextBox_Input_Countermeasure.Value = "";
          for (int index = 0; index < strArray.Length; ++index)
          {
            if (this.newTextBox_Input_Countermeasure.Value == "")
            {
              this.newTextBox_Input_Countermeasure.Value += strArray[index];
            }
            else
            {
              NewTextBox inputCountermeasure = this.newTextBox_Input_Countermeasure;
              inputCountermeasure.Value = inputCountermeasure.Value + Environment.NewLine + strArray[index];
            }
          }
        }
        string str15 = clsReportUtill.ReadINI("Input", "Filling1", this.m_fiReportSystem.FullName);
        if (str15 != "")
          this.unitTextBox_Input_Filling1.Value = str15;
        string str16 = clsReportUtill.ReadINI("Input", "Filling2", this.m_fiReportSystem.FullName);
        if (str16 != "")
          this.unitTextBox_Input_Filling2.Value = str16;
        string str17 = clsReportUtill.ReadINI("Input", "Filling3", this.m_fiReportSystem.FullName);
        if (str17 != "")
          this.unitTextBox_Input_Filling3.Value = str17;
        string str18 = clsReportUtill.ReadINI("Input", "Filling4", this.m_fiReportSystem.FullName);
        if (str18 != "")
          this.unitTextBox_Input_Filling4.Value = str18;
        string str19 = clsReportUtill.ReadINI("Input", "Filling5", this.m_fiReportSystem.FullName);
        if (str19 != "")
          this.unitTextBox_Input_Filling5.Value = str19;
        string str20 = clsReportUtill.ReadINI("Input", "Filling6", this.m_fiReportSystem.FullName);
        if (str20 != "")
          this.unitTextBox_Input_Filling6.Value = str20;
        string str21 = clsReportUtill.ReadINI("Input", "Filling7", this.m_fiReportSystem.FullName);
        if (str21 != "")
          this.unitTextBox_Input_Filling7.Value = str21;
        string str22 = clsReportUtill.ReadINI("Input", "Filling8", this.m_fiReportSystem.FullName);
        if (str22 != "")
          this.unitTextBox_Input_Filling8.Value = str22;
        string str23 = clsReportUtill.ReadINI("Input", "FillingFrame", this.m_fiReportSystem.FullName);
        if (str23 != "")
          this.newTextBox_Input_FillingFrame.Value = str23;
        string str24 = clsReportUtill.ReadINI("Input", "Frozen1", this.m_fiReportSystem.FullName);
        if (str24 != "")
          this.unitTextBox_Input_Frozen1.Value = str24;
        string str25 = clsReportUtill.ReadINI("Input", "Frozen2", this.m_fiReportSystem.FullName);
        if (str25 != "")
          this.unitTextBox_Input_Frozen2.Value = str25;
        string str26 = clsReportUtill.ReadINI("Input", "Frozen3", this.m_fiReportSystem.FullName);
        if (str26 != "")
          this.unitTextBox_Input_Frozen3.Value = str26;
        string str27 = clsReportUtill.ReadINI("Input", "Frozen4", this.m_fiReportSystem.FullName);
        if (str27 != "")
          this.unitTextBox_Input_Frozen4.Value = str27;
        string str28 = clsReportUtill.ReadINI("Input", "SinkMarkMin", this.m_fiReportSystem.FullName);
        if (str28 != "")
          this.newTextBox_Input_SinkMarkMin.Value = str28;
        string str29 = clsReportUtill.ReadINI("Input", "SinkMarkMax", this.m_fiReportSystem.FullName);
        if (str29 != "")
          this.newTextBox_Input_SinkMarkMax.Value = str29;
        string str30 = clsReportUtill.ReadINI("Input", "DefScale", this.m_fiReportSystem.FullName);
        if (str30 != "")
          this.newTextBox_Input_DefScale.Value = str30;
        string str31 = clsReportUtill.ReadINI("Input", "ShrinkageOffset1", this.m_fiReportSystem.FullName);
        if (str31 != "")
          this.newTextBox_Input_ShrinkageOffset1.Value = str31;
        string str32 = clsReportUtill.ReadINI("Input", "ShrinkageOffset2", this.m_fiReportSystem.FullName);
        if (str32 != "")
          this.newTextBox_Input_ShrinkageOffset2.Value = str32;
        string str33 = clsReportUtill.ReadINI("View", "ViewType", this.m_fiReportSystem.FullName);
        if (str33 != "")
          this.newComboBox_View_Type.SelectedIndex = this.newComboBox_View_Type.Items.IndexOf((object) str33);
        for (int index = 0; index < this.dataGridView_View.Rows.Count; ++index)
        {
          string[] strArray = clsReportUtill.ReadINI("View", this.dataGridView_View.Rows[index].Cells[0].Value.ToString(), this.m_fiReportSystem.FullName).Split('|');
          this.dataGridView_View.Rows[index].Cells[3].Value = (object) strArray[0];
          this.dataGridView_View.Rows[index].Cells[4].Value = (object) strArray[1];
          this.dataGridView_View.Rows[index].Cells[5].Value = (object) strArray[2];
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][frmHDSolutions]LoadSystem):" + ex.Message));
      }
    }

    private void newComboBox_Input_InjData_SelectedIndexChanged(object sender, EventArgs e)
    {
      if (this.newComboBox_Input_InjData.SelectedIndex == 0)
      {
        if (this.m_dicReport == null || this.m_dicReport.Count <= 0)
          return;
        this.unitTextBox_Input_InjMaxStroke.Value = this.m_dicReport["InjMaxStroke"];
        this.unitTextBox_Input_InjMaxRate.Value = this.m_dicReport["InjMaxRate"];
        this.unitTextBox_Input_InjScrewDia.Value = this.m_dicReport["InjScrewDia"];
        this.unitTextBox_Input_InjMaxPressure.Value = this.m_dicReport["InjMaxPressure"];
        this.unitTextBox_Input_InjMaxClamp.Value = this.m_dicReport["InjMaxClamp"];
        this.unitTextBox_Input_InjPreRatio.Value = "";
      }
      else
      {
        DataRow dataRow = clsReportDefine.g_dtInjectionDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["FileName"].ToString() == this.newComboBox_Input_InjData.Value)).FirstOrDefault<DataRow>();
        if (dataRow == null)
          return;
        this.unitTextBox_Input_InjMaxStroke.Value = dataRow["MaxStroke"].ToString();
        this.unitTextBox_Input_InjMaxRate.Value = dataRow["MaxRate"].ToString();
        this.unitTextBox_Input_InjScrewDia.Value = dataRow["ScrewDia"].ToString();
        this.unitTextBox_Input_InjMaxPressure.Value = dataRow["MaxPressure"].ToString();
        this.unitTextBox_Input_InjMaxClamp.Value = dataRow["MaxClamp"].ToString();
        this.unitTextBox_Input_InjPreRatio.Value = dataRow["PreRatio"].ToString();
      }
    }

    private void newComboBox_Input_WeldLine_SelectedIndexChanged(object sender, EventArgs e)
    {
      switch (this.newComboBox_Input_WeldLine.SelectedIndex)
      {
        case 0:
          this.newTextBox_Input_WeldLine.Value = LocaleControl.getInstance().GetString("IDS_WELDLINE") + " " + LocaleControl.getInstance().GetString("IDS_NO_PROBLEM");
          break;
        case 1:
          this.newTextBox_Input_WeldLine.Value = LocaleControl.getInstance().GetString("IDS_WELDLINE") + " " + LocaleControl.getInstance().GetString("IDS_DISCUSSION");
          break;
        case 2:
          this.newTextBox_Input_WeldLine.Value = LocaleControl.getInstance().GetString("IDS_WELDLINE") + " " + LocaleControl.getInstance().GetString("IDS_PROBLEM");
          break;
      }
    }

    private void newComboBox_Input_AirTrap_SelectedIndexChanged(object sender, EventArgs e)
    {
      switch (this.newComboBox_Input_AirTrap.SelectedIndex)
      {
        case 0:
          this.newTextBox_Input_AirTrap.Value = LocaleControl.getInstance().GetString("IDS_AIRTRAP") + " " + LocaleControl.getInstance().GetString("IDS_NO_PROBLEM");
          break;
        case 1:
          this.newTextBox_Input_AirTrap.Value = LocaleControl.getInstance().GetString("IDS_AIRTRAP") + " " + LocaleControl.getInstance().GetString("IDS_DISCUSSION");
          break;
        case 2:
          this.newTextBox_Input_AirTrap.Value = LocaleControl.getInstance().GetString("IDS_AIRTRAP") + " " + LocaleControl.getInstance().GetString("IDS_PROBLEM");
          break;
      }
    }

    private void newComboBox_Input_ShrinkageSink_SelectedIndexChanged(object sender, EventArgs e)
    {
      switch (this.newComboBox_Input_ShrinkageSink.SelectedIndex)
      {
        case 0:
          this.newTextBox_Input_ShrinkageSink.Value = LocaleControl.getInstance().GetString("IDS_SINKMARK") + " " + LocaleControl.getInstance().GetString("IDS_NO_PROBLEM");
          break;
        case 1:
          this.newTextBox_Input_ShrinkageSink.Value = LocaleControl.getInstance().GetString("IDS_SINKMARK") + " " + LocaleControl.getInstance().GetString("IDS_DISCUSSION");
          break;
        case 2:
          this.newTextBox_Input_ShrinkageSink.Value = LocaleControl.getInstance().GetString("IDS_SINKMARK") + " " + LocaleControl.getInstance().GetString("IDS_PROBLEM");
          break;
      }
    }

    private void newComboBox_Input_Cooling_SelectedIndexChanged(object sender, EventArgs e)
    {
      switch (this.newComboBox_Input_Cooling.SelectedIndex)
      {
        case 0:
          this.newTextBox_Input_Cooling.Value = LocaleControl.getInstance().GetString("IDS_COOL_TEMP") + " " + LocaleControl.getInstance().GetString("IDS_NO_PROBLEM");
          break;
        case 1:
          this.newTextBox_Input_Cooling.Value = LocaleControl.getInstance().GetString("IDS_COOL_TEMP") + " " + LocaleControl.getInstance().GetString("IDS_DISCUSSION");
          break;
        case 2:
          this.newTextBox_Input_Cooling.Value = LocaleControl.getInstance().GetString("IDS_COOL_TEMP") + " " + LocaleControl.getInstance().GetString("IDS_PROBLEM");
          break;
      }
    }

    private void newComboBox_Input_Balance_SelectedIndexChanged(object sender, EventArgs e)
    {
      switch (this.newComboBox_Input_Balance.SelectedIndex)
      {
        case 0:
          this.newTextBox_Input_Balance.Value = LocaleControl.getInstance().GetString("IDS_BALANCE") + " " + LocaleControl.getInstance().GetString("IDS_NO_PROBLEM");
          break;
        case 1:
          this.newTextBox_Input_Balance.Value = LocaleControl.getInstance().GetString("IDS_BALANCE") + " " + LocaleControl.getInstance().GetString("IDS_DISCUSSION");
          break;
        case 2:
          this.newTextBox_Input_Balance.Value = LocaleControl.getInstance().GetString("IDS_BALANCE") + " " + LocaleControl.getInstance().GetString("IDS_PROBLEM");
          break;
      }
    }

    private void newComboBox_View_Type_SelectedIndexChanged(object sender, EventArgs e)
    {
      string s = this.newComboBox_View_Type.Value;
      // ISSUE: reference to a compiler-generated method
      switch (\u003CPrivateImplementationDetails\u003E.ComputeStringHash(s))
      {
        case 122316834:
          if (!(s == "BACK"))
            break;
          this.newTextBox_View_X.Value = "0";
          this.newTextBox_View_Y.Value = "180";
          this.newTextBox_View_Z.Value = "0";
          break;
        case 184671100:
          if (!(s == "BOT-BACK-LEFT"))
            break;
          this.newTextBox_View_X.Value = "45";
          this.newTextBox_View_Y.Value = "145";
          this.newTextBox_View_Z.Value = "-30";
          break;
        case 272375920:
          if (!(s == "LEFT"))
            break;
          this.newTextBox_View_X.Value = "0";
          this.newTextBox_View_Y.Value = "90";
          this.newTextBox_View_Z.Value = "0";
          break;
        case 462935941:
          if (!(s == "RIGHT"))
            break;
          this.newTextBox_View_X.Value = "0";
          this.newTextBox_View_Y.Value = "-90";
          this.newTextBox_View_Z.Value = "0";
          break;
        case 546288794:
          if (!(s == "LEFT-FRONT-BOT"))
            break;
          this.newTextBox_View_X.Value = "-45";
          this.newTextBox_View_Y.Value = "35";
          this.newTextBox_View_Z.Value = "30";
          break;
        case 632706351:
          if (!(s == "BOT-FRONT-RIGHT"))
            break;
          this.newTextBox_View_X.Value = "-45";
          this.newTextBox_View_Y.Value = "-35";
          this.newTextBox_View_Z.Value = "-30";
          break;
        case 692843612:
          if (!(s == "TOP"))
            break;
          this.newTextBox_View_X.Value = "90";
          this.newTextBox_View_Y.Value = "0";
          this.newTextBox_View_Z.Value = "0";
          break;
        case 1869497751:
          if (!(s == "TOP-BACK-RIGHT"))
            break;
          this.newTextBox_View_X.Value = "135";
          this.newTextBox_View_Y.Value = "-35";
          this.newTextBox_View_Z.Value = "-30";
          break;
        case 2573064646:
          if (!(s == "LEFT-BACK-TOP"))
            break;
          this.newTextBox_View_X.Value = "135";
          this.newTextBox_View_Y.Value = "35";
          this.newTextBox_View_Z.Value = "30";
          break;
        case 2596576200:
          if (!(s == "TOP-FRONT-LEFT"))
            break;
          this.newTextBox_View_X.Value = "-135";
          this.newTextBox_View_Y.Value = "145";
          this.newTextBox_View_Z.Value = "-30";
          break;
        case 3291408952:
          if (!(s == "FRONT"))
            break;
          this.newTextBox_View_X.Value = "0";
          this.newTextBox_View_Y.Value = "0";
          this.newTextBox_View_Z.Value = "0";
          break;
        case 3351543025:
          if (!(s == "RIGHT-FRONT-TOP"))
            break;
          this.newTextBox_View_X.Value = "-135";
          this.newTextBox_View_Y.Value = "-145";
          this.newTextBox_View_Z.Value = "30";
          break;
        case 3407844522:
          if (!(s == "BOTTOM"))
            break;
          this.newTextBox_View_X.Value = "90";
          this.newTextBox_View_Y.Value = "180";
          this.newTextBox_View_Z.Value = "0";
          break;
        case 4249270451:
          if (!(s == "RIGHT-BACK-BOT"))
            break;
          this.newTextBox_View_X.Value = "45";
          this.newTextBox_View_Y.Value = "-145";
          this.newTextBox_View_Z.Value = "30";
          break;
      }
    }

    private void newButton_NewClick(object sender, EventArgs e)
    {
      NewButton newButton = sender as NewButton;
      if (newButton == this.newButton_Input)
      {
        newButton.ButtonBackColor = Color.LightSteelBlue;
        this.newButton_View.ButtonBackColor = Color.White;
        this.tabControl_Main.SelectedIndex = 0;
      }
      else if (newButton == this.newButton_View)
      {
        this.newButton_Input.ButtonBackColor = Color.White;
        newButton.ButtonBackColor = Color.LightSteelBlue;
        this.tabControl_Main.SelectedIndex = 1;
        this.dataGridView_View.ClearSelection();
      }
      else if (newButton == this.newButton_View_All)
      {
        foreach (DataGridViewRow row in (IEnumerable) this.dataGridView_View.Rows)
        {
          row.Cells[3].Value = (object) this.newTextBox_View_X.Value;
          row.Cells[4].Value = (object) this.newTextBox_View_Y.Value;
          row.Cells[5].Value = (object) this.newTextBox_View_Z.Value;
        }
      }
      else if (newButton == this.newButton_View_Select)
      {
        foreach (DataGridViewRow row in (IEnumerable) this.dataGridView_View.Rows)
        {
          if ((bool) row.Cells[1].FormattedValue)
          {
            row.Cells[3].Value = (object) this.newTextBox_View_X.Value;
            row.Cells[4].Value = (object) this.newTextBox_View_Y.Value;
            row.Cells[5].Value = (object) this.newTextBox_View_Z.Value;
          }
        }
      }
      else
      {
        if (newButton != this.newButton_Apply)
          return;
        this.Apply();
      }
    }

    private void Apply()
    {
      string p_strValue1 = "";
      StringBuilder stringBuilder = new StringBuilder();
      Dictionary<string, string> dictionary = new Dictionary<string, string>();
      try
      {
        if (!this.checkBox_Input_InjCond.Checked)
          clsReportUtill.WriteINI("Input", "InjCond", "0", this.m_fiReportUser.FullName);
        else
          clsReportUtill.WriteINI("Input", "InjCond", "1", this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "InjName", this.newComboBox_Input_InjData.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "InjMaxStroke", this.unitTextBox_Input_InjMaxStroke.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "InjMaxRate", this.unitTextBox_Input_InjMaxRate.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "InjScrewDia", this.unitTextBox_Input_InjScrewDia.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "InjMaxPressure", this.unitTextBox_Input_InjMaxPressure.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "InjMaxClamp", this.unitTextBox_Input_InjMaxClamp.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "InjPreRatio", this.unitTextBox_Input_InjPreRatio.Value, this.m_fiReportUser.FullName);
        if (this.radioButton_ProductData.Checked)
          p_strValue1 = "0";
        else if (this.radioButton_MoldData.Checked)
          p_strValue1 = "1";
        clsReportUtill.WriteINI("Input", "InjType", p_strValue1, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "InjRange1", this.unitTextBox_Input_InjRange1.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "InjRange2", this.unitTextBox_Input_InjRange2.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "InjRange3", this.unitTextBox_Input_InjRange3.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "InjRange4", this.unitTextBox_Input_InjRange4.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "InjRangeVP", this.unitTextBox_Input_InjRangeVP.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Item", this.newTextBox_Input_Item.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Sequence", this.newTextBox_Input_Sequence.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Engineer", this.newTextBox_Input_Engineer.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Manager", this.newTextBox_Input_Manager.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "WeldLine", this.newComboBox_Input_WeldLine.Value + "|" + this.newTextBox_Input_WeldLine.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "AirTrap", this.newComboBox_Input_AirTrap.Value + "|" + this.newTextBox_Input_AirTrap.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "ShrinkageSink", this.newComboBox_Input_ShrinkageSink.Value + "|" + this.newTextBox_Input_ShrinkageSink.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Cooling", this.newComboBox_Input_Cooling.Value + "|" + this.newTextBox_Input_Cooling.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Balance", this.newComboBox_Input_Balance.Value + "|" + this.newTextBox_Input_Balance.Value, this.m_fiReportUser.FullName);
        stringBuilder.Clear();
        for (int index = 0; index < this.newTextBox_Input_Countermeasure.Lines.Length; ++index)
        {
          if (stringBuilder.Length == 0)
            stringBuilder.Append(this.newTextBox_Input_Countermeasure.Lines[index]);
          else
            stringBuilder.Append("/" + this.newTextBox_Input_Countermeasure.Lines[index]);
        }
        clsReportUtill.WriteINI("Input", "Countermeasure", stringBuilder.ToString(), this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "FillingTime", this.newTextBox_Input_FllingTime.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "PackingTime", this.newTextBox_Input_PackingTime.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "CoolingTime", this.newTextBox_Input_CoolingTime.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Schizonepeta", this.newTextBox_Input_Schizonepeta.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Filling1", this.unitTextBox_Input_Filling1.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Filling2", this.unitTextBox_Input_Filling2.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Filling3", this.unitTextBox_Input_Filling3.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Filling4", this.unitTextBox_Input_Filling4.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Filling5", this.unitTextBox_Input_Filling5.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Filling6", this.unitTextBox_Input_Filling6.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Filling7", this.unitTextBox_Input_Filling7.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Filling8", this.unitTextBox_Input_Filling8.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "FillingFrame", this.newTextBox_Input_FillingFrame.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Frozen1", this.unitTextBox_Input_Frozen1.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Frozen2", this.unitTextBox_Input_Frozen2.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Frozen3", this.unitTextBox_Input_Frozen3.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Frozen4", this.unitTextBox_Input_Frozen4.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "MoldTempMin", this.unitTextBox_Input_MoldTempMin.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "MoldTempMax", this.unitTextBox_Input_MoldTempMax.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "ShrinkageMin", this.unitTextBox_Input_ShrinkageMin.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "ShrinkageMax", this.unitTextBox_Input_ShrinkageMax.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "SinkMarkMin", this.newTextBox_Input_SinkMarkMin.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "SinkMarkMax", this.newTextBox_Input_SinkMarkMax.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "DefScale", this.newTextBox_Input_DefScale.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "ShrinkageOffset1", this.newTextBox_Input_ShrinkageOffset1.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "ShrinkageOffset2", this.newTextBox_Input_ShrinkageOffset2.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "DefAll1", this.newComboBox_Input_DefAll.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "DefAll2", this.newTextBox_Input_DefAll.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "DefX1", this.newComboBox_Input_DefX.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "DefX2", this.newTextBox_Input_DefX.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "DefY1", this.newComboBox_Input_DefY.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "DefY2", this.newTextBox_Input_DefY.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "DefZ1", this.newComboBox_Input_DefZ.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "DefZ2", this.newTextBox_Input_DefZ.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("View", "ViewType", this.newComboBox_View_Type.Value, this.m_fiReportUser.FullName);
        for (int index = 0; index < this.dataGridView_View.Rows.Count; ++index)
        {
          string p_strValue2 = this.dataGridView_View.Rows[index].Cells[3].Value.ToString() + "|" + this.dataGridView_View.Rows[index].Cells[4].Value + "|" + this.dataGridView_View.Rows[index].Cells[5].Value;
          clsReportUtill.WriteINI("View", this.dataGridView_View.Rows[index].Cells[0].Value.ToString(), p_strValue2, this.m_fiReportUser.FullName);
        }
        foreach (DataGridViewRow row in (IEnumerable) this.dataGridView_View.Rows)
        {
          if (dictionary.ContainsKey((string) row.Cells[6].Value))
          {
            if (row.Cells[1].Value.ToString() != dictionary[(string) row.Cells[6].Value])
              dictionary[(string) row.Cells[6].Value] = "True";
          }
          else if (row.Cells[1].Value == null)
            dictionary.Add((string) row.Cells[6].Value, "False");
          else
            dictionary.Add((string) row.Cells[6].Value, row.Cells[1].Value.ToString());
        }
        foreach (KeyValuePair<string, string> keyValuePair in dictionary)
          clsReportUtill.WriteINI("Use", keyValuePair.Key, keyValuePair.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Template", "Company", this.m_enumCompany.ToString(), this.m_fiReportUser.FullName);
        this.DialogResult = DialogResult.OK;
        this.Close();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][frmHDSolutions]Apply):" + ex.Message));
      }
    }

    private void frmHDSolutions_FormClosed(object sender, FormClosedEventArgs e)
    {
      string p_strValue1 = "";
      StringBuilder stringBuilder = new StringBuilder();
      try
      {
        if (!this.checkBox_Input_InjCond.Checked)
          clsReportUtill.WriteINI("Input", "InjCond", "0", this.m_fiReportSystem.FullName);
        else
          clsReportUtill.WriteINI("Input", "InjCond", "1", this.m_fiReportSystem.FullName);
        if (this.radioButton_ProductData.Checked)
          p_strValue1 = "0";
        else if (this.radioButton_MoldData.Checked)
          p_strValue1 = "1";
        clsReportUtill.WriteINI("Input", "InjType", p_strValue1, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "InjRange1", this.unitTextBox_Input_InjRange1.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "InjRange2", this.unitTextBox_Input_InjRange2.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "InjRange3", this.unitTextBox_Input_InjRange3.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "InjRange4", this.unitTextBox_Input_InjRange4.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "InjRangeVP", this.unitTextBox_Input_InjRangeVP.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Engineer", this.newTextBox_Input_Engineer.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Manager", this.newTextBox_Input_Manager.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "WeldLine", this.newComboBox_Input_WeldLine.Value + "|" + this.newTextBox_Input_WeldLine.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "AirTrap", this.newComboBox_Input_AirTrap.Value + "|" + this.newTextBox_Input_AirTrap.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "ShrinkageSink", this.newComboBox_Input_ShrinkageSink.Value + "|" + this.newTextBox_Input_ShrinkageSink.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Cooling", this.newComboBox_Input_Cooling.Value + "|" + this.newTextBox_Input_Cooling.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Balance", this.newComboBox_Input_Balance.Value + "|" + this.newTextBox_Input_Balance.Value, this.m_fiReportSystem.FullName);
        stringBuilder.Clear();
        for (int index = 0; index < this.newTextBox_Input_Countermeasure.Lines.Length; ++index)
        {
          if (stringBuilder.Length == 0)
            stringBuilder.Append(this.newTextBox_Input_Countermeasure.Lines[index]);
          else
            stringBuilder.Append("/" + this.newTextBox_Input_Countermeasure.Lines[index]);
        }
        clsReportUtill.WriteINI("Input", "Countermeasure", stringBuilder.ToString(), this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Filling1", this.unitTextBox_Input_Filling1.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Filling2", this.unitTextBox_Input_Filling2.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Filling3", this.unitTextBox_Input_Filling3.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Filling4", this.unitTextBox_Input_Filling4.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Filling5", this.unitTextBox_Input_Filling5.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Filling6", this.unitTextBox_Input_Filling6.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Filling7", this.unitTextBox_Input_Filling7.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Filling8", this.unitTextBox_Input_Filling8.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "FillingFrame", this.newTextBox_Input_FillingFrame.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Frozen1", this.unitTextBox_Input_Frozen1.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Frozen2", this.unitTextBox_Input_Frozen2.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Frozen3", this.unitTextBox_Input_Frozen3.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Frozen4", this.unitTextBox_Input_Frozen4.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "SinkMarkMin", this.newTextBox_Input_SinkMarkMin.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "SinkMarkMax", this.newTextBox_Input_SinkMarkMax.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "DefScale", this.newTextBox_Input_DefScale.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "ShrinkageOffset1", this.newTextBox_Input_ShrinkageOffset1.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "ShrinkageOffset2", this.newTextBox_Input_ShrinkageOffset2.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("View", "ViewType", this.newComboBox_View_Type.Value, this.m_fiReportSystem.FullName);
        for (int index = 0; index < this.dataGridView_View.Rows.Count; ++index)
        {
          string p_strValue2 = this.dataGridView_View.Rows[index].Cells[3].Value.ToString() + "|" + this.dataGridView_View.Rows[index].Cells[4].Value + "|" + this.dataGridView_View.Rows[index].Cells[5].Value;
          clsReportUtill.WriteINI("View", this.dataGridView_View.Rows[index].Cells[0].Value.ToString(), p_strValue2, this.m_fiReportSystem.FullName);
        }
        clsReportUtill.WriteINI("Template", "Company", this.m_enumCompany.ToString(), this.m_fiReportSystem.FullName);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][frmHDSolutions]frmHDSolutions_FormClosed):" + ex.Message));
      }
    }

    private void checkBox_AllCheck_CheckedChanged(object sender, EventArgs e)
    {
      foreach (DataGridViewRow row in (IEnumerable) this.dataGridView_View.Rows)
        row.Cells[1].Value = (object) ((CheckBox) sender).Checked;
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      DataGridViewCellStyle gridViewCellStyle1 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle2 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle3 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle4 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle5 = new DataGridViewCellStyle();
      this.tabControl_Main = new TabControl();
      this.tabPage_Input = new TabPage();
      this.panel3 = new Panel();
      this.radioButton_ProductData = new RadioButton();
      this.radioButton_MoldData = new RadioButton();
      this.newTextBox_Input_SinkMarkMax = new NewTextBox();
      this.label_Input_SinkMarkMax = new Label();
      this.newTextBox_Input_ShrinkageOffset1 = new NewTextBox();
      this.newTextBox_Input_ShrinkageOffset2 = new NewTextBox();
      this.newTextBox_Input_DefScale = new NewTextBox();
      this.newTextBox_Input_SinkMarkMin = new NewTextBox();
      this.label_Input_DefScale = new Label();
      this.label_Input_ShrinkageOffset2 = new Label();
      this.label_Input_ShrinkageOffset1 = new Label();
      this.label_Input_SinkMarkMin = new Label();
      this.label_Input_ScaleValue = new Label();
      this.checkBox_Input_InjCond = new CheckBox();
      this.newComboBox_Input_DefZ = new NewComboBox();
      this.newComboBox_Input_DefY = new NewComboBox();
      this.newComboBox_Input_DefX = new NewComboBox();
      this.newComboBox_Input_DefAll = new NewComboBox();
      this.newComboBox_Input_Balance = new NewComboBox();
      this.newComboBox_Input_Cooling = new NewComboBox();
      this.newComboBox_Input_ShrinkageSink = new NewComboBox();
      this.newComboBox_Input_AirTrap = new NewComboBox();
      this.newComboBox_Input_WeldLine = new NewComboBox();
      this.label29 = new Label();
      this.label28 = new Label();
      this.label31 = new Label();
      this.label32 = new Label();
      this.label26 = new Label();
      this.label27 = new Label();
      this.newTextBox_Input_Schizonepeta = new NewTextBox();
      this.newTextBox_Input_Manager = new NewTextBox();
      this.newTextBox_Input_CoolingTime = new NewTextBox();
      this.newTextBox_Input_Engineer = new NewTextBox();
      this.newTextBox_Input_FillingFrame = new NewTextBox();
      this.newTextBox_Input_PackingTime = new NewTextBox();
      this.newTextBox_Input_Sequence = new NewTextBox();
      this.newTextBox_Input_Countermeasure = new NewTextBox();
      this.newTextBox_Input_DefZ = new NewTextBox();
      this.newTextBox_Input_DefY = new NewTextBox();
      this.newTextBox_Input_DefX = new NewTextBox();
      this.newTextBox_Input_DefAll = new NewTextBox();
      this.newTextBox_Input_Balance = new NewTextBox();
      this.newTextBox_Input_Cooling = new NewTextBox();
      this.newTextBox_Input_ShrinkageSink = new NewTextBox();
      this.newTextBox_Input_AirTrap = new NewTextBox();
      this.newTextBox_Input_WeldLine = new NewTextBox();
      this.newTextBox_Input_FllingTime = new NewTextBox();
      this.newTextBox_Input_Item = new NewTextBox();
      this.label_Input_InjRangeVP = new Label();
      this.label_Input_InjRange4 = new Label();
      this.label_Input_InjRange3 = new Label();
      this.label_Input_MoldOpen = new Label();
      this.label10 = new Label();
      this.label35 = new Label();
      this.label11 = new Label();
      this.label_Input_InjRange2 = new Label();
      this.label34 = new Label();
      this.label53 = new Label();
      this.label38 = new Label();
      this.label43 = new Label();
      this.label_Input_Filling8 = new Label();
      this.label_Input_Frozen4 = new Label();
      this.label_Input_Filling4 = new Label();
      this.label_Input_Filling7 = new Label();
      this.label_Input_Frozen3 = new Label();
      this.label_Input_Filling3 = new Label();
      this.label_Input_Filling6 = new Label();
      this.label_Input_Frozen2 = new Label();
      this.label_Input_Filling2 = new Label();
      this.label_Input_Filling5 = new Label();
      this.label_Input_Frozen1 = new Label();
      this.label_Input_Filling1 = new Label();
      this.label_Input_ShrinkageMax = new Label();
      this.label67 = new Label();
      this.label66 = new Label();
      this.label64 = new Label();
      this.label60 = new Label();
      this.label_Input_SurfaceMaxTemp = new Label();
      this.label_Input_ShrinkageMin = new Label();
      this.label_Input_SurfaceMinTemp = new Label();
      this.label33 = new Label();
      this.label13 = new Label();
      this.label14 = new Label();
      this.label_Input_InjRange1 = new Label();
      this.unitTextBox_Input_InjPreRatio = new UnitTextBox();
      this.unitTextBox_Input_InjMaxClamp = new UnitTextBox();
      this.unitTextBox_Input_InjMaxPressure = new UnitTextBox();
      this.unitTextBox_Input_InjRangeVP = new UnitTextBox();
      this.unitTextBox_Input_InjRange4 = new UnitTextBox();
      this.unitTextBox_Input_InjRange3 = new UnitTextBox();
      this.unitTextBox_Input_InjScrewDia = new UnitTextBox();
      this.unitTextBox_Input_InjRange2 = new UnitTextBox();
      this.unitTextBox_Input_Filling8 = new UnitTextBox();
      this.unitTextBox_Input_InjMaxRate = new UnitTextBox();
      this.unitTextBox_Input_Filling7 = new UnitTextBox();
      this.unitTextBox_Input_Frozen4 = new UnitTextBox();
      this.unitTextBox_Input_Filling4 = new UnitTextBox();
      this.unitTextBox_Input_Filling6 = new UnitTextBox();
      this.unitTextBox_Input_Frozen3 = new UnitTextBox();
      this.unitTextBox_Input_ShrinkageMax = new UnitTextBox();
      this.unitTextBox_Input_Filling3 = new UnitTextBox();
      this.unitTextBox_Input_ShrinkageMin = new UnitTextBox();
      this.unitTextBox_Input_MoldTempMax = new UnitTextBox();
      this.unitTextBox_Input_MoldTempMin = new UnitTextBox();
      this.unitTextBox_Input_Filling5 = new UnitTextBox();
      this.unitTextBox_Input_Frozen2 = new UnitTextBox();
      this.unitTextBox_Input_Filling2 = new UnitTextBox();
      this.unitTextBox_Input_Frozen1 = new UnitTextBox();
      this.unitTextBox_Input_Filling1 = new UnitTextBox();
      this.unitTextBox_Input_InjRange1 = new UnitTextBox();
      this.unitTextBox_Input_InjMaxStroke = new UnitTextBox();
      this.label_Input_InjPreRatio = new Label();
      this.label_Input_InjScrewDia = new Label();
      this.label_Input_InjMaxClamp = new Label();
      this.label_Input_InjMaxRate = new Label();
      this.label_Input_InjMaxPressure = new Label();
      this.label_Input_InjMaxStroke = new Label();
      this.newComboBox_Input_InjData = new NewComboBox();
      this.label_Input_Deflection = new Label();
      this.label_Input_SurfShrinkage = new Label();
      this.label48 = new Label();
      this.label37 = new Label();
      this.label30 = new Label();
      this.label_Input_Result_Comment = new Label();
      this.label_Input_Sign = new Label();
      this.label_Input_Range = new Label();
      this.label61 = new Label();
      this.label_Input_Inj = new Label();
      this.panel2 = new Panel();
      this.tabPage_View = new TabPage();
      this.checkBox_AllCheck = new CheckBox();
      this.dataGridView_View = new DataGridView();
      this.Column6 = new DataGridViewTextBoxColumn();
      this.Column5 = new DataGridViewCheckBoxColumn();
      this.Column_Item = new DataGridViewTextBoxColumn();
      this.Column2 = new DataGridViewTextBoxColumn();
      this.Column3 = new DataGridViewTextBoxColumn();
      this.Column4 = new DataGridViewTextBoxColumn();
      this.Column_SlideID = new DataGridViewTextBoxColumn();
      this.label2 = new Label();
      this.label3 = new Label();
      this.label4 = new Label();
      this.label5 = new Label();
      this.label_View_Item = new Label();
      this.label_View_Model = new Label();
      this.newButton_View_Select = new NewButton();
      this.newButton_View_All = new NewButton();
      this.newTextBox_View_Z = new NewTextBox();
      this.newTextBox_View_Y = new NewTextBox();
      this.newTextBox_View_X = new NewTextBox();
      this.newComboBox_View_Type = new NewComboBox();
      this.panel1 = new Panel();
      this.newButton_Apply = new NewButton();
      this.newButton_View = new NewButton();
      this.newButton_Input = new NewButton();
      this.tabControl_Main.SuspendLayout();
      this.tabPage_Input.SuspendLayout();
      this.panel3.SuspendLayout();
      this.tabPage_View.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_View).BeginInit();
      this.panel1.SuspendLayout();
      this.SuspendLayout();
      this.tabControl_Main.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.tabControl_Main.Controls.Add((Control) this.tabPage_Input);
      this.tabControl_Main.Controls.Add((Control) this.tabPage_View);
      this.tabControl_Main.Location = new Point(-3, -23);
      this.tabControl_Main.Name = "tabControl_Main";
      this.tabControl_Main.SelectedIndex = 0;
      this.tabControl_Main.Size = new Size(603, 976);
      this.tabControl_Main.TabIndex = 0;
      this.tabPage_Input.Controls.Add((Control) this.panel3);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_SinkMarkMax);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_SinkMarkMax);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_ShrinkageOffset1);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_ShrinkageOffset2);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_DefScale);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_SinkMarkMin);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_DefScale);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_ShrinkageOffset2);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_ShrinkageOffset1);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_SinkMarkMin);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_ScaleValue);
      this.tabPage_Input.Controls.Add((Control) this.checkBox_Input_InjCond);
      this.tabPage_Input.Controls.Add((Control) this.newComboBox_Input_DefZ);
      this.tabPage_Input.Controls.Add((Control) this.newComboBox_Input_DefY);
      this.tabPage_Input.Controls.Add((Control) this.newComboBox_Input_DefX);
      this.tabPage_Input.Controls.Add((Control) this.newComboBox_Input_DefAll);
      this.tabPage_Input.Controls.Add((Control) this.newComboBox_Input_Balance);
      this.tabPage_Input.Controls.Add((Control) this.newComboBox_Input_Cooling);
      this.tabPage_Input.Controls.Add((Control) this.newComboBox_Input_ShrinkageSink);
      this.tabPage_Input.Controls.Add((Control) this.newComboBox_Input_AirTrap);
      this.tabPage_Input.Controls.Add((Control) this.newComboBox_Input_WeldLine);
      this.tabPage_Input.Controls.Add((Control) this.label29);
      this.tabPage_Input.Controls.Add((Control) this.label28);
      this.tabPage_Input.Controls.Add((Control) this.label31);
      this.tabPage_Input.Controls.Add((Control) this.label32);
      this.tabPage_Input.Controls.Add((Control) this.label26);
      this.tabPage_Input.Controls.Add((Control) this.label27);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_Schizonepeta);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_Manager);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_CoolingTime);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_Engineer);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_FillingFrame);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_PackingTime);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_Sequence);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_Countermeasure);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_DefZ);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_DefY);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_DefX);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_DefAll);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_Balance);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_Cooling);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_ShrinkageSink);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_AirTrap);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_WeldLine);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_FllingTime);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_Item);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_InjRangeVP);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_InjRange4);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_InjRange3);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_MoldOpen);
      this.tabPage_Input.Controls.Add((Control) this.label10);
      this.tabPage_Input.Controls.Add((Control) this.label35);
      this.tabPage_Input.Controls.Add((Control) this.label11);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_InjRange2);
      this.tabPage_Input.Controls.Add((Control) this.label34);
      this.tabPage_Input.Controls.Add((Control) this.label53);
      this.tabPage_Input.Controls.Add((Control) this.label38);
      this.tabPage_Input.Controls.Add((Control) this.label43);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Filling8);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Frozen4);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Filling4);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Filling7);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Frozen3);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Filling3);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Filling6);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Frozen2);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Filling2);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Filling5);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Frozen1);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Filling1);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_ShrinkageMax);
      this.tabPage_Input.Controls.Add((Control) this.label67);
      this.tabPage_Input.Controls.Add((Control) this.label66);
      this.tabPage_Input.Controls.Add((Control) this.label64);
      this.tabPage_Input.Controls.Add((Control) this.label60);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_SurfaceMaxTemp);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_ShrinkageMin);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_SurfaceMinTemp);
      this.tabPage_Input.Controls.Add((Control) this.label33);
      this.tabPage_Input.Controls.Add((Control) this.label13);
      this.tabPage_Input.Controls.Add((Control) this.label14);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_InjRange1);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_InjPreRatio);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_InjMaxClamp);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_InjMaxPressure);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_InjRangeVP);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_InjRange4);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_InjRange3);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_InjScrewDia);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_InjRange2);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_Filling8);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_InjMaxRate);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_Filling7);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_Frozen4);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_Filling4);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_Filling6);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_Frozen3);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_ShrinkageMax);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_Filling3);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_ShrinkageMin);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_MoldTempMax);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_MoldTempMin);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_Filling5);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_Frozen2);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_Filling2);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_Frozen1);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_Filling1);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_InjRange1);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_InjMaxStroke);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_InjPreRatio);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_InjScrewDia);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_InjMaxClamp);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_InjMaxRate);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_InjMaxPressure);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_InjMaxStroke);
      this.tabPage_Input.Controls.Add((Control) this.newComboBox_Input_InjData);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Deflection);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_SurfShrinkage);
      this.tabPage_Input.Controls.Add((Control) this.label48);
      this.tabPage_Input.Controls.Add((Control) this.label37);
      this.tabPage_Input.Controls.Add((Control) this.label30);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Result_Comment);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Sign);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Range);
      this.tabPage_Input.Controls.Add((Control) this.label61);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Inj);
      this.tabPage_Input.Controls.Add((Control) this.panel2);
      this.tabPage_Input.Location = new Point(4, 24);
      this.tabPage_Input.Name = "tabPage_Input";
      this.tabPage_Input.Padding = new Padding(3);
      this.tabPage_Input.Size = new Size(595, 948);
      this.tabPage_Input.TabIndex = 0;
      this.tabPage_Input.Text = "tabPage1";
      this.tabPage_Input.UseVisualStyleBackColor = true;
      this.panel3.BackColor = Color.FromArgb(229, 238, 248);
      this.panel3.BorderStyle = BorderStyle.FixedSingle;
      this.panel3.Controls.Add((Control) this.radioButton_ProductData);
      this.panel3.Controls.Add((Control) this.radioButton_MoldData);
      this.panel3.Location = new Point(317, 106);
      this.panel3.Name = "panel3";
      this.panel3.Size = new Size(274, 25);
      this.panel3.TabIndex = 167;
      this.radioButton_ProductData.AutoSize = true;
      this.radioButton_ProductData.BackColor = Color.FromArgb(229, 238, 248);
      this.radioButton_ProductData.Location = new Point(31, 2);
      this.radioButton_ProductData.Name = "radioButton_ProductData";
      this.radioButton_ProductData.Size = new Size(85, 19);
      this.radioButton_ProductData.TabIndex = 165;
      this.radioButton_ProductData.TabStop = true;
      this.radioButton_ProductData.Text = "제품데이터";
      this.radioButton_ProductData.UseVisualStyleBackColor = false;
      this.radioButton_MoldData.AutoSize = true;
      this.radioButton_MoldData.BackColor = Color.FromArgb(229, 238, 248);
      this.radioButton_MoldData.Location = new Point(156, 2);
      this.radioButton_MoldData.Name = "radioButton_MoldData";
      this.radioButton_MoldData.Size = new Size(85, 19);
      this.radioButton_MoldData.TabIndex = 166;
      this.radioButton_MoldData.TabStop = true;
      this.radioButton_MoldData.Text = "금형데이터";
      this.radioButton_MoldData.UseVisualStyleBackColor = false;
      this.newTextBox_Input_SinkMarkMax.BackColor = Color.White;
      this.newTextBox_Input_SinkMarkMax.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_SinkMarkMax.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_SinkMarkMax.IsDigit = false;
      this.newTextBox_Input_SinkMarkMax.Lines = new string[0];
      this.newTextBox_Input_SinkMarkMax.Location = new Point(193, 792);
      this.newTextBox_Input_SinkMarkMax.MultiLine = false;
      this.newTextBox_Input_SinkMarkMax.Name = "newTextBox_Input_SinkMarkMax";
      this.newTextBox_Input_SinkMarkMax.ReadOnly = false;
      this.newTextBox_Input_SinkMarkMax.Size = new Size(105, 23);
      this.newTextBox_Input_SinkMarkMax.TabIndex = 163;
      this.newTextBox_Input_SinkMarkMax.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_SinkMarkMax.TextBoxBackColor = Color.White;
      this.newTextBox_Input_SinkMarkMax.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_SinkMarkMax.Value = "";
      this.label_Input_SinkMarkMax.BackColor = Color.Lavender;
      this.label_Input_SinkMarkMax.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_SinkMarkMax.Location = new Point(5, 792);
      this.label_Input_SinkMarkMax.Name = "label_Input_SinkMarkMax";
      this.label_Input_SinkMarkMax.Size = new Size(189, 23);
      this.label_Input_SinkMarkMax.TabIndex = 162;
      this.label_Input_SinkMarkMax.Text = "Sink Mark 최대 값";
      this.label_Input_SinkMarkMax.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Input_ShrinkageOffset1.BackColor = Color.White;
      this.newTextBox_Input_ShrinkageOffset1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_ShrinkageOffset1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_ShrinkageOffset1.IsDigit = false;
      this.newTextBox_Input_ShrinkageOffset1.Lines = new string[0];
      this.newTextBox_Input_ShrinkageOffset1.Location = new Point(485, 770);
      this.newTextBox_Input_ShrinkageOffset1.MultiLine = false;
      this.newTextBox_Input_ShrinkageOffset1.Name = "newTextBox_Input_ShrinkageOffset1";
      this.newTextBox_Input_ShrinkageOffset1.ReadOnly = false;
      this.newTextBox_Input_ShrinkageOffset1.Size = new Size(106, 23);
      this.newTextBox_Input_ShrinkageOffset1.TabIndex = 161;
      this.newTextBox_Input_ShrinkageOffset1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_ShrinkageOffset1.TextBoxBackColor = Color.White;
      this.newTextBox_Input_ShrinkageOffset1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_ShrinkageOffset1.Value = "";
      this.newTextBox_Input_ShrinkageOffset2.BackColor = Color.White;
      this.newTextBox_Input_ShrinkageOffset2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_ShrinkageOffset2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_ShrinkageOffset2.IsDigit = false;
      this.newTextBox_Input_ShrinkageOffset2.Lines = new string[0];
      this.newTextBox_Input_ShrinkageOffset2.Location = new Point(485, 792);
      this.newTextBox_Input_ShrinkageOffset2.MultiLine = false;
      this.newTextBox_Input_ShrinkageOffset2.Name = "newTextBox_Input_ShrinkageOffset2";
      this.newTextBox_Input_ShrinkageOffset2.ReadOnly = false;
      this.newTextBox_Input_ShrinkageOffset2.Size = new Size(106, 23);
      this.newTextBox_Input_ShrinkageOffset2.TabIndex = 160;
      this.newTextBox_Input_ShrinkageOffset2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_ShrinkageOffset2.TextBoxBackColor = Color.White;
      this.newTextBox_Input_ShrinkageOffset2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_ShrinkageOffset2.Value = "";
      this.newTextBox_Input_DefScale.BackColor = Color.White;
      this.newTextBox_Input_DefScale.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_DefScale.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_DefScale.IsDigit = false;
      this.newTextBox_Input_DefScale.Lines = new string[0];
      this.newTextBox_Input_DefScale.Location = new Point(193, 814);
      this.newTextBox_Input_DefScale.MultiLine = false;
      this.newTextBox_Input_DefScale.Name = "newTextBox_Input_DefScale";
      this.newTextBox_Input_DefScale.ReadOnly = false;
      this.newTextBox_Input_DefScale.Size = new Size(105, 23);
      this.newTextBox_Input_DefScale.TabIndex = 158;
      this.newTextBox_Input_DefScale.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_DefScale.TextBoxBackColor = Color.White;
      this.newTextBox_Input_DefScale.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_DefScale.Value = "";
      this.newTextBox_Input_SinkMarkMin.BackColor = Color.White;
      this.newTextBox_Input_SinkMarkMin.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_SinkMarkMin.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_SinkMarkMin.IsDigit = false;
      this.newTextBox_Input_SinkMarkMin.Lines = new string[0];
      this.newTextBox_Input_SinkMarkMin.Location = new Point(193, 770);
      this.newTextBox_Input_SinkMarkMin.MultiLine = false;
      this.newTextBox_Input_SinkMarkMin.Name = "newTextBox_Input_SinkMarkMin";
      this.newTextBox_Input_SinkMarkMin.ReadOnly = false;
      this.newTextBox_Input_SinkMarkMin.Size = new Size(105, 23);
      this.newTextBox_Input_SinkMarkMin.TabIndex = 157;
      this.newTextBox_Input_SinkMarkMin.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_SinkMarkMin.TextBoxBackColor = Color.White;
      this.newTextBox_Input_SinkMarkMin.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_SinkMarkMin.Value = "";
      this.label_Input_DefScale.BackColor = Color.Lavender;
      this.label_Input_DefScale.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_DefScale.Location = new Point(5, 814);
      this.label_Input_DefScale.Name = "label_Input_DefScale";
      this.label_Input_DefScale.Size = new Size(189, 23);
      this.label_Input_DefScale.TabIndex = 156;
      this.label_Input_DefScale.Text = "Deflection 조정 값";
      this.label_Input_DefScale.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_ShrinkageOffset2.BackColor = Color.Lavender;
      this.label_Input_ShrinkageOffset2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_ShrinkageOffset2.Location = new Point(297, 792);
      this.label_Input_ShrinkageOffset2.Name = "label_Input_ShrinkageOffset2";
      this.label_Input_ShrinkageOffset2.Size = new Size(189, 23);
      this.label_Input_ShrinkageOffset2.TabIndex = 154;
      this.label_Input_ShrinkageOffset2.Text = "수축률 조정 값 - 2";
      this.label_Input_ShrinkageOffset2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_ShrinkageOffset1.BackColor = Color.Lavender;
      this.label_Input_ShrinkageOffset1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_ShrinkageOffset1.Location = new Point(297, 770);
      this.label_Input_ShrinkageOffset1.Name = "label_Input_ShrinkageOffset1";
      this.label_Input_ShrinkageOffset1.Size = new Size(189, 23);
      this.label_Input_ShrinkageOffset1.TabIndex = 151;
      this.label_Input_ShrinkageOffset1.Text = "수축률 조정 값 - 1";
      this.label_Input_ShrinkageOffset1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_SinkMarkMin.BackColor = Color.Lavender;
      this.label_Input_SinkMarkMin.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_SinkMarkMin.Location = new Point(5, 770);
      this.label_Input_SinkMarkMin.Name = "label_Input_SinkMarkMin";
      this.label_Input_SinkMarkMin.Size = new Size(189, 23);
      this.label_Input_SinkMarkMin.TabIndex = 152;
      this.label_Input_SinkMarkMin.Text = "Sink Mark 최소 값";
      this.label_Input_SinkMarkMin.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_ScaleValue.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Input_ScaleValue.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_ScaleValue.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Input_ScaleValue.ForeColor = Color.MidnightBlue;
      this.label_Input_ScaleValue.Location = new Point(5, 751);
      this.label_Input_ScaleValue.Name = "label_Input_ScaleValue";
      this.label_Input_ScaleValue.Size = new Size(586, 20);
      this.label_Input_ScaleValue.TabIndex = 148;
      this.label_Input_ScaleValue.Text = "Scale Value";
      this.label_Input_ScaleValue.TextAlign = ContentAlignment.MiddleCenter;
      this.checkBox_Input_InjCond.BackColor = Color.FromArgb(229, 238, 248);
      this.checkBox_Input_InjCond.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.checkBox_Input_InjCond.Location = new Point(247, 4);
      this.checkBox_Input_InjCond.Name = "checkBox_Input_InjCond";
      this.checkBox_Input_InjCond.Size = new Size(103, 17);
      this.checkBox_Input_InjCond.TabIndex = 147;
      this.checkBox_Input_InjCond.Text = "사출 조건표";
      this.checkBox_Input_InjCond.UseVisualStyleBackColor = false;
      this.newComboBox_Input_DefZ.BackColor = Color.White;
      this.newComboBox_Input_DefZ.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Input_DefZ.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_Input_DefZ.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Input_DefZ.isSameSelect = false;
      this.newComboBox_Input_DefZ.Location = new Point(108, 921);
      this.newComboBox_Input_DefZ.Name = "newComboBox_Input_DefZ";
      this.newComboBox_Input_DefZ.SelectedIndex = -1;
      this.newComboBox_Input_DefZ.Size = new Size(130, 23);
      this.newComboBox_Input_DefZ.TabIndex = 146;
      this.newComboBox_Input_DefZ.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Input_DefZ.Value = "";
      this.newComboBox_Input_DefY.BackColor = Color.White;
      this.newComboBox_Input_DefY.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Input_DefY.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_Input_DefY.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Input_DefY.isSameSelect = false;
      this.newComboBox_Input_DefY.Location = new Point(108, 899);
      this.newComboBox_Input_DefY.Name = "newComboBox_Input_DefY";
      this.newComboBox_Input_DefY.SelectedIndex = -1;
      this.newComboBox_Input_DefY.Size = new Size(130, 23);
      this.newComboBox_Input_DefY.TabIndex = 146;
      this.newComboBox_Input_DefY.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Input_DefY.Value = "";
      this.newComboBox_Input_DefX.BackColor = Color.White;
      this.newComboBox_Input_DefX.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Input_DefX.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_Input_DefX.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Input_DefX.isSameSelect = false;
      this.newComboBox_Input_DefX.Location = new Point(108, 877);
      this.newComboBox_Input_DefX.Name = "newComboBox_Input_DefX";
      this.newComboBox_Input_DefX.SelectedIndex = -1;
      this.newComboBox_Input_DefX.Size = new Size(130, 23);
      this.newComboBox_Input_DefX.TabIndex = 146;
      this.newComboBox_Input_DefX.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Input_DefX.Value = "";
      this.newComboBox_Input_DefAll.BackColor = Color.White;
      this.newComboBox_Input_DefAll.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Input_DefAll.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_Input_DefAll.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Input_DefAll.isSameSelect = false;
      this.newComboBox_Input_DefAll.Location = new Point(108, 855);
      this.newComboBox_Input_DefAll.Name = "newComboBox_Input_DefAll";
      this.newComboBox_Input_DefAll.SelectedIndex = -1;
      this.newComboBox_Input_DefAll.Size = new Size(130, 23);
      this.newComboBox_Input_DefAll.TabIndex = 146;
      this.newComboBox_Input_DefAll.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Input_DefAll.Value = "";
      this.newComboBox_Input_Balance.BackColor = Color.White;
      this.newComboBox_Input_Balance.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Input_Balance.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_Input_Balance.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Input_Balance.isSameSelect = false;
      this.newComboBox_Input_Balance.Location = new Point(108, 366);
      this.newComboBox_Input_Balance.Name = "newComboBox_Input_Balance";
      this.newComboBox_Input_Balance.SelectedIndex = -1;
      this.newComboBox_Input_Balance.Size = new Size(130, 23);
      this.newComboBox_Input_Balance.TabIndex = 146;
      this.newComboBox_Input_Balance.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Input_Balance.Value = "";
      this.newComboBox_Input_Balance.SelectedIndexChanged += new EventHandler(this.newComboBox_Input_Balance_SelectedIndexChanged);
      this.newComboBox_Input_Cooling.BackColor = Color.White;
      this.newComboBox_Input_Cooling.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Input_Cooling.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_Input_Cooling.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Input_Cooling.isSameSelect = false;
      this.newComboBox_Input_Cooling.Location = new Point(108, 344);
      this.newComboBox_Input_Cooling.Name = "newComboBox_Input_Cooling";
      this.newComboBox_Input_Cooling.SelectedIndex = -1;
      this.newComboBox_Input_Cooling.Size = new Size(130, 23);
      this.newComboBox_Input_Cooling.TabIndex = 146;
      this.newComboBox_Input_Cooling.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Input_Cooling.Value = "";
      this.newComboBox_Input_Cooling.SelectedIndexChanged += new EventHandler(this.newComboBox_Input_Cooling_SelectedIndexChanged);
      this.newComboBox_Input_ShrinkageSink.BackColor = Color.White;
      this.newComboBox_Input_ShrinkageSink.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Input_ShrinkageSink.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_Input_ShrinkageSink.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Input_ShrinkageSink.isSameSelect = false;
      this.newComboBox_Input_ShrinkageSink.Location = new Point(108, 322);
      this.newComboBox_Input_ShrinkageSink.Name = "newComboBox_Input_ShrinkageSink";
      this.newComboBox_Input_ShrinkageSink.SelectedIndex = -1;
      this.newComboBox_Input_ShrinkageSink.Size = new Size(130, 23);
      this.newComboBox_Input_ShrinkageSink.TabIndex = 146;
      this.newComboBox_Input_ShrinkageSink.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Input_ShrinkageSink.Value = "";
      this.newComboBox_Input_ShrinkageSink.SelectedIndexChanged += new EventHandler(this.newComboBox_Input_ShrinkageSink_SelectedIndexChanged);
      this.newComboBox_Input_AirTrap.BackColor = Color.White;
      this.newComboBox_Input_AirTrap.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Input_AirTrap.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_Input_AirTrap.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Input_AirTrap.isSameSelect = false;
      this.newComboBox_Input_AirTrap.Location = new Point(108, 300);
      this.newComboBox_Input_AirTrap.Name = "newComboBox_Input_AirTrap";
      this.newComboBox_Input_AirTrap.SelectedIndex = -1;
      this.newComboBox_Input_AirTrap.Size = new Size(130, 23);
      this.newComboBox_Input_AirTrap.TabIndex = 146;
      this.newComboBox_Input_AirTrap.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Input_AirTrap.Value = "";
      this.newComboBox_Input_AirTrap.SelectedIndexChanged += new EventHandler(this.newComboBox_Input_AirTrap_SelectedIndexChanged);
      this.newComboBox_Input_WeldLine.BackColor = Color.White;
      this.newComboBox_Input_WeldLine.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Input_WeldLine.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_Input_WeldLine.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Input_WeldLine.isSameSelect = false;
      this.newComboBox_Input_WeldLine.Location = new Point(108, 278);
      this.newComboBox_Input_WeldLine.Name = "newComboBox_Input_WeldLine";
      this.newComboBox_Input_WeldLine.SelectedIndex = -1;
      this.newComboBox_Input_WeldLine.Size = new Size(130, 23);
      this.newComboBox_Input_WeldLine.TabIndex = 146;
      this.newComboBox_Input_WeldLine.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Input_WeldLine.Value = "";
      this.newComboBox_Input_WeldLine.SelectedIndexChanged += new EventHandler(this.newComboBox_Input_WeldLine_SelectedIndexChanged);
      this.label29.BackColor = Color.Lavender;
      this.label29.BorderStyle = BorderStyle.FixedSingle;
      this.label29.Location = new Point(5, 300);
      this.label29.Name = "label29";
      this.label29.Size = new Size(104, 23);
      this.label29.TabIndex = 142;
      this.label29.Text = "Air trap";
      this.label29.TextAlign = ContentAlignment.MiddleCenter;
      this.label28.BackColor = Color.Lavender;
      this.label28.BorderStyle = BorderStyle.FixedSingle;
      this.label28.Location = new Point(5, 388);
      this.label28.Name = "label28";
      this.label28.Size = new Size(586, 23);
      this.label28.TabIndex = 143;
      this.label28.Text = "Countermeasure - recomandation";
      this.label28.TextAlign = ContentAlignment.MiddleCenter;
      this.label31.BackColor = Color.Lavender;
      this.label31.BorderStyle = BorderStyle.FixedSingle;
      this.label31.Location = new Point(5, 366);
      this.label31.Name = "label31";
      this.label31.Size = new Size(104, 23);
      this.label31.TabIndex = 143;
      this.label31.Text = "Balance";
      this.label31.TextAlign = ContentAlignment.MiddleCenter;
      this.label32.BackColor = Color.Lavender;
      this.label32.BorderStyle = BorderStyle.FixedSingle;
      this.label32.Location = new Point(5, 344);
      this.label32.Name = "label32";
      this.label32.Size = new Size(104, 23);
      this.label32.TabIndex = 143;
      this.label32.Text = "Cooling";
      this.label32.TextAlign = ContentAlignment.MiddleCenter;
      this.label26.BackColor = Color.Lavender;
      this.label26.BorderStyle = BorderStyle.FixedSingle;
      this.label26.Location = new Point(5, 322);
      this.label26.Name = "label26";
      this.label26.Size = new Size(104, 23);
      this.label26.TabIndex = 144;
      this.label26.Text = "Shrinkage/Sink";
      this.label26.TextAlign = ContentAlignment.MiddleCenter;
      this.label27.BackColor = Color.Lavender;
      this.label27.BorderStyle = BorderStyle.FixedSingle;
      this.label27.Location = new Point(5, 278);
      this.label27.Name = "label27";
      this.label27.Size = new Size(104, 23);
      this.label27.TabIndex = 145;
      this.label27.Text = "Weld line";
      this.label27.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Input_Schizonepeta.BackColor = SystemColors.Window;
      this.newTextBox_Input_Schizonepeta.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_Schizonepeta.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_Schizonepeta.IsDigit = false;
      this.newTextBox_Input_Schizonepeta.Lines = new string[0];
      this.newTextBox_Input_Schizonepeta.Location = new Point(443, 496);
      this.newTextBox_Input_Schizonepeta.MultiLine = false;
      this.newTextBox_Input_Schizonepeta.Name = "newTextBox_Input_Schizonepeta";
      this.newTextBox_Input_Schizonepeta.ReadOnly = false;
      this.newTextBox_Input_Schizonepeta.Size = new Size(148, 23);
      this.newTextBox_Input_Schizonepeta.TabIndex = 141;
      this.newTextBox_Input_Schizonepeta.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_Schizonepeta.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Input_Schizonepeta.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_Schizonepeta.Value = "";
      this.newTextBox_Input_Manager.BackColor = SystemColors.Window;
      this.newTextBox_Input_Manager.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_Manager.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_Manager.IsDigit = false;
      this.newTextBox_Input_Manager.Lines = new string[0];
      this.newTextBox_Input_Manager.Location = new Point(395, 237);
      this.newTextBox_Input_Manager.MultiLine = false;
      this.newTextBox_Input_Manager.Name = "newTextBox_Input_Manager";
      this.newTextBox_Input_Manager.ReadOnly = false;
      this.newTextBox_Input_Manager.Size = new Size(196, 23);
      this.newTextBox_Input_Manager.TabIndex = 141;
      this.newTextBox_Input_Manager.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_Manager.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Input_Manager.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_Manager.Value = "";
      this.newTextBox_Input_CoolingTime.BackColor = SystemColors.Window;
      this.newTextBox_Input_CoolingTime.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_CoolingTime.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_CoolingTime.IsDigit = false;
      this.newTextBox_Input_CoolingTime.Lines = new string[0];
      this.newTextBox_Input_CoolingTime.Location = new Point(297, 496);
      this.newTextBox_Input_CoolingTime.MultiLine = false;
      this.newTextBox_Input_CoolingTime.Name = "newTextBox_Input_CoolingTime";
      this.newTextBox_Input_CoolingTime.ReadOnly = false;
      this.newTextBox_Input_CoolingTime.Size = new Size(147, 23);
      this.newTextBox_Input_CoolingTime.TabIndex = 140;
      this.newTextBox_Input_CoolingTime.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_CoolingTime.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Input_CoolingTime.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_CoolingTime.Value = "";
      this.newTextBox_Input_Engineer.BackColor = SystemColors.Window;
      this.newTextBox_Input_Engineer.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_Engineer.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_Engineer.IsDigit = false;
      this.newTextBox_Input_Engineer.Lines = new string[0];
      this.newTextBox_Input_Engineer.Location = new Point(200, 237);
      this.newTextBox_Input_Engineer.MultiLine = false;
      this.newTextBox_Input_Engineer.Name = "newTextBox_Input_Engineer";
      this.newTextBox_Input_Engineer.ReadOnly = false;
      this.newTextBox_Input_Engineer.Size = new Size(196, 23);
      this.newTextBox_Input_Engineer.TabIndex = 140;
      this.newTextBox_Input_Engineer.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_Engineer.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Input_Engineer.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_Engineer.Value = "";
      this.newTextBox_Input_FillingFrame.BackColor = Color.White;
      this.newTextBox_Input_FillingFrame.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_FillingFrame.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_FillingFrame.IsDigit = true;
      this.newTextBox_Input_FillingFrame.Lines = new string[0];
      this.newTextBox_Input_FillingFrame.Location = new Point(505, 603);
      this.newTextBox_Input_FillingFrame.MultiLine = false;
      this.newTextBox_Input_FillingFrame.Name = "newTextBox_Input_FillingFrame";
      this.newTextBox_Input_FillingFrame.ReadOnly = false;
      this.newTextBox_Input_FillingFrame.Size = new Size(86, 23);
      this.newTextBox_Input_FillingFrame.TabIndex = 139;
      this.newTextBox_Input_FillingFrame.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_FillingFrame.TextBoxBackColor = Color.White;
      this.newTextBox_Input_FillingFrame.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_FillingFrame.Value = "";
      this.newTextBox_Input_PackingTime.BackColor = Color.White;
      this.newTextBox_Input_PackingTime.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_PackingTime.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_PackingTime.IsDigit = false;
      this.newTextBox_Input_PackingTime.Lines = new string[0];
      this.newTextBox_Input_PackingTime.Location = new Point(151, 496);
      this.newTextBox_Input_PackingTime.MultiLine = false;
      this.newTextBox_Input_PackingTime.Name = "newTextBox_Input_PackingTime";
      this.newTextBox_Input_PackingTime.ReadOnly = false;
      this.newTextBox_Input_PackingTime.Size = new Size(147, 23);
      this.newTextBox_Input_PackingTime.TabIndex = 139;
      this.newTextBox_Input_PackingTime.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_PackingTime.TextBoxBackColor = Color.White;
      this.newTextBox_Input_PackingTime.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_PackingTime.Value = "";
      this.newTextBox_Input_Sequence.BackColor = Color.LavenderBlush;
      this.newTextBox_Input_Sequence.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_Sequence.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_Sequence.IsDigit = false;
      this.newTextBox_Input_Sequence.Lines = new string[0];
      this.newTextBox_Input_Sequence.Location = new Point(5, 237);
      this.newTextBox_Input_Sequence.MultiLine = false;
      this.newTextBox_Input_Sequence.Name = "newTextBox_Input_Sequence";
      this.newTextBox_Input_Sequence.ReadOnly = true;
      this.newTextBox_Input_Sequence.Size = new Size(196, 23);
      this.newTextBox_Input_Sequence.TabIndex = 139;
      this.newTextBox_Input_Sequence.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_Sequence.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_Input_Sequence.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_Sequence.Value = "";
      this.newTextBox_Input_Countermeasure.BackColor = Color.White;
      this.newTextBox_Input_Countermeasure.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_Countermeasure.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_Countermeasure.IsDigit = false;
      this.newTextBox_Input_Countermeasure.Lines = new string[0];
      this.newTextBox_Input_Countermeasure.Location = new Point(5, 409);
      this.newTextBox_Input_Countermeasure.MultiLine = true;
      this.newTextBox_Input_Countermeasure.Name = "newTextBox_Input_Countermeasure";
      this.newTextBox_Input_Countermeasure.ReadOnly = false;
      this.newTextBox_Input_Countermeasure.Size = new Size(586, 47);
      this.newTextBox_Input_Countermeasure.TabIndex = 138;
      this.newTextBox_Input_Countermeasure.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_Countermeasure.TextBoxBackColor = Color.White;
      this.newTextBox_Input_Countermeasure.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_Countermeasure.Value = "";
      this.newTextBox_Input_DefZ.BackColor = Color.White;
      this.newTextBox_Input_DefZ.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_DefZ.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_DefZ.IsDigit = false;
      this.newTextBox_Input_DefZ.Lines = new string[0];
      this.newTextBox_Input_DefZ.Location = new Point(237, 921);
      this.newTextBox_Input_DefZ.MultiLine = false;
      this.newTextBox_Input_DefZ.Name = "newTextBox_Input_DefZ";
      this.newTextBox_Input_DefZ.ReadOnly = false;
      this.newTextBox_Input_DefZ.Size = new Size(354, 23);
      this.newTextBox_Input_DefZ.TabIndex = 138;
      this.newTextBox_Input_DefZ.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_DefZ.TextBoxBackColor = Color.White;
      this.newTextBox_Input_DefZ.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_DefZ.Value = "";
      this.newTextBox_Input_DefY.BackColor = Color.White;
      this.newTextBox_Input_DefY.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_DefY.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_DefY.IsDigit = false;
      this.newTextBox_Input_DefY.Lines = new string[0];
      this.newTextBox_Input_DefY.Location = new Point(237, 899);
      this.newTextBox_Input_DefY.MultiLine = false;
      this.newTextBox_Input_DefY.Name = "newTextBox_Input_DefY";
      this.newTextBox_Input_DefY.ReadOnly = false;
      this.newTextBox_Input_DefY.Size = new Size(354, 23);
      this.newTextBox_Input_DefY.TabIndex = 138;
      this.newTextBox_Input_DefY.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_DefY.TextBoxBackColor = Color.White;
      this.newTextBox_Input_DefY.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_DefY.Value = "";
      this.newTextBox_Input_DefX.BackColor = Color.White;
      this.newTextBox_Input_DefX.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_DefX.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_DefX.IsDigit = false;
      this.newTextBox_Input_DefX.Lines = new string[0];
      this.newTextBox_Input_DefX.Location = new Point(237, 877);
      this.newTextBox_Input_DefX.MultiLine = false;
      this.newTextBox_Input_DefX.Name = "newTextBox_Input_DefX";
      this.newTextBox_Input_DefX.ReadOnly = false;
      this.newTextBox_Input_DefX.Size = new Size(354, 23);
      this.newTextBox_Input_DefX.TabIndex = 138;
      this.newTextBox_Input_DefX.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_DefX.TextBoxBackColor = Color.White;
      this.newTextBox_Input_DefX.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_DefX.Value = "";
      this.newTextBox_Input_DefAll.BackColor = Color.White;
      this.newTextBox_Input_DefAll.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_DefAll.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_DefAll.IsDigit = false;
      this.newTextBox_Input_DefAll.Lines = new string[0];
      this.newTextBox_Input_DefAll.Location = new Point(237, 855);
      this.newTextBox_Input_DefAll.MultiLine = false;
      this.newTextBox_Input_DefAll.Name = "newTextBox_Input_DefAll";
      this.newTextBox_Input_DefAll.ReadOnly = false;
      this.newTextBox_Input_DefAll.Size = new Size(354, 23);
      this.newTextBox_Input_DefAll.TabIndex = 138;
      this.newTextBox_Input_DefAll.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_DefAll.TextBoxBackColor = Color.White;
      this.newTextBox_Input_DefAll.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_DefAll.Value = "";
      this.newTextBox_Input_Balance.BackColor = Color.LavenderBlush;
      this.newTextBox_Input_Balance.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_Balance.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_Balance.IsDigit = false;
      this.newTextBox_Input_Balance.Lines = new string[0];
      this.newTextBox_Input_Balance.Location = new Point(237, 366);
      this.newTextBox_Input_Balance.MultiLine = false;
      this.newTextBox_Input_Balance.Name = "newTextBox_Input_Balance";
      this.newTextBox_Input_Balance.ReadOnly = true;
      this.newTextBox_Input_Balance.Size = new Size(354, 23);
      this.newTextBox_Input_Balance.TabIndex = 138;
      this.newTextBox_Input_Balance.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_Balance.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_Input_Balance.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_Balance.Value = "";
      this.newTextBox_Input_Cooling.BackColor = Color.LavenderBlush;
      this.newTextBox_Input_Cooling.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_Cooling.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_Cooling.IsDigit = false;
      this.newTextBox_Input_Cooling.Lines = new string[0];
      this.newTextBox_Input_Cooling.Location = new Point(237, 344);
      this.newTextBox_Input_Cooling.MultiLine = false;
      this.newTextBox_Input_Cooling.Name = "newTextBox_Input_Cooling";
      this.newTextBox_Input_Cooling.ReadOnly = true;
      this.newTextBox_Input_Cooling.Size = new Size(354, 23);
      this.newTextBox_Input_Cooling.TabIndex = 138;
      this.newTextBox_Input_Cooling.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_Cooling.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_Input_Cooling.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_Cooling.Value = "";
      this.newTextBox_Input_ShrinkageSink.BackColor = Color.LavenderBlush;
      this.newTextBox_Input_ShrinkageSink.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_ShrinkageSink.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_ShrinkageSink.IsDigit = false;
      this.newTextBox_Input_ShrinkageSink.Lines = new string[0];
      this.newTextBox_Input_ShrinkageSink.Location = new Point(237, 322);
      this.newTextBox_Input_ShrinkageSink.MultiLine = false;
      this.newTextBox_Input_ShrinkageSink.Name = "newTextBox_Input_ShrinkageSink";
      this.newTextBox_Input_ShrinkageSink.ReadOnly = true;
      this.newTextBox_Input_ShrinkageSink.Size = new Size(354, 23);
      this.newTextBox_Input_ShrinkageSink.TabIndex = 138;
      this.newTextBox_Input_ShrinkageSink.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_ShrinkageSink.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_Input_ShrinkageSink.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_ShrinkageSink.Value = "";
      this.newTextBox_Input_AirTrap.BackColor = Color.LavenderBlush;
      this.newTextBox_Input_AirTrap.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_AirTrap.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_AirTrap.IsDigit = false;
      this.newTextBox_Input_AirTrap.Lines = new string[0];
      this.newTextBox_Input_AirTrap.Location = new Point(237, 300);
      this.newTextBox_Input_AirTrap.MultiLine = false;
      this.newTextBox_Input_AirTrap.Name = "newTextBox_Input_AirTrap";
      this.newTextBox_Input_AirTrap.ReadOnly = true;
      this.newTextBox_Input_AirTrap.Size = new Size(354, 23);
      this.newTextBox_Input_AirTrap.TabIndex = 138;
      this.newTextBox_Input_AirTrap.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_AirTrap.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_Input_AirTrap.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_AirTrap.Value = "";
      this.newTextBox_Input_WeldLine.BackColor = Color.LavenderBlush;
      this.newTextBox_Input_WeldLine.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_WeldLine.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_WeldLine.IsDigit = false;
      this.newTextBox_Input_WeldLine.Lines = new string[0];
      this.newTextBox_Input_WeldLine.Location = new Point(237, 278);
      this.newTextBox_Input_WeldLine.MultiLine = false;
      this.newTextBox_Input_WeldLine.Name = "newTextBox_Input_WeldLine";
      this.newTextBox_Input_WeldLine.ReadOnly = true;
      this.newTextBox_Input_WeldLine.Size = new Size(354, 23);
      this.newTextBox_Input_WeldLine.TabIndex = 138;
      this.newTextBox_Input_WeldLine.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_WeldLine.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_Input_WeldLine.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_WeldLine.Value = "";
      this.newTextBox_Input_FllingTime.BackColor = Color.White;
      this.newTextBox_Input_FllingTime.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_FllingTime.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_FllingTime.IsDigit = false;
      this.newTextBox_Input_FllingTime.Lines = new string[0];
      this.newTextBox_Input_FllingTime.Location = new Point(5, 496);
      this.newTextBox_Input_FllingTime.MultiLine = false;
      this.newTextBox_Input_FllingTime.Name = "newTextBox_Input_FllingTime";
      this.newTextBox_Input_FllingTime.ReadOnly = false;
      this.newTextBox_Input_FllingTime.Size = new Size(147, 23);
      this.newTextBox_Input_FllingTime.TabIndex = 138;
      this.newTextBox_Input_FllingTime.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_FllingTime.TextBoxBackColor = Color.White;
      this.newTextBox_Input_FllingTime.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_FllingTime.Value = "";
      this.newTextBox_Input_Item.BackColor = Color.LavenderBlush;
      this.newTextBox_Input_Item.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_Item.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_Item.IsDigit = false;
      this.newTextBox_Input_Item.Lines = new string[0];
      this.newTextBox_Input_Item.Location = new Point(151, 193);
      this.newTextBox_Input_Item.MultiLine = false;
      this.newTextBox_Input_Item.Name = "newTextBox_Input_Item";
      this.newTextBox_Input_Item.ReadOnly = true;
      this.newTextBox_Input_Item.Size = new Size(440, 23);
      this.newTextBox_Input_Item.TabIndex = 138;
      this.newTextBox_Input_Item.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_Item.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_Input_Item.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_Item.Value = "";
      this.label_Input_InjRangeVP.BackColor = Color.Lavender;
      this.label_Input_InjRangeVP.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_InjRangeVP.Location = new Point(444, 130);
      this.label_Input_InjRangeVP.Name = "label_Input_InjRangeVP";
      this.label_Input_InjRangeVP.Size = new Size(147, 23);
      this.label_Input_InjRangeVP.TabIndex = 129;
      this.label_Input_InjRangeVP.Text = "V/P 절환 위치(Cushion)";
      this.label_Input_InjRangeVP.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_InjRange4.BackColor = Color.Lavender;
      this.label_Input_InjRange4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_InjRange4.Location = new Point(317, 130);
      this.label_Input_InjRange4.Name = "label_Input_InjRange4";
      this.label_Input_InjRange4.Size = new Size(128, 23);
      this.label_Input_InjRange4.TabIndex = 130;
      this.label_Input_InjRange4.Text = "4차(V/P전환시점)";
      this.label_Input_InjRange4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_InjRange3.BackColor = Color.Lavender;
      this.label_Input_InjRange3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_InjRange3.Location = new Point(213, 130);
      this.label_Input_InjRange3.Name = "label_Input_InjRange3";
      this.label_Input_InjRange3.Size = new Size(105, 23);
      this.label_Input_InjRange3.TabIndex = 131;
      this.label_Input_InjRange3.Text = "3차";
      this.label_Input_InjRange3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_MoldOpen.BackColor = Color.Lavender;
      this.label_Input_MoldOpen.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_MoldOpen.Location = new Point(443, 474);
      this.label_Input_MoldOpen.Name = "label_Input_MoldOpen";
      this.label_Input_MoldOpen.Size = new Size(148, 23);
      this.label_Input_MoldOpen.TabIndex = 134;
      this.label_Input_MoldOpen.Text = "형개 오픈(s)";
      this.label_Input_MoldOpen.TextAlign = ContentAlignment.MiddleCenter;
      this.label10.BackColor = Color.Lavender;
      this.label10.BorderStyle = BorderStyle.FixedSingle;
      this.label10.Location = new Point(395, 215);
      this.label10.Name = "label10";
      this.label10.Size = new Size(196, 23);
      this.label10.TabIndex = 134;
      this.label10.Text = "Manager";
      this.label10.TextAlign = ContentAlignment.MiddleCenter;
      this.label35.BackColor = Color.Lavender;
      this.label35.BorderStyle = BorderStyle.FixedSingle;
      this.label35.Location = new Point(151, 474);
      this.label35.Name = "label35";
      this.label35.Size = new Size(147, 23);
      this.label35.TabIndex = 133;
      this.label35.Text = "Packing time(s)";
      this.label35.TextAlign = ContentAlignment.MiddleCenter;
      this.label11.BackColor = Color.Lavender;
      this.label11.BorderStyle = BorderStyle.FixedSingle;
      this.label11.Location = new Point(5, 215);
      this.label11.Name = "label11";
      this.label11.Size = new Size(196, 23);
      this.label11.TabIndex = 133;
      this.label11.Text = "Sequence";
      this.label11.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_InjRange2.BackColor = Color.Lavender;
      this.label_Input_InjRange2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_InjRange2.Location = new Point(109, 130);
      this.label_Input_InjRange2.Name = "label_Input_InjRange2";
      this.label_Input_InjRange2.Size = new Size(105, 23);
      this.label_Input_InjRange2.TabIndex = 132;
      this.label_Input_InjRange2.Text = "2차";
      this.label_Input_InjRange2.TextAlign = ContentAlignment.MiddleCenter;
      this.label34.BackColor = Color.Lavender;
      this.label34.BorderStyle = BorderStyle.FixedSingle;
      this.label34.Location = new Point(297, 474);
      this.label34.Name = "label34";
      this.label34.Size = new Size(147, 23);
      this.label34.TabIndex = 137;
      this.label34.Text = "Cooling time(s)";
      this.label34.TextAlign = ContentAlignment.MiddleCenter;
      this.label53.BackColor = Color.Lavender;
      this.label53.BorderStyle = BorderStyle.FixedSingle;
      this.label53.Location = new Point(5, 644);
      this.label53.Name = "label53";
      this.label53.Size = new Size(85, 45);
      this.label53.TabIndex = 136;
      this.label53.Text = "Frozen\r\nVolume(%)";
      this.label53.TextAlign = ContentAlignment.MiddleCenter;
      this.label38.BackColor = Color.Lavender;
      this.label38.BorderStyle = BorderStyle.FixedSingle;
      this.label38.Location = new Point(5, 537);
      this.label38.Name = "label38";
      this.label38.Size = new Size(85, 89);
      this.label38.TabIndex = 136;
      this.label38.Text = "Filling \r\nVolume(%)";
      this.label38.TextAlign = ContentAlignment.MiddleCenter;
      this.label43.BackColor = Color.Lavender;
      this.label43.BorderStyle = BorderStyle.FixedSingle;
      this.label43.Location = new Point(505, 537);
      this.label43.Name = "label43";
      this.label43.Size = new Size(86, 67);
      this.label43.TabIndex = 136;
      this.label43.Text = "Animation\r\nFrame";
      this.label43.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_Filling8.BackColor = Color.Lavender;
      this.label_Input_Filling8.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Filling8.Location = new Point(401, 581);
      this.label_Input_Filling8.Name = "label_Input_Filling8";
      this.label_Input_Filling8.Size = new Size(105, 23);
      this.label_Input_Filling8.TabIndex = 136;
      this.label_Input_Filling8.Text = "8장";
      this.label_Input_Filling8.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_Frozen4.BackColor = Color.Lavender;
      this.label_Input_Frozen4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Frozen4.Location = new Point(464, 644);
      this.label_Input_Frozen4.Name = "label_Input_Frozen4";
      this.label_Input_Frozen4.Size = new Size((int) sbyte.MaxValue, 23);
      this.label_Input_Frozen4.TabIndex = 136;
      this.label_Input_Frozen4.Text = "4장";
      this.label_Input_Frozen4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_Filling4.BackColor = Color.Lavender;
      this.label_Input_Filling4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Filling4.Location = new Point(401, 537);
      this.label_Input_Filling4.Name = "label_Input_Filling4";
      this.label_Input_Filling4.Size = new Size(105, 23);
      this.label_Input_Filling4.TabIndex = 136;
      this.label_Input_Filling4.Text = "4장";
      this.label_Input_Filling4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_Filling7.BackColor = Color.Lavender;
      this.label_Input_Filling7.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Filling7.Location = new Point(297, 581);
      this.label_Input_Filling7.Name = "label_Input_Filling7";
      this.label_Input_Filling7.Size = new Size(105, 23);
      this.label_Input_Filling7.TabIndex = 136;
      this.label_Input_Filling7.Text = "7장";
      this.label_Input_Filling7.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_Frozen3.BackColor = Color.Lavender;
      this.label_Input_Frozen3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Frozen3.Location = new Point(339, 644);
      this.label_Input_Frozen3.Name = "label_Input_Frozen3";
      this.label_Input_Frozen3.Size = new Size(126, 23);
      this.label_Input_Frozen3.TabIndex = 136;
      this.label_Input_Frozen3.Text = "3장";
      this.label_Input_Frozen3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_Filling3.BackColor = Color.Lavender;
      this.label_Input_Filling3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Filling3.Location = new Point(297, 537);
      this.label_Input_Filling3.Name = "label_Input_Filling3";
      this.label_Input_Filling3.Size = new Size(105, 23);
      this.label_Input_Filling3.TabIndex = 136;
      this.label_Input_Filling3.Text = "3장";
      this.label_Input_Filling3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_Filling6.BackColor = Color.Lavender;
      this.label_Input_Filling6.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Filling6.Location = new Point(193, 581);
      this.label_Input_Filling6.Name = "label_Input_Filling6";
      this.label_Input_Filling6.Size = new Size(105, 23);
      this.label_Input_Filling6.TabIndex = 136;
      this.label_Input_Filling6.Text = "6장";
      this.label_Input_Filling6.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_Frozen2.BackColor = Color.Lavender;
      this.label_Input_Frozen2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Frozen2.Location = new Point(214, 644);
      this.label_Input_Frozen2.Name = "label_Input_Frozen2";
      this.label_Input_Frozen2.Size = new Size(126, 23);
      this.label_Input_Frozen2.TabIndex = 136;
      this.label_Input_Frozen2.Text = "2장";
      this.label_Input_Frozen2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_Filling2.BackColor = Color.Lavender;
      this.label_Input_Filling2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Filling2.Location = new Point(193, 537);
      this.label_Input_Filling2.Name = "label_Input_Filling2";
      this.label_Input_Filling2.Size = new Size(105, 23);
      this.label_Input_Filling2.TabIndex = 136;
      this.label_Input_Filling2.Text = "2장";
      this.label_Input_Filling2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_Filling5.BackColor = Color.Lavender;
      this.label_Input_Filling5.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Filling5.Location = new Point(89, 581);
      this.label_Input_Filling5.Name = "label_Input_Filling5";
      this.label_Input_Filling5.Size = new Size(105, 23);
      this.label_Input_Filling5.TabIndex = 136;
      this.label_Input_Filling5.Text = "5장";
      this.label_Input_Filling5.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_Frozen1.BackColor = Color.Lavender;
      this.label_Input_Frozen1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Frozen1.Location = new Point(89, 644);
      this.label_Input_Frozen1.Name = "label_Input_Frozen1";
      this.label_Input_Frozen1.Size = new Size(126, 23);
      this.label_Input_Frozen1.TabIndex = 136;
      this.label_Input_Frozen1.Text = "1장";
      this.label_Input_Frozen1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_Filling1.BackColor = Color.Lavender;
      this.label_Input_Filling1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Filling1.Location = new Point(89, 537);
      this.label_Input_Filling1.Name = "label_Input_Filling1";
      this.label_Input_Filling1.Size = new Size(105, 23);
      this.label_Input_Filling1.TabIndex = 136;
      this.label_Input_Filling1.Text = "1장";
      this.label_Input_Filling1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_ShrinkageMax.BackColor = Color.Lavender;
      this.label_Input_ShrinkageMax.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_ShrinkageMax.Location = new Point(297, 729);
      this.label_Input_ShrinkageMax.Name = "label_Input_ShrinkageMax";
      this.label_Input_ShrinkageMax.Size = new Size(189, 23);
      this.label_Input_ShrinkageMax.TabIndex = 136;
      this.label_Input_ShrinkageMax.Text = "수축률 최대 값";
      this.label_Input_ShrinkageMax.TextAlign = ContentAlignment.MiddleCenter;
      this.label67.BackColor = Color.Lavender;
      this.label67.BorderStyle = BorderStyle.FixedSingle;
      this.label67.Location = new Point(5, 921);
      this.label67.Name = "label67";
      this.label67.Size = new Size(104, 23);
      this.label67.TabIndex = 136;
      this.label67.Text = "Z - Deflection";
      this.label67.TextAlign = ContentAlignment.MiddleCenter;
      this.label66.BackColor = Color.Lavender;
      this.label66.BorderStyle = BorderStyle.FixedSingle;
      this.label66.Location = new Point(5, 899);
      this.label66.Name = "label66";
      this.label66.Size = new Size(104, 23);
      this.label66.TabIndex = 136;
      this.label66.Text = "Y - Deflection";
      this.label66.TextAlign = ContentAlignment.MiddleCenter;
      this.label64.BackColor = Color.Lavender;
      this.label64.BorderStyle = BorderStyle.FixedSingle;
      this.label64.Location = new Point(5, 877);
      this.label64.Name = "label64";
      this.label64.Size = new Size(104, 23);
      this.label64.TabIndex = 136;
      this.label64.Text = "X - Deflection";
      this.label64.TextAlign = ContentAlignment.MiddleCenter;
      this.label60.BackColor = Color.Lavender;
      this.label60.BorderStyle = BorderStyle.FixedSingle;
      this.label60.Location = new Point(5, 855);
      this.label60.Name = "label60";
      this.label60.Size = new Size(104, 23);
      this.label60.TabIndex = 136;
      this.label60.Text = "All - Deflection";
      this.label60.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_SurfaceMaxTemp.BackColor = Color.Lavender;
      this.label_Input_SurfaceMaxTemp.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_SurfaceMaxTemp.Location = new Point(5, 729);
      this.label_Input_SurfaceMaxTemp.Name = "label_Input_SurfaceMaxTemp";
      this.label_Input_SurfaceMaxTemp.Size = new Size(189, 23);
      this.label_Input_SurfaceMaxTemp.TabIndex = 136;
      this.label_Input_SurfaceMaxTemp.Text = "금형 표면 온도 최대 값";
      this.label_Input_SurfaceMaxTemp.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_ShrinkageMin.BackColor = Color.Lavender;
      this.label_Input_ShrinkageMin.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_ShrinkageMin.Location = new Point(297, 707);
      this.label_Input_ShrinkageMin.Name = "label_Input_ShrinkageMin";
      this.label_Input_ShrinkageMin.Size = new Size(189, 23);
      this.label_Input_ShrinkageMin.TabIndex = 136;
      this.label_Input_ShrinkageMin.Text = "수축률 최소 값";
      this.label_Input_ShrinkageMin.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_SurfaceMinTemp.BackColor = Color.Lavender;
      this.label_Input_SurfaceMinTemp.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_SurfaceMinTemp.Location = new Point(5, 707);
      this.label_Input_SurfaceMinTemp.Name = "label_Input_SurfaceMinTemp";
      this.label_Input_SurfaceMinTemp.Size = new Size(189, 23);
      this.label_Input_SurfaceMinTemp.TabIndex = 136;
      this.label_Input_SurfaceMinTemp.Text = "금형 표면 온도 최소 값";
      this.label_Input_SurfaceMinTemp.TextAlign = ContentAlignment.MiddleCenter;
      this.label33.BackColor = Color.Lavender;
      this.label33.BorderStyle = BorderStyle.FixedSingle;
      this.label33.Location = new Point(5, 474);
      this.label33.Name = "label33";
      this.label33.Size = new Size(147, 23);
      this.label33.TabIndex = 136;
      this.label33.Text = "Filling time(s)";
      this.label33.TextAlign = ContentAlignment.MiddleCenter;
      this.label13.BackColor = Color.Lavender;
      this.label13.BorderStyle = BorderStyle.FixedSingle;
      this.label13.Location = new Point(200, 215);
      this.label13.Name = "label13";
      this.label13.Size = new Size(196, 23);
      this.label13.TabIndex = 137;
      this.label13.Text = "Engineer";
      this.label13.TextAlign = ContentAlignment.MiddleCenter;
      this.label14.BackColor = Color.Lavender;
      this.label14.BorderStyle = BorderStyle.FixedSingle;
      this.label14.Location = new Point(5, 193);
      this.label14.Name = "label14";
      this.label14.Size = new Size(147, 23);
      this.label14.TabIndex = 136;
      this.label14.Text = "Item";
      this.label14.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_InjRange1.BackColor = Color.Lavender;
      this.label_Input_InjRange1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_InjRange1.Location = new Point(5, 130);
      this.label_Input_InjRange1.Name = "label_Input_InjRange1";
      this.label_Input_InjRange1.Size = new Size(105, 23);
      this.label_Input_InjRange1.TabIndex = 135;
      this.label_Input_InjRange1.Text = "1차";
      this.label_Input_InjRange1.TextAlign = ContentAlignment.MiddleCenter;
      this.unitTextBox_Input_InjPreRatio.BackColor = Color.White;
      this.unitTextBox_Input_InjPreRatio.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_InjPreRatio.ControlBackColor = Color.White;
      this.unitTextBox_Input_InjPreRatio.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_InjPreRatio.IsDigit = true;
      this.unitTextBox_Input_InjPreRatio.Location = new Point(488, 84);
      this.unitTextBox_Input_InjPreRatio.Name = "unitTextBox_Input_InjPreRatio";
      this.unitTextBox_Input_InjPreRatio.Size = new Size(103, 23);
      this.unitTextBox_Input_InjPreRatio.TabIndex = (int) sbyte.MaxValue;
      this.unitTextBox_Input_InjPreRatio.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_InjPreRatio.Unit = "배";
      this.unitTextBox_Input_InjPreRatio.Value = "";
      this.unitTextBox_Input_InjMaxClamp.BackColor = Color.White;
      this.unitTextBox_Input_InjMaxClamp.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_InjMaxClamp.ControlBackColor = Color.White;
      this.unitTextBox_Input_InjMaxClamp.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_InjMaxClamp.IsDigit = true;
      this.unitTextBox_Input_InjMaxClamp.Location = new Point(293, 84);
      this.unitTextBox_Input_InjMaxClamp.Name = "unitTextBox_Input_InjMaxClamp";
      this.unitTextBox_Input_InjMaxClamp.Size = new Size(103, 23);
      this.unitTextBox_Input_InjMaxClamp.TabIndex = 126;
      this.unitTextBox_Input_InjMaxClamp.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_InjMaxClamp.Unit = "t";
      this.unitTextBox_Input_InjMaxClamp.Value = "";
      this.unitTextBox_Input_InjMaxPressure.BackColor = Color.White;
      this.unitTextBox_Input_InjMaxPressure.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_InjMaxPressure.ControlBackColor = Color.White;
      this.unitTextBox_Input_InjMaxPressure.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_InjMaxPressure.IsDigit = true;
      this.unitTextBox_Input_InjMaxPressure.Location = new Point(98, 84);
      this.unitTextBox_Input_InjMaxPressure.Name = "unitTextBox_Input_InjMaxPressure";
      this.unitTextBox_Input_InjMaxPressure.Size = new Size(103, 23);
      this.unitTextBox_Input_InjMaxPressure.TabIndex = 128;
      this.unitTextBox_Input_InjMaxPressure.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_InjMaxPressure.Unit = "MP";
      this.unitTextBox_Input_InjMaxPressure.Value = "";
      this.unitTextBox_Input_InjRangeVP.BackColor = Color.White;
      this.unitTextBox_Input_InjRangeVP.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_InjRangeVP.ControlBackColor = Color.White;
      this.unitTextBox_Input_InjRangeVP.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_InjRangeVP.IsDigit = true;
      this.unitTextBox_Input_InjRangeVP.Location = new Point(444, 152);
      this.unitTextBox_Input_InjRangeVP.Name = "unitTextBox_Input_InjRangeVP";
      this.unitTextBox_Input_InjRangeVP.Size = new Size(147, 23);
      this.unitTextBox_Input_InjRangeVP.TabIndex = 125;
      this.unitTextBox_Input_InjRangeVP.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_InjRangeVP.Unit = "m";
      this.unitTextBox_Input_InjRangeVP.Value = "";
      this.unitTextBox_Input_InjRange4.BackColor = Color.White;
      this.unitTextBox_Input_InjRange4.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_InjRange4.ControlBackColor = Color.White;
      this.unitTextBox_Input_InjRange4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_InjRange4.IsDigit = true;
      this.unitTextBox_Input_InjRange4.Location = new Point(317, 152);
      this.unitTextBox_Input_InjRange4.Name = "unitTextBox_Input_InjRange4";
      this.unitTextBox_Input_InjRange4.Size = new Size(128, 23);
      this.unitTextBox_Input_InjRange4.TabIndex = 118;
      this.unitTextBox_Input_InjRange4.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_InjRange4.Unit = "%";
      this.unitTextBox_Input_InjRange4.Value = "";
      this.unitTextBox_Input_InjRange3.BackColor = Color.White;
      this.unitTextBox_Input_InjRange3.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_InjRange3.ControlBackColor = Color.White;
      this.unitTextBox_Input_InjRange3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_InjRange3.IsDigit = true;
      this.unitTextBox_Input_InjRange3.Location = new Point(213, 152);
      this.unitTextBox_Input_InjRange3.Name = "unitTextBox_Input_InjRange3";
      this.unitTextBox_Input_InjRange3.Size = new Size(105, 23);
      this.unitTextBox_Input_InjRange3.TabIndex = 124;
      this.unitTextBox_Input_InjRange3.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_InjRange3.Unit = "%";
      this.unitTextBox_Input_InjRange3.Value = "";
      this.unitTextBox_Input_InjScrewDia.BackColor = Color.White;
      this.unitTextBox_Input_InjScrewDia.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_InjScrewDia.ControlBackColor = Color.White;
      this.unitTextBox_Input_InjScrewDia.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_InjScrewDia.IsDigit = true;
      this.unitTextBox_Input_InjScrewDia.Location = new Point(488, 62);
      this.unitTextBox_Input_InjScrewDia.Name = "unitTextBox_Input_InjScrewDia";
      this.unitTextBox_Input_InjScrewDia.Size = new Size(103, 23);
      this.unitTextBox_Input_InjScrewDia.TabIndex = 123;
      this.unitTextBox_Input_InjScrewDia.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_InjScrewDia.Unit = "mm";
      this.unitTextBox_Input_InjScrewDia.Value = "";
      this.unitTextBox_Input_InjRange2.BackColor = Color.White;
      this.unitTextBox_Input_InjRange2.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_InjRange2.ControlBackColor = Color.White;
      this.unitTextBox_Input_InjRange2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_InjRange2.IsDigit = true;
      this.unitTextBox_Input_InjRange2.Location = new Point(109, 152);
      this.unitTextBox_Input_InjRange2.Name = "unitTextBox_Input_InjRange2";
      this.unitTextBox_Input_InjRange2.Size = new Size(105, 23);
      this.unitTextBox_Input_InjRange2.TabIndex = 122;
      this.unitTextBox_Input_InjRange2.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_InjRange2.Unit = "%";
      this.unitTextBox_Input_InjRange2.Value = "";
      this.unitTextBox_Input_Filling8.BackColor = Color.White;
      this.unitTextBox_Input_Filling8.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_Filling8.ControlBackColor = Color.White;
      this.unitTextBox_Input_Filling8.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_Filling8.IsDigit = true;
      this.unitTextBox_Input_Filling8.Location = new Point(401, 603);
      this.unitTextBox_Input_Filling8.Name = "unitTextBox_Input_Filling8";
      this.unitTextBox_Input_Filling8.Size = new Size(105, 23);
      this.unitTextBox_Input_Filling8.TabIndex = 120;
      this.unitTextBox_Input_Filling8.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_Filling8.Unit = "%";
      this.unitTextBox_Input_Filling8.Value = "";
      this.unitTextBox_Input_InjMaxRate.BackColor = Color.White;
      this.unitTextBox_Input_InjMaxRate.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_InjMaxRate.ControlBackColor = Color.White;
      this.unitTextBox_Input_InjMaxRate.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_InjMaxRate.IsDigit = true;
      this.unitTextBox_Input_InjMaxRate.Location = new Point(293, 62);
      this.unitTextBox_Input_InjMaxRate.Name = "unitTextBox_Input_InjMaxRate";
      this.unitTextBox_Input_InjMaxRate.Size = new Size(103, 23);
      this.unitTextBox_Input_InjMaxRate.TabIndex = 121;
      this.unitTextBox_Input_InjMaxRate.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_InjMaxRate.Unit = "cm\u00B3/s";
      this.unitTextBox_Input_InjMaxRate.Value = "";
      this.unitTextBox_Input_Filling7.BackColor = Color.White;
      this.unitTextBox_Input_Filling7.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_Filling7.ControlBackColor = Color.White;
      this.unitTextBox_Input_Filling7.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_Filling7.IsDigit = true;
      this.unitTextBox_Input_Filling7.Location = new Point(297, 603);
      this.unitTextBox_Input_Filling7.Name = "unitTextBox_Input_Filling7";
      this.unitTextBox_Input_Filling7.Size = new Size(105, 23);
      this.unitTextBox_Input_Filling7.TabIndex = 120;
      this.unitTextBox_Input_Filling7.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_Filling7.Unit = "%";
      this.unitTextBox_Input_Filling7.Value = "";
      this.unitTextBox_Input_Frozen4.BackColor = Color.White;
      this.unitTextBox_Input_Frozen4.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_Frozen4.ControlBackColor = Color.White;
      this.unitTextBox_Input_Frozen4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_Frozen4.IsDigit = true;
      this.unitTextBox_Input_Frozen4.Location = new Point(464, 666);
      this.unitTextBox_Input_Frozen4.Name = "unitTextBox_Input_Frozen4";
      this.unitTextBox_Input_Frozen4.Size = new Size((int) sbyte.MaxValue, 23);
      this.unitTextBox_Input_Frozen4.TabIndex = 120;
      this.unitTextBox_Input_Frozen4.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_Frozen4.Unit = "%";
      this.unitTextBox_Input_Frozen4.Value = "";
      this.unitTextBox_Input_Filling4.BackColor = Color.White;
      this.unitTextBox_Input_Filling4.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_Filling4.ControlBackColor = Color.White;
      this.unitTextBox_Input_Filling4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_Filling4.IsDigit = true;
      this.unitTextBox_Input_Filling4.Location = new Point(401, 559);
      this.unitTextBox_Input_Filling4.Name = "unitTextBox_Input_Filling4";
      this.unitTextBox_Input_Filling4.Size = new Size(105, 23);
      this.unitTextBox_Input_Filling4.TabIndex = 120;
      this.unitTextBox_Input_Filling4.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_Filling4.Unit = "%";
      this.unitTextBox_Input_Filling4.Value = "";
      this.unitTextBox_Input_Filling6.BackColor = Color.White;
      this.unitTextBox_Input_Filling6.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_Filling6.ControlBackColor = Color.White;
      this.unitTextBox_Input_Filling6.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_Filling6.IsDigit = true;
      this.unitTextBox_Input_Filling6.Location = new Point(193, 603);
      this.unitTextBox_Input_Filling6.Name = "unitTextBox_Input_Filling6";
      this.unitTextBox_Input_Filling6.Size = new Size(105, 23);
      this.unitTextBox_Input_Filling6.TabIndex = 120;
      this.unitTextBox_Input_Filling6.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_Filling6.Unit = "%";
      this.unitTextBox_Input_Filling6.Value = "";
      this.unitTextBox_Input_Frozen3.BackColor = Color.White;
      this.unitTextBox_Input_Frozen3.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_Frozen3.ControlBackColor = Color.White;
      this.unitTextBox_Input_Frozen3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_Frozen3.IsDigit = true;
      this.unitTextBox_Input_Frozen3.Location = new Point(339, 666);
      this.unitTextBox_Input_Frozen3.Name = "unitTextBox_Input_Frozen3";
      this.unitTextBox_Input_Frozen3.Size = new Size(126, 23);
      this.unitTextBox_Input_Frozen3.TabIndex = 120;
      this.unitTextBox_Input_Frozen3.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_Frozen3.Unit = "%";
      this.unitTextBox_Input_Frozen3.Value = "";
      this.unitTextBox_Input_ShrinkageMax.BackColor = Color.White;
      this.unitTextBox_Input_ShrinkageMax.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_ShrinkageMax.ControlBackColor = Color.White;
      this.unitTextBox_Input_ShrinkageMax.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_ShrinkageMax.IsDigit = true;
      this.unitTextBox_Input_ShrinkageMax.Location = new Point(485, 729);
      this.unitTextBox_Input_ShrinkageMax.Name = "unitTextBox_Input_ShrinkageMax";
      this.unitTextBox_Input_ShrinkageMax.Size = new Size(106, 23);
      this.unitTextBox_Input_ShrinkageMax.TabIndex = 120;
      this.unitTextBox_Input_ShrinkageMax.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_ShrinkageMax.Unit = "%";
      this.unitTextBox_Input_ShrinkageMax.Value = "";
      this.unitTextBox_Input_Filling3.BackColor = Color.White;
      this.unitTextBox_Input_Filling3.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_Filling3.ControlBackColor = Color.White;
      this.unitTextBox_Input_Filling3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_Filling3.IsDigit = true;
      this.unitTextBox_Input_Filling3.Location = new Point(297, 559);
      this.unitTextBox_Input_Filling3.Name = "unitTextBox_Input_Filling3";
      this.unitTextBox_Input_Filling3.Size = new Size(105, 23);
      this.unitTextBox_Input_Filling3.TabIndex = 120;
      this.unitTextBox_Input_Filling3.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_Filling3.Unit = "%";
      this.unitTextBox_Input_Filling3.Value = "";
      this.unitTextBox_Input_ShrinkageMin.BackColor = Color.White;
      this.unitTextBox_Input_ShrinkageMin.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_ShrinkageMin.ControlBackColor = Color.White;
      this.unitTextBox_Input_ShrinkageMin.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_ShrinkageMin.IsDigit = true;
      this.unitTextBox_Input_ShrinkageMin.Location = new Point(485, 707);
      this.unitTextBox_Input_ShrinkageMin.Name = "unitTextBox_Input_ShrinkageMin";
      this.unitTextBox_Input_ShrinkageMin.Size = new Size(106, 23);
      this.unitTextBox_Input_ShrinkageMin.TabIndex = 120;
      this.unitTextBox_Input_ShrinkageMin.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_ShrinkageMin.Unit = "%";
      this.unitTextBox_Input_ShrinkageMin.Value = "";
      this.unitTextBox_Input_MoldTempMax.BackColor = Color.White;
      this.unitTextBox_Input_MoldTempMax.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_MoldTempMax.ControlBackColor = Color.White;
      this.unitTextBox_Input_MoldTempMax.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_MoldTempMax.IsDigit = true;
      this.unitTextBox_Input_MoldTempMax.Location = new Point(193, 729);
      this.unitTextBox_Input_MoldTempMax.Name = "unitTextBox_Input_MoldTempMax";
      this.unitTextBox_Input_MoldTempMax.Size = new Size(105, 23);
      this.unitTextBox_Input_MoldTempMax.TabIndex = 120;
      this.unitTextBox_Input_MoldTempMax.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_MoldTempMax.Unit = "℃";
      this.unitTextBox_Input_MoldTempMax.Value = "";
      this.unitTextBox_Input_MoldTempMin.BackColor = Color.White;
      this.unitTextBox_Input_MoldTempMin.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_MoldTempMin.ControlBackColor = Color.White;
      this.unitTextBox_Input_MoldTempMin.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_MoldTempMin.IsDigit = true;
      this.unitTextBox_Input_MoldTempMin.Location = new Point(193, 707);
      this.unitTextBox_Input_MoldTempMin.Name = "unitTextBox_Input_MoldTempMin";
      this.unitTextBox_Input_MoldTempMin.Size = new Size(105, 23);
      this.unitTextBox_Input_MoldTempMin.TabIndex = 120;
      this.unitTextBox_Input_MoldTempMin.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_MoldTempMin.Unit = "℃";
      this.unitTextBox_Input_MoldTempMin.Value = "";
      this.unitTextBox_Input_Filling5.BackColor = Color.White;
      this.unitTextBox_Input_Filling5.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_Filling5.ControlBackColor = Color.White;
      this.unitTextBox_Input_Filling5.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_Filling5.IsDigit = true;
      this.unitTextBox_Input_Filling5.Location = new Point(89, 603);
      this.unitTextBox_Input_Filling5.Name = "unitTextBox_Input_Filling5";
      this.unitTextBox_Input_Filling5.Size = new Size(105, 23);
      this.unitTextBox_Input_Filling5.TabIndex = 120;
      this.unitTextBox_Input_Filling5.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_Filling5.Unit = "%";
      this.unitTextBox_Input_Filling5.Value = "";
      this.unitTextBox_Input_Frozen2.BackColor = Color.White;
      this.unitTextBox_Input_Frozen2.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_Frozen2.ControlBackColor = Color.White;
      this.unitTextBox_Input_Frozen2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_Frozen2.IsDigit = true;
      this.unitTextBox_Input_Frozen2.Location = new Point(214, 666);
      this.unitTextBox_Input_Frozen2.Name = "unitTextBox_Input_Frozen2";
      this.unitTextBox_Input_Frozen2.Size = new Size(126, 23);
      this.unitTextBox_Input_Frozen2.TabIndex = 120;
      this.unitTextBox_Input_Frozen2.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_Frozen2.Unit = "%";
      this.unitTextBox_Input_Frozen2.Value = "";
      this.unitTextBox_Input_Filling2.BackColor = Color.White;
      this.unitTextBox_Input_Filling2.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_Filling2.ControlBackColor = Color.White;
      this.unitTextBox_Input_Filling2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_Filling2.IsDigit = true;
      this.unitTextBox_Input_Filling2.Location = new Point(193, 559);
      this.unitTextBox_Input_Filling2.Name = "unitTextBox_Input_Filling2";
      this.unitTextBox_Input_Filling2.Size = new Size(105, 23);
      this.unitTextBox_Input_Filling2.TabIndex = 120;
      this.unitTextBox_Input_Filling2.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_Filling2.Unit = "%";
      this.unitTextBox_Input_Filling2.Value = "";
      this.unitTextBox_Input_Frozen1.BackColor = Color.White;
      this.unitTextBox_Input_Frozen1.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_Frozen1.ControlBackColor = Color.White;
      this.unitTextBox_Input_Frozen1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_Frozen1.IsDigit = true;
      this.unitTextBox_Input_Frozen1.Location = new Point(89, 666);
      this.unitTextBox_Input_Frozen1.Name = "unitTextBox_Input_Frozen1";
      this.unitTextBox_Input_Frozen1.Size = new Size(126, 23);
      this.unitTextBox_Input_Frozen1.TabIndex = 120;
      this.unitTextBox_Input_Frozen1.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_Frozen1.Unit = "%";
      this.unitTextBox_Input_Frozen1.Value = "";
      this.unitTextBox_Input_Filling1.BackColor = Color.White;
      this.unitTextBox_Input_Filling1.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_Filling1.ControlBackColor = Color.White;
      this.unitTextBox_Input_Filling1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_Filling1.IsDigit = true;
      this.unitTextBox_Input_Filling1.Location = new Point(89, 559);
      this.unitTextBox_Input_Filling1.Name = "unitTextBox_Input_Filling1";
      this.unitTextBox_Input_Filling1.Size = new Size(105, 23);
      this.unitTextBox_Input_Filling1.TabIndex = 120;
      this.unitTextBox_Input_Filling1.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_Filling1.Unit = "%";
      this.unitTextBox_Input_Filling1.Value = "";
      this.unitTextBox_Input_InjRange1.BackColor = Color.White;
      this.unitTextBox_Input_InjRange1.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_InjRange1.ControlBackColor = Color.White;
      this.unitTextBox_Input_InjRange1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_InjRange1.IsDigit = true;
      this.unitTextBox_Input_InjRange1.Location = new Point(5, 152);
      this.unitTextBox_Input_InjRange1.Name = "unitTextBox_Input_InjRange1";
      this.unitTextBox_Input_InjRange1.Size = new Size(105, 23);
      this.unitTextBox_Input_InjRange1.TabIndex = 120;
      this.unitTextBox_Input_InjRange1.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_InjRange1.Unit = "%";
      this.unitTextBox_Input_InjRange1.Value = "";
      this.unitTextBox_Input_InjMaxStroke.BackColor = Color.White;
      this.unitTextBox_Input_InjMaxStroke.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_InjMaxStroke.ControlBackColor = Color.White;
      this.unitTextBox_Input_InjMaxStroke.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_InjMaxStroke.IsDigit = true;
      this.unitTextBox_Input_InjMaxStroke.Location = new Point(98, 62);
      this.unitTextBox_Input_InjMaxStroke.Name = "unitTextBox_Input_InjMaxStroke";
      this.unitTextBox_Input_InjMaxStroke.Size = new Size(103, 23);
      this.unitTextBox_Input_InjMaxStroke.TabIndex = 119;
      this.unitTextBox_Input_InjMaxStroke.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_InjMaxStroke.Unit = "mm";
      this.unitTextBox_Input_InjMaxStroke.Value = "";
      this.label_Input_InjPreRatio.BackColor = Color.Lavender;
      this.label_Input_InjPreRatio.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_InjPreRatio.Location = new Point(395, 84);
      this.label_Input_InjPreRatio.Name = "label_Input_InjPreRatio";
      this.label_Input_InjPreRatio.Size = new Size(94, 23);
      this.label_Input_InjPreRatio.TabIndex = 112;
      this.label_Input_InjPreRatio.Text = "증압비";
      this.label_Input_InjPreRatio.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_InjScrewDia.BackColor = Color.Lavender;
      this.label_Input_InjScrewDia.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_InjScrewDia.Location = new Point(395, 62);
      this.label_Input_InjScrewDia.Name = "label_Input_InjScrewDia";
      this.label_Input_InjScrewDia.Size = new Size(94, 23);
      this.label_Input_InjScrewDia.TabIndex = 113;
      this.label_Input_InjScrewDia.Text = "스크류 지름";
      this.label_Input_InjScrewDia.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_InjMaxClamp.BackColor = Color.Lavender;
      this.label_Input_InjMaxClamp.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_InjMaxClamp.Location = new Point(200, 84);
      this.label_Input_InjMaxClamp.Name = "label_Input_InjMaxClamp";
      this.label_Input_InjMaxClamp.Size = new Size(94, 23);
      this.label_Input_InjMaxClamp.TabIndex = 114;
      this.label_Input_InjMaxClamp.Text = "최대 형체력";
      this.label_Input_InjMaxClamp.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_InjMaxRate.BackColor = Color.Lavender;
      this.label_Input_InjMaxRate.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_InjMaxRate.Location = new Point(200, 62);
      this.label_Input_InjMaxRate.Name = "label_Input_InjMaxRate";
      this.label_Input_InjMaxRate.Size = new Size(94, 23);
      this.label_Input_InjMaxRate.TabIndex = 115;
      this.label_Input_InjMaxRate.Text = "최대 사출률";
      this.label_Input_InjMaxRate.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_InjMaxPressure.BackColor = Color.Lavender;
      this.label_Input_InjMaxPressure.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_InjMaxPressure.Location = new Point(5, 84);
      this.label_Input_InjMaxPressure.Name = "label_Input_InjMaxPressure";
      this.label_Input_InjMaxPressure.Size = new Size(94, 23);
      this.label_Input_InjMaxPressure.TabIndex = 116;
      this.label_Input_InjMaxPressure.Text = "최대 사출압력";
      this.label_Input_InjMaxPressure.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_InjMaxStroke.BackColor = Color.Lavender;
      this.label_Input_InjMaxStroke.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_InjMaxStroke.Location = new Point(5, 62);
      this.label_Input_InjMaxStroke.Name = "label_Input_InjMaxStroke";
      this.label_Input_InjMaxStroke.Size = new Size(94, 23);
      this.label_Input_InjMaxStroke.TabIndex = 117;
      this.label_Input_InjMaxStroke.Text = "최대 스트로크";
      this.label_Input_InjMaxStroke.TextAlign = ContentAlignment.MiddleCenter;
      this.newComboBox_Input_InjData.BackColor = Color.White;
      this.newComboBox_Input_InjData.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Input_InjData.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_Input_InjData.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Input_InjData.isSameSelect = false;
      this.newComboBox_Input_InjData.Location = new Point(5, 40);
      this.newComboBox_Input_InjData.Name = "newComboBox_Input_InjData";
      this.newComboBox_Input_InjData.SelectedIndex = -1;
      this.newComboBox_Input_InjData.Size = new Size(586, 23);
      this.newComboBox_Input_InjData.TabIndex = 111;
      this.newComboBox_Input_InjData.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Input_InjData.Value = "";
      this.newComboBox_Input_InjData.SelectedIndexChanged += new EventHandler(this.newComboBox_Input_InjData_SelectedIndexChanged);
      this.label_Input_Deflection.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Input_Deflection.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Deflection.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Input_Deflection.ForeColor = Color.MidnightBlue;
      this.label_Input_Deflection.Location = new Point(5, 836);
      this.label_Input_Deflection.Name = "label_Input_Deflection";
      this.label_Input_Deflection.Size = new Size(586, 20);
      this.label_Input_Deflection.TabIndex = 110;
      this.label_Input_Deflection.Text = "변형";
      this.label_Input_Deflection.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_SurfShrinkage.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Input_SurfShrinkage.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_SurfShrinkage.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Input_SurfShrinkage.ForeColor = Color.MidnightBlue;
      this.label_Input_SurfShrinkage.Location = new Point(5, 688);
      this.label_Input_SurfShrinkage.Name = "label_Input_SurfShrinkage";
      this.label_Input_SurfShrinkage.Size = new Size(586, 20);
      this.label_Input_SurfShrinkage.TabIndex = 110;
      this.label_Input_SurfShrinkage.Text = "금형 표면 온도 / 수축률";
      this.label_Input_SurfShrinkage.TextAlign = ContentAlignment.MiddleCenter;
      this.label48.BackColor = Color.FromArgb(229, 238, 248);
      this.label48.BorderStyle = BorderStyle.FixedSingle;
      this.label48.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label48.ForeColor = Color.MidnightBlue;
      this.label48.Location = new Point(5, 625);
      this.label48.Name = "label48";
      this.label48.Size = new Size(586, 20);
      this.label48.TabIndex = 110;
      this.label48.Text = "Frozen layer fraction Pattern";
      this.label48.TextAlign = ContentAlignment.MiddleCenter;
      this.label37.BackColor = Color.FromArgb(229, 238, 248);
      this.label37.BorderStyle = BorderStyle.FixedSingle;
      this.label37.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label37.ForeColor = Color.MidnightBlue;
      this.label37.Location = new Point(5, 518);
      this.label37.Name = "label37";
      this.label37.Size = new Size(586, 20);
      this.label37.TabIndex = 110;
      this.label37.Text = "Filling Pattern";
      this.label37.TextAlign = ContentAlignment.MiddleCenter;
      this.label30.BackColor = Color.FromArgb(229, 238, 248);
      this.label30.BorderStyle = BorderStyle.FixedSingle;
      this.label30.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label30.ForeColor = Color.MidnightBlue;
      this.label30.Location = new Point(5, 455);
      this.label30.Name = "label30";
      this.label30.Size = new Size(586, 20);
      this.label30.TabIndex = 110;
      this.label30.Text = "Cycle Time";
      this.label30.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_Result_Comment.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Input_Result_Comment.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Result_Comment.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Input_Result_Comment.ForeColor = Color.MidnightBlue;
      this.label_Input_Result_Comment.Location = new Point(5, 259);
      this.label_Input_Result_Comment.Name = "label_Input_Result_Comment";
      this.label_Input_Result_Comment.Size = new Size(586, 20);
      this.label_Input_Result_Comment.TabIndex = 110;
      this.label_Input_Result_Comment.Text = "결과 코멘트 작성";
      this.label_Input_Result_Comment.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_Sign.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Input_Sign.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Sign.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Input_Sign.ForeColor = Color.MidnightBlue;
      this.label_Input_Sign.Location = new Point(5, 174);
      this.label_Input_Sign.Name = "label_Input_Sign";
      this.label_Input_Sign.Size = new Size(586, 20);
      this.label_Input_Sign.TabIndex = 110;
      this.label_Input_Sign.Text = "표지";
      this.label_Input_Sign.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_Range.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Input_Range.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Range.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Input_Range.ForeColor = Color.MidnightBlue;
      this.label_Input_Range.Location = new Point(5, 106);
      this.label_Input_Range.Name = "label_Input_Range";
      this.label_Input_Range.Size = new Size(313, 25);
      this.label_Input_Range.TabIndex = 109;
      this.label_Input_Range.Text = "사출 구간";
      this.label_Input_Range.TextAlign = ContentAlignment.MiddleCenter;
      this.label61.BackColor = Color.FromArgb(229, 238, 248);
      this.label61.BorderStyle = BorderStyle.FixedSingle;
      this.label61.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label61.ForeColor = Color.MidnightBlue;
      this.label61.Location = new Point(5, 2);
      this.label61.Name = "label61";
      this.label61.Size = new Size(586, 20);
      this.label61.TabIndex = 108;
      this.label61.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_Inj.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Input_Inj.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Inj.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Input_Inj.ForeColor = Color.MidnightBlue;
      this.label_Input_Inj.Location = new Point(5, 21);
      this.label_Input_Inj.Name = "label_Input_Inj";
      this.label_Input_Inj.Size = new Size(586, 20);
      this.label_Input_Inj.TabIndex = 108;
      this.label_Input_Inj.Text = "사출기 정보";
      this.label_Input_Inj.TextAlign = ContentAlignment.MiddleCenter;
      this.panel2.BorderStyle = BorderStyle.FixedSingle;
      this.panel2.Location = new Point(297, 813);
      this.panel2.Name = "panel2";
      this.panel2.Size = new Size(294, 24);
      this.panel2.TabIndex = 164;
      this.tabPage_View.Controls.Add((Control) this.checkBox_AllCheck);
      this.tabPage_View.Controls.Add((Control) this.dataGridView_View);
      this.tabPage_View.Controls.Add((Control) this.label2);
      this.tabPage_View.Controls.Add((Control) this.label3);
      this.tabPage_View.Controls.Add((Control) this.label4);
      this.tabPage_View.Controls.Add((Control) this.label5);
      this.tabPage_View.Controls.Add((Control) this.label_View_Item);
      this.tabPage_View.Controls.Add((Control) this.label_View_Model);
      this.tabPage_View.Controls.Add((Control) this.newButton_View_Select);
      this.tabPage_View.Controls.Add((Control) this.newButton_View_All);
      this.tabPage_View.Controls.Add((Control) this.newTextBox_View_Z);
      this.tabPage_View.Controls.Add((Control) this.newTextBox_View_Y);
      this.tabPage_View.Controls.Add((Control) this.newTextBox_View_X);
      this.tabPage_View.Controls.Add((Control) this.newComboBox_View_Type);
      this.tabPage_View.Location = new Point(4, 24);
      this.tabPage_View.Name = "tabPage_View";
      this.tabPage_View.Padding = new Padding(3);
      this.tabPage_View.Size = new Size(595, 948);
      this.tabPage_View.TabIndex = 1;
      this.tabPage_View.Text = "tabPage2";
      this.tabPage_View.UseVisualStyleBackColor = true;
      this.checkBox_AllCheck.AutoSize = true;
      this.checkBox_AllCheck.Location = new Point(9, 88);
      this.checkBox_AllCheck.Name = "checkBox_AllCheck";
      this.checkBox_AllCheck.Size = new Size(15, 14);
      this.checkBox_AllCheck.TabIndex = 118;
      this.checkBox_AllCheck.UseVisualStyleBackColor = true;
      this.checkBox_AllCheck.CheckedChanged += new EventHandler(this.checkBox_AllCheck_CheckedChanged);
      this.dataGridView_View.AllowUserToAddRows = false;
      this.dataGridView_View.AllowUserToDeleteRows = false;
      this.dataGridView_View.AllowUserToResizeColumns = false;
      this.dataGridView_View.AllowUserToResizeRows = false;
      this.dataGridView_View.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left;
      this.dataGridView_View.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_View.BackgroundColor = Color.White;
      this.dataGridView_View.BorderStyle = BorderStyle.None;
      gridViewCellStyle1.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle1.BackColor = Color.Lavender;
      gridViewCellStyle1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle1.ForeColor = SystemColors.WindowText;
      gridViewCellStyle1.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle1.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle1.WrapMode = DataGridViewTriState.True;
      this.dataGridView_View.ColumnHeadersDefaultCellStyle = gridViewCellStyle1;
      this.dataGridView_View.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_View.Columns.AddRange((DataGridViewColumn) this.Column6, (DataGridViewColumn) this.Column5, (DataGridViewColumn) this.Column_Item, (DataGridViewColumn) this.Column2, (DataGridViewColumn) this.Column3, (DataGridViewColumn) this.Column4, (DataGridViewColumn) this.Column_SlideID);
      this.dataGridView_View.EnableHeadersVisualStyles = false;
      this.dataGridView_View.Location = new Point(5, 84);
      this.dataGridView_View.Name = "dataGridView_View";
      this.dataGridView_View.RowHeadersVisible = false;
      this.dataGridView_View.RowTemplate.Height = 23;
      this.dataGridView_View.Size = new Size(586, 844);
      this.dataGridView_View.TabIndex = 117;
      this.Column6.HeaderText = "";
      this.Column6.Name = "Column6";
      this.Column6.Visible = false;
      this.Column5.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      this.Column5.HeaderText = "";
      this.Column5.Name = "Column5";
      this.Column5.Width = 20;
      gridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
      this.Column_Item.DefaultCellStyle = gridViewCellStyle2;
      this.Column_Item.HeaderText = "항목";
      this.Column_Item.Name = "Column_Item";
      this.Column_Item.ReadOnly = true;
      this.Column2.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      gridViewCellStyle3.Alignment = DataGridViewContentAlignment.MiddleCenter;
      this.Column2.DefaultCellStyle = gridViewCellStyle3;
      this.Column2.HeaderText = "X";
      this.Column2.Name = "Column2";
      this.Column2.Width = 60;
      this.Column3.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      gridViewCellStyle4.Alignment = DataGridViewContentAlignment.MiddleCenter;
      this.Column3.DefaultCellStyle = gridViewCellStyle4;
      this.Column3.HeaderText = "Y";
      this.Column3.Name = "Column3";
      this.Column3.Width = 60;
      this.Column4.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      gridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleCenter;
      this.Column4.DefaultCellStyle = gridViewCellStyle5;
      this.Column4.HeaderText = "Z";
      this.Column4.Name = "Column4";
      this.Column4.Width = 60;
      this.Column_SlideID.HeaderText = "SlideID";
      this.Column_SlideID.Name = "Column_SlideID";
      this.Column_SlideID.ReadOnly = true;
      this.Column_SlideID.Visible = false;
      this.label2.BackColor = Color.Lavender;
      this.label2.BorderStyle = BorderStyle.FixedSingle;
      this.label2.Location = new Point(351, 21);
      this.label2.Name = "label2";
      this.label2.Size = new Size(88, 23);
      this.label2.TabIndex = 110;
      this.label2.Text = "Z";
      this.label2.TextAlign = ContentAlignment.MiddleCenter;
      this.label3.BackColor = Color.Lavender;
      this.label3.BorderStyle = BorderStyle.FixedSingle;
      this.label3.Location = new Point(264, 21);
      this.label3.Name = "label3";
      this.label3.Size = new Size(88, 23);
      this.label3.TabIndex = 111;
      this.label3.Text = "Y";
      this.label3.TextAlign = ContentAlignment.MiddleCenter;
      this.label4.BackColor = Color.Lavender;
      this.label4.BorderStyle = BorderStyle.FixedSingle;
      this.label4.Location = new Point(5, 21);
      this.label4.Name = "label4";
      this.label4.Size = new Size(173, 23);
      this.label4.TabIndex = 112;
      this.label4.Text = "View Type";
      this.label4.TextAlign = ContentAlignment.MiddleCenter;
      this.label5.BackColor = Color.Lavender;
      this.label5.BorderStyle = BorderStyle.FixedSingle;
      this.label5.Location = new Point(177, 21);
      this.label5.Name = "label5";
      this.label5.Size = new Size(88, 23);
      this.label5.TabIndex = 113;
      this.label5.Text = "X";
      this.label5.TextAlign = ContentAlignment.MiddleCenter;
      this.label_View_Item.BackColor = Color.FromArgb(229, 238, 248);
      this.label_View_Item.BorderStyle = BorderStyle.FixedSingle;
      this.label_View_Item.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_View_Item.ForeColor = Color.MidnightBlue;
      this.label_View_Item.Location = new Point(5, 65);
      this.label_View_Item.Name = "label_View_Item";
      this.label_View_Item.Size = new Size(586, 20);
      this.label_View_Item.TabIndex = 109;
      this.label_View_Item.Text = "항목별 View 회전 각도";
      this.label_View_Item.TextAlign = ContentAlignment.MiddleCenter;
      this.label_View_Model.BackColor = Color.FromArgb(229, 238, 248);
      this.label_View_Model.BorderStyle = BorderStyle.FixedSingle;
      this.label_View_Model.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_View_Model.ForeColor = Color.MidnightBlue;
      this.label_View_Model.Location = new Point(5, 2);
      this.label_View_Model.Name = "label_View_Model";
      this.label_View_Model.Size = new Size(586, 20);
      this.label_View_Model.TabIndex = 109;
      this.label_View_Model.Text = "모델 Viewer 회전 각도";
      this.label_View_Model.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_View_Select.ButtonBackColor = Color.White;
      this.newButton_View_Select.ButtonText = "선택 적용";
      this.newButton_View_Select.FlatBorderSize = 1;
      this.newButton_View_Select.FlatStyle = FlatStyle.Flat;
      this.newButton_View_Select.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_View_Select.Image = (Image) null;
      this.newButton_View_Select.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_View_Select.Location = new Point(514, 21);
      this.newButton_View_Select.Name = "newButton_View_Select";
      this.newButton_View_Select.Size = new Size(77, 45);
      this.newButton_View_Select.TabIndex = 116;
      this.newButton_View_Select.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_View_Select.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_View_Select.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_View_All.ButtonBackColor = Color.White;
      this.newButton_View_All.ButtonText = "전체 적용";
      this.newButton_View_All.FlatBorderSize = 1;
      this.newButton_View_All.FlatStyle = FlatStyle.Flat;
      this.newButton_View_All.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_View_All.Image = (Image) null;
      this.newButton_View_All.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_View_All.Location = new Point(438, 21);
      this.newButton_View_All.Name = "newButton_View_All";
      this.newButton_View_All.Size = new Size(77, 45);
      this.newButton_View_All.TabIndex = 116;
      this.newButton_View_All.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_View_All.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_View_All.NewClick += new EventHandler(this.newButton_NewClick);
      this.newTextBox_View_Z.BackColor = Color.LavenderBlush;
      this.newTextBox_View_Z.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_View_Z.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_View_Z.IsDigit = true;
      this.newTextBox_View_Z.Lines = new string[1]{ "0" };
      this.newTextBox_View_Z.Location = new Point(351, 43);
      this.newTextBox_View_Z.MultiLine = false;
      this.newTextBox_View_Z.Name = "newTextBox_View_Z";
      this.newTextBox_View_Z.ReadOnly = false;
      this.newTextBox_View_Z.Size = new Size(88, 23);
      this.newTextBox_View_Z.TabIndex = 115;
      this.newTextBox_View_Z.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_View_Z.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_View_Z.TextForeColor = SystemColors.WindowText;
      this.newTextBox_View_Z.Value = "0";
      this.newTextBox_View_Y.BackColor = Color.LavenderBlush;
      this.newTextBox_View_Y.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_View_Y.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_View_Y.IsDigit = true;
      this.newTextBox_View_Y.Lines = new string[1]{ "0" };
      this.newTextBox_View_Y.Location = new Point(264, 43);
      this.newTextBox_View_Y.MultiLine = false;
      this.newTextBox_View_Y.Name = "newTextBox_View_Y";
      this.newTextBox_View_Y.ReadOnly = false;
      this.newTextBox_View_Y.Size = new Size(88, 23);
      this.newTextBox_View_Y.TabIndex = 115;
      this.newTextBox_View_Y.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_View_Y.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_View_Y.TextForeColor = SystemColors.WindowText;
      this.newTextBox_View_Y.Value = "0";
      this.newTextBox_View_X.BackColor = Color.LavenderBlush;
      this.newTextBox_View_X.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_View_X.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_View_X.IsDigit = true;
      this.newTextBox_View_X.Lines = new string[1]{ "0" };
      this.newTextBox_View_X.Location = new Point(177, 43);
      this.newTextBox_View_X.MultiLine = false;
      this.newTextBox_View_X.Name = "newTextBox_View_X";
      this.newTextBox_View_X.ReadOnly = false;
      this.newTextBox_View_X.Size = new Size(88, 23);
      this.newTextBox_View_X.TabIndex = 115;
      this.newTextBox_View_X.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_View_X.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_View_X.TextForeColor = SystemColors.WindowText;
      this.newTextBox_View_X.Value = "0";
      this.newComboBox_View_Type.BackColor = Color.White;
      this.newComboBox_View_Type.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_View_Type.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_View_Type.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_View_Type.isSameSelect = false;
      this.newComboBox_View_Type.Location = new Point(5, 43);
      this.newComboBox_View_Type.Name = "newComboBox_View_Type";
      this.newComboBox_View_Type.SelectedIndex = -1;
      this.newComboBox_View_Type.Size = new Size(173, 23);
      this.newComboBox_View_Type.TabIndex = 114;
      this.newComboBox_View_Type.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_View_Type.Value = "";
      this.newComboBox_View_Type.SelectedIndexChanged += new EventHandler(this.newComboBox_View_Type_SelectedIndexChanged);
      this.panel1.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.panel1.BorderStyle = BorderStyle.FixedSingle;
      this.panel1.Controls.Add((Control) this.tabControl_Main);
      this.panel1.Location = new Point(5, 31);
      this.panel1.Name = "panel1";
      this.panel1.Size = new Size(599, 954);
      this.panel1.TabIndex = 22;
      this.newButton_Apply.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
      this.newButton_Apply.ButtonBackColor = Color.White;
      this.newButton_Apply.ButtonText = "적용";
      this.newButton_Apply.FlatBorderSize = 1;
      this.newButton_Apply.FlatStyle = FlatStyle.Flat;
      this.newButton_Apply.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Apply.Image = (Image) Resources.OK;
      this.newButton_Apply.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Apply.Location = new Point(5, 989);
      this.newButton_Apply.Name = "newButton_Apply";
      this.newButton_Apply.Size = new Size(599, 23);
      this.newButton_Apply.TabIndex = 23;
      this.newButton_Apply.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Apply.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_View.ButtonBackColor = Color.White;
      this.newButton_View.ButtonText = "뷰 설정";
      this.newButton_View.FlatBorderSize = 1;
      this.newButton_View.FlatStyle = FlatStyle.Flat;
      this.newButton_View.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_View.Image = (Image) null;
      this.newButton_View.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_View.Location = new Point(126, 5);
      this.newButton_View.Name = "newButton_View";
      this.newButton_View.Size = new Size(122, 27);
      this.newButton_View.TabIndex = 20;
      this.newButton_View.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_View.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_View.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_Input.ButtonBackColor = Color.LightSteelBlue;
      this.newButton_Input.ButtonText = "입력 값";
      this.newButton_Input.FlatBorderSize = 1;
      this.newButton_Input.FlatStyle = FlatStyle.Flat;
      this.newButton_Input.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Input.Image = (Image) null;
      this.newButton_Input.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Input.Location = new Point(5, 5);
      this.newButton_Input.Name = "newButton_Input";
      this.newButton_Input.Size = new Size(122, 27);
      this.newButton_Input.TabIndex = 21;
      this.newButton_Input.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Input.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Input.NewClick += new EventHandler(this.newButton_NewClick);
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(609, 1015);
      this.Controls.Add((Control) this.newButton_Apply);
      this.Controls.Add((Control) this.panel1);
      this.Controls.Add((Control) this.newButton_View);
      this.Controls.Add((Control) this.newButton_Input);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmHDSolutions);
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "보고서 작성";
      this.FormClosed += new FormClosedEventHandler(this.frmHDSolutions_FormClosed);
      this.Load += new EventHandler(this.frmHDSolutions_Load);
      this.tabControl_Main.ResumeLayout(false);
      this.tabPage_Input.ResumeLayout(false);
      this.panel3.ResumeLayout(false);
      this.panel3.PerformLayout();
      this.tabPage_View.ResumeLayout(false);
      this.tabPage_View.PerformLayout();
      ((ISupportInitialize) this.dataGridView_View).EndInit();
      this.panel1.ResumeLayout(false);
      this.ResumeLayout(false);
    }
  }
}
