﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmMeshStat
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDMFUserControl;
using HDMoldFlow.Properties;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public class frmMeshStat : Form
  {
    private IContainer components = (IContainer) null;
    private Label label_MeshStat;
    private Label label_Value;
    private NewButton newButton_Apply;
    private NewTextBox newTextBox_Value;

    public frmMeshStat()
    {
      this.InitializeComponent();
      this.newButton_Apply.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Apply.Image);
      this.Text = LocaleControl.getInstance().GetString("IDS_MESH_STATISTICS");
      this.label_MeshStat.Text = LocaleControl.getInstance().GetString("IDS_MESH_STATISTICS") + "(Aspect Ratio)";
      this.label_Value.Text = LocaleControl.getInstance().GetString("IDS_REFERENCE_VALUE");
      this.newButton_Apply.ButtonText = LocaleControl.getInstance().GetString("IDS_APPLY");
    }

    private void frmMeshStat_Load(object sender, EventArgs e)
    {
      this.ActiveControl = (Control) this.label_Value;
      foreach (KeyValuePair<string, string> keyValuePair in clsDefine.g_dicMeshStat)
      {
        KeyValuePair<string, string> kvTmp = keyValuePair;
        ((NewTextBox) this.Controls.Cast<Control>().Where<Control>((Func<Control, bool>) (Temp => Temp.Name.Contains(kvTmp.Key))).FirstOrDefault<Control>()).Value = kvTmp.Value;
      }
    }

    private void frmMeshStat_KeyDown(object sender, KeyEventArgs e)
    {
      if (e.KeyCode != Keys.Escape)
        return;
      this.Close();
    }

    private void newButton_Apply_NewClick(object sender, EventArgs e)
    {
      if (this.newTextBox_Value.Value == "")
        return;
      clsDefine.g_dicMeshStat["Value"] = this.newTextBox_Value.Value;
      foreach (KeyValuePair<string, string> keyValuePair in clsDefine.g_dicMeshStat)
      {
        if (keyValuePair.Key.Contains("Value"))
          clsUtill.WriteINI("AspectRatio", keyValuePair.Key, keyValuePair.Value, clsDefine.g_fiMeshStatCfg.FullName);
      }
      this.Close();
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.label_MeshStat = new Label();
      this.label_Value = new Label();
      this.newButton_Apply = new NewButton();
      this.newTextBox_Value = new NewTextBox();
      this.SuspendLayout();
      this.label_MeshStat.BackColor = Color.FromArgb(229, 238, 248);
      this.label_MeshStat.BorderStyle = BorderStyle.FixedSingle;
      this.label_MeshStat.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_MeshStat.ForeColor = Color.MidnightBlue;
      this.label_MeshStat.Location = new Point(5, 5);
      this.label_MeshStat.Name = "label_MeshStat";
      this.label_MeshStat.Size = new Size(177, 20);
      this.label_MeshStat.TabIndex = 5;
      this.label_MeshStat.Text = "메쉬 통계(Aspect Ratio)";
      this.label_MeshStat.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Value.BackColor = Color.Lavender;
      this.label_Value.BorderStyle = BorderStyle.FixedSingle;
      this.label_Value.Location = new Point(5, 24);
      this.label_Value.Name = "label_Value";
      this.label_Value.Size = new Size(97, 23);
      this.label_Value.TabIndex = 7;
      this.label_Value.Text = "기준값";
      this.label_Value.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.ButtonBackColor = Color.White;
      this.newButton_Apply.ButtonText = "적용";
      this.newButton_Apply.FlatBorderSize = 1;
      this.newButton_Apply.FlatStyle = FlatStyle.Flat;
      this.newButton_Apply.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Apply.Image = (Image) Resources.OK;
      this.newButton_Apply.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Apply.Location = new Point(5, 53);
      this.newButton_Apply.Name = "newButton_Apply";
      this.newButton_Apply.Size = new Size(177, 23);
      this.newButton_Apply.TabIndex = 21;
      this.newButton_Apply.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Apply.NewClick += new EventHandler(this.newButton_Apply_NewClick);
      this.newTextBox_Value.BackColor = SystemColors.Window;
      this.newTextBox_Value.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Value.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Value.IsDigit = true;
      this.newTextBox_Value.Lines = new string[0];
      this.newTextBox_Value.Location = new Point(101, 24);
      this.newTextBox_Value.MultiLine = false;
      this.newTextBox_Value.Name = "newTextBox_Value";
      this.newTextBox_Value.ReadOnly = false;
      this.newTextBox_Value.Size = new Size(81, 23);
      this.newTextBox_Value.TabIndex = 22;
      this.newTextBox_Value.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Value.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Value.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Value.Value = "";
      this.AutoScaleDimensions = new SizeF(7f, 15f);
      this.AutoScaleMode = AutoScaleMode.Font;
      this.BackColor = Color.White;
      this.ClientSize = new Size(188, 83);
      this.Controls.Add((Control) this.newTextBox_Value);
      this.Controls.Add((Control) this.newButton_Apply);
      this.Controls.Add((Control) this.label_Value);
      this.Controls.Add((Control) this.label_MeshStat);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.Margin = new Padding(3, 4, 3, 4);
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmMeshStat);
      this.ShowInTaskbar = false;
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "메쉬 통계";
      this.Load += new EventHandler(this.frmMeshStat_Load);
      this.KeyDown += new KeyEventHandler(this.frmMeshStat_KeyDown);
      this.ResumeLayout(false);
    }
  }
}
