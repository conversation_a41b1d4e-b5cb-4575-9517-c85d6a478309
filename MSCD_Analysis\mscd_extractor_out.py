import re
import csv
import os
import sys

def load_mscd_codes(csv_file):
    """Carga códigos MSCD desde el archivo CSV generado."""
    mscd_dict = {}
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            mscd_dict[row['mscd_code']] = row
    return mscd_dict

def extract_section_data(lines, start_idx):
    """Extrae datos de secciones especiales como perfiles de presión."""
    section_data = []
    i = start_idx
    
    # Buscar la línea que contiene los encabezados de columnas
    while i < len(lines) and not ('duration' in lines[i].lower() and 'pressure' in lines[i].lower()):
        i += 1
    
    if i >= len(lines):
        return [], start_idx
    
    # Avanzar hasta encontrar la línea con guiones (separador)
    while i < len(lines) and '----' not in lines[i]:
        i += 1
    
    if i >= len(lines):
        return [], start_idx
    
    i += 1  # Avanzar después de la línea de guiones
    
    # Recopilar datos hasta encontrar una línea vacía o el final de la sección
    while i < len(lines) and lines[i].strip() and not lines[i].strip().startswith('---'):
        line = lines[i].strip()
        if line:  # Ignorar líneas vacías
            section_data.append(line)
        i += 1
    
    return section_data, i

def normalize_message(message):
    """Normaliza un mensaje para comparación, eliminando caracteres especiales y formateadores."""
    if not message:
        return ""
    
    # Eliminar formateadores como %11.4G, %s, etc.
    normalized = re.sub(r'%[\d\.]*[a-zA-Z]', '', message)
    
    # Eliminar caracteres especiales y normalizar espacios
    normalized = re.sub(r'[\[\]\(\)\{\}\*\+\?\^\$\\\.\|]', ' ', normalized)
    normalized = re.sub(r'\s+', ' ', normalized).strip()
    
    # Si hay un signo igual, tomar solo la parte izquierda
    if '=' in normalized:
        normalized = normalized.split('=')[0].strip()
    
    return normalized

def extract_mscd_values(file_path, mscd_dict, output_file, verbose=False):
    """Extrae valores de los códigos MSCD encontrados en el archivo de texto, respetando el formato."""
    results = []
    
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        lines = [line.rstrip() for line in f.readlines()]
    
    if verbose:
        print(f"Leyendo {len(lines)} líneas del archivo.")
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # Detectar secciones especiales como perfiles de presión
        if 'profile:' in line.lower():
            if verbose:
                print(f"Detectada sección de perfil en línea {i+1}: {line}")
            
            section_data, new_idx = extract_section_data(lines, i+1)
            
            if section_data:
                # Buscar coincidencias en los mensajes de los códigos MSCD para esta sección
                for mscd_code, mscd_info in mscd_dict.items():
                    message_template = mscd_info.get('message', '')
                    if 'profile' in message_template.lower():
                        results.append({
                            'mscd_code': mscd_code,
                            'message': message_template,
                            'value': '\n'.join(section_data),
                            'original_line': line,
                            'is_profile': True
                        })
                        if verbose:
                            print(f"  - Asociado con código MSCD: {mscd_code}")
                        break
            
            if new_idx > i:
                i = new_idx
                continue
        
        # Buscar líneas con formato "clave = valor"
        if '=' in line and not line.startswith('---'):
            key_part = line.split('=')[0].strip()
            value_part = line.split('=')[1].strip() if len(line.split('=')) > 1 else ''
            
            if verbose:
                print(f"Analizando línea {i+1}: {key_part} = {value_part}")
            
            # Buscar coincidencias en los mensajes de los códigos MSCD
            for mscd_code, mscd_info in mscd_dict.items():
                message_template = mscd_info.get('message', '')
                
                # Normalizar el mensaje para comparación
                normalized_message = normalize_message(message_template)
                normalized_key = normalize_message(key_part)
                
                # Verificar si el mensaje normalizado está contenido en la clave normalizada
                if normalized_message and normalized_message in normalized_key:
                    results.append({
                        'mscd_code': mscd_code,
                        'message': message_template,
                        'value': value_part,
                        'original_line': line,
                        'is_profile': False
                    })
                    if verbose:
                        print(f"  - Coincidencia encontrada con código MSCD: {mscd_code}")
                    break
        i += 1
    
    # Guardar resultados en CSV
    fieldnames = ['mscd_code', 'message', 'value', 'original_line', 'is_profile']
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        for result in results:
            writer.writerow(result)
    
    return len(results)

def main():
    if len(sys.argv) < 2:
        print("Uso: python mscd_extractor_out.py <archivo_txt> [--verbose]")
        print("Ejemplo: python mscd_extractor_out.py c:\\Moldflow\\Test.txt")
        return
    
    input_file = sys.argv[1]
    verbose = "--verbose" in sys.argv
    
    if not os.path.exists(input_file):
        print(f"Error: El archivo {input_file} no existe.")
        return
    
    # Rutas de archivos
    base_dir = os.path.dirname(os.path.abspath(__file__))
    mscd_csv = os.path.join(base_dir, 'mscd_codes.csv')
    
    if not os.path.exists(mscd_csv):
        print(f"Error: Archivo de códigos MSCD no encontrado en {mscd_csv}")
        print("Ejecute mscd_extractor.py primero para generar el archivo de códigos.")
        return
    
    # Nombre del archivo de salida basado en el archivo de entrada
    input_basename = os.path.basename(input_file)
    output_file = os.path.join(base_dir, f"extracted_{input_basename}.csv")
    
    print(f"Cargando códigos MSCD desde {mscd_csv}...")
    mscd_dict = load_mscd_codes(mscd_csv)
    print(f"Cargados {len(mscd_dict)} códigos MSCD.")
    
    print(f"Analizando archivo: {input_file}...")
    count = extract_mscd_values(input_file, mscd_dict, output_file, verbose)
    print(f"Se encontraron {count} valores de códigos MSCD en el archivo.")
    print(f"Resultados guardados en: {output_file}")

if __name__ == "__main__":
    main()