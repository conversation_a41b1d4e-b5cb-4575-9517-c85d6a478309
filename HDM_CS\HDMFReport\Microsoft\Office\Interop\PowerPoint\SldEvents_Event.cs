﻿// Decompiled with JetBrains decompiler
// Type: Microsoft.Office.Interop.PowerPoint.SldEvents_Event
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

namespace Microsoft.Office.Interop.PowerPoint
{
  [CompilerGenerated]
  [ComEventInterface(typeof (SldEvents), typeof (SldEvents))]
  [TypeIdentifier("91493440-5a91-11cf-8700-00aa0060263b", "Microsoft.Office.Interop.PowerPoint.SldEvents_Event")]
  [ComImport]
  public interface SldEvents_Event
  {
  }
}
