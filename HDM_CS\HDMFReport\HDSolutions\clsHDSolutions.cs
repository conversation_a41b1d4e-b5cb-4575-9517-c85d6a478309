﻿// Decompiled with JetBrains decompiler
// Type: HDMFReport.clsHDSolutions
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using HDLocale;
using HDLog4Net;
using HDMoldFlowLibrary;
using Microsoft.Office.Core;
using Microsoft.Office.Interop.PowerPoint;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace HDMFReport
{
  internal class clsHDSolutions : clsBase
  {
    public override void ExportReport(
      DataRow p_drStudy,
      string p_strLangType,
      clsHDMFLibDefine.Company p_enumCompany)
    {
      Dictionary<string, string> p_dicValue = new Dictionary<string, string>();
      Dictionary<string, string> p_dicView = new Dictionary<string, string>();
      Dictionary<string, string> p_dicUse = new Dictionary<string, string>();
      try
      {
        p_dicValue.Clear();
        p_dicView.Clear();
        p_dicUse.Clear();
        clsHDSolutionData.GetReportUser(p_drStudy, out p_dicValue, out p_dicView, out p_dicUse);
        this.StartExport(p_drStudy, p_dicValue, p_dicView, p_dicUse, p_enumCompany, p_strLangType);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]ExportReport):" + ex.Message));
      }
    }

    protected override void StartExport(
      DataRow p_drStudy,
      Dictionary<string, string> p_dicValue,
      Dictionary<string, string> p_dicView,
      Dictionary<string, string> p_dicUse,
      clsHDMFLibDefine.Company p_enumCompany,
      string p_strLangType = "KOR")
    {
      Dictionary<string, string> analysisData = clsHDSolutionData.GetAnalysisData(p_drStudy, p_dicValue, p_dicView);
      int num = 1;
      FileInfo fileInfo;
      while (true)
      {
        fileInfo = new FileInfo(clsReportDefine.g_diProject.Parent.FullName + "\\" + clsReportDefine.g_diProject.Name + "_Export\\" + p_drStudy["Name"] + "_" + DateTime.Now.ToString("yyMMdd") + "_Report_Rev" + (object) num + "." + clsReportDefine.g_dicExtension["PowerPoint"]);
        if (fileInfo.Exists)
          ++num;
        else
          break;
      }
      if (!fileInfo.Directory.Exists)
        fileInfo.Directory.Create();
      try
      {
        // ISSUE: variable of a compiler-generated type
        Presentations presentations = ((_Application) Activator.CreateInstance(Marshal.GetTypeFromCLSID(new Guid("91493441-5A91-11CF-8700-00AA0060263B")))).Presentations;
        FileInfo template = clsReportData.GetTemplate(p_enumCompany, p_strLangType);
        template.Refresh();
        if (template.Exists)
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          Presentation presentation = presentations.Open(template.FullName, MsoTriState.msoTrue, WithWindow: MsoTriState.msoFalse);
          List<Slide> allSlide = this.GetAllSlide(presentation);
          if (p_dicUse.Count > 0)
          {
            List<Slide> slideList = new List<Slide>();
            foreach (KeyValuePair<string, string> keyValuePair in p_dicUse)
            {
              KeyValuePair<string, string> kvpTmp = keyValuePair;
              if (!clsReportUtill.ConvertToBoolean(kvpTmp.Value))
              {
                // ISSUE: variable of a compiler-generated type
                Slide slide = allSlide.Where<Slide>((System.Func<Slide, bool>) (Temp => Temp.Name.Contains(kvpTmp.Key))).FirstOrDefault<Slide>();
                if (slide != null)
                  slideList.Add(slide);
              }
            }
            for (int index = 0; index < slideList.Count; ++index)
            {
              // ISSUE: reference to a compiler-generated method
              slideList[index].Delete();
              allSlide.Remove(slideList[index]);
            }
          }
          if (!p_drStudy["Sequence"].ToString().Contains("Cool"))
          {
            List<Slide> slideList = new List<Slide>();
            foreach (Slide slide in allSlide)
            {
              if (slide.Name == "Result1_2")
                slideList.Add(slide);
              else if (slide.Name.Contains("Cool"))
                slideList.Add(slide);
            }
            for (int index = 0; index < slideList.Count; ++index)
            {
              // ISSUE: reference to a compiler-generated method
              slideList[index].Delete();
              allSlide.Remove(slideList[index]);
            }
          }
          if (p_drStudy["Sequence"].ToString().Contains("Warp"))
          {
            List<Slide> slideList = new List<Slide>();
            foreach (Slide slide in allSlide)
            {
              if (slide.Name.Contains("DefX_2"))
              {
                if (p_dicValue["DefX1"] == "Best fit")
                  slideList.Add(slide);
              }
              else if (slide.Name.Contains("DefY_2"))
              {
                if (p_dicValue["DefY1"] == "Best fit")
                  slideList.Add(slide);
              }
              else if (slide.Name.Contains("DefZ_2") && p_dicValue["DefZ1"] == "Best fit")
                slideList.Add(slide);
            }
            for (int index = 0; index < slideList.Count; ++index)
            {
              // ISSUE: reference to a compiler-generated method
              slideList[index].Delete();
              allSlide.Remove(slideList[index]);
            }
          }
          else
          {
            List<Slide> slideList = new List<Slide>();
            foreach (Slide slide in allSlide)
            {
              if (slide.Name.Contains("Warp"))
                slideList.Add(slide);
            }
            for (int index = 0; index < slideList.Count; ++index)
            {
              // ISSUE: reference to a compiler-generated method
              slideList[index].Delete();
              allSlide.Remove(slideList[index]);
            }
          }
          foreach (Slide p_slData in allSlide)
          {
            switch (p_slData.Name)
            {
              case "AirTrap":
                this.SetSlide26(p_slData, this.GetSlide26(analysisData));
                continue;
              case "CircuitCoolantTemp_Cool":
                this.SetSlide28(p_slData, this.GetSlide28(analysisData));
                continue;
              case "ClampForce":
                this.SetSlide21(p_slData, this.GetSlide21(analysisData));
                continue;
              case "CoolingWater_Cool":
                this.SetSlide31(p_slData, this.GetSlide31(analysisData));
                continue;
              case "DefAll_Warp":
                this.SetSlide33(p_slData, this.GetSlide33(analysisData));
                continue;
              case "DefISO_Warp":
                this.SetSlide34(p_slData, this.GetSlide34(analysisData));
                continue;
              case "DefX_2_Warp":
                this.SetSlide38(p_slData, this.GetSlide38(analysisData));
                continue;
              case "DefX_Warp":
                this.SetSlide35(p_slData, this.GetSlide35(analysisData));
                continue;
              case "DefY_2_Warp":
                this.SetSlide39(p_slData, this.GetSlide39(analysisData));
                continue;
              case "DefY_Warp":
                this.SetSlide36(p_slData, this.GetSlide36(analysisData));
                continue;
              case "DefZ_2_Warp":
                this.SetSlide40(p_slData, this.GetSlide40(analysisData));
                continue;
              case "DefZ_Warp":
                this.SetSlide37(p_slData, this.GetSlide37(analysisData));
                continue;
              case "FillTime_1":
                this.SetSlide17(p_slData, this.GetSlide17(analysisData));
                continue;
              case "FillTime_2":
                this.SetSlide18(p_slData, this.GetSlide18(analysisData));
                continue;
              case "FrozenLayerFraction":
                this.SetSlide22(p_slData, this.GetSlide22(analysisData));
                continue;
              case "Material":
                this.SetSlide8(p_slData, this.GetSlide8(analysisData));
                continue;
              case "Material_1":
                this.SetSlide9(p_slData, this.GetSlide9(analysisData));
                continue;
              case "Mesh_1":
                this.SetSlide6(p_slData, this.GetSlide6(analysisData));
                continue;
              case "Mesh_2":
                this.SetSlide7(p_slData, this.GetSlide7(analysisData));
                continue;
              case "MoldTemp_Cool":
                this.SetSlide29(p_slData, this.GetSlide29(analysisData));
                continue;
              case "Pressure":
                this.SetSlide20(p_slData, this.GetSlide20(analysisData));
                continue;
              case "ProcessSetting_1":
                this.SetSlide11(p_slData, this.GetSlide11(analysisData));
                continue;
              case "Product_1":
                this.SetSlide4(p_slData, this.GetSlide4(analysisData));
                continue;
              case "Result1_1":
                this.SetSlide13(p_slData, this.GetSlide13(analysisData));
                continue;
              case "Result1_2":
                this.SetSlide14(p_slData, this.GetSlide14(analysisData));
                continue;
              case "Result1_3":
                this.SetSlide15(p_slData, this.GetSlide15(analysisData));
                continue;
              case "Shrinkage":
                this.SetSlide23(p_slData, this.GetSlide23(analysisData));
                continue;
              case "SinkMark":
                this.SetSlide24(p_slData, this.GetSlide24(analysisData));
                continue;
              case "TemperatureAtFlowFront":
                this.SetSlide19(p_slData, this.GetSlide19(analysisData));
                continue;
              case "TimeToReachEjectionTemp":
                this.SetSlide30(p_slData, this.GetSlide30(analysisData));
                continue;
              case "Title":
                this.SetSlide1(p_slData, this.GetSlide1(analysisData));
                continue;
              case "WeldLine":
                this.SetSlide25(p_slData, this.GetSlide25(analysisData));
                continue;
              default:
                continue;
            }
          }
          if (!fileInfo.Extension.Contains(".ppt"))
          {
            // ISSUE: reference to a compiler-generated method
            presentation.SaveAs(fileInfo.FullName.Replace(fileInfo.Extension, ".pptx"));
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            presentation.SaveAs(fileInfo.FullName);
          }
          // ISSUE: reference to a compiler-generated method
          presentation.Close();
          clsReportUtill.ReleaseComObject((object) presentation);
        }
        if (fileInfo.Extension.Contains(".ppt"))
          return;
        if (fileInfo.Exists)
          fileInfo.Delete();
        File.Move(fileInfo.FullName.Replace(fileInfo.Extension, ".pptx"), fileInfo.FullName);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]StartExport):" + ex.Message));
      }
    }

    private List<Slide> GetAllSlide(Presentation p_pptPre)
    {
      List<Slide> allSlide = new List<Slide>((IEnumerable<Slide>) p_pptPre.Slides.Cast<Slide>().ToArray<Slide>());
      try
      {
        for (int index = 0; index < allSlide.Count; ++index)
        {
          switch (index)
          {
            case 0:
              allSlide[index].Name = "Title";
              break;
            case 1:
              allSlide[index].Name = "Contents";
              break;
            case 2:
              allSlide[index].Name = "Product";
              break;
            case 3:
              allSlide[index].Name = "Product_1";
              break;
            case 4:
              allSlide[index].Name = "Mesh";
              break;
            case 5:
              allSlide[index].Name = "Mesh_1";
              break;
            case 6:
              allSlide[index].Name = "Mesh_2";
              break;
            case 7:
              allSlide[index].Name = "Material";
              break;
            case 8:
              allSlide[index].Name = "Material_1";
              break;
            case 9:
              allSlide[index].Name = "ProcessSetting";
              break;
            case 10:
              allSlide[index].Name = "ProcessSetting_1";
              break;
            case 11:
              allSlide[index].Name = "Result1";
              break;
            case 12:
              allSlide[index].Name = "Result1_1";
              break;
            case 13:
              allSlide[index].Name = "Result1_2";
              break;
            case 14:
              allSlide[index].Name = "Result1_3";
              break;
            case 15:
              allSlide[index].Name = "Result2";
              break;
            case 16:
              allSlide[index].Name = "FillTime_1";
              break;
            case 17:
              allSlide[index].Name = "FillTime_2";
              break;
            case 18:
              allSlide[index].Name = "TemperatureAtFlowFront";
              break;
            case 19:
              allSlide[index].Name = "Pressure";
              break;
            case 20:
              allSlide[index].Name = "ClampForce";
              break;
            case 21:
              allSlide[index].Name = "FrozenLayerFraction";
              break;
            case 22:
              allSlide[index].Name = "Shrinkage";
              break;
            case 23:
              allSlide[index].Name = "SinkMark";
              break;
            case 24:
              allSlide[index].Name = "WeldLine";
              break;
            case 25:
              allSlide[index].Name = "AirTrap";
              break;
            case 26:
              allSlide[index].Name = "Result_Cool";
              break;
            case 27:
              allSlide[index].Name = "CircuitCoolantTemp_Cool";
              break;
            case 28:
              allSlide[index].Name = "MoldTemp_Cool";
              break;
            case 29:
              allSlide[index].Name = "TimeToReachEjectionTemp";
              break;
            case 30:
              allSlide[index].Name = "CoolingWater_Cool";
              break;
            case 31:
              allSlide[index].Name = "Result_Warp";
              break;
            case 32:
              allSlide[index].Name = "DefAll_Warp";
              break;
            case 33:
              allSlide[index].Name = "DefISO_Warp";
              break;
            case 34:
              allSlide[index].Name = "DefX_Warp";
              break;
            case 35:
              allSlide[index].Name = "DefY_Warp";
              break;
            case 36:
              allSlide[index].Name = "DefZ_Warp";
              break;
            case 37:
              allSlide[index].Name = "DefX_2_Warp";
              break;
            case 38:
              allSlide[index].Name = "DefY_2_Warp";
              break;
            case 39:
              allSlide[index].Name = "DefZ_2_Warp";
              break;
            case 40:
              allSlide[index].Name = "Finish";
              break;
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetAllSlide):" + ex.Message));
      }
      return allSlide;
    }

    private Dictionary<string, string> GetSlide1(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide1 = new Dictionary<string, string>();
      try
      {
        slide1.Add("Item", p_dicData["Item[User]"]);
        slide1.Add("Sequence", p_dicData["Sequence[User]"]);
        slide1.Add("Engineer", p_dicData["Engineer[User]"]);
        slide1.Add("Manager", p_dicData["Manager[User]"]);
        slide1.Add("Image", p_dicData["Title[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide1):" + ex.Message));
      }
      return slide1;
    }

    private Dictionary<string, string> GetSlide4(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide4 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ModelRunner[IMG]")))
          slide4.Add("Image1", p_dicData["ModelRunner[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cool[IMG]")))
          slide4.Add("Image2", p_dicData["Cool[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ModelRunnerCool1[IMG]")))
          slide4.Add("Image3", p_dicData["ModelRunnerCool1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ModelRunnerCool2[IMG]")))
          slide4.Add("Image4", p_dicData["ModelRunnerCool2[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide4):" + ex.Message));
      }
      return slide4;
    }

    private Dictionary<string, string> GetSlide6(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide6 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Thickness[IMG]")))
          slide6.Add("Image", p_dicData["Thickness[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ElementNumber[Log]")))
          slide6.Add("Element Number", p_dicData["ElementNumber[Log]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MeshType[Log]")))
          slide6.Add("Mesh Type", p_dicData["MeshType[Log]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Sequence[User]")))
          slide6.Add("Analysis Sequence", p_dicData["Sequence[User]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PartVolume[Log]")))
          slide6.Add("Part Volume", p_dicData["PartVolume[Log]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ProjectedArea[Log]")))
          slide6.Add("Projected Area", p_dicData["ProjectedArea[Log]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Thickness[MF]")))
          slide6.Add("Thickness", p_dicData["Thickness[MF]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide6):" + ex.Message));
      }
      return slide6;
    }

    private Dictionary<string, string> GetSlide7(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide7 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CoolingChannel[IMG]")))
          slide7.Add("Image", p_dicData["CoolingChannel[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CoolingLineDimension[MF]")))
          slide7.Add("Cooling Line Dimension", p_dicData["CoolingLineDimension[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "BaffleLineDimension[MF]")))
          slide7.Add("Baffle Line Dimension", p_dicData["BaffleLineDimension[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "RunnerSystemType[MF]")))
          slide7.Add("Runner System Type", p_dicData["RunnerSystemType[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CoolantInletTemperature[MF]")))
          slide7.Add("Coolant Inlet Temperature", p_dicData["CoolantInletTemperature[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MoldTempMin[Log]")) && p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MoldTempMax[Log]")))
          slide7.Add("MoldTemp MinMax", p_dicData["MoldTempMin[Log]"] + "|" + p_dicData["MoldTempMax[Log]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MoldTempAvg[Log]")))
          slide7.Add("MoldTemp Avg", p_dicData["MoldTempAvg[Log]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MoldSurfaceTemperature[MF]")))
          slide7.Add("Setted MoldTemp", p_dicData["MoldSurfaceTemperature[MF]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide6):" + ex.Message));
      }
      return slide7;
    }

    private Dictionary<string, string> GetSlide8(Dictionary<string, string> p_dicData)
    {
      StringBuilder stringBuilder = new StringBuilder();
      Dictionary<string, string> slide8 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Manufacturer[MF]")))
          stringBuilder.Append(p_dicData["Manufacturer[MF]"]);
        stringBuilder.Append("_");
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TradeName[MF]")))
          stringBuilder.Append(p_dicData["TradeName[MF]"]);
        stringBuilder.Append("_");
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FamilyAbbreviation[MF]")))
          stringBuilder.Append(p_dicData["FamilyAbbreviation[MF]"]);
        slide8.Add("Material", stringBuilder.ToString());
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide8):" + ex.Message));
      }
      return slide8;
    }

    private Dictionary<string, string> GetSlide9(Dictionary<string, string> p_dicData)
    {
      StringBuilder stringBuilder = new StringBuilder();
      Dictionary<string, string> slide9 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Viscosity[MF]")))
          slide9.Add("Image1", p_dicData["Viscosity[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SpecifiedVolume[MF]")))
          slide9.Add("Image2", p_dicData["SpecifiedVolume[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Manufacturer[MF]")))
        {
          slide9.Add("Manufacturer", p_dicData["Manufacturer[MF]"]);
          stringBuilder.Append(p_dicData["Manufacturer[MF]"]);
        }
        stringBuilder.Append("_");
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TradeName[MF]")))
        {
          slide9.Add("Trade Name", p_dicData["TradeName[MF]"]);
          stringBuilder.Append(p_dicData["TradeName[MF]"]);
        }
        stringBuilder.Append("_");
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FamilyAbbreviation[MF]")))
        {
          slide9.Add("Family Abbreviation", p_dicData["FamilyAbbreviation[MF]"]);
          stringBuilder.Append(p_dicData["FamilyAbbreviation[MF]"]);
        }
        slide9.Add("Material", stringBuilder.ToString());
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TransitionTemp[MF]")))
          slide9.Add("Transition Temperature", p_dicData["TransitionTemp[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "EjectionTemp[MF]")))
          slide9.Add("Ejection Temperature", p_dicData["EjectionTemp[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ShearRate[MF]")))
          slide9.Add("Maximum Shear Rate", p_dicData["ShearRate[MF]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide9):" + ex.Message));
      }
      return slide9;
    }

    private Dictionary<string, string> GetSlide11(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide11 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MeltTemp[MF]")))
          slide11.Add("Melt Temp", p_dicData["MeltTemp[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MoldTemp[MF]")))
          slide11.Add("Mold Temp", p_dicData["MoldTemp[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CoolingTime[MF]")))
          slide11.Add("Cooling Time", p_dicData["CoolingTime[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillingControlType[MF]")))
          slide11.Add("Filling Control Type", p_dicData["FillingControlType[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillingControl[MF]")))
          slide11.Add("Filling Control", p_dicData["FillingControl[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VPSwitchOver[MF]")))
          slide11.Add("V/P Switch-over", p_dicData["VPSwitchOver[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PackHoldingControl[MF]")))
          slide11.Add("Pack/Holding Control", p_dicData["PackHoldingControl[MF]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide11):" + ex.Message));
      }
      return slide11;
    }

    private Dictionary<string, string> GetSlide13(Dictionary<string, string> p_dicData)
    {
      StringBuilder stringBuilder = new StringBuilder();
      Dictionary<string, string> slide13 = new Dictionary<string, string>();
      try
      {
        stringBuilder.Clear();
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillingFilled[Log]")))
          stringBuilder.Append(p_dicData["FillingFilled[Log]"]);
        else
          stringBuilder.Append("");
        stringBuilder.Append("|");
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ShortShot[Log]")))
        {
          if (p_dicData["ShortShot[Log]"] == "X")
            stringBuilder.Append(LocaleControl.getInstance().GetString("IDS_NO_SHORT"));
          else
            stringBuilder.Append(LocaleControl.getInstance().GetString("IDS_SHORT"));
        }
        else
          stringBuilder.Append("");
        stringBuilder.Append("|");
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Balance[User]")))
          stringBuilder.Append(p_dicData["Balance[User]"].Split('|')[1]);
        else
          stringBuilder.Append("");
        slide13.Add("Fill Pattern", stringBuilder.ToString());
        stringBuilder.Clear();
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SpruePressure[Log]")))
        {
          double num = clsReportUtill.ConvertToDouble(p_dicData["SpruePressure[Log]"]);
          stringBuilder.Append(num.ToString() + "|" + (object) Math.Round(num * 1.25, 2));
        }
        else
          stringBuilder.Append("|");
        slide13.Add("Pressure", stringBuilder.ToString());
        stringBuilder.Clear();
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ClampForce[Log]")))
        {
          double num = clsReportUtill.ConvertToDouble(p_dicData["ClampForce[Log]"]);
          stringBuilder.Append(num.ToString() + "|" + (object) Math.Round(num * 1.25, 2));
        }
        else
          stringBuilder.Append("|");
        slide13.Add("Clamp Force", stringBuilder.ToString());
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureAtFlowFront[Plot]")))
        {
          string[] strArray = p_dicData["TemperatureAtFlowFront[Plot]"].Split('|');
          slide13.Add("Temperature At Flow Front", strArray[0] + "|" + strArray[1]);
        }
        stringBuilder.Clear();
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperatureCavity[Log]")) && p_dicData["CircuitCoolantTemperatureCavity[Log]"] != "")
        {
          string[] strArray = p_dicData["CircuitCoolantTemperatureCavity[Log]"].Split('|');
          stringBuilder.Append(strArray[0] + "|" + strArray[1]);
        }
        else
          stringBuilder.Append("");
        stringBuilder.Append("/");
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperatureCore[Log]")) && p_dicData["CircuitCoolantTemperatureCore[Log]"] != "")
        {
          string[] strArray = p_dicData["CircuitCoolantTemperatureCore[Log]"].Split('|');
          stringBuilder.Append(strArray[0] + "|" + strArray[1]);
        }
        else
          stringBuilder.Append("");
        stringBuilder.Append("/");
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureMold1[Plot]")))
        {
          string[] strArray = p_dicData["TemperatureMold1[Plot]"].Split('|');
          stringBuilder.Append(strArray[0] + "|" + strArray[1]);
        }
        else
          stringBuilder.Append("");
        slide13.Add("Cooling", stringBuilder.ToString());
        stringBuilder.Clear();
        double num1 = 0.0;
        double num2 = 0.0;
        double num3 = 0.0;
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillingTime[User]")))
        {
          num1 = Math.Round(clsReportUtill.ConvertToDouble(p_dicData["FillingTime[User]"]), 2);
          stringBuilder.Append(num1);
        }
        else
          stringBuilder.Append("");
        stringBuilder.Append("|");
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PackingTime[User]")))
        {
          num2 = Math.Round(clsReportUtill.ConvertToDouble(p_dicData["PackingTime[User]"]), 2);
          stringBuilder.Append(num2);
        }
        else
          stringBuilder.Append("");
        stringBuilder.Append("|");
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenTime100%[Log]")))
        {
          if (p_dicData["FrozenTime100%[Log]"] != string.Empty)
            num3 = Math.Round(clsReportUtill.ConvertToDouble(p_dicData["FrozenTime100%[Log]"]) * 0.8, 2);
          else if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperature[Plot]")))
            num3 = Math.Round(clsReportUtill.ConvertToDouble(p_dicData["TimeToReachEjectionTemperature[Plot]"]), 2);
          if (num3 - num1 - num2 > 0.0)
            stringBuilder.Append(Math.Round(num3 - num1 - num2, 2));
          else
            stringBuilder.Append(0);
        }
        else
          stringBuilder.Append("");
        stringBuilder.Append("|");
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Schizonepeta[User]")))
          stringBuilder.Append(p_dicData["Schizonepeta[User]"]);
        else
          stringBuilder.Append("");
        slide13.Add("Cycle Time", stringBuilder.ToString());
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Countermeasure[User]")))
          slide13.Add("Conclusion", p_dicData["Countermeasure[User]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide13):" + ex.Message));
      }
      return slide13;
    }

    private Dictionary<string, string> GetSlide14(Dictionary<string, string> p_dicData)
    {
      string empty = string.Empty;
      string str = "|";
      StringBuilder stringBuilder = new StringBuilder();
      Dictionary<string, string> slide14 = new Dictionary<string, string>();
      try
      {
        stringBuilder.Clear();
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperatureCavity[Log]")))
        {
          if (p_dicData["CircuitCoolantTemperatureCavity[Log]"] == null || p_dicData["CircuitCoolantTemperatureCavity[Log]"] == "")
          {
            stringBuilder.Append("||");
          }
          else
          {
            double num = clsReportUtill.ConvertToDouble(p_dicData["CircuitCoolantTemperatureCavity[Log]"].Split('|')[2]);
            stringBuilder.Append(num);
            if (num <= 3.0)
              stringBuilder.Append("|NO Problem|OK");
            else
              stringBuilder.Append("|Discussion|NG");
          }
          slide14.Add("Circuit Coolant Temperature(Cavity)", stringBuilder.ToString());
        }
        stringBuilder.Clear();
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperatureCore[Log]")))
        {
          if (p_dicData["CircuitCoolantTemperatureCore[Log]"] == null || p_dicData["CircuitCoolantTemperatureCore[Log]"] == "")
          {
            stringBuilder.Append("||");
          }
          else
          {
            double num = clsReportUtill.ConvertToDouble(p_dicData["CircuitCoolantTemperatureCore[Log]"].Split('|')[2]);
            stringBuilder.Append(num);
            if (num <= 3.0)
              stringBuilder.Append("|NO Problem|OK");
            else
              stringBuilder.Append("|Discussion|NG");
          }
          slide14.Add("Circuit Coolant Temperature(Core)", stringBuilder.ToString());
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cooling[User]")))
        {
          string[] strArray = p_dicData["Cooling[User]"].Split('|');
          str = (!(strArray[0] == "OK") ? (!(strArray[0] == "Discussion") ? "Problem" : "Discussion") : "NO Problem") + "|" + strArray[0];
        }
        stringBuilder.Clear();
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitFlowRate[Plot]")) && p_dicData["CircuitFlowRate[Plot]"] != null && p_dicData["CircuitFlowRate[Plot]"] != "")
          stringBuilder.Append(p_dicData["CircuitFlowRate[Plot]"]);
        stringBuilder.Append("|");
        stringBuilder.Append(str);
        slide14.Add("Circuit Flow Rate", stringBuilder.ToString());
        stringBuilder.Clear();
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitPressure[Plot]")) && p_dicData["CircuitPressure[Plot]"] != null && p_dicData["CircuitPressure[Plot]"] != "")
          stringBuilder.Append(p_dicData["CircuitPressure[Plot]"]);
        stringBuilder.Append("|");
        stringBuilder.Append(str);
        slide14.Add("Circuit Pressure", stringBuilder.ToString());
        stringBuilder.Clear();
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperature[Plot]")))
        {
          if (p_dicData["TimeToReachEjectionTemperature[Plot]"] == null || p_dicData["TimeToReachEjectionTemperature[Plot]"] == "")
          {
            stringBuilder.Append("||");
          }
          else
          {
            double num1 = clsReportUtill.ConvertToDouble(p_dicData["TimeToReachEjectionTemperature[Plot]"]);
            stringBuilder.Append(num1);
            double num2 = !(p_dicData["FrozenTime95%[Log]"] == string.Empty) ? clsReportUtill.ConvertToDouble(p_dicData["FrozenTime95%[Log]"]) : num1;
            if (num1 >= num2)
              stringBuilder.Append("|NO Problem|OK");
            else
              stringBuilder.Append("|Discussion|NG");
          }
          slide14.Add("Time To Reach Ejection Temperature", stringBuilder.ToString());
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureMold1[Plot]")))
        {
          if (p_dicData["TemperatureMold1[Plot]"] == null || p_dicData["TemperatureMold1[Plot]"] == "")
            slide14.Add("Temperature, Mold", "||");
          else
            slide14.Add("Temperature, Mold", p_dicData["TemperatureMold1[Plot]"].Split('|')[1] + "||");
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperaturePart[Plot]")))
        {
          if (p_dicData["TemperaturePart[Plot]"] == null || p_dicData["TemperaturePart[Plot]"] == "")
            slide14.Add("Temperature, Part", "||");
          else
            slide14.Add("Temperature, Part", p_dicData["TemperaturePart[Plot]"].Split('|')[1] + "||");
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide14):" + ex.Message));
      }
      return slide14;
    }

    private Dictionary<string, string> GetSlide15(Dictionary<string, string> p_dicData)
    {
      StringBuilder stringBuilder = new StringBuilder();
      Dictionary<string, string> slide15 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Balance[User]")))
        {
          string[] strArray = p_dicData["Balance[User]"].Split('|');
          string str = "";
          if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillingFilled[Log]")))
            str = p_dicData["FillingFilled[Log]"];
          slide15.Add("Balance", strArray[1] + "|" + str + "|" + strArray[0]);
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SpruePressure[Log]")))
        {
          double num = clsReportUtill.ConvertToDouble(p_dicData["SpruePressure[Log]"]);
          slide15.Add("Pressure", num.ToString() + "|" + (object) Math.Round(num / 0.8, 2) + "|");
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ClampForce[Log]")))
        {
          double num = clsReportUtill.ConvertToDouble(p_dicData["ClampForce[Log]"]);
          slide15.Add("Clamp Force", num.ToString() + "|" + (object) Math.Round(num / 0.8, 2) + "|");
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "WeldLine[User]")))
        {
          string[] strArray = p_dicData["WeldLine[User]"].Split('|');
          string str = !(strArray[0] == "OK") ? (!(strArray[0] == "Discussion") ? "Problem" : "Discussion") : "NO Problem";
          slide15.Add("Weld Line", strArray[1] + "|" + str + "|" + strArray[0]);
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Shrinkage/Sink[User]")))
        {
          string[] strArray = p_dicData["Shrinkage/Sink[User]"].Split('|');
          string str = !(strArray[0] == "OK") ? (!(strArray[0] == "Discussion") ? "Problem" : "Discussion") : "NO Problem";
          slide15.Add("Sink Mark", strArray[1] + "|" + str + "|" + strArray[0]);
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "AirTrap[User]")))
        {
          string[] strArray = p_dicData["AirTrap[User]"].Split('|');
          string str = !(strArray[0] == "OK") ? (!(strArray[0] == "Discussion") ? "Problem" : "Discussion") : "NO Problem";
          slide15.Add("Air Trap", strArray[1] + "|" + str + "|" + strArray[0]);
        }
        stringBuilder.Clear();
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll_BestFit[Plot]")))
          stringBuilder.Append(p_dicData["DefAll_BestFit[Plot]"]);
        stringBuilder.Append("/");
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll_AnchorPlan[Plot]")))
          stringBuilder.Append(p_dicData["DefAll_AnchorPlan[Plot]"]);
        if (stringBuilder.Length > 0)
          slide15.Add("Deflection All", stringBuilder.ToString());
        stringBuilder.Clear();
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX_BestFit[Plot]")))
          stringBuilder.Append(p_dicData["DefX_BestFit[Plot]"]);
        stringBuilder.Append("/");
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX_AnchorPlan[Plot]")))
          stringBuilder.Append(p_dicData["DefX_AnchorPlan[Plot]"]);
        if (stringBuilder.Length > 0)
          slide15.Add("Deflection X", stringBuilder.ToString());
        stringBuilder.Clear();
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY_BestFit[Plot]")))
          stringBuilder.Append(p_dicData["DefY_BestFit[Plot]"]);
        stringBuilder.Append("/");
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY_AnchorPlan[Plot]")))
          stringBuilder.Append(p_dicData["DefY_AnchorPlan[Plot]"]);
        if (stringBuilder.Length > 0)
          slide15.Add("Deflection Y", stringBuilder.ToString());
        stringBuilder.Clear();
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ_BestFit[Plot]")))
          stringBuilder.Append(p_dicData["DefZ_BestFit[Plot]"]);
        stringBuilder.Append("/");
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ_AnchorPlan[Plot]")))
          stringBuilder.Append(p_dicData["DefZ_AnchorPlan[Plot]"]);
        if (stringBuilder.Length > 0)
          slide15.Add("Deflection Z", stringBuilder.ToString());
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide15):" + ex.Message));
      }
      return slide15;
    }

    private Dictionary<string, string> GetSlide17(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide17 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern1[IMG]")))
          slide17.Add("Image1", p_dicData["FlowPattern1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern2[IMG]")))
          slide17.Add("Image2", p_dicData["FlowPattern2[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern3[IMG]")))
          slide17.Add("Image3", p_dicData["FlowPattern3[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern4[IMG]")))
          slide17.Add("Image4", p_dicData["FlowPattern4[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern5[IMG]")))
          slide17.Add("Image5", p_dicData["FlowPattern5[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern6[IMG]")))
          slide17.Add("Image6", p_dicData["FlowPattern6[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern7[IMG]")))
          slide17.Add("Image7", p_dicData["FlowPattern7[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern8[IMG]")))
          slide17.Add("Image8", p_dicData["FlowPattern8[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide17):" + ex.Message));
      }
      return slide17;
    }

    private Dictionary<string, string> GetSlide18(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide18 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillAnimation[IMG]")))
          slide18.Add("Image", p_dicData["FillAnimation[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide18):" + ex.Message));
      }
      return slide18;
    }

    private Dictionary<string, string> GetSlide19(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide19 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureAtFlowFront[IMG]")))
          slide19.Add("Image", p_dicData["TemperatureAtFlowFront[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureAtFlowFront[Plot]")))
          slide19.Add("Temperature At Flow Front", p_dicData["TemperatureAtFlowFront[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MaterialStructure[MF]")))
        {
          string str1 = p_dicData["MaterialStructure[MF]"];
          string str2 = !(str1.ToUpper() == "AMORPHOUS") ? (!(str1.ToUpper() == "CRYSTALLINE") ? "0" : "5") : "10";
          slide19.Add("Material Structure", str2);
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureAtFlowFront_Min[Plot]")))
          slide19.Add("Min", p_dicData["TemperatureAtFlowFront_Min[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureAtFlowFront_Min[IMG]")))
          slide19.Add("Min Image", p_dicData["TemperatureAtFlowFront_Min[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureAtFlowFront_Max[Plot]")))
          slide19.Add("Max", p_dicData["TemperatureAtFlowFront_Max[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureAtFlowFront_Max[IMG]")))
          slide19.Add("Max Image", p_dicData["TemperatureAtFlowFront_Max[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide19):" + ex.Message));
      }
      return slide19;
    }

    private Dictionary<string, string> GetSlide20(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide20 = new Dictionary<string, string>();
      string str = "PressureXY";
      string strTmp = str;
      double minValue = double.MinValue;
      bool flag = false;
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PressureAtInjectionLocation[IMG]")))
          slide20.Add("Image1", p_dicData["PressureAtInjectionLocation[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PressureXY[IMG]")))
          slide20.Add("Image2", p_dicData["PressureXY[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PressureXY_1[IMG]")))
          slide20.Add("Image3", p_dicData["PressureXY_1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PressureXY_2[IMG]")))
          slide20.Add("Image4", p_dicData["PressureXY_2[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PressureXY_3[IMG]")))
          slide20.Add("Image5", p_dicData["PressureXY_3[IMG]"]);
        StringBuilder stringBuilder1 = new StringBuilder();
        StringBuilder stringBuilder2 = new StringBuilder();
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SpruePressure[Log]")))
        {
          double num = clsReportUtill.ConvertToDouble(p_dicData["SpruePressure[Log]"]);
          stringBuilder1.Append(num);
          stringBuilder2.Append(Math.Round(num * 1.25, 2));
        }
        strTmp = str;
        for (int index = 0; index < 4; ++index)
        {
          if (index > 0)
            strTmp = str + "_" + index.ToString();
          if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strTmp + "[Plot]")))
          {
            if (clsReportUtill.ConvertToDouble(p_dicData[strTmp + "[Plot]"]) > minValue)
              minValue = clsReportUtill.ConvertToDouble(p_dicData[strTmp + "[Plot]"]);
            flag = true;
          }
        }
        if (flag)
        {
          stringBuilder1.Append("|");
          stringBuilder1.Append(minValue);
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PressureXY[Plot]")))
        {
          stringBuilder1.Append("|");
          stringBuilder1.Append(p_dicData["PressureXY[Plot]"]);
        }
        if (stringBuilder1.Length > 0)
          slide20.Add("Injection Pressure1", stringBuilder1.ToString());
        if (stringBuilder2.Length > 0)
          slide20.Add("Injection Pressure2", stringBuilder2.ToString());
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide20):" + ex.Message));
      }
      return slide20;
    }

    private Dictionary<string, string> GetSlide21(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide21 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ClampForceXY[IMG]")))
          slide21.Add("Image", p_dicData["ClampForceXY[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ClampForce[Log]")))
        {
          double num = clsReportUtill.ConvertToDouble(p_dicData["ClampForce[Log]"]);
          slide21.Add("Clamp1", num.ToString());
          slide21.Add("Clamp2", Math.Round(num / 0.8, 2).ToString());
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide21):" + ex.Message));
      }
      return slide21;
    }

    private Dictionary<string, string> GetSlide22(Dictionary<string, string> p_dicData)
    {
      double num1 = 0.0;
      double minValue = double.MinValue;
      Dictionary<string, string> slide22 = new Dictionary<string, string>();
      string str = "FrozenLayerFractionXY";
      string strTmp = str;
      bool flag = false;
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFraction1[IMG]")))
          slide22.Add("Image1", p_dicData["FrozenLayerFraction1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFraction2[IMG]")))
          slide22.Add("Image2", p_dicData["FrozenLayerFraction2[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFraction3[IMG]")))
          slide22.Add("Image3", p_dicData["FrozenLayerFraction3[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFraction4[IMG]")))
          slide22.Add("Image4", p_dicData["FrozenLayerFraction4[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFractionXY[IMG]")))
          slide22.Add("Image5", p_dicData["FrozenLayerFractionXY[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFractionXY_1[IMG]")))
          slide22.Add("Image6", p_dicData["FrozenLayerFractionXY_1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFractionXY_2[IMG]")))
          slide22.Add("Image7", p_dicData["FrozenLayerFractionXY_2[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFractionXY_3[IMG]")))
          slide22.Add("Image8", p_dicData["FrozenLayerFractionXY_3[IMG]"]);
        for (int index = 0; index < 4; ++index)
        {
          if (index > 0)
            strTmp = str + "_" + index.ToString();
          if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strTmp + "[Plot]")) && clsReportUtill.ConvertToDouble(p_dicData[strTmp + "[Plot]"]) > minValue)
          {
            minValue = clsReportUtill.ConvertToDouble(p_dicData[strTmp + "[Plot]"]);
            flag = true;
          }
        }
        if (flag)
        {
          slide22.Add("Time1", minValue.ToString());
          num1 = minValue;
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PhaseZeroPressureTime[Log]")))
        {
          double num2 = Math.Round(clsReportUtill.ConvertToDouble(p_dicData["PhaseZeroPressureTime[Log]"]) - num1, 2, MidpointRounding.AwayFromZero);
          slide22.Add("Time2", num2.ToString());
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide22):" + ex.Message));
      }
      return slide22;
    }

    private Dictionary<string, string> GetSlide23(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide23 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VolumetricShrinkage1[IMG]")))
          slide23.Add("Image1", p_dicData["VolumetricShrinkage1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VolumetricShrinkage2[IMG]")))
          slide23.Add("Image2", p_dicData["VolumetricShrinkage2[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VolumetricShrinkage3[IMG]")))
          slide23.Add("Image3", p_dicData["VolumetricShrinkage3[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VolumetricShrinkage2[Plot]")))
        {
          string[] strArray = p_dicData["VolumetricShrinkage2[Plot]"].Split('|');
          slide23.Add("Shrinkage1", strArray[0] + "~" + strArray[1]);
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VolumetricShrinkage3[Plot]")))
        {
          string[] strArray = p_dicData["VolumetricShrinkage3[Plot]"].Split('|');
          slide23.Add("Shrinkage2", strArray[0] + "~" + strArray[1]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide23):" + ex.Message));
      }
      return slide23;
    }

    private Dictionary<string, string> GetSlide24(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide24 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SinkMark1[IMG]")))
          slide24.Add("Image1", p_dicData["SinkMark1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SinkMark2[IMG]")))
          slide24.Add("Image2", p_dicData["SinkMark2[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SinkMark[Plot]")))
        {
          string[] strArray = p_dicData["SinkMark[Plot]"].Split('|');
          slide24.Add("Sink Mark", strArray[0] + "~" + strArray[1]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide24):" + ex.Message));
      }
      return slide24;
    }

    private Dictionary<string, string> GetSlide25(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide25 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "WeldLine[IMG]")))
          slide25.Add("Image1", p_dicData["WeldLine[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "WeldLine_Small[IMG]")))
          slide25.Add("Image2", p_dicData["WeldLine_Small[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide25):" + ex.Message));
      }
      return slide25;
    }

    private Dictionary<string, string> GetSlide26(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide26 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "AirTrap[IMG]")))
          slide26.Add("Image", p_dicData["AirTrap[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide26):" + ex.Message));
      }
      return slide26;
    }

    private Dictionary<string, string> GetSlide28(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide28 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperature[IMG]")))
          slide28.Add("Image", p_dicData["CircuitCoolantTemperature[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperatureCavity[Log]")) && p_dicData["CircuitCoolantTemperatureCavity[Log]"] != null && p_dicData["CircuitCoolantTemperatureCavity[Log]"] != "")
          slide28.Add("Circuit Coolant Temperature(Cavity)", p_dicData["CircuitCoolantTemperatureCavity[Log]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperatureCore[Log]")))
        {
          if (p_dicData["CircuitCoolantTemperatureCore[Log]"] != null)
          {
            if (p_dicData["CircuitCoolantTemperatureCore[Log]"] != "")
              slide28.Add("Circuit Coolant Temperature(Core)", p_dicData["CircuitCoolantTemperatureCore[Log]"]);
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide28):" + ex.Message));
      }
      return slide28;
    }

    private Dictionary<string, string> GetSlide29(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide29 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureMold1[IMG]")))
          slide29.Add("Image1", p_dicData["TemperatureMold1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureMold2[IMG]")))
          slide29.Add("Image2", p_dicData["TemperatureMold2[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureMold3[IMG]")))
          slide29.Add("Image3", p_dicData["TemperatureMold3[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureMold4[IMG]")))
          slide29.Add("Image4", p_dicData["TemperatureMold4[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureMold5[IMG]")))
          slide29.Add("Image5", p_dicData["TemperatureMold5[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureMold2[Plot]")))
          slide29.Add("Temperature, Mold", p_dicData["TemperatureMold2[Plot]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide29):" + ex.Message));
      }
      return slide29;
    }

    private Dictionary<string, string> GetSlide30(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide30 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperature1[IMG]")))
          slide30.Add("Image1", p_dicData["TimeToReachEjectionTemperature1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperature2[IMG]")))
          slide30.Add("Image2", p_dicData["TimeToReachEjectionTemperature2[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperature[Plot]")))
          slide30.Add("Time To Reach Ejection Temperature", p_dicData["TimeToReachEjectionTemperature[Plot]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide29):" + ex.Message));
      }
      return slide30;
    }

    private Dictionary<string, string> GetSlide31(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide31 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitFlowRate[IMG]")))
          slide31.Add("Image1", p_dicData["CircuitFlowRate[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitPressure[IMG]")))
          slide31.Add("Image2", p_dicData["CircuitPressure[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitFlowRate[Plot]")))
          slide31.Add("Circuit Flow Rate", p_dicData["CircuitFlowRate[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitPressure[Plot]")))
          slide31.Add("Circuit Pressure", p_dicData["CircuitPressure[Plot]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide31):" + ex.Message));
      }
      return slide31;
    }

    private Dictionary<string, string> GetSlide33(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide33 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll[IMG]")))
          slide33.Add("Image", p_dicData["DefAll[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide33):" + ex.Message));
      }
      return slide33;
    }

    private Dictionary<string, string> GetSlide34(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide34 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll[IMG]")))
          slide34.Add("Image1", p_dicData["DefAll[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionCooling[IMG]")))
          slide34.Add("Image2", p_dicData["DeflectionCooling[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionShrinkage[IMG]")))
          slide34.Add("Image3", p_dicData["DeflectionShrinkage[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionOrientation[IMG]")))
          slide34.Add("Image4", p_dicData["DeflectionOrientation[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide33):" + ex.Message));
      }
      return slide34;
    }

    private Dictionary<string, string> GetSlide35(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide35 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX_BestFit[IMG]")))
          slide35.Add("Image", p_dicData["DefX_BestFit[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide35):" + ex.Message));
      }
      return slide35;
    }

    private Dictionary<string, string> GetSlide36(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide36 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY_BestFit[IMG]")))
          slide36.Add("Image", p_dicData["DefY_BestFit[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide36):" + ex.Message));
      }
      return slide36;
    }

    private Dictionary<string, string> GetSlide37(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide37 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ_BestFit[IMG]")))
          slide37.Add("Image", p_dicData["DefZ_BestFit[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide37):" + ex.Message));
      }
      return slide37;
    }

    private Dictionary<string, string> GetSlide38(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide38 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX_AnchorPlan[IMG]")))
          slide38.Add("Image", p_dicData["DefX_AnchorPlan[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide38):" + ex.Message));
      }
      return slide38;
    }

    private Dictionary<string, string> GetSlide39(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide39 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY_AnchorPlan[IMG]")))
          slide39.Add("Image", p_dicData["DefY_AnchorPlan[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide39):" + ex.Message));
      }
      return slide39;
    }

    private Dictionary<string, string> GetSlide40(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide40 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ_AnchorPlan[IMG]")))
          slide40.Add("Image", p_dicData["DefZ_AnchorPlan[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]GetSlide40):" + ex.Message));
      }
      return slide40;
    }

    private void SetSlide1(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Title == "S1_Title")).FirstOrDefault<Shape>();
        if (shape1 != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Item")) && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Sequence")) && p_dicSData["Item"] != "" && p_dicSData["Sequence"] != "")
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(p_dicSData["Item"] + Environment.NewLine + p_dicSData["Sequence"]);
          textRange.Font.Bold = MsoTriState.msoTrue;
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Title == "S1_WrittenBy")).FirstOrDefault<Shape>();
        if (shape2 != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Engineer")) && p_dicSData["Engineer"] != "")
        {
          // ISSUE: reference to a compiler-generated method
          shape2.TextFrame.TextRange.InsertAfter(p_dicSData["Engineer"]);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape3 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Title == "S1_ApprovedBy")).FirstOrDefault<Shape>();
        if (shape3 != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Manager")) && p_dicSData["Manager"] != "")
        {
          // ISSUE: reference to a compiler-generated method
          shape3.TextFrame.TextRange.InsertAfter(p_dicSData["Manager"]);
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 295f, 129f, 369f, 294f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide1):" + ex.Message));
      }
    }

    private void SetSlide4(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 50f, 123f, 306f, 267f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 388f, 123f, 297f, 266f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 718f, 123f, 192f, 141f);
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image4")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image4"], MsoTriState.msoFalse, MsoTriState.msoTrue, 718f, 272f, 192f, 141f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide4):" + ex.Message));
      }
    }

    private void SetSlide6(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string empty = string.Empty;
      StringBuilder stringBuilder = new StringBuilder();
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 50f, 104f, 435f, 355f);
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S5_Table")).FirstOrDefault<Shape>().Table;
        if (table == null)
          return;
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Element Number")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = table.Rows[1].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(p_dicSData["Element Number"]);
          textRange.Font.Bold = MsoTriState.msoTrue;
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Mesh Type")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = table.Rows[2].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(p_dicSData["Mesh Type"]);
          textRange.Font.Bold = MsoTriState.msoTrue;
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Analysis Sequence")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = table.Rows[3].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(p_dicSData["Analysis Sequence"]);
          textRange.Font.Bold = MsoTriState.msoTrue;
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Part Volume")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = table.Rows[4].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.InsertBefore(p_dicSData["Part Volume"] + " ");
          textRange.Font.Bold = MsoTriState.msoTrue;
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Projected Area")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = table.Rows[5].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.InsertBefore(p_dicSData["Projected Area"] + " ");
          textRange.Font.Bold = MsoTriState.msoTrue;
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Thickness")))
          return;
        string[] strArray1 = p_dicSData["Thickness"].Split('/');
        for (int index = 0; index < 3; ++index)
        {
          string[] strArray2 = strArray1[index].Split('|');
          if (stringBuilder.Length != 0)
            stringBuilder.Append(" | ");
          stringBuilder.Append(strArray2[0] + "t | " + strArray2[1] + "%");
        }
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = table.Rows[6].Cells[2].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange1.InsertBefore(stringBuilder.ToString());
        textRange1.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide6):" + ex.Message));
      }
    }

    private void SetSlide7(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 50f, 104f, 435f, 355f);
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S5_Table")).FirstOrDefault<Shape>().Table;
        if (table == null)
          return;
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cooling Line Dimension")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = table.Rows[1].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(p_dicSData["Cooling Line Dimension"] + "mm");
          textRange.Font.Bold = MsoTriState.msoTrue;
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Baffle Line Dimension")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = table.Rows[2].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(p_dicSData["Baffle Line Dimension"] + "mm");
          textRange.Font.Bold = MsoTriState.msoTrue;
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Runner System Type")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = table.Rows[3].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(p_dicSData["Runner System Type"]);
          textRange.Font.Bold = MsoTriState.msoTrue;
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Coolant Inlet Temperature")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = table.Rows[4].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(p_dicSData["Coolant Inlet Temperature"] + "℃");
          textRange.Font.Bold = MsoTriState.msoTrue;
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MoldTemp MinMax")))
        {
          string[] strArray = p_dicSData["MoldTemp MinMax"].Split('|');
          table.Rows[5].Cells[2].Shape.TextFrame.TextRange.Text = strArray[0] + "℃ ~ " + strArray[1] + "℃";
          // ISSUE: reference to a compiler-generated method
          table.Rows[5].Cells[2].Shape.TextFrame.TextRange.Words().Font.Bold = MsoTriState.msoTrue;
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MoldTemp Avg")))
        {
          table.Rows[6].Cells[2].Shape.TextFrame.TextRange.Text = p_dicSData["MoldTemp Avg"] + "℃";
          // ISSUE: reference to a compiler-generated method
          table.Rows[6].Cells[2].Shape.TextFrame.TextRange.Words().Font.Bold = MsoTriState.msoTrue;
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Setted MoldTemp")))
          return;
        table.Rows[7].Cells[2].Shape.TextFrame.TextRange.Text = p_dicSData["Setted MoldTemp"] + "℃";
        // ISSUE: reference to a compiler-generated method
        table.Rows[7].Cells[2].Shape.TextFrame.TextRange.Words().Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide7):" + ex.Message));
      }
    }

    private void SetSlide8(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "Subtitle 5")).FirstOrDefault<Shape>();
        if (shape == null || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Material")))
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange = shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange.InsertAfter(p_dicSData["Material"]);
        textRange.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide8):" + ex.Message));
      }
    }

    private void SetSlide9(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 104f, 97f, 331f, 252f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 453f, 97f, 331f, 252f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "세로 내용 개체 틀 4")).FirstOrDefault<Shape>();
        if (shape != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Material")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(p_dicSData["Material"]);
          textRange.Font.Bold = MsoTriState.msoTrue;
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S3_Table1")).FirstOrDefault<Shape>().Table;
        if (table == null)
          return;
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Manufacturer")))
        {
          table.Rows[1].Cells[2].Shape.TextFrame.TextRange.Text = p_dicSData["Manufacturer"];
          // ISSUE: reference to a compiler-generated method
          table.Rows[1].Cells[2].Shape.TextFrame.TextRange.Words().Font.Bold = MsoTriState.msoTrue;
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Trade Name")))
        {
          table.Rows[2].Cells[2].Shape.TextFrame.TextRange.Text = p_dicSData["Trade Name"];
          // ISSUE: reference to a compiler-generated method
          table.Rows[2].Cells[2].Shape.TextFrame.TextRange.Words().Font.Bold = MsoTriState.msoTrue;
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Family Abbreviation")))
        {
          table.Rows[3].Cells[2].Shape.TextFrame.TextRange.Text = p_dicSData["Family Abbreviation"];
          // ISSUE: reference to a compiler-generated method
          table.Rows[3].Cells[2].Shape.TextFrame.TextRange.Words().Font.Bold = MsoTriState.msoTrue;
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Transition Temperature")))
        {
          table.Rows[4].Cells[2].Shape.TextFrame.TextRange.Text = p_dicSData["Transition Temperature"];
          // ISSUE: reference to a compiler-generated method
          table.Rows[4].Cells[2].Shape.TextFrame.TextRange.Words().Font.Bold = MsoTriState.msoTrue;
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Ejection Temperature")))
        {
          table.Rows[5].Cells[2].Shape.TextFrame.TextRange.Text = p_dicSData["Ejection Temperature"];
          // ISSUE: reference to a compiler-generated method
          table.Rows[5].Cells[2].Shape.TextFrame.TextRange.Words().Font.Bold = MsoTriState.msoTrue;
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Maximum Shear Rate")))
          return;
        table.Rows[6].Cells[2].Shape.TextFrame.TextRange.Text = p_dicSData["Maximum Shear Rate"];
        // ISSUE: reference to a compiler-generated method
        table.Rows[6].Cells[2].Shape.TextFrame.TextRange.Words().Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide9):" + ex.Message));
      }
    }

    private void SetSlide11(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string str1 = "";
      string str2 = "";
      string str3 = "";
      StringBuilder stringBuilder = new StringBuilder();
      try
      {
        // ISSUE: variable of a compiler-generated type
        Table table1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S11_Table1")).FirstOrDefault<Shape>().Table;
        if (table1 != null)
        {
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Melt Temp")))
          {
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = table1.Rows[1].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter(p_dicSData["Melt Temp"] + "℃");
            textRange.Font.Bold = MsoTriState.msoTrue;
          }
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Mold Temp")))
          {
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = table1.Rows[2].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter(p_dicSData["Mold Temp"] + "℃");
            textRange.Font.Bold = MsoTriState.msoTrue;
          }
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cooling Time")))
          {
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = table1.Rows[3].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter(p_dicSData["Cooling Time"] + "s");
            textRange.Font.Bold = MsoTriState.msoTrue;
          }
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Filling Control Type")))
          {
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = table1.Rows[4].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter(p_dicSData["Filling Control Type"] + "s");
            textRange.Font.Bold = MsoTriState.msoTrue;
          }
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "V/P Switch-over")))
          {
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = table1.Rows[5].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter(p_dicSData["V/P Switch-over"]);
            textRange.Font.Bold = MsoTriState.msoTrue;
          }
          stringBuilder.Clear();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Pack/Holding Control")))
          {
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = table1.Rows[6].Cells[2].Shape.TextFrame.TextRange;
            string[] strArray1 = p_dicSData["Pack/Holding Control"].Replace("\r\n", "").Split('/');
            string[] strArray2 = strArray1[0].Replace("\r\n", "").Split('|');
            str1 = strArray2[0];
            str2 = strArray2[2];
            string str4 = strArray2[1];
            string str5 = strArray2[3];
            if (strArray1.Length > 1)
            {
              for (int index = 1; index < strArray1.Length; ++index)
              {
                string[] strArray3 = strArray1[index].Replace("\r\n", "").Split('|');
                if (stringBuilder.Length > 0)
                  stringBuilder.Append(Environment.NewLine);
                stringBuilder.Append(strArray3[0] + str4 + " - " + strArray3[1] + str5);
              }
              // ISSUE: reference to a compiler-generated method
              textRange.InsertAfter(stringBuilder.ToString());
              textRange.Font.Bold = MsoTriState.msoTrue;
            }
          }
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Filling Control")))
        {
          string[] strArray4 = p_dicSData["Filling Control"].Replace("\r\n", "").Split(',');
          if (strArray4.Length != 0)
          {
            string[] strArray5 = strArray4[0].Split('*');
            // ISSUE: variable of a compiler-generated type
            Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "Rectangle 17")).FirstOrDefault<Shape>();
            if (shape != null)
            {
              string[] strArray6 = strArray5[0].Split('|');
              string NewText = strArray6[0];
              str3 = strArray6[1];
              // ISSUE: variable of a compiler-generated type
              TextRange textRange = shape.TextFrame.TextRange;
              // ISSUE: reference to a compiler-generated method
              textRange.Delete();
              // ISSUE: reference to a compiler-generated method
              textRange.InsertAfter(NewText);
            }
            // ISSUE: variable of a compiler-generated type
            Table table2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S11_Table2")).FirstOrDefault<Shape>().Table;
            if (table2 != null)
            {
              string[] strArray7 = strArray5[1].Split('|');
              for (int index = 0; index < 12; ++index)
              {
                if (strArray7.Length <= index)
                {
                  table2.Rows[index + 1].Cells[1].Shape.TextFrame.TextRange.Text = "";
                  table2.Rows[index + 1].Cells[2].Shape.TextFrame.TextRange.Text = "";
                }
                else
                {
                  table2.Rows[index + 1].Cells[1].Shape.TextFrame.TextRange.Text = strArray7[index];
                  // ISSUE: reference to a compiler-generated method
                  table2.Rows[index + 1].Cells[1].Shape.TextFrame.TextRange.Words().Font.Bold = MsoTriState.msoTrue;
                  table2.Rows[index + 1].Cells[2].Shape.TextFrame.TextRange.Text = str3;
                }
              }
            }
          }
          if (strArray4.Length > 1)
          {
            string[] strArray8 = strArray4[1].Replace("\r\n", "").Split('*');
            // ISSUE: variable of a compiler-generated type
            Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "Rectangle 18")).FirstOrDefault<Shape>();
            if (shape != null)
            {
              string[] strArray9 = strArray8[0].Replace("\r\n", "").Split('|');
              string NewText = strArray9[0];
              str3 = strArray9[1];
              // ISSUE: variable of a compiler-generated type
              TextRange textRange = shape.TextFrame.TextRange;
              // ISSUE: reference to a compiler-generated method
              textRange.Delete();
              // ISSUE: reference to a compiler-generated method
              textRange.InsertAfter(NewText);
            }
            // ISSUE: variable of a compiler-generated type
            Table table3 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S11_Table4")).FirstOrDefault<Shape>().Table;
            if (table3 != null)
            {
              string[] strArray10 = strArray8[1].Replace("\r\n", "").Split('|');
              for (int index = 0; index < 12; ++index)
              {
                if (strArray10.Length <= index)
                {
                  table3.Rows[index + 1].Cells[1].Shape.TextFrame.TextRange.Text = "";
                  table3.Rows[index + 1].Cells[2].Shape.TextFrame.TextRange.Text = "";
                }
                else
                {
                  table3.Rows[index + 1].Cells[1].Shape.TextFrame.TextRange.Text = strArray10[index];
                  // ISSUE: reference to a compiler-generated method
                  table3.Rows[index + 1].Cells[1].Shape.TextFrame.TextRange.Words().Font.Bold = MsoTriState.msoTrue;
                  table3.Rows[index + 1].Cells[2].Shape.TextFrame.TextRange.Text = str3;
                }
              }
            }
          }
          if (strArray4.Length > 2)
          {
            string[] strArray11 = strArray4[2].Split('*');
            // ISSUE: variable of a compiler-generated type
            Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "Rectangle 20")).FirstOrDefault<Shape>();
            if (shape != null)
            {
              string[] strArray12 = strArray11[0].Split('|');
              string NewText = strArray12[0];
              str3 = strArray12[1];
              // ISSUE: variable of a compiler-generated type
              TextRange textRange = shape.TextFrame.TextRange;
              // ISSUE: reference to a compiler-generated method
              textRange.Delete();
              // ISSUE: reference to a compiler-generated method
              textRange.InsertAfter(NewText);
            }
            // ISSUE: variable of a compiler-generated type
            Table table4 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S11_Table3")).FirstOrDefault<Shape>().Table;
            if (table4 != null)
            {
              string[] strArray13 = strArray11[1].Split('|');
              for (int index = 0; index < 1; ++index)
              {
                if (strArray13.Length <= index)
                {
                  table4.Rows[index + 1].Cells[1].Shape.TextFrame.TextRange.Text = "";
                  table4.Rows[index + 1].Cells[2].Shape.TextFrame.TextRange.Text = "";
                }
                else
                {
                  table4.Rows[index + 1].Cells[1].Shape.TextFrame.TextRange.Text = strArray13[index];
                  // ISSUE: reference to a compiler-generated method
                  table4.Rows[index + 1].Cells[1].Shape.TextFrame.TextRange.Words().Font.Bold = MsoTriState.msoTrue;
                  table4.Rows[index + 1].Cells[2].Shape.TextFrame.TextRange.Text = str3;
                }
              }
            }
          }
          if (strArray4.Length > 3)
          {
            string[] strArray14 = strArray4[3].Split('*');
            // ISSUE: variable of a compiler-generated type
            Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "Rectangle 21")).FirstOrDefault<Shape>();
            if (shape != null)
            {
              string[] strArray15 = strArray14[0].Split('|');
              string NewText = strArray15[0];
              str3 = strArray15[1];
              // ISSUE: variable of a compiler-generated type
              TextRange textRange = shape.TextFrame.TextRange;
              // ISSUE: reference to a compiler-generated method
              textRange.Delete();
              // ISSUE: reference to a compiler-generated method
              textRange.InsertAfter(NewText);
            }
            // ISSUE: variable of a compiler-generated type
            Table table5 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S11_Table5")).FirstOrDefault<Shape>().Table;
            if (table5 != null)
            {
              string[] strArray16 = strArray14[1].Split('|');
              for (int index = 0; index < 1; ++index)
              {
                if (strArray16.Length <= index)
                {
                  table5.Rows[index + 1].Cells[1].Shape.TextFrame.TextRange.Text = "";
                  table5.Rows[index + 1].Cells[2].Shape.TextFrame.TextRange.Text = "";
                }
                else
                {
                  table5.Rows[index + 1].Cells[1].Shape.TextFrame.TextRange.Text = strArray16[index];
                  // ISSUE: reference to a compiler-generated method
                  table5.Rows[index + 1].Cells[1].Shape.TextFrame.TextRange.Words().Font.Bold = MsoTriState.msoTrue;
                  table5.Rows[index + 1].Cells[2].Shape.TextFrame.TextRange.Text = str3;
                }
              }
            }
          }
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Pack/Holding Control")))
          return;
        string[] strArray17 = p_dicSData["Pack/Holding Control"].Replace("\r\n", "").Split('/');
        string[] strArray18 = strArray17[0].Replace("\r\n", "").Split('|');
        string NewText1 = strArray18[0];
        string NewText2 = strArray18[2];
        string str6 = strArray18[1];
        string str7 = strArray18[3];
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "Rectangle 24")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(NewText1);
          textRange.Font.Bold = MsoTriState.msoTrue;
        }
        // ISSUE: variable of a compiler-generated type
        Table table6 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S11_Table6")).FirstOrDefault<Shape>().Table;
        if (table6 != null)
        {
          string[] strArray19 = new string[strArray17.Length - 1];
          for (int index = 1; index < strArray17.Length; ++index)
            strArray19[index - 1] = strArray17[index];
          for (int index = 0; index < 3; ++index)
          {
            if (strArray19.Length <= index)
            {
              table6.Rows[index + 1].Cells[1].Shape.TextFrame.TextRange.Text = "";
              table6.Rows[index + 1].Cells[2].Shape.TextFrame.TextRange.Text = "";
            }
            else
            {
              string[] strArray20 = strArray19[index].Replace("\r\n", "").Split('|');
              table6.Rows[index + 1].Cells[1].Shape.TextFrame.TextRange.Text = strArray20[0];
              // ISSUE: reference to a compiler-generated method
              table6.Rows[index + 1].Cells[1].Shape.TextFrame.TextRange.Words().Font.Bold = MsoTriState.msoTrue;
              table6.Rows[index + 1].Cells[2].Shape.TextFrame.TextRange.Text = str6;
              // ISSUE: reference to a compiler-generated method
              table6.Rows[index + 1].Cells[2].Shape.TextFrame.TextRange.Words().Font.Bold = MsoTriState.msoTrue;
            }
          }
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "Rectangle 27")).FirstOrDefault<Shape>();
        if (shape2 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape2.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(NewText2);
          textRange.Font.Bold = MsoTriState.msoTrue;
        }
        // ISSUE: variable of a compiler-generated type
        Table table7 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S11_Table7")).FirstOrDefault<Shape>().Table;
        if (table7 == null)
          return;
        string[] strArray21 = new string[strArray17.Length - 1];
        for (int index = 1; index < strArray17.Length; ++index)
          strArray21[index - 1] = strArray17[index];
        for (int index = 0; index < 3; ++index)
        {
          if (strArray21.Length <= index)
          {
            table7.Rows[index + 1].Cells[1].Shape.TextFrame.TextRange.Text = "";
            table7.Rows[index + 1].Cells[2].Shape.TextFrame.TextRange.Text = "";
          }
          else
          {
            string[] strArray22 = strArray21[index].Replace("\r\n", "").Split('|');
            table7.Rows[index + 1].Cells[1].Shape.TextFrame.TextRange.Text = strArray22[1];
            // ISSUE: reference to a compiler-generated method
            table7.Rows[index + 1].Cells[1].Shape.TextFrame.TextRange.Words().Font.Bold = MsoTriState.msoTrue;
            table7.Rows[index + 1].Cells[2].Shape.TextFrame.TextRange.Text = str7;
            // ISSUE: reference to a compiler-generated method
            table7.Rows[index + 1].Cells[2].Shape.TextFrame.TextRange.Words().Font.Bold = MsoTriState.msoTrue;
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide11):" + ex.Message));
      }
    }

    private void SetSlide13(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        // ISSUE: variable of a compiler-generated type
        Table table1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "표 17")).FirstOrDefault<Shape>().Table;
        if (table1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange1 = table1.Rows[1].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange1.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Fill Pattern")))
          {
            string[] strArray = p_dicSData["Fill Pattern"].Split('|');
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = textRange1.InsertAfter("충진 완료 시간 : ");
            textRange2.Font.Bold = MsoTriState.msoTrue;
            textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            if (strArray[0] != "")
            {
              // ISSUE: reference to a compiler-generated method
              textRange2 = textRange2.InsertAfter(strArray[0]);
              textRange2.Font.Bold = MsoTriState.msoTrue;
              textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange3 = textRange2.InsertAfter("s");
            textRange3.Font.Bold = MsoTriState.msoTrue;
            textRange3.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange4 = textRange3.InsertAfter(Environment.NewLine + "미성형 여부 : ");
            textRange4.Font.Bold = MsoTriState.msoTrue;
            textRange4.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange5 = textRange4.InsertAfter(strArray[1]);
            textRange5.Font.Bold = MsoTriState.msoTrue;
            textRange5.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange6 = textRange5.InsertAfter(Environment.NewLine + "유동 밸런스 : ");
            textRange6.Font.Bold = MsoTriState.msoTrue;
            textRange6.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange7 = textRange6.InsertAfter(strArray[2]);
            textRange7.Font.Bold = MsoTriState.msoTrue;
            textRange7.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange8 = table1.Rows[2].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange8.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Pressure")))
          {
            string[] strArray = p_dicSData["Pressure"].Split('|');
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange9 = textRange8.InsertAfter("Sprue Pressure : ");
            textRange9.Font.Bold = MsoTriState.msoTrue;
            textRange9.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange10 = textRange9.InsertAfter(strArray[0]);
            textRange10.Font.Bold = MsoTriState.msoTrue;
            textRange10.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange11 = textRange10.InsertAfter("MPa");
            textRange11.Font.Bold = MsoTriState.msoTrue;
            textRange11.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange12 = textRange11.InsertAfter(Environment.NewLine + "안전률을 고려하여 ");
            textRange12.Font.Bold = MsoTriState.msoTrue;
            textRange12.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange13 = textRange12.InsertAfter(strArray[1]);
            textRange13.Font.Bold = MsoTriState.msoTrue;
            textRange13.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange14 = textRange13.InsertAfter("Mpa 이상인 사출기에서 사출하길 권장 드립니다.");
            textRange14.Font.Bold = MsoTriState.msoTrue;
            textRange14.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange15 = table1.Rows[3].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange15.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Clamp Force")))
          {
            string[] strArray = p_dicSData["Clamp Force"].Split('|');
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange16 = textRange15.InsertAfter("Clamp Force : ");
            textRange16.Font.Bold = MsoTriState.msoTrue;
            textRange16.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange17 = textRange16.InsertAfter(strArray[0]);
            textRange17.Font.Bold = MsoTriState.msoTrue;
            textRange17.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange18 = textRange17.InsertAfter("t");
            textRange18.Font.Bold = MsoTriState.msoTrue;
            textRange18.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange19 = textRange18.InsertAfter(Environment.NewLine + "안전률을 고려하여 ");
            textRange19.Font.Bold = MsoTriState.msoTrue;
            textRange19.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange20 = textRange19.InsertAfter(strArray[1]);
            textRange20.Font.Bold = MsoTriState.msoTrue;
            textRange20.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange21 = textRange20.InsertAfter("t 이상인 사출기에서 사출하길 권장 드립니다.");
            textRange21.Font.Bold = MsoTriState.msoTrue;
            textRange21.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange22 = table1.Rows[4].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange22.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Temperature At Flow Front")))
          {
            string[] strArray = p_dicSData["Temperature At Flow Front"].Split('|');
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange23 = textRange22.InsertAfter(strArray[0] + "℃ ~ " + strArray[1] + "℃");
            textRange23.Font.Bold = MsoTriState.msoTrue;
            textRange23.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange24 = table1.Rows[5].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange24.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cooling")))
          {
            string[] strArray1 = p_dicSData["Cooling"].Split('/');
            if (strArray1[0] != "")
            {
              string[] strArray2 = strArray1[0].Split('|');
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange25 = textRange24.InsertAfter("냉각수 온도(캐비티) : ");
              textRange25.Font.Bold = MsoTriState.msoTrue;
              textRange25.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange26 = textRange25.InsertAfter(strArray2[0]);
              textRange26.Font.Bold = MsoTriState.msoTrue;
              textRange26.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange27 = textRange26.InsertAfter("℃ ~ ");
              textRange27.Font.Bold = MsoTriState.msoTrue;
              textRange27.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange28 = textRange27.InsertAfter(strArray2[1]);
              textRange28.Font.Bold = MsoTriState.msoTrue;
              textRange28.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              // ISSUE: reference to a compiler-generated method
              textRange24 = textRange28.InsertAfter("℃");
              textRange24.Font.Bold = MsoTriState.msoTrue;
              textRange24.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            }
            if (strArray1[1] != "")
            {
              string[] strArray3 = strArray1[1].Split('|');
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange29 = textRange24.InsertAfter(Environment.NewLine + "냉각수 온도(코어) : ");
              textRange29.Font.Bold = MsoTriState.msoTrue;
              textRange29.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange30 = textRange29.InsertAfter(strArray3[0]);
              textRange30.Font.Bold = MsoTriState.msoTrue;
              textRange30.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange31 = textRange30.InsertAfter("℃ ~ ");
              textRange31.Font.Bold = MsoTriState.msoTrue;
              textRange31.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange32 = textRange31.InsertAfter(strArray3[1]);
              textRange32.Font.Bold = MsoTriState.msoTrue;
              textRange32.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              // ISSUE: reference to a compiler-generated method
              textRange24 = textRange32.InsertAfter("℃");
              textRange24.Font.Bold = MsoTriState.msoTrue;
              textRange24.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            }
            if (strArray1[2] != "")
            {
              string[] strArray4 = strArray1[2].Split('|');
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange33 = textRange24.InsertAfter(Environment.NewLine + "Mold Surface Temperature : ");
              textRange33.Font.Bold = MsoTriState.msoTrue;
              textRange33.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange34 = textRange33.InsertAfter(strArray4[0]);
              textRange34.Font.Bold = MsoTriState.msoTrue;
              textRange34.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange35 = textRange34.InsertAfter("℃ ~ ");
              textRange35.Font.Bold = MsoTriState.msoTrue;
              textRange35.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange36 = textRange35.InsertAfter(strArray4[1]);
              textRange36.Font.Bold = MsoTriState.msoTrue;
              textRange36.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              // ISSUE: reference to a compiler-generated method
              textRange24 = textRange36.InsertAfter("℃");
              textRange24.Font.Bold = MsoTriState.msoTrue;
              textRange24.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            }
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange37 = textRange24.InsertAfter(Environment.NewLine + "런너의 배치에 따라 온도 편차가 발생하여 각 캐비티 간의 온도 편차가 발생합니다.");
            textRange37.Font.Bold = MsoTriState.msoTrue;
            textRange37.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          }
        }
        // ISSUE: variable of a compiler-generated type
        Table table2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "Table 17")).FirstOrDefault<Shape>().Table;
        if (table2 == null)
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange38 = table2.Rows[1].Cells[2].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange38.Delete();
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cycle Time")))
        {
          string[] strArray = p_dicSData["Cycle Time"].Split('|');
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange39 = textRange38.InsertAfter("Injection Time : ");
          textRange39.Font.Bold = MsoTriState.msoTrue;
          textRange39.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange40 = textRange39.InsertAfter(strArray[0]);
          textRange40.Font.Bold = MsoTriState.msoTrue;
          textRange40.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange41 = textRange40.InsertAfter("s");
          textRange41.Font.Bold = MsoTriState.msoTrue;
          textRange41.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange42 = textRange41.InsertAfter(Environment.NewLine + "Pack/Holding Time: ");
          textRange42.Font.Bold = MsoTriState.msoTrue;
          textRange42.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange43 = textRange42.InsertAfter(strArray[1]);
          textRange43.Font.Bold = MsoTriState.msoTrue;
          textRange43.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange44 = textRange43.InsertAfter("s");
          textRange44.Font.Bold = MsoTriState.msoTrue;
          textRange44.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange45 = textRange44.InsertAfter(Environment.NewLine + "Cooling Time: ");
          textRange45.Font.Bold = MsoTriState.msoTrue;
          textRange45.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange46 = textRange45.InsertAfter(strArray[2]);
          textRange46.Font.Bold = MsoTriState.msoTrue;
          textRange46.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange47 = textRange46.InsertAfter("s");
          textRange47.Font.Bold = MsoTriState.msoTrue;
          textRange47.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          if (strArray[3] != string.Empty)
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange48 = textRange47.InsertAfter(Environment.NewLine + "형개 오픈: ");
            textRange48.Font.Bold = MsoTriState.msoTrue;
            textRange48.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange49 = textRange48.InsertAfter(strArray[3]);
            textRange49.Font.Bold = MsoTriState.msoTrue;
            textRange49.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange50 = textRange49.InsertAfter("s");
            textRange50.Font.Bold = MsoTriState.msoTrue;
            textRange50.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          }
        }
        // ISSUE: variable of a compiler-generated type
        TextRange textRange51 = table2.Rows[3].Cells[1].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange51.Delete();
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Conclusion")))
          return;
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange52 = textRange51.InsertAfter(p_dicSData["Conclusion"]);
        textRange52.Font.Bold = MsoTriState.msoTrue;
        textRange52.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide13):" + ex.Message));
      }
    }

    private void SetSlide14(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S3_Table2")).FirstOrDefault<Shape>().Table;
        if (table == null)
          return;
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Coolant Temperature(Cavity)")))
        {
          string[] strArray = p_dicSData["Circuit Coolant Temperature(Cavity)"].Split('|');
          // ISSUE: variable of a compiler-generated type
          TextRange textRange1 = table.Rows[2].Cells[3].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange1.Delete();
          if (strArray[0] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = textRange1.InsertAfter(strArray[0]);
            textRange2.Font.Bold = MsoTriState.msoTrue;
            textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange3 = textRange2.InsertAfter("℃");
            textRange3.Font.Bold = MsoTriState.msoTrue;
            textRange3.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange4 = textRange1.InsertAfter("-");
            textRange4.Font.Bold = MsoTriState.msoTrue;
            textRange4.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange5 = table.Rows[2].Cells[4].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange5.Delete();
          if (strArray[1] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange6 = textRange5.InsertAfter(strArray[1]);
            textRange6.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange6.Font.Bold = MsoTriState.msoTrue;
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange7 = textRange5.InsertAfter("-");
            textRange7.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange7.Font.Bold = MsoTriState.msoTrue;
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange8 = table.Rows[2].Cells[5].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange8.Delete();
          if (strArray[2] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange9 = textRange8.InsertAfter(strArray[2]);
            textRange9.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange9.Font.Bold = MsoTriState.msoTrue;
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange10 = textRange8.InsertAfter("-");
            textRange10.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange10.Font.Bold = MsoTriState.msoTrue;
          }
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Coolant Temperature(Core)")))
        {
          string[] strArray = p_dicSData["Circuit Coolant Temperature(Core)"].Split('|');
          // ISSUE: variable of a compiler-generated type
          TextRange textRange11 = table.Rows[3].Cells[3].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange11.Delete();
          if (strArray[0] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange12 = textRange11.InsertAfter(strArray[0]);
            textRange12.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange12.Font.Bold = MsoTriState.msoTrue;
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange13 = textRange12.InsertAfter("℃");
            textRange13.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            textRange13.Font.Bold = MsoTriState.msoTrue;
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange14 = textRange11.InsertAfter("-");
            textRange14.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange14.Font.Bold = MsoTriState.msoTrue;
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange15 = table.Rows[3].Cells[4].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange15.Delete();
          if (strArray[1] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange16 = textRange15.InsertAfter(strArray[1]);
            textRange16.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange16.Font.Bold = MsoTriState.msoTrue;
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange17 = textRange15.InsertAfter("-");
            textRange17.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange17.Font.Bold = MsoTriState.msoTrue;
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange18 = table.Rows[3].Cells[5].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange18.Delete();
          if (strArray[2] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange19 = textRange18.InsertAfter(strArray[2]);
            textRange19.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange19.Font.Bold = MsoTriState.msoTrue;
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange20 = textRange18.InsertAfter("-");
            textRange20.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange20.Font.Bold = MsoTriState.msoTrue;
          }
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Flow Rate")))
        {
          string[] strArray = p_dicSData["Circuit Flow Rate"].Split('|');
          // ISSUE: variable of a compiler-generated type
          TextRange textRange21 = table.Rows[4].Cells[3].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange21.Delete();
          if (strArray[0] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange22 = textRange21.InsertAfter(strArray[0]);
            textRange22.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange22.Font.Bold = MsoTriState.msoTrue;
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange23 = textRange22.InsertAfter("lit/min");
            textRange23.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            textRange23.Font.Bold = MsoTriState.msoTrue;
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange24 = textRange21.InsertAfter("-");
            textRange24.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange24.Font.Bold = MsoTriState.msoTrue;
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange25 = table.Rows[4].Cells[4].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange25.Delete();
          if (strArray[1] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange26 = textRange25.InsertAfter(strArray[1]);
            textRange26.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange26.Font.Bold = MsoTriState.msoTrue;
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange27 = textRange25.InsertAfter("-");
            textRange27.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange27.Font.Bold = MsoTriState.msoTrue;
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange28 = table.Rows[4].Cells[5].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange28.Delete();
          if (strArray[2] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange29 = textRange28.InsertAfter(strArray[2]);
            textRange29.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange29.Font.Bold = MsoTriState.msoTrue;
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange30 = textRange28.InsertAfter("-");
            textRange30.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange30.Font.Bold = MsoTriState.msoTrue;
          }
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Pressure")))
        {
          string[] strArray = p_dicSData["Circuit Pressure"].Split('|');
          // ISSUE: variable of a compiler-generated type
          TextRange textRange31 = table.Rows[5].Cells[3].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange31.Delete();
          if (strArray[0] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange32 = textRange31.InsertAfter(strArray[0]);
            textRange32.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange32.Font.Bold = MsoTriState.msoTrue;
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange33 = textRange32.InsertAfter("kPa");
            textRange33.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            textRange33.Font.Bold = MsoTriState.msoTrue;
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange34 = textRange31.InsertAfter("-");
            textRange34.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange34.Font.Bold = MsoTriState.msoTrue;
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange35 = table.Rows[5].Cells[4].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange35.Delete();
          if (strArray[1] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange36 = textRange35.InsertAfter(strArray[1]);
            textRange36.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange36.Font.Bold = MsoTriState.msoTrue;
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange37 = textRange35.InsertAfter("-");
            textRange37.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange37.Font.Bold = MsoTriState.msoTrue;
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange38 = table.Rows[5].Cells[5].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange38.Delete();
          if (strArray[2] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange39 = textRange38.InsertAfter(strArray[2]);
            textRange39.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange39.Font.Bold = MsoTriState.msoTrue;
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange40 = textRange38.InsertAfter("-");
            textRange40.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange40.Font.Bold = MsoTriState.msoTrue;
          }
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Time To Reach Ejection Temperature")))
        {
          string[] strArray = p_dicSData["Time To Reach Ejection Temperature"].Split('|');
          // ISSUE: variable of a compiler-generated type
          TextRange textRange41 = table.Rows[6].Cells[3].Shape.TextFrame.TextRange;
          if (strArray[0] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange42 = textRange41.InsertAfter(strArray[0]);
            textRange42.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange42.Font.Bold = MsoTriState.msoTrue;
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange43 = textRange42.InsertAfter("s");
            textRange43.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            textRange43.Font.Bold = MsoTriState.msoTrue;
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange44 = textRange41.InsertAfter("-");
            textRange44.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange44.Font.Bold = MsoTriState.msoTrue;
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange45 = table.Rows[6].Cells[4].Shape.TextFrame.TextRange;
          if (strArray[1] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange46 = textRange45.InsertAfter(strArray[1]);
            textRange46.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange46.Font.Bold = MsoTriState.msoTrue;
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange47 = textRange45.InsertAfter("-");
            textRange47.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange47.Font.Bold = MsoTriState.msoTrue;
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange48 = table.Rows[6].Cells[5].Shape.TextFrame.TextRange;
          if (strArray[2] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange49 = textRange48.InsertAfter(strArray[2]);
            textRange49.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange49.Font.Bold = MsoTriState.msoTrue;
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange50 = textRange48.InsertAfter("-");
            textRange50.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange50.Font.Bold = MsoTriState.msoTrue;
          }
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Temperature, Mold")))
        {
          string[] strArray = p_dicSData["Temperature, Mold"].Split('|');
          // ISSUE: variable of a compiler-generated type
          TextRange textRange51 = table.Rows[7].Cells[3].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange51.Delete();
          if (strArray[0] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange52 = textRange51.InsertAfter(strArray[0]);
            textRange52.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange52.Font.Bold = MsoTriState.msoTrue;
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange53 = textRange52.InsertAfter("℃");
            textRange53.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            textRange53.Font.Bold = MsoTriState.msoTrue;
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange54 = textRange51.InsertAfter("-");
            textRange54.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange54.Font.Bold = MsoTriState.msoTrue;
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange55 = table.Rows[7].Cells[4].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange55.Delete();
          if (strArray[1] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange56 = textRange55.InsertAfter(strArray[1]);
            textRange56.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange56.Font.Bold = MsoTriState.msoTrue;
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange57 = textRange55.InsertAfter("-");
            textRange57.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange57.Font.Bold = MsoTriState.msoTrue;
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange58 = table.Rows[7].Cells[5].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange58.Delete();
          if (strArray[2] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange59 = textRange58.InsertAfter(strArray[2]);
            textRange59.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange59.Font.Bold = MsoTriState.msoTrue;
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange60 = textRange58.InsertAfter("-");
            textRange60.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange60.Font.Bold = MsoTriState.msoTrue;
          }
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Temperature, Part")))
          return;
        string[] strArray1 = p_dicSData["Temperature, Part"].Split('|');
        // ISSUE: variable of a compiler-generated type
        TextRange textRange61 = table.Rows[8].Cells[3].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange61.Delete();
        if (strArray1[0] != "")
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange62 = textRange61.InsertAfter(strArray1[0]);
          textRange62.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          textRange62.Font.Bold = MsoTriState.msoTrue;
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange63 = textRange62.InsertAfter("℃");
          textRange63.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          textRange63.Font.Bold = MsoTriState.msoTrue;
        }
        else
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange64 = textRange61.InsertAfter("-");
          textRange64.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          textRange64.Font.Bold = MsoTriState.msoTrue;
        }
        // ISSUE: variable of a compiler-generated type
        TextRange textRange65 = table.Rows[8].Cells[4].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange65.Delete();
        if (strArray1[1] != "")
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange66 = textRange65.InsertAfter(strArray1[1]);
          textRange66.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          textRange66.Font.Bold = MsoTriState.msoTrue;
        }
        else
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange67 = textRange65.InsertAfter("-");
          textRange67.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          textRange67.Font.Bold = MsoTriState.msoTrue;
        }
        // ISSUE: variable of a compiler-generated type
        TextRange textRange68 = table.Rows[8].Cells[5].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange68.Delete();
        if (strArray1[2] != "")
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange69 = textRange68.InsertAfter(strArray1[2]);
          textRange69.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          textRange69.Font.Bold = MsoTriState.msoTrue;
        }
        else
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange70 = textRange68.InsertAfter("-");
          textRange70.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          textRange70.Font.Bold = MsoTriState.msoTrue;
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide14):" + ex.Message));
      }
    }

    private void SetSlide15(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string[] arr_strKey = (string[]) null;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Table table1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S15_Table1")).FirstOrDefault<Shape>().Table;
        if (table1 != null)
        {
          arr_strKey = new string[6]
          {
            "Balance",
            "Pressure",
            "Clamp Force",
            "Weld Line",
            "Sink Mark",
            "Air Trap"
          };
          for (int i = 0; i < arr_strKey.Length; i++)
          {
            string[] strArray = new string[3]
            {
              "-",
              "-",
              "-"
            };
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == arr_strKey[i])))
              strArray = p_dicSData[arr_strKey[i]].Split('|');
            if (strArray[1] == "")
              strArray[1] = "-";
            if (strArray[2] == "")
              strArray[2] = "-";
            // ISSUE: variable of a compiler-generated type
            TextRange textRange1 = table1.Rows[i + 2].Cells[3].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange1.Delete();
            string NewText1;
            string NewText2;
            if (i == 0)
            {
              NewText1 = strArray[1];
              NewText2 = "s";
            }
            else if (i == 1)
            {
              NewText1 = strArray[1];
              NewText2 = "MPa";
            }
            else if (i == 2)
            {
              NewText1 = strArray[1];
              NewText2 = "t";
            }
            else
            {
              NewText1 = strArray[0];
              NewText2 = "";
            }
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = textRange1.InsertAfter(NewText1);
            textRange2.Font.Bold = MsoTriState.msoTrue;
            textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            if (NewText2 != "")
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange3 = textRange2.InsertAfter(NewText2);
              textRange3.Font.Bold = MsoTriState.msoTrue;
              textRange3.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange4 = table1.Rows[i + 2].Cells[4].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange4.Delete();
            if (i == 0)
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange5 = textRange4.InsertAfter("충전 시간은 ");
              textRange5.Font.Bold = MsoTriState.msoTrue;
              textRange5.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange6 = textRange5.InsertAfter(strArray[1]);
              textRange6.Font.Bold = MsoTriState.msoTrue;
              textRange6.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange7 = textRange6.InsertAfter("초 이며 ");
              textRange7.Font.Bold = MsoTriState.msoTrue;
              textRange7.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange8 = textRange7.InsertAfter(strArray[0]);
              textRange8.Font.Bold = MsoTriState.msoTrue;
              textRange8.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            else if (i == 1)
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange9 = textRange4.InsertAfter(strArray[1]);
              textRange9.Font.Bold = MsoTriState.msoTrue;
              textRange9.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange10 = textRange9.InsertAfter("MPa 이상의 사출기를 사용해야 한다.");
              textRange10.Font.Bold = MsoTriState.msoTrue;
              textRange10.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            }
            else if (i == 2)
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange11 = textRange4.InsertAfter(strArray[1]);
              textRange11.Font.Bold = MsoTriState.msoTrue;
              textRange11.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange12 = textRange11.InsertAfter("t 이상의 사출기를 사용해야 한다.");
              textRange12.Font.Bold = MsoTriState.msoTrue;
              textRange12.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            }
            else
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange13 = textRange4.InsertAfter(strArray[1]);
              textRange13.Font.Bold = MsoTriState.msoTrue;
              textRange13.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange14 = table1.Rows[i + 2].Cells[5].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange14.Delete();
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange15 = textRange14.InsertAfter(strArray[2]);
            textRange15.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            textRange15.Font.Bold = MsoTriState.msoTrue;
          }
        }
        // ISSUE: variable of a compiler-generated type
        Table table2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S15_Table2")).FirstOrDefault<Shape>().Table;
        if (table2 == null)
          return;
        arr_strKey = new string[4]
        {
          "Deflection All",
          "Deflection X",
          "Deflection Y",
          "Deflection Z"
        };
        for (int i = 0; i < arr_strKey.Length; i++)
        {
          string[] strArray1 = new string[2]{ "", "" };
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == arr_strKey[i])))
            strArray1 = p_dicSData[arr_strKey[i]].Split('/');
          // ISSUE: variable of a compiler-generated type
          TextRange textRange16 = table2.Rows[2].Cells[i + 2].Shape.TextFrame.TextRange;
          if (strArray1[0] != "")
          {
            string[] strArray2 = strArray1[0].Split('|');
            // ISSUE: reference to a compiler-generated method
            textRange16.InsertAfter(strArray2[0] + " ~ " + strArray2[1] + " mm");
            textRange16.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            textRange16.Font.Bold = MsoTriState.msoTrue;
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange17 = table2.Rows[3].Cells[i + 2].Shape.TextFrame.TextRange;
          if (strArray1[1] != "")
          {
            string[] strArray3 = strArray1[1].Split('|');
            // ISSUE: reference to a compiler-generated method
            textRange17.InsertAfter(strArray3[0] + " ~ " + strArray3[1] + " mm");
            textRange17.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            textRange17.Font.Bold = MsoTriState.msoTrue;
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide15):" + ex.Message));
      }
    }

    private void SetSlide17(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 41f, 93f, 213f, 186f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 267f, 93f, 213f, 186f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 492f, 93f, 213f, 186f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image4")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image4"], MsoTriState.msoFalse, MsoTriState.msoTrue, 718f, 93f, 213f, 186f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image5")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image5"], MsoTriState.msoFalse, MsoTriState.msoTrue, 41f, 298f, 213f, 186f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image6")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image6"], MsoTriState.msoFalse, MsoTriState.msoTrue, 267f, 298f, 213f, 186f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image7")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image7"], MsoTriState.msoFalse, MsoTriState.msoTrue, 492f, 298f, 213f, 186f);
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image8")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image8"], MsoTriState.msoFalse, MsoTriState.msoTrue, 718f, 298f, 213f, 186f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide17):" + ex.Message));
      }
    }

    private void SetSlide18(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 263.9055f, 71.43307f, 433.7008f, 422.078735f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide18):" + ex.Message));
      }
    }

    private void SetSlide19(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 50f, 104f, 435f, 355f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Temperature At Flow Front")))
        {
          // ISSUE: variable of a compiler-generated type
          Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S5_Table")).FirstOrDefault<Shape>().Table;
          if (table != null)
          {
            string[] strArray = p_dicSData["Temperature At Flow Front"].Split('|');
            table.Rows[1].Cells[2].Shape.TextFrame.TextRange.Text = strArray[0] + "℃ ~ " + strArray[1] + "℃";
            // ISSUE: reference to a compiler-generated method
            table.Rows[1].Cells[2].Shape.TextFrame.TextRange.Words().Font.Bold = MsoTriState.msoTrue;
          }
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "Vertical Content Placeholder 4")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange1 = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange1.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Material Structure")))
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = textRange1.InsertAfter("온도 편차는 ±" + p_dicSData["Material Structure"] + "℃ 이어야 합니다.");
            textRange2.Font.Bold = MsoTriState.msoTrue;
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Temperature At Flow Front")))
            {
              string[] strArray = p_dicSData["Temperature At Flow Front"].Split('|');
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange3 = textRange2.InsertAfter(Environment.NewLine + Environment.NewLine + "결과의 온도 편차는 ");
              textRange3.Font.Bold = MsoTriState.msoTrue;
              textRange3.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
              double num1 = Math.Round(clsReportUtill.ConvertToDouble(p_dicSData["Material Structure"]), 2);
              double num2 = Math.Round(clsReportUtill.ConvertToDouble(strArray[2]), 2);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange4 = textRange3.InsertAfter(num2.ToString() + "℃");
              textRange4.Font.Bold = MsoTriState.msoTrue;
              double num3 = num2;
              textRange4.Font.Color.RGB = num1 >= num3 ? ColorTranslator.ToOle(Color.Black) : ColorTranslator.ToOle(Color.Red);
              // ISSUE: reference to a compiler-generated method
              textRange2 = textRange4.InsertAfter(" 입니다.");
              textRange2.Font.Bold = MsoTriState.msoTrue;
              textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            }
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange5 = textRange2.InsertAfter(Environment.NewLine + Environment.NewLine + "온도는 유동 말단보다 게이트가 낮으면 게이트가 먼저 고화되어 보압을 제대로 주지 못할 수 있습니다.(게이트가 먼저 고화될 가능성 높음)");
            textRange5.Font.Bold = MsoTriState.msoTrue;
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange6 = textRange1.InsertAfter("온도는 유동 말단보다 게이트가 낮으면 게이트가 먼저 고화되어 보압을 제대로 주지 못할 수 있습니다.(게이트가 먼저 고화될 가능성 높음)");
            textRange6.Font.Bold = MsoTriState.msoTrue;
          }
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Min Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Min Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 539.1496f, 351.7795f, 170.079f, 149.1024f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Max Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Max Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 747.2126f, 351.7795f, 170.079f, 149.1024f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "Rectangle 5")).FirstOrDefault<Shape>();
        if (shape2 != null)
        {
          // ISSUE: reference to a compiler-generated method
          shape2.ZOrder(MsoZOrderCmd.msoBringToFront);
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Min")))
          {
            // ISSUE: variable of a compiler-generated type
            TextRange textRange7 = shape2.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange7.Delete();
            string[] strArray = p_dicSData["Min"].Split('|');
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange8 = textRange7.InsertAfter(strArray[0] + "℃ ~ " + strArray[1] + "℃");
            textRange8.Font.Color.RGB = ColorTranslator.ToOle(Color.White);
            textRange8.Font.Bold = MsoTriState.msoTrue;
          }
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape3 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "Rectangle 7")).FirstOrDefault<Shape>();
        if (shape3 == null)
          return;
        // ISSUE: reference to a compiler-generated method
        shape3.ZOrder(MsoZOrderCmd.msoBringToFront);
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Max")))
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange9 = shape3.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange9.Delete();
        string[] strArray1 = p_dicSData["Max"].Split('|');
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange10 = textRange9.InsertAfter(strArray1[0] + "℃ ~ " + strArray1[1] + "℃");
        textRange10.Font.Color.RGB = ColorTranslator.ToOle(Color.White);
        textRange10.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide19):" + ex.Message));
      }
    }

    private void SetSlide20(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 50f, 104f, 435f, 355f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 442.7717f, 180.5669f, 246.0472f, 166.3937f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 689.1024f, 180.5669f, 246.0472f, 166.3937f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image4")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image4"], MsoTriState.msoFalse, MsoTriState.msoTrue, 442.7717f, 346.9606f, 246.0472f, 166.3937f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image5")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image5"], MsoTriState.msoFalse, MsoTriState.msoTrue, 689.1024f, 346.9606f, 246.0472f, 166.3937f);
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S5_Table")).FirstOrDefault<Shape>().Table;
        if (table == null)
          return;
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Injection Pressure1")))
        {
          string[] strArray = p_dicSData["Injection Pressure1"].Split('|');
          double num = clsReportUtill.ConvertToDouble(strArray[0]) - clsReportUtill.ConvertToDouble(strArray[1]);
          table.Rows[1].Cells[2].Shape.TextFrame.TextRange.Text = "스프루: " + strArray[0] + "MPa" + Environment.NewLine + "게이트: " + strArray[1] + "MPa" + Environment.NewLine + "스프루 - 게이트: " + (object) Math.Round(num, 2) + "MPa";
          // ISSUE: reference to a compiler-generated method
          table.Rows[1].Cells[2].Shape.TextFrame.TextRange.Words().Font.Bold = MsoTriState.msoTrue;
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Injection Pressure2")))
          return;
        table.Rows[2].Cells[2].Shape.TextFrame.TextRange.Text = p_dicSData["Injection Pressure2"] + "MPa";
        // ISSUE: reference to a compiler-generated method
        table.Rows[2].Cells[2].Shape.TextFrame.TextRange.Words().Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide20):" + ex.Message));
      }
    }

    private void SetSlide21(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 50f, 104f, 435f, 355f);
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S5_Table")).FirstOrDefault<Shape>().Table;
        if (table == null)
          return;
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Clamp1")))
        {
          table.Rows[1].Cells[2].Shape.TextFrame.TextRange.Text = p_dicSData["Clamp1"] + "t";
          // ISSUE: reference to a compiler-generated method
          table.Rows[1].Cells[2].Shape.TextFrame.TextRange.Words().Font.Bold = MsoTriState.msoTrue;
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Clamp2")))
          return;
        table.Rows[2].Cells[2].Shape.TextFrame.TextRange.Text = p_dicSData["Clamp2"] + "t";
        // ISSUE: reference to a compiler-generated method
        table.Rows[2].Cells[2].Shape.TextFrame.TextRange.Words().Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide21):" + ex.Message));
      }
    }

    private void SetSlide22(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 65f, 99f, 191f, 162f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 264f, 99f, 191f, 162f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 65f, 282f, 191f, 162f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image4")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image4"], MsoTriState.msoFalse, MsoTriState.msoTrue, 264f, 282f, 191f, 162f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image5")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image5"], MsoTriState.msoFalse, MsoTriState.msoTrue, 505.7008f, 103.4646f, 198.9921f, 162.1417f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image6")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image6"], MsoTriState.msoFalse, MsoTriState.msoTrue, 704.693f, 103.4646f, 198.9921f, 162.1417f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image7")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image7"], MsoTriState.msoFalse, MsoTriState.msoTrue, 505.7008f, 268.4409f, 198.9921f, 162.1417f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image8")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image8"], MsoTriState.msoFalse, MsoTriState.msoTrue, 704.693f, 268.4409f, 198.9921f, 162.1417f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "Rectangle 12")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Time1")))
          {
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter("게이트 고화 시간: " + p_dicSData["Time1"] + "s");
            textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.White);
            textRange.Font.Bold = MsoTriState.msoTrue;
          }
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "Rectangle 9")).FirstOrDefault<Shape>();
        if (shape2 == null)
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape2.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange1.Delete();
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Time2")))
          return;
        double num = clsReportUtill.ConvertToDouble(p_dicSData["Time2"]);
        if (num < 0.0)
        {
          // ISSUE: reference to a compiler-generated method
          textRange1.InsertAfter("보압 시간: " + (object) Math.Abs(num) + "s 부족");
        }
        else
        {
          // ISSUE: reference to a compiler-generated method
          textRange1.InsertAfter("보압 시간: " + (object) Math.Abs(num) + "s 오버");
        }
        textRange1.Font.Color.RGB = ColorTranslator.ToOle(Color.White);
        textRange1.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide22):" + ex.Message));
      }
    }

    private void SetSlide23(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 81f, 136f, 350f, 340f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 503f, 180f, 213f, 184f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 731f, 180f, 213f, 184f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "Rectangle 10")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange1 = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange1.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Shrinkage1")))
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = textRange1.InsertAfter(p_dicSData["Shrinkage1"] + "% 수축");
            textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.White);
            textRange2.Font.Bold = MsoTriState.msoTrue;
          }
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "Rectangle 13")).FirstOrDefault<Shape>();
        if (shape2 == null)
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange3 = shape2.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange3.Delete();
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Shrinkage2")))
          return;
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange4 = textRange3.InsertAfter(p_dicSData["Shrinkage2"] + "% 수축");
        textRange4.Font.Color.RGB = ColorTranslator.ToOle(Color.White);
        textRange4.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide23):" + ex.Message));
      }
    }

    private void SetSlide24(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 81f, 136f, 350f, 340f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 530f, 136f, 350f, 340f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "Rectangle 9")).FirstOrDefault<Shape>();
        if (shape == null)
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange1.Delete();
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Sink Mark")))
          return;
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = textRange1.InsertAfter("Scale 조정 : " + p_dicSData["Sink Mark"] + " mm");
        textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.White);
        textRange2.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide24):" + ex.Message));
      }
    }

    private void SetSlide25(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 50f, 104f, 435f, 355f);
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 512.7874f, 310.1102f, 237.2598f, 188.2205f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide25):" + ex.Message));
      }
    }

    private void SetSlide26(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 50f, 104f, 435f, 355f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide26):" + ex.Message));
      }
    }

    private void SetSlide28(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      StringBuilder stringBuilder = new StringBuilder();
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 50f, 104f, 435f, 355f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "세로 내용 개체 틀 4")).FirstOrDefault<Shape>();
        if (shape == null)
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange1.Delete();
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Coolant Temperature(Cavity)")))
        {
          string[] strArray = p_dicSData["Circuit Coolant Temperature(Cavity)"].Split('|');
          stringBuilder.Append("냉각수(캐비티)의 온도는 " + strArray[0] + "℃ ~ " + strArray[1] + "℃로 편차는 " + strArray[2] + "℃입니다.");
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Coolant Temperature(Core)")))
        {
          if (stringBuilder.Length > 0)
            stringBuilder.Append(Environment.NewLine + Environment.NewLine);
          string[] strArray = p_dicSData["Circuit Coolant Temperature(Core)"].Split('|');
          stringBuilder.Append("냉각수(코어)의 온도는 " + strArray[0] + "℃ ~ " + strArray[1] + "℃로 편차는 " + strArray[2] + "℃입니다.");
        }
        if (stringBuilder.Length > 0)
          stringBuilder.Append(Environment.NewLine + Environment.NewLine);
        stringBuilder.Append("이론상 냉각수 온도는 2~3℃ 이내로 관리 되는 것을 추천합니다.");
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = textRange1.InsertAfter(stringBuilder.ToString());
        textRange2.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide28):" + ex.Message));
      }
    }

    private void SetSlide29(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 57f, 110f, 213f, 184f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 288f, 110f, 213f, 184f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 57f, 309f, 213f, 184f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image4")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image4"], MsoTriState.msoFalse, MsoTriState.msoTrue, 288f, 309f, 213f, 184f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image5")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image5"], MsoTriState.msoFalse, MsoTriState.msoTrue, 544.252f, 121.89f, 368.504f, 318.3307f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "Rectangle 12")).FirstOrDefault<Shape>();
        if (shape == null)
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange1.Delete();
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Temperature, Mold")))
          return;
        string[] strArray = p_dicSData["Temperature, Mold"].Split('|');
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = textRange1.InsertAfter(strArray[0] + "℃ ~ " + strArray[1] + "℃");
        textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.White);
        textRange2.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide29):" + ex.Message));
      }
    }

    private void SetSlide30(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string empty = string.Empty;
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 70f, 85f, 405f, 354f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 498f, 85f, 405f, 354f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Time To Reach Ejection Temperature")))
          empty = p_dicSData["Time To Reach Ejection Temperature"];
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "Rectangle 5")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange1 = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange1.Delete();
          if (empty != string.Empty)
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = textRange1.InsertAfter("취출 가능한 시간 : " + empty + "s");
            textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.White);
            textRange2.Font.Bold = MsoTriState.msoTrue;
          }
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "Rectangle 6")).FirstOrDefault<Shape>();
        if (shape2 == null)
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange3 = shape2.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange3.Delete();
        if (!(empty != string.Empty))
          return;
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange4 = textRange3.InsertAfter("Cycle Time " + empty + "s 이후 고화되어야 할 부위");
        textRange4.Font.Color.RGB = ColorTranslator.ToOle(Color.White);
        textRange4.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide27):" + ex.Message));
      }
    }

    private void SetSlide31(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 53f, 85f, 405f, 354f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 520f, 85f, 405f, 354f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "Rectangle 15")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange1 = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange1.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Flow Rate")))
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = textRange1.InsertAfter("Flow rate : " + p_dicSData["Circuit Flow Rate"] + " lit/min");
            textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.White);
            textRange2.Font.Bold = MsoTriState.msoTrue;
          }
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "Rectangle 14")).FirstOrDefault<Shape>();
        if (shape2 == null)
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange3 = shape2.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange3.Delete();
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Pressure")))
          return;
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange4 = textRange3.InsertAfter("Circuit Pressure : " + p_dicSData["Circuit Pressure"] + " MPa");
        textRange4.Font.Color.RGB = ColorTranslator.ToOle(Color.White);
        textRange4.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide31):" + ex.Message));
      }
    }

    private void SetSlide33(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 209f, 98f, 503f, 368f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide33):" + ex.Message));
      }
    }

    private void SetSlide34(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 186f, 79f, 263f, 198f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 469f, 79f, 263f, 198f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 186f, 290f, 263f, 198f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image4")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image4"], MsoTriState.msoFalse, MsoTriState.msoTrue, 469f, 290f, 263f, 198f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "TextBox 20")).FirstOrDefault<Shape>();
        // ISSUE: reference to a compiler-generated method
        shape1?.ZOrder(MsoZOrderCmd.msoBringToFront);
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "TextBox 3")).FirstOrDefault<Shape>();
        // ISSUE: reference to a compiler-generated method
        shape2?.ZOrder(MsoZOrderCmd.msoBringToFront);
        // ISSUE: variable of a compiler-generated type
        Shape shape3 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "TextBox 22")).FirstOrDefault<Shape>();
        // ISSUE: reference to a compiler-generated method
        shape3?.ZOrder(MsoZOrderCmd.msoBringToFront);
        // ISSUE: variable of a compiler-generated type
        Shape shape4 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "TextBox 21")).FirstOrDefault<Shape>();
        // ISSUE: reference to a compiler-generated method
        shape4?.ZOrder(MsoZOrderCmd.msoBringToFront);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide34):" + ex.Message));
      }
    }

    private void SetSlide35(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 209f, 98f, 503f, 381f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide35):" + ex.Message));
      }
    }

    private void SetSlide36(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 209f, 98f, 503f, 381f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide36):" + ex.Message));
      }
    }

    private void SetSlide37(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 209f, 98f, 503f, 381f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide37):" + ex.Message));
      }
    }

    private void SetSlide38(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 209f, 98f, 503f, 381f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide38):" + ex.Message));
      }
    }

    private void SetSlide39(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 209f, 98f, 503f, 381f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide39):" + ex.Message));
      }
    }

    private void SetSlide40(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 209f, 98f, 503f, 381f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide40):" + ex.Message));
      }
    }
  }
}
