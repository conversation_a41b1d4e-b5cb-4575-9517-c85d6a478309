﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmMidResult
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDMFUserControl;
using HDMoldFlow.Properties;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public class frmMidResult : Form
  {
    private IContainer components = (IContainer) null;
    private Label label2;
    private Label label_Focus;
    private Label label3;
    private NewTextBox newTextBox_Fill;
    private NewTextBox newTextBox_Pack;
    private NewTextBox newTextBox_Cool;
    private NewButton newButton_Apply;

    public frmMidResult()
    {
      this.InitializeComponent();
      this.newButton_Apply.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Apply.Image);
      this.Text = LocaleControl.getInstance().GetString("IDS_MID_RESULT");
      this.newButton_Apply.ButtonText = LocaleControl.getInstance().GetString("IDS_APPLY");
    }

    private void frmMidResult_Load(object sender, EventArgs e)
    {
      this.ActiveControl = (Control) this.label_Focus;
      this.newTextBox_Fill.Value = clsDefine.g_dicMidResult["Filling"];
      this.newTextBox_Pack.Value = clsDefine.g_dicMidResult["Packing"];
      this.newTextBox_Cool.Value = clsDefine.g_dicMidResult["Cooling"];
    }

    private void newButton_Apply_Click(object sender, EventArgs e)
    {
      if (this.newTextBox_Fill.Value == "" || this.newTextBox_Pack.Value == "" || this.newTextBox_Cool.Value == "")
        return;
      clsDefine.g_dicMidResult["Filling"] = this.newTextBox_Fill.Value;
      clsDefine.g_dicMidResult["Packing"] = this.newTextBox_Pack.Value;
      clsDefine.g_dicMidResult["Cooling"] = this.newTextBox_Cool.Value;
      foreach (KeyValuePair<string, string> keyValuePair in clsDefine.g_dicMidResult)
        clsUtill.WriteINI("Phase", keyValuePair.Key, keyValuePair.Value, clsDefine.g_fiMidResultCfg.FullName);
      this.Close();
    }

    private void frmMidResult_KeyDown(object sender, KeyEventArgs e)
    {
      if (e.KeyCode != Keys.Escape)
        return;
      this.Close();
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.label2 = new Label();
      this.label_Focus = new Label();
      this.label3 = new Label();
      this.newButton_Apply = new NewButton();
      this.newTextBox_Cool = new NewTextBox();
      this.newTextBox_Pack = new NewTextBox();
      this.newTextBox_Fill = new NewTextBox();
      this.SuspendLayout();
      this.label2.BackColor = Color.Lavender;
      this.label2.BorderStyle = BorderStyle.FixedSingle;
      this.label2.Location = new Point(5, 27);
      this.label2.Name = "label2";
      this.label2.Size = new Size(110, 23);
      this.label2.TabIndex = 7;
      this.label2.Text = "Packing Phase";
      this.label2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Focus.BackColor = Color.Lavender;
      this.label_Focus.BorderStyle = BorderStyle.FixedSingle;
      this.label_Focus.Location = new Point(5, 5);
      this.label_Focus.Name = "label_Focus";
      this.label_Focus.Size = new Size(110, 23);
      this.label_Focus.TabIndex = 8;
      this.label_Focus.Text = "Filling Phase";
      this.label_Focus.TextAlign = ContentAlignment.MiddleCenter;
      this.label3.BackColor = Color.Lavender;
      this.label3.BorderStyle = BorderStyle.FixedSingle;
      this.label3.Location = new Point(5, 49);
      this.label3.Name = "label3";
      this.label3.Size = new Size(110, 23);
      this.label3.TabIndex = 7;
      this.label3.Text = "Cooling Phase";
      this.label3.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.ButtonBackColor = Color.White;
      this.newButton_Apply.ButtonText = "적용";
      this.newButton_Apply.FlatBorderSize = 1;
      this.newButton_Apply.FlatStyle = FlatStyle.Flat;
      this.newButton_Apply.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Apply.Image = (Image) Resources.OK;
      this.newButton_Apply.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Apply.Location = new Point(5, 76);
      this.newButton_Apply.Name = "newButton_Apply";
      this.newButton_Apply.Size = new Size(169, 23);
      this.newButton_Apply.TabIndex = 17;
      this.newButton_Apply.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Apply.NewClick += new EventHandler(this.newButton_Apply_Click);
      this.newTextBox_Cool.BackColor = Color.White;
      this.newTextBox_Cool.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Cool.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Cool.IsDigit = true;
      this.newTextBox_Cool.Lines = new string[0];
      this.newTextBox_Cool.Location = new Point(114, 49);
      this.newTextBox_Cool.MultiLine = false;
      this.newTextBox_Cool.Name = "newTextBox_Cool";
      this.newTextBox_Cool.ReadOnly = false;
      this.newTextBox_Cool.Size = new Size(60, 23);
      this.newTextBox_Cool.TabIndex = 13;
      this.newTextBox_Cool.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Cool.TextBoxBackColor = Color.White;
      this.newTextBox_Cool.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Cool.Value = "";
      this.newTextBox_Pack.BackColor = Color.White;
      this.newTextBox_Pack.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pack.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pack.IsDigit = true;
      this.newTextBox_Pack.Lines = new string[0];
      this.newTextBox_Pack.Location = new Point(114, 27);
      this.newTextBox_Pack.MultiLine = false;
      this.newTextBox_Pack.Name = "newTextBox_Pack";
      this.newTextBox_Pack.ReadOnly = false;
      this.newTextBox_Pack.Size = new Size(60, 23);
      this.newTextBox_Pack.TabIndex = 13;
      this.newTextBox_Pack.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pack.TextBoxBackColor = Color.White;
      this.newTextBox_Pack.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pack.Value = "";
      this.newTextBox_Fill.BackColor = Color.White;
      this.newTextBox_Fill.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Fill.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Fill.IsDigit = true;
      this.newTextBox_Fill.Lines = new string[0];
      this.newTextBox_Fill.Location = new Point(114, 5);
      this.newTextBox_Fill.MultiLine = false;
      this.newTextBox_Fill.Name = "newTextBox_Fill";
      this.newTextBox_Fill.ReadOnly = false;
      this.newTextBox_Fill.Size = new Size(60, 23);
      this.newTextBox_Fill.TabIndex = 13;
      this.newTextBox_Fill.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Fill.TextBoxBackColor = Color.White;
      this.newTextBox_Fill.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Fill.Value = "";
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(179, 103);
      this.Controls.Add((Control) this.newButton_Apply);
      this.Controls.Add((Control) this.newTextBox_Cool);
      this.Controls.Add((Control) this.newTextBox_Pack);
      this.Controls.Add((Control) this.newTextBox_Fill);
      this.Controls.Add((Control) this.label3);
      this.Controls.Add((Control) this.label2);
      this.Controls.Add((Control) this.label_Focus);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmMidResult);
      this.ShowInTaskbar = false;
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "중간 결과 수";
      this.Load += new EventHandler(this.frmMidResult_Load);
      this.KeyDown += new KeyEventHandler(this.frmMidResult_KeyDown);
      this.ResumeLayout(false);
    }
  }
}
