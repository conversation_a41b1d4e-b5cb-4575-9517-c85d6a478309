﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.ControlHelper
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using System.Reflection;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public static class ControlHelper
  {
    public static void SetDoubleBuffered(this Control contorl, bool setting) => contorl.GetType().GetProperty("DoubleBuffered", BindingFlags.Instance | BindingFlags.NonPublic).SetValue((object) contorl, (object) setting, (object[]) null);
  }
}
