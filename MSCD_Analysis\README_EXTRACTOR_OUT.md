# Extractor de Valores MSCD para Archivos de Moldflow

## Descripción

Este script permite extraer valores asociados a códigos MSCD desde archivos de texto formateados de Moldflow (como archivos .txt o archivos LOG). El script identifica los valores correspondientes a cada código MSCD y los guarda en un archivo CSV para su análisis posterior.

## Características

- Extracción de valores simples con formato "clave = valor"
- Soporte para secciones especiales como perfiles de presión
- Normalización de mensajes para mejorar la coincidencia
- Modo detallado (verbose) para depuración
- Generación de archivos CSV con los resultados

## Requisitos

- Python 3.x
- Archivo `mscd_codes.csv` generado previamente con el script `mscd_extractor.py`

## Uso

```
python mscd_extractor_out.py <archivo_txt> [--verbose]
```

### Parámetros

- `<archivo_txt>`: Ruta al archivo de texto o LOG que contiene los valores a extraer
- `--verbose`: (Opcional) Muestra información detallada durante el proceso de extracción

### Ejemplos

```
python mscd_extractor_out.py c:\Moldflow\Test.txt
```

Para obtener información detallada durante la extracción:

```
python mscd_extractor_out.py c:\Moldflow\Test.txt --verbose
```

## Salida

El script genera un archivo CSV con el siguiente formato:

- `mscd_code`: Código MSCD identificado
- `message`: Plantilla del mensaje asociado al código
- `value`: Valor extraído del archivo
- `original_line`: Línea original donde se encontró el valor
- `is_profile`: Indica si el valor es parte de un perfil (como un perfil de presión)

## Características Especiales

### Extracción de Perfiles

El script puede extraer datos tabulares de secciones especiales como perfiles de presión. Estos datos se guardan como un valor único con saltos de línea para preservar la estructura tabular.

Ejemplo de perfil de presión extraído:

```
0.00 s             80.00
0.50 s             60.00
0.00 s             53.00
8.00 s             53.00
0.00 s             43.00
1.00 s             43.00
```

### Normalización de Mensajes

El script utiliza técnicas de normalización para mejorar la coincidencia entre los mensajes de los códigos MSCD y las líneas del archivo. Esto incluye:

- Eliminación de formateadores como `%11.4G`, `%s`, etc.
- Eliminación de caracteres especiales
- Normalización de espacios
- Extracción de la parte relevante antes del signo igual

## Flujo de Trabajo Recomendado

1. Ejecute `mscd_extractor.py` para generar el archivo CSV con todos los códigos MSCD (si aún no lo ha hecho)
2. Ejecute `mscd_extractor_out.py` con su archivo de texto o LOG para extraer los valores
3. Abra el archivo CSV resultante para analizar los valores extraídos
4. Utilice estos datos para su análisis o para generar reportes personalizados

## Solución de Problemas

Si el script no encuentra coincidencias esperadas, pruebe lo siguiente:

1. Utilice la opción `--verbose` para ver el proceso de coincidencia en detalle
2. Verifique que el archivo `mscd_codes.csv` esté actualizado y contenga los códigos necesarios
3. Examine el formato del archivo de entrada para asegurarse de que sigue el patrón esperado

## Notas

- El script está optimizado para trabajar con archivos de texto generados por Moldflow
- La precisión de la extracción depende de la calidad de los datos en el archivo `mscd_codes.csv`
- Para secciones especiales como perfiles, el script busca patrones específicos como encabezados de columnas y separadores