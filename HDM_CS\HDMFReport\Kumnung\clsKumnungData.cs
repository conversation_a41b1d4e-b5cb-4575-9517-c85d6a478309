﻿// Decompiled with JetBrains decompiler
// Type: HDMFReport.clsKumnungData
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using HDLocale;
using HDLog4Net;
using HDMoldFlowLibrary;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;

namespace HDMFReport
{
  internal class clsKumnungData
  {
    public static void GetReportUser(
      DataRow p_drStudy,
      out Dictionary<string, string> p_dicValue,
      out Dictionary<string, string> p_dicView,
      out Dictionary<string, string> p_dicUse)
    {
      StringBuilder stringBuilder = new StringBuilder();
      Dictionary<string, string> source = (Dictionary<string, string>) null;
      p_dicValue = new Dictionary<string, string>();
      p_dicView = new Dictionary<string, string>();
      p_dicUse = new Dictionary<string, string>();
      try
      {
        if (p_drStudy["Summary"] != DBNull.Value && p_drStudy["Summary"].ToString() != "")
          source = JsonConvert.DeserializeObject<Dictionary<string, string>>(p_drStudy["Summary"].ToString());
        p_dicValue.Add("InjCond", "0");
        Dictionary<string, string> injectionData = clsHDMFLib.GetInjectionData();
        p_dicValue.Add("InjMaxStroke", injectionData["MaxStroke"]);
        p_dicValue.Add("InjMaxRate", injectionData["MaxRate"]);
        p_dicValue.Add("InjScrewDia", injectionData["ScrewDia"]);
        p_dicValue.Add("InjMaxPressure", injectionData["MaxPressure"]);
        p_dicValue.Add("InjMaxClamp", injectionData["MaxClamp"]);
        p_dicValue.Add("InjPreRatio", "0");
        p_dicValue.Add("InjType", "0");
        p_dicValue.Add("InjRange1", "5");
        p_dicValue.Add("InjRange2", "25");
        p_dicValue.Add("InjRange3", "80");
        p_dicValue.Add("InjRange4", "99");
        p_dicValue.Add("InjRangeVP", "7");
        p_dicValue.Add("Item", p_drStudy.Table.TableName);
        p_dicValue.Add("Sequence", p_drStudy["Sequence"].ToString().Replace("|", " + "));
        p_dicValue.Add("Engineer", "");
        p_dicValue.Add("Manager", "");
        p_dicValue.Add("WeldLine", "Discussion|" + LocaleControl.getInstance().GetString("IDS_WELDLINE") + " " + LocaleControl.getInstance().GetString("IDS_DISCUSSION"));
        p_dicValue.Add("AirTrap", "Discussion|" + LocaleControl.getInstance().GetString("IDS_AIRTRAP") + " " + LocaleControl.getInstance().GetString("IDS_DISCUSSION"));
        p_dicValue.Add("Shrinkage/Sink", "Discussion|" + LocaleControl.getInstance().GetString("IDS_SINKMARK") + " " + LocaleControl.getInstance().GetString("IDS_DISCUSSION"));
        p_dicValue.Add("Cooling", "OK|" + LocaleControl.getInstance().GetString("IDS_COOL_TEMP") + " " + LocaleControl.getInstance().GetString("IDS_NO_PROBLEM"));
        p_dicValue.Add("Balance", "OK|" + LocaleControl.getInstance().GetString("IDS_BALANCE") + " " + LocaleControl.getInstance().GetString("IDS_NO_PROBLEM"));
        p_dicValue.Add("Countermeasure", "To improve air trap, you should make a flow leader and increase thickness. + 0.5mm" + Environment.NewLine + "Almost all areas are cooled in 24 seconds" + Environment.NewLine + "Use of Mold inserts to improve deformation area");
        if (source != null && source.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillingTime")))
          p_dicValue.Add("FillingTime", source["FillingTime"]);
        else
          p_dicValue.Add("FillingTime", "0");
        if (source != null && source.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PackingTime")))
          p_dicValue.Add("PackingTime", source["PackingTime"]);
        else
          p_dicValue.Add("PackingTime", "0");
        if (source != null && source.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CoolingTime")))
          p_dicValue.Add("CoolingTime", source["CoolingTime"]);
        else
          p_dicValue.Add("CoolingTime", "0");
        p_dicValue.Add("Schizonepeta", "");
        p_dicValue.Add("Filling1", "10");
        p_dicValue.Add("Filling2", "30");
        p_dicValue.Add("Filling3", "45");
        p_dicValue.Add("Filling4", "55");
        p_dicValue.Add("Filling5", "70");
        p_dicValue.Add("Filling6", "80");
        p_dicValue.Add("Filling7", "95");
        p_dicValue.Add("Filling8", "100");
        p_dicValue.Add("FillingFrame", "150");
        p_dicValue.Add("Frozen1", "30");
        p_dicValue.Add("Frozen2", "60");
        p_dicValue.Add("Frozen3", "80");
        p_dicValue.Add("Frozen4", "95");
        if (source != null && source.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MoldTempMin")))
          p_dicValue.Add("MoldTempMin", source["MoldTempMin"]);
        else
          p_dicValue.Add("MoldTempMin", "0");
        if (source != null && source.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MoldTempMax")))
          p_dicValue.Add("MoldTempMax", source["MoldTempMax"]);
        else
          p_dicValue.Add("MoldTempMax", "0");
        if (source != null && source.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ShrinkageMin")))
          p_dicValue.Add("ShrinkageMin", source["ShrinkageMin"]);
        else
          p_dicValue.Add("ShrinkageMin", "0");
        if (source != null && source.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ShrinkageMax")))
          p_dicValue.Add("ShrinkageMax", source["ShrinkageMax"]);
        else
          p_dicValue.Add("ShrinkageMax", "0");
        p_dicValue.Add("SinkMarkMin", "0.01");
        p_dicValue.Add("SinkMarkMax", "1");
        p_dicValue.Add("DefScale", "5");
        p_dicValue.Add("ShrinkageOffset1", "0");
        p_dicValue.Add("ShrinkageOffset2", "3");
        p_dicValue.Add("DefAll1", "Best fit");
        p_dicValue.Add("DefAll2", "");
        p_dicValue.Add("DefX1", "Best fit");
        p_dicValue.Add("DefX2", "");
        p_dicValue.Add("DefY1", "Best fit");
        p_dicValue.Add("DefY2", "");
        p_dicValue.Add("DefZ1", "Best fit");
        p_dicValue.Add("DefZ2", "");
        string[] strArray1 = new string[26]
        {
          "Thickness|살두께 정보|Info_1",
          "GateInfo1|게이트 정보 1|Info_2",
          "GateInfo2|게이트 정보 2|Info_2",
          "Cool|냉각 라인 정보|Info_3",
          "FillTime6_1|Fill Time 1|FillTime_1",
          "FillTime6_2|Fill Time 2|FillTime_1",
          "FillTime6_3|Fill Time 3|FillTime_1",
          "FillAnimation6_1|Fill Time 4|FillTime_1",
          "FillAnimation6_2|Fill Time 5|FillTime_1",
          "FillAnimation6_3|Fill Time 6|FillTime_1",
          "FillTime7_1|Fill Time 1|FillTime_2",
          "FillTime7_2|Fill Time 2|FillTime_2",
          "FillTime7_3|Fill Time 3|FillTime_2",
          "FillTime7_4|Fill Time 4|FillTime_2",
          "FillTime7_5|Fill Time 5|FillTime_2",
          "AirTrap|Air Traps|AirTrap",
          "FillTime9_1|Fill Time 1|AirTrap",
          "FillTime9_2|Fill Time 2|AirTrap",
          "MeldLine|Meld Line|WeldMeldLine",
          "WeldLine|Weld Line|WeldMeldLine",
          "Pressure|Pressure|PressureClamp",
          "ClampForce|Clamp force|PressureClamp",
          "SinkMark|Sink Mark|SinkMark",
          "DefX|변형 X|DefXY",
          "DefY|변형 Y|DefXY",
          "DefZ|변형 Z|DefZ"
        };
        for (int index = 0; index < strArray1.Length; ++index)
        {
          string[] strArray2 = strArray1[index].Split('|');
          p_dicView.Add(strArray2[0], "-135|-145|30");
        }
        FileInfo fileInfo1 = new FileInfo(clsReportDefine.g_diCfg.FullName + "\\Report.ini");
        FileInfo fileInfo2 = new FileInfo(clsReportDefine.g_diTmpReport.ToString() + "\\" + p_drStudy["Name"].ToString() + "\\Report.ini");
        if (fileInfo2.Exists)
        {
          string str1 = clsReportUtill.ReadINI("Input", "InjCond", fileInfo2.FullName);
          if (str1 != "")
            p_dicValue["InjCond"] = str1;
          string str2 = clsReportUtill.ReadINI("Input", "InjMaxStroke", fileInfo2.FullName);
          if (str2 != "")
            p_dicValue["InjMaxStroke"] = str2;
          string str3 = clsReportUtill.ReadINI("Input", "InjMaxRate", fileInfo2.FullName);
          if (str3 != "")
            p_dicValue["InjMaxRate"] = str3;
          string str4 = clsReportUtill.ReadINI("Input", "InjScrewDia", fileInfo2.FullName);
          if (str4 != "")
            p_dicValue["InjScrewDia"] = str4;
          string str5 = clsReportUtill.ReadINI("Input", "InjMaxPressure", fileInfo2.FullName);
          if (str5 != "")
            p_dicValue["InjMaxPressure"] = str5;
          string str6 = clsReportUtill.ReadINI("Input", "InjMaxClamp", fileInfo2.FullName);
          if (str6 != "")
            p_dicValue["InjMaxClamp"] = str6;
          string str7 = clsReportUtill.ReadINI("Input", "InjPreRatio", fileInfo2.FullName);
          if (str7 != "")
            p_dicValue["InjPreRatio"] = str7;
          string str8 = clsReportUtill.ReadINI("Input", "InjType", fileInfo2.FullName);
          if (str8 != "")
            p_dicValue["InjType"] = str8;
          string str9 = clsReportUtill.ReadINI("Input", "InjRange1", fileInfo2.FullName);
          if (str9 != "")
            p_dicValue["InjRange1"] = str9;
          string str10 = clsReportUtill.ReadINI("Input", "InjRange2", fileInfo2.FullName);
          if (str10 != "")
            p_dicValue["InjRange2"] = str10;
          string str11 = clsReportUtill.ReadINI("Input", "InjRange3", fileInfo2.FullName);
          if (str11 != "")
            p_dicValue["InjRange3"] = str11;
          string str12 = clsReportUtill.ReadINI("Input", "InjRange4", fileInfo2.FullName);
          if (str12 != "")
            p_dicValue["InjRange4"] = str12;
          string str13 = clsReportUtill.ReadINI("Input", "InjRangeVP", fileInfo2.FullName);
          if (str13 != "")
            p_dicValue["InjRangeVP"] = str13;
          string str14 = clsReportUtill.ReadINI("Input", "Item", fileInfo2.FullName);
          if (str14 != "")
            p_dicValue["Item"] = str14;
          string str15 = clsReportUtill.ReadINI("Input", "Sequence", fileInfo2.FullName);
          if (str15 != "")
            p_dicValue["Sequence"] = str15.Replace("|", " + ");
          string str16 = clsReportUtill.ReadINI("Input", "Engineer", fileInfo2.FullName);
          if (str16 != "")
            p_dicValue["Engineer"] = str16;
          string str17 = clsReportUtill.ReadINI("Input", "Manager", fileInfo2.FullName);
          if (str17 != "")
            p_dicValue["Manager"] = str17;
          string str18 = clsReportUtill.ReadINI("Input", "WeldLine", fileInfo2.FullName);
          if (str18 != "")
            p_dicValue["WeldLine"] = str18;
          string str19 = clsReportUtill.ReadINI("Input", "AirTrap", fileInfo2.FullName);
          if (str19 != "")
            p_dicValue["AirTrap"] = str19;
          string str20 = clsReportUtill.ReadINI("Input", "ShrinkageSink", fileInfo2.FullName);
          if (str20 != "")
            p_dicValue["Shrinkage/Sink"] = str20;
          string str21 = clsReportUtill.ReadINI("Input", "Cooling", fileInfo2.FullName);
          if (str21 != "")
            p_dicValue["Cooling"] = str21;
          string str22 = clsReportUtill.ReadINI("Input", "Balance", fileInfo2.FullName);
          if (str22 != "")
            p_dicValue["Balance"] = str22;
          string str23 = clsReportUtill.ReadINI("Input", "Countermeasure", fileInfo2.FullName);
          if (str23 != "")
            p_dicValue["Countermeasure"] = str23;
          string str24 = clsReportUtill.ReadINI("Input", "FillingTime", fileInfo2.FullName);
          if (str24 != "")
            p_dicValue["FillingTime"] = str24;
          string str25 = clsReportUtill.ReadINI("Input", "PackingTime", fileInfo2.FullName);
          if (str25 != "")
            p_dicValue["PackingTime"] = str25;
          string str26 = clsReportUtill.ReadINI("Input", "CoolingTime", fileInfo2.FullName);
          if (str26 != "")
            p_dicValue["CoolingTime"] = str26;
          string str27 = clsReportUtill.ReadINI("Input", "Schizonepeta", fileInfo2.FullName);
          if (str27 != "")
            p_dicValue["Schizonepeta"] = str27;
          string str28 = clsReportUtill.ReadINI("Input", "Filling1", fileInfo2.FullName);
          if (str28 != "")
            p_dicValue["Filling1"] = str28;
          string str29 = clsReportUtill.ReadINI("Input", "Filling2", fileInfo2.FullName);
          if (str29 != "")
            p_dicValue["Filling2"] = str29;
          string str30 = clsReportUtill.ReadINI("Input", "Filling3", fileInfo2.FullName);
          if (str30 != "")
            p_dicValue["Filling3"] = str30;
          string str31 = clsReportUtill.ReadINI("Input", "Filling4", fileInfo2.FullName);
          if (str31 != "")
            p_dicValue["Filling4"] = str31;
          string str32 = clsReportUtill.ReadINI("Input", "Filling5", fileInfo2.FullName);
          if (str32 != "")
            p_dicValue["Filling5"] = str32;
          string str33 = clsReportUtill.ReadINI("Input", "Filling6", fileInfo2.FullName);
          if (str33 != "")
            p_dicValue["Filling6"] = str33;
          string str34 = clsReportUtill.ReadINI("Input", "Filling7", fileInfo2.FullName);
          if (str34 != "")
            p_dicValue["Filling7"] = str34;
          string str35 = clsReportUtill.ReadINI("Input", "Filling8", fileInfo2.FullName);
          if (str35 != "")
            p_dicValue["Filling8"] = str35;
          string str36 = clsReportUtill.ReadINI("Input", "FillingFrame", fileInfo2.FullName);
          if (str36 != "")
            p_dicValue["FillingFrame"] = str36;
          string str37 = clsReportUtill.ReadINI("Input", "Frozen1", fileInfo2.FullName);
          if (str37 != "")
            p_dicValue["Frozen1"] = str37;
          string str38 = clsReportUtill.ReadINI("Input", "Frozen2", fileInfo2.FullName);
          if (str38 != "")
            p_dicValue["Frozen2"] = str38;
          string str39 = clsReportUtill.ReadINI("Input", "Frozen3", fileInfo2.FullName);
          if (str39 != "")
            p_dicValue["Frozen3"] = str39;
          string str40 = clsReportUtill.ReadINI("Input", "Frozen4", fileInfo2.FullName);
          if (str40 != "")
            p_dicValue["Frozen4"] = str40;
          string str41 = clsReportUtill.ReadINI("Input", "MoldTempMin", fileInfo2.FullName);
          if (str41 != "")
            p_dicValue["MoldTempMin"] = str41;
          string str42 = clsReportUtill.ReadINI("Input", "MoldTempMax", fileInfo2.FullName);
          if (str42 != "")
            p_dicValue["MoldTempMax"] = str42;
          string str43 = clsReportUtill.ReadINI("Input", "ShrinkageMin", fileInfo2.FullName);
          if (str43 != "")
            p_dicValue["ShrinkageMin"] = str43;
          string str44 = clsReportUtill.ReadINI("Input", "ShrinkageMax", fileInfo2.FullName);
          if (str44 != "")
            p_dicValue["ShrinkageMax"] = str44;
          string str45 = clsReportUtill.ReadINI("Input", "SinkMarkMin", fileInfo2.FullName);
          if (str45 != "")
            p_dicValue["SinkMarkMin"] = str45;
          string str46 = clsReportUtill.ReadINI("Input", "SinkMarkMax", fileInfo2.FullName);
          if (str46 != "")
            p_dicValue["SinkMarkMax"] = str46;
          string str47 = clsReportUtill.ReadINI("Input", "DefScale", fileInfo2.FullName);
          if (str47 != "")
            p_dicValue["DefScale"] = str47;
          string str48 = clsReportUtill.ReadINI("Input", "ShrinkageOffset1", fileInfo2.FullName);
          if (str48 != "")
            p_dicValue["ShrinkageOffset1"] = str48;
          string str49 = clsReportUtill.ReadINI("Input", "ShrinkageOffset2", fileInfo2.FullName);
          if (str49 != "")
            p_dicValue["ShrinkageOffset2"] = str49;
          string str50 = clsReportUtill.ReadINI("Input", "DefAll1", fileInfo2.FullName);
          if (str50 != "")
            p_dicValue["DefAll1"] = str50;
          string str51 = clsReportUtill.ReadINI("Input", "DefAll2", fileInfo2.FullName);
          if (str51 != "")
            p_dicValue["DefAll2"] = str51;
          string str52 = clsReportUtill.ReadINI("Input", "DefX1", fileInfo2.FullName);
          if (str52 != "")
            p_dicValue["DefX1"] = str52;
          string str53 = clsReportUtill.ReadINI("Input", "DefX2", fileInfo2.FullName);
          if (str53 != "")
            p_dicValue["DefX2"] = str53;
          string str54 = clsReportUtill.ReadINI("Input", "DefY1", fileInfo2.FullName);
          if (str54 != "")
            p_dicValue["DefY1"] = str54;
          string str55 = clsReportUtill.ReadINI("Input", "DefY2", fileInfo2.FullName);
          if (str55 != "")
            p_dicValue["DefY2"] = str55;
          string str56 = clsReportUtill.ReadINI("Input", "DefZ1", fileInfo2.FullName);
          if (str56 != "")
            p_dicValue["DefZ1"] = str56;
          string str57 = clsReportUtill.ReadINI("Input", "DefZ2", fileInfo2.FullName);
          if (str57 != "")
            p_dicValue["DefZ2"] = str57;
          for (int index = 0; index < strArray1.Length; ++index)
          {
            string[] strArray3 = strArray1[index].Split('|');
            string str58 = clsReportUtill.ReadINI("View", strArray3[0], fileInfo2.FullName);
            if (str58 != "")
              p_dicView[strArray3[0]] = str58;
          }
          for (int index = 0; index < strArray1.Length; ++index)
          {
            string[] strArray4 = strArray1[index].Split('|');
            string str59 = clsReportUtill.ReadINI("Use", strArray4[2], fileInfo2.FullName);
            if (str59 != "")
              p_dicUse[strArray4[2]] = str59;
          }
        }
        else
        {
          string str60 = clsReportUtill.ReadINI("Input", "InjCond", fileInfo1.FullName);
          if (str60 != "")
            p_dicValue["InjCond"] = str60;
          string str61 = clsReportUtill.ReadINI("Input", "InjType", fileInfo1.FullName);
          if (str61 != "")
            p_dicValue["InjType"] = str61;
          string str62 = clsReportUtill.ReadINI("Input", "InjRange1", fileInfo1.FullName);
          if (str62 != "")
            p_dicValue["InjRange1"] = str62;
          string str63 = clsReportUtill.ReadINI("Input", "InjRange2", fileInfo1.FullName);
          if (str63 != "")
            p_dicValue["InjRange2"] = str63;
          string str64 = clsReportUtill.ReadINI("Input", "InjRange3", fileInfo1.FullName);
          if (str64 != "")
            p_dicValue["InjRange3"] = str64;
          string str65 = clsReportUtill.ReadINI("Input", "InjRange4", fileInfo1.FullName);
          if (str65 != "")
            p_dicValue["InjRange4"] = str65;
          string str66 = clsReportUtill.ReadINI("Input", "InjRangeVP", fileInfo1.FullName);
          if (str66 != "")
            p_dicValue["InjRangeVP"] = str66;
          string str67 = clsReportUtill.ReadINI("Input", "Engineer", fileInfo1.FullName);
          if (str67 != "")
            p_dicValue["Engineer"] = str67;
          string str68 = clsReportUtill.ReadINI("Input", "Manager", fileInfo1.FullName);
          if (str68 != "")
            p_dicValue["Manager"] = str68;
          string str69 = clsReportUtill.ReadINI("Input", "WeldLine", fileInfo1.FullName);
          if (str69 != "")
            p_dicValue["WeldLine"] = str69;
          string str70 = clsReportUtill.ReadINI("Input", "AirTrap", fileInfo1.FullName);
          if (str70 != "")
            p_dicValue["AirTrap"] = str70;
          string str71 = clsReportUtill.ReadINI("Input", "ShrinkageSink", fileInfo1.FullName);
          if (str71 != "")
            p_dicValue["Shrinkage/Sink"] = str71;
          string str72 = clsReportUtill.ReadINI("Input", "Cooling", fileInfo1.FullName);
          if (str72 != "")
            p_dicValue["Cooling"] = str72;
          string str73 = clsReportUtill.ReadINI("Input", "Balance", fileInfo1.FullName);
          if (str73 != "")
            p_dicValue["Balance"] = str73;
          string str74 = clsReportUtill.ReadINI("Input", "Countermeasure", fileInfo1.FullName);
          if (str74 != "")
            p_dicValue["Countermeasure"] = str74;
          string str75 = clsReportUtill.ReadINI("Input", "Filling1", fileInfo1.FullName);
          if (str75 != "")
            p_dicValue["Filling1"] = str75;
          string str76 = clsReportUtill.ReadINI("Input", "Filling2", fileInfo1.FullName);
          if (str76 != "")
            p_dicValue["Filling2"] = str76;
          string str77 = clsReportUtill.ReadINI("Input", "Filling3", fileInfo1.FullName);
          if (str77 != "")
            p_dicValue["Filling3"] = str77;
          string str78 = clsReportUtill.ReadINI("Input", "Filling4", fileInfo1.FullName);
          if (str78 != "")
            p_dicValue["Filling4"] = str78;
          string str79 = clsReportUtill.ReadINI("Input", "Filling5", fileInfo1.FullName);
          if (str79 != "")
            p_dicValue["Filling5"] = str79;
          string str80 = clsReportUtill.ReadINI("Input", "Filling6", fileInfo1.FullName);
          if (str80 != "")
            p_dicValue["Filling6"] = str80;
          string str81 = clsReportUtill.ReadINI("Input", "Filling7", fileInfo1.FullName);
          if (str81 != "")
            p_dicValue["Filling7"] = str81;
          string str82 = clsReportUtill.ReadINI("Input", "Filling8", fileInfo1.FullName);
          if (str82 != "")
            p_dicValue["Filling8"] = str82;
          string str83 = clsReportUtill.ReadINI("Input", "FillingFrame", fileInfo1.FullName);
          if (str83 != "")
            p_dicValue["FillingFrame"] = str83;
          string str84 = clsReportUtill.ReadINI("Input", "Frozen1", fileInfo1.FullName);
          if (str84 != "")
            p_dicValue["Frozen1"] = str84;
          string str85 = clsReportUtill.ReadINI("Input", "Frozen2", fileInfo1.FullName);
          if (str85 != "")
            p_dicValue["Frozen2"] = str85;
          string str86 = clsReportUtill.ReadINI("Input", "Frozen3", fileInfo1.FullName);
          if (str86 != "")
            p_dicValue["Frozen3"] = str86;
          string str87 = clsReportUtill.ReadINI("Input", "Frozen4", fileInfo1.FullName);
          if (str87 != "")
            p_dicValue["Frozen4"] = str87;
          string str88 = clsReportUtill.ReadINI("Input", "SinkMarkMin", fileInfo1.FullName);
          if (str88 != "")
            p_dicValue["SinkMarkMin"] = str88;
          string str89 = clsReportUtill.ReadINI("Input", "SinkMarkMax", fileInfo1.FullName);
          if (str89 != "")
            p_dicValue["SinkMarkMax"] = str89;
          string str90 = clsReportUtill.ReadINI("Input", "DefScale", fileInfo2.FullName);
          if (str90 != "")
            p_dicValue["DefScale"] = str90;
          string str91 = clsReportUtill.ReadINI("Input", "ShrinkageOffset1", fileInfo1.FullName);
          if (str91 != "")
            p_dicValue["ShrinkageOffset1"] = str91;
          string str92 = clsReportUtill.ReadINI("Input", "ShrinkageOffset2", fileInfo1.FullName);
          if (str92 != "")
            p_dicValue["ShrinkageOffset2"] = str92;
          string str93 = clsReportUtill.ReadINI("Input", "DefAll1", fileInfo2.FullName);
          if (str93 != "")
            p_dicValue["DefAll1"] = str93;
          string str94 = clsReportUtill.ReadINI("Input", "DefX1", fileInfo2.FullName);
          if (str94 != "")
            p_dicValue["DefX1"] = str94;
          string str95 = clsReportUtill.ReadINI("Input", "DefY1", fileInfo2.FullName);
          if (str95 != "")
            p_dicValue["DefY1"] = str95;
          string str96 = clsReportUtill.ReadINI("Input", "DefZ1", fileInfo2.FullName);
          if (str96 != "")
            p_dicValue["DefZ1"] = str96;
          for (int index = 0; index < strArray1.Length; ++index)
          {
            string[] strArray5 = strArray1[index].Split('|');
            string str97 = clsReportUtill.ReadINI("View", strArray5[0], fileInfo1.FullName);
            if (str97 != "")
              p_dicView[strArray5[0]] = str97;
          }
          for (int index = 0; index < strArray1.Length; ++index)
          {
            string[] strArray6 = strArray1[index].Split('|');
            string str98 = clsReportUtill.ReadINI("Use", strArray6[2], fileInfo1.FullName);
            if (str98 != "")
              p_dicUse[strArray6[0]] = str98;
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnungData]GetReportUser):" + ex.Message));
      }
    }

    public static Dictionary<string, string> GetAnalysisData(
      DataRow p_drStudy,
      Dictionary<string, string> p_dicValue,
      Dictionary<string, string> p_dicView)
    {
      bool flag = false;
      string str = "";
      string p_strSNNode = "";
      List<string> stringList = new List<string>();
      Dictionary<string, string> p_dicData = new Dictionary<string, string>();
      DataTable p_dtDet = new DataTable();
      try
      {
        DirectoryInfo directoryInfo = new DirectoryInfo(clsReportDefine.g_diTmpReport.ToString() + "\\" + p_drStudy["Name"]);
        if (!directoryInfo.Exists)
          directoryInfo.Create();
        p_drStudy["Sequence"].ToString().Contains("Cool");
        if (p_drStudy["Sequence"].ToString().Contains("Warp"))
          flag = true;
        FileInfo fileInfo = new FileInfo(clsReportDefine.g_diTmpReport.ToString() + "\\" + p_drStudy["Name"].ToString() + "\\ALog.log");
        if (!fileInfo.Exists)
          stringList = clsHDMFLib.ExportAnalysisLog(fileInfo.FullName);
        else
          stringList.AddRange((IEnumerable<string>) File.ReadAllLines(fileInfo.FullName, Encoding.Default));
        List<DataRow> p_lst_drFillPhase = new List<DataRow>((IEnumerable<DataRow>) clsHDMFLib.GetFillingPhaseData(stringList).Rows.Cast<DataRow>().ToArray<DataRow>());
        List<DataRow> dataRowList = new List<DataRow>((IEnumerable<DataRow>) clsHDMFLib.GetPackingPhaseData(stringList).Rows.Cast<DataRow>().ToArray<DataRow>());
        List<DataRow> p_lst_drAllPhase = new List<DataRow>();
        p_lst_drAllPhase.AddRange((IEnumerable<DataRow>) p_lst_drFillPhase.ToArray());
        p_lst_drAllPhase.AddRange((IEnumerable<DataRow>) dataRowList.ToArray());
        Dictionary<string, string> allNodes = clsHDMFLib.GetAllNodes(ref p_dtDet);
        string p_strGateNode = clsHDMFLib.GetGateNodeFromStudyNote();
        if (p_strGateNode == string.Empty)
        {
          p_strGateNode = string.Join(" ", clsHDMFLib.GetGateNodes(allNodes, p_dtDet));
          clsHDMFLib.WriteGateNodeToStudyNote(p_strGateNode);
        }
        string[] strArray1 = clsHDMFLib.GetCoreAndCavityNodeFromStudyNote();
        if (strArray1.Length == 0)
        {
          string[] strArray2 = p_strGateNode.Split(' ');
          strArray1 = clsHDMFLib.GetCavityCoreNodes(allNodes, stringList, strArray2[0]);
          if (strArray1.Length > 1)
          {
            clsHDMFLib.WriteCoreToStudyNote(strArray1[0]);
            clsHDMFLib.WriteCavityToStudyNote(strArray1[1]);
          }
        }
        if (strArray1.Length == 2)
        {
          str = strArray1[0];
          p_strSNNode = strArray1[1];
        }
        foreach (KeyValuePair<string, string> keyValuePair in p_dicValue)
          p_dicData.Add(keyValuePair.Key + "[User]", keyValuePair.Value);
        p_dicData.Add("StudyName", p_drStudy["Name"].ToString());
        p_dicData.Add("ProductVolume[Log]", clsHDMFLib.GetProductVolumeFromLog(stringList));
        p_dicData.Add("ColdFeedVolume[Log]", clsHDMFLib.GetColdFeedVolumeFromLog(stringList));
        p_dicData.Add("ColdMass[Log]", clsHDMFLib.GetColdMassFromLog(stringList));
        p_dicData.Add("PartMass[Log]", clsHDMFLib.GetPartMassFromLog(stringList));
        p_dicData.Add("FillTime[Log]", clsHDMFLib.GetFillLastTimeFromLog(p_lst_drFillPhase));
        p_dicData.Add("CycleTime[Log]", clsHDMFLib.GetCycleTimeFromLog());
        p_dicData.Add("CoolingTime[Log]", clsHDMFLib.GetCoolingTimeFromLog(p_lst_drAllPhase));
        p_dicData.Add("TotalMass(FillPhase)[Log]", clsHDMFLib.GetFillPhaseTotalMassFromLog(stringList));
        p_dicData.Add("TotalMass(PackPhase)[Log]", clsHDMFLib.GetPackPhaseTotalMassFromLog(stringList));
        p_dicData.Add("Totalweight(FillPhase)[Log]", clsHDMFLib.GetFillPhaseTotalweightFromLog(stringList));
        p_dicData.Add("Totalweight(PackPhase)[Log]", clsHDMFLib.GetPackPhaseTotalweightFromLog(stringList));
        p_dicData.Add("CircuitCoolantTemperature[Log]", clsHDMFLib.GetCircuitCoolantTemperatureFromLog(stringList, str));
        p_dicData.Add("CircuitCoolantTemperatureCavity[Log]", clsHDMFLib.GetCircuitCoolantTemperatureCavityCoreFromLog(stringList, p_strSNNode));
        p_dicData.Add("CircuitCoolantTemperatureCore[Log]", clsHDMFLib.GetCircuitCoolantTemperatureCavityCoreFromLog(stringList, str));
        p_dicData.Add("SpruePressure[Log]", clsHDMFLib.GetSpruePressureFromLog(p_lst_drAllPhase));
        p_dicData.Add("ClampForce[Log]", clsHDMFLib.GetClampForceFromLog2(p_lst_drAllPhase));
        p_dicData.Add("FamilyName[MF]", clsHDMFLib.GetFamilyName());
        p_dicData.Add("Manufacturer[MF]", clsHDMFLib.GetManufacturer());
        p_dicData.Add("TradeName[MF]", clsHDMFLib.GetTradeName());
        p_dicData.Add("FamilyAbbreviation[MF]", clsHDMFLib.GetFamilyAbbreviation());
        p_dicData.Add("MeltTemp[MF]", clsHDMFLib.GetMeltTemperatureFromProcess());
        p_dicData.Add("MoldTemp[MF]", clsHDMFLib.GetMoldTemperatureFromProcess());
        p_dicData.Add("CoolingTime[MF]", clsHDMFLib.GetCoolingTimeFromProcess());
        p_dicData.Add("FillingControlType[MF]", clsHDMFLib.GetFillingControlTypeFromProcess());
        p_dicData.Add("FillingControl[MF]", clsHDMFLib.GetFillingControlDataFromProcess());
        p_dicData.Add("VPSwitchOver[MF]", clsHDMFLib.GetVelocityPressureFromProcess());
        p_dicData.Add("PackHoldingControl[MF]", clsHDMFLib.GetPackHoldingControlFromProcess());
        p_dicData.Add("MeltDensity[MF]", clsHDMFLib.GetMaterialMeltDensity());
        p_dicData.Add("SolidDensity[MF]", clsHDMFLib.GetMaterialSolidDensity());
        p_dicData.Add("Thickness[MF]", clsHDMFLib.GetThicknessDataFromPlotFormat());
        DataTable allLayers = clsHDMFLib.GetAllLayers();
        clsHDMFLib.HideActivePlot();
        clsHDMFLib.ChangeActiveLayer(0, allLayers, false, 0L);
        clsHDMFLib.GetThicknessPlot("", directoryInfo.FullName, p_dicView["Thickness"], 900, 600, new Dictionary<string, string>()
        {
          {
            "Min",
            "3.5"
          },
          {
            "Max",
            "4"
          }
        }, ref p_dicData);
        clsHDMFLib.GetTemperatureAtFlowFront("TemperatureAtFlowFront", "Temperature at flow front", directoryInfo.FullName, "", 900, 600, stringList, ref p_dicData);
        p_dicData.Add("TemperatureAtFlowFront_Inj[IMG]", clsHDMFLib.GetPlotImage("TemperatureAtFlowFront_Inj", "Temperature at flow front", "", "", directoryInfo.FullName, "", 900, 600));
        clsHDMFLib.GetFillTimePlot2("FillTime6_1", directoryInfo.FullName, p_dicValue["Filling2"], p_dicView["FillTime6_1"], ref p_dicData);
        clsHDMFLib.GetFillTimePlot2("FillTime6_2", directoryInfo.FullName, p_dicValue["Filling4"], p_dicView["FillTime6_2"], ref p_dicData);
        clsHDMFLib.GetFillTimePlot2("FillTime6_3", directoryInfo.FullName, p_dicValue["Filling7"], p_dicView["FillTime6_3"], ref p_dicData);
        clsHDMFLib.GetFillAnimationPlot("FillAnimation6_1", directoryInfo.FullName, p_dicValue["FillingFrame"], p_dicView["FillAnimation6_1"], ".gif", ref p_dicData);
        clsHDMFLib.GetFillAnimationPlot("FillAnimation6_2", directoryInfo.FullName, p_dicValue["FillingFrame"], p_dicView["FillAnimation6_2"], ".gif", ref p_dicData);
        clsHDMFLib.GetFillAnimationPlot("FillAnimation6_3", directoryInfo.FullName, p_dicValue["FillingFrame"], p_dicView["FillAnimation6_3"], ".gif", ref p_dicData);
        clsHDMFLib.GetFillTimePlot2("FillTime7_1", directoryInfo.FullName, p_dicValue["Filling2"], p_dicView["FillTime7_1"], ref p_dicData);
        clsHDMFLib.GetFillTimePlot2("FillTime7_2", directoryInfo.FullName, p_dicValue["Filling4"], p_dicView["FillTime7_2"], ref p_dicData);
        clsHDMFLib.GetFillTimePlot2("FillTime7_3", directoryInfo.FullName, p_dicValue["Filling7"], p_dicView["FillTime7_3"], ref p_dicData);
        clsHDMFLib.GetFillTimePlot2("FillTime7_4", directoryInfo.FullName, p_dicValue["Filling4"], p_dicView["FillTime7_4"], ref p_dicData);
        clsHDMFLib.GetFillTimePlot2("FillTime7_5", directoryInfo.FullName, p_dicValue["Filling7"], p_dicView["FillTime7_5"], ref p_dicData);
        clsHDMFLib.GetFillTimePlot2("FillTime9_1", directoryInfo.FullName, p_dicValue["Filling2"], p_dicView["FillTime9_1"], ref p_dicData);
        clsHDMFLib.GetFillTimePlot2("FillTime9_2", directoryInfo.FullName, p_dicValue["Filling7"], p_dicView["FillTime9_2"], ref p_dicData);
        p_dicData.Add("PressureAtInjectionLocation[IMG]", clsHDMFLib.GetPlotImageBySettingRange("PressureAtInjectionLocation", "Pressure at injection location:XY Plot", "", "", directoryInfo.FullName, p_dicView["Pressure"], 900, 600, "PackPhase", 1.0, p_lst_drAllPhase));
        p_dicData.Add("PressureAtInjectionLocation_Inj[IMG]", clsHDMFLib.GetPlotImage("PressureAtInjectionLocation", "Pressure at injection location:XY Plot", "", "", directoryInfo.FullName, p_dicView["Pressure"], 900, 600));
        p_dicData.Add("ClampForceXY[IMG]", clsHDMFLib.GetPlotImageBySettingRange("ClampFoceXY", "Clamp force:XY Plot", "", "", directoryInfo.FullName, p_dicView["ClampForce"], 900, 600, "PackPhase", 1.0, p_lst_drAllPhase));
        p_dicData.Add("WeldLine[IMG]", clsHDMFLib.GetPlotImage("WeldLine", "Weld lines", "1722", directoryInfo.FullName, p_dicView["WeldLine"], 900, 600, new clsPlotOption()
        {
          dblOpacity = 1.0,
          enumColorType = PlotColorType.SingleColor,
          enumColor = PlotColor.Pink
        }));
        p_dicData.Add("MeldLine[IMG]", clsHDMFLib.GetPlotImage("MeldLine", "Weld and meld lines", "1732", directoryInfo.FullName, p_dicView["MeldLine"], 900, 600, new clsPlotOption()
        {
          dblOpacity = 1.0,
          enumColorType = PlotColorType.SingleColor,
          enumColor = PlotColor.Green
        }));
        KeyValuePair<string, string> keyValuePair1 = p_dicData.Where<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SinkMarkMin[User]")).FirstOrDefault<KeyValuePair<string, string>>();
        double p_dblSinkMarkMin = Math.Round(clsReportUtill.ConvertToDouble(keyValuePair1.Value), 2);
        keyValuePair1 = p_dicData.Where<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SinkMarkMax[User]")).FirstOrDefault<KeyValuePair<string, string>>();
        double p_dblSinkMarkMax = Math.Round(clsReportUtill.ConvertToDouble(keyValuePair1.Value), 2);
        clsHDMFLib.GetSinkMarkPlot(directoryInfo.FullName, p_dicView["SinkMark"], p_dblSinkMarkMin, p_dblSinkMarkMax, ref p_dicData);
        if (flag)
        {
          keyValuePair1 = p_dicData.Where<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefScale[User]")).FirstOrDefault<KeyValuePair<string, string>>();
          double p_dblScale = Math.Round(clsReportUtill.ConvertToDouble(keyValuePair1.Value), 2);
          clsHDMFLib.GetDeflectionPlot(directoryInfo.FullName, p_dicValue, p_dicView, p_dblScale, ref p_dicData);
        }
        p_dicData.Add("RamSpeedRecommended[IMG]", clsHDMFLib.GetPlotImage("RampSpeedRecommended", "Ram speed, recommended:XY Plot", "", "", directoryInfo.FullName, "", 900, 600));
        p_dicData.Add("PressureAtVPSwitchover[IMG]", clsHDMFLib.GetPlotImage("PressureAtVPSwitchover", "Pressure at V/P switchover", "", "", directoryInfo.FullName, "", 900, 600));
        clsHDMFLib.HideActivePlot();
        clsHDMFLib.ChangeActiveLayer(2, allLayers, false, 0L);
        clsHDMFLib.GetModelImage("Gate1", directoryInfo.FullName, p_dicView["GateInfo1"], true, true, 900, 600, ref p_dicData);
        clsHDMFLib.GetModelImage("Gate2", directoryInfo.FullName, p_dicView["GateInfo2"], true, true, 900, 600, ref p_dicData);
        clsHDMFLib.HideActivePlot();
        clsHDMFLib.ChangeActiveLayer(3, allLayers, false, 0L);
        p_dicData.Add("CircuitCoolantTemperature[IMG]", clsHDMFLib.GetPlotImage("CircuitCoolantTemperature", "Circuit coolant temperature", "", directoryInfo.FullName, p_dicView["Cool"], 900, 600, new clsPlotOption()
        {
          dblOpacity = 0.1,
          enumScaling = Scaling.Specified
        }));
        clsHDMFLib.GetAirTrapPlot(directoryInfo.FullName, p_dicView["AirTrap"], 900, 600, allLayers, ref p_dicData);
        if (p_dicValue.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "InjCond")))
        {
          if (p_dicValue["InjCond"] == "1")
            clsInjCondTable.ExportInjCondTable(p_drStudy, p_lst_drAllPhase, p_dicValue, p_dicData);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnungData]GetAnalysisData):" + ex.Message));
      }
      return p_dicData;
    }
  }
}
