﻿// Decompiled with JetBrains decompiler
// Type: HDMFAI.clsAIDefine
// Assembly: HDMFAI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: A66A47CE-D63B-4ACB-B9A4-A4F118252C4E
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFAI.dll

using System.IO;

namespace HDMFAI
{
  public class clsAIDefine
  {
    internal const string g_strAIURL = "http://api.hdsol.co.kr";
    internal const string g_strBigData = "/api/bigdata";
    internal const string g_strInput = "/api/input";
    internal const string g_strAIResult = "/api/output";
    internal static string g_strSaveFileName = string.Empty;
    internal static readonly string[] arr_strInput = new string[19]
    {
      "Study_Group",
      "Mesh_Type",
      "Filling_control_Injection_time",
      "Analysis_Type",
      "Melt_temperature",
      "Mold_temperature",
      "Velocity_pressure_switch_over",
      "cooling_time",
      "Total_Volume",
      "Part_Volume",
      "X_Length",
      "Y_Length",
      "Z_Length",
      "Thickness",
      "Thickness_Percentage",
      "Material_Company",
      "Trade_Name",
      "Material_Type",
      "Material_Density"
    };
    internal static readonly string[] arr_strIntermediate = new string[8]
    {
      "Filled_time",
      "Short_shot",
      "VP_time",
      "VP_flow_rate",
      "VP_Pressure",
      "Frozen_95_Time",
      "Frozen_99_Time",
      "Frozen_Gate_Time"
    };
    internal static readonly string[] arr_strOutput = new string[30]
    {
      "Total_Project_Area",
      "Minimum_Circuit_coolant_temperature_Core",
      "Maximum_Circuit_coolant_temperature_Core",
      "Diff_Circuit_coolant_temperature_Core",
      "Minimum_Circuit_coolant_temperature_Cavity",
      "Maximum_Circuit_coolant_temperature_Cavity",
      "Diff_Circuit_coolant_temperature_Cavity",
      "Minimum_Part_temperature",
      "Maximum_Part_temperature",
      "Diff_Part_temperature",
      "Minimum_Mold_temperature",
      "Maximum_Mold_temperature",
      "Diff_Mold_temperature",
      "Maximum_Time_to_reach_ejection_temperature_Cool",
      "Cavity_surface_temperature_average",
      "Cycle_time",
      "Injection_pressure",
      "Clamp_force",
      "Maximum_Sink_mark_depth",
      "Minimum_Temperature_at_flow_front",
      "Maximum_Temperature_at_flow_front",
      "Diff_Temperature_at_flow_front",
      "Minimum_Volumetric_shrinkage_at_ejection",
      "Maximum_Volumetric_shrinkage_at_ejection",
      "Diff_Volumetric_shrinkage_at_ejection",
      "Total_mass",
      "Range_Deflection",
      "Range_X_component",
      "Range_Y_component",
      "Range_Z_component"
    };

    public static DirectoryInfo g_diTemplate { get; set; }

    public static DirectoryInfo g_diTmpAI { get; set; }

    public static FileInfo g_fiAICfg { get; set; }
  }
}
