'%RunPerInstance
'@ DESCRIPTION Set shrinkage and scale range for Deflection results
'@ Author: <PERSON>("en-us")
Dim SynergyGetter, Synergy
On Error Resume Next
Set SynergyGetter = GetObject(CreateObject("WScript.Shell").ExpandEnvironmentStrings("%SAInstance%"))
On Error GoTo 0
If (Not IsEmpty(SynergyGetter)) Then
	Set Synergy = SynergyGetter.GetSASynergy
Else
	Set Synergy = CreateObject("synergy.Synergy")
End If
Synergy.SetUnits "Metric"

' SET scale on "shrkVector"

MsgBox  "Don't Forget! Fix Anchor!",,WScript.ScriptName

usrInput = InputBox("Shrinkage factor to use: ", "Shrinkage")
shrkg = CDbl(usrInput)
Set shrkVector = Synergy.CreateVector()
shrkVector.SetXYZ shrkg, shrkg, shrkg

' SET range on "myrange"
'usrInput = InputBox("Range for the scale (just the number, it will be converted and used both for Min and Max): ", "Results range")
'scaleInput = CDbl(usrInput)

'scaleRange(0)= scaleInput * -1
'scaleRange(1)= scaleInput

		'Set Plot = PlotMgr.FindPlotByName("Deflection, all effects:Deflection")

Dim scaleRange(1)
Dim Value(1)
Dim plotList(3)

plotList(0)="Deflection, all effects:Deflection"
plotList(1)="Deflection, all effects:X Component"
plotList(2)="Deflection, all effects:Y Component"
plotList(3)="Deflection, all effects:Z Component"

Set PlotManager = Synergy.PlotManager()
Set Viewer = Synergy.Viewer()

Dim lStr,I



For Each currplot in plotList
	Set Plot = PlotManager.FindPlotByName(currplot)
	
	Viewer.ShowPlot Plot
	

	Plot.SetPlotMethod 1
	Plot.SetDeflectionScaleDirection 3
	Plot.SetShrinkageCompensationOption "Isotropic"
	Plot.SetShrinkageCompensationEstimatedShrinkage shrkVector

	Plot.SetScaleOption 0
	Plot.SetExtendedColor True
	Plot.Regenerate
	
	minV = Plot.GetMinValue
	maxV = Plot.GetMaxValue
	minV = (Abs(minV))
	maxV = (Abs(maxV))
	Value(0) = Round(minV,2)
	Value(1) = Round (maxV,2)

		'lStr = ""
		'For I = 0 To 1
			'lStr = lStr & Value(I) & vbCrLf
		'Next
		'MsgBox  lStr,,WScript.ScriptName
	
	If Value(1) > Value(0) then
		scaleRange(0)= Value(1) * -1
		scaleRange(1)= Value(1)
		Plot.SetScaleOption 2
		Plot.SetMaxValue scaleRange(1)
		Plot.SetMinValue scaleRange(0)
	else
		scaleRange(0)= Value(0) * -1
		scaleRange(1)= Value(0)
		Plot.SetScaleOption 2
		Plot.SetMaxValue scaleRange(1)
		Plot.SetMinValue scaleRange(0)
		
	end if
	
	'Viewer.ShowPlot Plot
	'Plot.SetPlotMethod 1
	'Plot.SetDeflectionScaleDirection 3
	'Plot.SetShrinkageCompensationOption "Isotropic"
	'Plot.SetShrinkageCompensationEstimatedShrinkage shrkVector
	Plot.Regenerate
Next

'funcion de redondeo hacia arriba

Private Function Redondeo(ByVal Numero, ByVal Decimales) 

Redondeo = Int(Numero * 10 ^ Decimales + 1 / 2) / 10 ^ Decimales End Function