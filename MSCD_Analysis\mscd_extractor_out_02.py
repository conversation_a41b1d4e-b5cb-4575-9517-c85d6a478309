import re
import csv
import os
import sys
import subprocess
import logging

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('mscd_extractor.log')
    ]
)

def is_text_file(filepath, blocksize=512):
    """Checks if a file is likely a text file."""
    try:
        # Try reading with utf-8, common for text files
        with open(filepath, 'r', encoding='utf-8') as f:
            f.read(blocksize) # Try to read a block
        return True
    except UnicodeDecodeError:
        # If utf-8 fails, try latin-1, another common encoding
        try:
            with open(filepath, 'r', encoding='latin-1') as f:
                f.read(blocksize)
            return True
        except UnicodeDecodeError:
            # If both fail, it's likely not a text file or uses an unsupported encoding
            logging.warning(f"is_text_file: File {filepath} could not be decoded as UTF-8 or Latin-1.")
            return False
    except FileNotFoundError:
        logging.error(f"is_text_file: File not found at {filepath}")
        return False # File doesn't exist
    except Exception as e:
        # Other potential errors (e.g., permissions)
        logging.warning(f"is_text_file: Error checking file {filepath}: {e}")
        return False # Uncertain, assume not text for safety

def load_mscd_codes(csv_file):
    """Carga códigos MSCD desde el archivo CSV generado."""
    mscd_dict = {}
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            mscd_dict[row['mscd_code']] = row
    return mscd_dict

def extract_table_data(lines, start_idx, verbose=False):
    """
    Extrae datos tabulares genéricos.
    Busca líneas de encabezado, una línea separadora (---), y luego filas de datos.
    Devuelve el texto completo de la tabla (encabezados + separador + datos) y el nuevo índice.
    """
    collected_lines = [] # Para almacenar encabezados, separador y datos
    
    i = start_idx
    
    # 1. Leer líneas de encabezado hasta encontrar un separador '----'
    header_part_done = False
    initial_i = i
    while i < len(lines):
        line_strip = lines[i].strip()
        # Si la línea está vacía y no hemos recogido nada útil aún, podría ser el final prematuro.
        if not line_strip and not collected_lines:
            if verbose: print(f"extract_table_data: Empty line at {i+1} before any table content found after start_idx {start_idx+1}.")
            return "", initial_i # No es una tabla válida si hay vacío inmediatamente
        
        # Si la línea está vacía DESPUÉS de haber recogido algo, podría ser el fin de los encabezados antes del separador.
        if not line_strip and collected_lines:
             if verbose: print(f"extract_table_data: Empty line at {i+1} after collecting potential headers, before separator.")
             return "", initial_i # No es una tabla válida si hay vacío antes del separador

        collected_lines.append(lines[i]) # Usar la línea original con su indentación
        if '----' in line_strip: # Separador encontrado
            header_part_done = True
            i += 1
            break
        i += 1
        
    if not header_part_done:
        if verbose: print(f"extract_table_data: Separator '----' not found after line {start_idx+1}.")
        return "", initial_i # No se encontró separador

    # 2. Recopilar filas de datos después del separador
    data_start_idx = i
    while i < len(lines):
        line_strip = lines[i].strip()
        if not line_strip: # Línea vacía, fin de datos
            break
        
        # Heurística: si la línea parece un nuevo título de sección (ej. termina con ':')
        # o es un separador de fin de tabla como '---' (no '----').
        # O si la línea no está indentada como las líneas de datos anteriores (si aplica).
        if line_strip.endswith(':') or (line_strip.startswith('---') and '----' not in line_strip):
            if verbose: print(f"extract_table_data: Possible end of table detected at line {i+1}: '{line_strip}'")
            break
        
        collected_lines.append(lines[i]) # Usar la línea original
        i += 1

    # Si solo tenemos encabezados y separador pero no datos, aún podría ser válido (tabla vacía)
    # O podría ser un error de detección. Por ahora, lo permitimos.
    if i == data_start_idx and header_part_done:
        if verbose: print(f"extract_table_data: Headers and separator found, but no data lines followed from line {data_start_idx+1}.")

    if not collected_lines:
        return "", initial_i

    full_table_text = "\n".join(l.rstrip() for l in collected_lines)
    
    return full_table_text, i

def normalize_message(message):
    """Normaliza un mensaje para comparación, eliminando caracteres especiales y formateadores."""
    if not message:
        return ""
    
    # Eliminar formateadores como %11.4G, %s, etc.
    normalized = re.sub(r'%[\d\.]*[a-zA-Z]', '', message)
    
    # Eliminar caracteres especiales y normalizar espacios
    normalized = re.sub(r'[\[\]\(\)\{\}\*\+\?\^\$\\\.\|]', ' ', normalized)
    normalized = re.sub(r'\s+', ' ', normalized).strip()
    
    # Si hay un signo igual, tomar solo la parte izquierda
    if '=' in normalized:
        normalized = normalized.split('=')[0].strip()
    
    return normalized

def extract_mscd_values(file_path, mscd_dict, output_file, verbose=False):
    """Extrae valores de los códigos MSCD encontrados en el archivo de texto, respetando el formato."""
    results = []
    
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        lines = [line.rstrip() for line in f.readlines()] # rstrip para quitar espacios finales, mantener internos
    
    if verbose:
        print(f"Leyendo {len(lines)} líneas del archivo.")
    
    i = 0
    while i < len(lines):
        line_full = lines[i] # Línea original con su indentación
        line_strip = line_full.strip() # Línea sin espacios al inicio/final para análisis de contenido
        
        if not line_strip: # Saltar líneas completamente vacías
            i += 1
            continue
            
        matched_as_table = False
        # Primero, intentar coincidir con mensajes MSCD que podrían ser títulos de tabla
        for mscd_code, mscd_info in mscd_dict.items():
            message_template = mscd_info.get('message', '')
            if not message_template:
                continue

            # Heurística para identificar mensajes de tabla: termina con ':' o contiene palabras clave
            is_potential_table_header = message_template.strip().endswith(':') or \
                                        'profile' in message_template.lower() or \
                                        'data:' in message_template.lower() or \
                                        'tabulated' in message_template.lower()

            if is_potential_table_header:
                # Comparar la parte antes del primer ':' (si existe), normalizada
                line_content_for_match = normalize_message(line_strip.split(':')[0] if ':' in line_strip else line_strip)
                mscd_content_for_match = normalize_message(message_template.split(':')[0] if ':' in message_template else message_template)
                
                if mscd_content_for_match and mscd_content_for_match in line_content_for_match:
                    if verbose:
                        print(f"Línea {i+1} '{line_strip}' coincide potencialmente con encabezado de tabla MSCD {mscd_code} ('{message_template}')")

                    table_text, new_idx = extract_table_data(lines, i + 1, verbose=verbose) # Empezar desde la siguiente línea
                    
                    if table_text:
                        results.append({
                            'mscd_code': mscd_code,
                            'message': message_template,
                            'value': table_text,
                            'original_line': line_strip, # Línea que activó la detección
                            'is_table_or_profile': True
                        })
                        if verbose:
                            print(f"  - Tabla/Sección extraída para MSCD {mscd_code}. Avanzando de {i+1} a {new_idx}")
                        i = new_idx # new_idx es el índice DESPUÉS de la tabla
                        matched_as_table = True
                        break # Romper bucle mscd_dict, tabla encontrada
            
        if matched_as_table:
            # i ya fue actualizado por extract_table_data, el bucle while continuará desde allí
            continue 

        # Si no se emparejó como tabla, buscar líneas con formato "clave = valor"
        if '=' in line_strip and not line_strip.startswith('---'):
            key_part = line_strip.split('=')[0].strip()
            value_part = line_strip.split('=')[1].strip() if len(line_strip.split('=')) > 1 else ''
            
            if verbose:
                print(f"Analizando línea {i+1} (clave-valor): {key_part} = {value_part}")
            
            for mscd_code, mscd_info in mscd_dict.items():
                message_template = mscd_info.get('message', '')
                if not message_template:
                    continue

                normalized_message_template = normalize_message(message_template)
                normalized_key_part = normalize_message(key_part)
                
                if normalized_message_template and normalized_message_template in normalized_key_part:
                    results.append({
                        'mscd_code': mscd_code,
                        'message': message_template,
                        'value': value_part,
                        'original_line': line_strip,
                        'is_table_or_profile': False
                    })
                    if verbose:
                        print(f"  - Coincidencia clave-valor encontrada con MSCD: {mscd_code}")
                    break # Romper bucle mscd_dict para esta línea clave-valor
        i += 1 # Incrementar para la siguiente línea si no se actualizó por tabla
    
    # Guardar resultados en CSV
    fieldnames = ['mscd_code', 'message', 'value', 'original_line', 'is_table_or_profile']
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        for result in results:
            writer.writerow(result)
    
    return len(results)

def main():
    if len(sys.argv) < 2:
        print("Uso: python mscd_extractor_out_02.py <archivo_sdy> [--verbose]")
        print("Ejemplo: python mscd_extractor_out_02.py c:\\Moldflow\\Test.sdy")
        return

    input_file = sys.argv[1]
    verbose = "--verbose" in sys.argv

    if not os.path.exists(input_file):
        print(f"Error: El archivo {input_file} no existe.")
        return

    # Verificar si es un archivo .sdy
    base_dir = os.path.dirname(os.path.abspath(__file__))
    mscd_csv = os.path.join(base_dir, 'mscd_codes.csv')

    file_ext = os.path.splitext(input_file)[1].lower()
    if file_ext != '.sdy':
        print(f"Error: El archivo debe tener extensión .sdy (recibido: {file_ext})")
        return

    # Generar el archivo .txt usando studyrlt.exe
    txt_file = os.path.splitext(input_file)[0] + '.txt'
    moldflow_exe = r'C:\Program Files\Autodesk\Moldflow Synergy 2025\bin\studyrlt.exe'
    
    if not os.path.exists(moldflow_exe):
        logging.error(f"No se encontró el ejecutable studyrlt.exe en {moldflow_exe}")
        print(f"Error: No se encontró el ejecutable studyrlt.exe en {moldflow_exe}")
        print("Por favor, verifique la instalación de Autodesk Moldflow Synergy 2025.")
        return

    # Ejecutar studyrlt.exe con los parámetros para archivo .sdy
    command_args = [
        moldflow_exe,
        f'"{input_file}"',  # Archivo .sdy entre comillas
        "-exportoutput",
        "-unit",
        "Metric"
    ]
    
    logging.info(f"Convirtiendo {input_file} a texto usando studyrlt.exe...")
    
    # Construir la cadena de comando asegurando que el ejecutable y el archivo de entrada estén entrecomillados
    command_string = f'"{moldflow_exe}" "{input_file}" -exportoutput -unit Metric'
    logging.info(f"Ejecutando comando: {command_string}")

    try:
        result = subprocess.run(
            command_string,  # Usar la cadena de comando construida
            capture_output=True,
            text=True,
            shell=True  # Necesario para manejar las comillas en el path y la estructura del comando
        )

        if result.stdout:
            logging.info(f"studyrlt.exe stdout:\n{result.stdout}")
        if result.stderr:
            logging.warning(f"studyrlt.exe stderr:\n{result.stderr}")

        if result.returncode != 0:
            logging.error(f"studyrlt.exe falló con código de salida {result.returncode}")
            print(f"Error: La conversión del archivo .sdy falló.")
            return

        # Verificar que el archivo .txt se haya creado
        if not os.path.exists(txt_file) or os.path.getsize(txt_file) == 0:
            logging.error(f"No se encontró el archivo de salida {txt_file} o está vacío")
            print(f"Error: No se pudo generar el archivo de texto desde el .sdy")
            return

    except Exception as e:
        logging.error(f"Error al ejecutar studyrlt.exe: {e}")
        print(f"Error durante la conversión: {e}")
        return

    # Proceder con el análisis del archivo de texto
    print(f"Cargando códigos MSCD desde {mscd_csv}...")
    mscd_dict = load_mscd_codes(mscd_csv)
    print(f"Cargados {len(mscd_dict)} códigos MSCD.")
    print(f"Analizando archivo: {txt_file}...")
    
    # Generar nombre del archivo de salida
    output_file = os.path.join(base_dir, f"extracted_{os.path.splitext(os.path.basename(input_file))[0]}.csv")
    
    count = extract_mscd_values(txt_file, mscd_dict, output_file, verbose)
    print(f"Se encontraron {count} valores de códigos MSCD en el archivo.")
    print(f"Resultados guardados en: {output_file}")

if __name__ == "__main__":
    main()