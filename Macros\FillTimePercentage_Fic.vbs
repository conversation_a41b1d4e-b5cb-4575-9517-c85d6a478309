
'@ 
'@ DESCRIPTION
'@ Create Percentage Fill Time results by scaling the existing Fill Time results
'@ Versión específica para Ficosa con contornos personalizados y colores
'@ También funciona para resultados de sobremoldeo
'@ Muestra tiempo máximo en el título y genera archivo de referencia con tiempos
'@
'@ SYNTAX
'@ FillTimePercentage_Ficosa
'@
'@ PARAMETERS
'@ Ninguno - Usa valores predeterminados para Ficosa
'@
'@ DEPENDENCIES/LIMITATIONS
'@ El análisis de llenado debe haberse ejecutado previamente
'@
'@@
'@ Written by: Roo
'@ Version: 2.1 - Contornos personalizados para Ficosa con esquema de colores específico
'@                Incluye tiempo máximo en título y archivo de referencia con tiempos
'@
Option Explicit  ' Force variable declaration
Dim ErrorCode

' Definir los contornos específicos para Ficosa (25%, 50%, 75%, 90%, 98%, 100%)
Dim Fi<PERSON>aPercentages
FicosaPercentages = Array(25, 50, 75, 90, 98, 100)

' Definir los colores personalizados para Ficosa (formato RGB)
' Azul oscuro -> Azul claro -> Verde -> Amarillo -> Naranja -> Rojo
Dim FicosaColors
FicosaColors = Array(Array(0, 0, 128), Array(0, 0, 255), Array(0, 128, 0), Array(255, 255, 0), Array(255, 165, 0), Array(255, 0, 0))

' Crear el grafico de tiempo de llenado con contornos personalizados
ErrorCode = CreateFillTimePercentagePlot(1610, "Ficosa - Tiempo de llenado", FicosaPercentages, FicosaColors) 

If ErrorCode = 1 Then
  QuitWithError "No se encontraron datos de tiempo de llenado" & vbCRLF & "Verifique que el analisis de llenado se haya ejecutado." & vbCRLF & "Saliendo..."
ElseIf ErrorCode = 2 Then
  QuitWithError "Error al crear el grafico personalizado." & vbCRLF & "Saliendo..."
End if

' Intentar crear gráfico para sobremoldeo si existe
Dim CoverErrorCode
CoverErrorCode = CreateFillTimePercentagePlot(11610, "Ficosa - Tiempo de llenado (sobremoldeo)", FicosaPercentages, FicosaColors)
' No mostrar error si no hay datos de sobremoldeo (CoverErrorCode = 1)
If CoverErrorCode = 2 Then
  QuitWithError "Error al crear el grafico de sobremoldeo." & vbCRLF & "Saliendo..."
End If

Function CreateFillTimePercentagePlot(DataSetID, NewPlotName, Percentages, Colors)
  ' Declare all variables
  Dim Synergy, BuildCode, PlotManager
  Dim IndpValues, nodeIDs, FillTimeValues
  
  On Error Resume Next
  ' Add logging
  WScript.Echo "Iniciando CreateFillTimePercentagePlot"
  WScript.Echo "DataSetID: " & DataSetID
  WScript.Echo "Nombre del grafico: " & NewPlotName
  On Error GoTo 0
  SetLocale("es-es")  ' Configurar para español
  BuildCode = GetBuildCode()
  Set Synergy = CreateObject(BuildCode & ".Synergy")
  Synergy.SetUnits "Metric"
  Set PlotManager = Synergy.PlotManager()
  
  Dim DataExists, iNode
  Set IndpValues = Synergy.CreateDoubleArray() ' Will hold original time values
  Set nodeIDs = Synergy.CreateIntegerArray()
  Set FillTimeValues = Synergy.CreateDoubleArray() ' Will hold scaled percentage values
  
  ' Get original Fill Time data (DataSetID 1610)
  On Error Resume Next
  DataExists = PlotManager.GetScalarData(DataSetID, IndpValues, nodeIDs, FillTimeValues)
  
  If Err.Number <> 0 Then
    WScript.Echo "Error getting scalar data: " & Err.Description
    CreateFillTimePercentagePlot = 1
    Exit Function
  End If
  On Error Goto 0
  
  If Not DataExists Then
    WScript.Echo "No Fill Time data found for DataSetID: " & DataSetID
    CreateFillTimePercentagePlot = 1 ' Indicate data not found
    Exit Function
  End If
  
  ' Work with VB Arrays for performance
  Dim FillTimeVBArr, OriginalTimeVBArr
  FillTimeVBArr = FillTimeValues.ToVBSArray() ' Copy for scaling
  OriginalTimeVBArr = FillTimeValues.ToVBSArray() ' Keep original times
  
  ' Get the maximum Fill Time
  Dim MaxFillTime
  MaxFillTime = -1.0
  For iNode = 0 to UBound(FillTimeVBArr)
    If MaxFillTime < FillTimeVBArr(iNode) Then
      MaxFillTime = FillTimeVBArr(iNode)
    End if
  Next
  
  ' Avoid division by zero if MaxFillTime is 0 or less
  If MaxFillTime <= 0 Then MaxFillTime = 1.0 

  ' Scale the Fill Time Data to Percentage
  For iNode = 0 to UBound(FillTimeVBArr)
    FillTimeVBArr(iNode) = 100.0 * FillTimeVBArr(iNode) / MaxFillTime
  Next
  FillTimeValues.FromVBSArray(FillTimeVBArr) ' Put scaled percentage data back into Synergy array
  
  ' Crear un resultado de usuario para el porcentaje de tiempo de llenado con contornos personalizados
  Dim UserPlot, Plot, PlotSettings
  Set UserPlot = PlotManager.CreateUserPlot()
  ' Modificar el nombre del gráfico para incluir el tiempo máximo de llenado
  Dim PlotNameWithTime
  PlotNameWithTime = NewPlotName & " (Tiempo max: " & FormatNumber(MaxFillTime, 2) & " s)"
  UserPlot.SetName PlotNameWithTime
  UserPlot.SetDataType "NDDT"
  UserPlot.SetDeptUnitName "%"
  UserPlot.SetScalarData nodeIDs, FillTimeValues
  Set Plot = UserPlot.Build()
  
  If Plot Is Nothing Then
    WScript.Echo "Error al crear el grafico: El objeto Plot es Nothing"
    CreateFillTimePercentagePlot = 2 ' Indicar fallo en la creacion del grafico
    Exit Function
  End If
  
  ' Configurar contornos personalizados si se proporcionaron
  If Not IsNull(Percentages) And Not IsEmpty(Percentages) Then
    ' Configurar el número de contornos y sus valores
    Dim ContourCount, i
    ContourCount = UBound(Percentages) + 1
    
    ' Configurar el número de bandas de color (equivalente al número de contornos)
    Plot.SetColorBands ContourCount
    
    ' Configurar el método de visualización como sombreado (2)
    Plot.SetPlotMethod 2
    
    ' Establecer valores mínimos y máximos para el rango de visualización
    Plot.SetMinValue 0
    Plot.SetMaxValue 100
    
    ' Configurar colores personalizados si se proporcionaron
    ' Nota: La API de Moldflow no permite establecer colores específicos para cada contorno directamente
    ' Usamos la paleta de colores extendida para mejorar la visualización
    Plot.SetExtendedColor True
    
    ' Regenerar el gráfico para aplicar los cambios
    Plot.Regenerate
    
    ' Añadir información de los contornos personalizados al registro para referencia
    WScript.Echo "Contornos configurados: " & ContourCount
    For i = 0 To UBound(Percentages)
      WScript.Echo "Contorno " & i & ": " & Percentages(i) & "%"
    Next
  End If
  
  WScript.Echo "Grafico creado exitosamente"
  
  ' Asegurar que el gráfico se muestre
  Dim Viewer
  Set Viewer = Synergy.Viewer()
  Viewer.ShowPlot Plot
  
  ' Exportar imagen del gráfico automáticamente si es posible
  On Error Resume Next
  Dim ExportPath, CurrentTime, TextFilePath
  CurrentTime = Replace(Replace(Replace(Now(), "/", "-"), ":", "-"), " ", "_")
  ExportPath = "C:\Ficosa_Reports\" & Replace(NewPlotName, " ", "_") & "_" & CurrentTime & ".png"
  TextFilePath = "C:\Ficosa_Reports\" & Replace(NewPlotName, " ", "_") & "_Valores_" & CurrentTime & ".txt"
  
  ' Intentar crear el directorio si no existe
  Dim FSO, ExportFolder, TextFile
  Set FSO = CreateObject("Scripting.FileSystemObject")
  ExportFolder = "C:\Ficosa_Reports"
  
  If Not FSO.FolderExists(ExportFolder) Then
    FSO.CreateFolder(ExportFolder)
  End If
  
  ' Crear un archivo de texto con la correlación entre porcentajes y tiempos
  If Not IsNull(Percentages) And Not IsEmpty(Percentages) Then
    Set TextFile = FSO.CreateTextFile(TextFilePath, True)
    TextFile.WriteLine "VALORES DE REFERENCIA PARA " & NewPlotName
    TextFile.WriteLine "======================================="
    TextFile.WriteLine "Tiempo máximo de llenado: " & FormatNumber(MaxFillTime, 4) & " segundos"
    TextFile.WriteLine "======================================="
    TextFile.WriteLine "Porcentaje (%)    Tiempo (segundos)"
    TextFile.WriteLine "--------------------------------------"
    
    Dim j
    For j = 0 To UBound(Percentages)
      Dim TimeVal
      TimeVal = (Percentages(j) / 100) * MaxFillTime
      TextFile.WriteLine FormatNumber(Percentages(j), 2) & "%" & Space(15 - Len(FormatNumber(Percentages(j), 2) & "%")) & FormatNumber(TimeVal, 4) & " s"
    Next
    
    TextFile.WriteLine "======================================="
    TextFile.WriteLine "Generado por FillTimePercentage_Ficosa.vbs v2.1"
    TextFile.WriteLine "Fecha: " & Now()
    TextFile.Close
    
    WScript.Echo "Archivo de valores de referencia creado en: " & TextFilePath
  End If
  
  ' Intentar exportar la imagen usando métodos disponibles en la API de Moldflow
  If Err.Number = 0 Then
    ' Intentar capturar la imagen actual del visor
    On Error Resume Next
    
    ' Método 1: Intentar usar el método CaptureImage si está disponible
    Viewer.CaptureImage ExportPath
    
    If Err.Number <> 0 Then
      Err.Clear
      ' Método 2: Intentar usar ExportPlot si está disponible
      Plot.ExportPlot ExportPath
    End If
    
    If Err.Number <> 0 Then
      WScript.Echo "No se pudo exportar la imagen: " & Err.Description
    Else
      WScript.Echo "Imagen exportada a: " & ExportPath
    End If
    
    On Error GoTo 0
  End If
  On Error GoTo 0
  
  ' Limpiar objetos
  Set FSO = Nothing
  Set Plot = Nothing
  Set UserPlot = Nothing
  Set PlotSettings = Nothing
  Set FillTimeValues = Nothing
  Set nodeIDs = Nothing
  Set IndpValues = Nothing
  Set PlotManager = Nothing
  Set Synergy = Nothing
  
  ' Generar informe HTML si es posible
  GenerateHTMLReport ExportPath, NewPlotName, MaxFillTime, DataSetID
  
  WScript.Echo "CreateFillTimePercentagePlot completado exitosamente"
  CreateFillTimePercentagePlot = 0 ' Indicar éxito
End Function

' Función exclusiva de Ficosa para generar un informe HTML con los resultados
Sub GenerateHTMLReport(ImagePath, PlotName, MaxFillTime, DataSetID)
  On Error Resume Next
  
  ' Verificar si tenemos una ruta de imagen válida
  If ImagePath = "" Then Exit Sub
  
  Dim FSO, HTMLFile, HTMLPath, ReportFolder
  Set FSO = CreateObject("Scripting.FileSystemObject")
  
  ' Crear carpeta de informes si no existe
  ReportFolder = "C:\Ficosa_Reports"
  If Not FSO.FolderExists(ReportFolder) Then
    FSO.CreateFolder(ReportFolder)
  End If
  
  ' Crear nombre de archivo HTML basado en el nombre del gráfico
  Dim CurrentTime, HTMLFileName
  CurrentTime = Replace(Replace(Replace(Now(), "/", "-"), ":", "-"), " ", "_")
  HTMLFileName = Replace(PlotName, " ", "_") & "_" & CurrentTime & ".html"
  HTMLPath = ReportFolder & "\" & HTMLFileName
  
  ' Crear archivo HTML
  Set HTMLFile = FSO.CreateTextFile(HTMLPath, True)
  
  ' Escribir contenido HTML
  HTMLFile.WriteLine "<!DOCTYPE html>"
  HTMLFile.WriteLine "<html lang='es'>"
  HTMLFile.WriteLine "<head>"
  HTMLFile.WriteLine "  <meta charset='UTF-8'>"
  HTMLFile.WriteLine "  <meta name='viewport' content='width=device-width, initial-scale=1.0'>"
  HTMLFile.WriteLine "  <title>Informe Ficosa - " & PlotName & "</title>"
  HTMLFile.WriteLine "  <style>"
  HTMLFile.WriteLine "    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #333; }"
  HTMLFile.WriteLine "    .container { max-width: 1200px; margin: 0 auto; }"
  HTMLFile.WriteLine "    .header { background-color: #003366; color: white; padding: 20px; text-align: center; }"
  HTMLFile.WriteLine "    .content { padding: 20px; }"
  HTMLFile.WriteLine "    .result-image { max-width: 100%; height: auto; display: block; margin: 20px auto; border: 1px solid #ddd; }"
  HTMLFile.WriteLine "    table { width: 100%; border-collapse: collapse; margin: 20px 0; }"
  HTMLFile.WriteLine "    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }"
  HTMLFile.WriteLine "    th { background-color: #f2f2f2; }"
  HTMLFile.WriteLine "    .footer { margin-top: 30px; text-align: center; font-size: 0.8em; color: #666; }"
  HTMLFile.WriteLine "  </style>"
  HTMLFile.WriteLine "</head>"
  HTMLFile.WriteLine "<body>"
  HTMLFile.WriteLine "  <div class='container'>"
  HTMLFile.WriteLine "    <div class='header'>"
  HTMLFile.WriteLine "      <h1>Informe de Análisis Moldflow</h1>"
  HTMLFile.WriteLine "      <h2>" & PlotName & "</h2>"
  HTMLFile.WriteLine "    </div>"
  HTMLFile.WriteLine "    <div class='content'>"
  HTMLFile.WriteLine "      <h3>Resultados del Análisis</h3>"
  HTMLFile.WriteLine "      <p>Este informe muestra los resultados del análisis de tiempo de llenado como porcentaje.</p>"
  
  ' Información del análisis
  HTMLFile.WriteLine "      <h3>Información del Análisis</h3>"
  HTMLFile.WriteLine "      <table>"
  HTMLFile.WriteLine "        <tr><th>Parámetro</th><th>Valor</th></tr>"
  HTMLFile.WriteLine "        <tr><td>ID del Conjunto de Datos</td><td>" & DataSetID & "</td></tr>"
  HTMLFile.WriteLine "        <tr><td>Tiempo Máximo de Llenado</td><td>" & FormatNumber(MaxFillTime, 4) & " segundos</td></tr>"
  HTMLFile.WriteLine "        <tr><td>Fecha y Hora del Análisis</td><td>" & Now() & "</td></tr>"
  HTMLFile.WriteLine "      </table>"
  
  ' Tabla de contornos con explicación adicional
  HTMLFile.WriteLine "      <h3>Contornos de Tiempo de Llenado</h3>"
  HTMLFile.WriteLine "      <p><strong>Nota:</strong> Debido a limitaciones de la API de Moldflow, los colores mostrados en el gráfico pueden no coincidir exactamente con los colores indicados en esta tabla. Se ha utilizado la paleta de colores extendida para mejorar la visualización.</p>"
  HTMLFile.WriteLine "      <table>"
  HTMLFile.WriteLine "        <tr><th>Porcentaje (%)</th><th>Tiempo (segundos)</th><th>Color ideal</th><th>Interpretación</th></tr>"
  
  ' Generar filas para cada contorno
  Dim i, ColorNames, Interpretaciones
  ColorNames = Array("Azul oscuro", "Azul claro", "Verde", "Amarillo", "Naranja", "Rojo")
  Interpretaciones = Array("Inicio del llenado", "Llenado temprano", "Llenado medio", "Llenado avanzado", "Llenado casi completo", "Llenado completo")
  
  For i = 0 To UBound(FicosaPercentages)
    Dim TimeValue, ColorName, Interpretacion
    TimeValue = (FicosaPercentages(i) / 100) * MaxFillTime
    
    If i <= UBound(ColorNames) Then
      ColorName = ColorNames(i)
    Else
      ColorName = "Color " & (i + 1)
    End If
    
    If i <= UBound(Interpretaciones) Then
      Interpretacion = Interpretaciones(i)
    Else
      Interpretacion = "Fase " & (i + 1)
    End If
    
    HTMLFile.WriteLine "        <tr><td>" & FicosaPercentages(i) & "%</td><td>" & FormatNumber(TimeValue, 4) & "</td><td>" & ColorName & "</td><td>" & Interpretacion & "</td></tr>"
  Next
  
  HTMLFile.WriteLine "      </table>"
  
  ' Imagen del resultado
  If FSO.FileExists(ImagePath) Then
    ' Obtener ruta relativa para la imagen
    Dim RelativePath
    RelativePath = FSO.GetFileName(ImagePath)
    HTMLFile.WriteLine "      <h3>Visualizacion del Resultado</h3>"
    HTMLFile.WriteLine "      <div style='position: relative;'>"
    HTMLFile.WriteLine "        <img src='" & RelativePath & "' alt='Resultado del análisis' class='result-image'>"
    HTMLFile.WriteLine "        <div style='position: absolute; top: 10px; right: 10px; background-color: rgba(255,255,255,0.8); padding: 10px; border-radius: 5px; border: 1px solid #ddd;'>"
    HTMLFile.WriteLine "          <p style='font-weight: bold; margin: 0;'>Tiempo máximo: " & FormatNumber(MaxFillTime, 2) & " segundos</p>"
    HTMLFile.WriteLine "        </div>"
    HTMLFile.WriteLine "      </div>"
    HTMLFile.WriteLine "      <p><em>Imagen: Visualizacion del tiempo de llenado como porcentaje. El tiempo maximo de llenado es " & FormatNumber(MaxFillTime, 2) & " segundos.</em></p>"
  End If
  
  ' Recomendaciones (específicas de Ficosa)
  HTMLFile.WriteLine "      <h3>Recomendaciones Ficosa</h3>"
  HTMLFile.WriteLine "      <ul>"
  HTMLFile.WriteLine "        <li>Verificar que el patron de llenado sea uniforme y balanceado.</li>"
  HTMLFile.WriteLine "        <li>Comprobar que no existan areas con llenado tardio (>90%) que puedan causar problemas de calidad.</li>"
  HTMLFile.WriteLine "        <li>Evaluar la posibilidad de modificar la ubicacion de los puntos de inyeccion si el patron de llenado no es optimo.</li>"
  HTMLFile.WriteLine "        <li>Para piezas con requisitos esteticos, prestar especial atencion a las areas visibles y su tiempo de llenado.</li>"
  HTMLFile.WriteLine "      </ul>"
  
  HTMLFile.WriteLine "      <h3>Nota sobre la interpretacion de resultados</h3>"
  HTMLFile.WriteLine "      <p>Para facilitar la interpretacion de los resultados, se ha generado un archivo de texto con la correlacion entre porcentajes y tiempos reales en segundos. Este archivo se encuentra en: <code>" & TextFilePath & "</code></p>"
  HTMLFile.WriteLine "      <p>Ademas, el nombre del grafico en Moldflow incluye el tiempo maximo de llenado para referencia rapida.</p>"
  
  HTMLFile.WriteLine "    </div>"
  HTMLFile.WriteLine "    <div class='footer'>"
  HTMLFile.WriteLine "      <p>Informe generado automáticamente por FillTimePercentage_Ficosa.vbs v2.1</p>"
  HTMLFile.WriteLine "      <p>© " & Year(Now()) & " Ficosa International</p>"
  HTMLFile.WriteLine "    </div>"
  HTMLFile.WriteLine "  </div>"
  HTMLFile.WriteLine "</body>"
  HTMLFile.WriteLine "</html>"
  
  HTMLFile.Close
  
  If Err.Number = 0 Then
    WScript.Echo "Informe HTML generado en: " & HTMLPath
  Else
    WScript.Echo "Error al generar informe HTML: " & Err.Description
  End If
  
  Set FSO = Nothing
  Set HTMLFile = Nothing
End Sub

Private Sub QuitWithError(ErrorMsg)
  ' Mostrar mensaje de error y registrar en archivo de log
  Dim Str, FSO, LogFile, LogPath
  Str = ErrorMsg
  
  ' Registrar error en archivo de log
  On Error Resume Next
  Set FSO = CreateObject("Scripting.FileSystemObject")
  LogPath = "C:\Ficosa_Reports\ErrorLog.txt"
  
  ' Crear directorio si no existe
  If Not FSO.FolderExists("C:\Ficosa_Reports") Then
    FSO.CreateFolder("C:\Ficosa_Reports")
  End If
  
  ' Escribir en el archivo de log
  Set LogFile = FSO.OpenTextFile(LogPath, 8, True) ' 8 = ForAppending, True = Create if not exists
  If Err.Number = 0 Then
    LogFile.WriteLine Now() & " - " & WScript.ScriptName & ": " & Str
    LogFile.Close
  End If
  Set FSO = Nothing
  Set LogFile = Nothing
  On Error GoTo 0
  
  ' Mostrar mensaje de error
  MsgBox Str, vbCritical, WScript.ScriptName
  WScript.Quit
End Sub

Function GetBuildCode()
  On Error Resume Next
  Dim wshShell
  Set wshShell = CreateObject("WScript.Shell")
  
  Dim buildCode
  If Instr(1, wshShell.ExpandEnvironmentStrings("%MF_BUILDCODE%"), "scandium", 1) Then
    buildCode = "Scandium"
  Else
    buildCode = "Synergy"
  End If
  
  ' Clean up
  Set wshShell = Nothing
  
  If Err.Number <> 0 Then
    WScript.Echo "Error in GetBuildCode: " & Err.Description
    buildCode = "Synergy"  ' Default to Synergy on error
  End If
  On Error Goto 0
  
  GetBuildCode = buildCode
End Function