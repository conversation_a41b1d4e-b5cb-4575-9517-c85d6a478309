using System;
using System.Runtime.InteropServices;

namespace HDMoldFlowLibrary
{
    /// <summary>
    /// Clase para gestionar la conexión con la API de Synergy de Moldflow.
    /// Encapsula la inicialización, obtención y liberación de objetos Synergy.
    /// </summary>
    public class SynergyConnector : IDisposable
    {
        private dynamic synergyInstance;
        private bool disposed = false;

        /// <summary>
        /// Inicializa una nueva instancia de SynergyConnector y crea el objeto Synergy OLE/COM.
        /// </summary>
        /// <exception cref="COMException">Si no se puede crear la instancia de Synergy.</exception>
        public SynergyConnector()
        {
            try
            {
                // ProgID típico para Synergy OLE Automation
                synergyInstance = Activator.CreateInstance(Type.GetTypeFromProgID("Synergy.Application"));
            }
            catch (Exception ex)
            {
                throw new COMException("No se pudo inicializar Synergy. ¿Está instalado y registrado correctamente?", ex);
            }
        }

        /// <summary>
        /// Obtiene el objeto Project actual de Synergy.
        /// </summary>
        /// <returns>Objeto Project dinámico.</returns>
        public dynamic GetProject()
        {
            return synergyInstance?.Project();
        }

        /// <summary>
        /// Obtiene el objeto StudyDoc activo de Synergy.
        /// </summary>
        /// <returns>Objeto StudyDoc dinámico.</returns>
        public dynamic GetStudyDoc()
        {
            return synergyInstance?.StudyDoc();
        }

        /// <summary>
        /// Libera los recursos asociados al objeto Synergy.
        /// </summary>
        public void Dispose()
        {
            if (!disposed)
            {
                if (synergyInstance != null)
                {
                    try
                    {
                        Marshal.ReleaseComObject(synergyInstance);
                    }
                    catch { }
                    synergyInstance = null;
                }
                disposed = true;
                GC.SuppressFinalize(this);
            }
        }

        /// <summary>
        /// Destructor para asegurar la liberación de recursos.
        /// </summary>
        ~SynergyConnector()
        {
            Dispose();
        }
    }
}