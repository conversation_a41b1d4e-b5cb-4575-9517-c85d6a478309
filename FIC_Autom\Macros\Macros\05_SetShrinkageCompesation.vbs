'%RunPerInstance
'@ DESCRIPTION Set shrinkage and scale range for Deflection results
'@ Author: <PERSON>L<PERSON>ale("en-us")
Dim SynergyGetter, Synergy
On Error Resume Next
Set SynergyGetter = GetObject(CreateObject("WScript.Shell").ExpandEnvironmentStrings("%SAInstance%"))
On Error GoTo 0
If (Not IsEmpty(SynergyGetter)) Then
	Set Synergy = SynergyGetter.GetSASynergy
Else
	Set Synergy = CreateObject("synergy.Synergy")
End If
Synergy.SetUnits "Metric"

' SET scale on "shrkVector"
usrInput = InputBox("Shrinkage factor to use: ", "Shrinkage")
shrkg = CDbl(usrInput)
Set shrkVector = Synergy.CreateVector()
shrkVector.SetXYZ shrkg, shrkg, shrkg

' SET range on "myrange"
'usrInput = InputBox("Range for the scale (just the number, it will be converted and used both for Min and Max): ", "Results range")
'scaleInput = CDbl(usrInput)
'Dim scaleRange(1)
'scaleRange(0)= scaleInput * -1
'scaleRange(1)= scaleInput


Dim plotList(3)
plotList(0)="Deflection, all effects:Deflection"
plotList(1)="Deflection, all effects:X Component"
plotList(2)="Deflection, all effects:Y Component"
plotList(3)="Deflection, all effects:Z Component"

Set PlotManager = Synergy.PlotManager()
Set Viewer = Synergy.Viewer()
For Each currplot in plotList
	Set Plot = PlotManager.FindPlotByName(currplot)
	Plot.SetScaleOption 0
	Plot.SetExtendedColor False
	'Plot.SetMinValue scaleRange(0)
	'Plot.SetMaxValue scaleRange(1)
	Plot.SetPlotMethod 3
	Plot.SetDeflectionScaleDirection 31
	Plot.SetShrinkageCompensationOption "Isotropic"
	Plot.SetShrinkageCompensationEstimatedShrinkage shrkVector
	Plot.Regenerate
Next