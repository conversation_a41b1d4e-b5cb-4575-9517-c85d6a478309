﻿// Decompiled with JetBrains decompiler
// Type: Microsoft.Office.Interop.Excel.XlFileFormat
// Assembly: HDMFAI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: A66A47CE-D63B-4ACB-B9A4-A4F118252C4E
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFAI.dll

using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

namespace Microsoft.Office.Interop.Excel
{
  [CompilerGenerated]
  [TypeIdentifier("00020813-0000-0000-c000-000000000046", "Microsoft.Office.Interop.Excel.XlFileFormat")]
  public enum XlFileFormat
  {
    xlCurrentPlatformText = -4158, // 0xFFFFEFC2
    xlWorkbookNormal = -4143, // 0xFFFFEFD1
    xlSYLK = 2,
    xlWKS = 4,
    xlWK1 = 5,
    xlCSV = 6,
    xlDBF2 = 7,
    xlDBF3 = 8,
    xlDIF = 9,
    xlDBF4 = 11, // 0x0000000B
    xlWJ2WD1 = 14, // 0x0000000E
    xlWK3 = 15, // 0x0000000F
    xlExcel2 = 16, // 0x00000010
    xlTemplate = 17, // 0x00000011
    xlTemplate8 = 17, // 0x00000011
    xlAddIn = 18, // 0x00000012
    xlAddIn8 = 18, // 0x00000012
    xlTextMac = 19, // 0x00000013
    xlTextWindows = 20, // 0x00000014
    xlTextMSDOS = 21, // 0x00000015
    xlCSVMac = 22, // 0x00000016
    xlCSVWindows = 23, // 0x00000017
    xlCSVMSDOS = 24, // 0x00000018
    xlIntlMacro = 25, // 0x00000019
    xlIntlAddIn = 26, // 0x0000001A
    xlExcel2FarEast = 27, // 0x0000001B
    xlWorks2FarEast = 28, // 0x0000001C
    xlExcel3 = 29, // 0x0000001D
    xlWK1FMT = 30, // 0x0000001E
    xlWK1ALL = 31, // 0x0000001F
    xlWK3FM3 = 32, // 0x00000020
    xlExcel4 = 33, // 0x00000021
    xlWQ1 = 34, // 0x00000022
    xlExcel4Workbook = 35, // 0x00000023
    xlTextPrinter = 36, // 0x00000024
    xlWK4 = 38, // 0x00000026
    xlExcel5 = 39, // 0x00000027
    xlExcel7 = 39, // 0x00000027
    xlWJ3 = 40, // 0x00000028
    xlWJ3FJ3 = 41, // 0x00000029
    xlUnicodeText = 42, // 0x0000002A
    xlExcel9795 = 43, // 0x0000002B
    xlHtml = 44, // 0x0000002C
    xlWebArchive = 45, // 0x0000002D
    xlXMLSpreadsheet = 46, // 0x0000002E
    xlExcel12 = 50, // 0x00000032
    xlOpenXMLWorkbook = 51, // 0x00000033
    xlWorkbookDefault = 51, // 0x00000033
    xlOpenXMLWorkbookMacroEnabled = 52, // 0x00000034
    xlOpenXMLTemplateMacroEnabled = 53, // 0x00000035
    xlOpenXMLTemplate = 54, // 0x00000036
    xlOpenXMLAddIn = 55, // 0x00000037
    xlExcel8 = 56, // 0x00000038
    xlOpenDocumentSpreadsheet = 60, // 0x0000003C
    xlOpenXMLStrictWorkbook = 61, // 0x0000003D
  }
}
