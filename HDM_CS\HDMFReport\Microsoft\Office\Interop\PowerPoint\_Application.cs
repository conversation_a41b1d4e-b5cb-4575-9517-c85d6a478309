﻿// Decompiled with JetBrains decompiler
// Type: Microsoft.Office.Interop.PowerPoint._Application
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

namespace Microsoft.Office.Interop.PowerPoint
{
  [CompilerGenerated]
  [Guid("91493442-5A91-11CF-8700-00AA0060263B")]
  [DefaultMember("Name")]
  [TypeIdentifier]
  [ComImport]
  public interface _Application
  {
    [DispId(2001)]
    Presentations Presentations { [DispId(2001), MethodImpl(MethodImplOptions.InternalCall, MethodCodeType = MethodCodeType.Runtime)] [return: MarshalAs(UnmanagedType.Interface)] get; }

    [SpecialName]
    [MethodImpl(MethodCodeType = MethodCodeType.Runtime)]
    sealed extern void _VtblGap1_7();

    [DispId(0)]
    string Name { [DispId(0), MethodImpl(MethodImplOptions.InternalCall, MethodCodeType = MethodCodeType.Runtime)] [return: MarshalAs(UnmanagedType.BStr)] get; }
  }
}
