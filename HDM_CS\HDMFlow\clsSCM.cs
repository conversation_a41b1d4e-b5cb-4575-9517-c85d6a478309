﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.clsSCM
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLog4Net;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;

namespace HDMoldFlow
{
  public class clsSCM
  {
    public static List<JToken> GetJobData()
    {
      List<JToken> source = new List<JToken>();
      try
      {
        HttpClient httpClient = new HttpClient();
        HttpResponseMessage result1 = httpClient.GetAsync("http://" + Dns.GetHostName() + ":" + clsDefine.g_strScmPort + "/ComputeQueue/v1/jobs").Result;
        if (result1.ReasonPhrase != "OK")
          return source.ToList<JToken>();
        string result2 = result1.Content.ReadAsStringAsync().Result;
        httpClient.Dispose();
        if (result2 == "")
          return source.ToList<JToken>();
        JArray jarray = JArray.Parse(result2);
        List<JToken> jtokenList1 = new List<JToken>();
        for (int index = 0; index < jarray.Count; ++index)
          jtokenList1.Add(jarray[index]);
        jtokenList1.Sort(new Comparison<JToken>(clsSCM.SCMSort));
        List<JToken> jtokenList2 = new List<JToken>();
        while (true)
        {
          if (jtokenList1.Count != 0)
          {
            jtokenList2.Clear();
            JToken jtoken = jtokenList1[0];
            jtokenList2.Add(jtoken);
            for (int index = 0; index < jtokenList1.Count; ++index)
            {
              if ((string) jtoken[(object) "payload"][(object) "location"] == (string) jtokenList1[index][(object) "payload"][(object) "location"])
                jtokenList2.Add(jtokenList1[index]);
            }
            for (int index = 0; index < jtokenList2.Count; ++index)
            {
              if ((string) jtokenList2[index][(object) "payload"][(object) "type"] == "Study" || (string) jtokenList2[index][(object) "payload"][(object) "type"] == "mesh")
                jtoken = jtokenList2[index];
              jtokenList1.Remove(jtokenList2[index]);
            }
            source.Add(jtoken);
          }
          else
            break;
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsSCM]GetJobData):" + ex.Message));
      }
      return source.ToList<JToken>();
    }

    private static int SCMSort(JToken p_jt1, JToken p_jt2) => Convert.ToInt64((object) p_jt1[(object) "epoch"]) > Convert.ToInt64((object) p_jt2[(object) "epoch"]) ? -1 : 1;

    public static JToken GetLastDataFromTwoList(
      List<JToken> p_lst_jtBefore,
      List<JToken> p_lst_jtAfter)
    {
      for (int index = 0; index < p_lst_jtBefore.Count; ++index)
        p_lst_jtAfter.Remove(p_lst_jtBefore[index]);
      return p_lst_jtAfter.First<JToken>();
    }

    public static string GetJobStatus(string p_strJobID)
    {
      string jobStatus = "";
      try
      {
        HttpClient httpClient = new HttpClient();
        HttpResponseMessage result1 = httpClient.GetAsync("http://" + Dns.GetHostName() + ":" + clsDefine.g_strScmPort + "/ComputeQueue/v1/jobs").Result;
        if (result1.ReasonPhrase != "OK")
          return jobStatus;
        string result2 = result1.Content.ReadAsStringAsync().Result;
        httpClient.Dispose();
        if (result2 == "")
          return jobStatus;
        JArray jarray = JArray.Parse(result2);
        List<JToken> jtokenList1 = new List<JToken>();
        for (int index = 0; index < jarray.Count; ++index)
          jtokenList1.Add(jarray[index]);
        jtokenList1.Sort(new Comparison<JToken>(clsSCM.SCMSort));
        List<JToken> jtokenList2 = new List<JToken>();
        List<JToken> jtokenList3 = new List<JToken>();
        while (true)
        {
          if (jtokenList1.Count != 0)
          {
            jtokenList3.Clear();
            JToken jtoken = jtokenList1[0];
            jtokenList3.Add(jtoken);
            for (int index = 0; index < jtokenList1.Count; ++index)
            {
              if ((string) jtoken[(object) "payload"][(object) "location"] == (string) jtokenList1[index][(object) "payload"][(object) "location"])
                jtokenList3.Add(jtokenList1[index]);
            }
            for (int index = 0; index < jtokenList3.Count; ++index)
            {
              if (!jtokenList3[index][(object) "payload"][(object) "type"].ToString().Contains(":"))
                jtoken = jtokenList3[index];
              jtokenList1.Remove(jtokenList3[index]);
            }
            jtokenList2.Add(jtoken);
          }
          else
            break;
        }
        for (int index = 0; index < jtokenList2.Count; ++index)
        {
          if (jtokenList2[index][(object) "payload"][(object) "xref"].ToString() == p_strJobID)
          {
            if ((string) jtokenList2[index][(object) "status"] == "INPROGRESS")
            {
              jobStatus = jtokenList2[index][(object) "status"].ToString() + "(" + (object) jtokenList2[index][(object) "progress"][(object) "percent"] + ")";
              break;
            }
            jobStatus = jtokenList2[index][(object) "status"].ToString();
            break;
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsSCM]GetJobStatus):" + ex.Message));
      }
      return jobStatus;
    }
  }
}
