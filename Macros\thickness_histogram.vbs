'@
'@ DESCRIPTION
'@ Toma el diagnóstico estándar de espesores y lo convierte en un User Plot compatible con Synergy, permitiendo análisis estándar (histograma, valores extremos).
'@
'@ SYNTAX
'@ CustomThickness
'@
'@ PARAMETERS
'@ none
'@
'@ DEPENDENCIES/LIMITATIONS
'@ Asume que hay un estudio abierto en Synergy
'@ none
'@
'@ History
'@ Modificado para integración óptima Synergy y análisis estándar - 2024
'@@
Option Explicit
SetLocale("en-us")
Dim Synergy, SynergyGetter
On Error Resume Next
Set SynergyGetter = GetObject(CreateObject("WScript.Shell").ExpandEnvironmentStrings("%SAInstance%"))
On Error GoTo 0
If (Not IsEmpty(SynergyGetter)) Then
    Set Synergy = SynergyGetter.GetSASynergy
Else
    Set Synergy = CreateObject("synergy.Synergy")
End If
Synergy.SetUnits "METRIC"

Dim StudyDoc, MeshType
Set StudyDoc = Synergy.StudyDoc()
If StudyDoc Is Nothing Then
    MsgBox "Por favor, seleccione un estudio abierto en Synergy", vbCritical, "Error"
    WScript.Quit(2)
End If
MeshType = StudyDoc.MeshType

Dim DiagnosisManager, PlotManager, Plot, Elems, TH, PlotName, UserPlot
Set Elems = Synergy.CreateIntegerArray()
Set TH = Synergy.CreateDoubleArray()
Set DiagnosisManager = Synergy.DiagnosisManager()
If MeshType = "3D" Then
    DiagnosisManager.GetThicknessDiagnosis2 0.0, 1000.0, True, Elems, TH
Else
    DiagnosisManager.GetThicknessDiagnosis 0.0, 1000.0, Elems, TH
End If
Set DiagnosisManager = Nothing

' Determinar nombre de plot según tipo de malla
If (MeshType = "Fusion") Or (MeshType = "Midplane") Then
    PlotName = "Thickness"
Else
    PlotName = "Dimension 3D"
End If

Set PlotManager = Synergy.PlotManager()
Set Plot = PlotManager.FindPlotByName(PlotName)
If Not Plot Is Nothing Then
    PlotManager.DeletePlot(Plot)
End If
Set UserPlot = PlotManager.CreateUserPlot()
UserPlot.SetDataType "ELDT"
UserPlot.SetName PlotName
UserPlot.SetDeptUnitName("mm")

' Escalar los valores de TH entre 0 y 5
Dim THArr2, minTH2, maxTH2, i
THArr2 = TH.toVBSArray()
minTH2 = THArr2(0)
maxTH2 = THArr2(0)
For i = 0 To UBound(THArr2)
    If THArr2(i) < minTH2 Then minTH2 = THArr2(i)
    If THArr2(i) > maxTH2 Then maxTH2 = THArr2(i)
Next
For i = 0 To UBound(THArr2)
    If maxTH2 > minTH2 Then
        THArr2(i) = 5 * (THArr2(i) - minTH2) / (maxTH2 - minTH2)
    Else
        THArr2(i) = 0
    End If
Next

' Copiar los valores escalados a un nuevo DoubleArray compatible
' Eliminar Append y trabajar directamente con el array escalado
Set THScaledArr = Synergy.CreateDoubleArray()
If IsArray(THArr2) Then
    THScaledArr = Synergy.CreateDoubleArrayFromVBSArray(THArr2)
End If

UserPlot.AddScalarData 0.0, Elems, THScaledArr
UserPlot.Build

Set Plot = PlotManager.FindPlotByName(PlotName)
If Not Plot Is Nothing Then
    Plot.SetNodalAveraging False
    Plot.Regenerate
End If

'-----------------------------
' Generación de histograma de espesores
'-----------------------------
Dim THArr, minTH, maxTH, binCount, binSize, bins(), freq(), idx, outLine
THArr = TH.toVBSArray()

' Determinar el rango de espesores
minTH = THArr(0)
maxTH = THArr(0)
For i = LBound(THArr) To UBound(THArr)
    If THArr(i) < minTH Then minTH = THArr(i)
    If THArr(i) > maxTH Then maxTH = THArr(i)
Next

binCount = 10 ' Número de intervalos del histograma
ReDim bins(binCount)
ReDim freq(binCount)
binSize = (maxTH - minTH) / binCount
For i = 0 To binCount
    bins(i) = minTH + i * binSize
    freq(i) = 0
Next

' Contar frecuencias
For i = LBound(THArr) To UBound(THArr)
    idx = Int((THArr(i) - minTH) / binSize)
    If idx > binCount - 1 Then idx = binCount - 1
    freq(idx) = freq(idx) + 1
Next

' Exportar histograma a archivo
Dim fso, histFile, histPath
Set fso = CreateObject("Scripting.FileSystemObject")
histPath = fso.GetAbsolutePathName(".") & "\ThicknessHistogram.txt"
Set histFile = fso.CreateTextFile(histPath, True)
histFile.WriteLine "BinStart\tBinEnd\tFrequency"
For i = 0 To binCount - 1
    outLine = CStr(bins(i)) & "\t" & CStr(bins(i) + binSize) & "\t" & CStr(freq(i))
    histFile.WriteLine outLine
Next
histFile.Close
MsgBox "Histograma de espesores exportado a " & histPath, vbInformation, "Histograma"
' Establecer rango de escala compatible con Synergy (0 a 5 mm)
Set PlotManager = Synergy.PlotManager()
Set UserPlot = PlotManager.CreateUserPlot()
UserPlot.SetDataType "ELDT"
UserPlot.SetName "Thickness"
UserPlot.AddScalarData 0.0, Elems, TH
UserPlot.Build

' Configuración avanzada para compatibilidad con histograma y min/max
Dim Viewer
Set Viewer = Synergy.Viewer()
Set Plot = Viewer.GetActivePlot()
Plot.SetEdgeDisplay 1
Plot.SetScaleOption 2
Plot.SetExtendedColor True
Plot.SetMinValue 0
Plot.SetMaxValue 5
Plot.SetPlotMethod 2
Plot.Regenerate