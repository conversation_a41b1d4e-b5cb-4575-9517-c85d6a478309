'@
'@ DESCRIPTION
'@ Take the Standard Thickness Diagnostics Plot  and convert it into a custom plot
'@
'@ SYNTAX
'@ CustomThickness
'@
'@ PARAMETERS
'@ none
'@
'@ DEPENDENCIES/LIMITATIONS
'@ Assumes a study file is open within synergy
'@ none
'@
'@ History
'@ Created DRA 9/9/2006
'@@
Option Explicit
SetLocale("en-us")
Dim Synergy
Dim SynergyGetter
On Error Resume Next
Set SynergyGetter = GetObject(CreateObject("WScript.Shell").ExpandEnvironmentStrings("%SAInstance%"))
On Error GoTo 0
If (Not IsEmpty(SynergyGetter)) Then
	Set Synergy = SynergyGetter.GetSASynergy
Else
	Set Synergy = CreateObject("synergy.Synergy")
End If
Synergy.SetUnits "METRIC"


Dim DiagnosisManager, PlotManager, Viewer
Dim Elems, TH, ARPlot, Plot


' Get aspect ratio diagnostics
Set Elems = Synergy.CreateIntegerArray()
Set TH = Synergy.CreateDoubleArray()
Set DiagnosisManager = Synergy.DiagnosisManager()
DiagnosisManager.GetThicknessDiagnosis 0, 10.0, Elems, TH
Set DiagnosisManager = Nothing

' Create user plot
Set PlotManager = Synergy.PlotManager()
Set ARPlot = PlotManager.CreateUserPlot()
ARPlot.SetDataType "ELDT"
ARPlot.SetName "Thickness"
ARPlot.SetDeptUnitName("mm")
ARPlot.AddScalarData 0.0, Elems, TH
ARPlot.Build

Set Viewer = Synergy.Viewer()
Set Plot = Viewer.GetActivePlot
Plot.SetNodalAveraging False
Plot.Regenerate
