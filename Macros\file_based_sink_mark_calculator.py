#!/usr/bin/env python3
"""
File-Based Sink Mark Calculator

This version works around COM issues by using file-based communication:
1. User manually exports required data from Synergy
2. Python processes the data and calculates sink mark depths
3. Python generates import files that user can load back into Synergy

Author: AI Assistant
Version: 1.0
"""

import os
import sys
import csv
import json
from pathlib import Path
import logging

try:
    import numpy as np
except ImportError:
    print("Error: numpy is required. Install with: pip install numpy")
    sys.exit(1)

from moldflow_mesh_utils import MeshProcessor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FileBasedSinkMarkCalculator:
    """
    File-based calculator that works with exported Synergy data.
    """
    
    def __init__(self):
        self.base_dir = Path(".")
        self.data_dir = self.base_dir / "synergy_data"
        self.data_dir.mkdir(exist_ok=True)
        
    def show_instructions(self):
        """Show instructions for manual data export."""
        print("=" * 70)
        print("FILE-BASED SINK MARK DEPTH CALCULATOR")
        print("=" * 70)
        print()
        print("Since COM automation is not working on this computer, please follow")
        print("these steps to export the required data from Synergy manually:")
        print()
        print("STEP 1: Export Volumetric Shrinkage Data")
        print("----------------------------------------")
        print("1. In Synergy, go to Results > Plots")
        print("2. Select 'Average Volumetric Shrinkage' result")
        print("3. Go to File > Export > Export Plot Data...")
        print("4. Save as: synergy_data/volumetric_shrinkage.csv")
        print("   (Choose CSV format)")
        print()
        print("STEP 2: Export Model Geometry")
        print("-----------------------------")
        print("1. In Synergy, go to File > Export > Export Model...")
        print("2. Choose UDM format")
        print("3. Save as: synergy_data/model.udm")
        print()
        print("STEP 3: Export Study Information")
        print("--------------------------------")
        print("1. Note your current unit system (English/Metric)")
        print("2. Create a file: synergy_data/study_info.json")
        print("3. Content should be:")
        print('   {"units": "Metric"}  or  {"units": "English"}')
        print()
        print("STEP 4: Run Calculation")
        print("-----------------------")
        print("Once you have exported all files, press Enter to continue...")
        
        input()
    
    def check_required_files(self):
        """Check if all required files are present."""
        required_files = [
            self.data_dir / "volumetric_shrinkage.csv",
            self.data_dir / "model.udm", 
            self.data_dir / "study_info.json"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not file_path.exists():
                missing_files.append(str(file_path))
        
        if missing_files:
            print("❌ Missing required files:")
            for file_path in missing_files:
                print(f"   - {file_path}")
            print()
            print("Please export the missing files and try again.")
            return False
        
        print("✅ All required files found!")
        return True
    
    def load_volumetric_shrinkage_data(self):
        """Load volumetric shrinkage data from CSV."""
        csv_file = self.data_dir / "volumetric_shrinkage.csv"
        
        try:
            # Try to read CSV with different formats
            node_list = []
            values = []
            
            with open(csv_file, 'r') as f:
                # Skip header lines and find data
                lines = f.readlines()
                
                for line in lines:
                    line = line.strip()
                    if not line or line.startswith('#') or 'Node' in line:
                        continue
                    
                    # Try to parse as CSV
                    parts = line.replace(',', ' ').split()
                    if len(parts) >= 2:
                        try:
                            node_id = int(float(parts[0]))
                            value = float(parts[1])
                            node_list.append(node_id)
                            values.append(value)
                        except ValueError:
                            continue
            
            if not node_list:
                logger.error("No valid data found in volumetric shrinkage CSV")
                return None, None
            
            logger.info(f"Loaded volumetric shrinkage data for {len(node_list)} nodes")
            return node_list, values
            
        except Exception as e:
            logger.error(f"Error loading volumetric shrinkage data: {e}")
            return None, None
    
    def load_study_info(self):
        """Load study information from JSON."""
        json_file = self.data_dir / "study_info.json"
        
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)
            
            units = data.get('units', 'Metric')
            logger.info(f"Study units: {units}")
            return units
            
        except Exception as e:
            logger.error(f"Error loading study info: {e}")
            return "Metric"  # Default
    
    def process_mesh_and_calculate(self, node_list, vol_shrink_data, units):
        """Process mesh and calculate sink mark depths."""
        logger.info("Processing mesh data...")
        
        udm_file = self.data_dir / "model.udm"
        
        # Process UDM file
        mesh_processor = MeshProcessor()
        if not mesh_processor.read_udm_file(str(udm_file)):
            logger.error("Failed to process UDM file")
            return None
        
        # Set up units
        if units == "English":
            length_unit = "in"
            decimal_digits = 6
        else:
            length_unit = "mm"
            decimal_digits = 4
        
        logger.info(f"Calculating sink depths for {len(node_list)} nodes...")
        
        # Create node-based volumetric shrinkage array
        max_node_label = mesh_processor.get_highest_node_label()
        av_vol_shrink = np.full(max_node_label + 1, -1001.0)
        
        # Populate volumetric shrinkage data
        for i, node_id in enumerate(node_list):
            vol_shrink_val = vol_shrink_data[i]
            # Avoid RNULL on nodes which do not have valid result
            if vol_shrink_val > 10000.0:
                vol_shrink_val = 0.0
            if node_id < len(av_vol_shrink):
                av_vol_shrink[node_id] = vol_shrink_val
        
        # Calculate sink depths for surface tetrahedra
        sink_depths = []
        surface_elements = []
        
        for tet_idx in range(mesh_processor.get_num_tets()):
            tet_thickness = mesh_processor.get_tet_thickness(tet_idx)
            
            if tet_thickness > 0.0:  # This tet is on the surface
                # Calculate average volumetric shrinkage for this tet
                tet_vol_shrink = 0.0
                valid_nodes = 0
                
                for vert_idx in range(4):
                    node_label = mesh_processor.get_tet_node(tet_idx, vert_idx)
                    if node_label < len(av_vol_shrink) and av_vol_shrink[node_label] > -1000.0:
                        tet_vol_shrink += av_vol_shrink[node_label] / 300.0  # Convert % to fraction and average
                        valid_nodes += 1
                
                if valid_nodes > 0:
                    tet_vol_shrink /= valid_nodes  # Average the shrinkage
                
                # Calculate linear shrinkage from volumetric shrinkage
                if tet_vol_shrink < 1.0 and tet_vol_shrink > 0.0:
                    linear_shrink = 1 - (1 - tet_vol_shrink) ** (1.0/3.0)
                else:
                    linear_shrink = 0.0
                
                # Calculate sink depth: 0.5 * thickness * linear_shrinkage
                sink_depth = linear_shrink * tet_thickness / 2.0
                
                sink_depths.append(sink_depth)
                surface_elements.append(mesh_processor.get_tet_label(tet_idx))
        
        logger.info(f"Calculated sink depths for {len(sink_depths)} surface elements")
        
        return {
            'sink_depths': sink_depths,
            'surface_elements': surface_elements,
            'length_unit': length_unit,
            'decimal_digits': decimal_digits
        }
    
    def export_results(self, results):
        """Export results to files that can be imported into Synergy."""
        if not results:
            return False
        
        # Export as CSV for manual import
        csv_file = self.data_dir / "sink_mark_depths.csv"
        
        try:
            with open(csv_file, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['Element_ID', 'Sink_Depth_' + results['length_unit']])
                
                for i, elem_id in enumerate(results['surface_elements']):
                    depth = results['sink_depths'][i]
                    writer.writerow([elem_id, f"{depth:.{results['decimal_digits']}f}"])
            
            logger.info(f"Results exported to: {csv_file}")
            
            # Also create a summary
            summary_file = self.data_dir / "calculation_summary.txt"
            with open(summary_file, 'w') as f:
                f.write("SINK MARK DEPTH CALCULATION SUMMARY\n")
                f.write("=" * 40 + "\n\n")
                f.write(f"Total surface elements: {len(results['surface_elements'])}\n")
                f.write(f"Unit system: {results['length_unit']}\n")
                f.write(f"Average sink depth: {np.mean(results['sink_depths']):.{results['decimal_digits']}f} {results['length_unit']}\n")
                f.write(f"Maximum sink depth: {np.max(results['sink_depths']):.{results['decimal_digits']}f} {results['length_unit']}\n")
                f.write(f"Minimum sink depth: {np.min(results['sink_depths']):.{results['decimal_digits']}f} {results['length_unit']}\n")
                f.write(f"\nResults file: {csv_file.name}\n")
                f.write("\nTo import into Synergy:\n")
                f.write("1. Go to Results > User Plots > Import Plot Data\n")
                f.write("2. Select the CSV file\n")
                f.write("3. Map Element_ID to elements and Sink_Depth to values\n")
            
            return True
            
        except Exception as e:
            logger.error(f"Error exporting results: {e}")
            return False
    
    def show_completion_message(self):
        """Show completion message with import instructions."""
        print("\n" + "=" * 70)
        print("🎉 CALCULATION COMPLETED SUCCESSFULLY!")
        print("=" * 70)
        print()
        print("Results have been saved to:")
        print(f"  📊 Data: {self.data_dir}/sink_mark_depths.csv")
        print(f"  📋 Summary: {self.data_dir}/calculation_summary.txt")
        print()
        print("TO IMPORT RESULTS INTO SYNERGY:")
        print("1. In Synergy, go to Results > User Plots")
        print("2. Click 'Import Plot Data...'")
        print("3. Select the sink_mark_depths.csv file")
        print("4. Map columns:")
        print("   - Element_ID → Element IDs")
        print("   - Sink_Depth → Plot Values")
        print("5. Set plot name to 'Surface Sink Mark Depth'")
        print("6. Click OK to create the plot")
        print()
        print("The plot will show sink mark depths on your 3D model!")
    
    def run(self):
        """Main execution method."""
        try:
            # Show instructions
            self.show_instructions()
            
            # Check required files
            if not self.check_required_files():
                return False
            
            # Load data
            node_list, vol_shrink_data = self.load_volumetric_shrinkage_data()
            if not node_list:
                return False
            
            units = self.load_study_info()
            
            # Process and calculate
            results = self.process_mesh_and_calculate(node_list, vol_shrink_data, units)
            if not results:
                return False
            
            # Export results
            if not self.export_results(results):
                return False
            
            # Show completion message
            self.show_completion_message()
            
            return True
            
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return False

def main():
    """Main entry point."""
    calculator = FileBasedSinkMarkCalculator()
    success = calculator.run()
    
    if not success:
        print("\n❌ Calculation failed. Check the log messages above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
