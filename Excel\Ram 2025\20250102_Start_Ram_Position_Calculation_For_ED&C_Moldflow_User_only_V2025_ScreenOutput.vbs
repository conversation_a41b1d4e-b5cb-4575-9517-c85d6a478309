Attribute VB_Name = "ScreenOutput"

Attribute VB_Base = "0{FCFB3D2A-A0FA-1068-A738-08002B3371B5}"

Attribute VB_GlobalNameSpace = False

Attribute VB_Creatable = False

Attribute VB_PredeclaredId = False

Attribute VB_Exposed = False

Attribute VB_TemplateDerived = False

Attribute VB_Customizable = False

Const ForReading = 1

 Private mMessages()         ' Array of Messages associate with the screen output File

 Private mNumMessages       ' Number of messages in the screen output file

 

  Public Function LoadOutputFile(aFile)

  Dim fs

 '   Set fs = Server.CreateObject("Scripting.FileSystemObject")

    

    Set fs = CreateObject("Scripting.FileSystemObject")

    

    Dim File

    Set File = fs.OpenTextFile(aFile, ForReading)

    While Not File.AtEndOfStream

        Dim ID

        ID = -1

        ' Read the MSCD

        Dim line

        line = File.ReadLine

        If Not File.AtEndOfStream Then

            ID = line

            Dim curMessage As Message

            

            Set curMessage = New Message

            curMessage.SetMSCD (ID)

            ' Read the number of strings

            line = File.ReadLine

            If Not File.AtEndOfStream Then

                Dim numString

                numString = line

                ' Read Strings

                Dim i

                For i = 1 To numString

                    line = File.ReadLine

                    If Not File.AtEndOfStream Then

                        curMessage.AddString (line)

                    End If

                Next

                ' Read the number of floats

                line = File.ReadLine

                If Not File.AtEndOfStream Then

                    Dim numFloat

                    numFloat = line

                    ' Read Floats

                    For i = 1 To numFloat

                        line = File.ReadLine

                        If Not File.AtEndOfStream Then

                            curMessage.AddFloat (line)

                        End If

                    Next

                End If

                ' Add current message to the list

                           

                mNumMessages = mNumMessages + 1

                ReDim Preserve mMessages(mNumMessages)

                Set mMessages(mNumMessages - 1) = curMessage

                    

                'AddMessage (curMessage)

            End If

        End If

    Wend

    File.Close

  End Function



  Public Function GetNumMessages()

    GetNumMessages = mNumMessages

  End Function



  Public Function GetMessage(aMSCD, aOccur)

    Set GetMessage = Nothing

    Dim j

    Dim lFindInstance

    lFindInstance = 0

    If aOccur < 0 Then

        lFindInstance = 0

    End If

    Dim count

    count = 0

    For j = 0 To mNumMessages - 1

        'MsgBox mMessages(j).GetMSCD &"|"& aMSCD

        If CStr(mMessages(j).GetMSCD) = CStr(aMSCD) Then

           count = count + 1

           If count >= lFindInstance Then

                Set GetMessage = mMessages(j)

                Exit Function

           End If

        End If

    Next

  End Function

  Private Sub Class_Initialize()

    mNumMessages = 0

  End Sub





