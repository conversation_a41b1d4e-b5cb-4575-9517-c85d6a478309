﻿// Decompiled with Jet<PERSON>rains decompiler
// Type: HDMoldFlowLibrary.clsHDMFLibParams
// Assembly: HDMoldFlowLibrary, Version=2.24.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 16E5D508-B892-4B5D-A02C-2C1930D27867
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMoldFlowLibrary.dll

using System;
using System.Runtime.CompilerServices;

#nullable disable
namespace HDMoldFlowLibrary
{
  public class clsHDMFLibParams : IDisposable
  {
    public object MFHandle;
    public object MFImportOptions;
    public object MFCurve;
    public object MFPlot;
    public object MFUserPlot;
    public object MFVector;
    public object MFVector1;
    public object MFVector2;
    public object MFMeshEditor;
    public object MFMeshGenerator;
    public object MFPropertyEditor;
    public object MFPredicate;
    public object MFPredicateManager;
    public object MFUnits;
    public object MFIntegerArray;
    public object MFNode;
    public object MFCoord;
    public object MFEntList;
    public object MFFieldValues;
    public object MFFolder;
    public object MFProperty;
    public object MFDoubleArray;
    public object MFDoubleArray1;
    public object MFDoubleArray2;
    public object MFDoubleArray3;
    public object MFMeshSummary;
    public object MFViewer;
    public object MFBeam;
    public object MFLayer;
    public object MFMaterial;
    public object MFMaterialData;
    public object MFMaterialFinder;
    public object MFLayerManager;
    public object MFModeler;
    public object MFPlotManager;
    public object MFFolderManager;
    public object MFDiagnosisManager;
    public object MFMaterialSelector;
    public object MFStudy;
    public object MFProject;
    public object MFBoundaryConditions;

    public void Dispose()
    {
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFImportOptions));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFMeshEditor));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFCurve));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFMeshGenerator));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFVector));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFVector1));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFVector2));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFBeam));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFNode));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFCoord));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFEntList));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFDoubleArray));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFDoubleArray1));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFDoubleArray2));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFDoubleArray3));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFIntegerArray));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFUnits));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFFieldValues));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFProperty));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFPropertyEditor));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFPredicate));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFPredicateManager));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFViewer));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFLayer));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFMaterial));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFMaterialData));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFMaterialFinder));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFUserPlot));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFPlot));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFFolder));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFPlotManager));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFMeshSummary));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFLayerManager));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFModeler));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFDiagnosisManager));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFMaterialSelector));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFFolderManager));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFStudy));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFProject));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFHandle));
      clsHDMFLib.ReleaseComObject(RuntimeHelpers.GetObjectValue(this.MFBoundaryConditions));
      GC.SuppressFinalize((object) this);
    }
  }
}
