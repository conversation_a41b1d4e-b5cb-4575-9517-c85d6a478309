﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmMesh
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDMFUserControl;
using HDMoldFlow.Properties;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public class frmMesh : Form
  {
    private IContainer components = (IContainer) null;
    private Label label_SolidCad;
    private Label label_SolidScale;
    private Label label_SolidLength;
    private Label label_SurfaceStl;
    private Label label_SurfaceLength;
    private NewButton newButton_Apply;
    private NewTextBox newTextBox_SolidLength;
    private NewTextBox newTextBox_SolidScale;
    private NewTextBox newTextBox_SurfaceLength;
    private Label label_3D_Mesh;
    private Label label6;
    private NewTextBox newTextBox_TetraLayer;

    public frmMesh()
    {
      this.InitializeComponent();
      this.newButton_Apply.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Apply.Image);
      this.Text = LocaleControl.getInstance().GetString("IDS_CREATE_MESH");
      this.label_SolidCad.Text = LocaleControl.getInstance().GetString("IDS_SOLID_CAD_FILE");
      this.label_SolidLength.Text = LocaleControl.getInstance().GetString("IDS_MESH_RECOMMEND_LENGTH");
      this.label_SolidScale.Text = LocaleControl.getInstance().GetString("IDS_CAD_OPT_SCALE");
      this.label_SurfaceStl.Text = LocaleControl.getInstance().GetString("IDS_SURFACE_STL_FILE");
      this.label_SurfaceLength.Text = LocaleControl.getInstance().GetString("IDS_MESH_RECOMMEND_LENGTH");
      this.label_3D_Mesh.Text = LocaleControl.getInstance().GetString("IDS_SET_3D_MESH");
      this.newButton_Apply.ButtonText = LocaleControl.getInstance().GetString("IDS_APPLY");
    }

    private void frmMesh_Load(object sender, EventArgs e)
    {
      this.ActiveControl = (Control) this.label_SolidLength;
      foreach (KeyValuePair<string, string> keyValuePair in clsDefine.g_dicMesh)
      {
        KeyValuePair<string, string> kvTmp = keyValuePair;
        ((NewTextBox) this.Controls.Cast<Control>().Where<Control>((Func<Control, bool>) (Temp => Temp.Name.Contains(kvTmp.Key))).FirstOrDefault<Control>()).Value = kvTmp.Value;
      }
    }

    private void frmMesh_KeyDown(object sender, KeyEventArgs e)
    {
      if (e.KeyCode != Keys.Escape)
        return;
      this.Close();
    }

    private void newButton_Apply_NewClick(object sender, EventArgs e)
    {
      if (this.newTextBox_SolidLength.Value == "" || this.newTextBox_SolidScale.Value == "" || this.newTextBox_SurfaceLength.Value == "")
        return;
      clsDefine.g_dicMesh["SolidLength"] = this.newTextBox_SolidLength.Value;
      clsDefine.g_dicMesh["SolidScale"] = this.newTextBox_SolidScale.Value;
      clsDefine.g_dicMesh["SurfaceLength"] = this.newTextBox_SurfaceLength.Value;
      clsDefine.g_dicMesh["TetraLayer"] = this.newTextBox_TetraLayer.Value;
      foreach (KeyValuePair<string, string> keyValuePair in clsDefine.g_dicMesh)
      {
        if (keyValuePair.Key.Contains("Solid"))
          clsUtill.WriteINI("Solid", keyValuePair.Key.Replace("Solid", ""), keyValuePair.Value, clsDefine.g_fiMeshCfg.FullName);
        else if (keyValuePair.Key.Contains("Surface"))
          clsUtill.WriteINI("Surface", keyValuePair.Key.Replace("Surface", ""), keyValuePair.Value, clsDefine.g_fiMeshCfg.FullName);
        else
          clsUtill.WriteINI("3D", keyValuePair.Key, keyValuePair.Value, clsDefine.g_fiMeshCfg.FullName);
      }
      this.Close();
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.label_SolidCad = new Label();
      this.label_SolidScale = new Label();
      this.label_SolidLength = new Label();
      this.label_SurfaceStl = new Label();
      this.label_SurfaceLength = new Label();
      this.newButton_Apply = new NewButton();
      this.newTextBox_SolidLength = new NewTextBox();
      this.newTextBox_SolidScale = new NewTextBox();
      this.newTextBox_SurfaceLength = new NewTextBox();
      this.label_3D_Mesh = new Label();
      this.label6 = new Label();
      this.newTextBox_TetraLayer = new NewTextBox();
      this.SuspendLayout();
      this.label_SolidCad.BackColor = Color.FromArgb(229, 238, 248);
      this.label_SolidCad.BorderStyle = BorderStyle.FixedSingle;
      this.label_SolidCad.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_SolidCad.ForeColor = Color.MidnightBlue;
      this.label_SolidCad.Location = new Point(5, 5);
      this.label_SolidCad.Name = "label_SolidCad";
      this.label_SolidCad.Size = new Size(168, 20);
      this.label_SolidCad.TabIndex = 5;
      this.label_SolidCad.Text = "솔리드 CAD 파일";
      this.label_SolidCad.TextAlign = ContentAlignment.MiddleCenter;
      this.label_SolidScale.BackColor = Color.Lavender;
      this.label_SolidScale.BorderStyle = BorderStyle.FixedSingle;
      this.label_SolidScale.Location = new Point(5, 46);
      this.label_SolidScale.Name = "label_SolidScale";
      this.label_SolidScale.Size = new Size(97, 23);
      this.label_SolidScale.TabIndex = 6;
      this.label_SolidScale.Text = "CAD 옵션 Scale";
      this.label_SolidScale.TextAlign = ContentAlignment.MiddleCenter;
      this.label_SolidLength.BackColor = Color.Lavender;
      this.label_SolidLength.BorderStyle = BorderStyle.FixedSingle;
      this.label_SolidLength.Location = new Point(5, 24);
      this.label_SolidLength.Name = "label_SolidLength";
      this.label_SolidLength.Size = new Size(97, 23);
      this.label_SolidLength.TabIndex = 7;
      this.label_SolidLength.Text = "메쉬 추천 길이";
      this.label_SolidLength.TextAlign = ContentAlignment.MiddleCenter;
      this.label_SurfaceStl.BackColor = Color.FromArgb(229, 238, 248);
      this.label_SurfaceStl.BorderStyle = BorderStyle.FixedSingle;
      this.label_SurfaceStl.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_SurfaceStl.ForeColor = Color.MidnightBlue;
      this.label_SurfaceStl.Location = new Point(5, 73);
      this.label_SurfaceStl.Name = "label_SurfaceStl";
      this.label_SurfaceStl.Size = new Size(168, 20);
      this.label_SurfaceStl.TabIndex = 5;
      this.label_SurfaceStl.Text = "서피스 STL 파일";
      this.label_SurfaceStl.TextAlign = ContentAlignment.MiddleCenter;
      this.label_SurfaceLength.BackColor = Color.Lavender;
      this.label_SurfaceLength.BorderStyle = BorderStyle.FixedSingle;
      this.label_SurfaceLength.Location = new Point(5, 92);
      this.label_SurfaceLength.Name = "label_SurfaceLength";
      this.label_SurfaceLength.Size = new Size(97, 23);
      this.label_SurfaceLength.TabIndex = 7;
      this.label_SurfaceLength.Text = "메쉬 추천 길이";
      this.label_SurfaceLength.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.ButtonBackColor = Color.White;
      this.newButton_Apply.ButtonText = "적용";
      this.newButton_Apply.FlatBorderSize = 1;
      this.newButton_Apply.FlatStyle = FlatStyle.Flat;
      this.newButton_Apply.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Apply.Image = (Image) Resources.OK;
      this.newButton_Apply.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Apply.Location = new Point(5, 165);
      this.newButton_Apply.Name = "newButton_Apply";
      this.newButton_Apply.Size = new Size(168, 23);
      this.newButton_Apply.TabIndex = 21;
      this.newButton_Apply.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Apply.NewClick += new EventHandler(this.newButton_Apply_NewClick);
      this.newTextBox_SolidLength.BackColor = SystemColors.Window;
      this.newTextBox_SolidLength.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_SolidLength.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_SolidLength.IsDigit = true;
      this.newTextBox_SolidLength.Lines = new string[0];
      this.newTextBox_SolidLength.Location = new Point(101, 24);
      this.newTextBox_SolidLength.MultiLine = false;
      this.newTextBox_SolidLength.Name = "newTextBox_SolidLength";
      this.newTextBox_SolidLength.ReadOnly = false;
      this.newTextBox_SolidLength.Size = new Size(72, 23);
      this.newTextBox_SolidLength.TabIndex = 22;
      this.newTextBox_SolidLength.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_SolidLength.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_SolidLength.TextForeColor = SystemColors.WindowText;
      this.newTextBox_SolidLength.Value = "";
      this.newTextBox_SolidScale.BackColor = SystemColors.Window;
      this.newTextBox_SolidScale.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_SolidScale.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_SolidScale.IsDigit = true;
      this.newTextBox_SolidScale.Lines = new string[0];
      this.newTextBox_SolidScale.Location = new Point(101, 46);
      this.newTextBox_SolidScale.MultiLine = false;
      this.newTextBox_SolidScale.Name = "newTextBox_SolidScale";
      this.newTextBox_SolidScale.ReadOnly = false;
      this.newTextBox_SolidScale.Size = new Size(72, 23);
      this.newTextBox_SolidScale.TabIndex = 22;
      this.newTextBox_SolidScale.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_SolidScale.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_SolidScale.TextForeColor = SystemColors.WindowText;
      this.newTextBox_SolidScale.Value = "";
      this.newTextBox_SurfaceLength.BackColor = SystemColors.Window;
      this.newTextBox_SurfaceLength.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_SurfaceLength.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_SurfaceLength.IsDigit = true;
      this.newTextBox_SurfaceLength.Lines = new string[0];
      this.newTextBox_SurfaceLength.Location = new Point(101, 92);
      this.newTextBox_SurfaceLength.MultiLine = false;
      this.newTextBox_SurfaceLength.Name = "newTextBox_SurfaceLength";
      this.newTextBox_SurfaceLength.ReadOnly = false;
      this.newTextBox_SurfaceLength.Size = new Size(72, 23);
      this.newTextBox_SurfaceLength.TabIndex = 22;
      this.newTextBox_SurfaceLength.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_SurfaceLength.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_SurfaceLength.TextForeColor = SystemColors.WindowText;
      this.newTextBox_SurfaceLength.Value = "";
      this.label_3D_Mesh.BackColor = Color.FromArgb(229, 238, 248);
      this.label_3D_Mesh.BorderStyle = BorderStyle.FixedSingle;
      this.label_3D_Mesh.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_3D_Mesh.ForeColor = Color.MidnightBlue;
      this.label_3D_Mesh.Location = new Point(5, 119);
      this.label_3D_Mesh.Name = "label_3D_Mesh";
      this.label_3D_Mesh.Size = new Size(168, 20);
      this.label_3D_Mesh.TabIndex = 5;
      this.label_3D_Mesh.Text = "3D 메쉬 설정";
      this.label_3D_Mesh.TextAlign = ContentAlignment.MiddleCenter;
      this.label6.BackColor = Color.Lavender;
      this.label6.BorderStyle = BorderStyle.FixedSingle;
      this.label6.Location = new Point(5, 138);
      this.label6.Name = "label6";
      this.label6.Size = new Size(97, 23);
      this.label6.TabIndex = 7;
      this.label6.Text = "Tetra Layer";
      this.label6.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_TetraLayer.BackColor = SystemColors.Window;
      this.newTextBox_TetraLayer.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_TetraLayer.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_TetraLayer.IsDigit = true;
      this.newTextBox_TetraLayer.Lines = new string[0];
      this.newTextBox_TetraLayer.Location = new Point(101, 138);
      this.newTextBox_TetraLayer.MultiLine = false;
      this.newTextBox_TetraLayer.Name = "newTextBox_TetraLayer";
      this.newTextBox_TetraLayer.ReadOnly = false;
      this.newTextBox_TetraLayer.Size = new Size(72, 23);
      this.newTextBox_TetraLayer.TabIndex = 22;
      this.newTextBox_TetraLayer.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_TetraLayer.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_TetraLayer.TextForeColor = SystemColors.WindowText;
      this.newTextBox_TetraLayer.Value = "";
      this.AutoScaleDimensions = new SizeF(7f, 15f);
      this.AutoScaleMode = AutoScaleMode.Font;
      this.BackColor = Color.White;
      this.ClientSize = new Size(178, 193);
      this.Controls.Add((Control) this.newTextBox_TetraLayer);
      this.Controls.Add((Control) this.newTextBox_SurfaceLength);
      this.Controls.Add((Control) this.newTextBox_SolidScale);
      this.Controls.Add((Control) this.newTextBox_SolidLength);
      this.Controls.Add((Control) this.newButton_Apply);
      this.Controls.Add((Control) this.label_SolidScale);
      this.Controls.Add((Control) this.label6);
      this.Controls.Add((Control) this.label_SurfaceLength);
      this.Controls.Add((Control) this.label_3D_Mesh);
      this.Controls.Add((Control) this.label_SolidLength);
      this.Controls.Add((Control) this.label_SurfaceStl);
      this.Controls.Add((Control) this.label_SolidCad);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.Margin = new Padding(3, 4, 3, 4);
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmMesh);
      this.ShowInTaskbar = false;
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "메쉬 생성";
      this.Load += new EventHandler(this.frmMesh_Load);
      this.KeyDown += new KeyEventHandler(this.frmMesh_KeyDown);
      this.ResumeLayout(false);
    }
  }
}
