#!/usr/bin/env python3
"""
Moldflow Synergy COM API Wrapper

This module provides a Python wrapper for the Moldflow Synergy COM API,
simplifying common operations and providing better error handling.

Author: AI Assistant
Version: 1.0
"""

import os
import logging
from typing import Optional, List, Tuple, Any
import win32com.client
from win32com.client import constants

logger = logging.getLogger(__name__)


class SynergyError(Exception):
    """Custom exception for Synergy-related errors."""
    pass


class SynergyWrapper:
    """
    Python wrapper for Moldflow Synergy COM API.

    This class provides a simplified interface to common Synergy operations
    with improved error handling and Python-friendly data types.
    """

    def __init__(self):
        """Initialize the Synergy wrapper."""
        self.synergy = None
        self.study_doc = None
        self.plot_manager = None
        self.project = None
        self._connected = False

    def connect(self) -> bool:
        """
        Connect to Moldflow Synergy application.

        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            logger.info("Attempting to connect to Moldflow Synergy...")

            # Try multiple connection methods
            connection_methods = [
                self._connect_via_environment,
                self._connect_via_synergy_dispatch,
                self._connect_via_scandium_dispatch,
                self._connect_via_com_object
            ]

            for method in connection_methods:
                try:
                    if method():
                        logger.info(f"Successfully connected using {method.__name__}")
                        break
                except Exception as e:
                    logger.debug(f"{method.__name__} failed: {str(e)}")
                    continue
            else:
                raise SynergyError("All connection methods failed")

            if self.synergy is None:
                raise SynergyError("Failed to create Synergy instance")

            # Get commonly used objects
            try:
                self.study_doc = self.synergy.StudyDoc
                self.plot_manager = self.synergy.PlotManager
                self.project = self.synergy.Project
            except Exception as e:
                logger.warning(f"Could not get all Synergy objects: {str(e)}")
                # Continue anyway, objects will be retrieved when needed

            self._connected = True
            logger.info("Successfully connected to Moldflow Synergy")
            return True

        except Exception as e:
            logger.error(f"Failed to connect to Synergy: {str(e)}")
            return False

    def _connect_via_environment(self) -> bool:
        """Try to connect via SAInstance environment variable."""
        sa_instance = os.environ.get('SAInstance')
        if not sa_instance:
            raise Exception("SAInstance environment variable not set")

        synergy_getter = win32com.client.GetObject(sa_instance)
        self.synergy = synergy_getter.GetSASynergy
        return True

    def _connect_via_synergy_dispatch(self) -> bool:
        """Try to connect via Synergy.Synergy dispatch."""
        self.synergy = win32com.client.Dispatch("Synergy.Synergy")
        return True

    def _connect_via_scandium_dispatch(self) -> bool:
        """Try to connect via Scandium.Synergy dispatch."""
        self.synergy = win32com.client.Dispatch("Scandium.Synergy")
        return True

    def _connect_via_com_object(self) -> bool:
        """Try to connect via COM object lookup."""
        # Try to find existing Synergy instance
        import pythoncom
        try:
            # Get the running object table
            rot = pythoncom.GetRunningObjectTable()
            enum_moniker = rot.EnumRunning()

            for moniker in enum_moniker:
                try:
                    display_name = moniker.GetDisplayName(None, None)
                    if 'synergy' in display_name.lower():
                        obj = rot.GetObject(moniker)
                        self.synergy = obj
                        return True
                except:
                    continue
        except:
            pass

        raise Exception("No running Synergy instance found")

    def _get_build_code(self) -> str:
        """
        Determine the Synergy build code.

        Returns:
            str: Build code ('Scandium' or 'Synergy')
        """
        try:
            import winreg
            # Try to detect build from environment or registry
            mf_buildcode = os.environ.get('MF_BUILDCODE', '')
            if 'scandium' in mf_buildcode.lower():
                return "Scandium"
            else:
                return "Synergy"
        except:
            return "Synergy"  # Default fallback

    def has_open_study(self) -> bool:
        """
        Check if a study is currently open.

        Returns:
            bool: True if study is open, False otherwise
        """
        try:
            return self.study_doc is not None
        except:
            return False

    def is_3d_mesh(self) -> bool:
        """
        Check if the current study has a 3D mesh.

        Returns:
            bool: True if 3D mesh, False otherwise
        """
        try:
            return self.study_doc.MeshType == "3D"
        except:
            return False

    def get_units(self) -> str:
        """
        Get the current unit system.

        Returns:
            str: Unit system ('English' or 'Metric')
        """
        try:
            return self.synergy.GetUnits()
        except Exception as e:
            logger.error(f"Failed to get units: {str(e)}")
            return "Metric"  # Default fallback

    def has_result(self, result_id: int) -> bool:
        """
        Check if a specific result is available.

        Args:
            result_id: Result ID to check

        Returns:
            bool: True if result exists, False otherwise
        """
        try:
            indp_values = self.synergy.CreateDoubleArray()
            return self.plot_manager.GetIndpValues(result_id, indp_values)
        except:
            return False

    def get_scalar_data(self, result_id: int) -> Tuple[List[int], List[float]]:
        """
        Get scalar data for a specific result.

        Args:
            result_id: Result ID to retrieve

        Returns:
            Tuple of (node_list, values)
        """
        try:
            # Get independent values (time steps)
            indp_values = self.synergy.CreateDoubleArray()
            if not self.plot_manager.GetIndpValues(result_id, indp_values):
                raise SynergyError(f"Failed to get independent values for result {result_id}")

            # Use the last time step
            indp = self.synergy.CreateDoubleArray()
            indp.AddDouble(indp_values.Val(indp_values.Size - 1))

            # Get scalar data
            entity_list = self.synergy.CreateIntegerArray()
            node_result = self.synergy.CreateDoubleArray()

            if not self.plot_manager.GetScalarData(result_id, indp, entity_list, node_result):
                raise SynergyError(f"Failed to get scalar data for result {result_id}")

            # Convert to Python lists
            node_list = entity_list.ToVBSArray()
            values = node_result.ToVBSArray()

            return list(node_list), list(values)

        except Exception as e:
            logger.error(f"Failed to get scalar data: {str(e)}")
            raise SynergyError(f"Failed to get scalar data: {str(e)}")

    def export_model(self, file_path: str) -> bool:
        """
        Export the current model to a file.

        Args:
            file_path: Path where to export the model

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.project.ExportModel(file_path)
            return True
        except Exception as e:
            logger.error(f"Failed to export model: {str(e)}")
            return False

    def delete_plot_by_name(self, plot_name: str) -> bool:
        """
        Delete a plot by name.

        Args:
            plot_name: Name of the plot to delete

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.plot_manager.DeletePlotByName(plot_name)
            return True
        except Exception as e:
            logger.warning(f"Could not delete plot '{plot_name}': {str(e)}")
            return False

    def create_user_plot(self, plot_name: str, data_type: str, unit_name: str,
                        element_ids: List[int], values: List[float]) -> bool:
        """
        Create a user plot with the given data.

        Args:
            plot_name: Name for the new plot
            data_type: Data type (e.g., "ELDT" for element data)
            unit_name: Unit name for the data
            element_ids: List of element IDs
            values: List of values corresponding to elements

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Create Synergy arrays
            surf_list = self.synergy.CreateIntegerArray()
            sink_depth = self.synergy.CreateDoubleArray()

            # Populate arrays
            for elem_id in element_ids:
                surf_list.AddInteger(elem_id)

            for value in values:
                sink_depth.AddDouble(value)

            # Create user plot
            user_plot = self.plot_manager.CreateUserPlot()
            user_plot.SetDataType(data_type)
            user_plot.SetDeptUnitName(unit_name)
            user_plot.SetName(plot_name)
            user_plot.SetScalarData(surf_list, sink_depth)
            user_plot.Build()

            logger.info(f"Created user plot: {plot_name}")
            return True

        except Exception as e:
            logger.error(f"Failed to create user plot: {str(e)}")
            return False

    def create_double_array(self):
        """Create a Synergy double array."""
        return self.synergy.CreateDoubleArray()

    def create_integer_array(self):
        """Create a Synergy integer array."""
        return self.synergy.CreateIntegerArray()

    def is_connected(self) -> bool:
        """Check if connected to Synergy."""
        return self._connected

    def disconnect(self):
        """Disconnect from Synergy."""
        self.synergy = None
        self.study_doc = None
        self.plot_manager = None
        self.project = None
        self._connected = False
        logger.info("Disconnected from Moldflow Synergy")
