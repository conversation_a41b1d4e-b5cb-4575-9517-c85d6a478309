﻿// Decompiled with JetBrains decompiler
// Type: HDMFAI.clsAISolution
// Assembly: HDMFAI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: A66A47CE-D63B-4ACB-B9A4-A4F118252C4E
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFAI.dll

using HDLog4Net;
using HDMoldFlowLibrary;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Xml;

namespace HDMFAI
{
  public class clsAISolution
  {
    public static string ReceiveAIResult(
      DataRow p_drStudy,
      DirectoryInfo p_diProjectPath,
      ref string p_strResult)
    {
      string empty1 = string.Empty;
      string p_strReceiveURL = string.Empty;
      string empty2 = string.Empty;
      string p_strMsg = string.Empty;
      JObject p_joAIData = (JObject) null;
      try
      {
        if (clsAIDefine.g_fiAICfg != null)
          p_strReceiveURL = clsUtill.ReadINI("Sub", "AISolution", clsAIDefine.g_fiAICfg.FullName);
        string p_strStudy = clsUtill.ReadINI("Input", "Study", clsAIDefine.g_diTmpAI.ToString() + "\\" + p_drStudy["Name"].ToString() + "\\Input.ini");
        if (string.IsNullOrEmpty(p_strReceiveURL))
          p_strReceiveURL = "/api/output";
        p_strMsg = clsWeb.ReceiveDataFromWeb(p_strReceiveURL, p_strStudy, ref p_joAIData);
        if (p_joAIData != null)
        {
          string strSequence = p_drStudy["Sequence"].ToString();
          p_strResult = clsAISolution.ParsingAIData(p_joAIData, p_diProjectPath.FullName, ref p_strMsg, ref strSequence);
          p_drStudy["Sequence"] = (object) strSequence;
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAISolution]ReceiveAIResult):" + ex.Message));
      }
      return p_strMsg;
    }

    private static string ParsingAIData(
      JObject p_joAIData,
      string p_strProjectPath,
      ref string p_strMsg,
      ref string strSequence)
    {
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      string empty3 = string.Empty;
      string str1 = string.Empty;
      string str2 = string.Empty;
      StringBuilder stringBuilder = new StringBuilder();
      Dictionary<string, string> dictionary = new Dictionary<string, string>();
      JArray jarray1 = new JArray();
      JObject jobject1 = new JObject();
      try
      {
        string str3 = p_joAIData["Study"].ToString().Split('/')[0];
        dictionary.Add("Study", str3);
        string p_strIniPath = clsAIDefine.g_diTmpAI.ToString() + "\\" + str3 + "\\Input.ini";
        string str4 = clsUtill.ReadINI("Input", "Mat_MaterialID", p_strIniPath);
        dictionary.Add("MaterialID", str4);
        string str5 = clsUtill.ReadINI("Input", "Mat_Company", p_strIniPath);
        dictionary.Add("Material_Company", str5);
        string str6 = clsUtill.ReadINI("Input", "Mat_Trade_Name", p_strIniPath);
        dictionary.Add("Trade_Name", str6);
        string str7 = clsUtill.ReadINI("Input", "Mat_Type", p_strIniPath);
        dictionary.Add("FamilyAbb", str7);
        string str8 = clsUtill.ReadINI("Input", "Mat_Density", p_strIniPath);
        dictionary.Add("Density", str8);
        string str9 = clsUtill.ReadINI("Input", "Analysis_Type", p_strIniPath);
        strSequence = str9.Replace("_", "|");
        dictionary.Add("Analysis_Type", str9);
        if (clsAIDefine.g_fiAICfg != null)
          str2 = clsUtill.ReadINI("Main", "URL", clsAIDefine.g_fiAICfg.FullName);
        int num1 = 1;
        DirectoryInfo directoryInfo;
        while (true)
        {
          directoryInfo = new DirectoryInfo(p_strProjectPath + "_Export\\AI Result " + DateTime.Now.ToString("yy-MM-dd") + "_" + (object) num1);
          if (directoryInfo.Exists)
            ++num1;
          else
            break;
        }
        if (!directoryInfo.Exists)
          directoryInfo.Create();
        foreach (JToken jtoken in (JArray) p_joAIData.SelectToken("Attachment"))
        {
          string str10 = jtoken[(object) "Url"].ToString();
          string str11 = jtoken[(object) "Filename"].ToString();
          new WebClient().DownloadFile(str2 + str10, directoryInfo.FullName + "\\" + str11);
        }
        for (int index = 0; index < 2; ++index)
        {
          JObject jobject2 = (JObject) p_joAIData.SelectToken("result.Option" + (object) (index + 1) + ".Optimized_Input");
          if (jobject2 != null)
          {
            if (jobject2.GetValue("Mesh_Type") != null)
            {
              string str12 = jobject2.GetValue("Mesh_Type").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Mesh_Type", str12);
            }
            if (jobject2.GetValue("Filling_Control_Injection_Time") != null)
            {
              string str13 = jobject2.GetValue("Filling_Control_Injection_Time").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Filling_Control_Injection_Time", str13);
            }
            if (jobject2.GetValue("Melt_Temperature") != null)
            {
              string str14 = jobject2.GetValue("Melt_Temperature").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Melt_Temp", str14);
            }
            if (jobject2.GetValue("Mold_Temperature") != null)
            {
              string str15 = jobject2.GetValue("Mold_Temperature").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Mold_Temp", str15);
            }
            if (jobject2.GetValue("Velocity_Pressure_Switch_Over") != null)
            {
              string str16 = jobject2.GetValue("Velocity_Pressure_Switch_Over").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_VP_Switch_Over", str16);
            }
            if (jobject2.GetValue("Cooling_Time") != null)
            {
              string str17 = jobject2.GetValue("Cooling_Time").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Cooling_time", str17);
            }
            if (jobject2.GetValue("Circuit_Coolant_Temp_Core_Min") != null)
            {
              string str18 = jobject2.GetValue("Circuit_Coolant_Temp_Core_Min").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Circuit_Coolant_Temp_Core_Min", str18);
            }
            if (jobject2.GetValue("Part_Volume") != null)
            {
              string str19 = jobject2.GetValue("Part_Volume").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Part_Volume", str19);
            }
            if (jobject2.GetValue("Thickness") != null)
            {
              string str20 = jobject2.GetValue("Thickness").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Thickness", str20);
            }
            if (jobject2.GetValue("Thickness_Percent") != null)
            {
              string str21 = jobject2.GetValue("Thickness_Percent").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Thickness_Percent", str21);
            }
            JArray jarray2 = (JArray) jobject2.GetValue("Gates");
            if (jarray2 != null)
            {
              stringBuilder.Clear();
              string p_strValue1 = clsUtill.ReadINI("Input", "Data_Center_X", p_strIniPath);
              string p_strValue2 = clsUtill.ReadINI("Input", "Data_Center_Y", p_strIniPath);
              string p_strValue3 = clsUtill.ReadINI("Input", "Data_Center_Z", p_strIniPath);
              foreach (JToken jtoken in jarray2)
              {
                if (stringBuilder.Length != 0)
                  stringBuilder.Append("/");
                double num2 = clsUtill.ConvertToDouble(jtoken[(object) "X"].ToString()) + clsUtill.ConvertToDouble(p_strValue1);
                stringBuilder.Append(num2.ToString());
                stringBuilder.Append(",");
                double num3 = clsUtill.ConvertToDouble(jtoken[(object) "Y"].ToString()) + clsUtill.ConvertToDouble(p_strValue2);
                stringBuilder.Append(num3.ToString());
                stringBuilder.Append(",");
                double num4 = clsUtill.ConvertToDouble(jtoken[(object) "Z"].ToString()) + clsUtill.ConvertToDouble(p_strValue3);
                stringBuilder.Append(num4.ToString());
                stringBuilder.Append(",");
                stringBuilder.Append(jtoken[(object) "Valve_Open"].ToString());
                stringBuilder.Append(",");
                stringBuilder.Append(jtoken[(object) "Valve_Close"].ToString());
              }
              dictionary.Add("Option" + (object) (index + 1) + "_Gate", stringBuilder.ToString());
            }
            if (jobject2.GetValue("Number_Gate") != null)
            {
              string str22 = jobject2.GetValue("Number_Gate").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Gate_Number", str22);
            }
            JArray jarray3 = (JArray) jobject2.GetValue("Packings");
            if (jarray3 != null)
            {
              stringBuilder.Clear();
              foreach (JToken jtoken in jarray3)
              {
                if (stringBuilder.Length != 0)
                  stringBuilder.Append("/");
                stringBuilder.Append(jtoken[(object) "Packing_Time"].ToString());
                stringBuilder.Append(":");
                stringBuilder.Append(jtoken[(object) "Packing_Pressure"].ToString());
              }
              dictionary.Add("Option" + (object) (index + 1) + "_Packing_Time_Pressure", stringBuilder.ToString());
            }
            JObject jobject3 = (JObject) p_joAIData.SelectToken("result.Option" + (object) (index + 1) + ".Predicted_Output");
            if (jobject3.GetValue("Range_Deflection") != null)
            {
              string str23 = jobject3.GetValue("Range_Deflection").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Range_Deflection", str23);
            }
            if (jobject3.GetValue("Cycle_Time") != null)
            {
              string str24 = jobject3.GetValue("Cycle_Time").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Cycle_Time", str24);
            }
            if (jobject3.GetValue("Injection_Pressure") != null)
            {
              string str25 = jobject3.GetValue("Injection_Pressure").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Injection_Pressure", str25);
            }
            if (jobject3.GetValue("Sink_Mark_Depth_Max") != null)
            {
              string str26 = jobject3.GetValue("Sink_Mark_Depth_Max").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Sink_Mark", str26);
            }
            if (jobject3.GetValue("Filled_Time") != null)
            {
              string str27 = jobject3.GetValue("Filled_Time").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Filled_Time", str27);
            }
            if (jobject3.GetValue("Time_To_Reach_Ejection_Temp_Cool_Max") != null)
            {
              string str28 = jobject3.GetValue("Time_To_Reach_Ejection_Temp_Cool_Max").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Time_To_Reach_Ejection_Temp_Cool_Max", str28);
            }
            if (jobject3.GetValue("Cavity_Surface_Temp_Average") != null)
            {
              string str29 = jobject3.GetValue("Cavity_Surface_Temp_Average").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Cavity_Surface_Temp_Average", str29);
            }
            if (jobject3.GetValue("Temp_At_Flow_Front_Min") != null)
            {
              string str30 = jobject3.GetValue("Temp_At_Flow_Front_Min").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Temperature_At_Flow_Front_Min", str30);
            }
            if (jobject3.GetValue("Temp_At_Flow_Front_Max") != null)
            {
              string str31 = jobject3.GetValue("Temp_At_Flow_Front_Max").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Temperature_At_Flow_Front_Max", str31);
            }
            if (jobject3.GetValue("Volumetric_Shrinkage_At_Ejection_Min") != null)
            {
              string str32 = jobject3.GetValue("Volumetric_Shrinkage_At_Ejection_Min").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Volumetric_Shrinkage_At_Ejection_Min", str32);
            }
            if (jobject3.GetValue("Volumetric_Shrinkage_At_Ejection_Max") != null)
            {
              string str33 = jobject3.GetValue("Volumetric_Shrinkage_At_Ejection_Max").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Volumetric_Shrinkage_At_Ejection_Max", str33);
            }
            if (jobject3.GetValue("Range_X_Component_Min") != null)
            {
              string str34 = jobject3.GetValue("Range_X_Component_Min").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Range_X_Component_Min", str34);
            }
            if (jobject3.GetValue("Range_X_Component_Max") != null)
            {
              string str35 = jobject3.GetValue("Range_X_Component_Max").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Range_X_Component_Max", str35);
            }
            if (jobject3.GetValue("Range_Y_Component_Min") != null)
            {
              string str36 = jobject3.GetValue("Range_Y_Component_Min").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Range_Y_Component_Min", str36);
            }
            if (jobject3.GetValue("Range_Y_Component_Max") != null)
            {
              string str37 = jobject3.GetValue("Range_Y_Component_Max").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Range_Y_Component_Max", str37);
            }
            if (jobject3.GetValue("Range_Z_Component_Min") != null)
            {
              string str38 = jobject3.GetValue("Range_Z_Component_Min").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Range_Z_Component_Min", str38);
            }
            if (jobject3.GetValue("Range_Z_Component_Max") != null)
            {
              string str39 = jobject3.GetValue("Range_Z_Component_Max").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Range_Z_Component_Max", str39);
            }
            if (jobject3.GetValue("Projected_Area") != null)
            {
              string str40 = jobject3.GetValue("Projected_Area").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Projected_Area", str40);
            }
            if (jobject3.GetValue("Total_Mass") != null)
            {
              string str41 = jobject3.GetValue("Total_Mass").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Total_Mass", str41);
            }
            if (jobject3.GetValue("Clamp_Force") != null)
            {
              string str42 = jobject3.GetValue("Clamp_Force").ToString();
              dictionary.Add("Option" + (object) (index + 1) + "_Clamp_Force", str42);
            }
          }
        }
        str1 = JsonConvert.SerializeObject((object) dictionary);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAISolution]ParsingAIData):" + ex.Message));
        p_strMsg = p_strMsg + Environment.NewLine + "Exception([clsAISolution]ParsingAIData): " + ex.Message;
      }
      return str1;
    }

    public static void CreateAICase(
      DataRow p_drStudy,
      string p_strAIStudy,
      DataTable p_dtProcessDB,
      DataSet p_dsMaterial,
      string p_strProcessPath,
      ref string p_strSequence)
    {
      string empty = string.Empty;
      string strTmp = string.Empty;
      List<string[]> p_lst_arr_strData = new List<string[]>();
      Dictionary<string, string> dicAIData = new Dictionary<string, string>();
      string str1 = p_drStudy["Mesh"].ToString();
      try
      {
        dicAIData = clsUtill.GetINIDataFromSection(clsAIDefine.g_diTmpAI.ToString() + "\\" + p_drStudy["Name"] + "\\AIResult.ini", "AIData");
        clsAISolution.SaveAIDB(dicAIData, p_dtProcessDB, p_drStudy, p_strProcessPath);
        if (!clsHDMFLib.ExistFolder(p_drStudy.Table.TableName + "_" + str1))
          clsHDMFLib.CreateFolder(p_drStudy.Table.TableName, p_drStudy.Table.TableName + "_" + str1);
        clsHDMFLib.CopyStudy(p_drStudy["Name"].ToString(), p_strAIStudy, p_drStudy.Table.TableName + "_" + str1, true, false);
        clsHDMFLib.SelectSuji(p_dsMaterial.Tables["System"].AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Manufacturer"].ToString() == dicAIData["Material_Company"] && Temp["TradeName"].ToString() == dicAIData["Trade_Name"] && Temp["ID"].ToString() == dicAIData["MaterialID"])).FirstOrDefault<DataRow>(), true);
        p_strSequence = dicAIData["Sequence"];
        clsHDMFLib.SetAnalysisSequence(p_strSequence);
        clsHDMFLib.SetISOlateWarpage();
        clsHDMFLib.SetFillingControlDataForCase("0", clsUtill.ConvertToDouble(dicAIData["Fill_Time"]));
        clsHDMFLib.SetVPSwitchDataForCase("2", clsUtill.ConvertToDouble(dicAIData["VP_Switch_Over"]));
        strTmp = dicAIData["Cooling_Time"];
        string[] strArray1 = strTmp.Split('|');
        if (strArray1.Length > 1)
        {
          double p_dblValue = clsUtill.ConvertToDouble(strArray1[1]);
          clsHDMFLib.SetCoolingControlDataForCase(strArray1[0].ToLower(), p_dblValue);
        }
        clsHDMFLib.SetMeltTemperatureDataToProcessSet(clsUtill.ConvertToDouble(dicAIData["Melt_Temperature"]));
        clsHDMFLib.SetMoldTemperatureDataToProcessSet(clsUtill.ConvertToDouble(dicAIData["Mold_Temperature"]));
        p_lst_arr_strData.Clear();
        string str2 = dicAIData["Packing_Time_Pressure"];
        char[] chArray1 = new char[1]{ '/' };
        foreach (string str3 in str2.Split(chArray1))
        {
          strTmp = str3;
          string[] strArray2 = strTmp.Split(':');
          if (strArray2[0] == "")
            strArray2[0] = "0";
          if (strArray2[1] == "")
            strArray2[1] = "0";
          p_lst_arr_strData.Add(new string[2]
          {
            "0",
            strArray2[1]
          });
          p_lst_arr_strData.Add(new string[2]
          {
            strArray2[0],
            strArray2[1]
          });
        }
        if (p_lst_arr_strData.Count > 0)
          clsHDMFLib.SetPackHoldingControlDataForCase(p_lst_arr_strData, 1);
        clsHDMFLib.AssignLayer(clsHDMFLib.GetNodesOfLayers("Prohibit Nodes"), "Nodes", "Mesh Nodes");
        string str4 = dicAIData["Gate"];
        clsHDMFLib.GetAllNodes();
        char[] chArray2 = new char[1]{ '/' };
        string[] source = str4.Split(chArray2);
        clsHDMFLib.MergeNodes(((IEnumerable<string>) source).ToList<string>());
        Dictionary<string, string> allNodes = clsHDMFLib.GetAllNodes();
        StringBuilder stringBuilder = new StringBuilder();
        KeyValuePair<string, string> keyValuePair1 = new KeyValuePair<string, string>();
        for (int index = 0; index < source.Length; ++index)
        {
          string[] strArray3 = source[index].Split(',');
          strTmp = strArray3[0] + "," + strArray3[1] + "," + strArray3[2];
          KeyValuePair<string, string> keyValuePair2 = allNodes.Where<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Value == strTmp)).FirstOrDefault<KeyValuePair<string, string>>();
          if (!string.IsNullOrEmpty(keyValuePair2.Key))
            stringBuilder.Append(keyValuePair2.Key + " ");
        }
        clsHDMFLib.AssignLayer(stringBuilder.ToString(), "Gate Nodes", "Mesh Nodes");
        clsHDMFLib.ChangeColorOfLayer("Gate Nodes", 0);
        clsHDMFLib.ExpandLayer("Gate Nodes");
        clsHDMFLib.SaveStudy();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAISolution]CreateAICase):" + ex.Message));
      }
    }

    private static void SaveAIDB(
      Dictionary<string, string> p_dicAIResult,
      DataTable p_dtProcessDB,
      DataRow p_drStudy,
      string p_strPath)
    {
      string empty = string.Empty;
      string strName = string.Empty;
      string strItem = string.Empty;
      DataRow p_drProcessDB = (DataRow) null;
      try
      {
        strName = p_drStudy["Name"].ToString().Split('_')[0];
        strItem = p_drStudy["Name"].ToString().Split('_')[1];
        DataRow[] array = p_dtProcessDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp =>
        {
          if (!(Temp["Name"].ToString().Split('_')[0] == strName))
          {
            if (!(Temp["Name"].ToString().Split('_')[0].ToLower() == "basic"))
              return false;
          }
          return Temp["Name"].ToString().Split('_')[1] == strItem;
        })).ToArray<DataRow>();
        if (array.Length != 0)
        {
          p_drProcessDB = ((IEnumerable<DataRow>) array).Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString().Contains("_Default"))).FirstOrDefault<DataRow>() ?? array[0];
          strName = p_drProcessDB["Company"].ToString();
          strItem = p_drProcessDB["Item"].ToString();
        }
        else
          p_drProcessDB = p_dtProcessDB.Rows.Add();
        if (p_drProcessDB == null)
          return;
        p_drProcessDB["Name"] = (object) (strName + "_" + strItem + "_AI");
        p_drProcessDB["Company"] = (object) strName;
        p_drProcessDB["Item"] = (object) strItem;
        p_drProcessDB["Option"] = (object) "AI";
        p_drProcessDB["Mat_UDB"] = (object) string.Empty;
        p_drProcessDB["Mat_Manufacturer"] = (object) p_dicAIResult["Material_Company"];
        p_drProcessDB["Mat_TradeName"] = (object) p_dicAIResult["Trade_Name"];
        p_drProcessDB["Mat_Familyabbreviation"] = (object) p_dicAIResult["FamilyAbb"];
        p_drProcessDB["Mat_MaterialID"] = (object) p_dicAIResult["MaterialID"];
        p_drProcessDB["Mat_Sequence"] = (object) p_dicAIResult["Sequence"];
        string[] strArray1 = p_dicAIResult["Cooling_Time"].Split('|');
        if (strArray1.Length > 1)
          p_drProcessDB["Mat_CTime"] = (object) strArray1[1];
        p_drProcessDB["Mat_MeltTemp"] = (object) p_dicAIResult["Melt_Temperature"];
        p_drProcessDB["Mat_MoldTemp"] = (object) p_dicAIResult["Mold_Temperature"];
        p_drProcessDB["FC_Type"] = (object) "Injection time";
        p_drProcessDB["FC1_1"] = (object) p_dicAIResult["Fill_Time"];
        for (int index = 0; index < 14; ++index)
        {
          string str = index != 0 ? string.Empty : p_dicAIResult["Fill_Time"];
          p_drProcessDB["FC1_" + (object) (index + 1)] = (object) str;
        }
        for (int index = 0; index < 14; ++index)
          p_drProcessDB["FC2_" + (object) (index + 1)] = (object) string.Empty;
        p_drProcessDB["FC3"] = (object) string.Empty;
        p_drProcessDB["FC4"] = (object) string.Empty;
        p_drProcessDB["VP_Type"] = (object) "By %volume filled";
        p_drProcessDB["VP_Value"] = (object) p_dicAIResult["VP_Switch_Over"];
        p_drProcessDB["PHC_Type"] = (object) "%Filling pressure vs time";
        string[] strArray2 = p_dicAIResult["Packing_Time_Pressure"].Split('/');
        for (int index = 0; index < strArray2.Length; ++index)
        {
          string[] strArray3 = strArray2[index].Split(':');
          p_drProcessDB["PHC1_" + (object) (index + 1)] = (object) strArray3[0];
          p_drProcessDB["PHC2_" + (object) (index + 1)] = (object) strArray3[1];
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAISolution]SaveAIDB):" + ex.Message));
      }
      clsAISolution.CreateDBFile(p_drProcessDB, p_strPath);
    }

    private static void CreateDBFile(DataRow p_drProcessDB, string p_strPath)
    {
      try
      {
        FileInfo fileInfo = new FileInfo(p_strPath + "\\" + p_drProcessDB["Name"].ToString() + ".xml");
        if (!fileInfo.Directory.Exists)
          fileInfo.Directory.Create();
        using (XmlWriter xmlWriter = XmlWriter.Create(fileInfo.FullName, new XmlWriterSettings()
        {
          Indent = true,
          OmitXmlDeclaration = true,
          NewLineOnAttributes = true
        }))
        {
          xmlWriter.WriteStartDocument();
          xmlWriter.WriteStartElement("DB");
          xmlWriter.WriteEndElement();
          xmlWriter.Flush();
        }
        using (XmlReader reader = XmlReader.Create(fileInfo.FullName))
        {
          XmlDocument xmlDocument = new XmlDocument();
          xmlDocument.Load(reader);
          XmlElement documentElement = xmlDocument.DocumentElement;
          documentElement.RemoveAll();
          XmlNode element1 = (XmlNode) xmlDocument.CreateElement("Material");
          XmlNode element2 = (XmlNode) xmlDocument.CreateElement("UDB");
          element2.InnerText = p_drProcessDB["Mat_UDB"].ToString();
          element1.AppendChild(element2);
          XmlNode element3 = (XmlNode) xmlDocument.CreateElement("Manufacturer");
          element3.InnerText = p_drProcessDB["Mat_Manufacturer"].ToString();
          element1.AppendChild(element3);
          XmlNode element4 = (XmlNode) xmlDocument.CreateElement("TradeName");
          element4.InnerText = p_drProcessDB["Mat_TradeName"].ToString();
          element1.AppendChild(element4);
          XmlNode element5 = (XmlNode) xmlDocument.CreateElement("FamilyAbbreviation");
          element5.InnerText = p_drProcessDB["Mat_FamilyAbbreviation"].ToString();
          element1.AppendChild(element5);
          XmlNode element6 = (XmlNode) xmlDocument.CreateElement("MaterialID");
          element6.InnerText = p_drProcessDB["Mat_MaterialID"].ToString();
          element1.AppendChild(element6);
          XmlNode element7 = (XmlNode) xmlDocument.CreateElement("Sequence");
          element7.InnerText = p_drProcessDB["Mat_Sequence"].ToString();
          element1.AppendChild(element7);
          XmlNode element8 = (XmlNode) xmlDocument.CreateElement("CoolingTime");
          element8.InnerText = p_drProcessDB["Mat_CTime"].ToString();
          element1.AppendChild(element8);
          XmlNode element9 = (XmlNode) xmlDocument.CreateElement("MeltTemperature");
          element9.InnerText = p_drProcessDB["Mat_MeltTemp"].ToString();
          element1.AppendChild(element9);
          XmlNode element10 = (XmlNode) xmlDocument.CreateElement("MoldTemperature");
          element10.InnerText = p_drProcessDB["Mat_MoldTemp"].ToString();
          element1.AppendChild(element10);
          documentElement.AppendChild(element1);
          XmlNode element11 = (XmlNode) xmlDocument.CreateElement("FillingControl");
          XmlNode element12 = (XmlNode) xmlDocument.CreateElement("Type");
          element12.InnerText = p_drProcessDB["FC_Type"].ToString();
          element11.AppendChild(element12);
          XmlNode element13 = (XmlNode) xmlDocument.CreateElement("FC1");
          for (int index = 0; index < 14; ++index)
          {
            XmlAttribute attribute = xmlDocument.CreateAttribute("V" + (object) (index + 1));
            attribute.Value = p_drProcessDB["FC1_" + (object) (index + 1)].ToString();
            element13.Attributes.Append(attribute);
          }
          element11.AppendChild(element13);
          XmlNode element14 = (XmlNode) xmlDocument.CreateElement("FC2");
          for (int index = 0; index < 14; ++index)
          {
            XmlAttribute attribute = xmlDocument.CreateAttribute("V" + (object) (index + 1));
            attribute.Value = p_drProcessDB["FC2_" + (object) (index + 1)].ToString();
            element14.Attributes.Append(attribute);
          }
          element11.AppendChild(element14);
          XmlNode element15 = (XmlNode) xmlDocument.CreateElement("FC3");
          XmlAttribute attribute1 = xmlDocument.CreateAttribute("V1");
          attribute1.Value = p_drProcessDB["FC3"].ToString();
          element15.Attributes.Append(attribute1);
          element11.AppendChild(element15);
          XmlNode element16 = (XmlNode) xmlDocument.CreateElement("FC4");
          XmlAttribute attribute2 = xmlDocument.CreateAttribute("V1");
          attribute2.Value = p_drProcessDB["FC4"].ToString();
          element16.Attributes.Append(attribute2);
          element11.AppendChild(element16);
          documentElement.AppendChild(element11);
          XmlNode element17 = (XmlNode) xmlDocument.CreateElement("VPSwitchOver");
          XmlNode element18 = (XmlNode) xmlDocument.CreateElement("Type");
          element18.InnerText = p_drProcessDB["VP_Type"].ToString();
          element17.AppendChild(element18);
          XmlNode element19 = (XmlNode) xmlDocument.CreateElement("Value");
          element19.InnerText = p_drProcessDB["VP_Value"].ToString();
          element17.AppendChild(element19);
          documentElement.AppendChild(element17);
          XmlNode element20 = (XmlNode) xmlDocument.CreateElement("PackHoldingControl");
          XmlNode element21 = (XmlNode) xmlDocument.CreateElement("Type");
          element21.InnerText = p_drProcessDB["PHC_Type"].ToString();
          element20.AppendChild(element21);
          XmlNode element22 = (XmlNode) xmlDocument.CreateElement("PHC1");
          for (int index = 0; index < 3; ++index)
          {
            XmlAttribute attribute3 = xmlDocument.CreateAttribute("V" + (object) (index + 1));
            attribute3.Value = p_drProcessDB["PHC1_" + (object) (index + 1)].ToString();
            element22.Attributes.Append(attribute3);
          }
          element20.AppendChild(element22);
          XmlNode element23 = (XmlNode) xmlDocument.CreateElement("PHC2");
          for (int index = 0; index < 3; ++index)
          {
            XmlAttribute attribute4 = xmlDocument.CreateAttribute("V" + (object) (index + 1));
            attribute4.Value = p_drProcessDB["PHC2_" + (object) (index + 1)].ToString();
            element23.Attributes.Append(attribute4);
          }
          element20.AppendChild(element23);
          documentElement.AppendChild(element20);
          reader.Close();
          xmlDocument.Save(fileInfo.FullName);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAISolution]CreateDBFile):" + ex.Message));
      }
    }
  }
}
