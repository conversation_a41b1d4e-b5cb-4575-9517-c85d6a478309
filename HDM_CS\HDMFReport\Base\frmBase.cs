﻿// Decompiled with JetBrains decompiler
// Type: HDMFReport.frmBase
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7*********
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using HDMoldFlowLibrary;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Windows.Forms;

namespace HDMFReport
{
  internal class frmBase : Form
  {
    protected const int WS_EX_COMPOSITED = 33554432;
    public DataRow m_drStudy;
    public DataTable m_dtReportView;
    public Dictionary<string, string> m_dicReport;
    public clsHDMFLibDefine.Company m_enumCompany = clsHDMFLibDefine.Company.HDSolutions;
    private IContainer components;

    public frmBase() => this.InitializeComponent();

    private void frmBase_KeyDown(object sender, KeyEventArgs e)
    {
      if (e.KeyCode != Keys.Escape)
        return;
      this.Close();
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.SuspendLayout();
      this.AutoScaleDimensions = new SizeF(7f, 12f);
      this.AutoScaleMode = AutoScaleMode.Font;
      this.ClientSize = new Size(270, 88);
      this.Name = "frmReport_Base";
      this.Text = "frmReport_Base";
      this.KeyDown += new KeyEventHandler(this.frmBase_KeyDown);
      this.ResumeLayout(false);
    }
  }
}
