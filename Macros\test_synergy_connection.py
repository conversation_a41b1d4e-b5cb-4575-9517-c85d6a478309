#!/usr/bin/env python3
"""
Test script to verify Synergy connection and start Moldflow if needed.

This script will:
1. Try to connect to an existing Synergy instance
2. If no instance exists, try to start Moldflow Synergy
3. Test basic functionality

Author: AI Assistant
Version: 1.0
"""

import os
import sys
import time
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

try:
    import win32com.client
    from moldflow_synergy_wrapper import SynergyWrapper, SynergyError
except ImportError as e:
    print(f"Required dependency missing: {e}")
    print("Please install: pip install pywin32")
    sys.exit(1)


def find_moldflow_installation():
    """Find Moldflow Synergy installation path."""
    possible_paths = [
        r"C:\Program Files\Autodesk\Moldflow Insight 2023\bin\Synergy.exe",
        r"C:\Program Files\Autodesk\Moldflow Insight 2024\bin\Synergy.exe",
        r"C:\Program Files\Autodesk\Moldflow Insight 2022\bin\Synergy.exe",
        r"C:\Program Files\Autodesk\Moldflow Insight 2025\bin\Synergy.exe",
        r"C:\Program Files (x86)\Autodesk\Moldflow Insight 2023\bin\Synergy.exe",
        r"C:\Program Files (x86)\Autodesk\Moldflow Insight 2024\bin\Synergy.exe",
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            logger.info(f"Found Moldflow Synergy at: {path}")
            return path
    
    return None


def start_synergy():
    """Start Moldflow Synergy application."""
    synergy_path = find_moldflow_installation()
    
    if not synergy_path:
        logger.error("Moldflow Synergy installation not found")
        return False
    
    try:
        logger.info("Starting Moldflow Synergy...")
        # Start Synergy in the background
        subprocess.Popen([synergy_path], shell=True)
        
        # Wait a bit for Synergy to start
        logger.info("Waiting for Synergy to start...")
        time.sleep(10)
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to start Synergy: {str(e)}")
        return False


def test_synergy_connection():
    """Test connection to Synergy."""
    logger.info("Testing Synergy connection...")
    
    wrapper = SynergyWrapper()
    
    # Try to connect
    if wrapper.connect():
        logger.info("✅ Successfully connected to Synergy!")
        
        # Test basic functionality
        try:
            units = wrapper.get_units()
            logger.info(f"✅ Current unit system: {units}")
            
            has_study = wrapper.has_open_study()
            if has_study:
                logger.info("✅ A study is currently open")
                
                is_3d = wrapper.is_3d_mesh()
                if is_3d:
                    logger.info("✅ Study has 3D mesh")
                else:
                    logger.warning("⚠️ Study does not have 3D mesh")
            else:
                logger.warning("⚠️ No study is currently open")
                logger.info("To test the sink mark calculator, please:")
                logger.info("1. Open a 3D study in Synergy")
                logger.info("2. Run a flow analysis")
                logger.info("3. Then run the sink mark calculator")
            
            return True
            
        except Exception as e:
            logger.error(f"Error testing Synergy functionality: {str(e)}")
            return False
    else:
        logger.error("❌ Failed to connect to Synergy")
        return False


def main():
    """Main function."""
    print("=" * 60)
    print("Moldflow Synergy Connection Test")
    print("=" * 60)
    
    # First, try to connect to existing instance
    logger.info("Step 1: Trying to connect to existing Synergy instance...")
    if test_synergy_connection():
        print("\n🎉 Connection successful! Synergy is ready to use.")
        return
    
    # If connection failed, try to start Synergy
    logger.info("Step 2: No existing instance found. Trying to start Synergy...")
    if start_synergy():
        # Wait a bit more and try to connect again
        logger.info("Step 3: Attempting to connect to newly started Synergy...")
        time.sleep(5)
        
        if test_synergy_connection():
            print("\n🎉 Successfully started and connected to Synergy!")
            return
    
    # If all else fails, provide instructions
    print("\n❌ Could not connect to Moldflow Synergy.")
    print("\nTroubleshooting steps:")
    print("1. Make sure Moldflow Synergy is installed")
    print("2. Try starting Synergy manually from the Start menu")
    print("3. Check if any antivirus software is blocking the connection")
    print("4. Run this script as Administrator if needed")
    print("5. Make sure no other instances of Synergy are running")
    
    # Show available Moldflow installations
    synergy_path = find_moldflow_installation()
    if synergy_path:
        print(f"\nFound Moldflow installation at: {synergy_path}")
        print("You can try starting it manually.")
    else:
        print("\nNo Moldflow Synergy installation found in standard locations.")
        print("Please check your Moldflow installation.")


if __name__ == "__main__":
    main()
