'%RunPerInstance
'@Note: The script/macro is provided 'as-is' and cannot be liable for any problems caused.
'@
'@ DESCRIPTION
'@ Create user plot of thickness
'@
'@ SYNTAX
'@ thplot 
'@		
'@ PARAMETERS
'@
'@ LIMITATIONS: 
'@
'@ NOTES
'@ Date       Comment
'@ 2006-10-05 Berndt Nordh
'@ 2008-09-30 Berndt Nordh/Added Dimension Diag for 3D

SetLocale("en-us")
Dim SynergyGetter, Synergy
On Error Resume Next
Set SynergyGetter = GetObject(CreateObject("WScript.Shell").ExpandEnvironmentStrings("%SAInstance%"))
On Error GoTo 0
If (Not IsEmpty(SynergyGetter)) Then
	Set Synergy = SynergyGetter.GetSASynergy
Else
	Set Synergy = CreateObject("synergy.Synergy")
End If

Synergy.SetUnits "Metric"
Set StudyDoc = Synergy.StudyDoc()
CurrentMeshType = StudyDoc.MeshType

'--- Get thickness diagnostics
Set DiagnosisManager = Synergy.DiagnosisManager()
Set IntArr = Synergy.CreateIntegerArray()
Set DoubleArr = Synergy.CreateDoubleArray()
DiagnosisManager.GetThicknessDiagnosis 0.0, 0.0, IntArr, DoubleArr
 
'--- Create User Plot
If (CurrentMeshType = "Fusion") or (CurrentMeshType = "Midplane") Then
  PlotName = "Thickness"
Else
  PlotName = "Dimension 3D"
End if

Set PlotMgr = synergy.PlotManager()
Set Plot = PlotMgr.FindPlotByName(PlotName)
If Not Plot Is Nothing Then
  PlotMgr.DeletePlot(Plot)
End If

Set ThicknessPlot = PlotMgr.CreateUserPlot()
ThicknessPlot.SetDataType "ELDT"
ThicknessPlot.SetName PlotName
ThicknessPlot.AddScalarData 0.0 ,IntArr ,DoubleArr
ThicknessPlot.build
'--- Change properties of plot
Set Viewer = Synergy.Viewer()
Set Plot = Viewer.GetActivePlot()
Plot.SetEdgeDisplay 1
Plot.SetScaleOption 2
Plot.SetExtendedColor True
Plot.SetMinValue 0
Plot.SetMaxValue 5
Plot.SetPlotMethod 2
Plot.Regenerate 
