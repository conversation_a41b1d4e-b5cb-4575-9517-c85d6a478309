﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmCool
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDLog4Net;
using HDMFUserControl;
using HDMoldFlow.Properties;
using HDMoldFlowLibrary;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using System.Windows.Forms.Layout;

namespace HDMoldFlow
{
  public class frmCool : Form
  {
    private const int WS_EX_COMPOSITED = 33554432;
    public DataRow m_drStudy;
    private IContainer components = (IContainer) null;
    private Label label_CoolChannel;
    private Panel panel_Cool;
    private NewButton newButton_Apply;
    private NewButton newButton_Del;
    private NewButton newButton_Add;
    private Panel panel1;
    private Label label5;
    private Label label1;
    private NewButton newButton1;
    private NewTextBox newTextBox1;
    private CheckBox checkBox1;
    private Label label7;
    private Label label4;
    private Label label6;
    private Label label3;
    private NewTextBox newTextBox3;
    private NewTextBox newTextBox2;
    private Panel panel2;
    private Label label8;
    private Label label9;
    private Label label10;
    private Label label11;
    private Label label12;
    private NewTextBox newTextBox4;
    private Label label13;
    private NewTextBox newTextBox5;
    private NewButton newButton2;
    private NewTextBox newTextBox6;
    private CheckBox checkBox2;

    protected override CreateParams CreateParams
    {
      get
      {
        CreateParams createParams = base.CreateParams;
        createParams.ExStyle |= 33554432;
        return createParams;
      }
    }

    public frmCool()
    {
      this.InitializeComponent();
      this.panel_Cool.SetDoubleBuffered(true);
      this.newButton_Add.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Add.Image);
      this.newButton_Del.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Del.Image);
      this.newButton_Apply.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Apply.Image);
      this.label_CoolChannel.Text = LocaleControl.getInstance().GetString("IDS_COOL_CHANNEL");
      this.newButton_Apply.ButtonText = LocaleControl.getInstance().GetString("IDS_CREATE_COOL_CHANNEL");
    }

    private void frmCool_Load(object sender, EventArgs e)
    {
      this.Text = LocaleControl.getInstance().GetString("IDS_COOL") + " [" + LocaleControl.getInstance().GetString("IDS_STUDY") + ": " + this.m_drStudy["Name"].ToString() + "]";
      this.panel_Cool.Controls.Clear();
      this.AddControl();
    }

    private void AddControl()
    {
      int iNum = 1;
      try
      {
        int[] coolNumber = clsHDMFLib.GetCoolNumber();
        while (true)
        {
          if (((IEnumerable<int>) coolNumber).Any<int>((System.Func<int, bool>) (Temp => Temp == iNum)) || this.panel_Cool.Controls.Cast<Control>().Any<Control>((System.Func<Control, bool>) (Temp => Convert.ToInt32(Temp.Name.Replace("pnMain_", "")) == iNum)))
            iNum++;
          else
            break;
        }
        Panel panel = new Panel();
        panel.BorderStyle = BorderStyle.FixedSingle;
        panel.Location = new Point(1, 1);
        panel.Size = new Size(375, 48);
        panel.Name = "pnMain_" + (object) iNum;
        CheckBox checkBox = new CheckBox();
        checkBox.Location = new Point(3, 17);
        checkBox.Size = new Size(15, 14);
        checkBox.Name = "cbCheck";
        checkBox.Text = "";
        panel.Controls.Add((Control) checkBox);
        NewButton newButton = new NewButton();
        newButton.ButtonBackColor = Color.White;
        newButton.ButtonText = LocaleControl.getInstance().GetString("IDS_COOL_CHANNEL") + Environment.NewLine + (object) iNum;
        newButton.Location = new Point(19, -1);
        newButton.Name = "nbCool";
        newButton.Size = new Size(65, 48);
        newButton.NewClick += new EventHandler(this.newButton_Cool_NewClick);
        panel.Controls.Add((Control) newButton);
        Label label1 = new Label();
        label1.BorderStyle = BorderStyle.FixedSingle;
        label1.BackColor = Color.Lavender;
        label1.TextAlign = ContentAlignment.MiddleCenter;
        label1.AutoSize = false;
        label1.Location = new Point(83, -1);
        label1.Size = new Size(99, 25);
        label1.Text = "Channel";
        label1.Name = "lbChannel";
        panel.Controls.Add((Control) label1);
        Label label2 = new Label();
        label2.BackColor = Color.LavenderBlush;
        label2.BorderStyle = BorderStyle.FixedSingle;
        label2.TextAlign = ContentAlignment.MiddleCenter;
        label2.AutoSize = false;
        label2.Location = new Point(83, 23);
        label2.Size = new Size(60, 24);
        label2.Text = "Dim";
        label2.Name = "lbCDim";
        panel.Controls.Add((Control) label2);
        NewTextBox newTextBox1 = new NewTextBox();
        newTextBox1.IsDigit = true;
        newTextBox1.Location = new Point(142, 23);
        newTextBox1.Name = "tbCDim";
        newTextBox1.Size = new Size(40, 24);
        newTextBox1.Value = "10";
        newTextBox1.TextAlign = HorizontalAlignment.Center;
        newTextBox1.TextBoxBackColor = Color.White;
        panel.Controls.Add((Control) newTextBox1);
        Label label3 = new Label();
        label3.AutoSize = false;
        label3.BackColor = Color.Lavender;
        label3.BorderStyle = BorderStyle.FixedSingle;
        label3.Location = new Point(181, -1);
        label3.Name = "lbBaffle";
        label3.Text = "Baffle";
        label3.TextAlign = ContentAlignment.MiddleCenter;
        label3.Size = new Size(99, 25);
        panel.Controls.Add((Control) label3);
        Label label4 = new Label();
        label4.AutoSize = false;
        label4.BackColor = Color.LavenderBlush;
        label4.BorderStyle = BorderStyle.FixedSingle;
        label4.Location = new Point(181, 23);
        label4.Name = "lbBDim";
        label4.Text = "Dim";
        label4.TextAlign = ContentAlignment.MiddleCenter;
        label4.Size = new Size(60, 24);
        panel.Controls.Add((Control) label4);
        NewTextBox newTextBox2 = new NewTextBox();
        newTextBox2.IsDigit = true;
        newTextBox2.Location = new Point(240, 23);
        newTextBox2.Name = "tbBDim";
        newTextBox2.Size = new Size(40, 24);
        newTextBox2.Value = "14";
        newTextBox2.TextAlign = HorizontalAlignment.Center;
        newTextBox2.TextBoxBackColor = Color.White;
        panel.Controls.Add((Control) newTextBox2);
        Label label5 = new Label();
        label5.AutoSize = false;
        label5.BackColor = Color.Lavender;
        label5.BorderStyle = BorderStyle.FixedSingle;
        label5.Location = new Point(279, -1);
        label5.Name = "lbHose";
        label5.Text = "Hose";
        label5.TextAlign = ContentAlignment.MiddleCenter;
        label5.Size = new Size(99, 25);
        panel.Controls.Add((Control) label5);
        Label label6 = new Label();
        label6.AutoSize = false;
        label6.BackColor = Color.LavenderBlush;
        label6.BorderStyle = BorderStyle.FixedSingle;
        label6.Location = new Point(279, 23);
        label6.Name = "lbHDim";
        label6.Size = new Size(60, 24);
        label6.Text = "Dim";
        label6.TextAlign = ContentAlignment.MiddleCenter;
        panel.Controls.Add((Control) label6);
        NewTextBox newTextBox3 = new NewTextBox();
        newTextBox3.Name = "tbHDim";
        newTextBox3.Location = new Point(338, 23);
        newTextBox3.Size = new Size(40, 24);
        newTextBox3.Value = "10";
        newTextBox3.TextAlign = HorizontalAlignment.Center;
        newTextBox3.TextBoxBackColor = Color.White;
        panel.Controls.Add((Control) newTextBox3);
        this.panel_Cool.Controls.Add((Control) panel);
        this.ResizeControl();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmCool]AddControl):" + ex.Message));
      }
    }

    private void DelControl()
    {
      List<Panel> panelList = new List<Panel>((IEnumerable<Panel>) this.panel_Cool.Controls.Cast<Panel>().Where<Panel>((System.Func<Panel, bool>) (Temp => ((CheckBox) Temp.Controls["cbCheck"]).Checked)).ToArray<Panel>());
      if (panelList.Count <= 0)
        return;
      this.Hide();
      clsUtill.StartProgress("delete Cool Folder...", (Form) this);
      try
      {
        foreach (Panel panel in panelList)
        {
          int int32 = Convert.ToInt32(panel.Name.Replace("pnMain_", ""));
          if (((NewButton) panel.Controls["nbCool"]).ButtonBackColor == Color.LavenderBlush)
            clsHDMFLib.DeleteFolderFromFolderManager(this.m_drStudy["Name"].ToString(), "Cool" + (object) int32);
          this.panel_Cool.Controls.Remove((Control) panel);
        }
        this.ResizeControl();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmCool]DelControl):" + ex.Message));
      }
      clsUtill.EndProgress();
      clsUtill.ShowForm((Form) this);
    }

    private void ResizeControl()
    {
      int iNum = 1;
      int num = 0;
      try
      {
        foreach (Panel panel in new List<Panel>((IEnumerable<Panel>) this.panel_Cool.Controls.Cast<Panel>().ToArray<Panel>()))
        {
          int int32 = Convert.ToInt32(panel.Name.Replace("pnMain_", ""));
          clsHDMFLib.RenameFolderFromFolderManager(this.m_drStudy["Name"].ToString(), "Cool" + (object) int32, "CoolTmp");
          panel.Name = "pnMain";
          int[] coolNumber = clsHDMFLib.GetCoolNumber();
          while (true)
          {
            if (((IEnumerable<int>) coolNumber).Any<int>((System.Func<int, bool>) (Temp => Temp == iNum)) || this.panel_Cool.Controls.Cast<Control>().Any<Control>((System.Func<Control, bool>) (Temp => Temp.Name != "pnMain" && Convert.ToInt32(Temp.Name.Replace("pnMain_", "")) == iNum)))
              iNum++;
            else
              break;
          }
          panel.Name = "pnMain_" + (object) iNum;
          clsHDMFLib.RenameFolderFromFolderManager(this.m_drStudy["Name"].ToString(), "CoolTmp", "Cool" + (object) iNum);
          panel.Location = new Point(1, num + 1);
          ((NewButton) panel.Controls["nbCool"]).ButtonText = LocaleControl.getInstance().GetString("IDS_COOL_CHANNEL") + Environment.NewLine + (object) iNum;
          num += panel.Height + 1;
          iNum++;
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmCool]ResizeControl):" + ex.Message));
      }
    }

    private void newButton_Cool_NewClick(object sender, EventArgs e)
    {
      NewButton p_nbCool = sender as NewButton;
      if (p_nbCool == this.newButton_Add)
        this.AddControl();
      else if (p_nbCool == this.newButton_Del)
        this.DelControl();
      else if (p_nbCool == this.newButton_Apply)
        this.CreateCool();
      else
        this.SetFilePath(p_nbCool);
    }

    private void SetFilePath(NewButton p_nbCool)
    {
      try
      {
        OpenFileDialog openFileDialog = new OpenFileDialog();
        openFileDialog.Title = LocaleControl.getInstance().GetString("IDS_COOL_CHANNEL") + " " + LocaleControl.getInstance().GetString("IDS_SELECT_FILE");
        openFileDialog.Filter = "All Files (*.*)|*.*";
        if (openFileDialog.ShowDialog((IWin32Window) this) != DialogResult.OK)
          return;
        Panel parent = (Panel) p_nbCool.Parent;
        this.Hide();
        clsUtill.StartProgress("import Cool File...", (Form) this);
        if (clsHDMFLib.ImportCoolModel(this.m_drStudy["Name"].ToString(), openFileDialog.FileName, Convert.ToInt32(parent.Name.Replace("pnMain_", ""))))
          p_nbCool.ButtonBackColor = Color.LavenderBlush;
        clsUtill.EndProgress();
        clsUtill.ShowForm((Form) this);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmCool]SetFilePath):" + ex.Message));
      }
    }

    private void CreateCool()
    {
      bool flag = false;
      if (this.panel_Cool.Controls.Count == 0)
        return;
      this.Hide();
      clsUtill.StartProgress("create CoolChannel...", (Form) this);
      try
      {
        DataTable p_dtCData = new DataTable();
        p_dtCData.Columns.AddRange(new DataColumn[4]
        {
          new DataColumn("Number"),
          new DataColumn("Channel"),
          new DataColumn("Baffle"),
          new DataColumn("Hose")
        });
        foreach (Panel control in (ArrangedElementCollection) this.panel_Cool.Controls)
        {
          int int32 = Convert.ToInt32(control.Name.Replace("pnMain_", ""));
          if (clsHDMFLib.ExistFolderFromFolderManager("Cool" + (object) int32))
          {
            DataRow dataRow = p_dtCData.Rows.Add();
            dataRow["Number"] = (object) int32;
            dataRow["Channel"] = (object) clsUtill.ConvertToDouble(((NewTextBox) control.Controls["tbCDim"]).Value);
            dataRow["Baffle"] = (object) clsUtill.ConvertToDouble(((NewTextBox) control.Controls["tbBDim"]).Value);
            dataRow["Hose"] = (object) clsUtill.ConvertToDouble(((NewTextBox) control.Controls["tbHDim"]).Value);
          }
        }
        flag = clsHDMFLib.CreateCool(this.m_drStudy["Name"].ToString(), p_dtCData);
        clsHDMFLib.WriteCavityAndCoreToStudyNote();
        if (flag)
          clsHDMFLib.MoveCool(p_dtCData);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmCool]CreateCool):" + ex.Message));
      }
      clsUtill.EndProgress();
      if (!flag)
      {
        clsUtill.ShowForm((Form) this);
      }
      else
      {
        this.DialogResult = DialogResult.OK;
        this.Close();
      }
    }

    private void frmCool_KeyDown(object sender, KeyEventArgs e)
    {
      if (e.KeyCode != Keys.Escape)
        return;
      this.Close();
    }

    private void frmCool_FormClosed(object sender, FormClosedEventArgs e)
    {
      if (this.DialogResult == DialogResult.OK)
        return;
      List<Panel> panelList = new List<Panel>((IEnumerable<Panel>) this.panel_Cool.Controls.Cast<Panel>().Where<Panel>((System.Func<Panel, bool>) (Temp => ((NewButton) Temp.Controls["nbCool"]).ButtonBackColor == Color.LavenderBlush)).ToArray<Panel>());
      if (panelList.Count > 0)
      {
        this.Hide();
        clsUtill.StartProgress("delete Empty Cool Folder...", (Form) this);
        try
        {
          foreach (Panel panel in panelList)
          {
            int int32 = Convert.ToInt32(panel.Name.Replace("pnMain_", ""));
            if (((NewButton) panel.Controls["nbCool"]).ButtonBackColor == Color.LavenderBlush)
              clsHDMFLib.DeleteFolderFromFolderManager(this.m_drStudy["Name"].ToString(), "Cool" + (object) int32);
          }
        }
        catch (Exception ex)
        {
          HDLog.Instance().Error((object) ("Exception([frmCool]frmCool_FormClosed):" + ex.Message));
        }
        clsUtill.EndProgress();
      }
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.label_CoolChannel = new Label();
      this.panel_Cool = new Panel();
      this.panel2 = new Panel();
      this.label8 = new Label();
      this.label9 = new Label();
      this.label10 = new Label();
      this.label11 = new Label();
      this.label12 = new Label();
      this.newTextBox4 = new NewTextBox();
      this.label13 = new Label();
      this.newTextBox5 = new NewTextBox();
      this.newButton2 = new NewButton();
      this.newTextBox6 = new NewTextBox();
      this.checkBox2 = new CheckBox();
      this.panel1 = new Panel();
      this.label7 = new Label();
      this.label4 = new Label();
      this.label5 = new Label();
      this.label6 = new Label();
      this.label3 = new Label();
      this.newTextBox3 = new NewTextBox();
      this.label1 = new Label();
      this.newTextBox2 = new NewTextBox();
      this.newButton1 = new NewButton();
      this.newTextBox1 = new NewTextBox();
      this.checkBox1 = new CheckBox();
      this.newButton_Apply = new NewButton();
      this.newButton_Del = new NewButton();
      this.newButton_Add = new NewButton();
      this.panel_Cool.SuspendLayout();
      this.panel2.SuspendLayout();
      this.panel1.SuspendLayout();
      this.SuspendLayout();
      this.label_CoolChannel.BackColor = Color.FromArgb(229, 238, 248);
      this.label_CoolChannel.BorderStyle = BorderStyle.FixedSingle;
      this.label_CoolChannel.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_CoolChannel.ForeColor = Color.MidnightBlue;
      this.label_CoolChannel.Location = new Point(5, 5);
      this.label_CoolChannel.Name = "label_CoolChannel";
      this.label_CoolChannel.Size = new Size(379, 20);
      this.label_CoolChannel.TabIndex = 6;
      this.label_CoolChannel.Text = "냉각채널";
      this.label_CoolChannel.TextAlign = ContentAlignment.MiddleCenter;
      this.panel_Cool.AutoScroll = true;
      this.panel_Cool.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Cool.Controls.Add((Control) this.panel2);
      this.panel_Cool.Controls.Add((Control) this.panel1);
      this.panel_Cool.Location = new Point(5, 24);
      this.panel_Cool.Name = "panel_Cool";
      this.panel_Cool.Size = new Size(379, 425);
      this.panel_Cool.TabIndex = 8;
      this.panel2.BorderStyle = BorderStyle.FixedSingle;
      this.panel2.Controls.Add((Control) this.label8);
      this.panel2.Controls.Add((Control) this.label9);
      this.panel2.Controls.Add((Control) this.label10);
      this.panel2.Controls.Add((Control) this.label11);
      this.panel2.Controls.Add((Control) this.label12);
      this.panel2.Controls.Add((Control) this.newTextBox4);
      this.panel2.Controls.Add((Control) this.label13);
      this.panel2.Controls.Add((Control) this.newTextBox5);
      this.panel2.Controls.Add((Control) this.newButton2);
      this.panel2.Controls.Add((Control) this.newTextBox6);
      this.panel2.Controls.Add((Control) this.checkBox2);
      this.panel2.Location = new Point(1, 50);
      this.panel2.Name = "panel2";
      this.panel2.Size = new Size(375, 48);
      this.panel2.TabIndex = 0;
      this.label8.BackColor = Color.LavenderBlush;
      this.label8.BorderStyle = BorderStyle.FixedSingle;
      this.label8.Location = new Point(279, 23);
      this.label8.Name = "label8";
      this.label8.Size = new Size(60, 24);
      this.label8.TabIndex = 1;
      this.label8.Text = "Dim";
      this.label8.TextAlign = ContentAlignment.MiddleCenter;
      this.label9.BackColor = Color.LavenderBlush;
      this.label9.BorderStyle = BorderStyle.FixedSingle;
      this.label9.Location = new Point(181, 23);
      this.label9.Name = "label9";
      this.label9.Size = new Size(60, 24);
      this.label9.TabIndex = 1;
      this.label9.Text = "Dim";
      this.label9.TextAlign = ContentAlignment.MiddleCenter;
      this.label10.BackColor = Color.LavenderBlush;
      this.label10.BorderStyle = BorderStyle.FixedSingle;
      this.label10.Location = new Point(83, 23);
      this.label10.Name = "label10";
      this.label10.Size = new Size(60, 24);
      this.label10.TabIndex = 1;
      this.label10.Text = "Dim";
      this.label10.TextAlign = ContentAlignment.MiddleCenter;
      this.label11.BackColor = Color.Lavender;
      this.label11.BorderStyle = BorderStyle.FixedSingle;
      this.label11.Location = new Point(279, -1);
      this.label11.Name = "label11";
      this.label11.Size = new Size(99, 25);
      this.label11.TabIndex = 1;
      this.label11.Text = "Hose";
      this.label11.TextAlign = ContentAlignment.MiddleCenter;
      this.label12.BackColor = Color.Lavender;
      this.label12.BorderStyle = BorderStyle.FixedSingle;
      this.label12.Location = new Point(181, -1);
      this.label12.Name = "label12";
      this.label12.Size = new Size(99, 25);
      this.label12.TabIndex = 1;
      this.label12.Text = "Baffle";
      this.label12.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox4.BackColor = SystemColors.Window;
      this.newTextBox4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox4.IsDigit = false;
      this.newTextBox4.Lines = new string[1]{ "0" };
      this.newTextBox4.Location = new Point(338, 23);
      this.newTextBox4.MultiLine = false;
      this.newTextBox4.Name = "newTextBox4";
      this.newTextBox4.ReadOnly = false;
      this.newTextBox4.Size = new Size(40, 24);
      this.newTextBox4.TabIndex = 1;
      this.newTextBox4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox4.TextForeColor = SystemColors.WindowText;
      this.newTextBox4.Value = "0";
      this.label13.BackColor = Color.Lavender;
      this.label13.BorderStyle = BorderStyle.FixedSingle;
      this.label13.Location = new Point(83, -1);
      this.label13.Name = "label13";
      this.label13.Size = new Size(99, 25);
      this.label13.TabIndex = 1;
      this.label13.Text = "Channel";
      this.label13.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox5.BackColor = SystemColors.Window;
      this.newTextBox5.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox5.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox5.IsDigit = false;
      this.newTextBox5.Lines = new string[1]{ "0" };
      this.newTextBox5.Location = new Point(240, 23);
      this.newTextBox5.MultiLine = false;
      this.newTextBox5.Name = "newTextBox5";
      this.newTextBox5.ReadOnly = false;
      this.newTextBox5.Size = new Size(40, 24);
      this.newTextBox5.TabIndex = 1;
      this.newTextBox5.TextAlign = HorizontalAlignment.Center;
      this.newTextBox5.TextBoxBackColor = SystemColors.Window;
      this.newTextBox5.TextForeColor = SystemColors.WindowText;
      this.newTextBox5.Value = "0";
      this.newButton2.ButtonBackColor = Color.White;
      this.newButton2.ButtonText = "냉각채널1";
      this.newButton2.FlatBorderSize = 1;
      this.newButton2.FlatStyle = FlatStyle.Flat;
      this.newButton2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton2.Image = (Image) null;
      this.newButton2.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton2.Location = new Point(19, -1);
      this.newButton2.Name = "newButton2";
      this.newButton2.Size = new Size(65, 48);
      this.newButton2.TabIndex = 2;
      this.newButton2.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton2.TextImageRelocation = TextImageRelation.Overlay;
      this.newTextBox6.BackColor = SystemColors.Window;
      this.newTextBox6.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox6.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox6.IsDigit = false;
      this.newTextBox6.Lines = new string[1]{ "0" };
      this.newTextBox6.Location = new Point(142, 23);
      this.newTextBox6.MultiLine = false;
      this.newTextBox6.Name = "newTextBox6";
      this.newTextBox6.ReadOnly = false;
      this.newTextBox6.Size = new Size(40, 24);
      this.newTextBox6.TabIndex = 1;
      this.newTextBox6.TextAlign = HorizontalAlignment.Center;
      this.newTextBox6.TextBoxBackColor = SystemColors.Window;
      this.newTextBox6.TextForeColor = SystemColors.WindowText;
      this.newTextBox6.Value = "0";
      this.checkBox2.AutoSize = true;
      this.checkBox2.Location = new Point(3, 17);
      this.checkBox2.Name = "checkBox2";
      this.checkBox2.Size = new Size(15, 14);
      this.checkBox2.TabIndex = 0;
      this.checkBox2.UseVisualStyleBackColor = true;
      this.panel1.BorderStyle = BorderStyle.FixedSingle;
      this.panel1.Controls.Add((Control) this.label7);
      this.panel1.Controls.Add((Control) this.label4);
      this.panel1.Controls.Add((Control) this.label5);
      this.panel1.Controls.Add((Control) this.label6);
      this.panel1.Controls.Add((Control) this.label3);
      this.panel1.Controls.Add((Control) this.newTextBox3);
      this.panel1.Controls.Add((Control) this.label1);
      this.panel1.Controls.Add((Control) this.newTextBox2);
      this.panel1.Controls.Add((Control) this.newButton1);
      this.panel1.Controls.Add((Control) this.newTextBox1);
      this.panel1.Controls.Add((Control) this.checkBox1);
      this.panel1.Location = new Point(1, 1);
      this.panel1.Name = "panel1";
      this.panel1.Size = new Size(375, 48);
      this.panel1.TabIndex = 0;
      this.label7.BackColor = Color.LavenderBlush;
      this.label7.BorderStyle = BorderStyle.FixedSingle;
      this.label7.Location = new Point(279, 23);
      this.label7.Name = "label7";
      this.label7.Size = new Size(60, 24);
      this.label7.TabIndex = 1;
      this.label7.Text = "Dim";
      this.label7.TextAlign = ContentAlignment.MiddleCenter;
      this.label4.BackColor = Color.LavenderBlush;
      this.label4.BorderStyle = BorderStyle.FixedSingle;
      this.label4.Location = new Point(181, 23);
      this.label4.Name = "label4";
      this.label4.Size = new Size(60, 24);
      this.label4.TabIndex = 1;
      this.label4.Text = "Dim";
      this.label4.TextAlign = ContentAlignment.MiddleCenter;
      this.label5.BackColor = Color.LavenderBlush;
      this.label5.BorderStyle = BorderStyle.FixedSingle;
      this.label5.Location = new Point(83, 23);
      this.label5.Name = "label5";
      this.label5.Size = new Size(60, 24);
      this.label5.TabIndex = 1;
      this.label5.Text = "Dim";
      this.label5.TextAlign = ContentAlignment.MiddleCenter;
      this.label6.BackColor = Color.Lavender;
      this.label6.BorderStyle = BorderStyle.FixedSingle;
      this.label6.Location = new Point(279, -1);
      this.label6.Name = "label6";
      this.label6.Size = new Size(99, 25);
      this.label6.TabIndex = 1;
      this.label6.Text = "Hose";
      this.label6.TextAlign = ContentAlignment.MiddleCenter;
      this.label3.BackColor = Color.Lavender;
      this.label3.BorderStyle = BorderStyle.FixedSingle;
      this.label3.Location = new Point(181, -1);
      this.label3.Name = "label3";
      this.label3.Size = new Size(99, 25);
      this.label3.TabIndex = 1;
      this.label3.Text = "Baffle";
      this.label3.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox3.BackColor = SystemColors.Window;
      this.newTextBox3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox3.IsDigit = false;
      this.newTextBox3.Lines = new string[1]{ "0" };
      this.newTextBox3.Location = new Point(338, 23);
      this.newTextBox3.MultiLine = false;
      this.newTextBox3.Name = "newTextBox3";
      this.newTextBox3.ReadOnly = false;
      this.newTextBox3.Size = new Size(40, 24);
      this.newTextBox3.TabIndex = 1;
      this.newTextBox3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox3.TextForeColor = SystemColors.WindowText;
      this.newTextBox3.Value = "0";
      this.label1.BackColor = Color.Lavender;
      this.label1.BorderStyle = BorderStyle.FixedSingle;
      this.label1.Location = new Point(83, -1);
      this.label1.Name = "label1";
      this.label1.Size = new Size(99, 25);
      this.label1.TabIndex = 1;
      this.label1.Text = "Channel";
      this.label1.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox2.BackColor = SystemColors.Window;
      this.newTextBox2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox2.IsDigit = false;
      this.newTextBox2.Lines = new string[1]{ "0" };
      this.newTextBox2.Location = new Point(240, 23);
      this.newTextBox2.MultiLine = false;
      this.newTextBox2.Name = "newTextBox2";
      this.newTextBox2.ReadOnly = false;
      this.newTextBox2.Size = new Size(40, 24);
      this.newTextBox2.TabIndex = 1;
      this.newTextBox2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox2.TextForeColor = SystemColors.WindowText;
      this.newTextBox2.Value = "0";
      this.newButton1.ButtonBackColor = Color.White;
      this.newButton1.ButtonText = "냉각채널1";
      this.newButton1.FlatBorderSize = 1;
      this.newButton1.FlatStyle = FlatStyle.Flat;
      this.newButton1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton1.Image = (Image) null;
      this.newButton1.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton1.Location = new Point(19, -1);
      this.newButton1.Name = "newButton1";
      this.newButton1.Size = new Size(65, 48);
      this.newButton1.TabIndex = 2;
      this.newButton1.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton1.TextImageRelocation = TextImageRelation.Overlay;
      this.newTextBox1.BackColor = SystemColors.Window;
      this.newTextBox1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox1.IsDigit = false;
      this.newTextBox1.Lines = new string[1]{ "0" };
      this.newTextBox1.Location = new Point(142, 23);
      this.newTextBox1.MultiLine = false;
      this.newTextBox1.Name = "newTextBox1";
      this.newTextBox1.ReadOnly = false;
      this.newTextBox1.Size = new Size(40, 24);
      this.newTextBox1.TabIndex = 1;
      this.newTextBox1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox1.TextForeColor = SystemColors.WindowText;
      this.newTextBox1.Value = "0";
      this.checkBox1.AutoSize = true;
      this.checkBox1.Location = new Point(3, 17);
      this.checkBox1.Name = "checkBox1";
      this.checkBox1.Size = new Size(15, 14);
      this.checkBox1.TabIndex = 0;
      this.checkBox1.UseVisualStyleBackColor = true;
      this.newButton_Apply.ButtonBackColor = Color.White;
      this.newButton_Apply.ButtonText = "냉각 채널 생성";
      this.newButton_Apply.FlatBorderSize = 1;
      this.newButton_Apply.FlatStyle = FlatStyle.Flat;
      this.newButton_Apply.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Apply.Image = (Image) Resources.OK;
      this.newButton_Apply.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Apply.Location = new Point(5, 453);
      this.newButton_Apply.Name = "newButton_Apply";
      this.newButton_Apply.Size = new Size(379, 23);
      this.newButton_Apply.TabIndex = 19;
      this.newButton_Apply.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Apply.NewClick += new EventHandler(this.newButton_Cool_NewClick);
      this.newButton_Del.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Del.ButtonText = "";
      this.newButton_Del.FlatBorderSize = 0;
      this.newButton_Del.FlatStyle = FlatStyle.Flat;
      this.newButton_Del.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Del.Image = (Image) Resources.Del;
      this.newButton_Del.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Del.Location = new Point(362, 7);
      this.newButton_Del.Name = "newButton_Del";
      this.newButton_Del.Size = new Size(19, 17);
      this.newButton_Del.TabIndex = 20;
      this.newButton_Del.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Del.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Del.NewClick += new EventHandler(this.newButton_Cool_NewClick);
      this.newButton_Add.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Add.ButtonText = "";
      this.newButton_Add.FlatBorderSize = 0;
      this.newButton_Add.FlatStyle = FlatStyle.Flat;
      this.newButton_Add.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Add.Image = (Image) Resources.Add;
      this.newButton_Add.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Add.Location = new Point(337, 7);
      this.newButton_Add.Name = "newButton_Add";
      this.newButton_Add.Size = new Size(19, 17);
      this.newButton_Add.TabIndex = 21;
      this.newButton_Add.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Add.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Add.NewClick += new EventHandler(this.newButton_Cool_NewClick);
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(389, 480);
      this.Controls.Add((Control) this.newButton_Del);
      this.Controls.Add((Control) this.newButton_Add);
      this.Controls.Add((Control) this.newButton_Apply);
      this.Controls.Add((Control) this.panel_Cool);
      this.Controls.Add((Control) this.label_CoolChannel);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmCool);
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "냉각(GM_HSG_CHROME_211209_study_Tetra_3D_Rev1)";
      this.FormClosed += new FormClosedEventHandler(this.frmCool_FormClosed);
      this.Load += new EventHandler(this.frmCool_Load);
      this.KeyDown += new KeyEventHandler(this.frmCool_KeyDown);
      this.panel_Cool.ResumeLayout(false);
      this.panel2.ResumeLayout(false);
      this.panel2.PerformLayout();
      this.panel1.ResumeLayout(false);
      this.panel1.PerformLayout();
      this.ResumeLayout(false);
    }
  }
}
