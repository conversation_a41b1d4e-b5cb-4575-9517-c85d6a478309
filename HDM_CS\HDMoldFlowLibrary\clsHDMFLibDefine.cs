﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlowLibrary.clsHDMFLibDefine
// Assembly: HDMoldFlowLibrary, Version=2.24.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 16E5D508-B892-4B5D-A02C-2C1930D27867
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMoldFlowLibrary.dll

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;

#nullable disable
namespace HDMoldFlowLibrary
{
  [StandardModule]
  public sealed class clsHDMFLibDefine
  {
    public static string m_strMFDPath;
    public static string m_strTmpDPath;
    public static object m_strUserUDBFolder = (object) "My AMI 2021.1 Projects";
    public static object m_strUserUDBMFDPath = Operators.ConcatenateObject(Operators.ConcatenateObject((object) (Environment.GetFolderPath(Environment.SpecialFolder.Personal) + "\\"), clsHDMFLibDefine.m_strUserUDBFolder), (object) "\\udb");
    public static double m_dblZoom = 0.9;
    public static string m_strVersion = "2021 R1";
    public static Dictionary<string, string> m_dicLang = new Dictionary<string, string>();

    public enum Company
    {
      admin,
      HDSolutions,
      LGHH,
      Hyundai,
      Kumnung,
      Changsung,
      SL,
    }

    public enum SummaryType
    {
      Basic,
      AI,
    }
  }
}
