﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmUpdateMesh
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDLog4Net;
using HDMFUserControl;
using System;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public class frmUpdateMesh : Form
  {
    private const int CP_NOCLOSE_BUTTON = 512;
    public Form m_frmParent;
    public string m_strMesh;
    public DataRow[] m_arr_drStudy = (DataRow[]) null;
    private IContainer components = (IContainer) null;
    private Panel panel_Main;
    private Panel panel1;
    private ProgressBar progressBar1;
    private NewTextBox newTextBox2;
    private Label label3;
    private NewTextBox newTextBox1;
    private Label label1;
    private Label label4;
    private Label label2;
    private NewTextBox newTextBox3;

    protected override CreateParams CreateParams
    {
      get
      {
        CreateParams createParams = base.CreateParams;
        createParams.ClassStyle |= 512;
        return createParams;
      }
    }

    [DllImport("user32.dll")]
    private static extern bool SetForegroundWindow(IntPtr hWnd);

    public frmUpdateMesh() => this.InitializeComponent();

    private void frmUpdateMesh_Load(object sender, EventArgs e)
    {
      this.Text = "update " + this.m_strMesh + " Mesh";
      if (this.m_frmParent != null)
      {
        this.Top = this.m_frmParent.Top + (this.m_frmParent.Height / 2 - this.Height / 2);
        this.Left = this.m_frmParent.Left + (this.m_frmParent.Width / 2 - this.Width / 2);
      }
      else
        this.CenterToScreen();
      frmUpdateMesh.SetForegroundWindow(this.Handle);
      this.SetDefault();
    }

    private void SetDefault()
    {
      int y = 0;
      this.panel_Main.Controls.Clear();
      try
      {
        for (int index = 0; index < this.m_arr_drStudy.Length; ++index)
        {
          DataRow dataRow = this.m_arr_drStudy[index];
          Panel panel = new Panel();
          panel.BorderStyle = BorderStyle.FixedSingle;
          panel.Location = new Point(0, y);
          panel.Size = new Size(647, 60);
          panel.Name = "pnMain_" + dataRow["Name"].ToString();
          y += panel.Height - 1;
          Label label1 = new Label();
          label1.BackColor = Color.Lavender;
          label1.BorderStyle = BorderStyle.FixedSingle;
          label1.Location = new Point(-1, -1);
          label1.Size = new Size(47, 60);
          label1.Text = (index + 1).ToString();
          label1.TextAlign = ContentAlignment.MiddleCenter;
          label1.Name = "lbCount";
          panel.Controls.Add((Control) label1);
          Label label2 = new Label();
          label2.BackColor = Color.Lavender;
          label2.BorderStyle = BorderStyle.FixedSingle;
          label2.Location = new Point(45, -1);
          label2.Name = "lbProduct";
          label2.Size = new Size(117, 20);
          label2.Text = LocaleControl.getInstance().GetString("IDS_PRODUCT");
          label2.TextAlign = ContentAlignment.MiddleCenter;
          panel.Controls.Add((Control) label2);
          NewTextBox newTextBox1 = new NewTextBox();
          newTextBox1.Location = new Point(45, 18);
          newTextBox1.Name = "ntbProduct";
          newTextBox1.Size = new Size(117, 21);
          newTextBox1.TextAlign = HorizontalAlignment.Center;
          newTextBox1.TextBoxBackColor = Color.LavenderBlush;
          newTextBox1.Value = dataRow.Table.TableName;
          panel.Controls.Add((Control) newTextBox1);
          Label label3 = new Label();
          label3.BackColor = Color.Lavender;
          label3.BorderStyle = BorderStyle.FixedSingle;
          label3.Location = new Point(161, -1);
          label3.Name = "lbStudy";
          label3.Size = new Size(369, 20);
          label3.Text = LocaleControl.getInstance().GetString("IDS_STUDY");
          label3.TextAlign = ContentAlignment.MiddleCenter;
          panel.Controls.Add((Control) label3);
          NewTextBox newTextBox2 = new NewTextBox();
          newTextBox2.Location = new Point(161, 18);
          newTextBox2.Name = "ntbStudy";
          newTextBox2.Size = new Size(369, 21);
          newTextBox2.TextAlign = HorizontalAlignment.Center;
          newTextBox2.TextBoxBackColor = Color.LavenderBlush;
          newTextBox2.Value = dataRow["Name"].ToString();
          panel.Controls.Add((Control) newTextBox2);
          Label label4 = new Label();
          label4.BackColor = Color.Lavender;
          label4.BorderStyle = BorderStyle.FixedSingle;
          label4.Location = new Point(529, -1);
          label4.Name = "lbStatus";
          label4.Size = new Size(117, 20);
          label4.Text = LocaleControl.getInstance().GetString("IDS_STATUS");
          label4.TextAlign = ContentAlignment.MiddleCenter;
          panel.Controls.Add((Control) label4);
          NewTextBox newTextBox3 = new NewTextBox();
          newTextBox3.Location = new Point(529, 18);
          newTextBox3.Name = "ntbStatus";
          newTextBox3.Size = new Size(117, 21);
          newTextBox3.TextAlign = HorizontalAlignment.Center;
          newTextBox3.TextBoxBackColor = Color.LavenderBlush;
          newTextBox3.Value = "-";
          panel.Controls.Add((Control) newTextBox3);
          ProgressBar progressBar = new ProgressBar();
          progressBar.Location = new Point(45, 38);
          progressBar.Name = "pbStatus";
          progressBar.Size = new Size(601, 21);
          panel.Controls.Add((Control) progressBar);
          this.panel_Main.Controls.Add((Control) panel);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmUpdateMesh]SetDefault):" + ex.Message));
      }
    }

    public void UpdateStatus(string p_strStudy, string p_strText, string p_strProg)
    {
      try
      {
        Panel control1 = (Panel) this.panel_Main.Controls["pnMain_" + p_strStudy];
        NewTextBox control2 = (NewTextBox) control1.Controls["ntbStatus"];
        ProgressBar control3 = (ProgressBar) control1.Controls["pbStatus"];
        control2.Value = p_strText;
        if (!(p_strProg != ""))
          return;
        int num = clsUtill.ConvertToInt(p_strProg);
        if (num > 0)
          control3.Value = num;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmUpdateMesh]UpdateStatus):" + ex.Message));
      }
    }

    public void ChangeProgressColor(string p_strStudy, clsUtill.ProgressBarColor p_pbColor)
    {
      try
      {
        clsUtill.SendMessage(this.panel_Main.Controls["pnMain_" + p_strStudy].Controls["pbStatus"].Handle, 1040U, (IntPtr) (int) p_pbColor, IntPtr.Zero);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmUpdateMesh]ChangeProgressColor):" + ex.Message));
      }
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.panel_Main = new Panel();
      this.panel1 = new Panel();
      this.newTextBox2 = new NewTextBox();
      this.label3 = new Label();
      this.progressBar1 = new ProgressBar();
      this.newTextBox1 = new NewTextBox();
      this.label1 = new Label();
      this.label4 = new Label();
      this.label2 = new Label();
      this.newTextBox3 = new NewTextBox();
      this.panel_Main.SuspendLayout();
      this.panel1.SuspendLayout();
      this.SuspendLayout();
      this.panel_Main.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.panel_Main.AutoScroll = true;
      this.panel_Main.Controls.Add((Control) this.panel1);
      this.panel_Main.Location = new Point(5, 5);
      this.panel_Main.Name = "panel_Main";
      this.panel_Main.Size = new Size(647, 390);
      this.panel_Main.TabIndex = 0;
      this.panel1.BorderStyle = BorderStyle.FixedSingle;
      this.panel1.Controls.Add((Control) this.newTextBox1);
      this.panel1.Controls.Add((Control) this.label4);
      this.panel1.Controls.Add((Control) this.newTextBox3);
      this.panel1.Controls.Add((Control) this.newTextBox2);
      this.panel1.Controls.Add((Control) this.label3);
      this.panel1.Controls.Add((Control) this.progressBar1);
      this.panel1.Controls.Add((Control) this.label1);
      this.panel1.Controls.Add((Control) this.label2);
      this.panel1.Location = new Point(0, 0);
      this.panel1.Name = "panel1";
      this.panel1.Size = new Size(647, 60);
      this.panel1.TabIndex = 0;
      this.newTextBox2.BackColor = Color.LavenderBlush;
      this.newTextBox2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox2.IsDigit = false;
      this.newTextBox2.Lines = new string[0];
      this.newTextBox2.Location = new Point(161, 18);
      this.newTextBox2.MultiLine = false;
      this.newTextBox2.Name = "newTextBox2";
      this.newTextBox2.ReadOnly = false;
      this.newTextBox2.Size = new Size(369, 21);
      this.newTextBox2.TabIndex = 8;
      this.newTextBox2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox2.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox2.TextForeColor = SystemColors.WindowText;
      this.newTextBox2.Value = "";
      this.label3.BackColor = Color.Lavender;
      this.label3.BorderStyle = BorderStyle.FixedSingle;
      this.label3.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label3.ForeColor = Color.MidnightBlue;
      this.label3.Location = new Point(529, -1);
      this.label3.Name = "label3";
      this.label3.Size = new Size(117, 20);
      this.label3.TabIndex = 7;
      this.label3.Text = "상태";
      this.label3.TextAlign = ContentAlignment.MiddleCenter;
      this.progressBar1.BackColor = Color.White;
      this.progressBar1.Location = new Point(45, 38);
      this.progressBar1.Name = "progressBar1";
      this.progressBar1.Size = new Size(601, 21);
      this.progressBar1.TabIndex = 0;
      this.newTextBox1.BackColor = Color.LavenderBlush;
      this.newTextBox1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox1.IsDigit = false;
      this.newTextBox1.Lines = new string[0];
      this.newTextBox1.Location = new Point(45, 18);
      this.newTextBox1.MultiLine = false;
      this.newTextBox1.Name = "newTextBox1";
      this.newTextBox1.ReadOnly = false;
      this.newTextBox1.Size = new Size(117, 21);
      this.newTextBox1.TabIndex = 8;
      this.newTextBox1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox1.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox1.TextForeColor = SystemColors.WindowText;
      this.newTextBox1.Value = "";
      this.label1.BackColor = Color.Lavender;
      this.label1.BorderStyle = BorderStyle.FixedSingle;
      this.label1.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label1.ForeColor = Color.MidnightBlue;
      this.label1.Location = new Point(161, -1);
      this.label1.Name = "label1";
      this.label1.Size = new Size(369, 20);
      this.label1.TabIndex = 7;
      this.label1.Text = "스터디";
      this.label1.TextAlign = ContentAlignment.MiddleCenter;
      this.label4.BackColor = Color.Lavender;
      this.label4.BorderStyle = BorderStyle.FixedSingle;
      this.label4.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label4.ForeColor = Color.MidnightBlue;
      this.label4.Location = new Point(-1, -1);
      this.label4.Name = "label4";
      this.label4.Size = new Size(47, 60);
      this.label4.TabIndex = 7;
      this.label4.Text = "1";
      this.label4.TextAlign = ContentAlignment.MiddleCenter;
      this.label2.BackColor = Color.Lavender;
      this.label2.BorderStyle = BorderStyle.FixedSingle;
      this.label2.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label2.ForeColor = Color.MidnightBlue;
      this.label2.Location = new Point(45, -1);
      this.label2.Name = "label2";
      this.label2.Size = new Size(117, 20);
      this.label2.TabIndex = 7;
      this.label2.Text = "제품";
      this.label2.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox3.BackColor = Color.LavenderBlush;
      this.newTextBox3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox3.IsDigit = false;
      this.newTextBox3.Lines = new string[0];
      this.newTextBox3.Location = new Point(529, 18);
      this.newTextBox3.MultiLine = false;
      this.newTextBox3.Name = "newTextBox3";
      this.newTextBox3.ReadOnly = false;
      this.newTextBox3.Size = new Size(117, 21);
      this.newTextBox3.TabIndex = 8;
      this.newTextBox3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox3.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox3.TextForeColor = SystemColors.WindowText;
      this.newTextBox3.Value = "";
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(657, 400);
      this.Controls.Add((Control) this.panel_Main);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.MaximizeBox = false;
      this.Name = nameof (frmUpdateMesh);
      this.ShowIcon = false;
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "update Dual Mesh...";
      this.Load += new EventHandler(this.frmUpdateMesh_Load);
      this.panel_Main.ResumeLayout(false);
      this.panel1.ResumeLayout(false);
      this.ResumeLayout(false);
    }
  }
}
