﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.Program
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDLog4Net;
using HDMoldFlowLibrary;
using System;
using System.IO;
using System.Windows.Forms;

namespace HDMoldFlow
{
  internal static class Program
  {
    [STAThread]
    private static void Main()
    {
      Program.SetPath();
      Program.SetLocaleSetting();
      Program.SetCompanyType();
      Application.EnableVisualStyles();
      Application.SetCompatibleTextRenderingDefault(false);
      frmMain mainForm = new frmMain();
      if (mainForm.IsDisposed)
        return;
      Application.Run((Form) mainForm);
    }

    private static void SetPath()
    {
      string str = clsDefine.g_strPath;
      FileInfo fileInfo = new FileInfo(Application.StartupPath + "\\PathConf.ini");
      if (fileInfo.Exists)
      {
        str = clsUtill.ReadINI("Path", "Path", fileInfo.FullName);
        if (str == "")
          str = "C:\\HDSolutions\\HDMFlow";
        clsDefine.g_strPath = str;
      }
      clsDefine.g_diBasicProject = new DirectoryInfo(str + "\\Projects");
      clsDefine.g_diBigData = new DirectoryInfo(str + "\\AIBigData");
      clsDefine.g_fiLangCfg = new FileInfo(str + "\\Config\\Language.ini");
      clsDefine.g_fiProjCfg = new FileInfo(str + "\\Config\\Project.ini");
      clsDefine.g_fiBigDataCfg = new FileInfo(str + "\\Config\\BigData.ini");
      clsDefine.g_fiMeshCfg = new FileInfo(str + "\\Config\\Mesh.ini");
      clsDefine.g_fiMeshStatCfg = new FileInfo(str + "\\Config\\MeshStat.ini");
      clsDefine.g_fiRotateCfg = new FileInfo(str + "\\Config\\Rotate.ini");
      clsDefine.g_fiGateCfg = new FileInfo(str + "\\Config\\Gate.ini");
      clsDefine.g_fiValveCfg = new FileInfo(str + "\\Config\\Valve.ini");
      clsDefine.g_diRunnerDBCfg = new DirectoryInfo(str + "\\DB\\Runner");
      clsDefine.g_fiMidResultCfg = new FileInfo(str + "\\Config\\MidResult.ini");
      clsDefine.g_fiAICfg = new FileInfo(str + "\\Config\\AI.ini");
      clsDefine.g_fiSummaryCfg = new FileInfo(str + "\\Config\\Summary.ini");
      clsDefine.g_fiSummaryViewOptionCfg = new FileInfo(str + "\\Config\\SummaryView\\SummaryViewOption.ini");
      clsDefine.g_fiExtensionCfg = new FileInfo(str + "\\Config\\Extension.ini");
      clsDefine.g_diProcessDBCfg = new DirectoryInfo(str + "\\DB\\Process");
      clsDefine.g_diProcessUDBCfg = new DirectoryInfo(str + "\\UDB\\Process");
      clsDefine.g_diInjectionDBCfg = new DirectoryInfo(str + "\\DB\\Injection");
      clsDefine.g_diInputDBCfg = new DirectoryInfo(str + "\\DB\\Input");
      clsDefine.g_fiMaterialCfg = new FileInfo(str + "\\DB\\Material\\MaterialData.xml");
      clsDefine.g_diTemplate = new DirectoryInfo(str + "\\Template");
      clsDefine.g_diCfg = new DirectoryInfo(str + "\\Config");
      clsDefine.g_diTmpReport = new DirectoryInfo(str + "\\Temp\\Report");
      clsDefine.g_diTmpAI = new DirectoryInfo(str + "\\Temp\\AI");
    }

    private static void SetLocaleSetting()
    {
      try
      {
        clsData.LoadLang();
        clsDefine.g_strLanguageType = clsUtill.ReadINI("Option", "Lang", clsDefine.g_fiLangCfg.FullName).ToUpper();
        LocaleControl.getInstance().SetlanguageType(clsDefine.g_strLanguageType);
        clsDefine.g_iLanguageIndex = LocaleControl.getInstance().LoadLocale(clsDefine.g_strLocaleFPath);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([Program]SetLocaleSetting):" + ex.Message));
      }
    }

    private static void SetCompanyType()
    {
      try
      {
        clsDefine.g_fiDefaultCfg.Refresh();
        if (!clsDefine.g_fiDefaultCfg.Exists)
          return;
        string p_strValue = clsUtill.ReadINI("Option", "Company", clsDefine.g_fiDefaultCfg.FullName);
        clsDefine.g_enumCompany = !(p_strValue.ToUpper() == "DEFAULT") ? clsUtill.ConvertToEnumCompany(p_strValue) : clsHDMFLibDefine.Company.HDSolutions;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMoldFlow][Program]SetCompanyType):" + ex.Message));
      }
    }
  }
}
