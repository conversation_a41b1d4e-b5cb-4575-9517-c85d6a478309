﻿// Decompiled with JetBrains decompiler
// Type: Microsoft.Office.Interop.PowerPoint.Presentations
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using Microsoft.Office.Core;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

namespace Microsoft.Office.Interop.PowerPoint
{
  [CompilerGenerated]
  [Guid("91493462-5A91-11CF-8700-00AA0060263B")]
  [DefaultMember("Item")]
  [TypeIdentifier]
  [ComImport]
  public interface Presentations : Collection
  {
    [SpecialName]
    [MethodImpl(MethodCodeType = MethodCodeType.Runtime)]
    sealed extern void _VtblGap1_8();

    [DispId(2005)]
    [MethodImpl(MethodImplOptions.InternalCall, MethodCodeType = MethodCodeType.Runtime)]
    [return: MarshalAs(UnmanagedType.Interface)]
    Presentation Open(
      [MarshalAs(UnmanagedType.BStr), In] string FileName,
      [In] MsoTriState ReadOnly = MsoTriState.msoFalse,
      [In] MsoTriState Untitled = MsoTriState.msoFalse,
      [In] MsoTriState WithWindow = MsoTriState.msoTrue);
  }
}
