﻿// Decompiled with JetBrains decompiler
// Type: HDMFReport.clsInjCondTable
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using HDLocale;
using HDLog4Net;
using HDMoldFlowLibrary;
using Microsoft.CSharp.RuntimeBinder;
using Microsoft.Office.Core;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

namespace HDMFReport
{
  internal class clsInjCondTable
  {
    internal static void ExportInjCondTable(
      DataRow p_drStudy,
      List<DataRow> p_lst_drAllPhase,
      Dictionary<string, string> p_diValue,
      Dictionary<string, string> p_dicData)
    {
      uint lpdwProcessId = 0;
      string empty1 = string.Empty;
      string str1 = "사출조건표";
      string str2 = LocaleControl.getInstance().GetString("IDS_COOL");
      string str3 = LocaleControl.getInstance().GetString("IDS_FLOW");
      string str4 = LocaleControl.getInstance().GetString("IDS_WARP");
      FileInfo fileInfo1 = new FileInfo(clsReportDefine.g_diTemplate.ToString() + "\\사출조건표.xlsx");
      // ISSUE: variable of a compiler-generated type
      Application p_obj = (Application) null;
      if (p_drStudy["Mesh"] != DBNull.Value && p_drStudy["Mesh"].ToString() != "")
        empty1 = p_drStudy["Mesh"].ToString();
      string str5 = str1 + "_" + empty1;
      FileInfo fileInfo2 = new FileInfo(clsReportDefine.g_diTemplate.ToString() + "\\" + str5 + ".xlsx");
      if (!fileInfo2.Exists)
      {
        FileInfo fileInfo3 = new FileInfo(clsReportDefine.g_diTemplate.ToString() + "\\" + str5 + ".hde");
        if (fileInfo3.Exists)
          fileInfo3.CopyTo(fileInfo2.FullName);
      }
      fileInfo2.Refresh();
      if (!fileInfo2.Exists)
        return;
      try
      {
        string tableName = p_drStudy.Table.TableName;
        string str6 = p_dicData["ProductVolume[Log]"];
        string str7 = p_dicData["ColdFeedVolume[Log]"];
        string str8 = p_dicData["ColdMass[Log]"];
        string str9 = p_dicData["PartMass[Log]"];
        string str10 = p_diValue["InjMaxStroke"];
        string str11 = p_diValue["InjMaxRate"];
        string str12 = p_diValue["InjMaxPressure"];
        string str13 = p_diValue["InjMaxClamp"];
        string str14 = p_diValue["InjScrewDia"];
        string str15 = p_diValue["InjPreRatio"];
        string str16 = p_dicData["FillTime[Log]"];
        string str17 = p_dicData["SpruePressure[Log]"];
        string str18 = p_dicData["ClampForce[Log]"];
        string str19 = p_dicData["TemperatureAtFlowFront[Plot]"].Split('|')[1];
        string str20 = "0";
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperature[Log]")) && p_dicData["CircuitCoolantTemperature[Log]"] != null && p_dicData["CircuitCoolantTemperature[Log]"] != "")
        {
          string[] strArray = p_dicData["CircuitCoolantTemperature[Log]"].Split('|');
          if (strArray.Length > 1)
            str20 = strArray[1];
        }
        string str21 = p_dicData["CycleTime[Log]"];
        string[] strArray1 = new string[4]
        {
          p_diValue["InjRange1"],
          p_diValue["InjRange2"],
          p_diValue["InjRange3"],
          p_diValue["InjRange4"]
        };
        string str22 = p_diValue["InjRangeVP"];
        string empty2 = string.Empty;
        if (p_diValue.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "InjType")))
          empty2 = p_diValue["InjType"];
        string str23 = "";
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PressureAtVPSwitchover[IMG]")))
          str23 = p_dicData["PressureAtVPSwitchover[IMG]"];
        string str24 = "";
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PressureAtInjectionLocation_Inj[IMG]")))
          str24 = p_dicData["PressureAtInjectionLocation_Inj[IMG]"];
        string str25 = "";
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "RamSpeedRecommended[IMG]")))
          str25 = p_dicData["RamSpeedRecommended[IMG]"];
        string str26 = "";
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureAtFlowFront_Inj[IMG]")))
          str26 = p_dicData["TemperatureAtFlowFront_Inj[IMG]"];
        string str27 = p_dicData["Manufacturer[MF]"];
        string str28 = p_dicData["TradeName[MF]"];
        string str29 = p_dicData["FamilyAbbreviation[MF]"];
        string str30 = "";
        string str31 = "";
        switch (clsHDMFLib.GetCoolType())
        {
          case 0:
            str30 = str2;
            str31 = "Inj+Pack+Cooling time:Specified";
            break;
          case 1:
            str30 = str3;
            str31 = "Cooling Time:Specified";
            break;
          case 2:
            str30 = str3 + " + " + str4;
            str31 = "Cooling Time:Specified";
            break;
          case 3:
            str30 = str2 + " + " + str3 + " + " + str4;
            str31 = "Inj+Pack+Cooling time:Specified";
            break;
          case 4:
            str30 = str2 + " + " + str3;
            str31 = "Inj+Pack+Cooling time:Specified";
            break;
        }
        string str32 = p_dicData["CoolingTime[MF]"];
        string str33 = p_dicData["MeltTemp[MF]"];
        string str34 = p_dicData["MoldTemp[MF]"];
        string[] strArray2 = new string[3];
        string[] strArray3 = new string[3];
        int holdingControlType = clsHDMFLib.GetPackHoldingControlType();
        string str35 = holdingControlType != 1 ? "Packing pressure vs time" : "Filling pressure vs time";
        int num1 = 0;
        List<string> stringList1 = new List<string>();
        List<string> stringList2 = new List<string>();
        List<string> holdingControlData = clsHDMFLib.GetPackHoldingControlData(holdingControlType);
        for (int index = 0; index < holdingControlData.Count && index <= 6; ++index)
        {
          if (index % 2 == 1)
          {
            string[] strArray4 = holdingControlData[index].Split('|');
            stringList1.Add(strArray4[0]);
            stringList2.Add(strArray4[1]);
            ++num1;
          }
        }
        string str36 = "";
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TotalMass(FillPhase)[Log]")))
          str36 = p_dicData["TotalMass(FillPhase)[Log]"];
        string str37 = "";
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TotalMass(PackPhase)[Log]")))
          str37 = p_dicData["TotalMass(PackPhase)[Log]"];
        string str38 = "";
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Totalweight(FillPhase)[Log]")))
          str38 = p_dicData["Totalweight(FillPhase)[Log]"];
        string str39 = "";
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Totalweight(PackPhase)[Log]")))
          str39 = p_dicData["Totalweight(PackPhase)[Log]"];
        string str40 = "";
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MeltDensity[MF]")))
          str40 = p_dicData["MeltDensity[MF]"];
        string str41 = "";
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SolidDensity[MF]")))
          str41 = p_dicData["SolidDensity[MF]"];
        if (fileInfo2.Exists)
        {
          int num2 = 1;
          FileInfo fileInfo4;
          while (true)
          {
            fileInfo4 = new FileInfo(clsReportDefine.g_diProject.Parent.FullName + "\\" + clsReportDefine.g_diProject.Name + "_Export\\" + p_drStudy["Name"] + "_사출조건표_Rev" + (object) num2 + ".pdf");
            if (fileInfo4.Exists)
              ++num2;
            else
              break;
          }
          // ISSUE: variable of a compiler-generated type
          Application instance = (Application) Activator.CreateInstance(Marshal.GetTypeFromCLSID(new Guid("00024500-0000-0000-C000-000000000046")));
          instance.Visible = false;
          p_obj = instance;
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          Workbook workbook = p_obj.Workbooks.Open(fileInfo2.FullName, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing);
          // ISSUE: reference to a compiler-generated field
          if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__0 == null)
          {
            // ISSUE: reference to a compiler-generated field
            clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__0 = CallSite<Func<CallSite, object, Worksheet>>.Create(Binder.Convert(CSharpBinderFlags.ConvertExplicit, typeof (Worksheet), typeof (clsInjCondTable)));
          }
          // ISSUE: reference to a compiler-generated field
          // ISSUE: reference to a compiler-generated field
          // ISSUE: variable of a compiler-generated type
          Worksheet worksheet1 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__0.Target((CallSite) clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__0, workbook.Sheets[(object) 1]);
          worksheet1.Cells[(object) 5, (object) 1] = (object) tableName;
          worksheet1.Cells[(object) 5, (object) 7] = (object) (str6 + " cm3");
          worksheet1.Cells[(object) 5, (object) 9] = (object) str7;
          worksheet1.Cells[(object) 5, (object) 10] = (object) str8;
          worksheet1.Cells[(object) 5, (object) 11] = (object) str9;
          worksheet1.Cells[(object) 8, (object) 3] = (object) str10;
          worksheet1.Cells[(object) 8, (object) 7] = (object) str11;
          worksheet1.Cells[(object) 8, (object) 11] = (object) str14;
          worksheet1.Cells[(object) 9, (object) 3] = (object) str12;
          worksheet1.Cells[(object) 9, (object) 7] = (object) str13;
          worksheet1.Cells[(object) 9, (object) 11] = (object) str15;
          worksheet1.Cells[(object) 29, (object) 7] = (object) str16;
          worksheet1.Cells[(object) 29, (object) 9] = (object) str17;
          worksheet1.Cells[(object) 29, (object) 11] = (object) str18;
          worksheet1.Cells[(object) 31, (object) 7] = (object) str19;
          worksheet1.Cells[(object) 31, (object) 9] = (object) str20;
          worksheet1.Cells[(object) 31, (object) 11] = (object) str21;
          worksheet1.Cells[(object) 13, (object) 19] = (object) strArray1[0];
          worksheet1.Cells[(object) 13, (object) 21] = (object) strArray1[1];
          worksheet1.Cells[(object) 13, (object) 23] = (object) strArray1[2];
          worksheet1.Cells[(object) 13, (object) 25] = (object) strArray1[3];
          double num3 = Math.Pow(Convert.ToDouble(str14) * 0.1, 2.0) / 4.0 * Math.PI;
          double num4 = Math.Ceiling(Convert.ToDouble(str6) * 0.05 / num3);
          double num5 = Math.Ceiling(Convert.ToDouble(str22));
          if (num4 < num5)
            worksheet1.Cells[(object) 13, (object) 27] = (object) num5.ToString();
          else
            worksheet1.Cells[(object) 13, (object) 27] = (object) num4.ToString();
          if (str23 != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            Microsoft.Office.Interop.Excel.Range range = worksheet1.get_Range((object) "A33", (object) "F45");
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__13 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__13 = CallSite<Action<CallSite, Shapes, string, MsoTriState, MsoTriState, object, object, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.ResultDiscarded, "AddPicture", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[8]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Action<CallSite, Shapes, string, MsoTriState, MsoTriState, object, object, object, object> target1 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__13.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Action<CallSite, Shapes, string, MsoTriState, MsoTriState, object, object, object, object>> p13 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__13;
            // ISSUE: variable of a compiler-generated type
            Shapes shapes = worksheet1.Shapes;
            string str42 = str23;
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__3 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__3 = CallSite<Func<CallSite, object, int, object>>.Create(Binder.BinaryOperation(CSharpBinderFlags.None, ExpressionType.Add, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, object, int, object> target2 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__3.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, object, int, object>> p3 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__3;
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__2 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__2 = CallSite<Func<CallSite, Type, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "Parse", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.IsStaticType, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, Type, object, object> target3 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__2.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, Type, object, object>> p2 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__2;
            Type type1 = typeof (float);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__1 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__1 = CallSite<Func<CallSite, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "ToString", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[1]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            object obj1 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__1.Target((CallSite) clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__1, range.Left);
            object obj2 = target3((CallSite) p2, type1, obj1);
            object obj3 = target2((CallSite) p3, obj2, 2);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__6 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__6 = CallSite<Func<CallSite, object, int, object>>.Create(Binder.BinaryOperation(CSharpBinderFlags.None, ExpressionType.Add, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, object, int, object> target4 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__6.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, object, int, object>> p6 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__6;
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__5 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__5 = CallSite<Func<CallSite, Type, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "Parse", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.IsStaticType, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, Type, object, object> target5 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__5.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, Type, object, object>> p5 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__5;
            Type type2 = typeof (float);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__4 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__4 = CallSite<Func<CallSite, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "ToString", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[1]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            object obj4 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__4.Target((CallSite) clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__4, range.Top);
            object obj5 = target5((CallSite) p5, type2, obj4);
            object obj6 = target4((CallSite) p6, obj5, 1);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__9 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__9 = CallSite<Func<CallSite, object, double, object>>.Create(Binder.BinaryOperation(CSharpBinderFlags.None, ExpressionType.Subtract, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, object, double, object> target6 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__9.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, object, double, object>> p9 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__9;
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__8 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__8 = CallSite<Func<CallSite, Type, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "Parse", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.IsStaticType, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, Type, object, object> target7 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__8.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, Type, object, object>> p8 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__8;
            Type type3 = typeof (float);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__7 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__7 = CallSite<Func<CallSite, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "ToString", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[1]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            object obj7 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__7.Target((CallSite) clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__7, range.Width);
            object obj8 = target7((CallSite) p8, type3, obj7);
            object obj9 = target6((CallSite) p9, obj8, 2.75);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__12 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__12 = CallSite<Func<CallSite, object, double, object>>.Create(Binder.BinaryOperation(CSharpBinderFlags.None, ExpressionType.Subtract, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, object, double, object> target8 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__12.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, object, double, object>> p12 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__12;
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__11 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__11 = CallSite<Func<CallSite, Type, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "Parse", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.IsStaticType, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, Type, object, object> target9 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__11.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, Type, object, object>> p11 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__11;
            Type type4 = typeof (float);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__10 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__10 = CallSite<Func<CallSite, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "ToString", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[1]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            object obj10 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__10.Target((CallSite) clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__10, range.Height);
            object obj11 = target9((CallSite) p11, type4, obj10);
            object obj12 = target8((CallSite) p12, obj11, 2.5);
            target1((CallSite) p13, shapes, str42, MsoTriState.msoFalse, MsoTriState.msoTrue, obj3, obj6, obj9, obj12);
          }
          if (str24 != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            Microsoft.Office.Interop.Excel.Range range = worksheet1.get_Range((object) "G33", (object) "L45");
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__26 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__26 = CallSite<Action<CallSite, Shapes, string, MsoTriState, MsoTriState, object, object, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.ResultDiscarded, "AddPicture", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[8]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Action<CallSite, Shapes, string, MsoTriState, MsoTriState, object, object, object, object> target10 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__26.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Action<CallSite, Shapes, string, MsoTriState, MsoTriState, object, object, object, object>> p26 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__26;
            // ISSUE: variable of a compiler-generated type
            Shapes shapes = worksheet1.Shapes;
            string str43 = str24;
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__16 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__16 = CallSite<Func<CallSite, object, int, object>>.Create(Binder.BinaryOperation(CSharpBinderFlags.None, ExpressionType.Add, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, object, int, object> target11 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__16.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, object, int, object>> p16 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__16;
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__15 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__15 = CallSite<Func<CallSite, Type, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "Parse", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.IsStaticType, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, Type, object, object> target12 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__15.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, Type, object, object>> p15 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__15;
            Type type5 = typeof (float);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__14 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__14 = CallSite<Func<CallSite, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "ToString", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[1]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            object obj13 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__14.Target((CallSite) clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__14, range.Left);
            object obj14 = target12((CallSite) p15, type5, obj13);
            object obj15 = target11((CallSite) p16, obj14, 2);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__19 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__19 = CallSite<Func<CallSite, object, int, object>>.Create(Binder.BinaryOperation(CSharpBinderFlags.None, ExpressionType.Add, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, object, int, object> target13 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__19.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, object, int, object>> p19 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__19;
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__18 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__18 = CallSite<Func<CallSite, Type, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "Parse", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.IsStaticType, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, Type, object, object> target14 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__18.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, Type, object, object>> p18 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__18;
            Type type6 = typeof (float);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__17 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__17 = CallSite<Func<CallSite, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "ToString", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[1]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            object obj16 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__17.Target((CallSite) clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__17, range.Top);
            object obj17 = target14((CallSite) p18, type6, obj16);
            object obj18 = target13((CallSite) p19, obj17, 1);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__22 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__22 = CallSite<Func<CallSite, object, double, object>>.Create(Binder.BinaryOperation(CSharpBinderFlags.None, ExpressionType.Subtract, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, object, double, object> target15 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__22.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, object, double, object>> p22 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__22;
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__21 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__21 = CallSite<Func<CallSite, Type, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "Parse", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.IsStaticType, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, Type, object, object> target16 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__21.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, Type, object, object>> p21 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__21;
            Type type7 = typeof (float);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__20 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__20 = CallSite<Func<CallSite, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "ToString", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[1]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            object obj19 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__20.Target((CallSite) clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__20, range.Width);
            object obj20 = target16((CallSite) p21, type7, obj19);
            object obj21 = target15((CallSite) p22, obj20, 2.75);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__25 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__25 = CallSite<Func<CallSite, object, double, object>>.Create(Binder.BinaryOperation(CSharpBinderFlags.None, ExpressionType.Subtract, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, object, double, object> target17 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__25.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, object, double, object>> p25 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__25;
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__24 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__24 = CallSite<Func<CallSite, Type, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "Parse", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.IsStaticType, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, Type, object, object> target18 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__24.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, Type, object, object>> p24 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__24;
            Type type8 = typeof (float);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__23 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__23 = CallSite<Func<CallSite, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "ToString", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[1]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            object obj22 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__23.Target((CallSite) clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__23, range.Height);
            object obj23 = target18((CallSite) p24, type8, obj22);
            object obj24 = target17((CallSite) p25, obj23, 2.5);
            target10((CallSite) p26, shapes, str43, MsoTriState.msoFalse, MsoTriState.msoTrue, obj15, obj18, obj21, obj24);
          }
          if (str25 != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            Microsoft.Office.Interop.Excel.Range range = worksheet1.get_Range((object) "A46", (object) "F58");
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__39 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__39 = CallSite<Action<CallSite, Shapes, string, MsoTriState, MsoTriState, object, object, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.ResultDiscarded, "AddPicture", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[8]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Action<CallSite, Shapes, string, MsoTriState, MsoTriState, object, object, object, object> target19 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__39.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Action<CallSite, Shapes, string, MsoTriState, MsoTriState, object, object, object, object>> p39 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__39;
            // ISSUE: variable of a compiler-generated type
            Shapes shapes = worksheet1.Shapes;
            string str44 = str25;
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__29 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__29 = CallSite<Func<CallSite, object, int, object>>.Create(Binder.BinaryOperation(CSharpBinderFlags.None, ExpressionType.Add, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, object, int, object> target20 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__29.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, object, int, object>> p29 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__29;
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__28 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__28 = CallSite<Func<CallSite, Type, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "Parse", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.IsStaticType, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, Type, object, object> target21 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__28.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, Type, object, object>> p28 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__28;
            Type type9 = typeof (float);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__27 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__27 = CallSite<Func<CallSite, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "ToString", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[1]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            object obj25 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__27.Target((CallSite) clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__27, range.Left);
            object obj26 = target21((CallSite) p28, type9, obj25);
            object obj27 = target20((CallSite) p29, obj26, 2);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__32 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__32 = CallSite<Func<CallSite, object, int, object>>.Create(Binder.BinaryOperation(CSharpBinderFlags.None, ExpressionType.Add, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, object, int, object> target22 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__32.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, object, int, object>> p32 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__32;
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__31 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__31 = CallSite<Func<CallSite, Type, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "Parse", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.IsStaticType, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, Type, object, object> target23 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__31.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, Type, object, object>> p31 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__31;
            Type type10 = typeof (float);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__30 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__30 = CallSite<Func<CallSite, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "ToString", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[1]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            object obj28 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__30.Target((CallSite) clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__30, range.Top);
            object obj29 = target23((CallSite) p31, type10, obj28);
            object obj30 = target22((CallSite) p32, obj29, 2);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__35 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__35 = CallSite<Func<CallSite, object, double, object>>.Create(Binder.BinaryOperation(CSharpBinderFlags.None, ExpressionType.Subtract, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, object, double, object> target24 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__35.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, object, double, object>> p35 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__35;
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__34 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__34 = CallSite<Func<CallSite, Type, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "Parse", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.IsStaticType, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, Type, object, object> target25 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__34.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, Type, object, object>> p34 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__34;
            Type type11 = typeof (float);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__33 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__33 = CallSite<Func<CallSite, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "ToString", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[1]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            object obj31 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__33.Target((CallSite) clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__33, range.Width);
            object obj32 = target25((CallSite) p34, type11, obj31);
            object obj33 = target24((CallSite) p35, obj32, 2.75);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__38 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__38 = CallSite<Func<CallSite, object, double, object>>.Create(Binder.BinaryOperation(CSharpBinderFlags.None, ExpressionType.Subtract, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, object, double, object> target26 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__38.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, object, double, object>> p38 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__38;
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__37 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__37 = CallSite<Func<CallSite, Type, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "Parse", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.IsStaticType, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, Type, object, object> target27 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__37.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, Type, object, object>> p37 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__37;
            Type type12 = typeof (float);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__36 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__36 = CallSite<Func<CallSite, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "ToString", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[1]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            object obj34 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__36.Target((CallSite) clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__36, range.Height);
            object obj35 = target27((CallSite) p37, type12, obj34);
            object obj36 = target26((CallSite) p38, obj35, 2.5);
            target19((CallSite) p39, shapes, str44, MsoTriState.msoFalse, MsoTriState.msoTrue, obj27, obj30, obj33, obj36);
          }
          if (str26 != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            Microsoft.Office.Interop.Excel.Range range = worksheet1.get_Range((object) "G46", (object) "L58");
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__52 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__52 = CallSite<Action<CallSite, Shapes, string, MsoTriState, MsoTriState, object, object, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.ResultDiscarded, "AddPicture", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[8]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Action<CallSite, Shapes, string, MsoTriState, MsoTriState, object, object, object, object> target28 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__52.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Action<CallSite, Shapes, string, MsoTriState, MsoTriState, object, object, object, object>> p52 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__52;
            // ISSUE: variable of a compiler-generated type
            Shapes shapes = worksheet1.Shapes;
            string str45 = str26;
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__42 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__42 = CallSite<Func<CallSite, object, int, object>>.Create(Binder.BinaryOperation(CSharpBinderFlags.None, ExpressionType.Add, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, object, int, object> target29 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__42.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, object, int, object>> p42 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__42;
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__41 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__41 = CallSite<Func<CallSite, Type, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "Parse", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.IsStaticType, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, Type, object, object> target30 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__41.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, Type, object, object>> p41 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__41;
            Type type13 = typeof (float);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__40 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__40 = CallSite<Func<CallSite, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "ToString", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[1]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            object obj37 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__40.Target((CallSite) clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__40, range.Left);
            object obj38 = target30((CallSite) p41, type13, obj37);
            object obj39 = target29((CallSite) p42, obj38, 2);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__45 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__45 = CallSite<Func<CallSite, object, int, object>>.Create(Binder.BinaryOperation(CSharpBinderFlags.None, ExpressionType.Add, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, object, int, object> target31 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__45.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, object, int, object>> p45 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__45;
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__44 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__44 = CallSite<Func<CallSite, Type, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "Parse", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.IsStaticType, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, Type, object, object> target32 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__44.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, Type, object, object>> p44 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__44;
            Type type14 = typeof (float);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__43 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__43 = CallSite<Func<CallSite, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "ToString", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[1]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            object obj40 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__43.Target((CallSite) clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__43, range.Top);
            object obj41 = target32((CallSite) p44, type14, obj40);
            object obj42 = target31((CallSite) p45, obj41, 2);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__48 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__48 = CallSite<Func<CallSite, object, double, object>>.Create(Binder.BinaryOperation(CSharpBinderFlags.None, ExpressionType.Subtract, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, object, double, object> target33 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__48.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, object, double, object>> p48 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__48;
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__47 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__47 = CallSite<Func<CallSite, Type, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "Parse", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.IsStaticType, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, Type, object, object> target34 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__47.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, Type, object, object>> p47 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__47;
            Type type15 = typeof (float);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__46 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__46 = CallSite<Func<CallSite, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "ToString", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[1]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            object obj43 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__46.Target((CallSite) clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__46, range.Width);
            object obj44 = target34((CallSite) p47, type15, obj43);
            object obj45 = target33((CallSite) p48, obj44, 2.75);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__51 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__51 = CallSite<Func<CallSite, object, double, object>>.Create(Binder.BinaryOperation(CSharpBinderFlags.None, ExpressionType.Subtract, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, object, double, object> target35 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__51.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, object, double, object>> p51 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__51;
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__50 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__50 = CallSite<Func<CallSite, Type, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "Parse", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.IsStaticType, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, Type, object, object> target36 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__50.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, Type, object, object>> p50 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__50;
            Type type16 = typeof (float);
            // ISSUE: reference to a compiler-generated field
            if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__49 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__49 = CallSite<Func<CallSite, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "ToString", (IEnumerable<Type>) null, typeof (clsInjCondTable), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[1]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            object obj46 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__49.Target((CallSite) clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__49, range.Height);
            object obj47 = target36((CallSite) p50, type16, obj46);
            object obj48 = target35((CallSite) p51, obj47, 2.5);
            target28((CallSite) p52, shapes, str45, MsoTriState.msoFalse, MsoTriState.msoTrue, obj39, obj42, obj45, obj48);
          }
          // ISSUE: reference to a compiler-generated field
          if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__53 == null)
          {
            // ISSUE: reference to a compiler-generated field
            clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__53 = CallSite<Func<CallSite, object, Worksheet>>.Create(Binder.Convert(CSharpBinderFlags.ConvertExplicit, typeof (Worksheet), typeof (clsInjCondTable)));
          }
          // ISSUE: reference to a compiler-generated field
          // ISSUE: reference to a compiler-generated field
          // ISSUE: variable of a compiler-generated type
          Worksheet worksheet2 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__53.Target((CallSite) clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__53, workbook.Sheets[(object) 2]);
          worksheet2.Cells[(object) 9, (object) 2] = (object) str27;
          worksheet2.Cells[(object) 9, (object) 4] = (object) str28;
          worksheet2.Cells[(object) 9, (object) 6] = (object) str29;
          worksheet2.Cells[(object) 12, (object) 6] = (object) str30;
          worksheet2.Cells[(object) 13, (object) 2] = (object) str31;
          worksheet2.Cells[(object) 13, (object) 6] = (object) str32;
          worksheet2.Cells[(object) 14, (object) 6] = (object) str33;
          worksheet2.Cells[(object) 15, (object) 6] = (object) str34;
          worksheet2.Cells[(object) 39, (object) 2] = (object) str35;
          for (int index = 0; index < stringList1.Count; ++index)
          {
            worksheet2.Cells[(object) (40 + index), (object) 6] = (object) stringList1[index];
            worksheet2.Cells[(object) (40 + index), (object) 9] = (object) stringList2[index];
          }
          for (int index = 0; index < p_lst_drAllPhase.Count; ++index)
          {
            if (!(p_lst_drAllPhase[index].Table.TableName != "FillPhase"))
            {
              for (int columnIndex = 0; columnIndex < p_lst_drAllPhase[index].Table.Columns.Count; ++columnIndex)
                worksheet2.Cells[(object) (8 + index), (object) (14 + columnIndex)] = p_lst_drAllPhase[index][columnIndex];
            }
          }
          string empty3 = string.Empty;
          string str46 = !(empty2 == "0") ? LocaleControl.getInstance().GetString("IDS_MOLD_DATA") : LocaleControl.getInstance().GetString("IDS_PRODUCT_DATA");
          if (empty1 == "3D")
          {
            worksheet2.Cells[(object) 35, (object) 28] = (object) str36;
            worksheet2.Cells[(object) 38, (object) 28] = (object) str37;
            worksheet2.Cells[(object) 49, (object) 28] = (object) str46;
            worksheet2.Cells[(object) 52, (object) 28] = (object) str40;
            worksheet2.Cells[(object) 54, (object) 28] = (object) str41;
          }
          else
          {
            worksheet2.Cells[(object) 27, (object) 27] = (object) str46;
            worksheet2.Cells[(object) 30, (object) 27] = (object) str40;
            worksheet2.Cells[(object) 32, (object) 27] = (object) str41;
            worksheet2.Cells[(object) 58, (object) 27] = (object) str38;
            worksheet2.Cells[(object) 60, (object) 27] = (object) str39;
          }
          // ISSUE: reference to a compiler-generated field
          if (clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__54 == null)
          {
            // ISSUE: reference to a compiler-generated field
            clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__54 = CallSite<Func<CallSite, object, Worksheet>>.Create(Binder.Convert(CSharpBinderFlags.ConvertExplicit, typeof (Worksheet), typeof (clsInjCondTable)));
          }
          // ISSUE: reference to a compiler-generated field
          // ISSUE: reference to a compiler-generated field
          // ISSUE: variable of a compiler-generated type
          Worksheet worksheet3 = clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__54.Target((CallSite) clsInjCondTable.\u003C\u003Eo__0.\u003C\u003Ep__54, workbook.Sheets[(object) 1]);
          // ISSUE: reference to a compiler-generated method
          worksheet3.ExportAsFixedFormat(XlFixedFormatType.xlTypePDF, (object) fileInfo4.FullName, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing);
          // ISSUE: reference to a compiler-generated method
          workbook.Close((object) false, Type.Missing, Type.Missing);
          int windowThreadProcessId = (int) clsReportUtill.GetWindowThreadProcessId(new IntPtr(p_obj.Hwnd), out lpdwProcessId);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsInjCondTable]ExportInjCondTable):" + ex.Message));
      }
      if (p_obj == null)
        return;
      // ISSUE: reference to a compiler-generated method
      p_obj.Quit();
      clsReportUtill.ReleaseComObject((object) p_obj);
      if (lpdwProcessId == 0U)
        return;
      Process processById = Process.GetProcessById((int) lpdwProcessId);
      processById.CloseMainWindow();
      processById.Refresh();
      processById.Kill();
    }
  }
}
