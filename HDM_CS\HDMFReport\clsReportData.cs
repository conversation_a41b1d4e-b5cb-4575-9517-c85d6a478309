﻿// Decompiled with JetBrains decompiler
// Type: HDMFReport.clsReportData
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using HDLog4Net;
using HDMoldFlowLibrary;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;

namespace HDMFReport
{
  internal class clsReportData
  {
    public static DataTable GetReportViewDataFromIni(
      clsHDMFLibDefine.Company p_enumCompany,
      string p_strLanguageType = "KOR")
    {
      DataTable reportViewDataFromIni = (DataTable) null;
      try
      {
        FileInfo fileInfo;
        if (p_strLanguageType == "KOR")
        {
          fileInfo = new FileInfo(clsReportDefine.g_diCfg.FullName + "\\ReportView\\" + p_enumCompany.ToString() + "\\ReportView.ini");
        }
        else
        {
          fileInfo = new FileInfo(clsReportDefine.g_diCfg.FullName + "\\ReportView\\" + p_enumCompany.ToString() + "\\ReportView_" + p_strLanguageType + ".ini");
          if (!fileInfo.Exists)
            fileInfo = new FileInfo(clsReportDefine.g_diCfg.FullName + "\\ReportView\\" + p_enumCompany.ToString() + "\\ReportView.ini");
        }
        reportViewDataFromIni = clsReportUtill.GetINIData(fileInfo.FullName);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsBase]SetDefault):" + ex.Message));
      }
      return reportViewDataFromIni;
    }

    public static FileInfo GetTemplate(
      clsHDMFLibDefine.Company p_enumCompany,
      string p_strLangType = "KOR",
      int p_iReportType = 1)
    {
      FileInfo template = (FileInfo) null;
      string str = clsReportDefine.g_diTemplate.FullName + "\\" + p_enumCompany.ToString() + "\\Template";
      switch (p_iReportType)
      {
        case 2:
          str += "_2-Case";
          break;
        case 3:
          str += "_3-Case";
          break;
      }
      FileInfo fileInfo;
      if (p_strLangType == "KOR")
      {
        fileInfo = new FileInfo(str + ".hdp");
      }
      else
      {
        fileInfo = new FileInfo(str + "_" + p_strLangType + ".hdp");
        if (!fileInfo.Exists)
          fileInfo = new FileInfo(str + ".hdp");
        else
          str = str + "_" + p_strLangType;
      }
      if (fileInfo.Exists)
      {
        template = new FileInfo(str + "." + clsReportDefine.g_dicExtension["PowerPoint"]);
        if (!template.Exists)
          fileInfo.CopyTo(template.FullName);
      }
      return template;
    }

    public static Dictionary<string, string> GetReportData(DataRow p_drStudy)
    {
      string empty1 = string.Empty;
      List<string> stringList = new List<string>();
      Dictionary<string, string> dictionary1 = new Dictionary<string, string>();
      Dictionary<string, string> reportData = new Dictionary<string, string>();
      try
      {
        FileInfo fileInfo = new FileInfo(clsReportDefine.g_diTmpReport.ToString() + "\\" + p_drStudy["Name"].ToString() + "\\ALog.log");
        if (!fileInfo.Exists)
          stringList = clsHDMFLib.ExportAnalysisLog(fileInfo.FullName);
        else
          stringList.AddRange((IEnumerable<string>) File.ReadAllLines(fileInfo.FullName, Encoding.Default));
        List<DataRow> p_lst_drFillPhase1 = new List<DataRow>((IEnumerable<DataRow>) clsHDMFLib.GetFillingPhaseData(stringList).Rows.Cast<DataRow>().ToArray<DataRow>());
        List<DataRow> p_lst_drFillPhase2 = new List<DataRow>((IEnumerable<DataRow>) clsHDMFLib.GetPackingPhaseData(stringList).Rows.Cast<DataRow>().ToArray<DataRow>());
        List<DataRow> p_lst_drAllPhase = new List<DataRow>();
        p_lst_drAllPhase.AddRange((IEnumerable<DataRow>) p_lst_drFillPhase1.ToArray());
        p_lst_drAllPhase.AddRange((IEnumerable<DataRow>) p_lst_drFillPhase2.ToArray());
        Dictionary<string, string> injectionData = clsHDMFLib.GetInjectionData();
        reportData.Add("InjMaxStroke", injectionData["MaxStroke"]);
        reportData.Add("InjMaxRate", injectionData["MaxRate"]);
        reportData.Add("InjScrewDia", injectionData["ScrewDia"]);
        reportData.Add("InjMaxPressure", injectionData["MaxPressure"]);
        reportData.Add("InjMaxClamp", injectionData["MaxClamp"]);
        reportData.Add("FillingTime", clsHDMFLib.GetVPSwitchOverTimeFromLog(p_lst_drFillPhase1));
        double num1 = Math.Round(clsHDMFLibUtil.ConvertToDouble(clsHDMFLib.GetPhaseZeroPressureTimeFromLog(p_lst_drAllPhase)), 2);
        double num2 = Math.Round(clsHDMFLibUtil.ConvertToDouble(reportData["FillingTime"]), 2);
        Dictionary<string, string> dictionary2 = reportData;
        double num3 = num1 - num2;
        string str = num3.ToString();
        dictionary2.Add("PackingTime", str);
        string empty2 = string.Empty;
        if (p_lst_drFillPhase2.Count != 0)
        {
          num3 = Math.Round(clsHDMFLibUtil.ConvertToDouble(clsHDMFLib.GetFillLastTimeFromLog(p_lst_drFillPhase2)), 2) - Math.Round(clsHDMFLibUtil.ConvertToDouble(clsHDMFLib.GetPhaseZeroPressureTimeFromLog(p_lst_drAllPhase)), 2);
          empty2 = num3.ToString();
        }
        reportData.Add("CoolingTime", empty2);
        reportData.Add("Schizonepeta", clsHDMFLib.GetMoldOpenTime());
        reportData.Add("MoldTemperature", clsHDMFLib.GetCavitySurfaceTemperatureFromLog(3, stringList));
        reportData.Add("VolumetricShrinkage", clsHDMFLib.GetVolumetricShrinkageFromLog(3, stringList));
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsReportData]GetReportData):" + ex.Message));
      }
      return reportData;
    }

    public static Dictionary<string, string> GetReportDataSL(DataRow p_drStudy)
    {
      string empty = string.Empty;
      List<string> p_lst_strAnalysisLog = new List<string>();
      Dictionary<string, string> dictionary = new Dictionary<string, string>();
      Dictionary<string, string> reportDataSl = new Dictionary<string, string>();
      try
      {
        FileInfo fileInfo = new FileInfo(clsReportDefine.g_diTmpReport.ToString() + "\\" + p_drStudy["Name"].ToString() + "\\ALog.log");
        if (!fileInfo.Exists)
          p_lst_strAnalysisLog = clsHDMFLib.ExportAnalysisLog(fileInfo.FullName);
        else
          p_lst_strAnalysisLog.AddRange((IEnumerable<string>) File.ReadAllLines(fileInfo.FullName, Encoding.Default));
        reportDataSl = clsHDMFLibSL.GetAnalysisToIni(p_lst_strAnalysisLog);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsReportData]GetReportDataSL):" + ex.Message));
      }
      return reportDataSl;
    }
  }
}
