# 3D Surface Sink Mark Depth Calculator - Python Conversion

## Overview

This document describes the Python conversion of the VBScript file `3DSurfaceSinkMarkDepth.vbs`. The Python version provides the same functionality with improved error handling, better code structure, and enhanced maintainability.

## Files Created

### 1. `3DSurfaceSinkMarkDepth.py`
Main script that calculates sink mark depth on 3D part surfaces based on volumetric shrinkage results and local thickness.

### 2. `moldflow_synergy_wrapper.py`
Python wrapper for the Moldflow Synergy COM API, providing:
- Simplified connection management
- Error handling for COM operations
- Python-friendly data type conversions
- Common Synergy operations

### 3. `moldflow_mesh_utils.py`
Mesh processing utilities including:
- `Node` class for mesh nodes
- `Tet` class for tetrahedral elements
- `MeshProcessor` class for UDM file processing and mesh analysis

## Dependencies

### Required Python Packages
```bash
pip install pywin32 numpy
```

### System Requirements
- Windows operating system
- Autodesk Moldflow Synergy installed
- Python 3.7 or higher

## Usage

### Basic Usage
```python
from 3DSurfaceSinkMarkDepth import SinkMarkCalculator

# Create calculator instance
calculator = SinkMarkCalculator()

# Run the calculation
success = calculator.run()

if success:
    print("Sink mark depth calculation completed successfully!")
else:
    print("Calculation failed - check logs for details")
```

### Command Line Usage
```bash
python 3DSurfaceSinkMarkDepth.py
```

## Key Differences from VBScript Version

### 1. **Improved Error Handling**
- Python version uses try-catch blocks with specific exception handling
- Detailed logging for debugging and troubleshooting
- Graceful fallbacks for non-critical failures

### 2. **Object-Oriented Design**
- Main functionality encapsulated in `SinkMarkCalculator` class
- Separate classes for different responsibilities (Synergy wrapper, mesh processing)
- Better code organization and reusability

### 3. **Type Hints and Documentation**
- Full type hints for better IDE support and code clarity
- Comprehensive docstrings for all classes and methods
- Clear parameter and return value documentation

### 4. **Modern Python Features**
- Use of `pathlib` for file operations
- Context managers for file handling
- List comprehensions and generator expressions where appropriate

### 5. **Enhanced Logging**
- Structured logging with different levels (INFO, WARNING, ERROR)
- Progress reporting with percentage completion
- Detailed error messages for troubleshooting

## Algorithm Overview

The script performs the following steps:

1. **Initialize Synergy Connection**
   - Connect to Moldflow Synergy via COM interface
   - Validate that a 3D study is open
   - Determine unit system (English/Metric)

2. **Read Volumetric Shrinkage Data**
   - Extract average volumetric shrinkage results from the study
   - Support both overmolding (11619) and first shot (1629) flow results

3. **Process Mesh Data**
   - Export model to UDM format
   - Parse mesh geometry and connectivity
   - Build node and element data structures

4. **Calculate Thickness**
   - Identify surface elements
   - Calculate local thickness for each surface element
   - (Note: Simplified implementation in current version)

5. **Compute Sink Mark Depth**
   - For each surface element:
     - Average volumetric shrinkage from connected nodes
     - Convert to linear shrinkage: `linear_shrink = 1 - (1 - vol_shrink)^(1/3)`
     - Calculate sink depth: `sink_depth = 0.5 * thickness * linear_shrink`

6. **Create Result Plot**
   - Generate new user plot in Synergy
   - Display sink mark depth results on the model

## Configuration Options

### Unit System
The script automatically detects the unit system from Synergy:
- **English**: inches (6 decimal places)
- **Metric**: millimeters (4 decimal places)

### Result IDs
The script searches for volumetric shrinkage results in this order:
1. Overmolding flow result (ID: 11619)
2. First shot flow result (ID: 1629)

## Limitations and Known Issues

### 1. **Simplified Thickness Calculation**
The current Python version uses a simplified thickness calculation method. The original VBScript performs complex surface mesh analysis by:
- Exporting surface mesh to Patran format
- Importing as a new Synergy study
- Using Synergy's thickness diagnosis tools
- Mapping results back to original elements

**Recommendation**: Implement full thickness calculation for production use.

### 2. **Surface Element Detection**
The current implementation uses simplified surface element detection. A full implementation would require:
- Complex neighbor analysis
- Face matching algorithms
- Proper surface identification

### 3. **COM Interface Dependencies**
The script requires:
- Moldflow Synergy to be installed and accessible
- Proper COM registration
- Windows environment

## Troubleshooting

### Common Issues

1. **"Required dependency missing" Error**
   ```bash
   pip install pywin32 numpy
   ```

2. **"Unable to connect to Moldflow Synergy" Error**
   - Ensure Moldflow Synergy is installed
   - Check that Synergy is not already running with a different user
   - Verify COM registration

3. **"No study is open" Error**
   - Open a 3D study in Moldflow Synergy before running the script
   - Ensure the study contains flow analysis results

4. **"Unable to find Average Volumetric Shrinkage result" Error**
   - Run a flow analysis in the study
   - Verify that volumetric shrinkage results are available

### Debug Mode
Enable detailed logging by modifying the logging level:
```python
logging.basicConfig(level=logging.DEBUG)
```

## Future Enhancements

### 1. **Full Thickness Calculation**
Implement the complete thickness calculation algorithm from the original VBScript.

### 2. **Advanced Surface Detection**
Add proper surface element identification and neighbor analysis.

### 3. **Batch Processing**
Support for processing multiple studies in batch mode.

### 4. **GUI Interface**
Create a user-friendly GUI for parameter input and result visualization.

### 5. **Result Export**
Add options to export results to various formats (CSV, Excel, etc.).

## Support and Maintenance

For issues or questions regarding this Python conversion:
1. Check the troubleshooting section above
2. Review the detailed logging output
3. Verify that all dependencies are properly installed
4. Ensure Moldflow Synergy is properly configured

## License

This Python conversion maintains the same licensing terms as the original VBScript version.
