Attribute VB_Name = "Message"

Attribute VB_Base = "0{FCFB3D2A-A0FA-1068-A738-08002B3371B5}"

Attribute VB_GlobalNameSpace = False

Attribute VB_Creatable = False

Attribute VB_PredeclaredId = False

Attribute VB_Exposed = False

Attribute VB_TemplateDerived = False

Attribute VB_Customizable = False



  Private mMSCD         ' MSCD Message ID

  Private mNumString    ' Number of Strings associated with the Message

  Private mNumFloat     ' Number of Floats  Associated with the Message

  Private mStrings()    ' The Strings Associated with the Message

  Private mFloats()     ' The Numerical Values Associated with the Message

  

  Public Sub SetMSCD(aMSCD)

    mMSCD = aMSCD

  End Sub

  Public Sub SetNumString(aNumString)

    mNumString = aNumString

  End Sub

  Public Sub SetNumFloat(aNumFloat)

    mNumFloat = aNumFloat

  End Sub

  Public Sub AddFloat(aFloat)

    mNumFloat = mNumFloat + 1

    ReDim Preserve mFloats(mNumFloat)

    mFloats(mNumFloat - 1) = aFloat

  End Sub

  Public Sub AddString(aString)

    mNumString = mNumString + 1

    ReDim Preserve mStrings(mNumString)

    mStrings(mNumString - 1) = aString

  End Sub

  Public Function GetMSCD()

    GetMSCD = mMSCD

  End Function

  Public Function GetString(aIndex)

    GetString = ""

    If aIndex >= 0 And aIndex < mNumString Then

       GetString = mStrings(aIndex)

    End If

  End Function

  Public Function GetFloat(aIndex)

    GetFloat = ""

    If aIndex >= 0 And aIndex < mNumFloat Then

       GetFloat = mFloats(aIndex)

    End If

  End Function

  Public Function GetNumString()

    GetNumString = mNumString

  End Function

  Public Function GetNumFloat()

    GetNumFloat = mNumFloat

  End Function

  Private Sub Class_Initialize()

    mMSCD = -1

    mNumString = 0

    mNumFloat = 0

  End Sub

