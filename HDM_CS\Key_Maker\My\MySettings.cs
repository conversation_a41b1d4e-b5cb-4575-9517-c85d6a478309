﻿// Decompiled with JetBrains decompiler
// Type: Key_Maker.My.MySettings
// Assembly: Key_Maker, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 43BE3483-FE2B-4F9D-8AD0-2D447D49E2D6
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\Key_Maker.exe

using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Configuration;
using System.Runtime.CompilerServices;

namespace Key_Maker.My
{
  [CompilerGenerated]
  [GeneratedCode("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "14.0.0.0")]
  [EditorBrowsable(EditorBrowsableState.Advanced)]
  internal sealed class MySettings : ApplicationSettingsBase
  {
    private static MySettings defaultInstance = (MySettings) SettingsBase.Synchronized((SettingsBase) new MySettings());

    public static MySettings Default
    {
      get
      {
        MySettings defaultInstance = MySettings.defaultInstance;
        return defaultInstance;
      }
    }
  }
}
