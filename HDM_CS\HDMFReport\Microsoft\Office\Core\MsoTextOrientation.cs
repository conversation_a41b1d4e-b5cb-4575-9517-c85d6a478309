﻿// Decompiled with JetBrains decompiler
// Type: Microsoft.Office.Core.MsoTextOrientation
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

namespace Microsoft.Office.Core
{
  [CompilerGenerated]
  [TypeIdentifier("2df8d04c-5bfa-101b-bde5-00aa0044de52", "Microsoft.Office.Core.MsoTextOrientation")]
  public enum MsoTextOrientation
  {
    msoTextOrientationMixed = -2, // 0xFFFFFFFE
    msoTextOrientationHorizontal = 1,
    msoTextOrientationUpward = 2,
    msoTextOrientationDownward = 3,
    msoTextOrientationVerticalFarEast = 4,
    msoTextOrientationVertical = 5,
    msoTextOrientationHorizontalRotatedFarEast = 6,
  }
}
