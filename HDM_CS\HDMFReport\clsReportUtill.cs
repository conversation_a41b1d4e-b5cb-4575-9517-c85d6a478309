﻿// Decompiled with JetBrains decompiler
// Type: HDMFReport.clsReportUtill
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using arUtil;
using HDLog4Net;
using HDMoldFlowLibrary;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;

namespace HDMFReport
{
  public class clsReportUtill
  {
    public static Image ChangeSyncBackgroundImage(Image p_imgTarget)
    {
      Bitmap bitmap = (Bitmap) p_imgTarget;
      bitmap.MakeTransparent(bitmap.GetPixel(0, 0));
      return (Image) bitmap;
    }

    [DllImport("KERNEL32.DLL", EntryPoint = "GetPrivateProfileStringW", CharSet = CharSet.Unicode, CallingConvention = CallingConvention.StdCall, SetLastError = true)]
    private static extern int GetPrivateProfileString(
      string lpAppName,
      string lpKeyName,
      string lpDefault,
      string lpReturnString,
      int nSize,
      string lpFilename);

    [DllImport("kernel32")]
    private static extern int GetPrivateProfileString(
      string section,
      string key,
      string def,
      StringBuilder retVal,
      int size,
      string filePath);

    [DllImport("kernel32")]
    private static extern long WritePrivateProfileString(
      string section,
      string key,
      string val,
      string filePath);

    public static string ReadINI(string p_strSection, string p_strKey, string p_strIniPath)
    {
      try
      {
        StringBuilder retVal = new StringBuilder(1024);
        if (clsReportUtill.GetPrivateProfileString(p_strSection, p_strKey, "", retVal, retVal.Capacity, p_strIniPath) > 0)
          return retVal.ToString();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsReportUtill]GetINIData):" + ex.Message));
      }
      return "";
    }

    public static void WriteINI(
      string p_strSection,
      string p_strKey,
      string p_strValue,
      string p_strIniPath)
    {
      FileInfo fileInfo = new FileInfo(p_strIniPath);
      if (!fileInfo.Directory.Exists)
        fileInfo.Directory.Create();
      clsReportUtill.WritePrivateProfileString(p_strSection, p_strKey, p_strValue, p_strIniPath);
    }

    public static Dictionary<string, string> GetINIDataFromSection(
      string p_strIniFPath,
      string p_strSection)
    {
      Dictionary<string, string> iniDataFromSection = new Dictionary<string, string>();
      try
      {
        iniDataFromSection = new INIHelper(p_strIniFPath).GetItemList(p_strSection);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsReportUtill]GetINIDataFromSection):" + ex.Message));
      }
      return iniDataFromSection;
    }

    public static DataTable GetINIData(string p_strIniFPath)
    {
      DataTable iniData = (DataTable) null;
      try
      {
        INIHelper iniHelper = new INIHelper(p_strIniFPath);
        List<string> sectionList = iniHelper.GetSectionList();
        if (sectionList.Count > 0)
        {
          iniData = new DataTable();
          foreach (string Section_ in sectionList)
          {
            Dictionary<string, string> itemList = iniHelper.GetItemList(Section_);
            if (iniData.Columns.Count == 0)
            {
              iniData.Columns.Add("Section");
              foreach (KeyValuePair<string, string> keyValuePair in itemList)
                iniData.Columns.Add(keyValuePair.Key);
            }
            DataRow dataRow = iniData.Rows.Add();
            dataRow["Section"] = (object) Section_;
            foreach (KeyValuePair<string, string> keyValuePair in itemList)
              dataRow[keyValuePair.Key] = (object) keyValuePair.Value;
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception[HDMFReport][clsReportUtill]GetINIData):" + ex.Message));
      }
      return iniData;
    }

    public static string[] GetINIAllKeys(string p_strIniFPath, string p_strSection)
    {
      List<string> stringList1 = new List<string>();
      string lpReturnString = new string(' ', 32000);
      try
      {
        clsReportUtill.GetPrivateProfileString(p_strSection, (string) null, "", lpReturnString, 32000, p_strIniFPath);
        if (lpReturnString.Length > 0)
        {
          List<string> stringList2 = new List<string>((IEnumerable<string>) lpReturnString.Split(new char[1]));
          if (stringList2.Count > 0)
          {
            stringList2.RemoveRange(stringList2.Count - 2, 2);
            foreach (string str in stringList2)
              stringList1.Add(str);
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsReportUtill]GetINIAllKeys):" + ex.Message));
      }
      return stringList1.ToArray();
    }

    public static double ConvertToDouble(string p_strValue)
    {
      double result = 0.0;
      double.TryParse(p_strValue, out result);
      return result;
    }

    public static int ConvertToInt(string p_strValue)
    {
      int result = 0;
      int.TryParse(p_strValue, out result);
      return result;
    }

    public static bool ConvertToBoolean(string p_strValue)
    {
      bool result = false;
      bool.TryParse(p_strValue, out result);
      return result;
    }

    public static clsHDMFLibDefine.Company ConvertToEnumCompany(string p_strValue)
    {
      clsHDMFLibDefine.Company result = clsHDMFLibDefine.Company.HDSolutions;
      Enum.TryParse<clsHDMFLibDefine.Company>(p_strValue, out result);
      return result;
    }

    public static void CopyFolder(string sourceFolder, string destFolder)
    {
      if (!Directory.Exists(destFolder))
        Directory.CreateDirectory(destFolder);
      string[] files = Directory.GetFiles(sourceFolder);
      string[] directories = Directory.GetDirectories(sourceFolder);
      foreach (string str in files)
      {
        string fileName = Path.GetFileName(str);
        File.Copy(str, Path.Combine(destFolder, fileName));
      }
      foreach (string str in directories)
      {
        string fileName = Path.GetFileName(str);
        clsReportUtill.CopyFolder(str, Path.Combine(destFolder, fileName));
      }
    }

    [DllImport("user32.dll", SetLastError = true)]
    public static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint lpdwProcessId);

    public static void ReleaseComObject(object p_obj)
    {
      if (p_obj == null)
        return;
      Marshal.ReleaseComObject(p_obj);
      GC.Collect();
    }

    public static void Delay(int ms)
    {
      DateTime now = DateTime.Now;
      TimeSpan timeSpan = new TimeSpan(0, 0, 0, 0, ms);
      for (DateTime dateTime = now.Add(timeSpan); dateTime >= now; now = DateTime.Now)
        Application.DoEvents();
    }
  }
}
