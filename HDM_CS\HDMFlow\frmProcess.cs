﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmProcess
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDLog4Net;
using HDMFUserControl;
using HDMoldFlow.Properties;
using HDMoldFlowLibrary;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Windows.Forms.Layout;

namespace HDMoldFlow
{
  public class frmProcess : Form
  {
    private const int WS_EX_COMPOSITED = 33554432;
    public DataRow m_drStudy;
    public DataRow m_drMaterial;
    public Dictionary<string, string> m_dicInj;
    private IContainer components = (IContainer) null;
    private Label label_Suji_Material;
    private ListBox listBox_Inj;
    private NewButton newButton_Inj_Edit;
    private Label label_Inj_Name;
    private Label label_Inj_Company;
    private Label label4;
    private Label label5;
    private Label label_Inj_MaxStroke;
    private Label label_Inj_MaxRate;
    private Label label_Inj_ScrewDia;
    private Label label_Inj_MaxPressure;
    private Label label_Inj_MaxClamp;
    private Label label_Inj_PreRatio;
    private NewTextBox newTextBox_Inj_Name;
    private NewTextBox newTextBox_Inj_Company;
    private NewTextBox newTextBox_Inj_DataSource;
    private NewTextBox newTextBox_Inj_DataLastModified;
    private UnitTextBox unitTextBox_Inj_MaxStroke;
    private UnitTextBox unitTextBox_Inj_MaxRate;
    private UnitTextBox unitTextBox_Inj_ScrewDia;
    private UnitTextBox unitTextBox_Inj_MaxPressure;
    private UnitTextBox unitTextBox_Inj_MaxClampForce;
    private NewTextBox newTextBox_Inj_PreRatio;
    private Panel panel1;
    private RadioButton radioButton_Suji_User;
    private RadioButton radioButton_Suji_System;
    private Label label12;
    private NewTextBox newTextBox_Suji_Search;
    private Label label13;
    private NewComboBox newComboBox_Suji_Manufacturer;
    private Label label14;
    private NewComboBox newComboBox_Suji_TradeName;
    private Label label15;
    private NewTextBox newTextBox_Suji_FamilyAbbreviation;
    private Label label16;
    private NewTextBox newTextBox_Suji_FillerData;
    private Label label17;
    private NewTextBox newTextBox_Suji_MaterialID;
    private NewButton newButton_Suji_Select;
    private PictureBox pictureBox_Suji_Image1;
    private PictureBox pictureBox_Suji_Image2;
    private NewTextBox newTextBox_Suji_Image1;
    private NewTextBox newTextBox_Suji_Image2;
    private Label label18;
    private NewTextBox newTextBox_Suji_EjectionTemp;
    private Label label19;
    private NewTextBox newTextBox_Suji_TransitionTemp;
    private Label label20;
    private NewTextBox newTextBox_Suji_MFR;
    private Label label21;
    private NewTextBox newTextBox_Suji_MeltTemp1;
    private Label label22;
    private NewTextBox newTextBox_Suji_MeltTemp2;
    private NewTextBox newTextBox20;
    private NewComboBox newComboBox_Proc_CoolType;
    private NewTextBox newTextBox_Proc_CoolType;
    private Label label_Proc_CoolingTime;
    private NewTextBox newTextBox_Proc_MeltTemp1;
    private NewTextBox newTextBox_Proc_MeltTemp2;
    private Label label24;
    private Label label25;
    private NewComboBox newComboBox_Proc_FC;
    private Label label_Inj_Data;
    private Label label_Inj_List;
    private Label label_Suji_ResultValue;
    private Label label_Suji_SearchValue;
    private Label label_Suji_SearchCond;
    private NewTextBox newTextBox33;
    private NewTextBox newTextBox_Suji_MoldTemp2;
    private NewTextBox newTextBox_Suji_MoldTemp1;
    private NewComboBox newComboBox_Item;
    private NewComboBox newComboBox_Company;
    private Label label_DB_Item;
    private Label label_DB_Company;
    private ListBox listBox_DB;
    private Label label_DB;
    private Label label32;
    private NewComboBox newComboBox_Proc_VP;
    private Panel panel3;
    private TabControl tabControl_Main;
    private TabPage tabPage1;
    private TabPage tabPage2;
    private TabPage tabPage3;
    private Panel panel4;
    private NewButton newButton_Proc;
    private NewButton newButton_Suji;
    private NewButton newButton_Inj;
    private Panel panel5;
    private Label label35;
    private NewTextBox newTextBox_Proc_MoldTemp1;
    private NewTextBox newTextBox24;
    private NewTextBox newTextBox21;
    private NewTextBox newTextBox19;
    private NewTextBox newTextBox_Proc_MoldTemp2;
    private NewTextBox newTextBox_Proc_CoolingTime;
    private Panel panel6;
    private DataGridView dataGridView_Proc_FC1;
    private DataGridViewTextBoxColumn Column1;
    private DataGridViewTextBoxColumn Column2;
    private Panel panel7;
    private DataGridView dataGridView_Proc_FC2;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn1;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn2;
    private Panel panel8;
    private DataGridView dataGridView_Proc_FC3;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn3;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn4;
    private Panel panel9;
    private DataGridView dataGridView_Proc_FC4;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn5;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn6;
    private Panel panel10;
    private DataGridView dataGridView_Proc_VP;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn7;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn8;
    private Panel panel13;
    private DataGridView dataGridView_Proc_PHC2;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn13;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn14;
    private Panel panel12;
    private DataGridView dataGridView_Proc_PHC1;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn11;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn12;
    private NewComboBox newComboBox_Proc_PHC;
    private Label label38;
    private Label label37;
    private Label label_Proc_FC2;
    private Label label_Proc_FC4;
    private Label label_Proc_PHC2;
    private Label label_Proc_PHC1;
    private Label label_Proc_FC3;
    private Label label_Proc_FC1;
    private NewButton newButton_Suji_UserUDB;
    private NewButton newButton_Apply;
    private NewTextBox newTextBox_Suji_UDB;
    private Label label23;
    private NewButton newButton_BeforeDB;
    private Panel panel_Tab;
    private Panel panel_DB;

    protected override CreateParams CreateParams
    {
      get
      {
        CreateParams createParams = base.CreateParams;
        createParams.ExStyle |= 33554432;
        return createParams;
      }
    }

    public frmProcess()
    {
      this.InitializeComponent();
      this.newButton_BeforeDB.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_BeforeDB.Image);
      this.newButton_Apply.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Apply.Image);
      this.label_DB.Text = "DB " + LocaleControl.getInstance().GetString("IDS_LIST");
      this.label_DB_Company.Text = LocaleControl.getInstance().GetString("IDS_COMPANY_NAME");
      this.label_DB_Item.Text = LocaleControl.getInstance().GetString("IDS_ITEM_NAME");
      this.newButton_BeforeDB.ButtonText = LocaleControl.getInstance().GetString("IDS_LOAD_BEFORE_SETTING");
      this.newButton_Inj.ButtonText = LocaleControl.getInstance().GetString("IDS_SET_INJ");
      this.newButton_Suji.ButtonText = LocaleControl.getInstance().GetString("IDS_SELECT_SUJI");
      this.newButton_Proc.ButtonText = LocaleControl.getInstance().GetString("IDS_PROCESS");
      this.label_Inj_List.Text = LocaleControl.getInstance().GetString("IDS_INJ_LIST");
      this.label_Inj_Data.Text = LocaleControl.getInstance().GetString("IDS_INJ_DATA");
      this.label_Inj_Name.Text = LocaleControl.getInstance().GetString("IDS_INJ_NAME");
      this.label_Inj_Company.Text = LocaleControl.getInstance().GetString("IDS_INJ_COMPANY");
      this.label_Inj_MaxStroke.Text = LocaleControl.getInstance().GetString("IDS_INJ_MAXSTROKE");
      this.label_Inj_MaxRate.Text = LocaleControl.getInstance().GetString("IDS_INJ_MAXRATE");
      this.label_Inj_ScrewDia.Text = LocaleControl.getInstance().GetString("IDS_INJ_SCREWDIA");
      this.label_Inj_MaxPressure.Text = LocaleControl.getInstance().GetString("IDS_INJ_MAXPRESSURE");
      this.label_Inj_MaxClamp.Text = LocaleControl.getInstance().GetString("IDS_INJ_MAXCLAMP");
      this.label_Inj_PreRatio.Text = LocaleControl.getInstance().GetString("IDS_INJ_PRERATIO");
      this.newButton_Inj_Edit.ButtonText = LocaleControl.getInstance().GetString("IDS_EDIT");
      this.label_Suji_Material.Text = LocaleControl.getInstance().GetString("IDS_MATERIAL_INFO");
      this.label_Suji_SearchCond.Text = LocaleControl.getInstance().GetString("IDS_SEARCH_CONDITION");
      this.label_Suji_SearchValue.Text = LocaleControl.getInstance().GetString("IDS_SEARCH_VALUE");
      this.label_Suji_ResultValue.Text = LocaleControl.getInstance().GetString("IDS_RESULT_VALUE");
      this.radioButton_Suji_System.Text = LocaleControl.getInstance().GetString("IDS_SYSTEM");
      this.radioButton_Suji_User.Text = LocaleControl.getInstance().GetString("IDS_USER");
      this.newButton_Suji_UserUDB.ButtonText = LocaleControl.getInstance().GetString("IDS_IMPORT_USER_UDB");
      this.newButton_Suji_Select.ButtonText = LocaleControl.getInstance().GetString("IDS_SELECT_SUJI");
      this.newButton_Apply.ButtonText = LocaleControl.getInstance().GetString("IDS_SET_PROCESS");
    }

    private void frmProcess_Load(object sender, EventArgs e)
    {
      this.Text = LocaleControl.getInstance().GetString("IDS_SET_PROCESS") + " [" + LocaleControl.getInstance().GetString("IDS_STUDY") + ": " + this.m_drStudy["Name"].ToString() + "]";
      this.SetDefault_Inj();
      this.SetDefault_Suji();
      this.SetDefault_Process();
      this.SetDefault_DB();
      this.SetLoad_Inj();
      this.SetLoad_Suji();
      this.SetLoad_Process();
      if (clsDefine.enumLicLevel != clsDefine.LicLevel.Light)
        return;
      this.panel_DB.Visible = false;
      this.newButton_BeforeDB.Location = this.panel_DB.Location;
      this.panel_Tab.Location = new Point(this.panel_Tab.Location.X, this.newButton_BeforeDB.Location.Y + this.newButton_BeforeDB.Height - 1);
      this.Size = new Size(this.Width, this.panel_Tab.Location.Y + this.panel_Tab.Height + 43);
    }

    private void SetDefault_Inj()
    {
      if (clsDefine.g_dtInjectionDB.Rows.Count == 0)
        return;
      try
      {
        this.listBox_Inj.Items.Add((object) LocaleControl.getInstance().GetString("IDS_STUDY"));
        foreach (DataRow dataRow in clsDefine.g_dtInjectionDB.AsEnumerable())
          this.listBox_Inj.Items.Add(dataRow["FileName"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcess]SetDefault_Inj):" + ex.Message));
      }
    }

    private void SetLoad_Inj()
    {
      try
      {
        this.m_dicInj = clsHDMFLib.GetInjectionDataFromMoldFlow(this.m_drStudy);
        if (this.m_dicInj.Count == 0)
          return;
        DataRow dataRow = clsDefine.g_dtInjectionDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == this.m_dicInj["Name"] && Temp["Company"].ToString() == this.m_dicInj["Company"] && Temp["DataSource"].ToString() == this.m_dicInj["DataSource"] && Temp["LastModified"].ToString() == this.m_dicInj["LastModified"] && Temp["MaxStroke"].ToString() == this.m_dicInj["MaxStroke"] && Temp["MaxRate"].ToString() == this.m_dicInj["MaxRate"] && Temp["ScrewDia"].ToString() == this.m_dicInj["ScrewDia"] && Temp["MaxPressure"].ToString() == this.m_dicInj["MaxPressure"] && Temp["MaxClamp"].ToString() == this.m_dicInj["MaxClamp"])).FirstOrDefault<DataRow>();
        if (dataRow == null)
          this.listBox_Inj.SelectedIndex = 0;
        else
          this.listBox_Inj.SelectedIndex = this.listBox_Inj.Items.IndexOf((object) dataRow["FileName"].ToString());
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcess]SetLoad_Inj):" + ex.Message));
      }
    }

    private void SetDefault_Suji()
    {
      try
      {
        this.radioButton_Suji_System.Checked = true;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcess]SetDefault_Suji):" + ex.Message));
      }
    }

    private void SetLoad_Suji()
    {
      try
      {
        string[] arr_strMaterial = clsHDMFLib.GetCurrentMaterial(clsDefine.g_dsMaterial.Tables.Cast<DataTable>().Where<DataTable>((System.Func<DataTable, bool>) (Temp => Temp.TableName == "User")).FirstOrDefault<DataTable>());
        DataRow p_drMaterial = clsDefine.g_dsMaterial.Tables[arr_strMaterial[0]].AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["ID"].ToString() == arr_strMaterial[1])).FirstOrDefault<DataRow>();
        this.m_drMaterial = p_drMaterial;
        if (p_drMaterial == null)
          return;
        Dictionary<string, string> dictionary = clsHDMFLib.SelectSuji(p_drMaterial, true);
        if (dictionary["UDB"] == "System")
          this.radioButton_Suji_System.Checked = true;
        else
          this.radioButton_Suji_User.Checked = true;
        this.newComboBox_Suji_Manufacturer.SelectedIndex = this.newComboBox_Suji_Manufacturer.Items.IndexOf((object) p_drMaterial["Manufacturer"].ToString());
        this.newComboBox_Suji_TradeName.SelectedIndex = this.newComboBox_Suji_TradeName.Items.IndexOf((object) (p_drMaterial["TradeName"].ToString() + " [ID:" + p_drMaterial["ID"].ToString() + "]"));
        this.newTextBox_Suji_UDB.Value = dictionary["UDB"];
        this.newTextBox_Suji_Image1.Value = p_drMaterial["TradeName"].ToString() + "_PVT_PLOT";
        this.newTextBox_Suji_Image2.Value = p_drMaterial["TradeName"].ToString() + "_RHEOLOGICAL_PLOT";
        using (StreamReader streamReader = new StreamReader(dictionary["PVTPLOT"]))
          this.pictureBox_Suji_Image1.Image = Image.FromStream(streamReader.BaseStream);
        using (StreamReader streamReader = new StreamReader(dictionary["RHEOLOGICALPLOT"]))
          this.pictureBox_Suji_Image2.Image = Image.FromStream(streamReader.BaseStream);
        this.newTextBox_Suji_EjectionTemp.Value = dictionary["EjectionTemp"];
        this.newTextBox_Suji_TransitionTemp.Value = dictionary["TransitionTemp"];
        this.newTextBox_Suji_MFR.Value = dictionary["MFR"];
        this.newTextBox_Suji_MoldTemp1.Value = dictionary["MoldTemp1"];
        this.newTextBox_Suji_MoldTemp2.Value = dictionary["MoldTemp2"];
        this.newTextBox_Suji_MeltTemp1.Value = dictionary["MeltTemp1"];
        this.newTextBox_Suji_MeltTemp2.Value = dictionary["MeltTemp2"];
        this.newTextBox_Proc_MoldTemp1.Value = dictionary["MoldTemp1"];
        this.newTextBox_Proc_MoldTemp2.Value = dictionary["MoldTemp2"];
        this.newTextBox_Proc_MeltTemp1.Value = dictionary["MeltTemp1"];
        this.newTextBox_Proc_MeltTemp2.Value = dictionary["MeltTemp2"];
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcess]SetLoad_Suji):" + ex.Message));
      }
    }

    private void SetDefault_Process()
    {
      try
      {
        string[] strArray = new string[3]
        {
          LocaleControl.getInstance().GetString("IDS_COOL"),
          LocaleControl.getInstance().GetString("IDS_FLOW"),
          LocaleControl.getInstance().GetString("IDS_WARP")
        };
        this.newComboBox_Proc_CoolType.Items.Add((object) strArray[0]);
        this.newComboBox_Proc_CoolType.Items.Add((object) strArray[1]);
        this.newComboBox_Proc_CoolType.Items.Add((object) (strArray[1] + "+" + strArray[2]));
        this.newComboBox_Proc_CoolType.Items.Add((object) (strArray[0] + "+" + strArray[1] + "+" + strArray[2]));
        this.newComboBox_Proc_CoolType.Items.Add((object) (strArray[0] + "+" + strArray[1]));
        this.newComboBox_Proc_CoolType.SelectedIndex = 0;
        this.newComboBox_Proc_FC.Items.Add((object) LocaleControl.getInstance().GetString("IDS_UNSUPPORTED_TYPE"));
        this.newComboBox_Proc_FC.Items.Add((object) "Automatic");
        this.newComboBox_Proc_FC.Items.Add((object) "Injection time");
        this.newComboBox_Proc_FC.Items.Add((object) "Flow rate");
        this.newComboBox_Proc_FC.Items.Add((object) "Ram speed vs ram position");
        this.newComboBox_Proc_FC.Items.Add((object) "%Flow rate vs %shot volume");
        this.newComboBox_Proc_FC.SelectedIndex = 0;
        this.newComboBox_Proc_VP.Items.Add((object) LocaleControl.getInstance().GetString("IDS_UNSUPPORTED_TYPE"));
        this.newComboBox_Proc_VP.Items.Add((object) "Automatic");
        this.newComboBox_Proc_VP.Items.Add((object) "By %volume filled");
        this.newComboBox_Proc_VP.Items.Add((object) "By ram position");
        this.newComboBox_Proc_VP.SelectedIndex = 0;
        this.newComboBox_Proc_PHC.Items.Add((object) LocaleControl.getInstance().GetString("IDS_UNSUPPORTED_TYPE"));
        this.newComboBox_Proc_PHC.Items.Add((object) "%Filling pressure vs time");
        this.newComboBox_Proc_PHC.Items.Add((object) "Packing pressure vs time");
        this.newComboBox_Proc_PHC.SelectedIndex = 0;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcess]SetDefault_Process):" + ex.Message));
      }
    }

    private void SetLoad_Process()
    {
      List<string> stringList = new List<string>();
      try
      {
        clsHDMFLib.OpenStudy(this.m_drStudy["Name"].ToString());
        this.newComboBox_Proc_CoolType.SelectedIndex = clsHDMFLib.GetCoolType();
        switch (this.newComboBox_Proc_CoolType.SelectedIndex)
        {
          case 0:
            this.newTextBox_Proc_CoolType.Value = "Cool";
            int packCoolingtimeType1 = clsHDMFLib.GetInjPackCoolingtimeType();
            if (packCoolingtimeType1 == 1)
              this.label_Proc_CoolingTime.Text = "Inj+Pack+Cooling time: Specified";
            else
              this.label_Proc_CoolingTime.Text = "Inj+Pack+Cooling time: Automatic";
            this.newTextBox_Proc_CoolingTime.Value = clsHDMFLib.GetInjPackCoolingtimeData(packCoolingtimeType1);
            break;
          case 1:
            this.newTextBox_Proc_CoolType.Text = "Flow";
            int coolingTimeType1 = clsHDMFLib.GetCoolingTimeType();
            if (coolingTimeType1 == 1)
              this.label_Proc_CoolingTime.Text = "Cooling Time: Specified";
            else
              this.label_Proc_CoolingTime.Text = "Cooling Time: Automatic";
            this.newTextBox_Proc_CoolingTime.Value = clsHDMFLib.GetCoolingTimeData(coolingTimeType1);
            break;
          case 2:
            this.newTextBox_Proc_CoolType.Text = "Flow|Warp";
            int coolingTimeType2 = clsHDMFLib.GetCoolingTimeType();
            if (coolingTimeType2 == 1)
              this.label_Proc_CoolingTime.Text = "Cooling Time: Specified";
            else
              this.label_Proc_CoolingTime.Text = "Cooling Time: Automatic";
            this.newTextBox_Proc_CoolingTime.Value = clsHDMFLib.GetCoolingTimeData(coolingTimeType2);
            break;
          case 3:
            this.newTextBox_Proc_CoolType.Text = "Cool|Flow|Warp";
            int packCoolingtimeType2 = clsHDMFLib.GetInjPackCoolingtimeType();
            if (packCoolingtimeType2 == 1)
              this.label_Proc_CoolingTime.Text = "Inj+Pack+Cooling time: Specified";
            else
              this.label_Proc_CoolingTime.Text = "Inj+Pack+Cooling time: Automatic";
            this.newTextBox_Proc_CoolingTime.Value = clsHDMFLib.GetInjPackCoolingtimeData(packCoolingtimeType2);
            break;
          case 4:
            this.newTextBox_Proc_CoolType.Text = "Cool|Flow";
            int packCoolingtimeType3 = clsHDMFLib.GetInjPackCoolingtimeType();
            if (packCoolingtimeType3 == 1)
              this.label_Proc_CoolingTime.Text = "Inj+Pack+Cooling time: Specified";
            else
              this.label_Proc_CoolingTime.Text = "Inj+Pack+Cooling time: Automatic";
            this.newTextBox_Proc_CoolingTime.Value = clsHDMFLib.GetInjPackCoolingtimeData(packCoolingtimeType3);
            break;
        }
        this.newTextBox_Proc_MeltTemp1.Value = clsHDMFLib.GetMeltTemperatureDataFromProcessSet();
        this.newTextBox_Proc_MeltTemp2.Value = clsHDMFLib.GetMeltTemperatureData()[1];
        this.newTextBox_Proc_MoldTemp1.Value = clsHDMFLib.GetMoldTemperatureDataFromProcessSet();
        this.newTextBox_Proc_MoldTemp2.Value = clsHDMFLib.GetMoldTemperatureData()[1];
        string fillingControlType = clsHDMFLib.GetFillingControlType();
        if (!this.newComboBox_Proc_FC.Items.Contains((object) fillingControlType))
        {
          this.newComboBox_Proc_FC.SelectedIndex = 0;
        }
        else
        {
          this.newComboBox_Proc_FC.SelectedIndex = this.newComboBox_Proc_FC.Items.IndexOf((object) fillingControlType);
          if (this.newComboBox_Proc_FC.SelectedIndex > 1)
          {
            List<string> fillingControlData = clsHDMFLib.GetFillingControlData(this.newComboBox_Proc_FC.SelectedIndex);
            for (int index1 = 0; index1 < fillingControlData.Count; ++index1)
            {
              switch (index1)
              {
                case 0:
                  this.dataGridView_Proc_FC1.Rows.Clear();
                  if (fillingControlData[index1] != "")
                  {
                    string[] strArray = fillingControlData[index1].Split(',');
                    for (int index2 = 0; index2 < strArray.Length; ++index2)
                    {
                      this.dataGridView_Proc_FC1.Rows.Add();
                      this.dataGridView_Proc_FC1.Rows[index2].Cells[0].Value = (object) strArray[index2];
                    }
                    break;
                  }
                  break;
                case 1:
                  this.dataGridView_Proc_FC2.Rows.Clear();
                  if (fillingControlData[index1] != "")
                  {
                    string[] strArray = fillingControlData[index1].Split(',');
                    for (int index3 = 0; index3 < strArray.Length; ++index3)
                    {
                      this.dataGridView_Proc_FC2.Rows.Add();
                      this.dataGridView_Proc_FC2.Rows[index3].Cells[0].Value = (object) strArray[index3];
                    }
                    break;
                  }
                  break;
                case 2:
                  this.dataGridView_Proc_FC3.Rows[0].Cells[0].Value = (object) fillingControlData[index1];
                  break;
                case 3:
                  this.dataGridView_Proc_FC4.Rows[0].Cells[0].Value = (object) fillingControlData[index1];
                  break;
              }
            }
          }
        }
        this.newComboBox_Proc_VP.SelectedIndex = clsHDMFLib.GetVelocityPressureType();
        if (this.newComboBox_Proc_VP.SelectedIndex > 0)
          this.dataGridView_Proc_VP.Rows[0].Cells[0].Value = (object) clsHDMFLib.GetVelocityPressureData(this.newComboBox_Proc_VP.SelectedIndex);
        this.newComboBox_Proc_PHC.SelectedIndex = clsHDMFLib.GetPackHoldingControlType();
        List<string> holdingControlData = clsHDMFLib.GetPackHoldingControlData(this.newComboBox_Proc_PHC.SelectedIndex);
        for (int index4 = 0; index4 < holdingControlData.Count; ++index4)
        {
          if (index4 % 2 != 0)
          {
            this.dataGridView_Proc_PHC1.Rows[this.dataGridView_Proc_PHC1.Rows.Add()].Cells[0].Value = (object) holdingControlData[index4].Split('|')[0];
            int index5 = this.dataGridView_Proc_PHC2.Rows.Add();
            this.dataGridView_Proc_PHC2.Rows[index5].Cells[0].Value = (object) holdingControlData[index4].Split('|')[1];
            this.dataGridView_Proc_PHC1.Rows[index5].Cells[1].Value = (object) "s";
            this.dataGridView_Proc_PHC2.Rows[index5].Cells[1].Value = this.newComboBox_Proc_PHC.SelectedIndex != 1 ? (object) "MPa" : (object) "%";
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcess]SetLoad_Process):" + ex.Message));
      }
    }

    private void SetDefault_DB()
    {
      try
      {
        this.RefreshDBList();
        this.RefreshCompanyUI();
        this.RefreshItemUI();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcess]SetDefault_DB):" + ex.Message));
      }
    }

    private void newButton_NewClick(object sender, EventArgs e)
    {
      NewButton newButton = sender as NewButton;
      if (newButton == this.newButton_Inj || newButton == this.newButton_Suji || newButton == this.newButton_Proc)
      {
        newButton.ButtonBackColor = Color.LightSteelBlue;
        if (newButton == this.newButton_Inj)
        {
          this.tabControl_Main.SelectedIndex = 0;
          this.newButton_Suji.ButtonBackColor = Color.White;
          this.newButton_Proc.ButtonBackColor = Color.White;
        }
        else if (newButton == this.newButton_Suji)
        {
          this.tabControl_Main.SelectedIndex = 1;
          this.newButton_Inj.ButtonBackColor = Color.White;
          this.newButton_Proc.ButtonBackColor = Color.White;
        }
        else
        {
          this.tabControl_Main.SelectedIndex = 2;
          this.newButton_Inj.ButtonBackColor = Color.White;
          this.newButton_Suji.ButtonBackColor = Color.White;
        }
      }
      else if (newButton == this.newButton_Suji_UserUDB)
        this.ImportUserUDB();
      else if (newButton == this.newButton_Suji_Select)
        this.SelectSuji(true);
      else if (newButton == this.newButton_BeforeDB)
      {
        this.LoadProcessSettingData();
      }
      else
      {
        if (newButton != this.newButton_Apply)
          return;
        this.SetProcess();
      }
    }

    private void radioButton_Suji_System_CheckedChanged(object sender, EventArgs e)
    {
      if (!this.radioButton_Suji_System.Checked)
        return;
      this.RefreshMaterial();
      this.newButton_Suji_UserUDB.Enabled = false;
    }

    private void radioButton_Suji_User_CheckedChanged(object sender, EventArgs e)
    {
      if (!this.radioButton_Suji_User.Checked)
        return;
      this.RefreshMaterial();
      this.newButton_Suji_UserUDB.Enabled = true;
    }

    private void RefreshMaterial()
    {
      this.newComboBox_Suji_Manufacturer.Items.Clear();
      this.newComboBox_Suji_Manufacturer.Enabled = false;
      this.newComboBox_Suji_TradeName.Items.Clear();
      this.newComboBox_Suji_TradeName.Enabled = false;
      this.newTextBox_Suji_FamilyAbbreviation.Value = "";
      this.newTextBox_Suji_FillerData.Value = "";
      this.newTextBox_Suji_MaterialID.Value = "";
      string strQuery = this.newTextBox_Suji_Search.Value;
      try
      {
        DataTable source = !this.radioButton_Suji_System.Checked ? clsDefine.g_dsMaterial.Tables["User"] : clsDefine.g_dsMaterial.Tables["System"];
        if (source == null)
          return;
        DataRow[] dataRowArray = strQuery != null && !(strQuery == "") ? source.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["TradeName"].ToString().ToUpper().Contains(strQuery.ToUpper()))).ToArray<DataRow>() : source.AsEnumerable().ToArray<DataRow>();
        if (dataRowArray.Length == 0)
          return;
        List<string> stringList = new List<string>();
        foreach (DataRow dataRow in dataRowArray)
        {
          if (!stringList.Contains(dataRow["Manufacturer"].ToString()))
            stringList.Add(dataRow["Manufacturer"].ToString());
        }
        stringList.Sort();
        this.newComboBox_Suji_Manufacturer.Items.AddRange((object[]) stringList.ToArray());
        this.newComboBox_Suji_Manufacturer.Enabled = true;
        this.newComboBox_Suji_Manufacturer.SelectedIndex = 0;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcess]RefreshMaterial):" + ex.Message));
      }
    }

    private void newComboBox_Suji_Manufacturer_SelectedIndexChanged(object sender, EventArgs e)
    {
      this.newComboBox_Suji_TradeName.Items.Clear();
      this.newComboBox_Suji_TradeName.Enabled = false;
      this.newTextBox_Suji_FamilyAbbreviation.Value = "";
      this.newTextBox_Suji_FillerData.Value = "";
      this.newTextBox_Suji_MaterialID.Value = "";
      string strQuery = this.newTextBox_Suji_Search.Value;
      string strManufacturer = this.newComboBox_Suji_Manufacturer.Value;
      try
      {
        DataTable source = !this.radioButton_Suji_System.Checked ? clsDefine.g_dsMaterial.Tables["User"] : clsDefine.g_dsMaterial.Tables["System"];
        DataRow[] dataRowArray = strQuery != null && !(strQuery == "") ? source.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Manufacturer"].ToString() == strManufacturer && Temp["TradeName"].ToString().ToUpper().Contains(strQuery.ToUpper()))).ToArray<DataRow>() : source.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Manufacturer"].ToString() == strManufacturer)).ToArray<DataRow>();
        if (dataRowArray.Length == 0)
          return;
        List<string> stringList = new List<string>();
        foreach (DataRow dataRow in dataRowArray)
        {
          string str = dataRow["TradeName"].ToString() + " [ID:" + dataRow["ID"].ToString() + "]";
          if (!stringList.Contains(str))
            stringList.Add(str);
        }
        stringList.Sort();
        this.newComboBox_Suji_TradeName.Items.AddRange((object[]) stringList.ToArray());
        this.newComboBox_Suji_TradeName.Enabled = true;
        this.newComboBox_Suji_TradeName.SelectedIndex = 0;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcess]newComboBox_Suji_Manufacturer_SelectedIndexChanged):" + ex.Message));
      }
    }

    private void newComboBox_Suji_TradeName_SelectedIndexChanged(object sender, EventArgs e)
    {
      this.newTextBox_Suji_FamilyAbbreviation.Value = "";
      this.newTextBox_Suji_FillerData.Value = "";
      this.newTextBox_Suji_MaterialID.Value = "";
      try
      {
        string strManufacturer = this.newComboBox_Suji_Manufacturer.Value;
        string strTradeName = "";
        string strMaterialID = "";
        string[] strArray = this.newComboBox_Suji_TradeName.Value.Split('[');
        strTradeName = strArray[0].TrimEnd();
        strMaterialID = strArray[1].Replace("]", "").Replace("ID:", "");
        DataRow dataRow = (!this.radioButton_Suji_System.Checked ? clsDefine.g_dsMaterial.Tables["User"] : clsDefine.g_dsMaterial.Tables["System"]).AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Manufacturer"].ToString() == strManufacturer && Temp["TradeName"].ToString() == strTradeName && Temp["ID"].ToString() == strMaterialID)).FirstOrDefault<DataRow>();
        if (dataRow == null)
          return;
        this.newTextBox_Suji_FamilyAbbreviation.Value = dataRow["Familyabbreviation"].ToString();
        this.newTextBox_Suji_FillerData.Value = dataRow["FillerData"].ToString();
        this.newTextBox_Suji_MaterialID.Value = dataRow["MaterialID"].ToString();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcess]newComboBox_Suji_TradeName_SelectedIndexChanged):" + ex.Message));
      }
    }

    private void SelectSuji(bool p_isProg)
    {
      try
      {
        string strManufacturer = this.newComboBox_Suji_Manufacturer.Value;
        string strTradeName = "";
        string strMaterialID = "";
        string[] strArray = this.newComboBox_Suji_TradeName.Value.Split('[');
        strTradeName = strArray[0].TrimEnd();
        strMaterialID = strArray[1].Replace("]", "").Replace("ID:", "");
        DataRow p_drMaterial = (!this.radioButton_Suji_System.Checked ? clsDefine.g_dsMaterial.Tables["User"] : clsDefine.g_dsMaterial.Tables["System"]).AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Manufacturer"].ToString() == strManufacturer && Temp["TradeName"].ToString() == strTradeName && Temp["ID"].ToString() == strMaterialID)).FirstOrDefault<DataRow>();
        if (p_drMaterial == null)
          return;
        if (p_isProg)
        {
          clsUtill.StartProgress(LocaleControl.getInstance().GetString("IDS_CHANGE_SUJI") + "...", (Form) this);
          this.Hide();
        }
        Dictionary<string, string> dictionary = clsHDMFLib.SelectSuji(p_drMaterial, true);
        if (p_isProg)
        {
          clsUtill.EndProgress();
          clsUtill.ShowForm((Form) this);
        }
        if (dictionary.Count == 0)
          return;
        this.newTextBox_Suji_UDB.Value = dictionary["UDB"];
        this.newTextBox_Suji_Image1.Value = p_drMaterial["TradeName"].ToString() + "_PVT_PLOT";
        this.newTextBox_Suji_Image2.Value = p_drMaterial["TradeName"].ToString() + "_RHEOLOGICAL_PLOT";
        using (StreamReader streamReader = new StreamReader(dictionary["PVTPLOT"]))
          this.pictureBox_Suji_Image1.Image = Image.FromStream(streamReader.BaseStream);
        using (StreamReader streamReader = new StreamReader(dictionary["RHEOLOGICALPLOT"]))
          this.pictureBox_Suji_Image2.Image = Image.FromStream(streamReader.BaseStream);
        this.newTextBox_Suji_EjectionTemp.Value = dictionary["EjectionTemp"];
        this.newTextBox_Suji_TransitionTemp.Value = dictionary["TransitionTemp"];
        this.newTextBox_Suji_MFR.Value = dictionary["MFR"];
        this.newTextBox_Suji_MoldTemp1.Value = dictionary["MoldTemp1"];
        this.newTextBox_Suji_MoldTemp2.Value = dictionary["MoldTemp2"];
        this.newTextBox_Suji_MeltTemp1.Value = dictionary["MeltTemp1"];
        this.newTextBox_Suji_MeltTemp2.Value = dictionary["MeltTemp2"];
        this.newTextBox_Proc_MoldTemp1.Value = dictionary["MoldTemp1"];
        this.newTextBox_Proc_MoldTemp2.Value = dictionary["MoldTemp2"];
        this.newTextBox_Proc_MeltTemp1.Value = dictionary["MeltTemp1"];
        this.newTextBox_Proc_MeltTemp2.Value = dictionary["MeltTemp2"];
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcess]SelectSuji):" + ex.Message));
      }
    }

    private void newComboBox_Proc_CoolType_SelectedIndexChanged(object sender, EventArgs e)
    {
      try
      {
        switch (this.newComboBox_Proc_CoolType.SelectedIndex)
        {
          case 0:
            this.newTextBox_Proc_CoolType.Value = "Cool";
            this.label_Proc_CoolingTime.Text = "Inj+Pack+Cooling time:Specified";
            break;
          case 1:
            this.newTextBox_Proc_CoolType.Value = "Flow";
            this.label_Proc_CoolingTime.Text = "Cooling Time:Specified";
            break;
          case 2:
            this.newTextBox_Proc_CoolType.Value = "Flow|Warp";
            this.label_Proc_CoolingTime.Text = "Cooling Time:Specified";
            break;
          case 3:
            this.newTextBox_Proc_CoolType.Value = "Cool|Flow|Warp";
            this.label_Proc_CoolingTime.Text = "Inj+Pack+Cooling time:Specified";
            break;
          case 4:
            this.newTextBox_Proc_CoolType.Value = "Cool|Flow";
            this.label_Proc_CoolingTime.Text = "Inj+Pack+Cooling time:Specified";
            break;
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcess]newComboBox_Proc_CoolType_SelectedIndexChanged):" + ex.Message));
      }
    }

    private void listBox_Inj_SelectedIndexChanged(object sender, EventArgs e)
    {
      try
      {
        if (this.listBox_Inj.SelectedIndex == 0)
        {
          this.newTextBox_Inj_Name.Value = this.m_dicInj["Name"];
          this.newTextBox_Inj_Company.Value = this.m_dicInj["Company"];
          this.newTextBox_Inj_DataSource.Value = this.m_dicInj["DataSource"];
          this.newTextBox_Inj_DataLastModified.Value = this.m_dicInj["LastModified"];
          this.unitTextBox_Inj_MaxStroke.Value = this.m_dicInj["MaxStroke"];
          this.unitTextBox_Inj_MaxRate.Value = this.m_dicInj["MaxRate"];
          this.unitTextBox_Inj_ScrewDia.Value = this.m_dicInj["ScrewDia"];
          this.unitTextBox_Inj_MaxPressure.Value = this.m_dicInj["MaxPressure"];
          this.unitTextBox_Inj_MaxClampForce.Value = this.m_dicInj["MaxClamp"];
          this.newTextBox_Inj_PreRatio.Value = "";
        }
        else
        {
          DataRow dataRow = clsDefine.g_dtInjectionDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["FileName"].ToString() == this.listBox_Inj.Text)).FirstOrDefault<DataRow>();
          if (dataRow != null)
          {
            this.newTextBox_Inj_Name.Value = dataRow["Name"].ToString();
            this.newTextBox_Inj_Company.Value = dataRow["Company"].ToString();
            this.newTextBox_Inj_DataSource.Value = dataRow["DataSource"].ToString();
            this.newTextBox_Inj_DataLastModified.Value = dataRow["LastModified"].ToString();
            this.unitTextBox_Inj_MaxStroke.Value = dataRow["MaxStroke"].ToString();
            this.unitTextBox_Inj_MaxRate.Value = dataRow["MaxRate"].ToString();
            this.unitTextBox_Inj_ScrewDia.Value = dataRow["ScrewDia"].ToString();
            this.unitTextBox_Inj_MaxPressure.Value = dataRow["MaxPressure"].ToString();
            this.unitTextBox_Inj_MaxClampForce.Value = dataRow["MaxClamp"].ToString();
            this.newTextBox_Inj_PreRatio.Value = dataRow["PreRatio"].ToString();
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcess]listBox_Inj_SelectedIndexChanged):" + ex.Message));
      }
    }

    private void tabControl_Main_SelectedIndexChanged(object sender, EventArgs e)
    {
      if (this.tabControl_Main.SelectedIndex != 2)
        return;
      foreach (Control control1 in (ArrangedElementCollection) this.tabPage3.Controls)
      {
        if (!(control1.GetType().Name != "Panel"))
        {
          foreach (Control control2 in (ArrangedElementCollection) control1.Controls)
          {
            if (!(control2.GetType().Name != "DataGridView"))
              ((DataGridView) control2).ClearSelection();
          }
        }
      }
    }

    private void dataGridView_Proc_FC_CellEndEdit(object sender, DataGridViewCellEventArgs e)
    {
      DataGridView dataGridView = sender as DataGridView;
      if (e.ColumnIndex != 0 || dataGridView.Rows[e.RowIndex].Cells[e.ColumnIndex].Value == null || !(dataGridView.Rows[e.RowIndex].Cells[e.ColumnIndex].Value.ToString() != ""))
        return;
      switch (this.newComboBox_Proc_FC.SelectedIndex)
      {
        case 4:
          dataGridView.Rows[e.RowIndex].Cells[1].Value = dataGridView != this.dataGridView_Proc_FC1 ? (object) "mm/s" : (object) "mm";
          break;
        case 5:
          dataGridView.Rows[e.RowIndex].Cells[1].Value = (object) "%";
          break;
      }
    }

    private void dataGridView_Proc_MouseLeave(object sender, EventArgs e) => (sender as DataGridView).ClearSelection();

    private void dataGridView_Proc_SelectionChanged(object sender, EventArgs e)
    {
      DataGridView dataGridView = sender as DataGridView;
      if (dataGridView.CurrentCell == null || dataGridView.CurrentCell.ColumnIndex != 1)
        return;
      dataGridView.CurrentCell.Selected = false;
    }

    private void dataGridView_Proc_FC1_RowsAdded(object sender, DataGridViewRowsAddedEventArgs e)
    {
      if (this.newComboBox_Proc_FC.SelectedIndex == 2)
      {
        for (int index = 0; index < this.dataGridView_Proc_FC1.Rows.Count; ++index)
          this.dataGridView_Proc_FC1.Rows[index].Cells[1].Value = (object) "s";
      }
      else if (this.newComboBox_Proc_FC.SelectedIndex == 3)
      {
        for (int index = 0; index < this.dataGridView_Proc_FC1.Rows.Count; ++index)
          this.dataGridView_Proc_FC1.Rows[index].Cells[1].Value = (object) "cm3/s";
      }
      else if (this.newComboBox_Proc_FC.SelectedIndex == 4)
      {
        for (int index = 0; index < this.dataGridView_Proc_FC1.Rows.Count; ++index)
          this.dataGridView_Proc_FC1.Rows[index].Cells[1].Value = (object) "mm";
      }
      else
      {
        if (this.newComboBox_Proc_FC.SelectedIndex != 5)
          return;
        for (int index = 0; index < this.dataGridView_Proc_FC1.Rows.Count; ++index)
          this.dataGridView_Proc_FC1.Rows[index].Cells[1].Value = (object) "%";
      }
    }

    private void dataGridView_Proc_FC2_RowsAdded(object sender, DataGridViewRowsAddedEventArgs e)
    {
      if (this.newComboBox_Proc_FC.SelectedIndex != 4 && this.newComboBox_Proc_FC.SelectedIndex != 5)
        return;
      if (this.newComboBox_Proc_FC.SelectedIndex == 4)
      {
        for (int index = 0; index < this.dataGridView_Proc_FC2.Rows.Count; ++index)
          this.dataGridView_Proc_FC2.Rows[index].Cells[1].Value = (object) "mm";
      }
      else
      {
        for (int index = 0; index < this.dataGridView_Proc_FC2.Rows.Count; ++index)
          this.dataGridView_Proc_FC2.Rows[index].Cells[1].Value = (object) "%";
      }
    }

    private void dataGridView_Proc_FC_RowPostPaint(
      object sender,
      DataGridViewRowPostPaintEventArgs e)
    {
      DataGridView dataGridView = sender as DataGridView;
      string s = (e.RowIndex + 1).ToString();
      StringFormat format = new StringFormat()
      {
        Alignment = StringAlignment.Center,
        LineAlignment = StringAlignment.Center
      };
      Rectangle layoutRectangle;
      ref Rectangle local = ref layoutRectangle;
      Rectangle rowBounds = e.RowBounds;
      int left = rowBounds.Left;
      rowBounds = e.RowBounds;
      int top = rowBounds.Top;
      int rowHeadersWidth = dataGridView.RowHeadersWidth;
      rowBounds = e.RowBounds;
      int height = rowBounds.Height;
      local = new Rectangle(left, top, rowHeadersWidth, height);
      e.Graphics.DrawString(s, this.Font, SystemBrushes.ControlText, (RectangleF) layoutRectangle, format);
    }

    private void newComboBox_Proc_FC_SelectedIndexChanged(object sender, EventArgs e)
    {
      this.label_Proc_FC1.BackColor = Color.WhiteSmoke;
      this.label_Proc_FC2.BackColor = Color.WhiteSmoke;
      this.label_Proc_FC3.BackColor = Color.WhiteSmoke;
      this.label_Proc_FC4.BackColor = Color.WhiteSmoke;
      this.label_Proc_FC1.Text = "";
      this.label_Proc_FC2.Text = "";
      this.label_Proc_FC3.Text = "";
      this.label_Proc_FC4.Text = "";
      this.dataGridView_Proc_FC1.Enabled = false;
      this.dataGridView_Proc_FC2.Enabled = false;
      this.dataGridView_Proc_FC3.Enabled = false;
      this.dataGridView_Proc_FC4.Enabled = false;
      this.dataGridView_Proc_FC1.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_Proc_FC2.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_Proc_FC3.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_Proc_FC4.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_Proc_FC1.AllowUserToAddRows = false;
      this.dataGridView_Proc_FC2.AllowUserToAddRows = false;
      this.dataGridView_Proc_FC3.AllowUserToAddRows = false;
      this.dataGridView_Proc_FC4.AllowUserToAddRows = false;
      this.dataGridView_Proc_FC1.RowHeadersVisible = false;
      this.dataGridView_Proc_FC2.RowHeadersVisible = false;
      this.dataGridView_Proc_FC1.Rows.Clear();
      this.dataGridView_Proc_FC2.Rows.Clear();
      this.dataGridView_Proc_FC3.Rows.Clear();
      this.dataGridView_Proc_FC4.Rows.Clear();
      this.dataGridView_Proc_FC1.RowPostPaint -= new DataGridViewRowPostPaintEventHandler(this.dataGridView_Proc_FC_RowPostPaint);
      this.dataGridView_Proc_FC2.RowPostPaint -= new DataGridViewRowPostPaintEventHandler(this.dataGridView_Proc_FC_RowPostPaint);
      try
      {
        switch (this.newComboBox_Proc_FC.SelectedIndex)
        {
          case 2:
            this.label_Proc_FC1.BackColor = Color.Lavender;
            this.label_Proc_FC1.Text = LocaleControl.getInstance().GetString("IDS_TIME") + "(S)";
            this.dataGridView_Proc_FC1.Enabled = true;
            this.dataGridView_Proc_FC1.BackgroundColor = Color.White;
            this.dataGridView_Proc_FC1.Rows.Add();
            this.dataGridView_Proc_FC1.Rows[this.dataGridView_Proc_FC1.Rows.Count - 1].Cells[1].Value = (object) "s";
            this.dataGridView_Proc_FC1.ClearSelection();
            break;
          case 3:
            this.label_Proc_FC1.BackColor = Color.Lavender;
            this.label_Proc_FC1.Text = LocaleControl.getInstance().GetString("IDS_FLOW_RATE") + "(cm3/s)";
            this.dataGridView_Proc_FC1.Enabled = true;
            this.dataGridView_Proc_FC1.BackgroundColor = Color.White;
            this.dataGridView_Proc_FC1.Rows.Add();
            this.dataGridView_Proc_FC1.Rows[this.dataGridView_Proc_FC1.Rows.Count - 1].Cells[1].Value = (object) "cm3/s";
            this.dataGridView_Proc_FC1.ClearSelection();
            break;
          case 4:
            this.label_Proc_FC1.BackColor = Color.Lavender;
            this.label_Proc_FC1.Text = "Ram Position(mm)";
            this.dataGridView_Proc_FC1.Enabled = true;
            this.dataGridView_Proc_FC1.BackgroundColor = Color.White;
            this.dataGridView_Proc_FC1.AllowUserToAddRows = true;
            this.dataGridView_Proc_FC1.RowHeadersVisible = true;
            this.dataGridView_Proc_FC1.RowPostPaint += new DataGridViewRowPostPaintEventHandler(this.dataGridView_Proc_FC_RowPostPaint);
            this.dataGridView_Proc_FC1.Rows[this.dataGridView_Proc_FC1.Rows.Count - 1].Cells[1].Value = (object) "mm";
            this.dataGridView_Proc_FC1.ClearSelection();
            this.label_Proc_FC2.BackColor = Color.Lavender;
            this.label_Proc_FC2.Text = "Ram Speed(mm/s)";
            this.dataGridView_Proc_FC2.Enabled = true;
            this.dataGridView_Proc_FC2.BackgroundColor = Color.White;
            this.dataGridView_Proc_FC2.AllowUserToAddRows = true;
            this.dataGridView_Proc_FC2.RowHeadersVisible = true;
            this.dataGridView_Proc_FC2.RowPostPaint += new DataGridViewRowPostPaintEventHandler(this.dataGridView_Proc_FC_RowPostPaint);
            this.dataGridView_Proc_FC2.Rows[this.dataGridView_Proc_FC2.Rows.Count - 1].Cells[1].Value = (object) "mm";
            this.dataGridView_Proc_FC2.ClearSelection();
            this.label_Proc_FC3.BackColor = Color.Lavender;
            this.label_Proc_FC3.Text = "Cushion warning limit";
            this.dataGridView_Proc_FC3.Enabled = true;
            this.dataGridView_Proc_FC3.BackgroundColor = Color.White;
            this.dataGridView_Proc_FC3.Rows.Add();
            this.dataGridView_Proc_FC3.Rows[this.dataGridView_Proc_FC3.Rows.Count - 1].Cells[1].Value = (object) "mm";
            this.dataGridView_Proc_FC3.ClearSelection();
            this.label_Proc_FC4.BackColor = Color.Lavender;
            this.label_Proc_FC4.Text = "Starting ram position";
            this.dataGridView_Proc_FC4.Enabled = true;
            this.dataGridView_Proc_FC4.BackgroundColor = Color.White;
            this.dataGridView_Proc_FC4.Rows.Add();
            this.dataGridView_Proc_FC4.Rows[this.dataGridView_Proc_FC4.Rows.Count - 1].Cells[1].Value = (object) "mm";
            this.dataGridView_Proc_FC4.ClearSelection();
            break;
          case 5:
            this.label_Proc_FC1.BackColor = Color.Lavender;
            this.label_Proc_FC1.Text = "%Shot Volume(%)";
            this.dataGridView_Proc_FC1.Enabled = true;
            this.dataGridView_Proc_FC1.BackgroundColor = Color.White;
            this.dataGridView_Proc_FC1.AllowUserToAddRows = true;
            this.dataGridView_Proc_FC1.RowHeadersVisible = true;
            this.dataGridView_Proc_FC1.RowPostPaint += new DataGridViewRowPostPaintEventHandler(this.dataGridView_Proc_FC_RowPostPaint);
            this.dataGridView_Proc_FC1.Rows[this.dataGridView_Proc_FC1.Rows.Count - 1].Cells[1].Value = (object) "%";
            this.dataGridView_Proc_FC1.ClearSelection();
            this.label_Proc_FC2.BackColor = Color.Lavender;
            this.label_Proc_FC2.Text = "%Flow rate(%)";
            this.dataGridView_Proc_FC2.Enabled = true;
            this.dataGridView_Proc_FC2.BackgroundColor = Color.White;
            this.dataGridView_Proc_FC2.AllowUserToAddRows = true;
            this.dataGridView_Proc_FC2.RowHeadersVisible = true;
            this.dataGridView_Proc_FC2.RowPostPaint += new DataGridViewRowPostPaintEventHandler(this.dataGridView_Proc_FC_RowPostPaint);
            this.dataGridView_Proc_FC2.Rows[this.dataGridView_Proc_FC2.Rows.Count - 1].Cells[1].Value = (object) "%";
            this.dataGridView_Proc_FC2.ClearSelection();
            this.label_Proc_FC3.BackColor = Color.Lavender;
            this.label_Proc_FC3.Text = "Normal flow rate";
            this.dataGridView_Proc_FC3.Enabled = true;
            this.dataGridView_Proc_FC3.BackgroundColor = Color.White;
            this.dataGridView_Proc_FC3.Rows.Add();
            this.dataGridView_Proc_FC3.Rows[this.dataGridView_Proc_FC3.Rows.Count - 1].Cells[1].Value = (object) "cm3/s";
            this.dataGridView_Proc_FC3.ClearSelection();
            break;
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcess]newComboBox_Proc_FC_SelectedIndexChanged):" + ex.Message));
      }
    }

    private void newComboBox_Proc_VP_SelectedIndexChanged(object sender, EventArgs e)
    {
      this.dataGridView_Proc_VP.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_Proc_VP.Enabled = false;
      this.dataGridView_Proc_VP.Rows.Clear();
      switch (this.newComboBox_Proc_VP.SelectedIndex)
      {
        case 2:
          this.dataGridView_Proc_VP.Enabled = true;
          this.dataGridView_Proc_VP.BackgroundColor = Color.White;
          this.dataGridView_Proc_VP.Rows.Add();
          this.dataGridView_Proc_VP.Rows[this.dataGridView_Proc_VP.Rows.Count - 1].Cells[1].Value = (object) "%";
          this.dataGridView_Proc_VP.ClearSelection();
          break;
        case 3:
          this.dataGridView_Proc_VP.Enabled = true;
          this.dataGridView_Proc_VP.BackgroundColor = Color.White;
          this.dataGridView_Proc_VP.Rows.Add();
          this.dataGridView_Proc_VP.Rows[this.dataGridView_Proc_VP.Rows.Count - 1].Cells[1].Value = (object) "mm";
          this.dataGridView_Proc_VP.ClearSelection();
          break;
      }
    }

    private void dataGridView_Proc_VP_SelectionChanged(object sender, EventArgs e)
    {
      if (this.dataGridView_Proc_VP.CurrentCell == null || this.dataGridView_Proc_VP.CurrentCell.ColumnIndex != 1)
        return;
      this.dataGridView_Proc_VP.CurrentCell.Selected = false;
    }

    private void dataGridView_Proc_VP_MouseLeave(object sender, EventArgs e) => this.dataGridView_Proc_VP.ClearSelection();

    private void newComboBox_Proc_PHC_SelectedIndexChanged(object sender, EventArgs e)
    {
      this.label_Proc_PHC1.BackColor = Color.WhiteSmoke;
      this.label_Proc_PHC1.Text = "";
      this.dataGridView_Proc_PHC1.AllowUserToAddRows = false;
      this.dataGridView_Proc_PHC1.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_Proc_PHC1.Rows.Clear();
      this.label_Proc_PHC2.BackColor = Color.WhiteSmoke;
      this.label_Proc_PHC2.Text = "";
      this.dataGridView_Proc_PHC2.AllowUserToAddRows = false;
      this.dataGridView_Proc_PHC2.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_Proc_PHC2.Rows.Clear();
      if (this.newComboBox_Proc_PHC.SelectedIndex <= 0)
        return;
      this.label_Proc_PHC1.BackColor = Color.Lavender;
      this.label_Proc_PHC1.Text = "Duration";
      this.dataGridView_Proc_PHC1.AllowUserToAddRows = true;
      this.dataGridView_Proc_PHC1.BackgroundColor = Color.White;
      this.label_Proc_PHC2.BackColor = Color.Lavender;
      this.label_Proc_PHC2.Text = "Pressure";
      this.dataGridView_Proc_PHC2.AllowUserToAddRows = true;
      this.dataGridView_Proc_PHC2.BackgroundColor = Color.White;
    }

    private void dataGridView_Proc_PHC1_MouseLeave(object sender, EventArgs e) => this.dataGridView_Proc_PHC1.ClearSelection();

    private void dataGridView_Proc_PHC1_RowsAdded(object sender, DataGridViewRowsAddedEventArgs e)
    {
      for (int index = 0; index < this.dataGridView_Proc_PHC1.Rows.Count; ++index)
        this.dataGridView_Proc_PHC1.Rows[index].Cells[1].Value = (object) "s";
    }

    private void dataGridView_Proc_PHC1_UserAddedRow(object sender, DataGridViewRowEventArgs e)
    {
      if (this.dataGridView_Proc_PHC1.Rows.Count >= 4)
        this.dataGridView_Proc_PHC1.AllowUserToAddRows = false;
      else
        this.dataGridView_Proc_PHC1.AllowUserToAddRows = true;
    }

    private void dataGridView_Proc_PHC1_CellEndEdit(object sender, DataGridViewCellEventArgs e)
    {
      if (e.ColumnIndex != 0 || this.dataGridView_Proc_PHC1.Rows[e.RowIndex].Cells[e.ColumnIndex].Value == null || !(this.dataGridView_Proc_PHC1.Rows[e.RowIndex].Cells[e.ColumnIndex].Value.ToString() != ""))
        return;
      this.dataGridView_Proc_PHC1.Rows[e.RowIndex].Cells[1].Value = (object) "s";
    }

    private void dataGridView_Proc_PHC1_SelectionChanged(object sender, EventArgs e)
    {
      if (this.dataGridView_Proc_PHC1.CurrentCell == null || this.dataGridView_Proc_PHC1.CurrentCell.ColumnIndex != 1)
        return;
      this.dataGridView_Proc_PHC1.CurrentCell.Selected = false;
    }

    private void dataGridView_Proc_PHC2_CellEndEdit(object sender, DataGridViewCellEventArgs e)
    {
      if (e.ColumnIndex != 0 || this.dataGridView_Proc_PHC2.Rows[e.RowIndex].Cells[e.ColumnIndex].Value == null || !(this.dataGridView_Proc_PHC2.Rows[e.RowIndex].Cells[e.ColumnIndex].Value.ToString() != ""))
        return;
      if (this.newComboBox_Proc_PHC.SelectedIndex == 1)
        this.dataGridView_Proc_PHC2.Rows[e.RowIndex].Cells[1].Value = (object) "%";
      else if (this.newComboBox_Proc_PHC.SelectedIndex == 2)
        this.dataGridView_Proc_PHC2.Rows[e.RowIndex].Cells[1].Value = (object) "MPa";
    }

    private void dataGridView_Proc_PHC2_RowsAdded(object sender, DataGridViewRowsAddedEventArgs e)
    {
      for (int index = 0; index < this.dataGridView_Proc_PHC2.Rows.Count; ++index)
      {
        if (this.newComboBox_Proc_PHC.SelectedIndex == 1)
          this.dataGridView_Proc_PHC2.Rows[index].Cells[1].Value = (object) "%";
        else if (this.newComboBox_Proc_PHC.SelectedIndex == 2)
          this.dataGridView_Proc_PHC2.Rows[index].Cells[1].Value = (object) "MPa";
      }
    }

    private void dataGridView_Proc_PHC2_UserAddedRow(object sender, DataGridViewRowEventArgs e)
    {
      if (this.dataGridView_Proc_PHC2.Rows.Count >= 4)
        this.dataGridView_Proc_PHC2.AllowUserToAddRows = false;
      else
        this.dataGridView_Proc_PHC2.AllowUserToAddRows = true;
    }

    private void dataGridView_Proc_PHC2_SelectionChanged(object sender, EventArgs e)
    {
      if (this.dataGridView_Proc_PHC2.CurrentCell == null || this.dataGridView_Proc_PHC2.CurrentCell.ColumnIndex != 1)
        return;
      this.dataGridView_Proc_PHC2.CurrentCell.Selected = false;
    }

    private void dataGridView_Proc_PHC2_MouseLeave(object sender, EventArgs e) => this.dataGridView_Proc_PHC2.ClearSelection();

    private void newTextBox_Suji_Search_TextBoxKeyUp(object sender, KeyEventArgs e)
    {
      if (e.KeyCode != Keys.Return)
        return;
      this.RefreshMaterial();
    }

    private void ImportUserUDB()
    {
      bool flag = false;
      try
      {
        OpenFileDialog openFileDialog = new OpenFileDialog();
        openFileDialog.Filter = "Autodesk Moldflow Material Files(*.21000.udb)|*.21000.udb";
        if (openFileDialog.ShowDialog() != DialogResult.OK)
          return;
        FileInfo fileInfo = new FileInfo(openFileDialog.FileName);
        foreach (FileInfo file in clsDefine.g_diUserUDB.GetFiles("*.21000.udb"))
        {
          if (fileInfo.Name.ToUpper() == file.Name.ToUpper())
          {
            flag = true;
            break;
          }
        }
        if (flag)
          return;
        fileInfo.CopyTo(clsDefine.g_diUserUDB.ToString() + "\\" + fileInfo.Name);
        DataTable table = clsDefine.g_dsMaterial.Tables.Cast<DataTable>().Where<DataTable>((System.Func<DataTable, bool>) (Temp => Temp.TableName == "User")).FirstOrDefault<DataTable>();
        if (table != null)
          clsDefine.g_dsMaterial.Tables.Remove(table);
        DataTable materialsFromName = clsHDMFLib.GetUserMaterialsFromName(fileInfo.Name.Replace(".21000.udb", ""));
        if (materialsFromName != null)
          clsDefine.g_dsMaterial.Tables.Add(materialsFromName);
        string str1 = this.newComboBox_Suji_Manufacturer.Value;
        string str2 = this.newComboBox_Suji_TradeName.Value;
        this.RefreshMaterial();
        this.newComboBox_Suji_Manufacturer.SelectedIndex = this.newComboBox_Suji_Manufacturer.Items.IndexOf((object) str1);
        this.newComboBox_Suji_TradeName.SelectedIndex = this.newComboBox_Suji_TradeName.Items.IndexOf((object) str2);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcess]ImportUserUDB):" + ex.Message));
      }
    }

    private void frmProcess_FormClosed(object sender, FormClosedEventArgs e)
    {
      if (this.DialogResult == DialogResult.OK)
        return;
      string[] arr_strMaterial = clsHDMFLib.GetCurrentMaterial(clsDefine.g_dsMaterial.Tables.Cast<DataTable>().Where<DataTable>((System.Func<DataTable, bool>) (Temp => Temp.TableName == "User")).FirstOrDefault<DataTable>());
      if (this.m_drMaterial != clsDefine.g_dsMaterial.Tables[arr_strMaterial[0]].AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["ID"].ToString() == arr_strMaterial[1])).FirstOrDefault<DataRow>())
        clsHDMFLib.SelectSuji(this.m_drMaterial);
    }

    public void RefreshDBList(string p_strCompany = null, string p_strItem = null)
    {
      this.listBox_DB.Items.Clear();
      if (p_strCompany == null)
        p_strCompany = LocaleControl.getInstance().GetString("IDS_ALL");
      if (p_strItem == null)
        p_strItem = LocaleControl.getInstance().GetString("IDS_ALL");
      try
      {
        DataRow[] array = clsDefine.g_dtProcessDB.AsEnumerable().ToArray<DataRow>();
        if (p_strCompany != LocaleControl.getInstance().GetString("IDS_ALL"))
          array = ((IEnumerable<DataRow>) array).Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Company"].ToString() == p_strCompany)).ToArray<DataRow>();
        if (array.Length == 0)
          return;
        if (p_strItem != LocaleControl.getInstance().GetString("IDS_ALL"))
          array = ((IEnumerable<DataRow>) array).Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Item"].ToString() == p_strItem)).ToArray<DataRow>();
        if (array.Length != 0)
        {
          List<string> stringList = new List<string>();
          foreach (DataRow dataRow in array)
            stringList.Add(dataRow["Name"].ToString());
          stringList.Sort();
          this.listBox_DB.Items.AddRange((object[]) stringList.ToArray());
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcess]RefreshDBList):" + ex.Message));
      }
    }

    public void RefreshCompanyUI(string p_strCompany = null)
    {
      this.newComboBox_Company.Items.Clear();
      if (p_strCompany == null)
        p_strCompany = LocaleControl.getInstance().GetString("IDS_ALL");
      try
      {
        this.newComboBox_Company.Items.Add((object) LocaleControl.getInstance().GetString("IDS_ALL"));
        DataRow[] array = clsDefine.g_dtProcessDB.AsEnumerable().ToArray<DataRow>();
        List<string> stringList = new List<string>();
        foreach (DataRow dataRow in array)
        {
          if (!stringList.Contains(dataRow["Company"].ToString()))
            stringList.Add(dataRow["Company"].ToString());
        }
        stringList.Sort();
        this.newComboBox_Company.Items.AddRange((object[]) stringList.ToArray());
        this.newComboBox_Company.SelectedIndex = this.newComboBox_Company.Items.IndexOf((object) p_strCompany);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcess]RefreshCompanyUI):" + ex.Message));
      }
    }

    public void RefreshItemUI(string p_strCompany = null)
    {
      this.newComboBox_Item.Items.Clear();
      if (p_strCompany == null)
        p_strCompany = LocaleControl.getInstance().GetString("IDS_ALL");
      try
      {
        this.newComboBox_Item.Items.Add((object) LocaleControl.getInstance().GetString("IDS_ALL"));
        DataRow[] array = clsDefine.g_dtProcessDB.AsEnumerable().ToArray<DataRow>();
        if (p_strCompany != LocaleControl.getInstance().GetString("IDS_ALL"))
          array = ((IEnumerable<DataRow>) array).Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Company"].ToString() == p_strCompany)).ToArray<DataRow>();
        if (array.Length != 0)
        {
          List<string> stringList = new List<string>();
          foreach (DataRow dataRow in array)
          {
            if (!stringList.Contains(dataRow["Item"].ToString()))
              stringList.Add(dataRow["Item"].ToString());
          }
          stringList.Sort();
          this.newComboBox_Item.Items.AddRange((object[]) stringList.ToArray());
          this.newComboBox_Item.SelectedIndex = 0;
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcess]RefreshItemUI):" + ex.Message));
      }
      if (this.newComboBox_Item.Items.Count == 1)
      {
        this.newComboBox_Item.Items.Clear();
        this.newComboBox_Item.Enabled = false;
      }
      else
        this.newComboBox_Item.Enabled = true;
    }

    private void newComboBox_Company_SelectedIndexChanged(object sender, EventArgs e)
    {
      this.RefreshDBList(this.newComboBox_Company.Value);
      this.RefreshItemUI(this.newComboBox_Company.Value);
    }

    private void newComboBox_Item_SelectedIndexChanged(object sender, EventArgs e) => this.RefreshDBList(this.newComboBox_Company.Value, this.newComboBox_Item.Value);

    private void listBox_DB_SelectedIndexChanged(object sender, EventArgs e)
    {
      DataRow dataRow = (DataRow) null;
      DataRow drProcessDB = clsDefine.g_dtProcessDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == this.listBox_DB.Text)).FirstOrDefault<DataRow>();
      if (drProcessDB == null)
        return;
      this.Hide();
      clsUtill.StartProgress("apply Process DB...", (Form) this);
      try
      {
        DataTable table = clsDefine.g_dsMaterial.Tables.Cast<DataTable>().Where<DataTable>((System.Func<DataTable, bool>) (Temp => Temp.TableName == "User")).FirstOrDefault<DataTable>();
        if (table != null)
          clsDefine.g_dsMaterial.Tables.Remove(table);
        if (drProcessDB["Mat_UDB"] == DBNull.Value || drProcessDB["Mat_UDB"].ToString() == "")
        {
          this.radioButton_Suji_System.Checked = true;
          DataRow[] array = clsDefine.g_dsMaterial.Tables["System"].AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Manufacturer"].ToString() == drProcessDB["Mat_Manufacturer"].ToString() && Temp["TradeName"].ToString() == drProcessDB["Mat_TradeName"].ToString())).ToArray<DataRow>();
          for (int index = 0; index < array.Length; ++index)
          {
            if (clsHDMFLib.CheckSuji(array[index], array[index]["Manufacturer"].ToString() + ".21000.udb"))
            {
              dataRow = array[index];
              break;
            }
          }
        }
        else
        {
          FileInfo fiUserUDB = new FileInfo(clsDefine.g_diProcessUDBCfg.ToString() + "\\" + drProcessDB["Mat_UDB"] + ".21000.udb");
          if (fiUserUDB.Exists)
          {
            if (!((IEnumerable<FileInfo>) clsDefine.g_diUserUDB.GetFiles("*.21000.udb")).Any<FileInfo>((System.Func<FileInfo, bool>) (Temp => Temp.Name == fiUserUDB.Name)))
              fiUserUDB.CopyTo(clsDefine.g_diUserUDB.FullName + "\\" + fiUserUDB.Name);
            DataTable materialsFromName = clsHDMFLib.GetUserMaterialsFromName(drProcessDB["Mat_UDB"].ToString());
            if (materialsFromName != null)
              clsDefine.g_dsMaterial.Tables.Add(materialsFromName);
            string str1 = this.newComboBox_Suji_Manufacturer.Value;
            string str2 = this.newComboBox_Suji_TradeName.Value;
            this.newTextBox_Suji_Search.Value = "";
            this.RefreshMaterial();
            if (this.newComboBox_Suji_Manufacturer.Items.Contains((object) str1))
              this.newComboBox_Suji_Manufacturer.SelectedIndex = this.newComboBox_Suji_Manufacturer.Items.IndexOf((object) str1);
            if (this.newComboBox_Suji_TradeName.Items.Contains((object) str2))
              this.newComboBox_Suji_TradeName.SelectedIndex = this.newComboBox_Suji_TradeName.Items.IndexOf((object) str2);
            this.radioButton_Suji_User.Checked = true;
            DataRow[] array = clsDefine.g_dsMaterial.Tables["User"].AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Manufacturer"].ToString() == drProcessDB["Mat_Manufacturer"].ToString() && Temp["TradeName"].ToString() == drProcessDB["Mat_TradeName"].ToString())).ToArray<DataRow>();
            for (int index = 0; index < array.Length; ++index)
            {
              if (clsHDMFLib.CheckSuji(array[index], fiUserUDB.Name))
              {
                dataRow = array[index];
                break;
              }
            }
          }
        }
        if (dataRow != null)
        {
          this.newComboBox_Suji_Manufacturer.SelectedIndex = this.newComboBox_Suji_Manufacturer.Items.IndexOf((object) dataRow["Manufacturer"].ToString());
          this.newComboBox_Suji_TradeName.SelectedIndex = this.newComboBox_Suji_TradeName.Items.IndexOf((object) (dataRow["TradeName"].ToString() + " [ID:" + dataRow["ID"].ToString() + "]"));
        }
        this.SelectSuji(false);
        this.newComboBox_Proc_CoolType.SelectedIndex = clsUtill.ConvertToInt(drProcessDB["Mat_Sequence"].ToString().Replace(" ", ""));
        this.newTextBox_Proc_CoolingTime.Value = drProcessDB["Mat_CTime"].ToString();
        this.newTextBox_Proc_MeltTemp1.Value = drProcessDB["Mat_MeltTemp"].ToString();
        this.newTextBox_Proc_MoldTemp1.Value = drProcessDB["Mat_MoldTemp"].ToString();
        this.newComboBox_Proc_FC.SelectedIndex = this.newComboBox_Proc_FC.Items.IndexOf((object) drProcessDB["FC_Type"].ToString());
        if (this.dataGridView_Proc_FC1.Enabled)
        {
          int num = 0;
          this.dataGridView_Proc_FC1.Rows.Clear();
          for (int index = 0; index < 14; ++index)
          {
            string str = drProcessDB["FC1_" + (object) (index + 1)].ToString();
            if (!(str == ""))
            {
              this.dataGridView_Proc_FC1.Rows[this.dataGridView_Proc_FC1.Rows.Add()].Cells[0].Value = (object) str;
              ++num;
            }
          }
          this.dataGridView_Proc_FC1.ClearSelection();
        }
        if (this.dataGridView_Proc_FC2.Enabled)
        {
          int num = 0;
          this.dataGridView_Proc_FC2.Rows.Clear();
          for (int index = 0; index < 14; ++index)
          {
            string str = drProcessDB["FC2_" + (object) (index + 1)].ToString();
            if (!(str == ""))
            {
              this.dataGridView_Proc_FC2.Rows[this.dataGridView_Proc_FC2.Rows.Add()].Cells[0].Value = (object) str;
              ++num;
            }
          }
          this.dataGridView_Proc_FC2.ClearSelection();
        }
        if (this.dataGridView_Proc_FC3.Enabled)
        {
          this.dataGridView_Proc_FC3.Rows[0].Cells[0].Value = (object) drProcessDB["FC3"].ToString();
          this.dataGridView_Proc_FC3.ClearSelection();
        }
        if (this.dataGridView_Proc_FC4.Enabled)
        {
          this.dataGridView_Proc_FC4.Rows[0].Cells[0].Value = (object) drProcessDB["FC4"].ToString();
          this.dataGridView_Proc_FC4.ClearSelection();
        }
        this.newComboBox_Proc_VP.SelectedIndex = this.newComboBox_Proc_VP.Items.IndexOf((object) drProcessDB["VP_Type"].ToString());
        if (this.newComboBox_Proc_VP.SelectedIndex == 2)
          this.dataGridView_Proc_VP.Rows[0].Cells[0].Value = (object) drProcessDB["VP_Value"].ToString();
        this.newComboBox_Proc_PHC.SelectedIndex = this.newComboBox_Proc_PHC.Items.IndexOf((object) drProcessDB["PHC_Type"].ToString());
        if (this.dataGridView_Proc_PHC1.Enabled)
        {
          int num = 0;
          this.dataGridView_Proc_PHC1.Rows.Clear();
          for (int index = 0; index < 3; ++index)
          {
            string str = drProcessDB["PHC1_" + (object) (index + 1)].ToString();
            if (!(str == ""))
            {
              this.dataGridView_Proc_PHC1.Rows[this.dataGridView_Proc_PHC1.Rows.Add()].Cells[0].Value = (object) str;
              ++num;
            }
          }
          this.dataGridView_Proc_PHC1.ClearSelection();
        }
        if (this.dataGridView_Proc_PHC2.Enabled)
        {
          int num = 0;
          this.dataGridView_Proc_PHC2.Rows.Clear();
          for (int index = 0; index < 3; ++index)
          {
            string str = drProcessDB["PHC2_" + (object) (index + 1)].ToString();
            if (!(str == ""))
            {
              this.dataGridView_Proc_PHC2.Rows[this.dataGridView_Proc_PHC2.Rows.Add()].Cells[0].Value = (object) str;
              ++num;
            }
          }
          this.dataGridView_Proc_PHC2.ClearSelection();
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcess]listBox_DB_SelectedIndexChanged):" + ex.Message));
      }
      clsUtill.EndProgress();
      clsUtill.ShowForm((Form) this);
    }

    private void SetProcess()
    {
      bool flag = false;
      Dictionary<string, string> p_dicProcData = new Dictionary<string, string>();
      this.Hide();
      clsUtill.StartProgress("applying Process Setting...", (Form) this);
      try
      {
        List<string> stringList = new List<string>();
        if (this.listBox_Inj.SelectedIndex > 0)
        {
          p_dicProcData.Add("Inj_Name", this.newTextBox_Inj_Name.Value);
          p_dicProcData.Add("Inj_Company", this.newTextBox_Inj_Company.Value);
          p_dicProcData.Add("Inj_DataSource", this.newTextBox_Inj_DataSource.Value);
          p_dicProcData.Add("Inj_DateLast", this.newTextBox_Inj_DataLastModified.Value);
          p_dicProcData.Add("Inj_MaxStroke", this.unitTextBox_Inj_MaxStroke.Value);
          p_dicProcData.Add("Inj_MaxRate", this.unitTextBox_Inj_MaxRate.Value);
          p_dicProcData.Add("Inj_ScrewDia", this.unitTextBox_Inj_ScrewDia.Value);
          p_dicProcData.Add("Inj_MaxPressure", this.unitTextBox_Inj_MaxPressure.Value);
          p_dicProcData.Add("Inj_MaxClamp", this.unitTextBox_Inj_MaxClampForce.Value);
          p_dicProcData.Add("Inj_PreRatio", this.newTextBox_Inj_PreRatio.Value);
        }
        p_dicProcData.Add("Proc_CoolType", this.newTextBox_Proc_CoolType.Value);
        p_dicProcData.Add("Proc_CoolingTime", this.newTextBox_Proc_CoolingTime.Value);
        p_dicProcData.Add("Proc_MoldTemp", this.newTextBox_Proc_MoldTemp1.Value);
        p_dicProcData.Add("Proc_MeltTemp", this.newTextBox_Proc_MeltTemp1.Value);
        p_dicProcData.Add("Proc_FCType", this.newComboBox_Proc_FC.SelectedIndex.ToString());
        for (int index = 0; index < this.dataGridView_Proc_FC1.Rows.Count; ++index)
        {
          if (this.dataGridView_Proc_FC1.Rows[index].Cells[0].Value != null && !(this.dataGridView_Proc_FC1.Rows[index].Cells[0].Value.ToString() == ""))
            p_dicProcData.Add("Proc_FCData1_" + (object) index, this.dataGridView_Proc_FC1.Rows[index].Cells[0].Value.ToString());
        }
        for (int index = 0; index < this.dataGridView_Proc_FC2.Rows.Count; ++index)
        {
          if (this.dataGridView_Proc_FC2.Rows[index].Cells[0].Value != null && !(this.dataGridView_Proc_FC2.Rows[index].Cells[0].Value.ToString() == ""))
            p_dicProcData.Add("Proc_FCData2_" + (object) index, this.dataGridView_Proc_FC2.Rows[index].Cells[0].Value.ToString());
        }
        for (int index = 0; index < this.dataGridView_Proc_FC3.Rows.Count; ++index)
        {
          if (this.dataGridView_Proc_FC3.Rows[index].Cells[0].Value != null && !(this.dataGridView_Proc_FC3.Rows[index].Cells[0].Value.ToString() == ""))
            p_dicProcData.Add("Proc_FCData3_" + (object) index, this.dataGridView_Proc_FC3.Rows[index].Cells[0].Value.ToString());
        }
        for (int index = 0; index < this.dataGridView_Proc_FC4.Rows.Count; ++index)
        {
          if (this.dataGridView_Proc_FC4.Rows[index].Cells[0].Value != null && !(this.dataGridView_Proc_FC4.Rows[index].Cells[0].Value.ToString() == ""))
            p_dicProcData.Add("Proc_FCData4_" + (object) index, this.dataGridView_Proc_FC4.Rows[index].Cells[0].Value.ToString());
        }
        Dictionary<string, string> dictionary1 = p_dicProcData;
        int selectedIndex = this.newComboBox_Proc_VP.SelectedIndex;
        string str1 = selectedIndex.ToString();
        dictionary1.Add("Proc_VPType", str1);
        if (this.dataGridView_Proc_VP.Rows.Count > 0)
          p_dicProcData.Add("Proc_VPData", this.dataGridView_Proc_VP.Rows[0].Cells[0].Value.ToString());
        Dictionary<string, string> dictionary2 = p_dicProcData;
        selectedIndex = this.newComboBox_Proc_PHC.SelectedIndex;
        string str2 = selectedIndex.ToString();
        dictionary2.Add("Proc_PHType", str2);
        for (int index = 0; index < this.dataGridView_Proc_PHC1.Rows.Count; ++index)
        {
          if (this.dataGridView_Proc_PHC1.Rows[index].Cells[0].Value != null && !(this.dataGridView_Proc_PHC1.Rows[index].Cells[0].Value.ToString() == ""))
            p_dicProcData.Add("Proc_PHData1_" + (object) index, this.dataGridView_Proc_PHC1.Rows[index].Cells[0].Value.ToString());
        }
        for (int index = 0; index < this.dataGridView_Proc_PHC2.Rows.Count; ++index)
        {
          if (this.dataGridView_Proc_PHC2.Rows[index].Cells[0].Value != null && !(this.dataGridView_Proc_PHC2.Rows[index].Cells[0].Value.ToString() == ""))
            p_dicProcData.Add("Proc_PHData2_" + (object) index, this.dataGridView_Proc_PHC2.Rows[index].Cells[0].Value.ToString());
        }
        this.m_drStudy["Sequence"] = (object) this.newTextBox_Proc_CoolType.Value;
        flag = clsHDMFLib.SetProcessSet(p_dicProcData);
        this.SaveProcessSettingData();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcess]SetProcess):" + ex.Message));
      }
      clsUtill.EndProgress();
      if (flag)
      {
        this.DialogResult = DialogResult.OK;
        this.Close();
      }
      else
        clsUtill.ShowForm((Form) this);
    }

    private void SaveProcessSettingData()
    {
      try
      {
        FileInfo fileInfo = new FileInfo(clsDefine.g_diCfg.FullName + "\\Process.ini");
        if (fileInfo.Exists)
          fileInfo.Delete();
        else if (!fileInfo.Directory.Exists)
          fileInfo.Directory.Create();
        string[] arr_strMaterial = clsHDMFLib.GetCurrentMaterial(clsDefine.g_dsMaterial.Tables.Cast<DataTable>().Where<DataTable>((System.Func<DataTable, bool>) (Temp => Temp.TableName == "User")).FirstOrDefault<DataTable>());
        DataRow dataRow = clsDefine.g_dsMaterial.Tables[arr_strMaterial[0]].AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["ID"].ToString() == arr_strMaterial[1])).FirstOrDefault<DataRow>();
        if (dataRow != null)
        {
          clsUtill.WriteINI("Material", "Type", dataRow.Table.TableName, fileInfo.FullName);
          clsUtill.WriteINI("Material", "ID", dataRow["ID"].ToString(), fileInfo.FullName);
        }
        clsUtill.WriteINI("Process", "Proc_CoolType", this.newComboBox_Proc_CoolType.SelectedIndex.ToString(), fileInfo.FullName);
        clsUtill.WriteINI("Process", "Proc_CoolingTimeType", this.label_Proc_CoolingTime.Text, fileInfo.FullName);
        clsUtill.WriteINI("Process", "Proc_CoolingTime", this.newTextBox_Proc_CoolingTime.Value, fileInfo.FullName);
        clsUtill.WriteINI("Process", "Proc_MoldTemp", this.newTextBox_Proc_MoldTemp1.Value, fileInfo.FullName);
        clsUtill.WriteINI("Process", "Proc_MeltTemp", this.newTextBox_Proc_MeltTemp1.Value, fileInfo.FullName);
        clsUtill.WriteINI("Process", "Proc_FCType", this.newComboBox_Proc_FC.SelectedIndex.ToString(), fileInfo.FullName);
        for (int index = 0; index < this.dataGridView_Proc_FC1.Rows.Count; ++index)
        {
          if (this.dataGridView_Proc_FC1.Rows[index].Cells[0].Value != null && !(this.dataGridView_Proc_FC1.Rows[index].Cells[0].Value.ToString() == ""))
            clsUtill.WriteINI("Process", "Proc_FCData1_" + (object) index, this.dataGridView_Proc_FC1.Rows[index].Cells[0].Value.ToString(), fileInfo.FullName);
        }
        for (int index = 0; index < this.dataGridView_Proc_FC2.Rows.Count; ++index)
        {
          if (this.dataGridView_Proc_FC2.Rows[index].Cells[0].Value != null && !(this.dataGridView_Proc_FC2.Rows[index].Cells[0].Value.ToString() == ""))
            clsUtill.WriteINI("Process", "Proc_FCData2_" + (object) index, this.dataGridView_Proc_FC2.Rows[index].Cells[0].Value.ToString(), fileInfo.FullName);
        }
        for (int index = 0; index < this.dataGridView_Proc_FC3.Rows.Count; ++index)
        {
          if (this.dataGridView_Proc_FC3.Rows[index].Cells[0].Value != null && !(this.dataGridView_Proc_FC3.Rows[index].Cells[0].Value.ToString() == ""))
            clsUtill.WriteINI("Process", "Proc_FCData3_" + (object) index, this.dataGridView_Proc_FC3.Rows[index].Cells[0].Value.ToString(), fileInfo.FullName);
        }
        for (int index = 0; index < this.dataGridView_Proc_FC4.Rows.Count; ++index)
        {
          if (this.dataGridView_Proc_FC4.Rows[index].Cells[0].Value != null && !(this.dataGridView_Proc_FC4.Rows[index].Cells[0].Value.ToString() == ""))
            clsUtill.WriteINI("Process", "Proc_FCData4_" + (object) index, this.dataGridView_Proc_FC4.Rows[index].Cells[0].Value.ToString(), fileInfo.FullName);
        }
        clsUtill.WriteINI("Process", "Proc_VPType", this.newComboBox_Proc_VP.SelectedIndex.ToString(), fileInfo.FullName);
        if (this.dataGridView_Proc_VP.Rows.Count > 0)
          clsUtill.WriteINI("Process", "Proc_VPData", this.dataGridView_Proc_VP.Rows[0].Cells[0].Value.ToString(), fileInfo.FullName);
        clsUtill.WriteINI("Process", "Proc_PHType", this.newComboBox_Proc_PHC.SelectedIndex.ToString(), fileInfo.FullName);
        for (int index = 0; index < this.dataGridView_Proc_PHC1.Rows.Count; ++index)
        {
          if (this.dataGridView_Proc_PHC1.Rows[index].Cells[0].Value != null && !(this.dataGridView_Proc_PHC1.Rows[index].Cells[0].Value.ToString() == ""))
            clsUtill.WriteINI("Process", "Proc_PHData1_" + (object) index, this.dataGridView_Proc_PHC1.Rows[index].Cells[0].Value.ToString(), fileInfo.FullName);
        }
        for (int index = 0; index < this.dataGridView_Proc_PHC2.Rows.Count; ++index)
        {
          if (this.dataGridView_Proc_PHC2.Rows[index].Cells[0].Value != null && !(this.dataGridView_Proc_PHC2.Rows[index].Cells[0].Value.ToString() == ""))
            clsUtill.WriteINI("Process", "Proc_PHData2_" + (object) index, this.dataGridView_Proc_PHC2.Rows[index].Cells[0].Value.ToString(), fileInfo.FullName);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcess]SaveProcessSettingData):" + ex.Message));
      }
    }

    private void LoadProcessSettingData()
    {
      List<string> stringList = new List<string>();
      StringBuilder stringBuilder = new StringBuilder();
      this.listBox_DB.ClearSelected();
      this.Hide();
      clsUtill.StartProgress("apply Before Process...", (Form) this);
      try
      {
        FileInfo fileInfo = new FileInfo(clsDefine.g_diCfg.FullName + "\\Process.ini");
        if (!fileInfo.Exists)
          return;
        string name = clsUtill.ReadINI("Material", "Type", fileInfo.FullName);
        if (name != "")
        {
          if (name == "System")
            this.radioButton_Suji_System.Checked = true;
          else
            this.radioButton_Suji_User.Checked = true;
          string strMaterialID = clsUtill.ReadINI("Material", "ID", fileInfo.FullName);
          DataRow p_drMaterial = clsDefine.g_dsMaterial.Tables[name].AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["ID"].ToString() == strMaterialID)).FirstOrDefault<DataRow>();
          if (p_drMaterial != null)
          {
            Dictionary<string, string> dictionary = clsHDMFLib.SelectSuji(p_drMaterial, true);
            if (dictionary["UDB"] == "System")
              this.radioButton_Suji_System.Checked = true;
            else
              this.radioButton_Suji_User.Checked = true;
            this.newComboBox_Suji_Manufacturer.SelectedIndex = this.newComboBox_Suji_Manufacturer.Items.IndexOf((object) p_drMaterial["Manufacturer"].ToString());
            this.newComboBox_Suji_TradeName.SelectedIndex = this.newComboBox_Suji_TradeName.Items.IndexOf((object) (p_drMaterial["TradeName"].ToString() + " [ID:" + p_drMaterial["ID"].ToString() + "]"));
            this.newTextBox_Suji_UDB.Value = dictionary["UDB"];
            this.newTextBox_Suji_Image1.Value = p_drMaterial["TradeName"].ToString() + "_PVT_PLOT";
            this.newTextBox_Suji_Image2.Value = p_drMaterial["TradeName"].ToString() + "_RHEOLOGICAL_PLOT";
            using (StreamReader streamReader = new StreamReader(dictionary["PVTPLOT"]))
              this.pictureBox_Suji_Image1.Image = Image.FromStream(streamReader.BaseStream);
            using (StreamReader streamReader = new StreamReader(dictionary["RHEOLOGICALPLOT"]))
              this.pictureBox_Suji_Image2.Image = Image.FromStream(streamReader.BaseStream);
            this.newTextBox_Suji_EjectionTemp.Value = dictionary["EjectionTemp"];
            this.newTextBox_Suji_TransitionTemp.Value = dictionary["TransitionTemp"];
            this.newTextBox_Suji_MFR.Value = dictionary["MFR"];
            this.newTextBox_Suji_MoldTemp1.Value = dictionary["MoldTemp1"];
            this.newTextBox_Suji_MoldTemp2.Value = dictionary["MoldTemp2"];
            this.newTextBox_Suji_MeltTemp1.Value = dictionary["MeltTemp1"];
            this.newTextBox_Suji_MeltTemp2.Value = dictionary["MeltTemp2"];
            this.newTextBox_Proc_MoldTemp1.Value = dictionary["MoldTemp1"];
            this.newTextBox_Proc_MoldTemp2.Value = dictionary["MoldTemp2"];
            this.newTextBox_Proc_MeltTemp1.Value = dictionary["MeltTemp1"];
            this.newTextBox_Proc_MeltTemp2.Value = dictionary["MeltTemp2"];
          }
        }
        this.newComboBox_Proc_CoolType.SelectedIndex = clsUtill.ConvertToInt(clsUtill.ReadINI("Process", "Proc_CoolType", fileInfo.FullName));
        switch (this.newComboBox_Proc_CoolType.SelectedIndex)
        {
          case 0:
            this.newTextBox_Proc_CoolType.Value = "Cool";
            break;
          case 1:
            this.newTextBox_Proc_CoolType.Text = "Flow";
            break;
          case 2:
            this.newTextBox_Proc_CoolType.Text = "Flow|Warp";
            break;
          case 3:
            this.newTextBox_Proc_CoolType.Text = "Cool|Flow|Warp";
            break;
          case 4:
            this.newTextBox_Proc_CoolType.Text = "Cool|Flow";
            break;
        }
        this.label_Proc_CoolingTime.Text = clsUtill.ReadINI("Process", "Proc_CoolingTimeType", fileInfo.FullName);
        this.newTextBox_Proc_CoolingTime.Value = clsUtill.ReadINI("Process", "Proc_CoolingTime", fileInfo.FullName);
        this.newTextBox_Proc_MeltTemp1.Value = clsUtill.ReadINI("Process", "Proc_MeltTemp", fileInfo.FullName);
        this.newTextBox_Proc_MeltTemp2.Value = clsHDMFLib.GetMeltTemperatureData()[1];
        this.newTextBox_Proc_MoldTemp1.Value = clsUtill.ReadINI("Process", "Proc_MoldTemp", fileInfo.FullName);
        this.newTextBox_Proc_MoldTemp2.Value = clsHDMFLib.GetMoldTemperatureData()[1];
        stringList.Clear();
        string p_strValue = clsUtill.ReadINI("Process", "Proc_FCType", fileInfo.FullName);
        if (p_strValue == "")
        {
          this.newComboBox_Proc_FC.SelectedIndex = 0;
        }
        else
        {
          this.newComboBox_Proc_FC.SelectedIndex = clsUtill.ConvertToInt(p_strValue);
          if (this.newComboBox_Proc_FC.SelectedIndex > 1)
          {
            stringBuilder.Clear();
            for (int index = 0; index < 13; ++index)
            {
              string str = clsUtill.ReadINI("Process", "Proc_FCData1_" + (object) index, fileInfo.FullName);
              if (!(str == ""))
              {
                if (stringBuilder.Length > 0)
                  stringBuilder.Append(",");
                stringBuilder.Append(str);
              }
            }
            if (stringBuilder.Length > 0)
              stringList.Add(stringBuilder.ToString());
            stringBuilder.Clear();
            for (int index = 0; index < 13; ++index)
            {
              string str = clsUtill.ReadINI("Process", "Proc_FCData2_" + (object) index, fileInfo.FullName);
              if (!(str == ""))
              {
                if (stringBuilder.Length > 0)
                  stringBuilder.Append(",");
                stringBuilder.Append(str);
              }
            }
            if (stringBuilder.Length > 0)
              stringList.Add(stringBuilder.ToString());
            string str1 = clsUtill.ReadINI("Process", "Proc_FCData3_0", fileInfo.FullName);
            if (str1 != "")
              stringList.Add(str1);
            string str2 = clsUtill.ReadINI("Process", "Proc_FCData4_0", fileInfo.FullName);
            if (str2 != "")
              stringList.Add(str2);
            for (int index1 = 0; index1 < stringList.Count; ++index1)
            {
              switch (index1)
              {
                case 0:
                  this.dataGridView_Proc_FC1.Rows.Clear();
                  if (stringList[index1] != "")
                  {
                    string[] strArray = stringList[index1].Split(',');
                    for (int index2 = 0; index2 < strArray.Length; ++index2)
                    {
                      this.dataGridView_Proc_FC1.Rows.Add();
                      this.dataGridView_Proc_FC1.Rows[index2].Cells[0].Value = (object) strArray[index2];
                    }
                    break;
                  }
                  break;
                case 1:
                  this.dataGridView_Proc_FC2.Rows.Clear();
                  if (stringList[index1] != "")
                  {
                    string[] strArray = stringList[index1].Split(',');
                    for (int index3 = 0; index3 < strArray.Length; ++index3)
                    {
                      this.dataGridView_Proc_FC2.Rows.Add();
                      this.dataGridView_Proc_FC2.Rows[index3].Cells[0].Value = (object) strArray[index3];
                    }
                    break;
                  }
                  break;
                case 2:
                  this.dataGridView_Proc_FC3.Rows[0].Cells[0].Value = (object) stringList[index1];
                  break;
                case 3:
                  this.dataGridView_Proc_FC4.Rows[0].Cells[0].Value = (object) stringList[index1];
                  break;
              }
            }
          }
        }
        this.newComboBox_Proc_VP.SelectedIndex = clsUtill.ConvertToInt(clsUtill.ReadINI("Process", "Proc_VPType", fileInfo.FullName));
        if (this.newComboBox_Proc_VP.SelectedIndex > 1)
          this.dataGridView_Proc_VP.Rows[0].Cells[0].Value = (object) clsUtill.ReadINI("Process", "Proc_VPData", fileInfo.FullName);
        stringList.Clear();
        this.newComboBox_Proc_PHC.SelectedIndex = clsUtill.ConvertToInt(clsUtill.ReadINI("Process", "Proc_PHType", fileInfo.FullName));
        for (int index = 0; index < 3; ++index)
        {
          stringBuilder.Clear();
          string str3 = clsUtill.ReadINI("Process", "Proc_PHData1_" + (object) index, fileInfo.FullName);
          if (str3 != "")
            stringBuilder.Append(str3);
          string str4 = clsUtill.ReadINI("Process", "Proc_PHData2_" + (object) index, fileInfo.FullName);
          if (str4 != "")
          {
            if (stringBuilder.Length > 0)
              stringBuilder.Append("|");
            stringBuilder.Append(str4);
          }
          if (stringBuilder.Length > 0)
            stringList.Add(stringBuilder.ToString());
        }
        this.dataGridView_Proc_PHC1.Rows.Clear();
        this.dataGridView_Proc_PHC2.Rows.Clear();
        for (int index4 = 0; index4 < stringList.Count; ++index4)
        {
          this.dataGridView_Proc_PHC1.Rows[this.dataGridView_Proc_PHC1.Rows.Add()].Cells[0].Value = (object) stringList[index4].Split('|')[0];
          int index5 = this.dataGridView_Proc_PHC2.Rows.Add();
          this.dataGridView_Proc_PHC2.Rows[index5].Cells[0].Value = (object) stringList[index4].Split('|')[1];
          this.dataGridView_Proc_PHC1.Rows[index5].Cells[1].Value = (object) "s";
          this.dataGridView_Proc_PHC2.Rows[index5].Cells[1].Value = this.newComboBox_Proc_PHC.SelectedIndex != 1 ? (object) "MPa" : (object) "%";
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcess]LoadProcessSettingData):" + ex.Message));
      }
      clsUtill.EndProgress();
      clsUtill.ShowForm((Form) this);
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      DataGridViewCellStyle gridViewCellStyle1 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle2 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle3 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle4 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle5 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle6 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle7 = new DataGridViewCellStyle();
      this.label_Suji_Material = new Label();
      this.listBox_Inj = new ListBox();
      this.label_Inj_Name = new Label();
      this.label_Inj_Company = new Label();
      this.label4 = new Label();
      this.label5 = new Label();
      this.label_Inj_MaxStroke = new Label();
      this.label_Inj_MaxRate = new Label();
      this.label_Inj_ScrewDia = new Label();
      this.label_Inj_MaxPressure = new Label();
      this.label_Inj_MaxClamp = new Label();
      this.label_Inj_PreRatio = new Label();
      this.panel1 = new Panel();
      this.newButton_Suji_UserUDB = new NewButton();
      this.radioButton_Suji_User = new RadioButton();
      this.radioButton_Suji_System = new RadioButton();
      this.label12 = new Label();
      this.label13 = new Label();
      this.label14 = new Label();
      this.label15 = new Label();
      this.label16 = new Label();
      this.label17 = new Label();
      this.pictureBox_Suji_Image1 = new PictureBox();
      this.pictureBox_Suji_Image2 = new PictureBox();
      this.label18 = new Label();
      this.label19 = new Label();
      this.label20 = new Label();
      this.label21 = new Label();
      this.label22 = new Label();
      this.label_Proc_CoolingTime = new Label();
      this.label24 = new Label();
      this.label25 = new Label();
      this.label_Inj_Data = new Label();
      this.label_Inj_List = new Label();
      this.label_Suji_SearchCond = new Label();
      this.label_Suji_SearchValue = new Label();
      this.label_Suji_ResultValue = new Label();
      this.label_DB_Item = new Label();
      this.label_DB_Company = new Label();
      this.listBox_DB = new ListBox();
      this.label_DB = new Label();
      this.label32 = new Label();
      this.panel3 = new Panel();
      this.tabControl_Main = new TabControl();
      this.tabPage1 = new TabPage();
      this.newButton_Inj_Edit = new NewButton();
      this.unitTextBox_Inj_MaxRate = new UnitTextBox();
      this.newTextBox_Inj_DataLastModified = new NewTextBox();
      this.unitTextBox_Inj_ScrewDia = new UnitTextBox();
      this.newTextBox_Inj_DataSource = new NewTextBox();
      this.unitTextBox_Inj_MaxPressure = new UnitTextBox();
      this.unitTextBox_Inj_MaxStroke = new UnitTextBox();
      this.newTextBox_Inj_PreRatio = new NewTextBox();
      this.newTextBox_Inj_Company = new NewTextBox();
      this.newTextBox_Inj_Name = new NewTextBox();
      this.unitTextBox_Inj_MaxClampForce = new UnitTextBox();
      this.tabPage2 = new TabPage();
      this.label23 = new Label();
      this.newTextBox_Suji_MFR = new NewTextBox();
      this.newButton_Suji_Select = new NewButton();
      this.newTextBox_Suji_TransitionTemp = new NewTextBox();
      this.newTextBox33 = new NewTextBox();
      this.newTextBox_Suji_FamilyAbbreviation = new NewTextBox();
      this.newTextBox20 = new NewTextBox();
      this.newTextBox_Suji_MoldTemp2 = new NewTextBox();
      this.newTextBox_Suji_MeltTemp2 = new NewTextBox();
      this.newTextBox_Suji_EjectionTemp = new NewTextBox();
      this.newTextBox_Suji_Image2 = new NewTextBox();
      this.newTextBox_Suji_UDB = new NewTextBox();
      this.newTextBox_Suji_Image1 = new NewTextBox();
      this.newTextBox_Suji_FillerData = new NewTextBox();
      this.newTextBox_Suji_MoldTemp1 = new NewTextBox();
      this.newComboBox_Suji_Manufacturer = new NewComboBox();
      this.newTextBox_Suji_MeltTemp1 = new NewTextBox();
      this.newTextBox_Suji_MaterialID = new NewTextBox();
      this.newTextBox_Suji_Search = new NewTextBox();
      this.newComboBox_Suji_TradeName = new NewComboBox();
      this.tabPage3 = new TabPage();
      this.panel7 = new Panel();
      this.dataGridView_Proc_FC2 = new DataGridView();
      this.dataGridViewTextBoxColumn1 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn2 = new DataGridViewTextBoxColumn();
      this.panel9 = new Panel();
      this.dataGridView_Proc_FC4 = new DataGridView();
      this.dataGridViewTextBoxColumn5 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn6 = new DataGridViewTextBoxColumn();
      this.panel10 = new Panel();
      this.dataGridView_Proc_VP = new DataGridView();
      this.dataGridViewTextBoxColumn7 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn8 = new DataGridViewTextBoxColumn();
      this.panel8 = new Panel();
      this.dataGridView_Proc_FC3 = new DataGridView();
      this.dataGridViewTextBoxColumn3 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn4 = new DataGridViewTextBoxColumn();
      this.panel13 = new Panel();
      this.dataGridView_Proc_PHC2 = new DataGridView();
      this.dataGridViewTextBoxColumn13 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn14 = new DataGridViewTextBoxColumn();
      this.panel12 = new Panel();
      this.dataGridView_Proc_PHC1 = new DataGridView();
      this.dataGridViewTextBoxColumn11 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn12 = new DataGridViewTextBoxColumn();
      this.panel6 = new Panel();
      this.dataGridView_Proc_FC1 = new DataGridView();
      this.Column1 = new DataGridViewTextBoxColumn();
      this.Column2 = new DataGridViewTextBoxColumn();
      this.label38 = new Label();
      this.label37 = new Label();
      this.label35 = new Label();
      this.label_Proc_FC2 = new Label();
      this.label_Proc_FC4 = new Label();
      this.label_Proc_PHC2 = new Label();
      this.label_Proc_PHC1 = new Label();
      this.label_Proc_FC3 = new Label();
      this.label_Proc_FC1 = new Label();
      this.newComboBox_Proc_PHC = new NewComboBox();
      this.newComboBox_Proc_FC = new NewComboBox();
      this.newComboBox_Proc_CoolType = new NewComboBox();
      this.newComboBox_Proc_VP = new NewComboBox();
      this.newTextBox_Proc_CoolType = new NewTextBox();
      this.newTextBox_Proc_MoldTemp1 = new NewTextBox();
      this.newTextBox_Proc_MeltTemp1 = new NewTextBox();
      this.newTextBox24 = new NewTextBox();
      this.newTextBox21 = new NewTextBox();
      this.newTextBox19 = new NewTextBox();
      this.newTextBox_Proc_MoldTemp2 = new NewTextBox();
      this.newTextBox_Proc_CoolingTime = new NewTextBox();
      this.newTextBox_Proc_MeltTemp2 = new NewTextBox();
      this.panel4 = new Panel();
      this.newButton_Proc = new NewButton();
      this.newButton_Suji = new NewButton();
      this.newButton_Inj = new NewButton();
      this.panel5 = new Panel();
      this.newButton_Apply = new NewButton();
      this.newComboBox_Item = new NewComboBox();
      this.newComboBox_Company = new NewComboBox();
      this.newButton_BeforeDB = new NewButton();
      this.panel_Tab = new Panel();
      this.panel_DB = new Panel();
      this.panel1.SuspendLayout();
      ((ISupportInitialize) this.pictureBox_Suji_Image1).BeginInit();
      ((ISupportInitialize) this.pictureBox_Suji_Image2).BeginInit();
      this.panel3.SuspendLayout();
      this.tabControl_Main.SuspendLayout();
      this.tabPage1.SuspendLayout();
      this.tabPage2.SuspendLayout();
      this.tabPage3.SuspendLayout();
      this.panel7.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_Proc_FC2).BeginInit();
      this.panel9.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_Proc_FC4).BeginInit();
      this.panel10.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_Proc_VP).BeginInit();
      this.panel8.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_Proc_FC3).BeginInit();
      this.panel13.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_Proc_PHC2).BeginInit();
      this.panel12.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_Proc_PHC1).BeginInit();
      this.panel6.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_Proc_FC1).BeginInit();
      this.panel4.SuspendLayout();
      this.panel5.SuspendLayout();
      this.panel_Tab.SuspendLayout();
      this.panel_DB.SuspendLayout();
      this.SuspendLayout();
      this.label_Suji_Material.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Suji_Material.BorderStyle = BorderStyle.FixedSingle;
      this.label_Suji_Material.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Suji_Material.ForeColor = Color.MidnightBlue;
      this.label_Suji_Material.Location = new Point(5, 5);
      this.label_Suji_Material.Name = "label_Suji_Material";
      this.label_Suji_Material.Size = new Size(482, 20);
      this.label_Suji_Material.TabIndex = 17;
      this.label_Suji_Material.Text = "재질 정보";
      this.label_Suji_Material.TextAlign = ContentAlignment.MiddleLeft;
      this.listBox_Inj.Dock = DockStyle.Fill;
      this.listBox_Inj.FormattingEnabled = true;
      this.listBox_Inj.ItemHeight = 15;
      this.listBox_Inj.Location = new Point(0, 0);
      this.listBox_Inj.Name = "listBox_Inj";
      this.listBox_Inj.Size = new Size(226, 185);
      this.listBox_Inj.TabIndex = 10;
      this.listBox_Inj.TabStop = false;
      this.listBox_Inj.SelectedIndexChanged += new EventHandler(this.listBox_Inj_SelectedIndexChanged);
      this.label_Inj_Name.BackColor = Color.Lavender;
      this.label_Inj_Name.BorderStyle = BorderStyle.FixedSingle;
      this.label_Inj_Name.Location = new Point(235, 24);
      this.label_Inj_Name.Name = "label_Inj_Name";
      this.label_Inj_Name.Size = new Size(126, 22);
      this.label_Inj_Name.TabIndex = 20;
      this.label_Inj_Name.Text = "사출기 이름";
      this.label_Inj_Name.TextAlign = ContentAlignment.MiddleLeft;
      this.label_Inj_Company.BackColor = Color.Lavender;
      this.label_Inj_Company.BorderStyle = BorderStyle.FixedSingle;
      this.label_Inj_Company.Location = new Point(235, 45);
      this.label_Inj_Company.Name = "label_Inj_Company";
      this.label_Inj_Company.Size = new Size(126, 22);
      this.label_Inj_Company.TabIndex = 20;
      this.label_Inj_Company.Text = "사출기 회사";
      this.label_Inj_Company.TextAlign = ContentAlignment.MiddleLeft;
      this.label4.BackColor = Color.Lavender;
      this.label4.BorderStyle = BorderStyle.FixedSingle;
      this.label4.Location = new Point(235, 66);
      this.label4.Name = "label4";
      this.label4.Size = new Size(126, 22);
      this.label4.TabIndex = 20;
      this.label4.Text = "Data Source";
      this.label4.TextAlign = ContentAlignment.MiddleLeft;
      this.label5.BackColor = Color.Lavender;
      this.label5.BorderStyle = BorderStyle.FixedSingle;
      this.label5.Location = new Point(235, 87);
      this.label5.Name = "label5";
      this.label5.Size = new Size(126, 22);
      this.label5.TabIndex = 20;
      this.label5.Text = "Data last modified";
      this.label5.TextAlign = ContentAlignment.MiddleLeft;
      this.label_Inj_MaxStroke.BackColor = Color.Lavender;
      this.label_Inj_MaxStroke.BorderStyle = BorderStyle.FixedSingle;
      this.label_Inj_MaxStroke.Location = new Point(235, 108);
      this.label_Inj_MaxStroke.Name = "label_Inj_MaxStroke";
      this.label_Inj_MaxStroke.Size = new Size(126, 22);
      this.label_Inj_MaxStroke.TabIndex = 20;
      this.label_Inj_MaxStroke.Text = "최대 사출 스트로크";
      this.label_Inj_MaxStroke.TextAlign = ContentAlignment.MiddleLeft;
      this.label_Inj_MaxRate.BackColor = Color.Lavender;
      this.label_Inj_MaxRate.BorderStyle = BorderStyle.FixedSingle;
      this.label_Inj_MaxRate.Location = new Point(235, 129);
      this.label_Inj_MaxRate.Name = "label_Inj_MaxRate";
      this.label_Inj_MaxRate.Size = new Size(126, 22);
      this.label_Inj_MaxRate.TabIndex = 20;
      this.label_Inj_MaxRate.Text = "최대 사출률";
      this.label_Inj_MaxRate.TextAlign = ContentAlignment.MiddleLeft;
      this.label_Inj_ScrewDia.BackColor = Color.Lavender;
      this.label_Inj_ScrewDia.BorderStyle = BorderStyle.FixedSingle;
      this.label_Inj_ScrewDia.Location = new Point(235, 150);
      this.label_Inj_ScrewDia.Name = "label_Inj_ScrewDia";
      this.label_Inj_ScrewDia.Size = new Size(126, 22);
      this.label_Inj_ScrewDia.TabIndex = 20;
      this.label_Inj_ScrewDia.Text = "스크류 직경";
      this.label_Inj_ScrewDia.TextAlign = ContentAlignment.MiddleLeft;
      this.label_Inj_MaxPressure.BackColor = Color.Lavender;
      this.label_Inj_MaxPressure.BorderStyle = BorderStyle.FixedSingle;
      this.label_Inj_MaxPressure.Location = new Point(235, 171);
      this.label_Inj_MaxPressure.Name = "label_Inj_MaxPressure";
      this.label_Inj_MaxPressure.Size = new Size(126, 22);
      this.label_Inj_MaxPressure.TabIndex = 20;
      this.label_Inj_MaxPressure.Text = "사출기 최대 사출 압력";
      this.label_Inj_MaxPressure.TextAlign = ContentAlignment.MiddleLeft;
      this.label_Inj_MaxClamp.BackColor = Color.Lavender;
      this.label_Inj_MaxClamp.BorderStyle = BorderStyle.FixedSingle;
      this.label_Inj_MaxClamp.Location = new Point(235, 192);
      this.label_Inj_MaxClamp.Name = "label_Inj_MaxClamp";
      this.label_Inj_MaxClamp.Size = new Size(126, 22);
      this.label_Inj_MaxClamp.TabIndex = 20;
      this.label_Inj_MaxClamp.Text = "사출기 최대 형체력";
      this.label_Inj_MaxClamp.TextAlign = ContentAlignment.MiddleLeft;
      this.label_Inj_PreRatio.BackColor = Color.Lavender;
      this.label_Inj_PreRatio.BorderStyle = BorderStyle.FixedSingle;
      this.label_Inj_PreRatio.Location = new Point(235, 213);
      this.label_Inj_PreRatio.Name = "label_Inj_PreRatio";
      this.label_Inj_PreRatio.Size = new Size(126, 22);
      this.label_Inj_PreRatio.TabIndex = 20;
      this.label_Inj_PreRatio.Text = "증압비";
      this.label_Inj_PreRatio.TextAlign = ContentAlignment.MiddleLeft;
      this.panel1.BorderStyle = BorderStyle.FixedSingle;
      this.panel1.Controls.Add((Control) this.newButton_Suji_UserUDB);
      this.panel1.Controls.Add((Control) this.radioButton_Suji_User);
      this.panel1.Controls.Add((Control) this.radioButton_Suji_System);
      this.panel1.Location = new Point(5, 24);
      this.panel1.Name = "panel1";
      this.panel1.Size = new Size(482, 30);
      this.panel1.TabIndex = 22;
      this.newButton_Suji_UserUDB.ButtonBackColor = Color.White;
      this.newButton_Suji_UserUDB.ButtonText = "사용자 UDB 삽입";
      this.newButton_Suji_UserUDB.FlatBorderSize = 1;
      this.newButton_Suji_UserUDB.FlatStyle = FlatStyle.Flat;
      this.newButton_Suji_UserUDB.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Suji_UserUDB.Image = (Image) null;
      this.newButton_Suji_UserUDB.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Suji_UserUDB.Location = new Point(135, 2);
      this.newButton_Suji_UserUDB.Name = "newButton_Suji_UserUDB";
      this.newButton_Suji_UserUDB.Size = new Size(110, 24);
      this.newButton_Suji_UserUDB.TabIndex = 25;
      this.newButton_Suji_UserUDB.TabStop = false;
      this.newButton_Suji_UserUDB.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Suji_UserUDB.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Suji_UserUDB.NewClick += new EventHandler(this.newButton_NewClick);
      this.radioButton_Suji_User.Location = new Point(73, 6);
      this.radioButton_Suji_User.Name = "radioButton_Suji_User";
      this.radioButton_Suji_User.Size = new Size(61, 18);
      this.radioButton_Suji_User.TabIndex = 24;
      this.radioButton_Suji_User.Text = "사용자";
      this.radioButton_Suji_User.UseVisualStyleBackColor = true;
      this.radioButton_Suji_User.CheckedChanged += new EventHandler(this.radioButton_Suji_User_CheckedChanged);
      this.radioButton_Suji_System.Location = new Point(6, 6);
      this.radioButton_Suji_System.Name = "radioButton_Suji_System";
      this.radioButton_Suji_System.Size = new Size(61, 18);
      this.radioButton_Suji_System.TabIndex = 23;
      this.radioButton_Suji_System.Text = "시스템";
      this.radioButton_Suji_System.UseVisualStyleBackColor = true;
      this.radioButton_Suji_System.CheckedChanged += new EventHandler(this.radioButton_Suji_System_CheckedChanged);
      this.label12.BackColor = Color.Lavender;
      this.label12.BorderStyle = BorderStyle.FixedSingle;
      this.label12.Location = new Point(5, 72);
      this.label12.Name = "label12";
      this.label12.Size = new Size(100, 23);
      this.label12.TabIndex = 20;
      this.label12.Text = "Trade Name";
      this.label12.TextAlign = ContentAlignment.MiddleCenter;
      this.label13.BackColor = Color.Lavender;
      this.label13.BorderStyle = BorderStyle.FixedSingle;
      this.label13.Location = new Point(5, 113);
      this.label13.Name = "label13";
      this.label13.Size = new Size(242, 23);
      this.label13.TabIndex = 20;
      this.label13.Text = "Manufacturer";
      this.label13.TextAlign = ContentAlignment.MiddleCenter;
      this.label14.BackColor = Color.Lavender;
      this.label14.BorderStyle = BorderStyle.FixedSingle;
      this.label14.Location = new Point(246, 113);
      this.label14.Name = "label14";
      this.label14.Size = new Size(241, 23);
      this.label14.TabIndex = 20;
      this.label14.Text = "Trade Name";
      this.label14.TextAlign = ContentAlignment.MiddleCenter;
      this.label15.BackColor = Color.Lavender;
      this.label15.BorderStyle = BorderStyle.FixedSingle;
      this.label15.Location = new Point(5, 157);
      this.label15.Name = "label15";
      this.label15.Size = new Size(161, 23);
      this.label15.TabIndex = 20;
      this.label15.Text = "Family abbreviation";
      this.label15.TextAlign = ContentAlignment.MiddleCenter;
      this.label16.BackColor = Color.Lavender;
      this.label16.BorderStyle = BorderStyle.FixedSingle;
      this.label16.Location = new Point(165, 157);
      this.label16.Name = "label16";
      this.label16.Size = new Size(162, 23);
      this.label16.TabIndex = 20;
      this.label16.Text = "Filler data";
      this.label16.TextAlign = ContentAlignment.MiddleCenter;
      this.label17.BackColor = Color.Lavender;
      this.label17.BorderStyle = BorderStyle.FixedSingle;
      this.label17.Location = new Point(326, 157);
      this.label17.Name = "label17";
      this.label17.Size = new Size(161, 23);
      this.label17.TabIndex = 20;
      this.label17.Text = "Material ID";
      this.label17.TextAlign = ContentAlignment.MiddleCenter;
      this.pictureBox_Suji_Image1.BorderStyle = BorderStyle.FixedSingle;
      this.pictureBox_Suji_Image1.Location = new Point(5, 292);
      this.pictureBox_Suji_Image1.Name = "pictureBox_Suji_Image1";
      this.pictureBox_Suji_Image1.Size = new Size(242, 135);
      this.pictureBox_Suji_Image1.SizeMode = PictureBoxSizeMode.StretchImage;
      this.pictureBox_Suji_Image1.TabIndex = 25;
      this.pictureBox_Suji_Image1.TabStop = false;
      this.pictureBox_Suji_Image2.BorderStyle = BorderStyle.FixedSingle;
      this.pictureBox_Suji_Image2.Location = new Point(246, 292);
      this.pictureBox_Suji_Image2.Name = "pictureBox_Suji_Image2";
      this.pictureBox_Suji_Image2.Size = new Size(241, 135);
      this.pictureBox_Suji_Image2.SizeMode = PictureBoxSizeMode.StretchImage;
      this.pictureBox_Suji_Image2.TabIndex = 25;
      this.pictureBox_Suji_Image2.TabStop = false;
      this.label18.BackColor = Color.Lavender;
      this.label18.BorderStyle = BorderStyle.FixedSingle;
      this.label18.Location = new Point(5, 426);
      this.label18.Name = "label18";
      this.label18.Size = new Size(161, 23);
      this.label18.TabIndex = 20;
      this.label18.Text = "Ejection temp";
      this.label18.TextAlign = ContentAlignment.MiddleCenter;
      this.label19.BackColor = Color.Lavender;
      this.label19.BorderStyle = BorderStyle.FixedSingle;
      this.label19.Location = new Point(165, 426);
      this.label19.Name = "label19";
      this.label19.Size = new Size(162, 23);
      this.label19.TabIndex = 20;
      this.label19.Text = "Transition temp";
      this.label19.TextAlign = ContentAlignment.MiddleCenter;
      this.label20.BackColor = Color.Lavender;
      this.label20.BorderStyle = BorderStyle.FixedSingle;
      this.label20.Location = new Point(326, 426);
      this.label20.Name = "label20";
      this.label20.Size = new Size(161, 23);
      this.label20.TabIndex = 20;
      this.label20.Text = "MFR";
      this.label20.TextAlign = ContentAlignment.MiddleCenter;
      this.label21.BackColor = Color.Lavender;
      this.label21.BorderStyle = BorderStyle.FixedSingle;
      this.label21.Location = new Point(5, 470);
      this.label21.Name = "label21";
      this.label21.Size = new Size(132, 23);
      this.label21.TabIndex = 20;
      this.label21.Text = "Melt Temperature";
      this.label21.TextAlign = ContentAlignment.MiddleCenter;
      this.label22.BackColor = Color.Lavender;
      this.label22.BorderStyle = BorderStyle.FixedSingle;
      this.label22.Location = new Point(5, 492);
      this.label22.Name = "label22";
      this.label22.Size = new Size(132, 23);
      this.label22.TabIndex = 20;
      this.label22.Text = "Mold Temperature";
      this.label22.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Proc_CoolingTime.BackColor = Color.Lavender;
      this.label_Proc_CoolingTime.BorderStyle = BorderStyle.FixedSingle;
      this.label_Proc_CoolingTime.Location = new Point(5, 46);
      this.label_Proc_CoolingTime.Name = "label_Proc_CoolingTime";
      this.label_Proc_CoolingTime.Size = new Size(220, 23);
      this.label_Proc_CoolingTime.TabIndex = 20;
      this.label_Proc_CoolingTime.TextAlign = ContentAlignment.MiddleCenter;
      this.label24.BackColor = Color.Lavender;
      this.label24.BorderStyle = BorderStyle.FixedSingle;
      this.label24.Location = new Point(5, 68);
      this.label24.Name = "label24";
      this.label24.Size = new Size(220, 23);
      this.label24.TabIndex = 20;
      this.label24.Text = "Melt Temperature";
      this.label24.TextAlign = ContentAlignment.MiddleCenter;
      this.label25.BackColor = Color.Lavender;
      this.label25.BorderStyle = BorderStyle.FixedSingle;
      this.label25.Location = new Point(5, 90);
      this.label25.Name = "label25";
      this.label25.Size = new Size(220, 23);
      this.label25.TabIndex = 20;
      this.label25.Text = "Mold Temperature";
      this.label25.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Inj_Data.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Inj_Data.BorderStyle = BorderStyle.FixedSingle;
      this.label_Inj_Data.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Inj_Data.ForeColor = Color.MidnightBlue;
      this.label_Inj_Data.Location = new Point(235, 5);
      this.label_Inj_Data.Name = "label_Inj_Data";
      this.label_Inj_Data.Size = new Size(251, 20);
      this.label_Inj_Data.TabIndex = 17;
      this.label_Inj_Data.Text = "사출기 데이터";
      this.label_Inj_Data.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Inj_List.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Inj_List.BorderStyle = BorderStyle.FixedSingle;
      this.label_Inj_List.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Inj_List.ForeColor = Color.MidnightBlue;
      this.label_Inj_List.Location = new Point(5, 5);
      this.label_Inj_List.Name = "label_Inj_List";
      this.label_Inj_List.Size = new Size(226, 20);
      this.label_Inj_List.TabIndex = 17;
      this.label_Inj_List.Text = "사출기 리스트";
      this.label_Inj_List.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Suji_SearchCond.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Suji_SearchCond.BorderStyle = BorderStyle.FixedSingle;
      this.label_Suji_SearchCond.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Suji_SearchCond.ForeColor = Color.MidnightBlue;
      this.label_Suji_SearchCond.Location = new Point(5, 53);
      this.label_Suji_SearchCond.Name = "label_Suji_SearchCond";
      this.label_Suji_SearchCond.Size = new Size(482, 20);
      this.label_Suji_SearchCond.TabIndex = 17;
      this.label_Suji_SearchCond.Text = "검색 조건";
      this.label_Suji_SearchCond.TextAlign = ContentAlignment.MiddleLeft;
      this.label_Suji_SearchValue.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Suji_SearchValue.BorderStyle = BorderStyle.FixedSingle;
      this.label_Suji_SearchValue.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Suji_SearchValue.ForeColor = Color.MidnightBlue;
      this.label_Suji_SearchValue.Location = new Point(5, 94);
      this.label_Suji_SearchValue.Name = "label_Suji_SearchValue";
      this.label_Suji_SearchValue.Size = new Size(482, 20);
      this.label_Suji_SearchValue.TabIndex = 17;
      this.label_Suji_SearchValue.Text = "검색 값";
      this.label_Suji_SearchValue.TextAlign = ContentAlignment.MiddleLeft;
      this.label_Suji_ResultValue.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Suji_ResultValue.BorderStyle = BorderStyle.FixedSingle;
      this.label_Suji_ResultValue.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Suji_ResultValue.ForeColor = Color.MidnightBlue;
      this.label_Suji_ResultValue.Location = new Point(5, 229);
      this.label_Suji_ResultValue.Name = "label_Suji_ResultValue";
      this.label_Suji_ResultValue.Size = new Size(482, 20);
      this.label_Suji_ResultValue.TabIndex = 17;
      this.label_Suji_ResultValue.Text = "결과 값";
      this.label_Suji_ResultValue.TextAlign = ContentAlignment.MiddleLeft;
      this.label_DB_Item.BackColor = Color.Lavender;
      this.label_DB_Item.BorderStyle = BorderStyle.FixedSingle;
      this.label_DB_Item.Location = new Point(249, 19);
      this.label_DB_Item.Name = "label_DB_Item";
      this.label_DB_Item.Size = new Size(70, 23);
      this.label_DB_Item.TabIndex = 94;
      this.label_DB_Item.Text = "아이템명";
      this.label_DB_Item.TextAlign = ContentAlignment.MiddleCenter;
      this.label_DB_Company.BackColor = Color.Lavender;
      this.label_DB_Company.BorderStyle = BorderStyle.FixedSingle;
      this.label_DB_Company.Location = new Point(0, 19);
      this.label_DB_Company.Name = "label_DB_Company";
      this.label_DB_Company.Size = new Size(70, 23);
      this.label_DB_Company.TabIndex = 95;
      this.label_DB_Company.Text = "회사명";
      this.label_DB_Company.TextAlign = ContentAlignment.MiddleCenter;
      this.listBox_DB.BackColor = Color.LavenderBlush;
      this.listBox_DB.FormattingEnabled = true;
      this.listBox_DB.ItemHeight = 15;
      this.listBox_DB.Location = new Point(0, 41);
      this.listBox_DB.Name = "listBox_DB";
      this.listBox_DB.SelectionMode = SelectionMode.MultiExtended;
      this.listBox_DB.Size = new Size(498, 154);
      this.listBox_DB.TabIndex = 3;
      this.listBox_DB.SelectedIndexChanged += new EventHandler(this.listBox_DB_SelectedIndexChanged);
      this.label_DB.BackColor = Color.FromArgb(229, 238, 248);
      this.label_DB.BorderStyle = BorderStyle.FixedSingle;
      this.label_DB.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_DB.ForeColor = Color.MidnightBlue;
      this.label_DB.Location = new Point(0, 0);
      this.label_DB.Name = "label_DB";
      this.label_DB.Size = new Size(498, 20);
      this.label_DB.TabIndex = 92;
      this.label_DB.Text = "DB 리스트";
      this.label_DB.TextAlign = ContentAlignment.MiddleCenter;
      this.label32.BackColor = Color.FromArgb(229, 238, 248);
      this.label32.BorderStyle = BorderStyle.FixedSingle;
      this.label32.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label32.ForeColor = Color.MidnightBlue;
      this.label32.Location = new Point(5, 5);
      this.label32.Name = "label32";
      this.label32.Size = new Size(482, 20);
      this.label32.TabIndex = 92;
      this.label32.Text = "Default";
      this.label32.TextAlign = ContentAlignment.MiddleCenter;
      this.panel3.Controls.Add((Control) this.listBox_Inj);
      this.panel3.Location = new Point(5, 24);
      this.panel3.Name = "panel3";
      this.panel3.Size = new Size(226, 185);
      this.panel3.TabIndex = 23;
      this.tabControl_Main.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.tabControl_Main.Controls.Add((Control) this.tabPage1);
      this.tabControl_Main.Controls.Add((Control) this.tabPage2);
      this.tabControl_Main.Controls.Add((Control) this.tabPage3);
      this.tabControl_Main.Location = new Point(-3, -23);
      this.tabControl_Main.Name = "tabControl_Main";
      this.tabControl_Main.SelectedIndex = 0;
      this.tabControl_Main.Size = new Size(500, 546);
      this.tabControl_Main.TabIndex = 9;
      this.tabControl_Main.SelectedIndexChanged += new EventHandler(this.tabControl_Main_SelectedIndexChanged);
      this.tabPage1.Controls.Add((Control) this.panel3);
      this.tabPage1.Controls.Add((Control) this.label_Inj_List);
      this.tabPage1.Controls.Add((Control) this.label_Inj_Data);
      this.tabPage1.Controls.Add((Control) this.label_Inj_ScrewDia);
      this.tabPage1.Controls.Add((Control) this.label_Inj_MaxRate);
      this.tabPage1.Controls.Add((Control) this.newButton_Inj_Edit);
      this.tabPage1.Controls.Add((Control) this.unitTextBox_Inj_MaxRate);
      this.tabPage1.Controls.Add((Control) this.newTextBox_Inj_DataLastModified);
      this.tabPage1.Controls.Add((Control) this.unitTextBox_Inj_ScrewDia);
      this.tabPage1.Controls.Add((Control) this.label_Inj_Company);
      this.tabPage1.Controls.Add((Control) this.label_Inj_MaxPressure);
      this.tabPage1.Controls.Add((Control) this.newTextBox_Inj_DataSource);
      this.tabPage1.Controls.Add((Control) this.unitTextBox_Inj_MaxPressure);
      this.tabPage1.Controls.Add((Control) this.unitTextBox_Inj_MaxStroke);
      this.tabPage1.Controls.Add((Control) this.label_Inj_MaxStroke);
      this.tabPage1.Controls.Add((Control) this.label_Inj_Name);
      this.tabPage1.Controls.Add((Control) this.newTextBox_Inj_PreRatio);
      this.tabPage1.Controls.Add((Control) this.newTextBox_Inj_Company);
      this.tabPage1.Controls.Add((Control) this.newTextBox_Inj_Name);
      this.tabPage1.Controls.Add((Control) this.label5);
      this.tabPage1.Controls.Add((Control) this.label_Inj_PreRatio);
      this.tabPage1.Controls.Add((Control) this.unitTextBox_Inj_MaxClampForce);
      this.tabPage1.Controls.Add((Control) this.label_Inj_MaxClamp);
      this.tabPage1.Controls.Add((Control) this.label4);
      this.tabPage1.Location = new Point(4, 24);
      this.tabPage1.Name = "tabPage1";
      this.tabPage1.Padding = new Padding(3);
      this.tabPage1.Size = new Size(492, 518);
      this.tabPage1.TabIndex = 0;
      this.tabPage1.Text = "tabPage1";
      this.tabPage1.UseVisualStyleBackColor = true;
      this.newButton_Inj_Edit.ButtonBackColor = Color.White;
      this.newButton_Inj_Edit.ButtonText = "수정";
      this.newButton_Inj_Edit.FlatBorderSize = 1;
      this.newButton_Inj_Edit.FlatStyle = FlatStyle.Flat;
      this.newButton_Inj_Edit.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Inj_Edit.Image = (Image) Resources.Edit;
      this.newButton_Inj_Edit.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Inj_Edit.Location = new Point(5, 208);
      this.newButton_Inj_Edit.Name = "newButton_Inj_Edit";
      this.newButton_Inj_Edit.Size = new Size(226, 27);
      this.newButton_Inj_Edit.TabIndex = 11;
      this.newButton_Inj_Edit.TabStop = false;
      this.newButton_Inj_Edit.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Inj_Edit.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Inj_Edit.NewClick += new EventHandler(this.newButton_NewClick);
      this.unitTextBox_Inj_MaxRate.BackColor = Color.White;
      this.unitTextBox_Inj_MaxRate.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Inj_MaxRate.ControlBackColor = Color.White;
      this.unitTextBox_Inj_MaxRate.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Inj_MaxRate.IsDigit = false;
      this.unitTextBox_Inj_MaxRate.Location = new Point(360, 129);
      this.unitTextBox_Inj_MaxRate.Name = "unitTextBox_Inj_MaxRate";
      this.unitTextBox_Inj_MaxRate.Size = new Size(126, 22);
      this.unitTextBox_Inj_MaxRate.TabIndex = 17;
      this.unitTextBox_Inj_MaxRate.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Inj_MaxRate.Unit = "cm\u00B3/s";
      this.unitTextBox_Inj_MaxRate.Value = "";
      this.newTextBox_Inj_DataLastModified.BackColor = SystemColors.Window;
      this.newTextBox_Inj_DataLastModified.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Inj_DataLastModified.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Inj_DataLastModified.IsDigit = false;
      this.newTextBox_Inj_DataLastModified.Lines = new string[0];
      this.newTextBox_Inj_DataLastModified.Location = new Point(360, 87);
      this.newTextBox_Inj_DataLastModified.MultiLine = false;
      this.newTextBox_Inj_DataLastModified.Name = "newTextBox_Inj_DataLastModified";
      this.newTextBox_Inj_DataLastModified.ReadOnly = false;
      this.newTextBox_Inj_DataLastModified.Size = new Size(126, 22);
      this.newTextBox_Inj_DataLastModified.TabIndex = 15;
      this.newTextBox_Inj_DataLastModified.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Inj_DataLastModified.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Inj_DataLastModified.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Inj_DataLastModified.Value = "";
      this.unitTextBox_Inj_ScrewDia.BackColor = Color.White;
      this.unitTextBox_Inj_ScrewDia.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Inj_ScrewDia.ControlBackColor = Color.White;
      this.unitTextBox_Inj_ScrewDia.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Inj_ScrewDia.IsDigit = false;
      this.unitTextBox_Inj_ScrewDia.Location = new Point(360, 150);
      this.unitTextBox_Inj_ScrewDia.Name = "unitTextBox_Inj_ScrewDia";
      this.unitTextBox_Inj_ScrewDia.Size = new Size(126, 22);
      this.unitTextBox_Inj_ScrewDia.TabIndex = 18;
      this.unitTextBox_Inj_ScrewDia.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Inj_ScrewDia.Unit = "mm";
      this.unitTextBox_Inj_ScrewDia.Value = "";
      this.newTextBox_Inj_DataSource.BackColor = SystemColors.Window;
      this.newTextBox_Inj_DataSource.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Inj_DataSource.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Inj_DataSource.IsDigit = false;
      this.newTextBox_Inj_DataSource.Lines = new string[0];
      this.newTextBox_Inj_DataSource.Location = new Point(360, 66);
      this.newTextBox_Inj_DataSource.MultiLine = false;
      this.newTextBox_Inj_DataSource.Name = "newTextBox_Inj_DataSource";
      this.newTextBox_Inj_DataSource.ReadOnly = false;
      this.newTextBox_Inj_DataSource.Size = new Size(126, 22);
      this.newTextBox_Inj_DataSource.TabIndex = 14;
      this.newTextBox_Inj_DataSource.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Inj_DataSource.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Inj_DataSource.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Inj_DataSource.Value = "";
      this.unitTextBox_Inj_MaxPressure.BackColor = Color.White;
      this.unitTextBox_Inj_MaxPressure.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Inj_MaxPressure.ControlBackColor = Color.White;
      this.unitTextBox_Inj_MaxPressure.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Inj_MaxPressure.IsDigit = false;
      this.unitTextBox_Inj_MaxPressure.Location = new Point(360, 171);
      this.unitTextBox_Inj_MaxPressure.Name = "unitTextBox_Inj_MaxPressure";
      this.unitTextBox_Inj_MaxPressure.Size = new Size(126, 22);
      this.unitTextBox_Inj_MaxPressure.TabIndex = 19;
      this.unitTextBox_Inj_MaxPressure.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Inj_MaxPressure.Unit = "MPa";
      this.unitTextBox_Inj_MaxPressure.Value = "";
      this.unitTextBox_Inj_MaxStroke.BackColor = Color.White;
      this.unitTextBox_Inj_MaxStroke.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Inj_MaxStroke.ControlBackColor = Color.White;
      this.unitTextBox_Inj_MaxStroke.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Inj_MaxStroke.IsDigit = false;
      this.unitTextBox_Inj_MaxStroke.Location = new Point(360, 108);
      this.unitTextBox_Inj_MaxStroke.Name = "unitTextBox_Inj_MaxStroke";
      this.unitTextBox_Inj_MaxStroke.Size = new Size(126, 22);
      this.unitTextBox_Inj_MaxStroke.TabIndex = 16;
      this.unitTextBox_Inj_MaxStroke.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Inj_MaxStroke.Unit = "mm";
      this.unitTextBox_Inj_MaxStroke.Value = "";
      this.newTextBox_Inj_PreRatio.BackColor = SystemColors.Window;
      this.newTextBox_Inj_PreRatio.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Inj_PreRatio.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Inj_PreRatio.IsDigit = false;
      this.newTextBox_Inj_PreRatio.Lines = new string[0];
      this.newTextBox_Inj_PreRatio.Location = new Point(360, 213);
      this.newTextBox_Inj_PreRatio.MultiLine = false;
      this.newTextBox_Inj_PreRatio.Name = "newTextBox_Inj_PreRatio";
      this.newTextBox_Inj_PreRatio.ReadOnly = false;
      this.newTextBox_Inj_PreRatio.Size = new Size(126, 22);
      this.newTextBox_Inj_PreRatio.TabIndex = 21;
      this.newTextBox_Inj_PreRatio.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Inj_PreRatio.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Inj_PreRatio.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Inj_PreRatio.Value = "";
      this.newTextBox_Inj_Company.BackColor = SystemColors.Window;
      this.newTextBox_Inj_Company.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Inj_Company.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Inj_Company.IsDigit = false;
      this.newTextBox_Inj_Company.Lines = new string[0];
      this.newTextBox_Inj_Company.Location = new Point(360, 45);
      this.newTextBox_Inj_Company.MultiLine = false;
      this.newTextBox_Inj_Company.Name = "newTextBox_Inj_Company";
      this.newTextBox_Inj_Company.ReadOnly = false;
      this.newTextBox_Inj_Company.Size = new Size(126, 22);
      this.newTextBox_Inj_Company.TabIndex = 13;
      this.newTextBox_Inj_Company.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Inj_Company.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Inj_Company.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Inj_Company.Value = "";
      this.newTextBox_Inj_Name.BackColor = SystemColors.Window;
      this.newTextBox_Inj_Name.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Inj_Name.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Inj_Name.IsDigit = false;
      this.newTextBox_Inj_Name.Lines = new string[0];
      this.newTextBox_Inj_Name.Location = new Point(360, 24);
      this.newTextBox_Inj_Name.MultiLine = false;
      this.newTextBox_Inj_Name.Name = "newTextBox_Inj_Name";
      this.newTextBox_Inj_Name.ReadOnly = false;
      this.newTextBox_Inj_Name.Size = new Size(126, 22);
      this.newTextBox_Inj_Name.TabIndex = 12;
      this.newTextBox_Inj_Name.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Inj_Name.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Inj_Name.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Inj_Name.Value = "";
      this.unitTextBox_Inj_MaxClampForce.BackColor = Color.White;
      this.unitTextBox_Inj_MaxClampForce.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Inj_MaxClampForce.ControlBackColor = Color.White;
      this.unitTextBox_Inj_MaxClampForce.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Inj_MaxClampForce.IsDigit = false;
      this.unitTextBox_Inj_MaxClampForce.Location = new Point(360, 192);
      this.unitTextBox_Inj_MaxClampForce.Name = "unitTextBox_Inj_MaxClampForce";
      this.unitTextBox_Inj_MaxClampForce.Size = new Size(126, 22);
      this.unitTextBox_Inj_MaxClampForce.TabIndex = 20;
      this.unitTextBox_Inj_MaxClampForce.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Inj_MaxClampForce.Unit = "tone";
      this.unitTextBox_Inj_MaxClampForce.Value = "";
      this.tabPage2.Controls.Add((Control) this.label_Suji_ResultValue);
      this.tabPage2.Controls.Add((Control) this.pictureBox_Suji_Image2);
      this.tabPage2.Controls.Add((Control) this.panel1);
      this.tabPage2.Controls.Add((Control) this.label_Suji_Material);
      this.tabPage2.Controls.Add((Control) this.pictureBox_Suji_Image1);
      this.tabPage2.Controls.Add((Control) this.label_Suji_SearchValue);
      this.tabPage2.Controls.Add((Control) this.label17);
      this.tabPage2.Controls.Add((Control) this.label_Suji_SearchCond);
      this.tabPage2.Controls.Add((Control) this.label15);
      this.tabPage2.Controls.Add((Control) this.label16);
      this.tabPage2.Controls.Add((Control) this.label14);
      this.tabPage2.Controls.Add((Control) this.label22);
      this.tabPage2.Controls.Add((Control) this.label13);
      this.tabPage2.Controls.Add((Control) this.label21);
      this.tabPage2.Controls.Add((Control) this.label18);
      this.tabPage2.Controls.Add((Control) this.label23);
      this.tabPage2.Controls.Add((Control) this.label12);
      this.tabPage2.Controls.Add((Control) this.label19);
      this.tabPage2.Controls.Add((Control) this.label20);
      this.tabPage2.Controls.Add((Control) this.newTextBox_Suji_MFR);
      this.tabPage2.Controls.Add((Control) this.newButton_Suji_Select);
      this.tabPage2.Controls.Add((Control) this.newTextBox_Suji_TransitionTemp);
      this.tabPage2.Controls.Add((Control) this.newTextBox33);
      this.tabPage2.Controls.Add((Control) this.newTextBox_Suji_FamilyAbbreviation);
      this.tabPage2.Controls.Add((Control) this.newTextBox20);
      this.tabPage2.Controls.Add((Control) this.newTextBox_Suji_MoldTemp2);
      this.tabPage2.Controls.Add((Control) this.newTextBox_Suji_MeltTemp2);
      this.tabPage2.Controls.Add((Control) this.newTextBox_Suji_EjectionTemp);
      this.tabPage2.Controls.Add((Control) this.newTextBox_Suji_Image2);
      this.tabPage2.Controls.Add((Control) this.newTextBox_Suji_UDB);
      this.tabPage2.Controls.Add((Control) this.newTextBox_Suji_Image1);
      this.tabPage2.Controls.Add((Control) this.newTextBox_Suji_FillerData);
      this.tabPage2.Controls.Add((Control) this.newTextBox_Suji_MoldTemp1);
      this.tabPage2.Controls.Add((Control) this.newComboBox_Suji_Manufacturer);
      this.tabPage2.Controls.Add((Control) this.newTextBox_Suji_MeltTemp1);
      this.tabPage2.Controls.Add((Control) this.newTextBox_Suji_MaterialID);
      this.tabPage2.Controls.Add((Control) this.newTextBox_Suji_Search);
      this.tabPage2.Controls.Add((Control) this.newComboBox_Suji_TradeName);
      this.tabPage2.Location = new Point(4, 22);
      this.tabPage2.Name = "tabPage2";
      this.tabPage2.Padding = new Padding(3);
      this.tabPage2.Size = new Size(492, 520);
      this.tabPage2.TabIndex = 1;
      this.tabPage2.Text = "tabPage2";
      this.tabPage2.UseVisualStyleBackColor = true;
      this.label23.BackColor = Color.Lavender;
      this.label23.BorderStyle = BorderStyle.FixedSingle;
      this.label23.Location = new Point(5, 248);
      this.label23.Name = "label23";
      this.label23.Size = new Size(100, 23);
      this.label23.TabIndex = 20;
      this.label23.Text = "UDB File";
      this.label23.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Suji_MFR.BackColor = Color.LavenderBlush;
      this.newTextBox_Suji_MFR.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Suji_MFR.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Suji_MFR.IsDigit = false;
      this.newTextBox_Suji_MFR.Lines = new string[0];
      this.newTextBox_Suji_MFR.Location = new Point(326, 448);
      this.newTextBox_Suji_MFR.MultiLine = false;
      this.newTextBox_Suji_MFR.Name = "newTextBox_Suji_MFR";
      this.newTextBox_Suji_MFR.ReadOnly = true;
      this.newTextBox_Suji_MFR.Size = new Size(161, 23);
      this.newTextBox_Suji_MFR.TabIndex = 38;
      this.newTextBox_Suji_MFR.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Suji_MFR.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_Suji_MFR.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Suji_MFR.Value = "";
      this.newButton_Suji_Select.ButtonBackColor = Color.White;
      this.newButton_Suji_Select.ButtonText = "수지 선택";
      this.newButton_Suji_Select.FlatBorderSize = 1;
      this.newButton_Suji_Select.FlatStyle = FlatStyle.Flat;
      this.newButton_Suji_Select.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Suji_Select.Image = (Image) null;
      this.newButton_Suji_Select.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Suji_Select.Location = new Point(5, 201);
      this.newButton_Suji_Select.Name = "newButton_Suji_Select";
      this.newButton_Suji_Select.Size = new Size(482, 24);
      this.newButton_Suji_Select.TabIndex = 32;
      this.newButton_Suji_Select.TabStop = false;
      this.newButton_Suji_Select.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Suji_Select.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Suji_Select.NewClick += new EventHandler(this.newButton_NewClick);
      this.newTextBox_Suji_TransitionTemp.BackColor = Color.LavenderBlush;
      this.newTextBox_Suji_TransitionTemp.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Suji_TransitionTemp.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Suji_TransitionTemp.IsDigit = false;
      this.newTextBox_Suji_TransitionTemp.Lines = new string[0];
      this.newTextBox_Suji_TransitionTemp.Location = new Point(165, 448);
      this.newTextBox_Suji_TransitionTemp.MultiLine = false;
      this.newTextBox_Suji_TransitionTemp.Name = "newTextBox_Suji_TransitionTemp";
      this.newTextBox_Suji_TransitionTemp.ReadOnly = true;
      this.newTextBox_Suji_TransitionTemp.Size = new Size(162, 23);
      this.newTextBox_Suji_TransitionTemp.TabIndex = 37;
      this.newTextBox_Suji_TransitionTemp.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Suji_TransitionTemp.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_Suji_TransitionTemp.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Suji_TransitionTemp.Value = "";
      this.newTextBox33.BackColor = Color.LavenderBlush;
      this.newTextBox33.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox33.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox33.IsDigit = false;
      this.newTextBox33.Lines = new string[1]{ "℃" };
      this.newTextBox33.Location = new Point(433, 492);
      this.newTextBox33.MultiLine = false;
      this.newTextBox33.Name = "newTextBox33";
      this.newTextBox33.ReadOnly = true;
      this.newTextBox33.Size = new Size(54, 23);
      this.newTextBox33.TabIndex = 21;
      this.newTextBox33.TextAlign = HorizontalAlignment.Center;
      this.newTextBox33.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox33.TextForeColor = SystemColors.WindowText;
      this.newTextBox33.Value = "℃";
      this.newTextBox_Suji_FamilyAbbreviation.BackColor = Color.LavenderBlush;
      this.newTextBox_Suji_FamilyAbbreviation.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Suji_FamilyAbbreviation.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Suji_FamilyAbbreviation.IsDigit = false;
      this.newTextBox_Suji_FamilyAbbreviation.Lines = new string[0];
      this.newTextBox_Suji_FamilyAbbreviation.Location = new Point(5, 179);
      this.newTextBox_Suji_FamilyAbbreviation.MultiLine = false;
      this.newTextBox_Suji_FamilyAbbreviation.Name = "newTextBox_Suji_FamilyAbbreviation";
      this.newTextBox_Suji_FamilyAbbreviation.ReadOnly = true;
      this.newTextBox_Suji_FamilyAbbreviation.Size = new Size(161, 23);
      this.newTextBox_Suji_FamilyAbbreviation.TabIndex = 29;
      this.newTextBox_Suji_FamilyAbbreviation.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Suji_FamilyAbbreviation.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_Suji_FamilyAbbreviation.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Suji_FamilyAbbreviation.Value = "";
      this.newTextBox20.BackColor = Color.LavenderBlush;
      this.newTextBox20.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox20.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox20.IsDigit = false;
      this.newTextBox20.Lines = new string[1]{ "℃" };
      this.newTextBox20.Location = new Point(433, 470);
      this.newTextBox20.MultiLine = false;
      this.newTextBox20.Name = "newTextBox20";
      this.newTextBox20.ReadOnly = true;
      this.newTextBox20.Size = new Size(54, 23);
      this.newTextBox20.TabIndex = 21;
      this.newTextBox20.TextAlign = HorizontalAlignment.Center;
      this.newTextBox20.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox20.TextForeColor = SystemColors.WindowText;
      this.newTextBox20.Value = "℃";
      this.newTextBox_Suji_MoldTemp2.BackColor = Color.LavenderBlush;
      this.newTextBox_Suji_MoldTemp2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Suji_MoldTemp2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Suji_MoldTemp2.IsDigit = false;
      this.newTextBox_Suji_MoldTemp2.Lines = new string[0];
      this.newTextBox_Suji_MoldTemp2.Location = new Point(212, 492);
      this.newTextBox_Suji_MoldTemp2.MultiLine = false;
      this.newTextBox_Suji_MoldTemp2.Name = "newTextBox_Suji_MoldTemp2";
      this.newTextBox_Suji_MoldTemp2.ReadOnly = true;
      this.newTextBox_Suji_MoldTemp2.Size = new Size(222, 23);
      this.newTextBox_Suji_MoldTemp2.TabIndex = 42;
      this.newTextBox_Suji_MoldTemp2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Suji_MoldTemp2.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_Suji_MoldTemp2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Suji_MoldTemp2.Value = "";
      this.newTextBox_Suji_MeltTemp2.BackColor = Color.LavenderBlush;
      this.newTextBox_Suji_MeltTemp2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Suji_MeltTemp2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Suji_MeltTemp2.IsDigit = false;
      this.newTextBox_Suji_MeltTemp2.Lines = new string[0];
      this.newTextBox_Suji_MeltTemp2.Location = new Point(212, 470);
      this.newTextBox_Suji_MeltTemp2.MultiLine = false;
      this.newTextBox_Suji_MeltTemp2.Name = "newTextBox_Suji_MeltTemp2";
      this.newTextBox_Suji_MeltTemp2.ReadOnly = true;
      this.newTextBox_Suji_MeltTemp2.Size = new Size(222, 23);
      this.newTextBox_Suji_MeltTemp2.TabIndex = 40;
      this.newTextBox_Suji_MeltTemp2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Suji_MeltTemp2.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_Suji_MeltTemp2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Suji_MeltTemp2.Value = "";
      this.newTextBox_Suji_EjectionTemp.BackColor = Color.LavenderBlush;
      this.newTextBox_Suji_EjectionTemp.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Suji_EjectionTemp.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Suji_EjectionTemp.IsDigit = false;
      this.newTextBox_Suji_EjectionTemp.Lines = new string[0];
      this.newTextBox_Suji_EjectionTemp.Location = new Point(5, 448);
      this.newTextBox_Suji_EjectionTemp.MultiLine = false;
      this.newTextBox_Suji_EjectionTemp.Name = "newTextBox_Suji_EjectionTemp";
      this.newTextBox_Suji_EjectionTemp.ReadOnly = true;
      this.newTextBox_Suji_EjectionTemp.Size = new Size(161, 23);
      this.newTextBox_Suji_EjectionTemp.TabIndex = 36;
      this.newTextBox_Suji_EjectionTemp.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Suji_EjectionTemp.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_Suji_EjectionTemp.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Suji_EjectionTemp.Value = "";
      this.newTextBox_Suji_Image2.BackColor = Color.LavenderBlush;
      this.newTextBox_Suji_Image2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Suji_Image2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Suji_Image2.IsDigit = false;
      this.newTextBox_Suji_Image2.Lines = new string[0];
      this.newTextBox_Suji_Image2.Location = new Point(246, 270);
      this.newTextBox_Suji_Image2.MultiLine = false;
      this.newTextBox_Suji_Image2.Name = "newTextBox_Suji_Image2";
      this.newTextBox_Suji_Image2.ReadOnly = true;
      this.newTextBox_Suji_Image2.Size = new Size(241, 23);
      this.newTextBox_Suji_Image2.TabIndex = 35;
      this.newTextBox_Suji_Image2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Suji_Image2.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_Suji_Image2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Suji_Image2.Value = "";
      this.newTextBox_Suji_UDB.BackColor = Color.LavenderBlush;
      this.newTextBox_Suji_UDB.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Suji_UDB.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Suji_UDB.IsDigit = false;
      this.newTextBox_Suji_UDB.Lines = new string[0];
      this.newTextBox_Suji_UDB.Location = new Point(104, 248);
      this.newTextBox_Suji_UDB.MultiLine = false;
      this.newTextBox_Suji_UDB.Name = "newTextBox_Suji_UDB";
      this.newTextBox_Suji_UDB.ReadOnly = true;
      this.newTextBox_Suji_UDB.Size = new Size(383, 23);
      this.newTextBox_Suji_UDB.TabIndex = 33;
      this.newTextBox_Suji_UDB.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Suji_UDB.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_Suji_UDB.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Suji_UDB.Value = "";
      this.newTextBox_Suji_Image1.BackColor = Color.LavenderBlush;
      this.newTextBox_Suji_Image1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Suji_Image1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Suji_Image1.IsDigit = false;
      this.newTextBox_Suji_Image1.Lines = new string[0];
      this.newTextBox_Suji_Image1.Location = new Point(5, 270);
      this.newTextBox_Suji_Image1.MultiLine = false;
      this.newTextBox_Suji_Image1.Name = "newTextBox_Suji_Image1";
      this.newTextBox_Suji_Image1.ReadOnly = true;
      this.newTextBox_Suji_Image1.Size = new Size(242, 23);
      this.newTextBox_Suji_Image1.TabIndex = 34;
      this.newTextBox_Suji_Image1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Suji_Image1.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_Suji_Image1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Suji_Image1.Value = "";
      this.newTextBox_Suji_FillerData.BackColor = Color.LavenderBlush;
      this.newTextBox_Suji_FillerData.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Suji_FillerData.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Suji_FillerData.IsDigit = false;
      this.newTextBox_Suji_FillerData.Lines = new string[0];
      this.newTextBox_Suji_FillerData.Location = new Point(165, 179);
      this.newTextBox_Suji_FillerData.MultiLine = false;
      this.newTextBox_Suji_FillerData.Name = "newTextBox_Suji_FillerData";
      this.newTextBox_Suji_FillerData.ReadOnly = true;
      this.newTextBox_Suji_FillerData.Size = new Size(162, 23);
      this.newTextBox_Suji_FillerData.TabIndex = 30;
      this.newTextBox_Suji_FillerData.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Suji_FillerData.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_Suji_FillerData.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Suji_FillerData.Value = "";
      this.newTextBox_Suji_MoldTemp1.BackColor = Color.LavenderBlush;
      this.newTextBox_Suji_MoldTemp1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Suji_MoldTemp1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Suji_MoldTemp1.IsDigit = false;
      this.newTextBox_Suji_MoldTemp1.Lines = new string[0];
      this.newTextBox_Suji_MoldTemp1.Location = new Point(136, 492);
      this.newTextBox_Suji_MoldTemp1.MultiLine = false;
      this.newTextBox_Suji_MoldTemp1.Name = "newTextBox_Suji_MoldTemp1";
      this.newTextBox_Suji_MoldTemp1.ReadOnly = true;
      this.newTextBox_Suji_MoldTemp1.Size = new Size(77, 23);
      this.newTextBox_Suji_MoldTemp1.TabIndex = 41;
      this.newTextBox_Suji_MoldTemp1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Suji_MoldTemp1.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_Suji_MoldTemp1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Suji_MoldTemp1.Value = "";
      this.newComboBox_Suji_Manufacturer.BackColor = Color.White;
      this.newComboBox_Suji_Manufacturer.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Suji_Manufacturer.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_Suji_Manufacturer.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Suji_Manufacturer.isSameSelect = false;
      this.newComboBox_Suji_Manufacturer.Location = new Point(5, 135);
      this.newComboBox_Suji_Manufacturer.Name = "newComboBox_Suji_Manufacturer";
      this.newComboBox_Suji_Manufacturer.SelectedIndex = -1;
      this.newComboBox_Suji_Manufacturer.Size = new Size(242, 23);
      this.newComboBox_Suji_Manufacturer.TabIndex = 27;
      this.newComboBox_Suji_Manufacturer.TabStop = false;
      this.newComboBox_Suji_Manufacturer.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Suji_Manufacturer.Value = "";
      this.newComboBox_Suji_Manufacturer.SelectedIndexChanged += new EventHandler(this.newComboBox_Suji_Manufacturer_SelectedIndexChanged);
      this.newTextBox_Suji_MeltTemp1.BackColor = Color.LavenderBlush;
      this.newTextBox_Suji_MeltTemp1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Suji_MeltTemp1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Suji_MeltTemp1.IsDigit = false;
      this.newTextBox_Suji_MeltTemp1.Lines = new string[0];
      this.newTextBox_Suji_MeltTemp1.Location = new Point(136, 470);
      this.newTextBox_Suji_MeltTemp1.MultiLine = false;
      this.newTextBox_Suji_MeltTemp1.Name = "newTextBox_Suji_MeltTemp1";
      this.newTextBox_Suji_MeltTemp1.ReadOnly = true;
      this.newTextBox_Suji_MeltTemp1.Size = new Size(77, 23);
      this.newTextBox_Suji_MeltTemp1.TabIndex = 39;
      this.newTextBox_Suji_MeltTemp1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Suji_MeltTemp1.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_Suji_MeltTemp1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Suji_MeltTemp1.Value = "";
      this.newTextBox_Suji_MaterialID.BackColor = Color.LavenderBlush;
      this.newTextBox_Suji_MaterialID.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Suji_MaterialID.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Suji_MaterialID.IsDigit = false;
      this.newTextBox_Suji_MaterialID.Lines = new string[0];
      this.newTextBox_Suji_MaterialID.Location = new Point(326, 179);
      this.newTextBox_Suji_MaterialID.MultiLine = false;
      this.newTextBox_Suji_MaterialID.Name = "newTextBox_Suji_MaterialID";
      this.newTextBox_Suji_MaterialID.ReadOnly = true;
      this.newTextBox_Suji_MaterialID.Size = new Size(161, 23);
      this.newTextBox_Suji_MaterialID.TabIndex = 31;
      this.newTextBox_Suji_MaterialID.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Suji_MaterialID.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_Suji_MaterialID.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Suji_MaterialID.Value = "";
      this.newTextBox_Suji_Search.BackColor = SystemColors.Window;
      this.newTextBox_Suji_Search.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Suji_Search.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Suji_Search.IsDigit = false;
      this.newTextBox_Suji_Search.Lines = new string[0];
      this.newTextBox_Suji_Search.Location = new Point(104, 72);
      this.newTextBox_Suji_Search.MultiLine = false;
      this.newTextBox_Suji_Search.Name = "newTextBox_Suji_Search";
      this.newTextBox_Suji_Search.ReadOnly = false;
      this.newTextBox_Suji_Search.Size = new Size(383, 23);
      this.newTextBox_Suji_Search.TabIndex = 26;
      this.newTextBox_Suji_Search.TextAlign = HorizontalAlignment.Left;
      this.newTextBox_Suji_Search.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Suji_Search.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Suji_Search.Value = "";
      this.newTextBox_Suji_Search.TextBoxKeyUp += new KeyEventHandler(this.newTextBox_Suji_Search_TextBoxKeyUp);
      this.newComboBox_Suji_TradeName.BackColor = Color.White;
      this.newComboBox_Suji_TradeName.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Suji_TradeName.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_Suji_TradeName.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Suji_TradeName.isSameSelect = false;
      this.newComboBox_Suji_TradeName.Location = new Point(246, 135);
      this.newComboBox_Suji_TradeName.Name = "newComboBox_Suji_TradeName";
      this.newComboBox_Suji_TradeName.SelectedIndex = -1;
      this.newComboBox_Suji_TradeName.Size = new Size(241, 23);
      this.newComboBox_Suji_TradeName.TabIndex = 28;
      this.newComboBox_Suji_TradeName.TabStop = false;
      this.newComboBox_Suji_TradeName.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Suji_TradeName.Value = "";
      this.newComboBox_Suji_TradeName.SelectedIndexChanged += new EventHandler(this.newComboBox_Suji_TradeName_SelectedIndexChanged);
      this.tabPage3.Controls.Add((Control) this.panel7);
      this.tabPage3.Controls.Add((Control) this.panel9);
      this.tabPage3.Controls.Add((Control) this.panel10);
      this.tabPage3.Controls.Add((Control) this.panel8);
      this.tabPage3.Controls.Add((Control) this.panel13);
      this.tabPage3.Controls.Add((Control) this.panel12);
      this.tabPage3.Controls.Add((Control) this.panel6);
      this.tabPage3.Controls.Add((Control) this.label38);
      this.tabPage3.Controls.Add((Control) this.label37);
      this.tabPage3.Controls.Add((Control) this.label35);
      this.tabPage3.Controls.Add((Control) this.label32);
      this.tabPage3.Controls.Add((Control) this.label_Proc_FC2);
      this.tabPage3.Controls.Add((Control) this.label_Proc_FC4);
      this.tabPage3.Controls.Add((Control) this.label_Proc_PHC2);
      this.tabPage3.Controls.Add((Control) this.label_Proc_PHC1);
      this.tabPage3.Controls.Add((Control) this.label_Proc_FC3);
      this.tabPage3.Controls.Add((Control) this.label_Proc_FC1);
      this.tabPage3.Controls.Add((Control) this.label25);
      this.tabPage3.Controls.Add((Control) this.label24);
      this.tabPage3.Controls.Add((Control) this.label_Proc_CoolingTime);
      this.tabPage3.Controls.Add((Control) this.newComboBox_Proc_PHC);
      this.tabPage3.Controls.Add((Control) this.newComboBox_Proc_FC);
      this.tabPage3.Controls.Add((Control) this.newComboBox_Proc_CoolType);
      this.tabPage3.Controls.Add((Control) this.newComboBox_Proc_VP);
      this.tabPage3.Controls.Add((Control) this.newTextBox_Proc_CoolType);
      this.tabPage3.Controls.Add((Control) this.newTextBox_Proc_MoldTemp1);
      this.tabPage3.Controls.Add((Control) this.newTextBox_Proc_MeltTemp1);
      this.tabPage3.Controls.Add((Control) this.newTextBox24);
      this.tabPage3.Controls.Add((Control) this.newTextBox21);
      this.tabPage3.Controls.Add((Control) this.newTextBox19);
      this.tabPage3.Controls.Add((Control) this.newTextBox_Proc_MoldTemp2);
      this.tabPage3.Controls.Add((Control) this.newTextBox_Proc_CoolingTime);
      this.tabPage3.Controls.Add((Control) this.newTextBox_Proc_MeltTemp2);
      this.tabPage3.Location = new Point(4, 22);
      this.tabPage3.Name = "tabPage3";
      this.tabPage3.Padding = new Padding(3);
      this.tabPage3.Size = new Size(492, 520);
      this.tabPage3.TabIndex = 2;
      this.tabPage3.Text = "tabPage3";
      this.tabPage3.UseVisualStyleBackColor = true;
      this.panel7.BorderStyle = BorderStyle.FixedSingle;
      this.panel7.Controls.Add((Control) this.dataGridView_Proc_FC2);
      this.panel7.Location = new Point(343, 158);
      this.panel7.Name = "panel7";
      this.panel7.Size = new Size(144, 134);
      this.panel7.TabIndex = 95;
      this.dataGridView_Proc_FC2.AllowUserToAddRows = false;
      this.dataGridView_Proc_FC2.AllowUserToOrderColumns = true;
      this.dataGridView_Proc_FC2.AllowUserToResizeColumns = false;
      this.dataGridView_Proc_FC2.AllowUserToResizeRows = false;
      this.dataGridView_Proc_FC2.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_Proc_FC2.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_Proc_FC2.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_Proc_FC2.BorderStyle = BorderStyle.None;
      this.dataGridView_Proc_FC2.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_Proc_FC2.ColumnHeadersVisible = false;
      this.dataGridView_Proc_FC2.Columns.AddRange((DataGridViewColumn) this.dataGridViewTextBoxColumn1, (DataGridViewColumn) this.dataGridViewTextBoxColumn2);
      gridViewCellStyle1.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle1.BackColor = Color.White;
      gridViewCellStyle1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle1.ForeColor = SystemColors.ControlText;
      gridViewCellStyle1.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle1.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle1.WrapMode = DataGridViewTriState.False;
      this.dataGridView_Proc_FC2.DefaultCellStyle = gridViewCellStyle1;
      this.dataGridView_Proc_FC2.Location = new Point(-1, -1);
      this.dataGridView_Proc_FC2.Name = "dataGridView_Proc_FC2";
      this.dataGridView_Proc_FC2.RowHeadersVisible = false;
      this.dataGridView_Proc_FC2.RowTemplate.Height = 23;
      this.dataGridView_Proc_FC2.Size = new Size(146, 135);
      this.dataGridView_Proc_FC2.TabIndex = 55;
      this.dataGridView_Proc_FC2.CellEndEdit += new DataGridViewCellEventHandler(this.dataGridView_Proc_FC_CellEndEdit);
      this.dataGridView_Proc_FC2.RowPostPaint += new DataGridViewRowPostPaintEventHandler(this.dataGridView_Proc_FC_RowPostPaint);
      this.dataGridView_Proc_FC2.RowsAdded += new DataGridViewRowsAddedEventHandler(this.dataGridView_Proc_FC2_RowsAdded);
      this.dataGridView_Proc_FC2.SelectionChanged += new EventHandler(this.dataGridView_Proc_SelectionChanged);
      this.dataGridView_Proc_FC2.MouseLeave += new EventHandler(this.dataGridView_Proc_MouseLeave);
      this.dataGridViewTextBoxColumn1.HeaderText = "";
      this.dataGridViewTextBoxColumn1.Name = "dataGridViewTextBoxColumn1";
      this.dataGridViewTextBoxColumn2.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      this.dataGridViewTextBoxColumn2.HeaderText = "";
      this.dataGridViewTextBoxColumn2.Name = "dataGridViewTextBoxColumn2";
      this.dataGridViewTextBoxColumn2.ReadOnly = true;
      this.dataGridViewTextBoxColumn2.Width = 45;
      this.panel9.BorderStyle = BorderStyle.FixedSingle;
      this.panel9.Controls.Add((Control) this.dataGridView_Proc_FC4);
      this.panel9.Location = new Point(343, 318);
      this.panel9.Name = "panel9";
      this.panel9.Size = new Size(144, 23);
      this.panel9.TabIndex = 95;
      this.dataGridView_Proc_FC4.AllowUserToAddRows = false;
      this.dataGridView_Proc_FC4.AllowUserToOrderColumns = true;
      this.dataGridView_Proc_FC4.AllowUserToResizeColumns = false;
      this.dataGridView_Proc_FC4.AllowUserToResizeRows = false;
      this.dataGridView_Proc_FC4.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_Proc_FC4.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_Proc_FC4.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_Proc_FC4.BorderStyle = BorderStyle.None;
      this.dataGridView_Proc_FC4.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_Proc_FC4.ColumnHeadersVisible = false;
      this.dataGridView_Proc_FC4.Columns.AddRange((DataGridViewColumn) this.dataGridViewTextBoxColumn5, (DataGridViewColumn) this.dataGridViewTextBoxColumn6);
      gridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle2.BackColor = Color.White;
      gridViewCellStyle2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle2.ForeColor = SystemColors.ControlText;
      gridViewCellStyle2.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle2.WrapMode = DataGridViewTriState.False;
      this.dataGridView_Proc_FC4.DefaultCellStyle = gridViewCellStyle2;
      this.dataGridView_Proc_FC4.Location = new Point(-1, -1);
      this.dataGridView_Proc_FC4.Name = "dataGridView_Proc_FC4";
      this.dataGridView_Proc_FC4.RowHeadersVisible = false;
      this.dataGridView_Proc_FC4.RowTemplate.Height = 23;
      this.dataGridView_Proc_FC4.Size = new Size(146, 24);
      this.dataGridView_Proc_FC4.TabIndex = 57;
      this.dataGridViewTextBoxColumn5.HeaderText = "";
      this.dataGridViewTextBoxColumn5.Name = "dataGridViewTextBoxColumn5";
      this.dataGridViewTextBoxColumn6.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      this.dataGridViewTextBoxColumn6.HeaderText = "";
      this.dataGridViewTextBoxColumn6.Name = "dataGridViewTextBoxColumn6";
      this.dataGridViewTextBoxColumn6.ReadOnly = true;
      this.dataGridViewTextBoxColumn6.Width = 45;
      this.panel10.BorderStyle = BorderStyle.FixedSingle;
      this.panel10.Controls.Add((Control) this.dataGridView_Proc_VP);
      this.panel10.Location = new Point(197, 365);
      this.panel10.Name = "panel10";
      this.panel10.Size = new Size(144, 23);
      this.panel10.TabIndex = 95;
      this.dataGridView_Proc_VP.AllowUserToAddRows = false;
      this.dataGridView_Proc_VP.AllowUserToOrderColumns = true;
      this.dataGridView_Proc_VP.AllowUserToResizeColumns = false;
      this.dataGridView_Proc_VP.AllowUserToResizeRows = false;
      this.dataGridView_Proc_VP.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_Proc_VP.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_Proc_VP.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_Proc_VP.BorderStyle = BorderStyle.None;
      this.dataGridView_Proc_VP.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_Proc_VP.ColumnHeadersVisible = false;
      this.dataGridView_Proc_VP.Columns.AddRange((DataGridViewColumn) this.dataGridViewTextBoxColumn7, (DataGridViewColumn) this.dataGridViewTextBoxColumn8);
      gridViewCellStyle3.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle3.BackColor = Color.White;
      gridViewCellStyle3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle3.ForeColor = SystemColors.ControlText;
      gridViewCellStyle3.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle3.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle3.WrapMode = DataGridViewTriState.False;
      this.dataGridView_Proc_VP.DefaultCellStyle = gridViewCellStyle3;
      this.dataGridView_Proc_VP.Location = new Point(-1, -1);
      this.dataGridView_Proc_VP.Name = "dataGridView_Proc_VP";
      this.dataGridView_Proc_VP.RowHeadersVisible = false;
      this.dataGridView_Proc_VP.RowTemplate.Height = 23;
      this.dataGridView_Proc_VP.Size = new Size(146, 24);
      this.dataGridView_Proc_VP.TabIndex = 59;
      this.dataGridView_Proc_VP.SelectionChanged += new EventHandler(this.dataGridView_Proc_VP_SelectionChanged);
      this.dataGridView_Proc_VP.MouseLeave += new EventHandler(this.dataGridView_Proc_VP_MouseLeave);
      this.dataGridViewTextBoxColumn7.HeaderText = "";
      this.dataGridViewTextBoxColumn7.Name = "dataGridViewTextBoxColumn7";
      this.dataGridViewTextBoxColumn8.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      this.dataGridViewTextBoxColumn8.HeaderText = "";
      this.dataGridViewTextBoxColumn8.Name = "dataGridViewTextBoxColumn8";
      this.dataGridViewTextBoxColumn8.ReadOnly = true;
      this.dataGridViewTextBoxColumn8.Width = 45;
      this.panel8.BorderStyle = BorderStyle.FixedSingle;
      this.panel8.Controls.Add((Control) this.dataGridView_Proc_FC3);
      this.panel8.Location = new Point(197, 318);
      this.panel8.Name = "panel8";
      this.panel8.Size = new Size(144, 23);
      this.panel8.TabIndex = 95;
      this.dataGridView_Proc_FC3.AllowUserToAddRows = false;
      this.dataGridView_Proc_FC3.AllowUserToOrderColumns = true;
      this.dataGridView_Proc_FC3.AllowUserToResizeColumns = false;
      this.dataGridView_Proc_FC3.AllowUserToResizeRows = false;
      this.dataGridView_Proc_FC3.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_Proc_FC3.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_Proc_FC3.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_Proc_FC3.BorderStyle = BorderStyle.None;
      this.dataGridView_Proc_FC3.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_Proc_FC3.ColumnHeadersVisible = false;
      this.dataGridView_Proc_FC3.Columns.AddRange((DataGridViewColumn) this.dataGridViewTextBoxColumn3, (DataGridViewColumn) this.dataGridViewTextBoxColumn4);
      gridViewCellStyle4.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle4.BackColor = Color.White;
      gridViewCellStyle4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle4.ForeColor = SystemColors.ControlText;
      gridViewCellStyle4.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle4.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle4.WrapMode = DataGridViewTriState.False;
      this.dataGridView_Proc_FC3.DefaultCellStyle = gridViewCellStyle4;
      this.dataGridView_Proc_FC3.Location = new Point(-1, -1);
      this.dataGridView_Proc_FC3.Name = "dataGridView_Proc_FC3";
      this.dataGridView_Proc_FC3.RowHeadersVisible = false;
      this.dataGridView_Proc_FC3.RowTemplate.Height = 23;
      this.dataGridView_Proc_FC3.Size = new Size(146, 24);
      this.dataGridView_Proc_FC3.TabIndex = 53;
      this.dataGridViewTextBoxColumn3.HeaderText = "";
      this.dataGridViewTextBoxColumn3.Name = "dataGridViewTextBoxColumn3";
      this.dataGridViewTextBoxColumn4.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      this.dataGridViewTextBoxColumn4.HeaderText = "";
      this.dataGridViewTextBoxColumn4.Name = "dataGridViewTextBoxColumn4";
      this.dataGridViewTextBoxColumn4.ReadOnly = true;
      this.dataGridViewTextBoxColumn4.Width = 45;
      this.panel13.BorderStyle = BorderStyle.FixedSingle;
      this.panel13.Controls.Add((Control) this.dataGridView_Proc_PHC2);
      this.panel13.Location = new Point(343, 433);
      this.panel13.Name = "panel13";
      this.panel13.Size = new Size(144, 70);
      this.panel13.TabIndex = 95;
      this.dataGridView_Proc_PHC2.AllowUserToAddRows = false;
      this.dataGridView_Proc_PHC2.AllowUserToOrderColumns = true;
      this.dataGridView_Proc_PHC2.AllowUserToResizeColumns = false;
      this.dataGridView_Proc_PHC2.AllowUserToResizeRows = false;
      this.dataGridView_Proc_PHC2.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_Proc_PHC2.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_Proc_PHC2.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_Proc_PHC2.BorderStyle = BorderStyle.None;
      this.dataGridView_Proc_PHC2.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_Proc_PHC2.ColumnHeadersVisible = false;
      this.dataGridView_Proc_PHC2.Columns.AddRange((DataGridViewColumn) this.dataGridViewTextBoxColumn13, (DataGridViewColumn) this.dataGridViewTextBoxColumn14);
      gridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle5.BackColor = Color.White;
      gridViewCellStyle5.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle5.ForeColor = SystemColors.ControlText;
      gridViewCellStyle5.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle5.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle5.WrapMode = DataGridViewTriState.False;
      this.dataGridView_Proc_PHC2.DefaultCellStyle = gridViewCellStyle5;
      this.dataGridView_Proc_PHC2.Location = new Point(-1, -1);
      this.dataGridView_Proc_PHC2.Name = "dataGridView_Proc_PHC2";
      this.dataGridView_Proc_PHC2.RowHeadersVisible = false;
      this.dataGridView_Proc_PHC2.RowTemplate.Height = 23;
      this.dataGridView_Proc_PHC2.Size = new Size(146, 71);
      this.dataGridView_Proc_PHC2.TabIndex = 64;
      this.dataGridView_Proc_PHC2.CellEndEdit += new DataGridViewCellEventHandler(this.dataGridView_Proc_PHC2_CellEndEdit);
      this.dataGridView_Proc_PHC2.RowsAdded += new DataGridViewRowsAddedEventHandler(this.dataGridView_Proc_PHC2_RowsAdded);
      this.dataGridView_Proc_PHC2.SelectionChanged += new EventHandler(this.dataGridView_Proc_PHC2_SelectionChanged);
      this.dataGridView_Proc_PHC2.UserAddedRow += new DataGridViewRowEventHandler(this.dataGridView_Proc_PHC2_UserAddedRow);
      this.dataGridView_Proc_PHC2.MouseLeave += new EventHandler(this.dataGridView_Proc_PHC2_MouseLeave);
      this.dataGridViewTextBoxColumn13.HeaderText = "";
      this.dataGridViewTextBoxColumn13.Name = "dataGridViewTextBoxColumn13";
      this.dataGridViewTextBoxColumn14.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      this.dataGridViewTextBoxColumn14.HeaderText = "";
      this.dataGridViewTextBoxColumn14.Name = "dataGridViewTextBoxColumn14";
      this.dataGridViewTextBoxColumn14.ReadOnly = true;
      this.dataGridViewTextBoxColumn14.Width = 45;
      this.panel12.BorderStyle = BorderStyle.FixedSingle;
      this.panel12.Controls.Add((Control) this.dataGridView_Proc_PHC1);
      this.panel12.Location = new Point(197, 433);
      this.panel12.Name = "panel12";
      this.panel12.Size = new Size(144, 70);
      this.panel12.TabIndex = 95;
      this.dataGridView_Proc_PHC1.AllowUserToAddRows = false;
      this.dataGridView_Proc_PHC1.AllowUserToOrderColumns = true;
      this.dataGridView_Proc_PHC1.AllowUserToResizeColumns = false;
      this.dataGridView_Proc_PHC1.AllowUserToResizeRows = false;
      this.dataGridView_Proc_PHC1.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_Proc_PHC1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_Proc_PHC1.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_Proc_PHC1.BorderStyle = BorderStyle.None;
      this.dataGridView_Proc_PHC1.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_Proc_PHC1.ColumnHeadersVisible = false;
      this.dataGridView_Proc_PHC1.Columns.AddRange((DataGridViewColumn) this.dataGridViewTextBoxColumn11, (DataGridViewColumn) this.dataGridViewTextBoxColumn12);
      gridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle6.BackColor = Color.White;
      gridViewCellStyle6.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle6.ForeColor = SystemColors.ControlText;
      gridViewCellStyle6.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle6.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle6.WrapMode = DataGridViewTriState.False;
      this.dataGridView_Proc_PHC1.DefaultCellStyle = gridViewCellStyle6;
      this.dataGridView_Proc_PHC1.Location = new Point(-1, -1);
      this.dataGridView_Proc_PHC1.Name = "dataGridView_Proc_PHC1";
      this.dataGridView_Proc_PHC1.RowHeadersVisible = false;
      this.dataGridView_Proc_PHC1.RowTemplate.Height = 23;
      this.dataGridView_Proc_PHC1.Size = new Size(146, 71);
      this.dataGridView_Proc_PHC1.TabIndex = 62;
      this.dataGridView_Proc_PHC1.CellEndEdit += new DataGridViewCellEventHandler(this.dataGridView_Proc_PHC1_CellEndEdit);
      this.dataGridView_Proc_PHC1.RowsAdded += new DataGridViewRowsAddedEventHandler(this.dataGridView_Proc_PHC1_RowsAdded);
      this.dataGridView_Proc_PHC1.SelectionChanged += new EventHandler(this.dataGridView_Proc_PHC1_SelectionChanged);
      this.dataGridView_Proc_PHC1.UserAddedRow += new DataGridViewRowEventHandler(this.dataGridView_Proc_PHC1_UserAddedRow);
      this.dataGridView_Proc_PHC1.MouseLeave += new EventHandler(this.dataGridView_Proc_PHC1_MouseLeave);
      this.dataGridViewTextBoxColumn11.HeaderText = "";
      this.dataGridViewTextBoxColumn11.Name = "dataGridViewTextBoxColumn11";
      this.dataGridViewTextBoxColumn12.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      this.dataGridViewTextBoxColumn12.HeaderText = "";
      this.dataGridViewTextBoxColumn12.Name = "dataGridViewTextBoxColumn12";
      this.dataGridViewTextBoxColumn12.ReadOnly = true;
      this.dataGridViewTextBoxColumn12.Width = 45;
      this.panel6.BorderStyle = BorderStyle.FixedSingle;
      this.panel6.Controls.Add((Control) this.dataGridView_Proc_FC1);
      this.panel6.Location = new Point(197, 158);
      this.panel6.Name = "panel6";
      this.panel6.Size = new Size(144, 134);
      this.panel6.TabIndex = 95;
      this.dataGridView_Proc_FC1.AllowUserToAddRows = false;
      this.dataGridView_Proc_FC1.AllowUserToOrderColumns = true;
      this.dataGridView_Proc_FC1.AllowUserToResizeColumns = false;
      this.dataGridView_Proc_FC1.AllowUserToResizeRows = false;
      this.dataGridView_Proc_FC1.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_Proc_FC1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_Proc_FC1.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_Proc_FC1.BorderStyle = BorderStyle.None;
      this.dataGridView_Proc_FC1.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_Proc_FC1.ColumnHeadersVisible = false;
      this.dataGridView_Proc_FC1.Columns.AddRange((DataGridViewColumn) this.Column1, (DataGridViewColumn) this.Column2);
      gridViewCellStyle7.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle7.BackColor = Color.White;
      gridViewCellStyle7.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle7.ForeColor = SystemColors.ControlText;
      gridViewCellStyle7.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle7.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle7.WrapMode = DataGridViewTriState.False;
      this.dataGridView_Proc_FC1.DefaultCellStyle = gridViewCellStyle7;
      this.dataGridView_Proc_FC1.Location = new Point(-1, -1);
      this.dataGridView_Proc_FC1.Name = "dataGridView_Proc_FC1";
      this.dataGridView_Proc_FC1.RowHeadersVisible = false;
      this.dataGridView_Proc_FC1.RowTemplate.Height = 23;
      this.dataGridView_Proc_FC1.Size = new Size(146, 135);
      this.dataGridView_Proc_FC1.TabIndex = 51;
      this.dataGridView_Proc_FC1.CellEndEdit += new DataGridViewCellEventHandler(this.dataGridView_Proc_FC_CellEndEdit);
      this.dataGridView_Proc_FC1.RowPostPaint += new DataGridViewRowPostPaintEventHandler(this.dataGridView_Proc_FC_RowPostPaint);
      this.dataGridView_Proc_FC1.RowsAdded += new DataGridViewRowsAddedEventHandler(this.dataGridView_Proc_FC1_RowsAdded);
      this.dataGridView_Proc_FC1.SelectionChanged += new EventHandler(this.dataGridView_Proc_SelectionChanged);
      this.dataGridView_Proc_FC1.MouseLeave += new EventHandler(this.dataGridView_Proc_MouseLeave);
      this.Column1.HeaderText = "";
      this.Column1.Name = "Column1";
      this.Column2.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      this.Column2.HeaderText = "";
      this.Column2.Name = "Column2";
      this.Column2.ReadOnly = true;
      this.Column2.Width = 45;
      this.label38.BackColor = Color.FromArgb(229, 238, 248);
      this.label38.BorderStyle = BorderStyle.FixedSingle;
      this.label38.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label38.ForeColor = Color.MidnightBlue;
      this.label38.Location = new Point(5, 392);
      this.label38.Name = "label38";
      this.label38.Size = new Size(482, 20);
      this.label38.TabIndex = 92;
      this.label38.Text = "Pack/Holding Control";
      this.label38.TextAlign = ContentAlignment.MiddleCenter;
      this.label37.BackColor = Color.FromArgb(229, 238, 248);
      this.label37.BorderStyle = BorderStyle.FixedSingle;
      this.label37.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label37.ForeColor = Color.MidnightBlue;
      this.label37.Location = new Point(5, 346);
      this.label37.Name = "label37";
      this.label37.Size = new Size(482, 20);
      this.label37.TabIndex = 92;
      this.label37.Text = "V/P Switch-Over";
      this.label37.TextAlign = ContentAlignment.MiddleCenter;
      this.label35.BackColor = Color.FromArgb(229, 238, 248);
      this.label35.BorderStyle = BorderStyle.FixedSingle;
      this.label35.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label35.ForeColor = Color.MidnightBlue;
      this.label35.Location = new Point(5, 117);
      this.label35.Name = "label35";
      this.label35.Size = new Size(482, 20);
      this.label35.TabIndex = 92;
      this.label35.Text = "Filling Control";
      this.label35.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Proc_FC2.BackColor = Color.WhiteSmoke;
      this.label_Proc_FC2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Proc_FC2.Location = new Point(343, 136);
      this.label_Proc_FC2.Name = "label_Proc_FC2";
      this.label_Proc_FC2.Size = new Size(144, 23);
      this.label_Proc_FC2.TabIndex = 54;
      this.label_Proc_FC2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Proc_FC4.BackColor = Color.WhiteSmoke;
      this.label_Proc_FC4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Proc_FC4.Location = new Point(343, 296);
      this.label_Proc_FC4.Name = "label_Proc_FC4";
      this.label_Proc_FC4.Size = new Size(144, 23);
      this.label_Proc_FC4.TabIndex = 56;
      this.label_Proc_FC4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Proc_PHC2.BackColor = Color.WhiteSmoke;
      this.label_Proc_PHC2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Proc_PHC2.Location = new Point(343, 411);
      this.label_Proc_PHC2.Name = "label_Proc_PHC2";
      this.label_Proc_PHC2.Size = new Size(144, 23);
      this.label_Proc_PHC2.TabIndex = 63;
      this.label_Proc_PHC2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Proc_PHC1.BackColor = Color.WhiteSmoke;
      this.label_Proc_PHC1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Proc_PHC1.Location = new Point(197, 411);
      this.label_Proc_PHC1.Name = "label_Proc_PHC1";
      this.label_Proc_PHC1.Size = new Size(144, 23);
      this.label_Proc_PHC1.TabIndex = 6;
      this.label_Proc_PHC1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Proc_FC3.BackColor = Color.WhiteSmoke;
      this.label_Proc_FC3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Proc_FC3.Location = new Point(197, 296);
      this.label_Proc_FC3.Name = "label_Proc_FC3";
      this.label_Proc_FC3.Size = new Size(144, 23);
      this.label_Proc_FC3.TabIndex = 52;
      this.label_Proc_FC3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Proc_FC1.BackColor = Color.WhiteSmoke;
      this.label_Proc_FC1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Proc_FC1.Location = new Point(197, 136);
      this.label_Proc_FC1.Name = "label_Proc_FC1";
      this.label_Proc_FC1.Size = new Size(144, 23);
      this.label_Proc_FC1.TabIndex = 50;
      this.label_Proc_FC1.TextAlign = ContentAlignment.MiddleCenter;
      this.newComboBox_Proc_PHC.BackColor = Color.White;
      this.newComboBox_Proc_PHC.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Proc_PHC.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_Proc_PHC.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Proc_PHC.isSameSelect = false;
      this.newComboBox_Proc_PHC.Location = new Point(5, 411);
      this.newComboBox_Proc_PHC.Name = "newComboBox_Proc_PHC";
      this.newComboBox_Proc_PHC.SelectedIndex = -1;
      this.newComboBox_Proc_PHC.Size = new Size(187, 23);
      this.newComboBox_Proc_PHC.TabIndex = 60;
      this.newComboBox_Proc_PHC.TabStop = false;
      this.newComboBox_Proc_PHC.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Proc_PHC.Value = "";
      this.newComboBox_Proc_PHC.SelectedIndexChanged += new EventHandler(this.newComboBox_Proc_PHC_SelectedIndexChanged);
      this.newComboBox_Proc_FC.BackColor = Color.White;
      this.newComboBox_Proc_FC.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Proc_FC.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_Proc_FC.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Proc_FC.isSameSelect = false;
      this.newComboBox_Proc_FC.Location = new Point(5, 136);
      this.newComboBox_Proc_FC.Name = "newComboBox_Proc_FC";
      this.newComboBox_Proc_FC.SelectedIndex = -1;
      this.newComboBox_Proc_FC.Size = new Size(187, 23);
      this.newComboBox_Proc_FC.TabIndex = 49;
      this.newComboBox_Proc_FC.TabStop = false;
      this.newComboBox_Proc_FC.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Proc_FC.Value = "";
      this.newComboBox_Proc_FC.SelectedIndexChanged += new EventHandler(this.newComboBox_Proc_FC_SelectedIndexChanged);
      this.newComboBox_Proc_CoolType.BackColor = Color.White;
      this.newComboBox_Proc_CoolType.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Proc_CoolType.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_Proc_CoolType.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Proc_CoolType.isSameSelect = false;
      this.newComboBox_Proc_CoolType.Location = new Point(5, 24);
      this.newComboBox_Proc_CoolType.Name = "newComboBox_Proc_CoolType";
      this.newComboBox_Proc_CoolType.SelectedIndex = -1;
      this.newComboBox_Proc_CoolType.Size = new Size(220, 23);
      this.newComboBox_Proc_CoolType.TabIndex = 43;
      this.newComboBox_Proc_CoolType.TabStop = false;
      this.newComboBox_Proc_CoolType.TextAlign = HorizontalAlignment.Center;
      this.newComboBox_Proc_CoolType.Value = "";
      this.newComboBox_Proc_CoolType.SelectedIndexChanged += new EventHandler(this.newComboBox_Proc_CoolType_SelectedIndexChanged);
      this.newComboBox_Proc_VP.BackColor = Color.White;
      this.newComboBox_Proc_VP.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Proc_VP.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_Proc_VP.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Proc_VP.isSameSelect = false;
      this.newComboBox_Proc_VP.Location = new Point(5, 365);
      this.newComboBox_Proc_VP.Name = "newComboBox_Proc_VP";
      this.newComboBox_Proc_VP.SelectedIndex = -1;
      this.newComboBox_Proc_VP.Size = new Size(187, 23);
      this.newComboBox_Proc_VP.TabIndex = 58;
      this.newComboBox_Proc_VP.TabStop = false;
      this.newComboBox_Proc_VP.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Proc_VP.Value = "";
      this.newComboBox_Proc_VP.SelectedIndexChanged += new EventHandler(this.newComboBox_Proc_VP_SelectedIndexChanged);
      this.newTextBox_Proc_CoolType.BackColor = Color.LavenderBlush;
      this.newTextBox_Proc_CoolType.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Proc_CoolType.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Proc_CoolType.IsDigit = false;
      this.newTextBox_Proc_CoolType.Lines = new string[0];
      this.newTextBox_Proc_CoolType.Location = new Point(224, 24);
      this.newTextBox_Proc_CoolType.MultiLine = false;
      this.newTextBox_Proc_CoolType.Name = "newTextBox_Proc_CoolType";
      this.newTextBox_Proc_CoolType.ReadOnly = true;
      this.newTextBox_Proc_CoolType.Size = new Size(263, 23);
      this.newTextBox_Proc_CoolType.TabIndex = 44;
      this.newTextBox_Proc_CoolType.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Proc_CoolType.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_Proc_CoolType.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Proc_CoolType.Value = "";
      this.newTextBox_Proc_MoldTemp1.BackColor = SystemColors.Window;
      this.newTextBox_Proc_MoldTemp1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Proc_MoldTemp1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Proc_MoldTemp1.IsDigit = true;
      this.newTextBox_Proc_MoldTemp1.Lines = new string[0];
      this.newTextBox_Proc_MoldTemp1.Location = new Point(224, 90);
      this.newTextBox_Proc_MoldTemp1.MultiLine = false;
      this.newTextBox_Proc_MoldTemp1.Name = "newTextBox_Proc_MoldTemp1";
      this.newTextBox_Proc_MoldTemp1.ReadOnly = false;
      this.newTextBox_Proc_MoldTemp1.Size = new Size(76, 23);
      this.newTextBox_Proc_MoldTemp1.TabIndex = 48;
      this.newTextBox_Proc_MoldTemp1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Proc_MoldTemp1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Proc_MoldTemp1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Proc_MoldTemp1.Value = "";
      this.newTextBox_Proc_MeltTemp1.BackColor = SystemColors.Window;
      this.newTextBox_Proc_MeltTemp1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Proc_MeltTemp1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Proc_MeltTemp1.IsDigit = true;
      this.newTextBox_Proc_MeltTemp1.Lines = new string[0];
      this.newTextBox_Proc_MeltTemp1.Location = new Point(224, 68);
      this.newTextBox_Proc_MeltTemp1.MultiLine = false;
      this.newTextBox_Proc_MeltTemp1.Name = "newTextBox_Proc_MeltTemp1";
      this.newTextBox_Proc_MeltTemp1.ReadOnly = false;
      this.newTextBox_Proc_MeltTemp1.Size = new Size(76, 23);
      this.newTextBox_Proc_MeltTemp1.TabIndex = 46;
      this.newTextBox_Proc_MeltTemp1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Proc_MeltTemp1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Proc_MeltTemp1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Proc_MeltTemp1.Value = "";
      this.newTextBox24.BackColor = Color.LavenderBlush;
      this.newTextBox24.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox24.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox24.IsDigit = false;
      this.newTextBox24.Lines = new string[1]{ "℃" };
      this.newTextBox24.Location = new Point(451, 90);
      this.newTextBox24.MultiLine = false;
      this.newTextBox24.Name = "newTextBox24";
      this.newTextBox24.ReadOnly = true;
      this.newTextBox24.Size = new Size(36, 23);
      this.newTextBox24.TabIndex = 21;
      this.newTextBox24.TabStop = false;
      this.newTextBox24.TextAlign = HorizontalAlignment.Center;
      this.newTextBox24.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox24.TextForeColor = SystemColors.WindowText;
      this.newTextBox24.Value = "℃";
      this.newTextBox21.BackColor = Color.LavenderBlush;
      this.newTextBox21.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox21.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox21.IsDigit = false;
      this.newTextBox21.Lines = new string[1]{ "℃" };
      this.newTextBox21.Location = new Point(451, 68);
      this.newTextBox21.MultiLine = false;
      this.newTextBox21.Name = "newTextBox21";
      this.newTextBox21.ReadOnly = true;
      this.newTextBox21.Size = new Size(36, 23);
      this.newTextBox21.TabIndex = 21;
      this.newTextBox21.TabStop = false;
      this.newTextBox21.TextAlign = HorizontalAlignment.Center;
      this.newTextBox21.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox21.TextForeColor = SystemColors.WindowText;
      this.newTextBox21.Value = "℃";
      this.newTextBox19.BackColor = Color.LavenderBlush;
      this.newTextBox19.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox19.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox19.IsDigit = false;
      this.newTextBox19.Lines = new string[1]{ "S" };
      this.newTextBox19.Location = new Point(451, 46);
      this.newTextBox19.MultiLine = false;
      this.newTextBox19.Name = "newTextBox19";
      this.newTextBox19.ReadOnly = true;
      this.newTextBox19.Size = new Size(36, 23);
      this.newTextBox19.TabIndex = 21;
      this.newTextBox19.TabStop = false;
      this.newTextBox19.TextAlign = HorizontalAlignment.Center;
      this.newTextBox19.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox19.TextForeColor = SystemColors.WindowText;
      this.newTextBox19.Value = "S";
      this.newTextBox_Proc_MoldTemp2.BackColor = Color.LavenderBlush;
      this.newTextBox_Proc_MoldTemp2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Proc_MoldTemp2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Proc_MoldTemp2.IsDigit = false;
      this.newTextBox_Proc_MoldTemp2.Lines = new string[0];
      this.newTextBox_Proc_MoldTemp2.Location = new Point(299, 90);
      this.newTextBox_Proc_MoldTemp2.MultiLine = false;
      this.newTextBox_Proc_MoldTemp2.Name = "newTextBox_Proc_MoldTemp2";
      this.newTextBox_Proc_MoldTemp2.ReadOnly = true;
      this.newTextBox_Proc_MoldTemp2.Size = new Size(153, 23);
      this.newTextBox_Proc_MoldTemp2.TabIndex = 21;
      this.newTextBox_Proc_MoldTemp2.TabStop = false;
      this.newTextBox_Proc_MoldTemp2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Proc_MoldTemp2.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_Proc_MoldTemp2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Proc_MoldTemp2.Value = "";
      this.newTextBox_Proc_CoolingTime.BackColor = SystemColors.Window;
      this.newTextBox_Proc_CoolingTime.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Proc_CoolingTime.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Proc_CoolingTime.IsDigit = true;
      this.newTextBox_Proc_CoolingTime.Lines = new string[0];
      this.newTextBox_Proc_CoolingTime.Location = new Point(224, 46);
      this.newTextBox_Proc_CoolingTime.MultiLine = false;
      this.newTextBox_Proc_CoolingTime.Name = "newTextBox_Proc_CoolingTime";
      this.newTextBox_Proc_CoolingTime.ReadOnly = false;
      this.newTextBox_Proc_CoolingTime.Size = new Size(228, 23);
      this.newTextBox_Proc_CoolingTime.TabIndex = 45;
      this.newTextBox_Proc_CoolingTime.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Proc_CoolingTime.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Proc_CoolingTime.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Proc_CoolingTime.Value = "";
      this.newTextBox_Proc_MeltTemp2.BackColor = Color.LavenderBlush;
      this.newTextBox_Proc_MeltTemp2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Proc_MeltTemp2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Proc_MeltTemp2.IsDigit = false;
      this.newTextBox_Proc_MeltTemp2.Lines = new string[0];
      this.newTextBox_Proc_MeltTemp2.Location = new Point(299, 68);
      this.newTextBox_Proc_MeltTemp2.MultiLine = false;
      this.newTextBox_Proc_MeltTemp2.Name = "newTextBox_Proc_MeltTemp2";
      this.newTextBox_Proc_MeltTemp2.ReadOnly = true;
      this.newTextBox_Proc_MeltTemp2.Size = new Size(153, 23);
      this.newTextBox_Proc_MeltTemp2.TabIndex = 47;
      this.newTextBox_Proc_MeltTemp2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Proc_MeltTemp2.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_Proc_MeltTemp2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Proc_MeltTemp2.Value = "";
      this.panel4.BorderStyle = BorderStyle.FixedSingle;
      this.panel4.Controls.Add((Control) this.newButton_Proc);
      this.panel4.Controls.Add((Control) this.newButton_Suji);
      this.panel4.Controls.Add((Control) this.newButton_Inj);
      this.panel4.Location = new Point(0, 0);
      this.panel4.Name = "panel4";
      this.panel4.Size = new Size(498, 24);
      this.panel4.TabIndex = 100;
      this.newButton_Proc.ButtonBackColor = Color.White;
      this.newButton_Proc.ButtonText = "프로세스";
      this.newButton_Proc.FlatBorderSize = 1;
      this.newButton_Proc.FlatStyle = FlatStyle.Flat;
      this.newButton_Proc.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Proc.Image = (Image) null;
      this.newButton_Proc.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Proc.Location = new Point(328, -1);
      this.newButton_Proc.Name = "newButton_Proc";
      this.newButton_Proc.Size = new Size(169, 25);
      this.newButton_Proc.TabIndex = 8;
      this.newButton_Proc.TabStop = false;
      this.newButton_Proc.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Proc.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Proc.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_Suji.ButtonBackColor = Color.White;
      this.newButton_Suji.ButtonText = "수지 선택";
      this.newButton_Suji.FlatBorderSize = 1;
      this.newButton_Suji.FlatStyle = FlatStyle.Flat;
      this.newButton_Suji.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Suji.Image = (Image) null;
      this.newButton_Suji.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Suji.Location = new Point(167, -1);
      this.newButton_Suji.Name = "newButton_Suji";
      this.newButton_Suji.Size = new Size(162, 25);
      this.newButton_Suji.TabIndex = 7;
      this.newButton_Suji.TabStop = false;
      this.newButton_Suji.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Suji.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Suji.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_Inj.ButtonBackColor = Color.LightSteelBlue;
      this.newButton_Inj.ButtonText = "사출기 설정";
      this.newButton_Inj.FlatBorderSize = 1;
      this.newButton_Inj.FlatStyle = FlatStyle.Flat;
      this.newButton_Inj.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Inj.Image = (Image) null;
      this.newButton_Inj.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Inj.Location = new Point(-1, -1);
      this.newButton_Inj.Name = "newButton_Inj";
      this.newButton_Inj.Size = new Size(169, 25);
      this.newButton_Inj.TabIndex = 6;
      this.newButton_Inj.TabStop = false;
      this.newButton_Inj.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Inj.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Inj.NewClick += new EventHandler(this.newButton_NewClick);
      this.panel5.BorderStyle = BorderStyle.FixedSingle;
      this.panel5.Controls.Add((Control) this.tabControl_Main);
      this.panel5.Location = new Point(0, 23);
      this.panel5.Name = "panel5";
      this.panel5.Size = new Size(498, 524);
      this.panel5.TabIndex = 100;
      this.newButton_Apply.ButtonBackColor = Color.White;
      this.newButton_Apply.ButtonText = "프로세스 세팅";
      this.newButton_Apply.FlatBorderSize = 1;
      this.newButton_Apply.FlatStyle = FlatStyle.Flat;
      this.newButton_Apply.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Apply.ForeColor = Color.Navy;
      this.newButton_Apply.Image = (Image) Resources.OK;
      this.newButton_Apply.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Apply.Location = new Point(0, 550);
      this.newButton_Apply.Name = "newButton_Apply";
      this.newButton_Apply.Size = new Size(498, 27);
      this.newButton_Apply.TabIndex = 65;
      this.newButton_Apply.TabStop = false;
      this.newButton_Apply.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Apply.NewClick += new EventHandler(this.newButton_NewClick);
      this.newComboBox_Item.BackColor = Color.LavenderBlush;
      this.newComboBox_Item.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Item.ComboBoxBackColor = Color.LavenderBlush;
      this.newComboBox_Item.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Item.isSameSelect = false;
      this.newComboBox_Item.Location = new Point(317, 19);
      this.newComboBox_Item.Name = "newComboBox_Item";
      this.newComboBox_Item.SelectedIndex = -1;
      this.newComboBox_Item.Size = new Size(181, 23);
      this.newComboBox_Item.TabIndex = 2;
      this.newComboBox_Item.TabStop = false;
      this.newComboBox_Item.TextAlign = HorizontalAlignment.Center;
      this.newComboBox_Item.Value = "";
      this.newComboBox_Item.SelectedIndexChanged += new EventHandler(this.newComboBox_Item_SelectedIndexChanged);
      this.newComboBox_Company.BackColor = Color.LavenderBlush;
      this.newComboBox_Company.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Company.ComboBoxBackColor = Color.LavenderBlush;
      this.newComboBox_Company.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Company.isSameSelect = false;
      this.newComboBox_Company.Location = new Point(69, 19);
      this.newComboBox_Company.Name = "newComboBox_Company";
      this.newComboBox_Company.SelectedIndex = -1;
      this.newComboBox_Company.Size = new Size(181, 23);
      this.newComboBox_Company.TabIndex = 1;
      this.newComboBox_Company.TabStop = false;
      this.newComboBox_Company.TextAlign = HorizontalAlignment.Center;
      this.newComboBox_Company.Value = "";
      this.newComboBox_Company.SelectedIndexChanged += new EventHandler(this.newComboBox_Company_SelectedIndexChanged);
      this.newButton_BeforeDB.ButtonBackColor = Color.White;
      this.newButton_BeforeDB.ButtonText = "이전 설정 불러오기";
      this.newButton_BeforeDB.FlatBorderSize = 1;
      this.newButton_BeforeDB.FlatStyle = FlatStyle.Flat;
      this.newButton_BeforeDB.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_BeforeDB.ForeColor = Color.Navy;
      this.newButton_BeforeDB.Image = (Image) Resources.Backup;
      this.newButton_BeforeDB.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_BeforeDB.Location = new Point(5, 199);
      this.newButton_BeforeDB.Name = "newButton_BeforeDB";
      this.newButton_BeforeDB.Size = new Size(498, 27);
      this.newButton_BeforeDB.TabIndex = 4;
      this.newButton_BeforeDB.TabStop = false;
      this.newButton_BeforeDB.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_BeforeDB.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_BeforeDB.NewClick += new EventHandler(this.newButton_NewClick);
      this.panel_Tab.Controls.Add((Control) this.panel4);
      this.panel_Tab.Controls.Add((Control) this.panel5);
      this.panel_Tab.Controls.Add((Control) this.newButton_Apply);
      this.panel_Tab.Location = new Point(5, 225);
      this.panel_Tab.Name = "panel_Tab";
      this.panel_Tab.Size = new Size(498, 577);
      this.panel_Tab.TabIndex = 5;
      this.panel_DB.Controls.Add((Control) this.label_DB);
      this.panel_DB.Controls.Add((Control) this.newComboBox_Company);
      this.panel_DB.Controls.Add((Control) this.listBox_DB);
      this.panel_DB.Controls.Add((Control) this.newComboBox_Item);
      this.panel_DB.Controls.Add((Control) this.label_DB_Company);
      this.panel_DB.Controls.Add((Control) this.label_DB_Item);
      this.panel_DB.Location = new Point(5, 5);
      this.panel_DB.Name = "panel_DB";
      this.panel_DB.Size = new Size(498, 195);
      this.panel_DB.TabIndex = 102;
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(508, 806);
      this.Controls.Add((Control) this.panel_DB);
      this.Controls.Add((Control) this.panel_Tab);
      this.Controls.Add((Control) this.newButton_BeforeDB);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmProcess);
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "프로세스 세팅";
      this.FormClosed += new FormClosedEventHandler(this.frmProcess_FormClosed);
      this.Load += new EventHandler(this.frmProcess_Load);
      this.panel1.ResumeLayout(false);
      ((ISupportInitialize) this.pictureBox_Suji_Image1).EndInit();
      ((ISupportInitialize) this.pictureBox_Suji_Image2).EndInit();
      this.panel3.ResumeLayout(false);
      this.tabControl_Main.ResumeLayout(false);
      this.tabPage1.ResumeLayout(false);
      this.tabPage2.ResumeLayout(false);
      this.tabPage3.ResumeLayout(false);
      this.panel7.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_Proc_FC2).EndInit();
      this.panel9.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_Proc_FC4).EndInit();
      this.panel10.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_Proc_VP).EndInit();
      this.panel8.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_Proc_FC3).EndInit();
      this.panel13.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_Proc_PHC2).EndInit();
      this.panel12.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_Proc_PHC1).EndInit();
      this.panel6.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_Proc_FC1).EndInit();
      this.panel4.ResumeLayout(false);
      this.panel5.ResumeLayout(false);
      this.panel_Tab.ResumeLayout(false);
      this.panel_DB.ResumeLayout(false);
      this.ResumeLayout(false);
    }
  }
}
