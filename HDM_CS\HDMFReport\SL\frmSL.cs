﻿// Decompiled with JetBrains decompiler
// Type: HDMFReport.frmSL
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using HDLocale;
using HDLog4Net;
using HDMFReport.Properties;
using HDMFUserControl;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace HDMFReport
{
  internal class frmSL : frmBase
  {
    private FileInfo m_fiReportUser;
    private FileInfo m_fiReportSystem = new FileInfo(clsReportDefine.g_diCfg.FullName + "\\HDSolutions.ini");
    public DirectoryInfo g_diSLDefault = new DirectoryInfo(Application.StartupPath + "\\Config\\SLReport");
    public DirectoryInfo g_diSL = new DirectoryInfo(clsReportDefine.g_diCfg.FullName + "\\SLReport");
    private DataTable g_dtCoolEfficiency;
    private FileInfo m_fiResult;
    private FileInfo m_fiCoolEfficiency;
    private FileInfo m_fiShirnkage;
    private FileInfo m_fiShrinkComp;
    private FileInfo m_fiThickness;
    private IContainer components;
    private TabControl tabControl_Main;
    private TabPage tabPage_Input;
    private TabPage tabPage_View;
    private NewButton newButton_View;
    private NewButton newButton_Input;
    private Panel panel1;
    private NewComboBox newComboBox_Input_DefZ;
    private NewComboBox newComboBox_Input_DefY;
    private NewComboBox newComboBox_Input_DefX;
    private NewComboBox newComboBox_Input_DefAll;
    private NewComboBox newComboBox_Input_Cooling;
    private NewComboBox newComboBox_Input_SinkMark;
    private NewComboBox newComboBox_Input_WeldLine;
    private Label label28;
    private Label label32;
    private Label label26;
    private Label label27;
    private NewTextBox newTextBox_Input_Manager;
    private NewTextBox newTextBox_Input_Countermeasure;
    private NewTextBox newTextBox_Input_DefZ;
    private NewTextBox newTextBox_Input_DefY;
    private NewTextBox newTextBox_Input_DefX;
    private NewTextBox newTextBox_Input_DefAll;
    private NewTextBox newTextBox_Input_Cooling;
    private NewTextBox newTextBox_Input_SinkMark;
    private NewTextBox newTextBox_Input_WeldLine;
    private NewTextBox newTextBox_Input_Item;
    private Label label10;
    private Label label11;
    private Label label38;
    private Label label43;
    private Label label_Input_TVTMTemp;
    private Label label_Input_Filling4;
    private Label label_Input_TVTFTemp;
    private Label label_Input_Filling3;
    private Label label_TVTTime;
    private Label label_Input_Filling2;
    private Label label_Input_TVTVPercentage;
    private Label label_Input_Filling1;
    private Label label67;
    private Label label66;
    private Label label64;
    private Label label60;
    private Label label14;
    private UnitTextBox unitTextBox_Input_Filling4;
    private UnitTextBox unitTextBox_Input_Filling3;
    private UnitTextBox unitTextBox_Input_Filling2;
    private UnitTextBox unitTextBox_Input_Filling1;
    private Label label_Input_Deflection;
    private Label label48;
    private Label label37;
    private Label label_Input_Result_Comment;
    private Label label_Input_Sign;
    private NewButton newButton_View_Select;
    private NewButton newButton_View_All;
    private NewTextBox newTextBox_View_Z;
    private NewTextBox newTextBox_View_Y;
    private NewTextBox newTextBox_View_X;
    private NewComboBox newComboBox_View_Type;
    private Label label2;
    private Label label3;
    private Label label4;
    private Label label5;
    private Label label_View_Model;
    private DataGridView dataGridView_View;
    private NewTextBox newTextBox_Input_FillingFrame;
    private NewButton newButton_Apply;
    private CheckBox checkBox_Input_InjCond;
    private Label label61;
    private DataGridViewTextBoxColumn Column6;
    private DataGridViewCheckBoxColumn Column5;
    private DataGridViewTextBoxColumn Column_Item;
    private DataGridViewTextBoxColumn Column2;
    private DataGridViewTextBoxColumn Column3;
    private DataGridViewTextBoxColumn Column4;
    private Label label_Input_InjRangeVP;
    private Label label_Input_InjRange4;
    private Label label_Input_InjRange3;
    private Label label_Input_InjRange2;
    private Label label_Input_InjRange1;
    private UnitTextBox unitTextBox_Input_InjPreRatio;
    private UnitTextBox unitTextBox_Input_InjMaxClamp;
    private UnitTextBox unitTextBox_Input_InjMaxPressure;
    private UnitTextBox unitTextBox_Input_InjRangeVP;
    private UnitTextBox unitTextBox_Input_InjRange4;
    private UnitTextBox unitTextBox_Input_InjRange3;
    private UnitTextBox unitTextBox_Input_InjScrewDia;
    private UnitTextBox unitTextBox_Input_InjRange2;
    private UnitTextBox unitTextBox_Input_InjMaxRate;
    private UnitTextBox unitTextBox_Input_InjRange1;
    private UnitTextBox unitTextBox_Input_InjMaxStroke;
    private Label label_Input_InjPreRatio;
    private Label label_Input_InjScrewDia;
    private Label label_Input_InjMaxClamp;
    private Label label_Input_InjMaxRate;
    private Label label_Input_InjMaxPressure;
    private Label label_Input_InjMaxStroke;
    private NewComboBox newComboBox_Input_InjData;
    private Label label_Input_Range;
    private Label label_Input_Inj;
    private NewComboBox newComboBox_Input_AirTrap;
    private Label label6;
    private NewTextBox newTextBox_Input_AirTrap;
    private NewTextBox newTextBox_Input_Engineer;
    private Label label13;
    private CheckBox checkBox_AllCheck;
    private Label label7;
    private UnitTextBox unitTextBox_Input_InjPressure;
    private Label label12;
    private Label label9;
    private UnitTextBox unitTextBox_Input_InCavityPressure;
    private Label label15;
    private UnitTextBox unitTextBox_Input_NozzleGateSize;
    private UnitTextBox unitTextBox_Input_ValvePinSize;
    private UnitTextBox unitTextBox_Input_NozzleSize;
    private UnitTextBox unitTextBox_Input_ManifoldSize;
    private Label label19;
    private Label label18;
    private Label label17;
    private Label label16;
    private UnitTextBox unitTextBox_Input_HeatDiameter;
    private UnitTextBox unitTextBox_Input_ManifoldVolume;
    private Label label20;
    private Label label21;
    private Label label22;
    private UnitTextBox unitTextBox_Input_TVTFTemp;
    private UnitTextBox unitTextBox_Input_TVTMTemp;
    private UnitTextBox unitTextBox_Input_TVTTime;
    private UnitTextBox unitTextBox_Input_TVTVPercentage;
    private NewTextBox newTextBox_Input_HeatpipeEff;
    private NewTextBox newTextBox_Input_CEGoal;
    private Label label_Target;
    private NewTextBox newTextBox_Input_CETemp2;
    private Label label90;
    private NewTextBox newTextBox_Input_CETemp1;
    private Label label29;
    private NewTextBox newTextBox_Input_SCVal;
    private Label label_Input_SCVal;
    private NewComboBox newComboBox_Input_SCOpt;
    private Label label_Cooling_Efficiency;
    private Label label_Deflection_Rate;
    private Label label_Option;
    private TabPage tabPage_Set;
    private Label label_Report;
    private NewButton newButton_Set;
    private Label label39;
    private Label label40;
    private Label label41;
    private Label label42;
    private Label label44;
    private Label label45;
    private Label label46;
    private Label label47;
    private Label label49;
    private Label label50;
    private Label label51;
    private Label label52;
    private Label label53;
    private Label label54;
    private Label label55;
    private Label label_Suji;
    private Label label_Mold_Target_Temp;
    private Label label_Cooling_Efficiency_Setting;
    private UnitTextBox unitTextBox_CoolantTempCavity;
    private UnitTextBox unitTextBox_ReynoldsNumber;
    private UnitTextBox unitTextBox_FlowRate;
    private UnitTextBox unitTextBox_CoolantTempCore;
    private UnitTextBox unitTextBox_InCavityPressure1;
    private UnitTextBox unitTextBox_InCavityPressure2;
    private UnitTextBox unitTextBox_InjPressure2;
    private Label label_Result;
    private UnitTextBox unitTextBox_InjPressure1;
    private Label label1;
    private Label label23;
    private Label label24;
    private Label label_InjPressure1;
    private Label label30;
    private Label label31;
    private Label label33;
    private Label label34;
    private NewTextBox newTextBox_RLENS_GTemp_PMMA;
    private NewTextBox newTextBox_RBEZEL_GTemp_PC;
    private NewTextBox newTextBox_RHSG_GTemp_PCABS;
    private NewTextBox newTextBox_FBEZEL_GTemp_PC;
    private NewTextBox newTextBox_FBEZEL_GTemp_PBT;
    private NewTextBox newTextBox_FHSG_GTemp_PP;
    private NewTextBox newTextBox_FLENS_GTemp_PC;
    private NewTextBox newTextBox_RLENS_Goal_PMMA;
    private NewTextBox newTextBox_RBEZEL_Goal_PC;
    private NewTextBox newTextBox_RHSG_Goal_PCABS;
    private NewTextBox newTextBox_FBEZEL_Goal_PC;
    private NewTextBox newTextBox_FBEZEL_Goal_PBT;
    private NewTextBox newTextBox_FHSG_Goal_PP;
    private NewTextBox newTextBox_FLENS_Goal_PC;
    private NewTextBox newTextBox_PA66;
    private Label label70;
    private NewTextBox newTextBox_PCABS;
    private Label label69;
    private NewTextBox newTextBox_PMMA;
    private Label label68;
    private NewTextBox newTextBox_PP;
    private Label label65;
    private NewTextBox newTextBox_ABS;
    private Label label63;
    private NewTextBox newTextBox_PBT;
    private Label label62;
    private NewTextBox newTextBox_PC;
    private Label label59;
    private Label label58;
    private Label label_FamilyAbb;
    private Label label_ShrinkSet;
    private Label label_ShrinkComp;
    private DataGridView dataGridView_ShrinkComp;
    private UnitTextBox unitTextBox_ThicknessMax;
    private Label label_Thickness;
    private UnitTextBox unitTextBox_ThicknessMin;
    private Label label73;
    private Label label74;
    private DataGridViewTextBoxColumn Column1;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn1;
    private DataGridViewTextBoxColumn Column7;
    private NewTextBox newTextBox_Input_Step;

    protected override CreateParams CreateParams
    {
      get
      {
        CreateParams createParams = base.CreateParams;
        createParams.ExStyle |= 33554432;
        return createParams;
      }
    }

    public frmSL()
    {
      this.InitializeComponent();
      this.newButton_Apply.Image = clsReportUtill.ChangeSyncBackgroundImage(this.newButton_Apply.Image);
      this.label_Input_Inj.Text = LocaleControl.getInstance().GetString("IDS_INJ_INFO");
      this.label_Input_InjMaxStroke.Text = LocaleControl.getInstance().GetString("IDS_INJ_MAXSTROKE");
      this.label_Input_InjMaxRate.Text = LocaleControl.getInstance().GetString("IDS_INJ_MAXRATE");
      this.label_Input_InjScrewDia.Text = LocaleControl.getInstance().GetString("IDS_INJ_SCREWDIA");
      this.label_Input_InjMaxPressure.Text = LocaleControl.getInstance().GetString("IDS_INJ_MAXPRESSURE");
      this.label_Input_InjMaxClamp.Text = LocaleControl.getInstance().GetString("IDS_INJ_MAXCLAMP");
      this.label_Input_InjPreRatio.Text = LocaleControl.getInstance().GetString("IDS_INJ_PRERATIO");
      this.label_Input_Range.Text = LocaleControl.getInstance().GetString("IDS_INJ_RANGE");
      this.label_Input_InjRange1.Text = "1" + LocaleControl.getInstance().GetString("IDS_STEP");
      this.label_Input_InjRange2.Text = "2" + LocaleControl.getInstance().GetString("IDS_STEP");
      this.label_Input_InjRange3.Text = "3" + LocaleControl.getInstance().GetString("IDS_STEP");
      this.label_Input_InjRange4.Text = "4" + LocaleControl.getInstance().GetString("IDS_STEP") + "(" + LocaleControl.getInstance().GetString("IDS_CHANGE_VP") + ")";
      this.label_Input_InjRangeVP.Text = LocaleControl.getInstance().GetString("IDS_VP_CUSHION") + "(Cushion)";
      this.label_Report.Text = LocaleControl.getInstance().GetString("IDS_REPORT");
      this.label_Input_Sign.Text = LocaleControl.getInstance().GetString("IDS_SIGN");
      this.label_Input_Result_Comment.Text = LocaleControl.getInstance().GetString("IDS_RESULT_COMMENT");
      this.label_Input_Filling1.Text = "1" + LocaleControl.getInstance().GetString("IDS_PAGE");
      this.label_Input_Filling2.Text = "2" + LocaleControl.getInstance().GetString("IDS_PAGE");
      this.label_Input_Filling3.Text = "3" + LocaleControl.getInstance().GetString("IDS_PAGE");
      this.label_Input_Filling4.Text = "4" + LocaleControl.getInstance().GetString("IDS_PAGE");
      this.label_TVTTime.Text = LocaleControl.getInstance().GetString("IDS_TIME");
      this.label_Deflection_Rate.Text = LocaleControl.getInstance().GetString("IDS_DEFLECTION_RATE");
      this.label_Option.Text = LocaleControl.getInstance().GetString("IDS_OPTION");
      this.label_Cooling_Efficiency.Text = LocaleControl.getInstance().GetString("IDS_COOLING_EFFICIENCY");
      this.label_Mold_Target_Temp.Text = LocaleControl.getInstance().GetString("IDS_MOLD_TARGET_TEMP");
      this.label_Target.Text = LocaleControl.getInstance().GetString("IDS_TARGET") + "(%)";
      this.label_Input_Deflection.Text = LocaleControl.getInstance().GetString("IDS_WARP");
      this.checkBox_Input_InjCond.Text = LocaleControl.getInstance().GetString("IDS_INJ_CONDITION");
      this.newButton_Input.ButtonText = LocaleControl.getInstance().GetString("IDS_INPUT_VALUE");
      this.newButton_View.ButtonText = LocaleControl.getInstance().GetString("IDS_VIEW_SETTING");
      this.label_View_Model.Text = LocaleControl.getInstance().GetString("IDS_MODEL_VIEW_ROTATE_ANGLE");
      this.newButton_View_All.ButtonText = LocaleControl.getInstance().GetString("IDS_APPLY_ALL");
      this.newButton_View_Select.ButtonText = LocaleControl.getInstance().GetString("IDS_APPLY_SELECT");
      this.Column_Item.HeaderText = LocaleControl.getInstance().GetString("IDS_ITEM");
      this.label_Result.Text = LocaleControl.getInstance().GetString("IDS_RESULT_SETTINGS");
      this.label_Cooling_Efficiency_Setting.Text = LocaleControl.getInstance().GetString("IDS_COOLING_EFFICIENCY_SETTINGS");
      this.label_Suji.Text = LocaleControl.getInstance().GetString("IDS_SUJI");
      this.label_Mold_Target_Temp.Text = LocaleControl.getInstance().GetString("IDS_MOLD_TARGET_TEMP");
      this.label_Target.Text = LocaleControl.getInstance().GetString("IDS_TARGET") + "(%)";
      this.label_ShrinkSet.Text = LocaleControl.getInstance().GetString("IDS_SHRINKAGE_SETTING");
      this.label_FamilyAbb.Text = LocaleControl.getInstance().GetString("IDS_MATERIAL");
      this.label_ShrinkComp.Text = LocaleControl.getInstance().GetString("IDS_SHRINKAGE_COMPENSATION");
      this.dataGridView_ShrinkComp.Columns[1].HeaderText = LocaleControl.getInstance().GetString("IDS_VALUE");
      this.label_Thickness.Text = LocaleControl.getInstance().GetString("IDS_THICKNESS_SETTINGS");
    }

    private void frmHDSolutions_Load(object sender, EventArgs e)
    {
      this.Text = LocaleControl.getInstance().GetString("IDS_WRITE_REPORT") + " [" + LocaleControl.getInstance().GetString("IDS_STUDY") + ": " + this.m_drStudy["Name"].ToString() + "]";
      this.m_fiReportSystem = new FileInfo(clsReportDefine.g_diCfg.FullName + "\\" + this.m_enumCompany.ToString() + ".ini");
      this.LoadSLData();
      this.LoadCoolEfficiencyData();
      this.SetDefault();
      this.SetLoad();
      if (clsReportDefine.enumLicLevel != clsReportDefine.LicLevel.Standard)
        return;
      this.checkBox_Input_InjCond.Checked = false;
      this.newComboBox_Input_InjData.Enabled = false;
      this.unitTextBox_Input_InjMaxClamp.Enabled = false;
      this.unitTextBox_Input_InjMaxPressure.Enabled = false;
      this.unitTextBox_Input_InjMaxRate.Enabled = false;
      this.unitTextBox_Input_InjMaxStroke.Enabled = false;
      this.unitTextBox_Input_InjPreRatio.Enabled = false;
      this.unitTextBox_Input_InjRange1.Enabled = false;
      this.unitTextBox_Input_InjRange2.Enabled = false;
      this.unitTextBox_Input_InjRange3.Enabled = false;
      this.unitTextBox_Input_InjRange4.Enabled = false;
      this.unitTextBox_Input_InjRangeVP.Enabled = false;
      this.unitTextBox_Input_InjScrewDia.Enabled = false;
    }

    private void LoadSLData()
    {
      try
      {
        if (!this.g_diSL.Exists || this.g_diSL.GetFiles().Length == 0)
        {
          this.g_diSLDefault.Refresh();
          if (this.g_diSLDefault.Exists && this.g_diSLDefault.GetFiles().Length != 0)
            clsReportUtill.CopyFolder(this.g_diSLDefault.FullName, this.g_diSL.FullName);
        }
        this.m_fiResult = new FileInfo(this.g_diSL.ToString() + "\\ResultConfig.ini");
        this.m_fiCoolEfficiency = new FileInfo(this.g_diSL.ToString() + "\\CoolEfficiencyConfig.ini");
        this.m_fiShirnkage = new FileInfo(this.g_diSL.ToString() + "\\ShrinkageConfig.ini");
        this.m_fiShrinkComp = new FileInfo(this.g_diSL.ToString() + "\\ShrinkCompConfig.ini");
        this.m_fiThickness = new FileInfo(this.g_diSL.ToString() + "\\ThicknessConfig.ini");
        this.m_fiResult = new FileInfo(this.g_diSL.ToString() + "\\ResultConfig.ini");
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsData]LoadTemplate):" + ex.Message));
      }
    }

    private void LoadCoolEfficiencyData()
    {
      this.g_dtCoolEfficiency = new DataTable();
      this.g_dtCoolEfficiency.Columns.Add("PMV");
      this.g_dtCoolEfficiency.Columns.Add("FR");
      this.g_dtCoolEfficiency.Columns.Add("ITEM");
      this.g_dtCoolEfficiency.Columns.Add("FA");
      this.g_dtCoolEfficiency.Columns.Add("GTEMP");
      this.g_dtCoolEfficiency.Columns.Add("GOAL");
      try
      {
        if (!this.m_fiCoolEfficiency.Exists)
          return;
        string[] strArray1 = clsReportUtill.ReadINI("FRONT_LENS", "PC", this.m_fiCoolEfficiency.FullName).Split('|');
        DataRow dataRow1 = this.g_dtCoolEfficiency.Rows.Add();
        dataRow1["PMV"] = (object) strArray1[0];
        dataRow1["FR"] = (object) "FRONT";
        dataRow1["ITEM"] = (object) "FRONT-LENS";
        dataRow1["FA"] = (object) "PC";
        dataRow1["GTEMP"] = (object) strArray1[1];
        dataRow1["GOAL"] = (object) strArray1[2];
        string[] strArray2 = clsReportUtill.ReadINI("FRONT_BEZEL", "PC", this.m_fiCoolEfficiency.FullName).Split('|');
        DataRow dataRow2 = this.g_dtCoolEfficiency.Rows.Add();
        dataRow2["PMV"] = (object) strArray2[0];
        dataRow2["FR"] = (object) "FRONT";
        dataRow2["ITEM"] = (object) "FRONT-BEZEL";
        dataRow2["FA"] = (object) "PC";
        dataRow2["GTEMP"] = (object) strArray2[1];
        dataRow2["GOAL"] = (object) strArray2[2];
        string[] strArray3 = clsReportUtill.ReadINI("FRONT_BEZEL", "PBT", this.m_fiCoolEfficiency.FullName).Split('|');
        DataRow dataRow3 = this.g_dtCoolEfficiency.Rows.Add();
        dataRow3["PMV"] = (object) strArray3[0];
        dataRow3["FR"] = (object) "FRONT";
        dataRow3["ITEM"] = (object) "FRONT-BEZEL";
        dataRow3["FA"] = (object) "PBT";
        dataRow3["GTEMP"] = (object) strArray3[1];
        dataRow3["GOAL"] = (object) strArray3[2];
        string[] strArray4 = clsReportUtill.ReadINI("FRONT_HSG", "PP", this.m_fiCoolEfficiency.FullName).Split('|');
        DataRow dataRow4 = this.g_dtCoolEfficiency.Rows.Add();
        dataRow4["PMV"] = (object) strArray4[0];
        dataRow4["FR"] = (object) "FRONT";
        dataRow4["ITEM"] = (object) "FRONT-HSG";
        dataRow4["FA"] = (object) "PP";
        dataRow4["GTEMP"] = (object) strArray4[1];
        dataRow4["GOAL"] = (object) strArray4[2];
        string[] strArray5 = clsReportUtill.ReadINI("REAR_LENS", "PMMA", this.m_fiCoolEfficiency.FullName).Split('|');
        DataRow dataRow5 = this.g_dtCoolEfficiency.Rows.Add();
        dataRow5["PMV"] = (object) strArray5[0];
        dataRow5["FR"] = (object) "REAR";
        dataRow5["ITEM"] = (object) "REAR-LENS";
        dataRow5["FA"] = (object) "PMMA";
        dataRow5["GTEMP"] = (object) strArray5[1];
        dataRow5["GOAL"] = (object) strArray5[2];
        string[] strArray6 = clsReportUtill.ReadINI("REAR_BEZEL", "PC", this.m_fiCoolEfficiency.FullName).Split('|');
        DataRow dataRow6 = this.g_dtCoolEfficiency.Rows.Add();
        dataRow6["PMV"] = (object) strArray6[0];
        dataRow6["FR"] = (object) "REAR";
        dataRow6["ITEM"] = (object) "REAR-BEZEL";
        dataRow6["FA"] = (object) "PC";
        dataRow6["GTEMP"] = (object) strArray6[1];
        dataRow6["GOAL"] = (object) strArray6[2];
        string[] strArray7 = clsReportUtill.ReadINI("REAR_HSG", "PC/ABS", this.m_fiCoolEfficiency.FullName).Split('|');
        DataRow dataRow7 = this.g_dtCoolEfficiency.Rows.Add();
        dataRow7["PMV"] = (object) strArray7[0];
        dataRow7["FR"] = (object) "REAR";
        dataRow7["ITEM"] = (object) "REAR-HSG";
        dataRow7["FA"] = (object) "PC|ABS";
        dataRow7["GTEMP"] = (object) strArray7[1];
        dataRow7["GOAL"] = (object) strArray7[2];
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmHDMoldFlow]LoadShrinkageData):" + ex.Message));
      }
    }

    private void SetDefault()
    {
      bool flag = false;
      string str1 = string.Empty;
      try
      {
        if (clsReportDefine.enumLicLevel < clsReportDefine.LicLevel.Premium)
          this.checkBox_Input_InjCond.Enabled = false;
        if (this.m_drStudy["Sequence"].ToString().Contains("Cool"))
          flag = true;
        string str2 = clsReportUtill.ReadINI("Value", "InjPressure1", this.m_fiResult.FullName);
        if (str2 == "")
          str2 = "104";
        this.unitTextBox_InjPressure1.Value = str2;
        string str3 = clsReportUtill.ReadINI("Value", "InjPressure2", this.m_fiResult.FullName);
        if (str3 == "")
          str3 = "95";
        this.unitTextBox_InjPressure2.Value = str3;
        string str4 = clsReportUtill.ReadINI("Value", "InCavityPressure1", this.m_fiResult.FullName);
        if (str4 == "")
          str4 = "80";
        this.unitTextBox_InCavityPressure1.Value = str4;
        string str5 = clsReportUtill.ReadINI("Value", "InCavityPressure2", this.m_fiResult.FullName);
        if (str5 == "")
          str5 = "70";
        this.unitTextBox_InCavityPressure2.Value = str5;
        string str6 = clsReportUtill.ReadINI("Value", "CircuitReynoldsNumber", this.m_fiResult.FullName);
        if (str6 == "")
          str6 = "10000";
        this.unitTextBox_ReynoldsNumber.Value = str6;
        string str7 = clsReportUtill.ReadINI("Value", "CircuitCoolantTempCore", this.m_fiResult.FullName);
        if (str7 == "")
          str7 = "3";
        this.unitTextBox_CoolantTempCore.Value = str7;
        string str8 = clsReportUtill.ReadINI("Value", "CircuitCoolantTempCavity", this.m_fiResult.FullName);
        if (str8 == "")
          str8 = "3";
        this.unitTextBox_CoolantTempCavity.Value = str8;
        string str9 = clsReportUtill.ReadINI("Value", "CircuitFlowRate", this.m_fiResult.FullName);
        if (str9 == "")
          str9 = "5";
        this.unitTextBox_FlowRate.Value = str9;
        string str10 = clsReportUtill.ReadINI("FRONT_LENS", "PC", this.m_fiCoolEfficiency.FullName);
        if (str10 != "")
        {
          string[] strArray = str10.Split('|');
          this.newTextBox_FLENS_GTemp_PC.Value = strArray[1];
          this.newTextBox_FLENS_Goal_PC.Value = strArray[2];
        }
        string str11 = clsReportUtill.ReadINI("FRONT_BEZEL", "PC", this.m_fiCoolEfficiency.FullName);
        if (str11 != "")
        {
          string[] strArray = str11.Split('|');
          this.newTextBox_FBEZEL_GTemp_PC.Value = strArray[1];
          this.newTextBox_FBEZEL_Goal_PC.Value = strArray[2];
        }
        string str12 = clsReportUtill.ReadINI("FRONT_BEZEL", "PBT", this.m_fiCoolEfficiency.FullName);
        if (str12 != "")
        {
          string[] strArray = str12.Split('|');
          this.newTextBox_FBEZEL_GTemp_PBT.Value = strArray[1];
          this.newTextBox_FBEZEL_Goal_PBT.Value = strArray[2];
        }
        string str13 = clsReportUtill.ReadINI("FRONT_HSG", "PP", this.m_fiCoolEfficiency.FullName);
        if (str13 != "")
        {
          string[] strArray = str13.Split('|');
          this.newTextBox_FHSG_GTemp_PP.Value = strArray[1];
          this.newTextBox_FHSG_Goal_PP.Value = strArray[2];
        }
        string str14 = clsReportUtill.ReadINI("REAR_LENS", "PMMA", this.m_fiCoolEfficiency.FullName);
        if (str14 != "")
        {
          string[] strArray = str14.Split('|');
          this.newTextBox_RLENS_GTemp_PMMA.Value = strArray[1];
          this.newTextBox_RLENS_Goal_PMMA.Value = strArray[2];
        }
        string str15 = clsReportUtill.ReadINI("REAR_BEZEL", "PC", this.m_fiCoolEfficiency.FullName);
        if (str15 != "")
        {
          string[] strArray = str15.Split('|');
          this.newTextBox_RBEZEL_GTemp_PC.Value = strArray[1];
          this.newTextBox_RBEZEL_Goal_PC.Value = strArray[2];
        }
        string str16 = clsReportUtill.ReadINI("REAR_HSG", "PC/ABS", this.m_fiCoolEfficiency.FullName);
        if (str16 != "")
        {
          string[] strArray = str16.Split('|');
          this.newTextBox_RHSG_GTemp_PCABS.Value = strArray[1];
          this.newTextBox_RHSG_Goal_PCABS.Value = strArray[2];
        }
        string str17 = clsReportUtill.ReadINI("Value", "PC", this.m_fiShirnkage.FullName);
        if (str17 == "")
          str17 = "8.5";
        this.newTextBox_PC.Value = str17;
        string str18 = clsReportUtill.ReadINI("Value", "PC", this.m_fiShirnkage.FullName);
        if (str18 == "")
          str18 = "8.5";
        this.newTextBox_PC.Value = str18;
        string str19 = clsReportUtill.ReadINI("Value", "PBT", this.m_fiShirnkage.FullName);
        if (str19 == "")
          str19 = "15.5";
        this.newTextBox_PBT.Value = str19;
        string str20 = clsReportUtill.ReadINI("Value", "ABS", this.m_fiShirnkage.FullName);
        if (str20 == "")
          str20 = "8";
        this.newTextBox_ABS.Value = str20;
        string str21 = clsReportUtill.ReadINI("Value", "PP", this.m_fiShirnkage.FullName);
        if (str21 == "")
          str21 = "15.5";
        this.newTextBox_PP.Value = str21;
        string str22 = clsReportUtill.ReadINI("Value", "PMMA", this.m_fiShirnkage.FullName);
        if (str22 == "")
          str22 = "7.5";
        this.newTextBox_PMMA.Value = str22;
        string str23 = clsReportUtill.ReadINI("Value", "PCABS", this.m_fiShirnkage.FullName);
        if (str23 == "")
          str23 = "8.5";
        this.newTextBox_PCABS.Value = str23;
        string str24 = clsReportUtill.ReadINI("Value", "PA66", this.m_fiShirnkage.FullName);
        if (str24 == "")
          str24 = "9";
        this.newTextBox_PA66.Value = str24;
        foreach (string iniAllKey in clsReportUtill.GetINIAllKeys(this.m_fiShrinkComp.FullName, "Shrinkage Compensation"))
        {
          string str25 = clsReportUtill.ReadINI("Shrinkage Compensation", iniAllKey, this.m_fiShrinkComp.FullName);
          if (str25 != string.Empty)
          {
            DataGridViewRow row = this.dataGridView_ShrinkComp.Rows[this.dataGridView_ShrinkComp.Rows.Add()];
            row.Cells[0].Value = (object) iniAllKey;
            row.Cells[1].Value = (object) str25;
            row.Cells[2].Value = (object) string.Empty;
            string[] strArray = iniAllKey.Split('+');
            if (strArray.Length > 1)
            {
              if (strArray[1].Contains("TD"))
                row.Cells[2].Value = (object) (strArray[1].Replace("TD", "") + "% Talc Filled");
              else if (strArray[1].Contains("GF"))
                row.Cells[2].Value = (object) (strArray[1].Replace("GF", "") + "% Glass Fiber Filled");
            }
          }
        }
        string str26 = clsReportUtill.ReadINI("Thickness", "Min", this.m_fiThickness.FullName);
        if (str26 == "")
          str26 = "0";
        this.unitTextBox_ThicknessMin.Value = str26;
        string str27 = clsReportUtill.ReadINI("Thickness", "Max", this.m_fiThickness.FullName);
        if (str27 == "")
          str27 = "4";
        this.unitTextBox_ThicknessMax.Value = str27;
        this.checkBox_Input_InjCond.Checked = false;
        this.newComboBox_Input_InjData.Items.Add((object) LocaleControl.getInstance().GetString("IDS_STUDY"));
        foreach (DataRow dataRow in clsReportDefine.g_dtInjectionDB.AsEnumerable())
          this.newComboBox_Input_InjData.Items.Add((object) dataRow["FileName"].ToString());
        this.newComboBox_Input_InjData.SelectedIndex = 0;
        this.unitTextBox_Input_InjRange1.Value = "5";
        this.unitTextBox_Input_InjRange2.Value = "15";
        this.unitTextBox_Input_InjRange3.Value = "65";
        this.unitTextBox_Input_InjRange4.Value = "97";
        this.unitTextBox_Input_InjRangeVP.Value = "7";
        str1 = this.m_drStudy.Table.TableName;
        string[] strArray1 = this.m_drStudy["Name"].ToString().Split('_');
        this.newTextBox_Input_Item.Value = strArray1[0] + " " + strArray1[1].Replace("-", " ") + " " + strArray1[2];
        this.newTextBox_Input_Engineer.Value = string.Empty;
        this.newTextBox_Input_Manager.Value = string.Empty;
        this.unitTextBox_Input_InjPressure.Value = this.unitTextBox_InjPressure1.Value;
        this.unitTextBox_Input_InCavityPressure.Value = this.unitTextBox_InCavityPressure1.Value;
        this.newComboBox_Input_WeldLine.Items.AddRange((object[]) new string[4]
        {
          "OK",
          "DISCUSSION",
          "DISCUSSION(Visible)",
          "DISCUSSION(Visible)-R"
        });
        this.newComboBox_Input_WeldLine.SelectedIndex = 0;
        this.newComboBox_Input_AirTrap.Items.AddRange((object[]) new string[3]
        {
          "OK",
          "DISCUSSION(Gas vent)",
          "NG"
        });
        this.newComboBox_Input_AirTrap.SelectedIndex = 1;
        this.newComboBox_Input_SinkMark.Items.AddRange((object[]) new string[4]
        {
          "OK",
          "DISCUSSION",
          "DISCUSSION(Visible)",
          "DISCUSSION(Visible)-R"
        });
        this.newComboBox_Input_SinkMark.SelectedIndex = 1;
        this.newComboBox_Input_Cooling.Items.AddRange((object[]) new string[3]
        {
          "OK",
          "DISCUSSION",
          "NG"
        });
        this.newComboBox_Input_Cooling.SelectedIndex = 1;
        if (!flag)
        {
          this.newComboBox_Input_Cooling.Enabled = false;
          this.newTextBox_Input_Cooling.Enabled = false;
        }
        this.newTextBox_Input_Countermeasure.Value = "To improve air trap, you should make a flow leader and increase thickness. + 0.5mm" + Environment.NewLine + "Almost all areas are cooled in 24 seconds" + Environment.NewLine + "Use of Mold inserts to improve deformation area";
        this.unitTextBox_Input_Filling1.Value = "25";
        this.unitTextBox_Input_Filling2.Value = "50";
        this.unitTextBox_Input_Filling3.Value = "80";
        this.unitTextBox_Input_Filling4.Value = "95";
        this.newTextBox_Input_FillingFrame.Value = "150";
        this.unitTextBox_Input_ManifoldSize.Value = "16";
        this.unitTextBox_Input_NozzleSize.Value = "16";
        this.unitTextBox_Input_ValvePinSize.Value = "5";
        this.unitTextBox_Input_NozzleGateSize.Value = "4";
        this.unitTextBox_Input_ManifoldVolume.Value = !this.m_dicReport.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ManifoldVolume")) ? "1.2" : this.m_dicReport["ManifoldVolume"];
        this.unitTextBox_Input_TVTVPercentage.Value = "0";
        if (this.m_dicReport.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VP")))
          this.unitTextBox_Input_TVTVPercentage.Value = this.m_dicReport["VP"];
        this.unitTextBox_Input_TVTTime.Value = "0";
        if (this.m_dicReport.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperature_Time")))
          this.unitTextBox_Input_TVTTime.Value = this.m_dicReport["TimeToReachEjectionTemperature_Time"];
        this.unitTextBox_Input_TVTFTemp.Value = "0";
        if (this.m_dicReport.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperature_Temp")))
          this.unitTextBox_Input_TVTFTemp.Value = this.m_dicReport["TimeToReachEjectionTemperature_Temp"];
        this.unitTextBox_Input_TVTMTemp.Value = "0";
        if (this.m_dicReport.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MoldTemp")))
          this.unitTextBox_Input_TVTMTemp.Value = this.m_dicReport["MoldTemp"];
        this.newComboBox_Input_DefAll.Items.AddRange((object[]) new string[2]
        {
          "Anchor",
          "Best fit"
        });
        this.newComboBox_Input_DefAll.SelectedIndex = 1;
        this.newComboBox_Input_DefX.Items.AddRange((object[]) new string[2]
        {
          "Anchor",
          "Best fit"
        });
        this.newComboBox_Input_DefX.SelectedIndex = 1;
        this.newComboBox_Input_DefY.Items.AddRange((object[]) new string[2]
        {
          "Anchor",
          "Best fit"
        });
        this.newComboBox_Input_DefY.SelectedIndex = 1;
        this.newComboBox_Input_DefZ.Items.AddRange((object[]) new string[2]
        {
          "Anchor",
          "Best fit"
        });
        this.newComboBox_Input_DefZ.SelectedIndex = 1;
        this.label_Input_SCVal.Text = this.m_dicReport["FamilyAbbreviation"];
        if (this.m_drStudy["Sequence"].ToString().Contains("Cool"))
        {
          this.newComboBox_Input_SCOpt.Enabled = false;
          this.newComboBox_Input_SCOpt.BackColor = Color.LightGray;
          this.newComboBox_Input_SCOpt.ComboBoxBackColor = Color.LightGray;
          this.newTextBox_Input_SCVal.Enabled = false;
          this.newTextBox_Input_SCVal.BackColor = Color.LightGray;
          this.newTextBox_Input_SCVal.TextBoxBackColor = Color.LightGray;
        }
        else
        {
          this.newComboBox_Input_SCOpt.Items.AddRange((object[]) new string[3]
          {
            "None",
            "Automatic",
            "Isotropic"
          });
          this.newComboBox_Input_SCOpt.SelectedIndex = 2;
          string str28 = this.m_dicReport["FamilyAbbreviation"];
          string str29 = this.m_dicReport["Fibers/Fillers"];
          string str30 = string.Empty;
          foreach (DataGridViewRow row in (IEnumerable) this.dataGridView_ShrinkComp.Rows)
          {
            string source = row.Cells[0].Value.ToString();
            if (source.Contains<char>('+'))
            {
              string[] strArray2 = source.Split('+');
              if (source.Contains("TF") || source.Contains("GF"))
              {
                if (strArray2[0] != str28)
                  continue;
              }
              else if (!str28.Contains(strArray2[0]) || !str28.Contains(strArray2[1]))
                continue;
            }
            else if (row.Cells[0].Value.ToString() != str28)
              continue;
            if (!(row.Cells[2].Value.ToString() != str29))
            {
              str30 = row.Cells[1].Value.ToString();
              break;
            }
          }
          if (str30 == string.Empty)
          {
            this.newComboBox_Input_SCOpt.SelectedIndex = 0;
            str30 = "-";
          }
          this.newTextBox_Input_SCVal.Value = str30;
        }
        if (!flag)
        {
          this.newTextBox_Input_CETemp1.Enabled = false;
          this.newTextBox_Input_CETemp1.BackColor = Color.LightGray;
          this.newTextBox_Input_CETemp1.TextBoxBackColor = Color.LightGray;
          this.newTextBox_Input_CETemp2.Enabled = false;
          this.newTextBox_Input_CETemp2.BackColor = Color.LightGray;
          this.newTextBox_Input_CETemp2.TextBoxBackColor = Color.LightGray;
          this.newTextBox_Input_CEGoal.Enabled = false;
          this.newTextBox_Input_CEGoal.BackColor = Color.LightGray;
          this.newTextBox_Input_CEGoal.TextBoxBackColor = Color.LightGray;
        }
        else
        {
          string str31 = this.m_drStudy["Name"].ToString();
          string str32 = this.m_dicReport["FamilyAbbreviation"];
          DataRow dataRow1 = (DataRow) null;
          foreach (DataRow dataRow2 in this.g_dtCoolEfficiency.AsEnumerable())
          {
            int num = 0;
            string[] strArray3 = dataRow2["FA"].ToString().Split('|');
            foreach (string str33 in strArray3)
            {
              if (str32.ToUpper().Contains(str33.ToUpper()))
                ++num;
            }
            if (num == strArray3.Length && str31.ToUpper().Contains(dataRow2["Item"].ToString().ToUpper()))
            {
              dataRow1 = dataRow2;
              break;
            }
          }
          if (dataRow1 == null)
          {
            this.newTextBox_Input_CETemp1.Value = "90";
            this.newTextBox_Input_CETemp2.Value = "110";
            this.newTextBox_Input_CEGoal.Value = "90";
          }
          else
          {
            NewTextBox textBoxInputCeTemp1 = this.newTextBox_Input_CETemp1;
            double num = Convert.ToDouble(dataRow1["GTEMP"]) - Convert.ToDouble(dataRow1["PMV"]);
            string str34 = num.ToString();
            textBoxInputCeTemp1.Value = str34;
            NewTextBox textBoxInputCeTemp2 = this.newTextBox_Input_CETemp2;
            num = Convert.ToDouble(dataRow1["GTEMP"]) + Convert.ToDouble(dataRow1["PMV"]);
            string str35 = num.ToString();
            textBoxInputCeTemp2.Value = str35;
            this.newTextBox_Input_CEGoal.Value = dataRow1["GOAL"].ToString();
          }
        }
        this.newComboBox_View_Type.Items.AddRange((object[]) new string[14]
        {
          "TOP",
          "FRONT",
          "LEFT",
          "RIGHT",
          "BACK",
          "BOTTOM",
          "LEFT-FRONT-BOT",
          "BOT-FRONT-RIGHT",
          "RIGHT-FRONT-TOP",
          "TOP-FRONT-LEFT",
          "LEFT-BACK-TOP",
          "TOP-BACK-RIGHT",
          "RIGHT-BACK-BOT",
          "BOT-BACK-LEFT"
        });
        this.newComboBox_View_Type.SelectedIndex = this.newComboBox_View_Type.Items.IndexOf((object) "RIGHT-FRONT-TOP");
        foreach (DataRow dataRow in this.m_dtReportView.AsEnumerable())
        {
          DataGridViewRow row = this.dataGridView_View.Rows[this.dataGridView_View.Rows.Add()];
          row.Cells[0].Value = dataRow["SlideID"];
          row.Cells[2].Value = (object) dataRow["name"].ToString();
          row.Cells[3].Value = (object) this.newTextBox_View_X.Value;
          row.Cells[4].Value = (object) this.newTextBox_View_Y.Value;
          row.Cells[5].Value = (object) this.newTextBox_View_Z.Value;
        }
        this.dataGridView_View.ClearSelection();
        this.checkBox_AllCheck.Checked = true;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][frmHDSolutions]SetDefault):" + ex.Message));
      }
    }

    private void SetLoad()
    {
      this.m_fiReportUser = new FileInfo(clsReportDefine.g_diTmpReport.FullName + "\\" + this.m_drStudy["Name"].ToString() + "\\Report.ini");
      if (this.m_fiReportUser.Exists)
      {
        this.LoadUser();
      }
      else
      {
        if (!this.m_fiReportSystem.Exists)
          return;
        this.LoadSystem();
      }
    }

    private void LoadUser()
    {
      bool flag = false;
      string str1 = "";
      try
      {
        if (this.m_drStudy["Sequence"].ToString().Contains("Cool"))
          flag = true;
        string str2 = clsReportUtill.ReadINI("Input", "InjCond", this.m_fiReportUser.FullName);
        if (str2 != "" && str2 == "1")
          this.checkBox_Input_InjCond.Checked = true;
        string str3 = clsReportUtill.ReadINI("Input", "InjName", this.m_fiReportUser.FullName);
        if (str3 != "")
          this.newComboBox_Input_InjData.SelectedIndex = this.newComboBox_Input_InjData.Items.IndexOf((object) str3);
        string str4 = clsReportUtill.ReadINI("Input", "InjMaxStroke", this.m_fiReportUser.FullName);
        if (str4 != "")
          this.unitTextBox_Input_InjMaxStroke.Value = str4;
        string str5 = clsReportUtill.ReadINI("Input", "InjMaxRate", this.m_fiReportUser.FullName);
        if (str5 != "")
          this.unitTextBox_Input_InjMaxRate.Value = str5;
        string str6 = clsReportUtill.ReadINI("Input", "InjScrewDia", this.m_fiReportUser.FullName);
        if (str6 != "")
          this.unitTextBox_Input_InjScrewDia.Value = str6;
        string str7 = clsReportUtill.ReadINI("Input", "InjMaxPressure", this.m_fiReportUser.FullName);
        if (str7 != "")
          this.unitTextBox_Input_InjMaxPressure.Value = str7;
        string str8 = clsReportUtill.ReadINI("Input", "InjMaxClamp", this.m_fiReportUser.FullName);
        if (str8 != "")
          this.unitTextBox_Input_InjMaxClamp.Value = str8;
        string str9 = clsReportUtill.ReadINI("Input", "InjPreRatio", this.m_fiReportUser.FullName);
        if (str9 != "")
          this.unitTextBox_Input_InjPreRatio.Value = str9;
        string str10 = clsReportUtill.ReadINI("Input", "InjRange1", this.m_fiReportUser.FullName);
        if (str10 != "")
          this.unitTextBox_Input_InjRange1.Value = str10;
        string str11 = clsReportUtill.ReadINI("Input", "InjRange2", this.m_fiReportUser.FullName);
        if (str11 != "")
          this.unitTextBox_Input_InjRange2.Value = str11;
        string str12 = clsReportUtill.ReadINI("Input", "InjRange3", this.m_fiReportUser.FullName);
        if (str12 != "")
          this.unitTextBox_Input_InjRange3.Value = str12;
        string str13 = clsReportUtill.ReadINI("Input", "InjRange4", this.m_fiReportUser.FullName);
        if (str13 != "")
          this.unitTextBox_Input_InjRange4.Value = str13;
        string str14 = clsReportUtill.ReadINI("Input", "InjRangeVP", this.m_fiReportUser.FullName);
        if (str14 != "")
          this.unitTextBox_Input_InjRangeVP.Value = str14;
        string str15 = clsReportUtill.ReadINI("Input", "Report_Item", this.m_fiReportUser.FullName);
        if (str15 != "")
          this.newTextBox_Input_Item.Value = str15;
        string str16 = clsReportUtill.ReadINI("Input", "Report_Step", this.m_fiReportUser.FullName);
        if (str16 != "")
          this.newTextBox_Input_Step.Value = str16;
        string str17 = clsReportUtill.ReadINI("Input", "Report_Engineer", this.m_fiReportUser.FullName);
        if (str17 != "")
          this.newTextBox_Input_Engineer.Value = str17;
        string str18 = clsReportUtill.ReadINI("Input", "Report_Manager", this.m_fiReportUser.FullName);
        if (str18 != "")
          this.newTextBox_Input_Manager.Value = str18;
        string str19 = clsReportUtill.ReadINI("Input", "Report_WeldLine", this.m_fiReportUser.FullName);
        if (str19 != "")
          this.newComboBox_Input_WeldLine.SelectedIndex = this.newComboBox_Input_WeldLine.Items.IndexOf((object) str19.Split('|')[0]);
        string str20 = clsReportUtill.ReadINI("Input", "Report_AirTrap", this.m_fiReportUser.FullName);
        if (str20 != "")
          this.newComboBox_Input_AirTrap.SelectedIndex = this.newComboBox_Input_AirTrap.Items.IndexOf((object) str20.Split('|')[0]);
        string str21 = clsReportUtill.ReadINI("Input", "Report_Shrinkage/Sink", this.m_fiReportUser.FullName);
        if (str21 != "")
          this.newComboBox_Input_SinkMark.SelectedIndex = this.newComboBox_Input_SinkMark.Items.IndexOf((object) str21.Split('|')[0]);
        string str22 = clsReportUtill.ReadINI("Input", "Report_Cooling", this.m_fiReportUser.FullName);
        if (str22 != "")
          this.newComboBox_Input_Cooling.SelectedIndex = this.newComboBox_Input_Cooling.Items.IndexOf((object) str22.Split('|')[0]);
        string str23 = clsReportUtill.ReadINI("Input", "Report_Countermeasure", this.m_fiReportUser.FullName);
        this.newTextBox_Input_Countermeasure.Value = str23;
        if (str23 != "")
        {
          string[] strArray = str23.Split('/');
          this.newTextBox_Input_Countermeasure.Value = "";
          for (int index = 0; index < strArray.Length; ++index)
          {
            if (this.newTextBox_Input_Countermeasure.Value == "")
            {
              this.newTextBox_Input_Countermeasure.Value += strArray[index];
            }
            else
            {
              NewTextBox inputCountermeasure = this.newTextBox_Input_Countermeasure;
              inputCountermeasure.Value = inputCountermeasure.Value + Environment.NewLine + strArray[index];
            }
          }
        }
        string str24 = clsReportUtill.ReadINI("Input", "Report_ManifoldSize", this.m_fiReportUser.FullName);
        if (str24 != "")
          this.unitTextBox_Input_ManifoldSize.Value = str24;
        string str25 = clsReportUtill.ReadINI("Input", "Report_NozzleSize", this.m_fiReportUser.FullName);
        if (str25 != "")
          this.unitTextBox_Input_NozzleSize.Value = str25;
        string str26 = clsReportUtill.ReadINI("Input", "Report_ValvePinSize", this.m_fiReportUser.FullName);
        if (str26 != "")
          this.unitTextBox_Input_ValvePinSize.Value = str26;
        string str27 = clsReportUtill.ReadINI("Input", "Report_NozzleGateSize", this.m_fiReportUser.FullName);
        if (str27 != "")
          this.unitTextBox_Input_NozzleGateSize.Value = str27;
        string str28 = clsReportUtill.ReadINI("Input", "Report_HeatDiameter", this.m_fiReportUser.FullName);
        if (str28 != "")
          this.unitTextBox_Input_HeatDiameter.Value = str28;
        string str29 = clsReportUtill.ReadINI("Input", "Report_HeatPipeEffectiveness", this.m_fiReportUser.FullName);
        if (str29 != "")
          this.newTextBox_Input_HeatpipeEff.Value = str29;
        string str30 = clsReportUtill.ReadINI("User", "Report_Filling", this.m_fiReportUser.FullName);
        if (str30 != "")
        {
          string[] strArray = str30.Split('/');
          this.unitTextBox_Input_Filling1.Value = strArray[0];
          this.unitTextBox_Input_Filling2.Value = strArray[1];
          this.unitTextBox_Input_Filling3.Value = strArray[2];
          this.unitTextBox_Input_Filling4.Value = strArray[3];
        }
        string str31 = clsReportUtill.ReadINI("Input", "Report_AnimationFrame", this.m_fiReportUser.FullName);
        if (str31 != "")
          this.newTextBox_Input_FillingFrame.Value = str31;
        string str32 = clsReportUtill.ReadINI("Input", "Report_VolumePercentage", this.m_fiReportUser.FullName);
        if (str32 != "")
          this.unitTextBox_Input_TVTVPercentage.Value = str32;
        string str33 = clsReportUtill.ReadINI("Input", "Report_Time", this.m_fiReportUser.FullName);
        if (str33 != "")
          this.unitTextBox_Input_TVTTime.Value = str33;
        string str34 = clsReportUtill.ReadINI("Input", "Report_FlowTemp", this.m_fiReportUser.FullName);
        if (str34 != "")
          this.unitTextBox_Input_TVTFTemp.Value = str34;
        string str35 = clsReportUtill.ReadINI("Input", "Report_MoldTemp", this.m_fiReportUser.FullName);
        if (str35 != "")
          this.unitTextBox_Input_TVTMTemp.Value = str35;
        string str36 = clsReportUtill.ReadINI("Input", "Report_DeflectionAll", this.m_fiReportUser.FullName);
        if (str36 != "")
        {
          string[] strArray = str36.Split('/');
          this.newComboBox_Input_DefAll.SelectedIndex = this.newComboBox_Input_DefAll.Items.IndexOf((object) strArray[0]);
          this.newTextBox_Input_DefAll.Value = strArray[1];
        }
        string str37 = clsReportUtill.ReadINI("Input", "Report_DeflectionX", this.m_fiReportUser.FullName);
        if (str37 != "")
        {
          string[] strArray = str37.Split('/');
          this.newComboBox_Input_DefX.SelectedIndex = this.newComboBox_Input_DefX.Items.IndexOf((object) strArray[0]);
          this.newTextBox_Input_DefX.Value = strArray[1];
        }
        string str38 = clsReportUtill.ReadINI("Input", "Report_DeflectionY", this.m_fiReportUser.FullName);
        if (str38 != "")
        {
          string[] strArray = str38.Split('/');
          this.newComboBox_Input_DefY.SelectedIndex = this.newComboBox_Input_DefY.Items.IndexOf((object) strArray[0]);
          this.newTextBox_Input_DefY.Value = strArray[1];
        }
        string str39 = clsReportUtill.ReadINI("Input", "Report_DeflectionZ", this.m_fiReportUser.FullName);
        if (str39 != "")
        {
          string[] strArray = str39.Split('/');
          this.newComboBox_Input_DefZ.SelectedIndex = this.newComboBox_Input_DefZ.Items.IndexOf((object) strArray[0]);
          this.newTextBox_Input_DefZ.Value = strArray[1];
        }
        str1 = clsReportUtill.ReadINI("Input", "DefAll1", this.m_fiReportUser.FullName);
        if (!flag)
        {
          string str40 = clsReportUtill.ReadINI("Input", "Report_SCOption", this.m_fiReportUser.FullName);
          if (str40 != "")
            this.newComboBox_Input_SCOpt.SelectedIndex = this.newComboBox_Input_SCOpt.Items.IndexOf((object) str40);
          string str41 = clsReportUtill.ReadINI("Input", "Report_SCValue", this.m_fiReportUser.FullName);
          if (str41 != "")
            this.newTextBox_Input_SCVal.Value = str41;
        }
        if (flag)
        {
          string str42 = clsReportUtill.ReadINI("Input", "Report_MoldTemp1", this.m_fiReportUser.FullName);
          if (str42 != "")
            this.newTextBox_Input_CETemp1.Value = str42;
          string str43 = clsReportUtill.ReadINI("Input", "Report_MoldTemp2", this.m_fiReportUser.FullName);
          if (str43 != "")
            this.newTextBox_Input_CETemp2.Value = str43;
          string str44 = clsReportUtill.ReadINI("Input", "Report_MoldGoal", this.m_fiReportUser.FullName);
          if (str44 != "")
            this.newTextBox_Input_CEGoal.Value = str44;
        }
        string str45 = clsReportUtill.ReadINI("View", "ViewType", this.m_fiReportUser.FullName);
        if (str45 != "")
          this.newComboBox_View_Type.SelectedIndex = this.newComboBox_View_Type.Items.IndexOf((object) str45);
        for (int index = 0; index < this.dataGridView_View.Rows.Count; ++index)
        {
          string[] strArray = clsReportUtill.ReadINI("View", this.dataGridView_View.Rows[index].Cells[2].Value.ToString(), this.m_fiReportUser.FullName).Split('/');
          this.dataGridView_View.Rows[index].Cells[3].Value = (object) strArray[0];
          this.dataGridView_View.Rows[index].Cells[4].Value = (object) strArray[1];
          this.dataGridView_View.Rows[index].Cells[5].Value = (object) strArray[2];
        }
        string[] arr_strUse = clsReportUtill.GetINIAllKeys(this.m_fiReportUser.FullName, "Use");
        for (int i = 0; i < arr_strUse.Length; i++)
        {
          string p_strValue = clsReportUtill.ReadINI("Use", arr_strUse[i], this.m_fiReportUser.FullName);
          foreach (DataGridViewRow dataGridViewRow in this.dataGridView_View.Rows.Cast<DataGridViewRow>().Where<DataGridViewRow>((System.Func<DataGridViewRow, bool>) (Temp => Temp.Cells[0].Value.ToString() == arr_strUse[i])).ToArray<DataGridViewRow>())
            dataGridViewRow.Cells[1].Value = (object) clsReportUtill.ConvertToBoolean(p_strValue);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][frmHDSolutions]LoadUser):" + ex.Message));
      }
    }

    private void LoadSystem()
    {
      try
      {
        string str1 = clsReportUtill.ReadINI("Input", "InjCond", this.m_fiReportSystem.FullName);
        if (str1 != "" && str1 == "1")
          this.checkBox_Input_InjCond.Checked = true;
        string str2 = clsReportUtill.ReadINI("Input", "InjRange1", this.m_fiReportSystem.FullName);
        if (str2 != "")
          this.unitTextBox_Input_InjRange1.Value = str2;
        string str3 = clsReportUtill.ReadINI("Input", "InjRange2", this.m_fiReportSystem.FullName);
        if (str3 != "")
          this.unitTextBox_Input_InjRange2.Value = str3;
        string str4 = clsReportUtill.ReadINI("Input", "InjRange3", this.m_fiReportSystem.FullName);
        if (str4 != "")
          this.unitTextBox_Input_InjRange3.Value = str4;
        string str5 = clsReportUtill.ReadINI("Input", "InjRange4", this.m_fiReportSystem.FullName);
        if (str5 != "")
          this.unitTextBox_Input_InjRange4.Value = str5;
        string str6 = clsReportUtill.ReadINI("Input", "InjRangeVP", this.m_fiReportSystem.FullName);
        if (str6 != "")
          this.unitTextBox_Input_InjRangeVP.Value = str6;
        string str7 = clsReportUtill.ReadINI("Input", "Item", this.m_fiReportSystem.FullName);
        if (str7 != "")
          this.newTextBox_Input_Item.Value = str7;
        string str8 = clsReportUtill.ReadINI("Input", "Step", this.m_fiReportSystem.FullName);
        if (str8 != "")
          this.newTextBox_Input_Step.Value = str8;
        string str9 = clsReportUtill.ReadINI("Input", "Engineer", this.m_fiReportSystem.FullName);
        if (str9 != "")
          this.newTextBox_Input_Engineer.Value = str9;
        string str10 = clsReportUtill.ReadINI("Input", "Manager", this.m_fiReportSystem.FullName);
        if (str10 != "")
          this.newTextBox_Input_Manager.Value = str10;
        string str11 = clsReportUtill.ReadINI("Input", "Report_WeldLine", this.m_fiReportSystem.FullName);
        if (str11 != "")
          this.newComboBox_Input_WeldLine.SelectedIndex = this.newComboBox_Input_WeldLine.Items.IndexOf((object) str11.Split('|')[0]);
        string str12 = clsReportUtill.ReadINI("Input", "Report_AirTrap", this.m_fiReportSystem.FullName);
        if (str12 != "")
          this.newComboBox_Input_AirTrap.SelectedIndex = this.newComboBox_Input_AirTrap.Items.IndexOf((object) str12.Split('|')[0]);
        string str13 = clsReportUtill.ReadINI("Input", "Report_Shrinkage/Sink", this.m_fiReportSystem.FullName);
        if (str13 != "")
          this.newComboBox_Input_SinkMark.SelectedIndex = this.newComboBox_Input_SinkMark.Items.IndexOf((object) str13.Split('|')[0]);
        string str14 = clsReportUtill.ReadINI("Input", "Report_Cooling", this.m_fiReportSystem.FullName);
        if (str14 != "")
          this.newComboBox_Input_Cooling.SelectedIndex = this.newComboBox_Input_Cooling.Items.IndexOf((object) str14.Split('|')[0]);
        string str15 = clsReportUtill.ReadINI("Input", "Report_Countermeasure", this.m_fiReportSystem.FullName);
        this.newTextBox_Input_Countermeasure.Value = str15;
        if (str15 != "")
        {
          string[] strArray = str15.Split('/');
          this.newTextBox_Input_Countermeasure.Value = "";
          for (int index = 0; index < strArray.Length; ++index)
          {
            if (this.newTextBox_Input_Countermeasure.Value == "")
            {
              this.newTextBox_Input_Countermeasure.Value += strArray[index];
            }
            else
            {
              NewTextBox inputCountermeasure = this.newTextBox_Input_Countermeasure;
              inputCountermeasure.Value = inputCountermeasure.Value + Environment.NewLine + strArray[index];
            }
          }
        }
        string str16 = clsReportUtill.ReadINI("Input", "Report_ManifoldSize", this.m_fiReportSystem.FullName);
        if (str16 != "")
          this.unitTextBox_Input_ManifoldSize.Value = str16;
        string str17 = clsReportUtill.ReadINI("Input", "Report_NozzleSize", this.m_fiReportSystem.FullName);
        if (str17 != "")
          this.unitTextBox_Input_NozzleSize.Value = str17;
        string str18 = clsReportUtill.ReadINI("Input", "Report_ValvePinSize", this.m_fiReportSystem.FullName);
        if (str18 != "")
          this.unitTextBox_Input_ValvePinSize.Value = str18;
        string str19 = clsReportUtill.ReadINI("Input", "Report_NozzleGateSize", this.m_fiReportSystem.FullName);
        if (str19 != "")
          this.unitTextBox_Input_NozzleGateSize.Value = str19;
        string str20 = clsReportUtill.ReadINI("Input", "Report_HeatDiameter", this.m_fiReportSystem.FullName);
        if (str20 != "")
          this.unitTextBox_Input_HeatDiameter.Value = str20;
        string str21 = clsReportUtill.ReadINI("Input", "Report_HeatPipeEffectiveness", this.m_fiReportSystem.FullName);
        if (str21 != "")
          this.newTextBox_Input_HeatpipeEff.Value = str21;
        string str22 = clsReportUtill.ReadINI("User", "Report_Filling", this.m_fiReportSystem.FullName);
        if (str22 != "")
        {
          string[] strArray = str22.Split('/');
          this.unitTextBox_Input_Filling1.Value = strArray[0];
          this.unitTextBox_Input_Filling2.Value = strArray[1];
          this.unitTextBox_Input_Filling3.Value = strArray[2];
          this.unitTextBox_Input_Filling4.Value = strArray[3];
        }
        string str23 = clsReportUtill.ReadINI("Input", "Report_AnimationFrame", this.m_fiReportSystem.FullName);
        if (str23 != "")
          this.newTextBox_Input_FillingFrame.Value = str23;
        string str24 = clsReportUtill.ReadINI("View", "ViewType", this.m_fiReportSystem.FullName);
        if (str24 != "")
          this.newComboBox_View_Type.SelectedIndex = this.newComboBox_View_Type.Items.IndexOf((object) str24);
        for (int index = 0; index < this.dataGridView_View.Rows.Count; ++index)
        {
          string[] strArray = clsReportUtill.ReadINI("View", this.dataGridView_View.Rows[index].Cells[2].Value.ToString(), this.m_fiReportSystem.FullName).Split('/');
          this.dataGridView_View.Rows[index].Cells[3].Value = (object) strArray[0];
          this.dataGridView_View.Rows[index].Cells[4].Value = (object) strArray[1];
          this.dataGridView_View.Rows[index].Cells[5].Value = (object) strArray[2];
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][frmHDSolutions]LoadSystem):" + ex.Message));
      }
    }

    private void newComboBox_Input_InjData_SelectedIndexChanged(object sender, EventArgs e)
    {
      if (this.newComboBox_Input_InjData.SelectedIndex == 0)
      {
        if (this.m_dicReport == null || this.m_dicReport.Count <= 0)
          return;
        this.unitTextBox_Input_InjMaxStroke.Value = this.m_dicReport["InjMaxStroke"];
        this.unitTextBox_Input_InjMaxRate.Value = this.m_dicReport["InjMaxRate"];
        this.unitTextBox_Input_InjScrewDia.Value = this.m_dicReport["InjScrewDia"];
        this.unitTextBox_Input_InjMaxPressure.Value = this.m_dicReport["InjMaxPressure"];
        this.unitTextBox_Input_InjMaxClamp.Value = this.m_dicReport["InjMaxClamp"];
      }
      else
      {
        DataRow dataRow = clsReportDefine.g_dtInjectionDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["FileName"].ToString() == this.newComboBox_Input_InjData.Value)).FirstOrDefault<DataRow>();
        if (dataRow == null)
          return;
        this.unitTextBox_Input_InjMaxStroke.Value = dataRow["MaxStroke"].ToString();
        this.unitTextBox_Input_InjMaxRate.Value = dataRow["MaxRate"].ToString();
        this.unitTextBox_Input_InjScrewDia.Value = dataRow["ScrewDia"].ToString();
        this.unitTextBox_Input_InjMaxPressure.Value = dataRow["MaxPressure"].ToString();
        this.unitTextBox_Input_InjMaxClamp.Value = dataRow["MaxClamp"].ToString();
        this.unitTextBox_Input_InjPreRatio.Value = dataRow["PreRatio"].ToString();
      }
    }

    private void newComboBox_View_Type_SelectedIndexChanged(object sender, EventArgs e)
    {
      string s = this.newComboBox_View_Type.Value;
      // ISSUE: reference to a compiler-generated method
      switch (\u003CPrivateImplementationDetails\u003E.ComputeStringHash(s))
      {
        case 122316834:
          if (!(s == "BACK"))
            break;
          this.newTextBox_View_X.Value = "0";
          this.newTextBox_View_Y.Value = "180";
          this.newTextBox_View_Z.Value = "0";
          break;
        case 184671100:
          if (!(s == "BOT-BACK-LEFT"))
            break;
          this.newTextBox_View_X.Value = "45";
          this.newTextBox_View_Y.Value = "145";
          this.newTextBox_View_Z.Value = "-30";
          break;
        case 272375920:
          if (!(s == "LEFT"))
            break;
          this.newTextBox_View_X.Value = "0";
          this.newTextBox_View_Y.Value = "90";
          this.newTextBox_View_Z.Value = "0";
          break;
        case 462935941:
          if (!(s == "RIGHT"))
            break;
          this.newTextBox_View_X.Value = "0";
          this.newTextBox_View_Y.Value = "-90";
          this.newTextBox_View_Z.Value = "0";
          break;
        case 546288794:
          if (!(s == "LEFT-FRONT-BOT"))
            break;
          this.newTextBox_View_X.Value = "-45";
          this.newTextBox_View_Y.Value = "35";
          this.newTextBox_View_Z.Value = "30";
          break;
        case 632706351:
          if (!(s == "BOT-FRONT-RIGHT"))
            break;
          this.newTextBox_View_X.Value = "-45";
          this.newTextBox_View_Y.Value = "-35";
          this.newTextBox_View_Z.Value = "-30";
          break;
        case 692843612:
          if (!(s == "TOP"))
            break;
          this.newTextBox_View_X.Value = "90";
          this.newTextBox_View_Y.Value = "0";
          this.newTextBox_View_Z.Value = "0";
          break;
        case 1869497751:
          if (!(s == "TOP-BACK-RIGHT"))
            break;
          this.newTextBox_View_X.Value = "135";
          this.newTextBox_View_Y.Value = "-35";
          this.newTextBox_View_Z.Value = "-30";
          break;
        case 2573064646:
          if (!(s == "LEFT-BACK-TOP"))
            break;
          this.newTextBox_View_X.Value = "135";
          this.newTextBox_View_Y.Value = "35";
          this.newTextBox_View_Z.Value = "30";
          break;
        case 2596576200:
          if (!(s == "TOP-FRONT-LEFT"))
            break;
          this.newTextBox_View_X.Value = "-135";
          this.newTextBox_View_Y.Value = "145";
          this.newTextBox_View_Z.Value = "-30";
          break;
        case 3291408952:
          if (!(s == "FRONT"))
            break;
          this.newTextBox_View_X.Value = "0";
          this.newTextBox_View_Y.Value = "0";
          this.newTextBox_View_Z.Value = "0";
          break;
        case 3351543025:
          if (!(s == "RIGHT-FRONT-TOP"))
            break;
          this.newTextBox_View_X.Value = "-135";
          this.newTextBox_View_Y.Value = "-145";
          this.newTextBox_View_Z.Value = "30";
          break;
        case 3407844522:
          if (!(s == "BOTTOM"))
            break;
          this.newTextBox_View_X.Value = "90";
          this.newTextBox_View_Y.Value = "180";
          this.newTextBox_View_Z.Value = "0";
          break;
        case 4249270451:
          if (!(s == "RIGHT-BACK-BOT"))
            break;
          this.newTextBox_View_X.Value = "45";
          this.newTextBox_View_Y.Value = "-145";
          this.newTextBox_View_Z.Value = "30";
          break;
      }
    }

    private void newButton_NewClick(object sender, EventArgs e)
    {
      NewButton newButton = sender as NewButton;
      if (newButton == this.newButton_Input)
      {
        newButton.ButtonBackColor = Color.LightSteelBlue;
        this.newButton_View.ButtonBackColor = Color.White;
        this.newButton_Set.ButtonBackColor = Color.White;
        this.tabControl_Main.SelectedIndex = 0;
      }
      else if (newButton == this.newButton_View)
      {
        this.newButton_Input.ButtonBackColor = Color.White;
        this.newButton_Set.ButtonBackColor = Color.White;
        newButton.ButtonBackColor = Color.LightSteelBlue;
        this.tabControl_Main.SelectedIndex = 1;
        this.dataGridView_View.ClearSelection();
      }
      else if (newButton == this.newButton_Set)
      {
        this.newButton_Input.ButtonBackColor = Color.White;
        this.newButton_View.ButtonBackColor = Color.White;
        newButton.ButtonBackColor = Color.LightSteelBlue;
        this.tabControl_Main.SelectedIndex = 2;
      }
      else if (newButton == this.newButton_View_All)
      {
        foreach (DataGridViewRow row in (IEnumerable) this.dataGridView_View.Rows)
        {
          row.Cells[3].Value = (object) this.newTextBox_View_X.Value;
          row.Cells[4].Value = (object) this.newTextBox_View_Y.Value;
          row.Cells[5].Value = (object) this.newTextBox_View_Z.Value;
        }
      }
      else if (newButton == this.newButton_View_Select)
      {
        foreach (DataGridViewRow row in (IEnumerable) this.dataGridView_View.Rows)
        {
          if ((bool) row.Cells[1].FormattedValue)
          {
            row.Cells[3].Value = (object) this.newTextBox_View_X.Value;
            row.Cells[4].Value = (object) this.newTextBox_View_Y.Value;
            row.Cells[5].Value = (object) this.newTextBox_View_Z.Value;
          }
        }
      }
      else
      {
        if (newButton != this.newButton_Apply)
          return;
        this.Apply();
      }
    }

    private void Apply()
    {
      bool flag = false;
      StringBuilder stringBuilder = new StringBuilder();
      Dictionary<string, string> dictionary = new Dictionary<string, string>();
      try
      {
        if (this.m_drStudy["Sequence"].ToString().Contains("Cool"))
          flag = true;
        if (this.m_fiResult.Exists)
        {
          clsReportUtill.WriteINI("Value", "InjPressure1", this.unitTextBox_InjPressure1.Value, this.m_fiResult.FullName);
          clsReportUtill.WriteINI("Value", "InjPressure2", this.unitTextBox_InjPressure2.Value, this.m_fiResult.FullName);
          clsReportUtill.WriteINI("Value", "InCavityPressure1", this.unitTextBox_InCavityPressure1.Value, this.m_fiResult.FullName);
          clsReportUtill.WriteINI("Value", "InCavityPressure2", this.unitTextBox_InCavityPressure2.Value, this.m_fiResult.FullName);
          clsReportUtill.WriteINI("Value", "CircuitReynoldsNumber", this.unitTextBox_ReynoldsNumber.Value, this.m_fiResult.FullName);
          clsReportUtill.WriteINI("Value", "CircuitCoolantTempCore", this.unitTextBox_CoolantTempCore.Value, this.m_fiResult.FullName);
          clsReportUtill.WriteINI("Value", "CircuitCoolantTempCavity", this.unitTextBox_CoolantTempCavity.Value, this.m_fiResult.FullName);
          clsReportUtill.WriteINI("Value", "CircuitFlowRate", this.unitTextBox_FlowRate.Value, this.m_fiResult.FullName);
        }
        if (this.m_fiCoolEfficiency.Exists)
        {
          clsReportUtill.WriteINI("FRONT_LENS", "PC", "10|" + this.newTextBox_FLENS_GTemp_PC.Value + "|" + this.newTextBox_FLENS_Goal_PC.Value, this.m_fiCoolEfficiency.FullName);
          clsReportUtill.WriteINI("FRONT_BEZEL", "PC", "10|" + this.newTextBox_FBEZEL_GTemp_PC.Value + "|" + this.newTextBox_FBEZEL_Goal_PC.Value, this.m_fiCoolEfficiency.FullName);
          clsReportUtill.WriteINI("FRONT_BEZEL", "PBT", "5|" + this.newTextBox_FBEZEL_GTemp_PBT.Value + "|" + this.newTextBox_FBEZEL_Goal_PBT.Value, this.m_fiCoolEfficiency.FullName);
          clsReportUtill.WriteINI("FRONT_HSG", "PP", "5|" + this.newTextBox_FHSG_GTemp_PP.Value + "|" + this.newTextBox_FHSG_Goal_PP.Value, this.m_fiCoolEfficiency.FullName);
          clsReportUtill.WriteINI("REAR_LENS", "PMMA", "10|" + this.newTextBox_RLENS_GTemp_PMMA.Value + "|" + this.newTextBox_RLENS_Goal_PMMA.Value, this.m_fiCoolEfficiency.FullName);
          clsReportUtill.WriteINI("REAR_BEZEL", "PC", "10|" + this.newTextBox_RBEZEL_GTemp_PC.Value + "|" + this.newTextBox_RBEZEL_Goal_PC.Value, this.m_fiCoolEfficiency.FullName);
          clsReportUtill.WriteINI("REAR_HSG", "PC/ABS", "10|" + this.newTextBox_RHSG_GTemp_PCABS.Value + "|" + this.newTextBox_RHSG_Goal_PCABS.Value, this.m_fiCoolEfficiency.FullName);
        }
        if (this.m_fiShirnkage.Exists)
        {
          clsReportUtill.WriteINI("Value", "PC", this.newTextBox_PC.Value, this.m_fiShirnkage.FullName);
          clsReportUtill.WriteINI("Value", "PBT", this.newTextBox_PBT.Value, this.m_fiShirnkage.FullName);
          clsReportUtill.WriteINI("Value", "ABS", this.newTextBox_ABS.Value, this.m_fiShirnkage.FullName);
          clsReportUtill.WriteINI("Value", "PP", this.newTextBox_PP.Value, this.m_fiShirnkage.FullName);
          clsReportUtill.WriteINI("Value", "PMMA", this.newTextBox_PMMA.Value, this.m_fiShirnkage.FullName);
          clsReportUtill.WriteINI("Value", "PCABS", this.newTextBox_PCABS.Value, this.m_fiShirnkage.FullName);
          clsReportUtill.WriteINI("Value", "PA66", this.newTextBox_PA66.Value, this.m_fiShirnkage.FullName);
        }
        foreach (DataGridViewRow row in (IEnumerable) this.dataGridView_ShrinkComp.Rows)
          clsReportUtill.WriteINI("Shrinkage Compensation", row.Cells[0].Value.ToString(), row.Cells[1].Value.ToString(), this.m_fiShrinkComp.FullName);
        clsReportUtill.WriteINI("Thickness", "Min", this.unitTextBox_ThicknessMin.Value, this.m_fiThickness.FullName);
        clsReportUtill.WriteINI("Thickness", "Max", this.unitTextBox_ThicknessMax.Value, this.m_fiThickness.FullName);
        if (!this.checkBox_Input_InjCond.Checked)
          clsReportUtill.WriteINI("Input", "InjCond", "0", this.m_fiReportUser.FullName);
        else
          clsReportUtill.WriteINI("Input", "InjCond", "1", this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "InjName", this.newComboBox_Input_InjData.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "InjMaxStroke", this.unitTextBox_Input_InjMaxStroke.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "InjMaxRate", this.unitTextBox_Input_InjMaxRate.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "InjScrewDia", this.unitTextBox_Input_InjScrewDia.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "InjMaxPressure", this.unitTextBox_Input_InjMaxPressure.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "InjMaxClamp", this.unitTextBox_Input_InjMaxClamp.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "InjPreRatio", this.unitTextBox_Input_InjPreRatio.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "InjRange1", this.unitTextBox_Input_InjRange1.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "InjRange2", this.unitTextBox_Input_InjRange2.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "InjRange3", this.unitTextBox_Input_InjRange3.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "InjRange4", this.unitTextBox_Input_InjRange4.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "InjRangeVP", this.unitTextBox_Input_InjRangeVP.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Report_Item", this.newTextBox_Input_Item.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Report_Step", this.newTextBox_Input_Step.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Report_Engineer", this.newTextBox_Input_Engineer.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Report_Manager", this.newTextBox_Input_Manager.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Report_WeldLine", this.newComboBox_Input_WeldLine.Value + "|" + this.newTextBox_Input_WeldLine.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Report_AirTrap", this.newComboBox_Input_AirTrap.Value + "|" + this.newTextBox_Input_AirTrap.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Report_Shrinkage/Sink", this.newComboBox_Input_SinkMark.Value + "|" + this.newTextBox_Input_SinkMark.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Report_Cooling", this.newComboBox_Input_Cooling.Value + "|" + this.newTextBox_Input_Cooling.Value, this.m_fiReportUser.FullName);
        stringBuilder.Clear();
        for (int index = 0; index < this.newTextBox_Input_Countermeasure.Lines.Length; ++index)
        {
          if (stringBuilder.Length == 0)
            stringBuilder.Append(this.newTextBox_Input_Countermeasure.Lines[index]);
          else
            stringBuilder.Append("/" + this.newTextBox_Input_Countermeasure.Lines[index]);
        }
        clsReportUtill.WriteINI("Input", "Report_Countermeasure", stringBuilder.ToString(), this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Report_ManifoldSize", this.unitTextBox_Input_ManifoldSize.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Report_NozzleSize", this.unitTextBox_Input_NozzleSize.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Report_ValvePinSize", this.unitTextBox_Input_ValvePinSize.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Report_NozzleGateSize", this.unitTextBox_Input_NozzleGateSize.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Report_ManifoldVolume", this.unitTextBox_Input_ManifoldVolume.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Report_HeatDiameter", this.unitTextBox_Input_HeatDiameter.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Report_HeatPipeEffectiveness", this.newTextBox_Input_HeatpipeEff.Value, this.m_fiReportUser.FullName);
        stringBuilder.Clear();
        if (this.unitTextBox_Input_Filling1.Value == "")
          return;
        stringBuilder.Append(this.unitTextBox_Input_Filling1.Value);
        if (this.unitTextBox_Input_Filling2.Value == "")
          return;
        stringBuilder.Append("/" + this.unitTextBox_Input_Filling2.Value);
        if (this.unitTextBox_Input_Filling3.Value == "")
          return;
        stringBuilder.Append("/" + this.unitTextBox_Input_Filling3.Value);
        if (this.unitTextBox_Input_Filling4.Value == "")
          return;
        stringBuilder.Append("/" + this.unitTextBox_Input_Filling4.Value);
        clsReportUtill.WriteINI("Input", "Report_Filling", stringBuilder.ToString(), this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Report_AnimationFrame", this.newTextBox_Input_FillingFrame.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Report_VolumePercentage", this.unitTextBox_Input_TVTVPercentage.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Report_Time", this.unitTextBox_Input_TVTTime.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Report_FlowTemp", this.unitTextBox_Input_TVTFTemp.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Report_MoldTemp", this.unitTextBox_Input_TVTMTemp.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Report_DeflectionAll", this.newComboBox_Input_DefAll.Value + "/" + this.newTextBox_Input_DefAll.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Report_DeflectionX", this.newComboBox_Input_DefX.Value + "/" + this.newTextBox_Input_DefX.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Report_DeflectionY", this.newComboBox_Input_DefY.Value + "/" + this.newTextBox_Input_DefY.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Input", "Report_DeflectionZ", this.newComboBox_Input_DefZ.Value + "/" + this.newTextBox_Input_DefZ.Value, this.m_fiReportUser.FullName);
        if (!flag)
        {
          clsReportUtill.WriteINI("Input", "Report_SCOption", this.newComboBox_Input_SCOpt.Value, this.m_fiReportUser.FullName);
          clsReportUtill.WriteINI("Input", "Report_SCValue", this.newTextBox_Input_SCVal.Value, this.m_fiReportUser.FullName);
        }
        if (flag)
        {
          clsReportUtill.WriteINI("Input", "Report_MoldTemp1", this.newTextBox_Input_CETemp1.Value, this.m_fiReportUser.FullName);
          clsReportUtill.WriteINI("Input", "Report_MoldTemp2", this.newTextBox_Input_CETemp2.Value, this.m_fiReportUser.FullName);
          clsReportUtill.WriteINI("Input", "Report_MoldGoal", this.newTextBox_Input_CEGoal.Value, this.m_fiReportUser.FullName);
        }
        clsReportUtill.WriteINI("View", "ViewType", this.newComboBox_View_Type.Value, this.m_fiReportUser.FullName);
        for (int index = 0; index < this.dataGridView_View.Rows.Count; ++index)
        {
          string p_strValue = this.dataGridView_View.Rows[index].Cells[3].Value.ToString() + "/" + this.dataGridView_View.Rows[index].Cells[4].Value + "/" + this.dataGridView_View.Rows[index].Cells[5].Value;
          clsReportUtill.WriteINI("View", this.dataGridView_View.Rows[index].Cells[2].Value.ToString(), p_strValue, this.m_fiReportUser.FullName);
        }
        foreach (DataGridViewRow row in (IEnumerable) this.dataGridView_View.Rows)
        {
          if (dictionary.ContainsKey((string) row.Cells[0].Value))
          {
            if (row.Cells[1].Value.ToString() != dictionary[(string) row.Cells[0].Value])
              dictionary[(string) row.Cells[0].Value] = "True";
          }
          else if (row.Cells[1].Value == null)
            dictionary.Add((string) row.Cells[0].Value, "False");
          else
            dictionary.Add((string) row.Cells[0].Value, row.Cells[1].Value.ToString());
        }
        foreach (KeyValuePair<string, string> keyValuePair in dictionary)
          clsReportUtill.WriteINI("Use", keyValuePair.Key, keyValuePair.Value, this.m_fiReportUser.FullName);
        clsReportUtill.WriteINI("Template", "Company", this.m_enumCompany.ToString(), this.m_fiReportUser.FullName);
        this.DialogResult = DialogResult.OK;
        this.Close();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][frmHDSolutions]Apply):" + ex.Message));
      }
    }

    private void frmHDSolutions_FormClosed(object sender, FormClosedEventArgs e)
    {
      bool flag = false;
      StringBuilder stringBuilder = new StringBuilder();
      try
      {
        if (this.m_drStudy["Sequence"].ToString().Contains("Cool"))
          flag = true;
        if (!this.checkBox_Input_InjCond.Checked)
          clsReportUtill.WriteINI("Input", "InjCond", "0", this.m_fiReportSystem.FullName);
        else
          clsReportUtill.WriteINI("Input", "InjCond", "1", this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "InjRange1", this.unitTextBox_Input_InjRange1.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "InjRange2", this.unitTextBox_Input_InjRange2.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "InjRange3", this.unitTextBox_Input_InjRange3.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "InjRange4", this.unitTextBox_Input_InjRange4.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "InjRangeVP", this.unitTextBox_Input_InjRangeVP.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Report_Item", this.newTextBox_Input_Item.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Report_Step", this.newTextBox_Input_Step.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Report_Engineer", this.newTextBox_Input_Engineer.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Report_Manager", this.newTextBox_Input_Manager.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Report_WeldLine", this.newComboBox_Input_WeldLine.Value + "|" + this.newTextBox_Input_WeldLine.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Report_AirTrap", this.newComboBox_Input_AirTrap.Value + "|" + this.newTextBox_Input_AirTrap.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Report_ShrinkageSink", this.newComboBox_Input_SinkMark.Value + "|" + this.newComboBox_Input_SinkMark.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Report_Cooling", this.newComboBox_Input_Cooling.Value + "|" + this.newTextBox_Input_Cooling.Value, this.m_fiReportSystem.FullName);
        stringBuilder.Clear();
        for (int index = 0; index < this.newTextBox_Input_Countermeasure.Lines.Length; ++index)
        {
          if (stringBuilder.Length == 0)
            stringBuilder.Append(this.newTextBox_Input_Countermeasure.Lines[index]);
          else
            stringBuilder.Append("/" + this.newTextBox_Input_Countermeasure.Lines[index]);
        }
        clsReportUtill.WriteINI("Input", "Report_Countermeasure", stringBuilder.ToString(), this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Report_ManifoldSize", this.unitTextBox_Input_ManifoldSize.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Report_NozzleSize", this.unitTextBox_Input_NozzleSize.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Report_ValvePinSize", this.unitTextBox_Input_ValvePinSize.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Report_NozzleGateSize", this.unitTextBox_Input_NozzleGateSize.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Report_ManifoldVolume", this.unitTextBox_Input_ManifoldVolume.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Report_HeatDiameter", this.unitTextBox_Input_HeatDiameter.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Report_HeatPipeEffectiveness", this.newTextBox_Input_HeatpipeEff.Value, this.m_fiReportSystem.FullName);
        stringBuilder.Clear();
        if (this.unitTextBox_Input_Filling1.Value == "")
          return;
        stringBuilder.Append(this.unitTextBox_Input_Filling1.Value);
        if (this.unitTextBox_Input_Filling2.Value == "")
          return;
        stringBuilder.Append("/" + this.unitTextBox_Input_Filling2.Value);
        if (this.unitTextBox_Input_Filling3.Value == "")
          return;
        stringBuilder.Append("/" + this.unitTextBox_Input_Filling3.Value);
        if (this.unitTextBox_Input_Filling4.Value == "")
          return;
        stringBuilder.Append("/" + this.unitTextBox_Input_Filling4.Value);
        clsReportUtill.WriteINI("Input", "Report_Filling", stringBuilder.ToString(), this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Report_FillingFrame", this.newTextBox_Input_FillingFrame.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Report_VolumePercentage", this.unitTextBox_Input_TVTVPercentage.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Report_Time", this.unitTextBox_Input_TVTTime.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Report_FlowTemp", this.unitTextBox_Input_TVTFTemp.Value, this.m_fiReportSystem.FullName);
        clsReportUtill.WriteINI("Input", "Report_MoldTemp", this.unitTextBox_Input_TVTMTemp.Value, this.m_fiReportSystem.FullName);
        if (!flag)
        {
          clsReportUtill.WriteINI("Input", "Report_SCOption", this.newComboBox_Input_SCOpt.Value, this.m_fiReportSystem.FullName);
          clsReportUtill.WriteINI("Input", "Report_SCValue", this.newTextBox_Input_SCVal.Value, this.m_fiReportUser.FullName);
        }
        if (flag)
        {
          clsReportUtill.WriteINI("Input", "Report_MoldTemp1", this.newTextBox_Input_CETemp1.Value, this.m_fiReportSystem.FullName);
          clsReportUtill.WriteINI("Input", "Report_MoldTemp2", this.newTextBox_Input_CETemp2.Value, this.m_fiReportSystem.FullName);
          clsReportUtill.WriteINI("Input", "Report_MoldGoal", this.newTextBox_Input_CEGoal.Value, this.m_fiReportSystem.FullName);
        }
        clsReportUtill.WriteINI("View", "ViewType", this.newComboBox_View_Type.Value, this.m_fiReportSystem.FullName);
        for (int index = 0; index < this.dataGridView_View.Rows.Count; ++index)
        {
          string p_strValue = this.dataGridView_View.Rows[index].Cells[3].Value.ToString() + "/" + this.dataGridView_View.Rows[index].Cells[4].Value + "/" + this.dataGridView_View.Rows[index].Cells[5].Value;
          clsReportUtill.WriteINI("View", this.dataGridView_View.Rows[index].Cells[2].Value.ToString(), p_strValue, this.m_fiReportSystem.FullName);
        }
        clsReportUtill.WriteINI("Template", "Company", this.m_enumCompany.ToString(), this.m_fiReportSystem.FullName);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][frmHDSolutions]frmHDSolutions_FormClosed):" + ex.Message));
      }
    }

    private void checkBox_AllCheck_CheckedChanged(object sender, EventArgs e)
    {
      foreach (DataGridViewRow row in (IEnumerable) this.dataGridView_View.Rows)
        row.Cells[1].Value = (object) ((CheckBox) sender).Checked;
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      DataGridViewCellStyle gridViewCellStyle1 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle2 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle3 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle4 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle5 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle6 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle7 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle8 = new DataGridViewCellStyle();
      this.tabControl_Main = new TabControl();
      this.tabPage_Input = new TabPage();
      this.newTextBox_Input_Step = new NewTextBox();
      this.label_Report = new Label();
      this.newTextBox_Input_CEGoal = new NewTextBox();
      this.label_Target = new Label();
      this.newTextBox_Input_CETemp2 = new NewTextBox();
      this.label90 = new Label();
      this.newTextBox_Input_CETemp1 = new NewTextBox();
      this.label29 = new Label();
      this.newTextBox_Input_SCVal = new NewTextBox();
      this.label_Input_SCVal = new Label();
      this.newComboBox_Input_SCOpt = new NewComboBox();
      this.label_Cooling_Efficiency = new Label();
      this.label_Deflection_Rate = new Label();
      this.label_Option = new Label();
      this.label_Input_TVTFTemp = new Label();
      this.unitTextBox_Input_TVTFTemp = new UnitTextBox();
      this.unitTextBox_Input_TVTMTemp = new UnitTextBox();
      this.unitTextBox_Input_TVTTime = new UnitTextBox();
      this.unitTextBox_Input_TVTVPercentage = new UnitTextBox();
      this.newTextBox_Input_HeatpipeEff = new NewTextBox();
      this.label18 = new Label();
      this.unitTextBox_Input_ValvePinSize = new UnitTextBox();
      this.unitTextBox_Input_HeatDiameter = new UnitTextBox();
      this.unitTextBox_Input_ManifoldVolume = new UnitTextBox();
      this.label20 = new Label();
      this.label21 = new Label();
      this.label22 = new Label();
      this.unitTextBox_Input_NozzleGateSize = new UnitTextBox();
      this.unitTextBox_Input_NozzleSize = new UnitTextBox();
      this.unitTextBox_Input_ManifoldSize = new UnitTextBox();
      this.label19 = new Label();
      this.label17 = new Label();
      this.label16 = new Label();
      this.unitTextBox_Input_InCavityPressure = new UnitTextBox();
      this.label15 = new Label();
      this.unitTextBox_Input_InjPressure = new UnitTextBox();
      this.label12 = new Label();
      this.label9 = new Label();
      this.label7 = new Label();
      this.newComboBox_Input_AirTrap = new NewComboBox();
      this.label6 = new Label();
      this.newTextBox_Input_AirTrap = new NewTextBox();
      this.checkBox_Input_InjCond = new CheckBox();
      this.newComboBox_Input_DefZ = new NewComboBox();
      this.newComboBox_Input_DefY = new NewComboBox();
      this.newComboBox_Input_DefX = new NewComboBox();
      this.newComboBox_Input_DefAll = new NewComboBox();
      this.newComboBox_Input_Cooling = new NewComboBox();
      this.newComboBox_Input_SinkMark = new NewComboBox();
      this.newComboBox_Input_WeldLine = new NewComboBox();
      this.label28 = new Label();
      this.label32 = new Label();
      this.label26 = new Label();
      this.label27 = new Label();
      this.newTextBox_Input_Manager = new NewTextBox();
      this.newTextBox_Input_Engineer = new NewTextBox();
      this.newTextBox_Input_FillingFrame = new NewTextBox();
      this.newTextBox_Input_Countermeasure = new NewTextBox();
      this.newTextBox_Input_DefZ = new NewTextBox();
      this.newTextBox_Input_DefY = new NewTextBox();
      this.newTextBox_Input_DefX = new NewTextBox();
      this.newTextBox_Input_DefAll = new NewTextBox();
      this.newTextBox_Input_Cooling = new NewTextBox();
      this.newTextBox_Input_SinkMark = new NewTextBox();
      this.newTextBox_Input_WeldLine = new NewTextBox();
      this.newTextBox_Input_Item = new NewTextBox();
      this.label_Input_InjRangeVP = new Label();
      this.label_Input_InjRange4 = new Label();
      this.label_Input_InjRange3 = new Label();
      this.label10 = new Label();
      this.label11 = new Label();
      this.label_Input_InjRange2 = new Label();
      this.label38 = new Label();
      this.label43 = new Label();
      this.label_Input_TVTMTemp = new Label();
      this.label_Input_Filling4 = new Label();
      this.label_Input_Filling3 = new Label();
      this.label_TVTTime = new Label();
      this.label_Input_Filling2 = new Label();
      this.label_Input_TVTVPercentage = new Label();
      this.label_Input_Filling1 = new Label();
      this.label67 = new Label();
      this.label66 = new Label();
      this.label64 = new Label();
      this.label60 = new Label();
      this.label13 = new Label();
      this.label14 = new Label();
      this.label_Input_InjRange1 = new Label();
      this.unitTextBox_Input_InjPreRatio = new UnitTextBox();
      this.unitTextBox_Input_InjMaxClamp = new UnitTextBox();
      this.unitTextBox_Input_InjMaxPressure = new UnitTextBox();
      this.unitTextBox_Input_InjRangeVP = new UnitTextBox();
      this.unitTextBox_Input_InjRange4 = new UnitTextBox();
      this.unitTextBox_Input_InjRange3 = new UnitTextBox();
      this.unitTextBox_Input_InjScrewDia = new UnitTextBox();
      this.unitTextBox_Input_InjRange2 = new UnitTextBox();
      this.unitTextBox_Input_InjMaxRate = new UnitTextBox();
      this.unitTextBox_Input_Filling4 = new UnitTextBox();
      this.unitTextBox_Input_Filling3 = new UnitTextBox();
      this.unitTextBox_Input_Filling2 = new UnitTextBox();
      this.unitTextBox_Input_Filling1 = new UnitTextBox();
      this.unitTextBox_Input_InjRange1 = new UnitTextBox();
      this.unitTextBox_Input_InjMaxStroke = new UnitTextBox();
      this.label_Input_InjPreRatio = new Label();
      this.label_Input_InjScrewDia = new Label();
      this.label_Input_InjMaxClamp = new Label();
      this.label_Input_InjMaxRate = new Label();
      this.label_Input_InjMaxPressure = new Label();
      this.label_Input_InjMaxStroke = new Label();
      this.newComboBox_Input_InjData = new NewComboBox();
      this.label_Input_Deflection = new Label();
      this.label48 = new Label();
      this.label37 = new Label();
      this.label_Input_Result_Comment = new Label();
      this.label_Input_Sign = new Label();
      this.label_Input_Range = new Label();
      this.label61 = new Label();
      this.label_Input_Inj = new Label();
      this.tabPage_View = new TabPage();
      this.checkBox_AllCheck = new CheckBox();
      this.dataGridView_View = new DataGridView();
      this.Column6 = new DataGridViewTextBoxColumn();
      this.Column5 = new DataGridViewCheckBoxColumn();
      this.Column_Item = new DataGridViewTextBoxColumn();
      this.Column2 = new DataGridViewTextBoxColumn();
      this.Column3 = new DataGridViewTextBoxColumn();
      this.Column4 = new DataGridViewTextBoxColumn();
      this.label2 = new Label();
      this.label3 = new Label();
      this.label4 = new Label();
      this.label5 = new Label();
      this.label_View_Model = new Label();
      this.newButton_View_Select = new NewButton();
      this.newButton_View_All = new NewButton();
      this.newTextBox_View_Z = new NewTextBox();
      this.newTextBox_View_Y = new NewTextBox();
      this.newTextBox_View_X = new NewTextBox();
      this.newComboBox_View_Type = new NewComboBox();
      this.tabPage_Set = new TabPage();
      this.unitTextBox_ThicknessMax = new UnitTextBox();
      this.label_Thickness = new Label();
      this.unitTextBox_ThicknessMin = new UnitTextBox();
      this.label73 = new Label();
      this.label74 = new Label();
      this.label_ShrinkComp = new Label();
      this.newTextBox_PA66 = new NewTextBox();
      this.label70 = new Label();
      this.newTextBox_PCABS = new NewTextBox();
      this.label69 = new Label();
      this.newTextBox_PMMA = new NewTextBox();
      this.label68 = new Label();
      this.newTextBox_PP = new NewTextBox();
      this.label65 = new Label();
      this.newTextBox_ABS = new NewTextBox();
      this.label63 = new Label();
      this.newTextBox_PBT = new NewTextBox();
      this.label62 = new Label();
      this.newTextBox_PC = new NewTextBox();
      this.label59 = new Label();
      this.label58 = new Label();
      this.label_FamilyAbb = new Label();
      this.label_ShrinkSet = new Label();
      this.newTextBox_RLENS_GTemp_PMMA = new NewTextBox();
      this.newTextBox_RBEZEL_GTemp_PC = new NewTextBox();
      this.newTextBox_RHSG_GTemp_PCABS = new NewTextBox();
      this.newTextBox_FBEZEL_GTemp_PC = new NewTextBox();
      this.newTextBox_FBEZEL_GTemp_PBT = new NewTextBox();
      this.newTextBox_FHSG_GTemp_PP = new NewTextBox();
      this.label_Mold_Target_Temp = new Label();
      this.newTextBox_FLENS_GTemp_PC = new NewTextBox();
      this.label40 = new Label();
      this.label41 = new Label();
      this.label42 = new Label();
      this.label44 = new Label();
      this.label45 = new Label();
      this.label54 = new Label();
      this.label39 = new Label();
      this.label46 = new Label();
      this.label47 = new Label();
      this.label49 = new Label();
      this.label50 = new Label();
      this.label51 = new Label();
      this.label52 = new Label();
      this.label53 = new Label();
      this.label55 = new Label();
      this.label_Suji = new Label();
      this.label_Cooling_Efficiency_Setting = new Label();
      this.unitTextBox_CoolantTempCavity = new UnitTextBox();
      this.unitTextBox_ReynoldsNumber = new UnitTextBox();
      this.unitTextBox_FlowRate = new UnitTextBox();
      this.unitTextBox_CoolantTempCore = new UnitTextBox();
      this.unitTextBox_InCavityPressure1 = new UnitTextBox();
      this.unitTextBox_InCavityPressure2 = new UnitTextBox();
      this.unitTextBox_InjPressure2 = new UnitTextBox();
      this.label_Result = new Label();
      this.unitTextBox_InjPressure1 = new UnitTextBox();
      this.label1 = new Label();
      this.label23 = new Label();
      this.label24 = new Label();
      this.label_InjPressure1 = new Label();
      this.label30 = new Label();
      this.label31 = new Label();
      this.label33 = new Label();
      this.label34 = new Label();
      this.newTextBox_RLENS_Goal_PMMA = new NewTextBox();
      this.newTextBox_RBEZEL_Goal_PC = new NewTextBox();
      this.newTextBox_RHSG_Goal_PCABS = new NewTextBox();
      this.newTextBox_FBEZEL_Goal_PC = new NewTextBox();
      this.newTextBox_FBEZEL_Goal_PBT = new NewTextBox();
      this.newTextBox_FHSG_Goal_PP = new NewTextBox();
      this.newTextBox_FLENS_Goal_PC = new NewTextBox();
      this.dataGridView_ShrinkComp = new DataGridView();
      this.Column1 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn1 = new DataGridViewTextBoxColumn();
      this.Column7 = new DataGridViewTextBoxColumn();
      this.newButton_Apply = new NewButton();
      this.panel1 = new Panel();
      this.newButton_View = new NewButton();
      this.newButton_Input = new NewButton();
      this.newButton_Set = new NewButton();
      this.tabControl_Main.SuspendLayout();
      this.tabPage_Input.SuspendLayout();
      this.tabPage_View.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_View).BeginInit();
      this.tabPage_Set.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_ShrinkComp).BeginInit();
      this.panel1.SuspendLayout();
      this.SuspendLayout();
      this.tabControl_Main.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.tabControl_Main.Controls.Add((Control) this.tabPage_Input);
      this.tabControl_Main.Controls.Add((Control) this.tabPage_View);
      this.tabControl_Main.Controls.Add((Control) this.tabPage_Set);
      this.tabControl_Main.Location = new Point(-5, -23);
      this.tabControl_Main.Name = "tabControl_Main";
      this.tabControl_Main.SelectedIndex = 0;
      this.tabControl_Main.Size = new Size(580, 880);
      this.tabControl_Main.TabIndex = 0;
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_Step);
      this.tabPage_Input.Controls.Add((Control) this.label_Report);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_CEGoal);
      this.tabPage_Input.Controls.Add((Control) this.label_Target);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_CETemp2);
      this.tabPage_Input.Controls.Add((Control) this.label90);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_CETemp1);
      this.tabPage_Input.Controls.Add((Control) this.label29);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_SCVal);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_SCVal);
      this.tabPage_Input.Controls.Add((Control) this.newComboBox_Input_SCOpt);
      this.tabPage_Input.Controls.Add((Control) this.label_Cooling_Efficiency);
      this.tabPage_Input.Controls.Add((Control) this.label_Deflection_Rate);
      this.tabPage_Input.Controls.Add((Control) this.label_Option);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_TVTFTemp);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_TVTFTemp);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_TVTMTemp);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_TVTTime);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_TVTVPercentage);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_HeatpipeEff);
      this.tabPage_Input.Controls.Add((Control) this.label18);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_ValvePinSize);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_HeatDiameter);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_ManifoldVolume);
      this.tabPage_Input.Controls.Add((Control) this.label20);
      this.tabPage_Input.Controls.Add((Control) this.label21);
      this.tabPage_Input.Controls.Add((Control) this.label22);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_NozzleGateSize);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_NozzleSize);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_ManifoldSize);
      this.tabPage_Input.Controls.Add((Control) this.label19);
      this.tabPage_Input.Controls.Add((Control) this.label17);
      this.tabPage_Input.Controls.Add((Control) this.label16);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_InCavityPressure);
      this.tabPage_Input.Controls.Add((Control) this.label15);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_InjPressure);
      this.tabPage_Input.Controls.Add((Control) this.label12);
      this.tabPage_Input.Controls.Add((Control) this.label9);
      this.tabPage_Input.Controls.Add((Control) this.label7);
      this.tabPage_Input.Controls.Add((Control) this.newComboBox_Input_AirTrap);
      this.tabPage_Input.Controls.Add((Control) this.label6);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_AirTrap);
      this.tabPage_Input.Controls.Add((Control) this.checkBox_Input_InjCond);
      this.tabPage_Input.Controls.Add((Control) this.newComboBox_Input_DefZ);
      this.tabPage_Input.Controls.Add((Control) this.newComboBox_Input_DefY);
      this.tabPage_Input.Controls.Add((Control) this.newComboBox_Input_DefX);
      this.tabPage_Input.Controls.Add((Control) this.newComboBox_Input_DefAll);
      this.tabPage_Input.Controls.Add((Control) this.newComboBox_Input_Cooling);
      this.tabPage_Input.Controls.Add((Control) this.newComboBox_Input_SinkMark);
      this.tabPage_Input.Controls.Add((Control) this.newComboBox_Input_WeldLine);
      this.tabPage_Input.Controls.Add((Control) this.label28);
      this.tabPage_Input.Controls.Add((Control) this.label32);
      this.tabPage_Input.Controls.Add((Control) this.label26);
      this.tabPage_Input.Controls.Add((Control) this.label27);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_Manager);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_Engineer);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_FillingFrame);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_Countermeasure);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_DefZ);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_DefY);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_DefX);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_DefAll);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_Cooling);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_SinkMark);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_WeldLine);
      this.tabPage_Input.Controls.Add((Control) this.newTextBox_Input_Item);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_InjRangeVP);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_InjRange4);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_InjRange3);
      this.tabPage_Input.Controls.Add((Control) this.label10);
      this.tabPage_Input.Controls.Add((Control) this.label11);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_InjRange2);
      this.tabPage_Input.Controls.Add((Control) this.label38);
      this.tabPage_Input.Controls.Add((Control) this.label43);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_TVTMTemp);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Filling4);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Filling3);
      this.tabPage_Input.Controls.Add((Control) this.label_TVTTime);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Filling2);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_TVTVPercentage);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Filling1);
      this.tabPage_Input.Controls.Add((Control) this.label67);
      this.tabPage_Input.Controls.Add((Control) this.label66);
      this.tabPage_Input.Controls.Add((Control) this.label64);
      this.tabPage_Input.Controls.Add((Control) this.label60);
      this.tabPage_Input.Controls.Add((Control) this.label13);
      this.tabPage_Input.Controls.Add((Control) this.label14);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_InjRange1);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_InjPreRatio);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_InjMaxClamp);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_InjMaxPressure);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_InjRangeVP);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_InjRange4);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_InjRange3);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_InjScrewDia);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_InjRange2);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_InjMaxRate);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_Filling4);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_Filling3);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_Filling2);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_Filling1);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_InjRange1);
      this.tabPage_Input.Controls.Add((Control) this.unitTextBox_Input_InjMaxStroke);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_InjPreRatio);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_InjScrewDia);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_InjMaxClamp);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_InjMaxRate);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_InjMaxPressure);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_InjMaxStroke);
      this.tabPage_Input.Controls.Add((Control) this.newComboBox_Input_InjData);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Deflection);
      this.tabPage_Input.Controls.Add((Control) this.label48);
      this.tabPage_Input.Controls.Add((Control) this.label37);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Result_Comment);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Sign);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Range);
      this.tabPage_Input.Controls.Add((Control) this.label61);
      this.tabPage_Input.Controls.Add((Control) this.label_Input_Inj);
      this.tabPage_Input.Location = new Point(4, 24);
      this.tabPage_Input.Name = "tabPage_Input";
      this.tabPage_Input.Padding = new Padding(3);
      this.tabPage_Input.Size = new Size(572, 852);
      this.tabPage_Input.TabIndex = 0;
      this.tabPage_Input.Text = "tabPage1";
      this.tabPage_Input.UseVisualStyleBackColor = true;
      this.newTextBox_Input_Step.BackColor = Color.PapayaWhip;
      this.newTextBox_Input_Step.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_Step.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_Step.IsDigit = false;
      this.newTextBox_Input_Step.Lines = new string[0];
      this.newTextBox_Input_Step.Location = new Point(5, 232);
      this.newTextBox_Input_Step.MultiLine = false;
      this.newTextBox_Input_Step.Name = "newTextBox_Input_Step";
      this.newTextBox_Input_Step.ReadOnly = false;
      this.newTextBox_Input_Step.Size = new Size(188, 20);
      this.newTextBox_Input_Step.TabIndex = 210;
      this.newTextBox_Input_Step.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_Step.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_Input_Step.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_Step.Value = "";
      this.label_Report.BackColor = Color.Gold;
      this.label_Report.BorderStyle = BorderStyle.FixedSingle;
      this.label_Report.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Report.ForeColor = Color.MidnightBlue;
      this.label_Report.Location = new Point(5, 156);
      this.label_Report.Name = "label_Report";
      this.label_Report.Size = new Size(562, 20);
      this.label_Report.TabIndex = 209;
      this.label_Report.Text = "보고서";
      this.label_Report.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Input_CEGoal.BackColor = Color.PapayaWhip;
      this.newTextBox_Input_CEGoal.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_CEGoal.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_CEGoal.IsDigit = true;
      this.newTextBox_Input_CEGoal.Lines = new string[0];
      this.newTextBox_Input_CEGoal.Location = new Point(417, 793);
      this.newTextBox_Input_CEGoal.MultiLine = false;
      this.newTextBox_Input_CEGoal.Name = "newTextBox_Input_CEGoal";
      this.newTextBox_Input_CEGoal.ReadOnly = false;
      this.newTextBox_Input_CEGoal.Size = new Size(150, 20);
      this.newTextBox_Input_CEGoal.TabIndex = 208;
      this.newTextBox_Input_CEGoal.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_CEGoal.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_Input_CEGoal.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_CEGoal.Value = "";
      this.label_Target.BackColor = Color.Ivory;
      this.label_Target.BorderStyle = BorderStyle.FixedSingle;
      this.label_Target.Location = new Point(285, 793);
      this.label_Target.Name = "label_Target";
      this.label_Target.Size = new Size(133, 20);
      this.label_Target.TabIndex = 207;
      this.label_Target.Text = "목표(%)";
      this.label_Target.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Input_CETemp2.BackColor = Color.PapayaWhip;
      this.newTextBox_Input_CETemp2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_CETemp2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_CETemp2.IsDigit = true;
      this.newTextBox_Input_CETemp2.Lines = new string[0];
      this.newTextBox_Input_CETemp2.Location = new Point(501, 774);
      this.newTextBox_Input_CETemp2.MultiLine = false;
      this.newTextBox_Input_CETemp2.Name = "newTextBox_Input_CETemp2";
      this.newTextBox_Input_CETemp2.ReadOnly = false;
      this.newTextBox_Input_CETemp2.Size = new Size(66, 20);
      this.newTextBox_Input_CETemp2.TabIndex = 206;
      this.newTextBox_Input_CETemp2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_CETemp2.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_Input_CETemp2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_CETemp2.Value = "";
      this.label90.BackColor = Color.Ivory;
      this.label90.BorderStyle = BorderStyle.FixedSingle;
      this.label90.Location = new Point(482, 774);
      this.label90.Name = "label90";
      this.label90.Size = new Size(20, 20);
      this.label90.TabIndex = 205;
      this.label90.Text = "~";
      this.label90.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Input_CETemp1.BackColor = Color.PapayaWhip;
      this.newTextBox_Input_CETemp1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_CETemp1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_CETemp1.IsDigit = true;
      this.newTextBox_Input_CETemp1.Lines = new string[0];
      this.newTextBox_Input_CETemp1.Location = new Point(417, 774);
      this.newTextBox_Input_CETemp1.MultiLine = false;
      this.newTextBox_Input_CETemp1.Name = "newTextBox_Input_CETemp1";
      this.newTextBox_Input_CETemp1.ReadOnly = false;
      this.newTextBox_Input_CETemp1.Size = new Size(66, 20);
      this.newTextBox_Input_CETemp1.TabIndex = 204;
      this.newTextBox_Input_CETemp1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_CETemp1.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_Input_CETemp1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_CETemp1.Value = "";
      this.label29.BackColor = Color.Ivory;
      this.label29.BorderStyle = BorderStyle.FixedSingle;
      this.label29.Location = new Point(285, 774);
      this.label29.Name = "label29";
      this.label29.Size = new Size(133, 20);
      this.label29.TabIndex = 203;
      this.label29.Text = "Manifold size";
      this.label29.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Input_SCVal.BackColor = Color.PapayaWhip;
      this.newTextBox_Input_SCVal.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_SCVal.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_SCVal.IsDigit = true;
      this.newTextBox_Input_SCVal.Lines = new string[0];
      this.newTextBox_Input_SCVal.Location = new Point(141, 793);
      this.newTextBox_Input_SCVal.MultiLine = false;
      this.newTextBox_Input_SCVal.Name = "newTextBox_Input_SCVal";
      this.newTextBox_Input_SCVal.ReadOnly = true;
      this.newTextBox_Input_SCVal.Size = new Size(145, 20);
      this.newTextBox_Input_SCVal.TabIndex = 202;
      this.newTextBox_Input_SCVal.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_SCVal.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_Input_SCVal.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_SCVal.Value = "";
      this.label_Input_SCVal.BackColor = Color.Ivory;
      this.label_Input_SCVal.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_SCVal.Location = new Point(5, 793);
      this.label_Input_SCVal.Name = "label_Input_SCVal";
      this.label_Input_SCVal.Size = new Size(138, 20);
      this.label_Input_SCVal.TabIndex = 201;
      this.label_Input_SCVal.TextAlign = ContentAlignment.MiddleCenter;
      this.newComboBox_Input_SCOpt.BackColor = Color.PapayaWhip;
      this.newComboBox_Input_SCOpt.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Input_SCOpt.ComboBoxBackColor = Color.PapayaWhip;
      this.newComboBox_Input_SCOpt.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Input_SCOpt.isSameSelect = false;
      this.newComboBox_Input_SCOpt.Location = new Point(141, 774);
      this.newComboBox_Input_SCOpt.Name = "newComboBox_Input_SCOpt";
      this.newComboBox_Input_SCOpt.SelectedIndex = -1;
      this.newComboBox_Input_SCOpt.Size = new Size(145, 20);
      this.newComboBox_Input_SCOpt.TabIndex = 200;
      this.newComboBox_Input_SCOpt.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Input_SCOpt.Value = "";
      this.label_Cooling_Efficiency.BackColor = SystemColors.Control;
      this.label_Cooling_Efficiency.BorderStyle = BorderStyle.FixedSingle;
      this.label_Cooling_Efficiency.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Cooling_Efficiency.ForeColor = Color.MidnightBlue;
      this.label_Cooling_Efficiency.Location = new Point(285, 757);
      this.label_Cooling_Efficiency.Name = "label_Cooling_Efficiency";
      this.label_Cooling_Efficiency.Size = new Size(282, 18);
      this.label_Cooling_Efficiency.TabIndex = 199;
      this.label_Cooling_Efficiency.Text = "냉각 효율";
      this.label_Cooling_Efficiency.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Deflection_Rate.BackColor = SystemColors.Control;
      this.label_Deflection_Rate.BorderStyle = BorderStyle.FixedSingle;
      this.label_Deflection_Rate.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Deflection_Rate.ForeColor = Color.MidnightBlue;
      this.label_Deflection_Rate.Location = new Point(5, 757);
      this.label_Deflection_Rate.Name = "label_Deflection_Rate";
      this.label_Deflection_Rate.Size = new Size(282, 18);
      this.label_Deflection_Rate.TabIndex = 198;
      this.label_Deflection_Rate.Text = "변형률";
      this.label_Deflection_Rate.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Option.BackColor = Color.Ivory;
      this.label_Option.BorderStyle = BorderStyle.FixedSingle;
      this.label_Option.Location = new Point(5, 774);
      this.label_Option.Name = "label_Option";
      this.label_Option.Size = new Size(138, 20);
      this.label_Option.TabIndex = 197;
      this.label_Option.Text = "옵션";
      this.label_Option.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_TVTFTemp.BackColor = Color.Ivory;
      this.label_Input_TVTFTemp.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_TVTFTemp.Location = new Point(285, 624);
      this.label_Input_TVTFTemp.Name = "label_Input_TVTFTemp";
      this.label_Input_TVTFTemp.Size = new Size(142, 20);
      this.label_Input_TVTFTemp.TabIndex = 136;
      this.label_Input_TVTFTemp.Text = "Ejection Temp";
      this.label_Input_TVTFTemp.TextAlign = ContentAlignment.MiddleCenter;
      this.unitTextBox_Input_TVTFTemp.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_TVTFTemp.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_TVTFTemp.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_TVTFTemp.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_TVTFTemp.IsDigit = true;
      this.unitTextBox_Input_TVTFTemp.Location = new Point(285, 643);
      this.unitTextBox_Input_TVTFTemp.Name = "unitTextBox_Input_TVTFTemp";
      this.unitTextBox_Input_TVTFTemp.ReadOnly = false;
      this.unitTextBox_Input_TVTFTemp.Size = new Size(142, 20);
      this.unitTextBox_Input_TVTFTemp.TabIndex = 195;
      this.unitTextBox_Input_TVTFTemp.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_TVTFTemp.Unit = "℃";
      this.unitTextBox_Input_TVTFTemp.Value = "";
      this.unitTextBox_Input_TVTMTemp.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_TVTMTemp.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_TVTMTemp.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_TVTMTemp.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_TVTMTemp.IsDigit = true;
      this.unitTextBox_Input_TVTMTemp.Location = new Point(425, 643);
      this.unitTextBox_Input_TVTMTemp.Name = "unitTextBox_Input_TVTMTemp";
      this.unitTextBox_Input_TVTMTemp.ReadOnly = false;
      this.unitTextBox_Input_TVTMTemp.Size = new Size(142, 20);
      this.unitTextBox_Input_TVTMTemp.TabIndex = 196;
      this.unitTextBox_Input_TVTMTemp.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_TVTMTemp.Unit = "℃";
      this.unitTextBox_Input_TVTMTemp.Value = "";
      this.unitTextBox_Input_TVTTime.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_TVTTime.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_TVTTime.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_TVTTime.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_TVTTime.IsDigit = true;
      this.unitTextBox_Input_TVTTime.Location = new Point(145, 643);
      this.unitTextBox_Input_TVTTime.Name = "unitTextBox_Input_TVTTime";
      this.unitTextBox_Input_TVTTime.ReadOnly = false;
      this.unitTextBox_Input_TVTTime.Size = new Size(142, 20);
      this.unitTextBox_Input_TVTTime.TabIndex = 194;
      this.unitTextBox_Input_TVTTime.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_TVTTime.Unit = "s";
      this.unitTextBox_Input_TVTTime.Value = "";
      this.unitTextBox_Input_TVTVPercentage.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_TVTVPercentage.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_TVTVPercentage.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_TVTVPercentage.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_TVTVPercentage.IsDigit = true;
      this.unitTextBox_Input_TVTVPercentage.Location = new Point(5, 643);
      this.unitTextBox_Input_TVTVPercentage.Name = "unitTextBox_Input_TVTVPercentage";
      this.unitTextBox_Input_TVTVPercentage.ReadOnly = false;
      this.unitTextBox_Input_TVTVPercentage.Size = new Size(142, 20);
      this.unitTextBox_Input_TVTVPercentage.TabIndex = 193;
      this.unitTextBox_Input_TVTVPercentage.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_TVTVPercentage.Unit = "%";
      this.unitTextBox_Input_TVTVPercentage.Value = "";
      this.newTextBox_Input_HeatpipeEff.BackColor = Color.PapayaWhip;
      this.newTextBox_Input_HeatpipeEff.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_HeatpipeEff.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_HeatpipeEff.IsDigit = true;
      this.newTextBox_Input_HeatpipeEff.Lines = new string[0];
      this.newTextBox_Input_HeatpipeEff.Location = new Point(285, 529);
      this.newTextBox_Input_HeatpipeEff.MultiLine = false;
      this.newTextBox_Input_HeatpipeEff.Name = "newTextBox_Input_HeatpipeEff";
      this.newTextBox_Input_HeatpipeEff.ReadOnly = false;
      this.newTextBox_Input_HeatpipeEff.Size = new Size(142, 20);
      this.newTextBox_Input_HeatpipeEff.TabIndex = 192;
      this.newTextBox_Input_HeatpipeEff.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_HeatpipeEff.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_Input_HeatpipeEff.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_HeatpipeEff.Value = "";
      this.label18.BackColor = Color.Ivory;
      this.label18.BorderStyle = BorderStyle.FixedSingle;
      this.label18.Location = new Point(285, 472);
      this.label18.Name = "label18";
      this.label18.Size = new Size(142, 20);
      this.label18.TabIndex = 179;
      this.label18.Text = "Valve pin size";
      this.label18.TextAlign = ContentAlignment.MiddleCenter;
      this.unitTextBox_Input_ValvePinSize.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_ValvePinSize.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_ValvePinSize.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_ValvePinSize.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_ValvePinSize.IsDigit = true;
      this.unitTextBox_Input_ValvePinSize.Location = new Point(285, 491);
      this.unitTextBox_Input_ValvePinSize.Name = "unitTextBox_Input_ValvePinSize";
      this.unitTextBox_Input_ValvePinSize.ReadOnly = false;
      this.unitTextBox_Input_ValvePinSize.Size = new Size(142, 20);
      this.unitTextBox_Input_ValvePinSize.TabIndex = 183;
      this.unitTextBox_Input_ValvePinSize.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_ValvePinSize.Unit = "Ø";
      this.unitTextBox_Input_ValvePinSize.Value = "";
      this.unitTextBox_Input_HeatDiameter.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_HeatDiameter.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_HeatDiameter.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_HeatDiameter.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_HeatDiameter.IsDigit = true;
      this.unitTextBox_Input_HeatDiameter.Location = new Point(145, 529);
      this.unitTextBox_Input_HeatDiameter.Name = "unitTextBox_Input_HeatDiameter";
      this.unitTextBox_Input_HeatDiameter.ReadOnly = false;
      this.unitTextBox_Input_HeatDiameter.Size = new Size(142, 20);
      this.unitTextBox_Input_HeatDiameter.TabIndex = 190;
      this.unitTextBox_Input_HeatDiameter.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_HeatDiameter.Unit = "mm";
      this.unitTextBox_Input_HeatDiameter.Value = "";
      this.unitTextBox_Input_ManifoldVolume.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_ManifoldVolume.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_ManifoldVolume.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_ManifoldVolume.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_ManifoldVolume.IsDigit = true;
      this.unitTextBox_Input_ManifoldVolume.Location = new Point(5, 529);
      this.unitTextBox_Input_ManifoldVolume.Name = "unitTextBox_Input_ManifoldVolume";
      this.unitTextBox_Input_ManifoldVolume.ReadOnly = false;
      this.unitTextBox_Input_ManifoldVolume.Size = new Size(142, 20);
      this.unitTextBox_Input_ManifoldVolume.TabIndex = 189;
      this.unitTextBox_Input_ManifoldVolume.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_ManifoldVolume.Unit = "cm\u00B3";
      this.unitTextBox_Input_ManifoldVolume.Value = "";
      this.label20.BackColor = Color.Ivory;
      this.label20.BorderStyle = BorderStyle.FixedSingle;
      this.label20.Location = new Point(285, 510);
      this.label20.Name = "label20";
      this.label20.Size = new Size(142, 20);
      this.label20.TabIndex = 187;
      this.label20.Text = "Heat pipe effectiveness";
      this.label20.TextAlign = ContentAlignment.MiddleCenter;
      this.label21.BackColor = Color.Ivory;
      this.label21.BorderStyle = BorderStyle.FixedSingle;
      this.label21.Location = new Point(145, 510);
      this.label21.Name = "label21";
      this.label21.Size = new Size(142, 20);
      this.label21.TabIndex = 186;
      this.label21.Text = "Heat diameter";
      this.label21.TextAlign = ContentAlignment.MiddleCenter;
      this.label22.BackColor = Color.Ivory;
      this.label22.BorderStyle = BorderStyle.FixedSingle;
      this.label22.Location = new Point(5, 510);
      this.label22.Name = "label22";
      this.label22.Size = new Size(142, 20);
      this.label22.TabIndex = 185;
      this.label22.Text = "Manifold volume";
      this.label22.TextAlign = ContentAlignment.MiddleCenter;
      this.unitTextBox_Input_NozzleGateSize.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_NozzleGateSize.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_NozzleGateSize.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_NozzleGateSize.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_NozzleGateSize.IsDigit = true;
      this.unitTextBox_Input_NozzleGateSize.Location = new Point(425, 491);
      this.unitTextBox_Input_NozzleGateSize.Name = "unitTextBox_Input_NozzleGateSize";
      this.unitTextBox_Input_NozzleGateSize.ReadOnly = false;
      this.unitTextBox_Input_NozzleGateSize.Size = new Size(142, 20);
      this.unitTextBox_Input_NozzleGateSize.TabIndex = 184;
      this.unitTextBox_Input_NozzleGateSize.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_NozzleGateSize.Unit = "mm";
      this.unitTextBox_Input_NozzleGateSize.Value = "";
      this.unitTextBox_Input_NozzleSize.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_NozzleSize.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_NozzleSize.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_NozzleSize.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_NozzleSize.IsDigit = true;
      this.unitTextBox_Input_NozzleSize.Location = new Point(145, 491);
      this.unitTextBox_Input_NozzleSize.Name = "unitTextBox_Input_NozzleSize";
      this.unitTextBox_Input_NozzleSize.ReadOnly = false;
      this.unitTextBox_Input_NozzleSize.Size = new Size(142, 20);
      this.unitTextBox_Input_NozzleSize.TabIndex = 182;
      this.unitTextBox_Input_NozzleSize.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_NozzleSize.Unit = "Ø";
      this.unitTextBox_Input_NozzleSize.Value = "";
      this.unitTextBox_Input_ManifoldSize.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_ManifoldSize.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_ManifoldSize.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_ManifoldSize.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_ManifoldSize.IsDigit = true;
      this.unitTextBox_Input_ManifoldSize.Location = new Point(5, 491);
      this.unitTextBox_Input_ManifoldSize.Name = "unitTextBox_Input_ManifoldSize";
      this.unitTextBox_Input_ManifoldSize.ReadOnly = false;
      this.unitTextBox_Input_ManifoldSize.Size = new Size(142, 20);
      this.unitTextBox_Input_ManifoldSize.TabIndex = 181;
      this.unitTextBox_Input_ManifoldSize.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_ManifoldSize.Unit = "Ø";
      this.unitTextBox_Input_ManifoldSize.Value = "";
      this.label19.BackColor = Color.Ivory;
      this.label19.BorderStyle = BorderStyle.FixedSingle;
      this.label19.Location = new Point(425, 472);
      this.label19.Name = "label19";
      this.label19.Size = new Size(142, 20);
      this.label19.TabIndex = 180;
      this.label19.Text = "Nozzle gate size";
      this.label19.TextAlign = ContentAlignment.MiddleCenter;
      this.label17.BackColor = Color.Ivory;
      this.label17.BorderStyle = BorderStyle.FixedSingle;
      this.label17.Location = new Point(145, 472);
      this.label17.Name = "label17";
      this.label17.Size = new Size(142, 20);
      this.label17.TabIndex = 178;
      this.label17.Text = "Nozzle size";
      this.label17.TextAlign = ContentAlignment.MiddleCenter;
      this.label16.BackColor = Color.Ivory;
      this.label16.BorderStyle = BorderStyle.FixedSingle;
      this.label16.Location = new Point(5, 472);
      this.label16.Name = "label16";
      this.label16.Size = new Size(142, 20);
      this.label16.TabIndex = 177;
      this.label16.Text = "Manifold size";
      this.label16.TextAlign = ContentAlignment.MiddleCenter;
      this.unitTextBox_Input_InCavityPressure.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InCavityPressure.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_InCavityPressure.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InCavityPressure.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_InCavityPressure.IsDigit = true;
      this.unitTextBox_Input_InCavityPressure.Location = new Point(424, 270);
      this.unitTextBox_Input_InCavityPressure.Name = "unitTextBox_Input_InCavityPressure";
      this.unitTextBox_Input_InCavityPressure.ReadOnly = true;
      this.unitTextBox_Input_InCavityPressure.Size = new Size(143, 20);
      this.unitTextBox_Input_InCavityPressure.TabIndex = 176;
      this.unitTextBox_Input_InCavityPressure.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_InCavityPressure.Unit = "MPa";
      this.unitTextBox_Input_InCavityPressure.Value = "";
      this.label15.BackColor = Color.Ivory;
      this.label15.BorderStyle = BorderStyle.FixedSingle;
      this.label15.Location = new Point(285, 270);
      this.label15.Name = "label15";
      this.label15.Size = new Size(141, 20);
      this.label15.TabIndex = 175;
      this.label15.Text = "In-Cavity Pressure";
      this.label15.TextAlign = ContentAlignment.MiddleCenter;
      this.unitTextBox_Input_InjPressure.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InjPressure.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_InjPressure.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InjPressure.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_InjPressure.IsDigit = true;
      this.unitTextBox_Input_InjPressure.Location = new Point(145, 270);
      this.unitTextBox_Input_InjPressure.Name = "unitTextBox_Input_InjPressure";
      this.unitTextBox_Input_InjPressure.ReadOnly = true;
      this.unitTextBox_Input_InjPressure.Size = new Size(141, 20);
      this.unitTextBox_Input_InjPressure.TabIndex = 174;
      this.unitTextBox_Input_InjPressure.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_InjPressure.Unit = "MPa";
      this.unitTextBox_Input_InjPressure.Value = "";
      this.label12.BackColor = Color.Ivory;
      this.label12.BorderStyle = BorderStyle.FixedSingle;
      this.label12.Location = new Point(5, 270);
      this.label12.Name = "label12";
      this.label12.Size = new Size(141, 20);
      this.label12.TabIndex = 173;
      this.label12.Text = "Injection Pressure";
      this.label12.TextAlign = ContentAlignment.MiddleCenter;
      this.label9.BackColor = SystemColors.Control;
      this.label9.BorderStyle = BorderStyle.FixedSingle;
      this.label9.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label9.ForeColor = Color.MidnightBlue;
      this.label9.Location = new Point(5, 251);
      this.label9.Name = "label9";
      this.label9.Size = new Size(562, 20);
      this.label9.TabIndex = 172;
      this.label9.Text = "Pressure";
      this.label9.TextAlign = ContentAlignment.MiddleCenter;
      this.label7.BackColor = Color.FromArgb(242, 242, 242);
      this.label7.BorderStyle = BorderStyle.FixedSingle;
      this.label7.Location = new Point(5, 453);
      this.label7.Name = "label7";
      this.label7.Size = new Size(562, 20);
      this.label7.TabIndex = 169;
      this.label7.Text = "Runner detail and gate positions";
      this.label7.TextAlign = ContentAlignment.MiddleCenter;
      this.newComboBox_Input_AirTrap.BackColor = Color.PapayaWhip;
      this.newComboBox_Input_AirTrap.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Input_AirTrap.ComboBoxBackColor = Color.PapayaWhip;
      this.newComboBox_Input_AirTrap.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Input_AirTrap.isSameSelect = false;
      this.newComboBox_Input_AirTrap.Location = new Point(108, 365);
      this.newComboBox_Input_AirTrap.Name = "newComboBox_Input_AirTrap";
      this.newComboBox_Input_AirTrap.SelectedIndex = -1;
      this.newComboBox_Input_AirTrap.Size = new Size(165, 20);
      this.newComboBox_Input_AirTrap.TabIndex = 158;
      this.newComboBox_Input_AirTrap.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Input_AirTrap.Value = "";
      this.label6.BackColor = Color.Ivory;
      this.label6.BorderStyle = BorderStyle.FixedSingle;
      this.label6.Location = new Point(5, 365);
      this.label6.Name = "label6";
      this.label6.Size = new Size(104, 20);
      this.label6.TabIndex = 160;
      this.label6.Text = "Air Trap";
      this.label6.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Input_AirTrap.BackColor = Color.PapayaWhip;
      this.newTextBox_Input_AirTrap.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_AirTrap.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_AirTrap.IsDigit = false;
      this.newTextBox_Input_AirTrap.Lines = new string[0];
      this.newTextBox_Input_AirTrap.Location = new Point(272, 365);
      this.newTextBox_Input_AirTrap.MultiLine = false;
      this.newTextBox_Input_AirTrap.Name = "newTextBox_Input_AirTrap";
      this.newTextBox_Input_AirTrap.ReadOnly = false;
      this.newTextBox_Input_AirTrap.Size = new Size(295, 20);
      this.newTextBox_Input_AirTrap.TabIndex = 159;
      this.newTextBox_Input_AirTrap.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_AirTrap.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_Input_AirTrap.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_AirTrap.Value = "";
      this.checkBox_Input_InjCond.BackColor = Color.Gold;
      this.checkBox_Input_InjCond.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.checkBox_Input_InjCond.Location = new Point(235, 6);
      this.checkBox_Input_InjCond.Name = "checkBox_Input_InjCond";
      this.checkBox_Input_InjCond.Size = new Size(90, 17);
      this.checkBox_Input_InjCond.TabIndex = 147;
      this.checkBox_Input_InjCond.Text = "사출 조건표";
      this.checkBox_Input_InjCond.UseVisualStyleBackColor = false;
      this.newComboBox_Input_DefZ.BackColor = Color.PapayaWhip;
      this.newComboBox_Input_DefZ.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Input_DefZ.ComboBoxBackColor = Color.PapayaWhip;
      this.newComboBox_Input_DefZ.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Input_DefZ.isSameSelect = false;
      this.newComboBox_Input_DefZ.Location = new Point(61, 738);
      this.newComboBox_Input_DefZ.Name = "newComboBox_Input_DefZ";
      this.newComboBox_Input_DefZ.SelectedIndex = -1;
      this.newComboBox_Input_DefZ.Size = new Size(81, 20);
      this.newComboBox_Input_DefZ.TabIndex = 51;
      this.newComboBox_Input_DefZ.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Input_DefZ.Value = "";
      this.newComboBox_Input_DefY.BackColor = Color.PapayaWhip;
      this.newComboBox_Input_DefY.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Input_DefY.ComboBoxBackColor = Color.PapayaWhip;
      this.newComboBox_Input_DefY.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Input_DefY.isSameSelect = false;
      this.newComboBox_Input_DefY.Location = new Point(61, 719);
      this.newComboBox_Input_DefY.Name = "newComboBox_Input_DefY";
      this.newComboBox_Input_DefY.SelectedIndex = -1;
      this.newComboBox_Input_DefY.Size = new Size(81, 20);
      this.newComboBox_Input_DefY.TabIndex = 49;
      this.newComboBox_Input_DefY.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Input_DefY.Value = "";
      this.newComboBox_Input_DefX.BackColor = Color.PapayaWhip;
      this.newComboBox_Input_DefX.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Input_DefX.ComboBoxBackColor = Color.PapayaWhip;
      this.newComboBox_Input_DefX.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Input_DefX.isSameSelect = false;
      this.newComboBox_Input_DefX.Location = new Point(61, 700);
      this.newComboBox_Input_DefX.Name = "newComboBox_Input_DefX";
      this.newComboBox_Input_DefX.SelectedIndex = -1;
      this.newComboBox_Input_DefX.Size = new Size(81, 20);
      this.newComboBox_Input_DefX.TabIndex = 47;
      this.newComboBox_Input_DefX.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Input_DefX.Value = "";
      this.newComboBox_Input_DefAll.BackColor = Color.PapayaWhip;
      this.newComboBox_Input_DefAll.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Input_DefAll.ComboBoxBackColor = Color.PapayaWhip;
      this.newComboBox_Input_DefAll.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Input_DefAll.isSameSelect = false;
      this.newComboBox_Input_DefAll.Location = new Point(61, 681);
      this.newComboBox_Input_DefAll.Name = "newComboBox_Input_DefAll";
      this.newComboBox_Input_DefAll.SelectedIndex = -1;
      this.newComboBox_Input_DefAll.Size = new Size(81, 20);
      this.newComboBox_Input_DefAll.TabIndex = 45;
      this.newComboBox_Input_DefAll.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Input_DefAll.Value = "";
      this.newComboBox_Input_Cooling.BackColor = Color.PapayaWhip;
      this.newComboBox_Input_Cooling.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Input_Cooling.ComboBoxBackColor = Color.PapayaWhip;
      this.newComboBox_Input_Cooling.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Input_Cooling.isSameSelect = false;
      this.newComboBox_Input_Cooling.Location = new Point(108, 346);
      this.newComboBox_Input_Cooling.Name = "newComboBox_Input_Cooling";
      this.newComboBox_Input_Cooling.SelectedIndex = -1;
      this.newComboBox_Input_Cooling.Size = new Size(165, 20);
      this.newComboBox_Input_Cooling.TabIndex = 21;
      this.newComboBox_Input_Cooling.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Input_Cooling.Value = "";
      this.newComboBox_Input_SinkMark.BackColor = Color.PapayaWhip;
      this.newComboBox_Input_SinkMark.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Input_SinkMark.ComboBoxBackColor = Color.PapayaWhip;
      this.newComboBox_Input_SinkMark.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Input_SinkMark.isSameSelect = false;
      this.newComboBox_Input_SinkMark.Location = new Point(108, 327);
      this.newComboBox_Input_SinkMark.Name = "newComboBox_Input_SinkMark";
      this.newComboBox_Input_SinkMark.SelectedIndex = -1;
      this.newComboBox_Input_SinkMark.Size = new Size(165, 20);
      this.newComboBox_Input_SinkMark.TabIndex = 19;
      this.newComboBox_Input_SinkMark.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Input_SinkMark.Value = "";
      this.newComboBox_Input_WeldLine.BackColor = Color.PapayaWhip;
      this.newComboBox_Input_WeldLine.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Input_WeldLine.ComboBoxBackColor = Color.PapayaWhip;
      this.newComboBox_Input_WeldLine.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Input_WeldLine.isSameSelect = false;
      this.newComboBox_Input_WeldLine.Location = new Point(108, 308);
      this.newComboBox_Input_WeldLine.Name = "newComboBox_Input_WeldLine";
      this.newComboBox_Input_WeldLine.SelectedIndex = -1;
      this.newComboBox_Input_WeldLine.Size = new Size(165, 20);
      this.newComboBox_Input_WeldLine.TabIndex = 17;
      this.newComboBox_Input_WeldLine.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Input_WeldLine.Value = "";
      this.label28.BackColor = Color.Ivory;
      this.label28.BorderStyle = BorderStyle.FixedSingle;
      this.label28.Location = new Point(5, 384);
      this.label28.Name = "label28";
      this.label28.Size = new Size(562, 20);
      this.label28.TabIndex = 143;
      this.label28.Text = "최종 코멘트";
      this.label28.TextAlign = ContentAlignment.MiddleCenter;
      this.label32.BackColor = Color.Ivory;
      this.label32.BorderStyle = BorderStyle.FixedSingle;
      this.label32.Location = new Point(5, 346);
      this.label32.Name = "label32";
      this.label32.Size = new Size(104, 20);
      this.label32.TabIndex = 143;
      this.label32.Text = "Cooling";
      this.label32.TextAlign = ContentAlignment.MiddleCenter;
      this.label26.BackColor = Color.Ivory;
      this.label26.BorderStyle = BorderStyle.FixedSingle;
      this.label26.Location = new Point(5, 327);
      this.label26.Name = "label26";
      this.label26.Size = new Size(104, 20);
      this.label26.TabIndex = 144;
      this.label26.Text = "Sink Mark";
      this.label26.TextAlign = ContentAlignment.MiddleCenter;
      this.label27.BackColor = Color.Ivory;
      this.label27.BorderStyle = BorderStyle.FixedSingle;
      this.label27.Location = new Point(5, 308);
      this.label27.Name = "label27";
      this.label27.Size = new Size(104, 20);
      this.label27.TabIndex = 145;
      this.label27.Text = "Weld line";
      this.label27.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Input_Manager.BackColor = Color.PapayaWhip;
      this.newTextBox_Input_Manager.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_Manager.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_Manager.IsDigit = false;
      this.newTextBox_Input_Manager.Lines = new string[0];
      this.newTextBox_Input_Manager.Location = new Point(379, 232);
      this.newTextBox_Input_Manager.MultiLine = false;
      this.newTextBox_Input_Manager.Name = "newTextBox_Input_Manager";
      this.newTextBox_Input_Manager.ReadOnly = false;
      this.newTextBox_Input_Manager.Size = new Size(188, 20);
      this.newTextBox_Input_Manager.TabIndex = 16;
      this.newTextBox_Input_Manager.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_Manager.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_Input_Manager.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_Manager.Value = "";
      this.newTextBox_Input_Engineer.BackColor = Color.PapayaWhip;
      this.newTextBox_Input_Engineer.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_Engineer.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_Engineer.IsDigit = false;
      this.newTextBox_Input_Engineer.Lines = new string[0];
      this.newTextBox_Input_Engineer.Location = new Point(192, 232);
      this.newTextBox_Input_Engineer.MultiLine = false;
      this.newTextBox_Input_Engineer.Name = "newTextBox_Input_Engineer";
      this.newTextBox_Input_Engineer.ReadOnly = false;
      this.newTextBox_Input_Engineer.Size = new Size(188, 20);
      this.newTextBox_Input_Engineer.TabIndex = 15;
      this.newTextBox_Input_Engineer.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_Engineer.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_Input_Engineer.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_Engineer.Value = "";
      this.newTextBox_Input_FillingFrame.BackColor = Color.PapayaWhip;
      this.newTextBox_Input_FillingFrame.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_FillingFrame.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_FillingFrame.IsDigit = true;
      this.newTextBox_Input_FillingFrame.Lines = new string[0];
      this.newTextBox_Input_FillingFrame.Location = new Point(431, 586);
      this.newTextBox_Input_FillingFrame.MultiLine = false;
      this.newTextBox_Input_FillingFrame.Name = "newTextBox_Input_FillingFrame";
      this.newTextBox_Input_FillingFrame.ReadOnly = false;
      this.newTextBox_Input_FillingFrame.Size = new Size(136, 20);
      this.newTextBox_Input_FillingFrame.TabIndex = 36;
      this.newTextBox_Input_FillingFrame.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_FillingFrame.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_Input_FillingFrame.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_FillingFrame.Value = "";
      this.newTextBox_Input_Countermeasure.BackColor = Color.PapayaWhip;
      this.newTextBox_Input_Countermeasure.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_Countermeasure.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_Countermeasure.IsDigit = false;
      this.newTextBox_Input_Countermeasure.Lines = new string[0];
      this.newTextBox_Input_Countermeasure.Location = new Point(5, 403);
      this.newTextBox_Input_Countermeasure.MultiLine = true;
      this.newTextBox_Input_Countermeasure.Name = "newTextBox_Input_Countermeasure";
      this.newTextBox_Input_Countermeasure.ReadOnly = false;
      this.newTextBox_Input_Countermeasure.Size = new Size(562, 51);
      this.newTextBox_Input_Countermeasure.TabIndex = 23;
      this.newTextBox_Input_Countermeasure.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_Countermeasure.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_Input_Countermeasure.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_Countermeasure.Value = "";
      this.newTextBox_Input_DefZ.BackColor = Color.PapayaWhip;
      this.newTextBox_Input_DefZ.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_DefZ.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_DefZ.IsDigit = false;
      this.newTextBox_Input_DefZ.Lines = new string[0];
      this.newTextBox_Input_DefZ.Location = new Point(141, 738);
      this.newTextBox_Input_DefZ.MultiLine = false;
      this.newTextBox_Input_DefZ.Name = "newTextBox_Input_DefZ";
      this.newTextBox_Input_DefZ.ReadOnly = false;
      this.newTextBox_Input_DefZ.Size = new Size(426, 20);
      this.newTextBox_Input_DefZ.TabIndex = 52;
      this.newTextBox_Input_DefZ.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_DefZ.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_Input_DefZ.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_DefZ.Value = "";
      this.newTextBox_Input_DefY.BackColor = Color.PapayaWhip;
      this.newTextBox_Input_DefY.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_DefY.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_DefY.IsDigit = false;
      this.newTextBox_Input_DefY.Lines = new string[0];
      this.newTextBox_Input_DefY.Location = new Point(141, 719);
      this.newTextBox_Input_DefY.MultiLine = false;
      this.newTextBox_Input_DefY.Name = "newTextBox_Input_DefY";
      this.newTextBox_Input_DefY.ReadOnly = false;
      this.newTextBox_Input_DefY.Size = new Size(426, 20);
      this.newTextBox_Input_DefY.TabIndex = 50;
      this.newTextBox_Input_DefY.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_DefY.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_Input_DefY.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_DefY.Value = "";
      this.newTextBox_Input_DefX.BackColor = Color.PapayaWhip;
      this.newTextBox_Input_DefX.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_DefX.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_DefX.IsDigit = false;
      this.newTextBox_Input_DefX.Lines = new string[0];
      this.newTextBox_Input_DefX.Location = new Point(141, 700);
      this.newTextBox_Input_DefX.MultiLine = false;
      this.newTextBox_Input_DefX.Name = "newTextBox_Input_DefX";
      this.newTextBox_Input_DefX.ReadOnly = false;
      this.newTextBox_Input_DefX.Size = new Size(426, 20);
      this.newTextBox_Input_DefX.TabIndex = 48;
      this.newTextBox_Input_DefX.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_DefX.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_Input_DefX.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_DefX.Value = "";
      this.newTextBox_Input_DefAll.BackColor = Color.PapayaWhip;
      this.newTextBox_Input_DefAll.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_DefAll.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_DefAll.IsDigit = false;
      this.newTextBox_Input_DefAll.Lines = new string[0];
      this.newTextBox_Input_DefAll.Location = new Point(141, 681);
      this.newTextBox_Input_DefAll.MultiLine = false;
      this.newTextBox_Input_DefAll.Name = "newTextBox_Input_DefAll";
      this.newTextBox_Input_DefAll.ReadOnly = false;
      this.newTextBox_Input_DefAll.Size = new Size(426, 20);
      this.newTextBox_Input_DefAll.TabIndex = 46;
      this.newTextBox_Input_DefAll.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_DefAll.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_Input_DefAll.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_DefAll.Value = "";
      this.newTextBox_Input_Cooling.BackColor = Color.PapayaWhip;
      this.newTextBox_Input_Cooling.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_Cooling.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_Cooling.IsDigit = false;
      this.newTextBox_Input_Cooling.Lines = new string[0];
      this.newTextBox_Input_Cooling.Location = new Point(272, 346);
      this.newTextBox_Input_Cooling.MultiLine = false;
      this.newTextBox_Input_Cooling.Name = "newTextBox_Input_Cooling";
      this.newTextBox_Input_Cooling.ReadOnly = false;
      this.newTextBox_Input_Cooling.Size = new Size(295, 20);
      this.newTextBox_Input_Cooling.TabIndex = 22;
      this.newTextBox_Input_Cooling.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_Cooling.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_Input_Cooling.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_Cooling.Value = "";
      this.newTextBox_Input_SinkMark.BackColor = Color.PapayaWhip;
      this.newTextBox_Input_SinkMark.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_SinkMark.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_SinkMark.IsDigit = false;
      this.newTextBox_Input_SinkMark.Lines = new string[0];
      this.newTextBox_Input_SinkMark.Location = new Point(272, 327);
      this.newTextBox_Input_SinkMark.MultiLine = false;
      this.newTextBox_Input_SinkMark.Name = "newTextBox_Input_SinkMark";
      this.newTextBox_Input_SinkMark.ReadOnly = false;
      this.newTextBox_Input_SinkMark.Size = new Size(295, 20);
      this.newTextBox_Input_SinkMark.TabIndex = 20;
      this.newTextBox_Input_SinkMark.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_SinkMark.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_Input_SinkMark.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_SinkMark.Value = "";
      this.newTextBox_Input_WeldLine.BackColor = Color.PapayaWhip;
      this.newTextBox_Input_WeldLine.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_WeldLine.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_WeldLine.IsDigit = false;
      this.newTextBox_Input_WeldLine.Lines = new string[0];
      this.newTextBox_Input_WeldLine.Location = new Point(272, 308);
      this.newTextBox_Input_WeldLine.MultiLine = false;
      this.newTextBox_Input_WeldLine.Name = "newTextBox_Input_WeldLine";
      this.newTextBox_Input_WeldLine.ReadOnly = false;
      this.newTextBox_Input_WeldLine.Size = new Size(295, 20);
      this.newTextBox_Input_WeldLine.TabIndex = 18;
      this.newTextBox_Input_WeldLine.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_WeldLine.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_Input_WeldLine.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_WeldLine.Value = "";
      this.newTextBox_Input_Item.BackColor = Color.PapayaWhip;
      this.newTextBox_Input_Item.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Input_Item.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Input_Item.IsDigit = false;
      this.newTextBox_Input_Item.Lines = new string[0];
      this.newTextBox_Input_Item.Location = new Point(192, 194);
      this.newTextBox_Input_Item.MultiLine = false;
      this.newTextBox_Input_Item.Name = "newTextBox_Input_Item";
      this.newTextBox_Input_Item.ReadOnly = true;
      this.newTextBox_Input_Item.Size = new Size(375, 20);
      this.newTextBox_Input_Item.TabIndex = 13;
      this.newTextBox_Input_Item.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Input_Item.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_Input_Item.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Input_Item.Value = "";
      this.label_Input_InjRangeVP.BackColor = Color.Ivory;
      this.label_Input_InjRangeVP.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_InjRangeVP.Location = new Point(421, 118);
      this.label_Input_InjRangeVP.Name = "label_Input_InjRangeVP";
      this.label_Input_InjRangeVP.Size = new Size(146, 20);
      this.label_Input_InjRangeVP.TabIndex = 129;
      this.label_Input_InjRangeVP.Text = "V/P 절환 위치(Cushion)";
      this.label_Input_InjRangeVP.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_InjRange4.BackColor = Color.Ivory;
      this.label_Input_InjRange4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_InjRange4.Location = new Point(317, 118);
      this.label_Input_InjRange4.Name = "label_Input_InjRange4";
      this.label_Input_InjRange4.Size = new Size(105, 20);
      this.label_Input_InjRange4.TabIndex = 130;
      this.label_Input_InjRange4.Text = "4차(V/P전환시점)";
      this.label_Input_InjRange4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_InjRange3.BackColor = Color.Ivory;
      this.label_Input_InjRange3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_InjRange3.Location = new Point(213, 118);
      this.label_Input_InjRange3.Name = "label_Input_InjRange3";
      this.label_Input_InjRange3.Size = new Size(105, 20);
      this.label_Input_InjRange3.TabIndex = 131;
      this.label_Input_InjRange3.Text = "3차";
      this.label_Input_InjRange3.TextAlign = ContentAlignment.MiddleCenter;
      this.label10.BackColor = Color.Ivory;
      this.label10.BorderStyle = BorderStyle.FixedSingle;
      this.label10.Location = new Point(379, 213);
      this.label10.Name = "label10";
      this.label10.Size = new Size(188, 20);
      this.label10.TabIndex = 134;
      this.label10.Text = "Manager";
      this.label10.TextAlign = ContentAlignment.MiddleCenter;
      this.label11.BackColor = Color.Ivory;
      this.label11.BorderStyle = BorderStyle.FixedSingle;
      this.label11.Location = new Point(5, 213);
      this.label11.Name = "label11";
      this.label11.Size = new Size(188, 20);
      this.label11.TabIndex = 133;
      this.label11.Text = "Step";
      this.label11.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_InjRange2.BackColor = Color.Ivory;
      this.label_Input_InjRange2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_InjRange2.Location = new Point(109, 118);
      this.label_Input_InjRange2.Name = "label_Input_InjRange2";
      this.label_Input_InjRange2.Size = new Size(105, 20);
      this.label_Input_InjRange2.TabIndex = 132;
      this.label_Input_InjRange2.Text = "2차";
      this.label_Input_InjRange2.TextAlign = ContentAlignment.MiddleCenter;
      this.label38.BackColor = Color.Ivory;
      this.label38.BorderStyle = BorderStyle.FixedSingle;
      this.label38.Location = new Point(5, 567);
      this.label38.Name = "label38";
      this.label38.Size = new Size(71, 39);
      this.label38.TabIndex = 136;
      this.label38.Text = "Filling \r\nVolume(%)";
      this.label38.TextAlign = ContentAlignment.MiddleCenter;
      this.label43.BackColor = Color.Ivory;
      this.label43.BorderStyle = BorderStyle.FixedSingle;
      this.label43.Location = new Point(431, 567);
      this.label43.Name = "label43";
      this.label43.Size = new Size(136, 20);
      this.label43.TabIndex = 136;
      this.label43.Text = "Animation\r\nFrame";
      this.label43.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_TVTMTemp.BackColor = Color.Ivory;
      this.label_Input_TVTMTemp.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_TVTMTemp.Location = new Point(425, 624);
      this.label_Input_TVTMTemp.Name = "label_Input_TVTMTemp";
      this.label_Input_TVTMTemp.Size = new Size(142, 20);
      this.label_Input_TVTMTemp.TabIndex = 136;
      this.label_Input_TVTMTemp.Text = "Mold Temp";
      this.label_Input_TVTMTemp.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_Filling4.BackColor = Color.Ivory;
      this.label_Input_Filling4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Filling4.Location = new Point(342, 567);
      this.label_Input_Filling4.Name = "label_Input_Filling4";
      this.label_Input_Filling4.Size = new Size(90, 20);
      this.label_Input_Filling4.TabIndex = 136;
      this.label_Input_Filling4.Text = "4장";
      this.label_Input_Filling4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_Filling3.BackColor = Color.Ivory;
      this.label_Input_Filling3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Filling3.Location = new Point(253, 567);
      this.label_Input_Filling3.Name = "label_Input_Filling3";
      this.label_Input_Filling3.Size = new Size(90, 20);
      this.label_Input_Filling3.TabIndex = 136;
      this.label_Input_Filling3.Text = "3장";
      this.label_Input_Filling3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_TVTTime.BackColor = Color.Ivory;
      this.label_TVTTime.BorderStyle = BorderStyle.FixedSingle;
      this.label_TVTTime.Location = new Point(145, 624);
      this.label_TVTTime.Name = "label_TVTTime";
      this.label_TVTTime.Size = new Size(142, 20);
      this.label_TVTTime.TabIndex = 136;
      this.label_TVTTime.Text = "시간";
      this.label_TVTTime.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_Filling2.BackColor = Color.Ivory;
      this.label_Input_Filling2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Filling2.Location = new Point(164, 567);
      this.label_Input_Filling2.Name = "label_Input_Filling2";
      this.label_Input_Filling2.Size = new Size(90, 20);
      this.label_Input_Filling2.TabIndex = 136;
      this.label_Input_Filling2.Text = "2장";
      this.label_Input_Filling2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_TVTVPercentage.BackColor = Color.Ivory;
      this.label_Input_TVTVPercentage.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_TVTVPercentage.Location = new Point(5, 624);
      this.label_Input_TVTVPercentage.Name = "label_Input_TVTVPercentage";
      this.label_Input_TVTVPercentage.Size = new Size(142, 20);
      this.label_Input_TVTVPercentage.TabIndex = 136;
      this.label_Input_TVTVPercentage.Text = "Volume Percentage";
      this.label_Input_TVTVPercentage.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_Filling1.BackColor = Color.Ivory;
      this.label_Input_Filling1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Filling1.Location = new Point(75, 567);
      this.label_Input_Filling1.Name = "label_Input_Filling1";
      this.label_Input_Filling1.Size = new Size(90, 20);
      this.label_Input_Filling1.TabIndex = 136;
      this.label_Input_Filling1.Text = "1장";
      this.label_Input_Filling1.TextAlign = ContentAlignment.MiddleCenter;
      this.label67.BackColor = Color.Ivory;
      this.label67.BorderStyle = BorderStyle.FixedSingle;
      this.label67.Location = new Point(5, 738);
      this.label67.Name = "label67";
      this.label67.Size = new Size(57, 20);
      this.label67.TabIndex = 136;
      this.label67.Text = "Z";
      this.label67.TextAlign = ContentAlignment.MiddleCenter;
      this.label66.BackColor = Color.Ivory;
      this.label66.BorderStyle = BorderStyle.FixedSingle;
      this.label66.Location = new Point(5, 719);
      this.label66.Name = "label66";
      this.label66.Size = new Size(57, 20);
      this.label66.TabIndex = 136;
      this.label66.Text = "Y";
      this.label66.TextAlign = ContentAlignment.MiddleCenter;
      this.label64.BackColor = Color.Ivory;
      this.label64.BorderStyle = BorderStyle.FixedSingle;
      this.label64.Location = new Point(5, 700);
      this.label64.Name = "label64";
      this.label64.Size = new Size(57, 20);
      this.label64.TabIndex = 136;
      this.label64.Text = "X";
      this.label64.TextAlign = ContentAlignment.MiddleCenter;
      this.label60.BackColor = Color.Ivory;
      this.label60.BorderStyle = BorderStyle.FixedSingle;
      this.label60.Location = new Point(5, 681);
      this.label60.Name = "label60";
      this.label60.Size = new Size(57, 20);
      this.label60.TabIndex = 136;
      this.label60.Text = "All";
      this.label60.TextAlign = ContentAlignment.MiddleCenter;
      this.label13.BackColor = Color.Ivory;
      this.label13.BorderStyle = BorderStyle.FixedSingle;
      this.label13.Location = new Point(192, 213);
      this.label13.Name = "label13";
      this.label13.Size = new Size(188, 20);
      this.label13.TabIndex = 137;
      this.label13.Text = "Engineer";
      this.label13.TextAlign = ContentAlignment.MiddleCenter;
      this.label14.BackColor = Color.Ivory;
      this.label14.BorderStyle = BorderStyle.FixedSingle;
      this.label14.Location = new Point(5, 194);
      this.label14.Name = "label14";
      this.label14.Size = new Size(189, 20);
      this.label14.TabIndex = 136;
      this.label14.Text = "Item";
      this.label14.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_InjRange1.BackColor = Color.Ivory;
      this.label_Input_InjRange1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_InjRange1.Location = new Point(5, 118);
      this.label_Input_InjRange1.Name = "label_Input_InjRange1";
      this.label_Input_InjRange1.Size = new Size(105, 20);
      this.label_Input_InjRange1.TabIndex = 135;
      this.label_Input_InjRange1.Text = "1차";
      this.label_Input_InjRange1.TextAlign = ContentAlignment.MiddleCenter;
      this.unitTextBox_Input_InjPreRatio.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InjPreRatio.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_InjPreRatio.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InjPreRatio.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_InjPreRatio.IsDigit = true;
      this.unitTextBox_Input_InjPreRatio.Location = new Point(470, 80);
      this.unitTextBox_Input_InjPreRatio.Name = "unitTextBox_Input_InjPreRatio";
      this.unitTextBox_Input_InjPreRatio.ReadOnly = false;
      this.unitTextBox_Input_InjPreRatio.Size = new Size(97, 20);
      this.unitTextBox_Input_InjPreRatio.TabIndex = 7;
      this.unitTextBox_Input_InjPreRatio.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_InjPreRatio.Unit = "배";
      this.unitTextBox_Input_InjPreRatio.Value = "";
      this.unitTextBox_Input_InjMaxClamp.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InjMaxClamp.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_InjMaxClamp.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InjMaxClamp.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_InjMaxClamp.IsDigit = true;
      this.unitTextBox_Input_InjMaxClamp.Location = new Point(284, 80);
      this.unitTextBox_Input_InjMaxClamp.Name = "unitTextBox_Input_InjMaxClamp";
      this.unitTextBox_Input_InjMaxClamp.ReadOnly = false;
      this.unitTextBox_Input_InjMaxClamp.Size = new Size(94, 20);
      this.unitTextBox_Input_InjMaxClamp.TabIndex = 5;
      this.unitTextBox_Input_InjMaxClamp.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_InjMaxClamp.Unit = "t";
      this.unitTextBox_Input_InjMaxClamp.Value = "";
      this.unitTextBox_Input_InjMaxPressure.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InjMaxPressure.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_InjMaxPressure.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InjMaxPressure.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_InjMaxPressure.IsDigit = true;
      this.unitTextBox_Input_InjMaxPressure.Location = new Point(98, 80);
      this.unitTextBox_Input_InjMaxPressure.Name = "unitTextBox_Input_InjMaxPressure";
      this.unitTextBox_Input_InjMaxPressure.ReadOnly = false;
      this.unitTextBox_Input_InjMaxPressure.Size = new Size(94, 20);
      this.unitTextBox_Input_InjMaxPressure.TabIndex = 3;
      this.unitTextBox_Input_InjMaxPressure.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_InjMaxPressure.Unit = "MP";
      this.unitTextBox_Input_InjMaxPressure.Value = "";
      this.unitTextBox_Input_InjRangeVP.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InjRangeVP.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_InjRangeVP.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InjRangeVP.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_InjRangeVP.IsDigit = true;
      this.unitTextBox_Input_InjRangeVP.Location = new Point(421, 137);
      this.unitTextBox_Input_InjRangeVP.Name = "unitTextBox_Input_InjRangeVP";
      this.unitTextBox_Input_InjRangeVP.ReadOnly = false;
      this.unitTextBox_Input_InjRangeVP.Size = new Size(146, 20);
      this.unitTextBox_Input_InjRangeVP.TabIndex = 12;
      this.unitTextBox_Input_InjRangeVP.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_InjRangeVP.Unit = "m";
      this.unitTextBox_Input_InjRangeVP.Value = "";
      this.unitTextBox_Input_InjRange4.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InjRange4.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_InjRange4.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InjRange4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_InjRange4.IsDigit = true;
      this.unitTextBox_Input_InjRange4.Location = new Point(317, 137);
      this.unitTextBox_Input_InjRange4.Name = "unitTextBox_Input_InjRange4";
      this.unitTextBox_Input_InjRange4.ReadOnly = false;
      this.unitTextBox_Input_InjRange4.Size = new Size(105, 20);
      this.unitTextBox_Input_InjRange4.TabIndex = 11;
      this.unitTextBox_Input_InjRange4.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_InjRange4.Unit = "%";
      this.unitTextBox_Input_InjRange4.Value = "";
      this.unitTextBox_Input_InjRange3.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InjRange3.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_InjRange3.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InjRange3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_InjRange3.IsDigit = true;
      this.unitTextBox_Input_InjRange3.Location = new Point(213, 137);
      this.unitTextBox_Input_InjRange3.Name = "unitTextBox_Input_InjRange3";
      this.unitTextBox_Input_InjRange3.ReadOnly = false;
      this.unitTextBox_Input_InjRange3.Size = new Size(105, 20);
      this.unitTextBox_Input_InjRange3.TabIndex = 10;
      this.unitTextBox_Input_InjRange3.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_InjRange3.Unit = "%";
      this.unitTextBox_Input_InjRange3.Value = "";
      this.unitTextBox_Input_InjScrewDia.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InjScrewDia.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_InjScrewDia.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InjScrewDia.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_InjScrewDia.IsDigit = true;
      this.unitTextBox_Input_InjScrewDia.Location = new Point(470, 61);
      this.unitTextBox_Input_InjScrewDia.Name = "unitTextBox_Input_InjScrewDia";
      this.unitTextBox_Input_InjScrewDia.ReadOnly = false;
      this.unitTextBox_Input_InjScrewDia.Size = new Size(97, 20);
      this.unitTextBox_Input_InjScrewDia.TabIndex = 6;
      this.unitTextBox_Input_InjScrewDia.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_InjScrewDia.Unit = "mm";
      this.unitTextBox_Input_InjScrewDia.Value = "";
      this.unitTextBox_Input_InjRange2.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InjRange2.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_InjRange2.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InjRange2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_InjRange2.IsDigit = true;
      this.unitTextBox_Input_InjRange2.Location = new Point(109, 137);
      this.unitTextBox_Input_InjRange2.Name = "unitTextBox_Input_InjRange2";
      this.unitTextBox_Input_InjRange2.ReadOnly = false;
      this.unitTextBox_Input_InjRange2.Size = new Size(105, 20);
      this.unitTextBox_Input_InjRange2.TabIndex = 9;
      this.unitTextBox_Input_InjRange2.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_InjRange2.Unit = "%";
      this.unitTextBox_Input_InjRange2.Value = "";
      this.unitTextBox_Input_InjMaxRate.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InjMaxRate.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_InjMaxRate.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InjMaxRate.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_InjMaxRate.IsDigit = true;
      this.unitTextBox_Input_InjMaxRate.Location = new Point(284, 61);
      this.unitTextBox_Input_InjMaxRate.Name = "unitTextBox_Input_InjMaxRate";
      this.unitTextBox_Input_InjMaxRate.ReadOnly = false;
      this.unitTextBox_Input_InjMaxRate.Size = new Size(94, 20);
      this.unitTextBox_Input_InjMaxRate.TabIndex = 4;
      this.unitTextBox_Input_InjMaxRate.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_InjMaxRate.Unit = "cm\u00B3/s";
      this.unitTextBox_Input_InjMaxRate.Value = "";
      this.unitTextBox_Input_Filling4.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_Filling4.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_Filling4.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_Filling4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_Filling4.IsDigit = true;
      this.unitTextBox_Input_Filling4.Location = new Point(342, 586);
      this.unitTextBox_Input_Filling4.Name = "unitTextBox_Input_Filling4";
      this.unitTextBox_Input_Filling4.ReadOnly = false;
      this.unitTextBox_Input_Filling4.Size = new Size(90, 20);
      this.unitTextBox_Input_Filling4.TabIndex = 31;
      this.unitTextBox_Input_Filling4.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_Filling4.Unit = "%";
      this.unitTextBox_Input_Filling4.Value = "";
      this.unitTextBox_Input_Filling3.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_Filling3.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_Filling3.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_Filling3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_Filling3.IsDigit = true;
      this.unitTextBox_Input_Filling3.Location = new Point(253, 586);
      this.unitTextBox_Input_Filling3.Name = "unitTextBox_Input_Filling3";
      this.unitTextBox_Input_Filling3.ReadOnly = false;
      this.unitTextBox_Input_Filling3.Size = new Size(90, 20);
      this.unitTextBox_Input_Filling3.TabIndex = 30;
      this.unitTextBox_Input_Filling3.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_Filling3.Unit = "%";
      this.unitTextBox_Input_Filling3.Value = "";
      this.unitTextBox_Input_Filling2.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_Filling2.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_Filling2.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_Filling2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_Filling2.IsDigit = true;
      this.unitTextBox_Input_Filling2.Location = new Point(164, 586);
      this.unitTextBox_Input_Filling2.Name = "unitTextBox_Input_Filling2";
      this.unitTextBox_Input_Filling2.ReadOnly = false;
      this.unitTextBox_Input_Filling2.Size = new Size(90, 20);
      this.unitTextBox_Input_Filling2.TabIndex = 29;
      this.unitTextBox_Input_Filling2.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_Filling2.Unit = "%";
      this.unitTextBox_Input_Filling2.Value = "";
      this.unitTextBox_Input_Filling1.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_Filling1.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_Filling1.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_Filling1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_Filling1.IsDigit = true;
      this.unitTextBox_Input_Filling1.Location = new Point(75, 586);
      this.unitTextBox_Input_Filling1.Name = "unitTextBox_Input_Filling1";
      this.unitTextBox_Input_Filling1.ReadOnly = false;
      this.unitTextBox_Input_Filling1.Size = new Size(90, 20);
      this.unitTextBox_Input_Filling1.TabIndex = 28;
      this.unitTextBox_Input_Filling1.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_Filling1.Unit = "%";
      this.unitTextBox_Input_Filling1.Value = "";
      this.unitTextBox_Input_InjRange1.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InjRange1.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_InjRange1.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InjRange1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_InjRange1.IsDigit = true;
      this.unitTextBox_Input_InjRange1.Location = new Point(5, 137);
      this.unitTextBox_Input_InjRange1.Name = "unitTextBox_Input_InjRange1";
      this.unitTextBox_Input_InjRange1.ReadOnly = false;
      this.unitTextBox_Input_InjRange1.Size = new Size(105, 20);
      this.unitTextBox_Input_InjRange1.TabIndex = 8;
      this.unitTextBox_Input_InjRange1.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_InjRange1.Unit = "%";
      this.unitTextBox_Input_InjRange1.Value = "";
      this.unitTextBox_Input_InjMaxStroke.BackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InjMaxStroke.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_Input_InjMaxStroke.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_Input_InjMaxStroke.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_Input_InjMaxStroke.IsDigit = true;
      this.unitTextBox_Input_InjMaxStroke.Location = new Point(98, 61);
      this.unitTextBox_Input_InjMaxStroke.Name = "unitTextBox_Input_InjMaxStroke";
      this.unitTextBox_Input_InjMaxStroke.ReadOnly = false;
      this.unitTextBox_Input_InjMaxStroke.Size = new Size(94, 20);
      this.unitTextBox_Input_InjMaxStroke.TabIndex = 2;
      this.unitTextBox_Input_InjMaxStroke.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_Input_InjMaxStroke.Unit = "mm";
      this.unitTextBox_Input_InjMaxStroke.Value = "";
      this.label_Input_InjPreRatio.BackColor = Color.Ivory;
      this.label_Input_InjPreRatio.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_InjPreRatio.Location = new Point(377, 80);
      this.label_Input_InjPreRatio.Name = "label_Input_InjPreRatio";
      this.label_Input_InjPreRatio.Size = new Size(94, 20);
      this.label_Input_InjPreRatio.TabIndex = 112;
      this.label_Input_InjPreRatio.Text = "증압비";
      this.label_Input_InjPreRatio.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_InjScrewDia.BackColor = Color.Ivory;
      this.label_Input_InjScrewDia.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_InjScrewDia.Location = new Point(377, 61);
      this.label_Input_InjScrewDia.Name = "label_Input_InjScrewDia";
      this.label_Input_InjScrewDia.Size = new Size(94, 20);
      this.label_Input_InjScrewDia.TabIndex = 113;
      this.label_Input_InjScrewDia.Text = "스크류 지름";
      this.label_Input_InjScrewDia.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_InjMaxClamp.BackColor = Color.Ivory;
      this.label_Input_InjMaxClamp.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_InjMaxClamp.Location = new Point(191, 80);
      this.label_Input_InjMaxClamp.Name = "label_Input_InjMaxClamp";
      this.label_Input_InjMaxClamp.Size = new Size(94, 20);
      this.label_Input_InjMaxClamp.TabIndex = 114;
      this.label_Input_InjMaxClamp.Text = "최대 형체력";
      this.label_Input_InjMaxClamp.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_InjMaxRate.BackColor = Color.Ivory;
      this.label_Input_InjMaxRate.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_InjMaxRate.Location = new Point(191, 61);
      this.label_Input_InjMaxRate.Name = "label_Input_InjMaxRate";
      this.label_Input_InjMaxRate.Size = new Size(94, 20);
      this.label_Input_InjMaxRate.TabIndex = 115;
      this.label_Input_InjMaxRate.Text = "최대 사출률";
      this.label_Input_InjMaxRate.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_InjMaxPressure.BackColor = Color.Ivory;
      this.label_Input_InjMaxPressure.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_InjMaxPressure.Location = new Point(5, 80);
      this.label_Input_InjMaxPressure.Name = "label_Input_InjMaxPressure";
      this.label_Input_InjMaxPressure.Size = new Size(94, 20);
      this.label_Input_InjMaxPressure.TabIndex = 116;
      this.label_Input_InjMaxPressure.Text = "최대 사출압력";
      this.label_Input_InjMaxPressure.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_InjMaxStroke.BackColor = Color.Ivory;
      this.label_Input_InjMaxStroke.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_InjMaxStroke.Location = new Point(5, 61);
      this.label_Input_InjMaxStroke.Name = "label_Input_InjMaxStroke";
      this.label_Input_InjMaxStroke.Size = new Size(94, 20);
      this.label_Input_InjMaxStroke.TabIndex = 117;
      this.label_Input_InjMaxStroke.Text = "최대 스트로크";
      this.label_Input_InjMaxStroke.TextAlign = ContentAlignment.MiddleCenter;
      this.newComboBox_Input_InjData.BackColor = Color.PapayaWhip;
      this.newComboBox_Input_InjData.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Input_InjData.ComboBoxBackColor = Color.PapayaWhip;
      this.newComboBox_Input_InjData.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Input_InjData.isSameSelect = false;
      this.newComboBox_Input_InjData.Location = new Point(5, 42);
      this.newComboBox_Input_InjData.Name = "newComboBox_Input_InjData";
      this.newComboBox_Input_InjData.SelectedIndex = -1;
      this.newComboBox_Input_InjData.Size = new Size(562, 20);
      this.newComboBox_Input_InjData.TabIndex = 1;
      this.newComboBox_Input_InjData.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Input_InjData.Value = "";
      this.newComboBox_Input_InjData.SelectedIndexChanged += new EventHandler(this.newComboBox_Input_InjData_SelectedIndexChanged);
      this.label_Input_Deflection.BackColor = SystemColors.Control;
      this.label_Input_Deflection.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Deflection.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Input_Deflection.ForeColor = Color.MidnightBlue;
      this.label_Input_Deflection.Location = new Point(5, 662);
      this.label_Input_Deflection.Name = "label_Input_Deflection";
      this.label_Input_Deflection.Size = new Size(562, 20);
      this.label_Input_Deflection.TabIndex = 110;
      this.label_Input_Deflection.Text = "변형";
      this.label_Input_Deflection.TextAlign = ContentAlignment.MiddleCenter;
      this.label48.BackColor = SystemColors.Control;
      this.label48.BorderStyle = BorderStyle.FixedSingle;
      this.label48.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label48.ForeColor = Color.MidnightBlue;
      this.label48.Location = new Point(5, 605);
      this.label48.Name = "label48";
      this.label48.Size = new Size(562, 20);
      this.label48.TabIndex = 110;
      this.label48.Text = "Temp/Vol/Time";
      this.label48.TextAlign = ContentAlignment.MiddleCenter;
      this.label37.BackColor = SystemColors.Control;
      this.label37.BorderStyle = BorderStyle.FixedSingle;
      this.label37.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label37.ForeColor = Color.MidnightBlue;
      this.label37.Location = new Point(5, 548);
      this.label37.Name = "label37";
      this.label37.Size = new Size(562, 20);
      this.label37.TabIndex = 110;
      this.label37.Text = "Filling Pattern";
      this.label37.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_Result_Comment.BackColor = SystemColors.Control;
      this.label_Input_Result_Comment.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Result_Comment.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Input_Result_Comment.ForeColor = Color.MidnightBlue;
      this.label_Input_Result_Comment.Location = new Point(5, 289);
      this.label_Input_Result_Comment.Name = "label_Input_Result_Comment";
      this.label_Input_Result_Comment.Size = new Size(562, 20);
      this.label_Input_Result_Comment.TabIndex = 110;
      this.label_Input_Result_Comment.Text = "결과 코멘트 작성";
      this.label_Input_Result_Comment.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_Sign.BackColor = SystemColors.Control;
      this.label_Input_Sign.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Sign.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Input_Sign.ForeColor = Color.MidnightBlue;
      this.label_Input_Sign.Location = new Point(5, 175);
      this.label_Input_Sign.Name = "label_Input_Sign";
      this.label_Input_Sign.Size = new Size(562, 20);
      this.label_Input_Sign.TabIndex = 110;
      this.label_Input_Sign.Text = "표지";
      this.label_Input_Sign.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_Range.BackColor = SystemColors.Control;
      this.label_Input_Range.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Range.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Input_Range.ForeColor = Color.MidnightBlue;
      this.label_Input_Range.Location = new Point(5, 99);
      this.label_Input_Range.Name = "label_Input_Range";
      this.label_Input_Range.Size = new Size(562, 20);
      this.label_Input_Range.TabIndex = 109;
      this.label_Input_Range.Text = "사출 구간";
      this.label_Input_Range.TextAlign = ContentAlignment.MiddleCenter;
      this.label61.BackColor = Color.Gold;
      this.label61.BorderStyle = BorderStyle.FixedSingle;
      this.label61.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label61.ForeColor = SystemColors.ControlText;
      this.label61.Location = new Point(5, 4);
      this.label61.Name = "label61";
      this.label61.Size = new Size(562, 20);
      this.label61.TabIndex = 108;
      this.label61.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Input_Inj.BackColor = SystemColors.Control;
      this.label_Input_Inj.BorderStyle = BorderStyle.FixedSingle;
      this.label_Input_Inj.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Input_Inj.ForeColor = SystemColors.ControlText;
      this.label_Input_Inj.Location = new Point(5, 23);
      this.label_Input_Inj.Name = "label_Input_Inj";
      this.label_Input_Inj.Size = new Size(562, 20);
      this.label_Input_Inj.TabIndex = 108;
      this.label_Input_Inj.Text = "사출기 정보";
      this.label_Input_Inj.TextAlign = ContentAlignment.MiddleCenter;
      this.tabPage_View.Controls.Add((Control) this.checkBox_AllCheck);
      this.tabPage_View.Controls.Add((Control) this.dataGridView_View);
      this.tabPage_View.Controls.Add((Control) this.label2);
      this.tabPage_View.Controls.Add((Control) this.label3);
      this.tabPage_View.Controls.Add((Control) this.label4);
      this.tabPage_View.Controls.Add((Control) this.label5);
      this.tabPage_View.Controls.Add((Control) this.label_View_Model);
      this.tabPage_View.Controls.Add((Control) this.newButton_View_Select);
      this.tabPage_View.Controls.Add((Control) this.newButton_View_All);
      this.tabPage_View.Controls.Add((Control) this.newTextBox_View_Z);
      this.tabPage_View.Controls.Add((Control) this.newTextBox_View_Y);
      this.tabPage_View.Controls.Add((Control) this.newTextBox_View_X);
      this.tabPage_View.Controls.Add((Control) this.newComboBox_View_Type);
      this.tabPage_View.Location = new Point(4, 22);
      this.tabPage_View.Name = "tabPage_View";
      this.tabPage_View.Padding = new Padding(3);
      this.tabPage_View.Size = new Size(572, 854);
      this.tabPage_View.TabIndex = 1;
      this.tabPage_View.Text = "tabPage2";
      this.tabPage_View.UseVisualStyleBackColor = true;
      this.checkBox_AllCheck.AutoSize = true;
      this.checkBox_AllCheck.Location = new Point(9, 69);
      this.checkBox_AllCheck.Name = "checkBox_AllCheck";
      this.checkBox_AllCheck.Size = new Size(15, 14);
      this.checkBox_AllCheck.TabIndex = 118;
      this.checkBox_AllCheck.UseVisualStyleBackColor = true;
      this.checkBox_AllCheck.CheckedChanged += new EventHandler(this.checkBox_AllCheck_CheckedChanged);
      this.dataGridView_View.AllowUserToAddRows = false;
      this.dataGridView_View.AllowUserToDeleteRows = false;
      this.dataGridView_View.AllowUserToResizeColumns = false;
      this.dataGridView_View.AllowUserToResizeRows = false;
      this.dataGridView_View.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_View.BackgroundColor = Color.White;
      this.dataGridView_View.BorderStyle = BorderStyle.None;
      gridViewCellStyle1.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle1.BackColor = Color.FromArgb(242, 242, 242);
      gridViewCellStyle1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle1.ForeColor = SystemColors.WindowText;
      gridViewCellStyle1.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle1.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle1.WrapMode = DataGridViewTriState.True;
      this.dataGridView_View.ColumnHeadersDefaultCellStyle = gridViewCellStyle1;
      this.dataGridView_View.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_View.Columns.AddRange((DataGridViewColumn) this.Column6, (DataGridViewColumn) this.Column5, (DataGridViewColumn) this.Column_Item, (DataGridViewColumn) this.Column2, (DataGridViewColumn) this.Column3, (DataGridViewColumn) this.Column4);
      this.dataGridView_View.EnableHeadersVisualStyles = false;
      this.dataGridView_View.Location = new Point(5, 64);
      this.dataGridView_View.Name = "dataGridView_View";
      this.dataGridView_View.RowHeadersVisible = false;
      this.dataGridView_View.RowTemplate.Height = 23;
      this.dataGridView_View.Size = new Size(562, 765);
      this.dataGridView_View.TabIndex = 117;
      this.Column6.HeaderText = "";
      this.Column6.Name = "Column6";
      this.Column6.Visible = false;
      this.Column5.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      this.Column5.HeaderText = "";
      this.Column5.Name = "Column5";
      this.Column5.Width = 20;
      gridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
      this.Column_Item.DefaultCellStyle = gridViewCellStyle2;
      this.Column_Item.HeaderText = "항목";
      this.Column_Item.Name = "Column_Item";
      this.Column_Item.ReadOnly = true;
      this.Column2.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      gridViewCellStyle3.Alignment = DataGridViewContentAlignment.MiddleCenter;
      this.Column2.DefaultCellStyle = gridViewCellStyle3;
      this.Column2.HeaderText = "X";
      this.Column2.Name = "Column2";
      this.Column2.Width = 60;
      this.Column3.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      gridViewCellStyle4.Alignment = DataGridViewContentAlignment.MiddleCenter;
      this.Column3.DefaultCellStyle = gridViewCellStyle4;
      this.Column3.HeaderText = "Y";
      this.Column3.Name = "Column3";
      this.Column3.Width = 60;
      this.Column4.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      gridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleCenter;
      this.Column4.DefaultCellStyle = gridViewCellStyle5;
      this.Column4.HeaderText = "Z";
      this.Column4.Name = "Column4";
      this.Column4.Width = 60;
      this.label2.BackColor = Color.FromArgb(242, 242, 242);
      this.label2.BorderStyle = BorderStyle.FixedSingle;
      this.label2.Location = new Point(341, 21);
      this.label2.Name = "label2";
      this.label2.Size = new Size(88, 22);
      this.label2.TabIndex = 110;
      this.label2.Text = "Z";
      this.label2.TextAlign = ContentAlignment.MiddleCenter;
      this.label3.BackColor = Color.FromArgb(242, 242, 242);
      this.label3.BorderStyle = BorderStyle.FixedSingle;
      this.label3.Location = new Point(254, 21);
      this.label3.Name = "label3";
      this.label3.Size = new Size(88, 22);
      this.label3.TabIndex = 111;
      this.label3.Text = "Y";
      this.label3.TextAlign = ContentAlignment.MiddleCenter;
      this.label4.BackColor = Color.FromArgb(242, 242, 242);
      this.label4.BorderStyle = BorderStyle.FixedSingle;
      this.label4.Location = new Point(5, 21);
      this.label4.Name = "label4";
      this.label4.Size = new Size(163, 22);
      this.label4.TabIndex = 112;
      this.label4.Text = "View Type";
      this.label4.TextAlign = ContentAlignment.MiddleCenter;
      this.label5.BackColor = Color.FromArgb(242, 242, 242);
      this.label5.BorderStyle = BorderStyle.FixedSingle;
      this.label5.Location = new Point(167, 21);
      this.label5.Name = "label5";
      this.label5.Size = new Size(88, 22);
      this.label5.TabIndex = 113;
      this.label5.Text = "X";
      this.label5.TextAlign = ContentAlignment.MiddleCenter;
      this.label_View_Model.BackColor = Color.Gold;
      this.label_View_Model.BorderStyle = BorderStyle.FixedSingle;
      this.label_View_Model.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_View_Model.ForeColor = Color.MidnightBlue;
      this.label_View_Model.Location = new Point(5, 2);
      this.label_View_Model.Name = "label_View_Model";
      this.label_View_Model.Size = new Size(562, 20);
      this.label_View_Model.TabIndex = 109;
      this.label_View_Model.Text = "모델 Viewer 회전 각도";
      this.label_View_Model.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_View_Select.BackColor = SystemColors.Menu;
      this.newButton_View_Select.ButtonBackColor = SystemColors.Menu;
      this.newButton_View_Select.ButtonText = "선택 적용";
      this.newButton_View_Select.FlatBorderSize = 1;
      this.newButton_View_Select.FlatStyle = FlatStyle.Flat;
      this.newButton_View_Select.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_View_Select.Image = (Image) null;
      this.newButton_View_Select.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_View_Select.Location = new Point(497, 21);
      this.newButton_View_Select.Name = "newButton_View_Select";
      this.newButton_View_Select.Size = new Size(70, 44);
      this.newButton_View_Select.TabIndex = 116;
      this.newButton_View_Select.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_View_Select.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_View_Select.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_View_All.BackColor = SystemColors.Menu;
      this.newButton_View_All.ButtonBackColor = SystemColors.Menu;
      this.newButton_View_All.ButtonText = "전체 적용";
      this.newButton_View_All.FlatBorderSize = 1;
      this.newButton_View_All.FlatStyle = FlatStyle.Flat;
      this.newButton_View_All.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_View_All.Image = (Image) null;
      this.newButton_View_All.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_View_All.Location = new Point(428, 21);
      this.newButton_View_All.Name = "newButton_View_All";
      this.newButton_View_All.Size = new Size(70, 44);
      this.newButton_View_All.TabIndex = 116;
      this.newButton_View_All.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_View_All.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_View_All.NewClick += new EventHandler(this.newButton_NewClick);
      this.newTextBox_View_Z.BackColor = Color.PapayaWhip;
      this.newTextBox_View_Z.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_View_Z.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_View_Z.IsDigit = true;
      this.newTextBox_View_Z.Lines = new string[1]{ "0" };
      this.newTextBox_View_Z.Location = new Point(341, 42);
      this.newTextBox_View_Z.MultiLine = false;
      this.newTextBox_View_Z.Name = "newTextBox_View_Z";
      this.newTextBox_View_Z.ReadOnly = false;
      this.newTextBox_View_Z.Size = new Size(88, 23);
      this.newTextBox_View_Z.TabIndex = 115;
      this.newTextBox_View_Z.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_View_Z.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_View_Z.TextForeColor = SystemColors.WindowText;
      this.newTextBox_View_Z.Value = "0";
      this.newTextBox_View_Y.BackColor = Color.PapayaWhip;
      this.newTextBox_View_Y.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_View_Y.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_View_Y.IsDigit = true;
      this.newTextBox_View_Y.Lines = new string[1]{ "0" };
      this.newTextBox_View_Y.Location = new Point(254, 42);
      this.newTextBox_View_Y.MultiLine = false;
      this.newTextBox_View_Y.Name = "newTextBox_View_Y";
      this.newTextBox_View_Y.ReadOnly = false;
      this.newTextBox_View_Y.Size = new Size(88, 23);
      this.newTextBox_View_Y.TabIndex = 115;
      this.newTextBox_View_Y.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_View_Y.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_View_Y.TextForeColor = SystemColors.WindowText;
      this.newTextBox_View_Y.Value = "0";
      this.newTextBox_View_X.BackColor = Color.PapayaWhip;
      this.newTextBox_View_X.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_View_X.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_View_X.IsDigit = true;
      this.newTextBox_View_X.Lines = new string[1]{ "0" };
      this.newTextBox_View_X.Location = new Point(167, 42);
      this.newTextBox_View_X.MultiLine = false;
      this.newTextBox_View_X.Name = "newTextBox_View_X";
      this.newTextBox_View_X.ReadOnly = false;
      this.newTextBox_View_X.Size = new Size(88, 23);
      this.newTextBox_View_X.TabIndex = 115;
      this.newTextBox_View_X.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_View_X.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_View_X.TextForeColor = SystemColors.WindowText;
      this.newTextBox_View_X.Value = "0";
      this.newComboBox_View_Type.BackColor = Color.PapayaWhip;
      this.newComboBox_View_Type.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_View_Type.ComboBoxBackColor = Color.PapayaWhip;
      this.newComboBox_View_Type.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_View_Type.isSameSelect = false;
      this.newComboBox_View_Type.Location = new Point(5, 42);
      this.newComboBox_View_Type.Name = "newComboBox_View_Type";
      this.newComboBox_View_Type.SelectedIndex = -1;
      this.newComboBox_View_Type.Size = new Size(163, 23);
      this.newComboBox_View_Type.TabIndex = 114;
      this.newComboBox_View_Type.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_View_Type.Value = "";
      this.newComboBox_View_Type.SelectedIndexChanged += new EventHandler(this.newComboBox_View_Type_SelectedIndexChanged);
      this.tabPage_Set.Controls.Add((Control) this.unitTextBox_ThicknessMax);
      this.tabPage_Set.Controls.Add((Control) this.label_Thickness);
      this.tabPage_Set.Controls.Add((Control) this.unitTextBox_ThicknessMin);
      this.tabPage_Set.Controls.Add((Control) this.label73);
      this.tabPage_Set.Controls.Add((Control) this.label74);
      this.tabPage_Set.Controls.Add((Control) this.label_ShrinkComp);
      this.tabPage_Set.Controls.Add((Control) this.newTextBox_PA66);
      this.tabPage_Set.Controls.Add((Control) this.label70);
      this.tabPage_Set.Controls.Add((Control) this.newTextBox_PCABS);
      this.tabPage_Set.Controls.Add((Control) this.label69);
      this.tabPage_Set.Controls.Add((Control) this.newTextBox_PMMA);
      this.tabPage_Set.Controls.Add((Control) this.label68);
      this.tabPage_Set.Controls.Add((Control) this.newTextBox_PP);
      this.tabPage_Set.Controls.Add((Control) this.label65);
      this.tabPage_Set.Controls.Add((Control) this.newTextBox_ABS);
      this.tabPage_Set.Controls.Add((Control) this.label63);
      this.tabPage_Set.Controls.Add((Control) this.newTextBox_PBT);
      this.tabPage_Set.Controls.Add((Control) this.label62);
      this.tabPage_Set.Controls.Add((Control) this.newTextBox_PC);
      this.tabPage_Set.Controls.Add((Control) this.label59);
      this.tabPage_Set.Controls.Add((Control) this.label58);
      this.tabPage_Set.Controls.Add((Control) this.label_FamilyAbb);
      this.tabPage_Set.Controls.Add((Control) this.label_ShrinkSet);
      this.tabPage_Set.Controls.Add((Control) this.newTextBox_RLENS_GTemp_PMMA);
      this.tabPage_Set.Controls.Add((Control) this.newTextBox_RBEZEL_GTemp_PC);
      this.tabPage_Set.Controls.Add((Control) this.newTextBox_RHSG_GTemp_PCABS);
      this.tabPage_Set.Controls.Add((Control) this.newTextBox_FBEZEL_GTemp_PC);
      this.tabPage_Set.Controls.Add((Control) this.newTextBox_FBEZEL_GTemp_PBT);
      this.tabPage_Set.Controls.Add((Control) this.newTextBox_FHSG_GTemp_PP);
      this.tabPage_Set.Controls.Add((Control) this.label_Mold_Target_Temp);
      this.tabPage_Set.Controls.Add((Control) this.newTextBox_FLENS_GTemp_PC);
      this.tabPage_Set.Controls.Add((Control) this.label40);
      this.tabPage_Set.Controls.Add((Control) this.label41);
      this.tabPage_Set.Controls.Add((Control) this.label42);
      this.tabPage_Set.Controls.Add((Control) this.label44);
      this.tabPage_Set.Controls.Add((Control) this.label45);
      this.tabPage_Set.Controls.Add((Control) this.label54);
      this.tabPage_Set.Controls.Add((Control) this.label39);
      this.tabPage_Set.Controls.Add((Control) this.label46);
      this.tabPage_Set.Controls.Add((Control) this.label47);
      this.tabPage_Set.Controls.Add((Control) this.label49);
      this.tabPage_Set.Controls.Add((Control) this.label50);
      this.tabPage_Set.Controls.Add((Control) this.label51);
      this.tabPage_Set.Controls.Add((Control) this.label52);
      this.tabPage_Set.Controls.Add((Control) this.label53);
      this.tabPage_Set.Controls.Add((Control) this.label55);
      this.tabPage_Set.Controls.Add((Control) this.label_Suji);
      this.tabPage_Set.Controls.Add((Control) this.label_Cooling_Efficiency_Setting);
      this.tabPage_Set.Controls.Add((Control) this.unitTextBox_CoolantTempCavity);
      this.tabPage_Set.Controls.Add((Control) this.unitTextBox_ReynoldsNumber);
      this.tabPage_Set.Controls.Add((Control) this.unitTextBox_FlowRate);
      this.tabPage_Set.Controls.Add((Control) this.unitTextBox_CoolantTempCore);
      this.tabPage_Set.Controls.Add((Control) this.unitTextBox_InCavityPressure1);
      this.tabPage_Set.Controls.Add((Control) this.unitTextBox_InCavityPressure2);
      this.tabPage_Set.Controls.Add((Control) this.unitTextBox_InjPressure2);
      this.tabPage_Set.Controls.Add((Control) this.label_Result);
      this.tabPage_Set.Controls.Add((Control) this.unitTextBox_InjPressure1);
      this.tabPage_Set.Controls.Add((Control) this.label1);
      this.tabPage_Set.Controls.Add((Control) this.label23);
      this.tabPage_Set.Controls.Add((Control) this.label24);
      this.tabPage_Set.Controls.Add((Control) this.label_InjPressure1);
      this.tabPage_Set.Controls.Add((Control) this.label30);
      this.tabPage_Set.Controls.Add((Control) this.label31);
      this.tabPage_Set.Controls.Add((Control) this.label33);
      this.tabPage_Set.Controls.Add((Control) this.label34);
      this.tabPage_Set.Controls.Add((Control) this.newTextBox_RLENS_Goal_PMMA);
      this.tabPage_Set.Controls.Add((Control) this.newTextBox_RBEZEL_Goal_PC);
      this.tabPage_Set.Controls.Add((Control) this.newTextBox_RHSG_Goal_PCABS);
      this.tabPage_Set.Controls.Add((Control) this.newTextBox_FBEZEL_Goal_PC);
      this.tabPage_Set.Controls.Add((Control) this.newTextBox_FBEZEL_Goal_PBT);
      this.tabPage_Set.Controls.Add((Control) this.newTextBox_FHSG_Goal_PP);
      this.tabPage_Set.Controls.Add((Control) this.newTextBox_FLENS_Goal_PC);
      this.tabPage_Set.Controls.Add((Control) this.dataGridView_ShrinkComp);
      this.tabPage_Set.Location = new Point(4, 22);
      this.tabPage_Set.Name = "tabPage_Set";
      this.tabPage_Set.Size = new Size(572, 854);
      this.tabPage_Set.TabIndex = 2;
      this.tabPage_Set.Text = "tabPage3";
      this.tabPage_Set.UseVisualStyleBackColor = true;
      this.unitTextBox_ThicknessMax.BackColor = Color.PapayaWhip;
      this.unitTextBox_ThicknessMax.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_ThicknessMax.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_ThicknessMax.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_ThicknessMax.IsDigit = true;
      this.unitTextBox_ThicknessMax.Location = new Point(468, 681);
      this.unitTextBox_ThicknessMax.Name = "unitTextBox_ThicknessMax";
      this.unitTextBox_ThicknessMax.ReadOnly = false;
      this.unitTextBox_ThicknessMax.Size = new Size(100, 20);
      this.unitTextBox_ThicknessMax.TabIndex = 275;
      this.unitTextBox_ThicknessMax.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_ThicknessMax.Unit = "m";
      this.unitTextBox_ThicknessMax.Value = "";
      this.label_Thickness.BackColor = Color.Gold;
      this.label_Thickness.BorderStyle = BorderStyle.FixedSingle;
      this.label_Thickness.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Thickness.ForeColor = Color.MidnightBlue;
      this.label_Thickness.Location = new Point(6, 662);
      this.label_Thickness.Name = "label_Thickness";
      this.label_Thickness.Size = new Size(562, 20);
      this.label_Thickness.TabIndex = 274;
      this.label_Thickness.Text = "두께 설정";
      this.label_Thickness.TextAlign = ContentAlignment.MiddleCenter;
      this.unitTextBox_ThicknessMin.BackColor = Color.PapayaWhip;
      this.unitTextBox_ThicknessMin.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_ThicknessMin.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_ThicknessMin.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_ThicknessMin.IsDigit = true;
      this.unitTextBox_ThicknessMin.Location = new Point(188, 681);
      this.unitTextBox_ThicknessMin.Name = "unitTextBox_ThicknessMin";
      this.unitTextBox_ThicknessMin.ReadOnly = false;
      this.unitTextBox_ThicknessMin.Size = new Size(100, 20);
      this.unitTextBox_ThicknessMin.TabIndex = 273;
      this.unitTextBox_ThicknessMin.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_ThicknessMin.Unit = "m";
      this.unitTextBox_ThicknessMin.Value = "";
      this.label73.BackColor = Color.Ivory;
      this.label73.BorderStyle = BorderStyle.FixedSingle;
      this.label73.Location = new Point(287, 681);
      this.label73.Name = "label73";
      this.label73.Size = new Size(183, 20);
      this.label73.TabIndex = 271;
      this.label73.Text = "Max";
      this.label73.TextAlign = ContentAlignment.MiddleCenter;
      this.label74.BackColor = Color.Ivory;
      this.label74.BorderStyle = BorderStyle.FixedSingle;
      this.label74.Location = new Point(6, 681);
      this.label74.Name = "label74";
      this.label74.Size = new Size(183, 20);
      this.label74.TabIndex = 272;
      this.label74.Text = "Min";
      this.label74.TextAlign = ContentAlignment.MiddleCenter;
      this.label_ShrinkComp.BackColor = Color.Gold;
      this.label_ShrinkComp.BorderStyle = BorderStyle.FixedSingle;
      this.label_ShrinkComp.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_ShrinkComp.ForeColor = Color.MidnightBlue;
      this.label_ShrinkComp.Location = new Point(6, 439);
      this.label_ShrinkComp.Name = "label_ShrinkComp";
      this.label_ShrinkComp.Size = new Size(562, 20);
      this.label_ShrinkComp.TabIndex = 269;
      this.label_ShrinkComp.Text = "변형율 설정";
      this.label_ShrinkComp.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_PA66.BackColor = Color.PapayaWhip;
      this.newTextBox_PA66.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_PA66.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_PA66.IsDigit = false;
      this.newTextBox_PA66.Lines = new string[0];
      this.newTextBox_PA66.Location = new Point(286, 420);
      this.newTextBox_PA66.MultiLine = false;
      this.newTextBox_PA66.Name = "newTextBox_PA66";
      this.newTextBox_PA66.ReadOnly = false;
      this.newTextBox_PA66.Size = new Size(282, 20);
      this.newTextBox_PA66.TabIndex = 268;
      this.newTextBox_PA66.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_PA66.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_PA66.TextForeColor = SystemColors.WindowText;
      this.newTextBox_PA66.Value = "";
      this.label70.BackColor = Color.Ivory;
      this.label70.BorderStyle = BorderStyle.FixedSingle;
      this.label70.Location = new Point(6, 420);
      this.label70.Name = "label70";
      this.label70.Size = new Size(282, 20);
      this.label70.TabIndex = 267;
      this.label70.Text = "PA66";
      this.label70.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_PCABS.BackColor = Color.PapayaWhip;
      this.newTextBox_PCABS.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_PCABS.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_PCABS.IsDigit = false;
      this.newTextBox_PCABS.Lines = new string[0];
      this.newTextBox_PCABS.Location = new Point(286, 401);
      this.newTextBox_PCABS.MultiLine = false;
      this.newTextBox_PCABS.Name = "newTextBox_PCABS";
      this.newTextBox_PCABS.ReadOnly = false;
      this.newTextBox_PCABS.Size = new Size(282, 20);
      this.newTextBox_PCABS.TabIndex = 266;
      this.newTextBox_PCABS.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_PCABS.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_PCABS.TextForeColor = SystemColors.WindowText;
      this.newTextBox_PCABS.Value = "";
      this.label69.BackColor = Color.Ivory;
      this.label69.BorderStyle = BorderStyle.FixedSingle;
      this.label69.Location = new Point(6, 401);
      this.label69.Name = "label69";
      this.label69.Size = new Size(282, 20);
      this.label69.TabIndex = 265;
      this.label69.Text = "PC + ABS";
      this.label69.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_PMMA.BackColor = Color.PapayaWhip;
      this.newTextBox_PMMA.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_PMMA.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_PMMA.IsDigit = false;
      this.newTextBox_PMMA.Lines = new string[0];
      this.newTextBox_PMMA.Location = new Point(286, 382);
      this.newTextBox_PMMA.MultiLine = false;
      this.newTextBox_PMMA.Name = "newTextBox_PMMA";
      this.newTextBox_PMMA.ReadOnly = false;
      this.newTextBox_PMMA.Size = new Size(282, 20);
      this.newTextBox_PMMA.TabIndex = 264;
      this.newTextBox_PMMA.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_PMMA.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_PMMA.TextForeColor = SystemColors.WindowText;
      this.newTextBox_PMMA.Value = "";
      this.label68.BackColor = Color.Ivory;
      this.label68.BorderStyle = BorderStyle.FixedSingle;
      this.label68.Location = new Point(6, 382);
      this.label68.Name = "label68";
      this.label68.Size = new Size(282, 20);
      this.label68.TabIndex = 263;
      this.label68.Text = "PMMA";
      this.label68.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_PP.BackColor = Color.PapayaWhip;
      this.newTextBox_PP.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_PP.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_PP.IsDigit = false;
      this.newTextBox_PP.Lines = new string[0];
      this.newTextBox_PP.Location = new Point(286, 363);
      this.newTextBox_PP.MultiLine = false;
      this.newTextBox_PP.Name = "newTextBox_PP";
      this.newTextBox_PP.ReadOnly = false;
      this.newTextBox_PP.Size = new Size(282, 20);
      this.newTextBox_PP.TabIndex = 262;
      this.newTextBox_PP.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_PP.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_PP.TextForeColor = SystemColors.WindowText;
      this.newTextBox_PP.Value = "";
      this.label65.BackColor = Color.Ivory;
      this.label65.BorderStyle = BorderStyle.FixedSingle;
      this.label65.Location = new Point(6, 363);
      this.label65.Name = "label65";
      this.label65.Size = new Size(282, 20);
      this.label65.TabIndex = 261;
      this.label65.Text = "PP";
      this.label65.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_ABS.BackColor = Color.PapayaWhip;
      this.newTextBox_ABS.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_ABS.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_ABS.IsDigit = false;
      this.newTextBox_ABS.Lines = new string[0];
      this.newTextBox_ABS.Location = new Point(286, 344);
      this.newTextBox_ABS.MultiLine = false;
      this.newTextBox_ABS.Name = "newTextBox_ABS";
      this.newTextBox_ABS.ReadOnly = false;
      this.newTextBox_ABS.Size = new Size(282, 20);
      this.newTextBox_ABS.TabIndex = 260;
      this.newTextBox_ABS.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_ABS.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_ABS.TextForeColor = SystemColors.WindowText;
      this.newTextBox_ABS.Value = "";
      this.label63.BackColor = Color.Ivory;
      this.label63.BorderStyle = BorderStyle.FixedSingle;
      this.label63.Location = new Point(6, 344);
      this.label63.Name = "label63";
      this.label63.Size = new Size(282, 20);
      this.label63.TabIndex = 259;
      this.label63.Text = "ABS";
      this.label63.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_PBT.BackColor = Color.PapayaWhip;
      this.newTextBox_PBT.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_PBT.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_PBT.IsDigit = false;
      this.newTextBox_PBT.Lines = new string[0];
      this.newTextBox_PBT.Location = new Point(286, 325);
      this.newTextBox_PBT.MultiLine = false;
      this.newTextBox_PBT.Name = "newTextBox_PBT";
      this.newTextBox_PBT.ReadOnly = false;
      this.newTextBox_PBT.Size = new Size(282, 20);
      this.newTextBox_PBT.TabIndex = 258;
      this.newTextBox_PBT.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_PBT.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_PBT.TextForeColor = SystemColors.WindowText;
      this.newTextBox_PBT.Value = "";
      this.label62.BackColor = Color.Ivory;
      this.label62.BorderStyle = BorderStyle.FixedSingle;
      this.label62.Location = new Point(6, 325);
      this.label62.Name = "label62";
      this.label62.Size = new Size(282, 20);
      this.label62.TabIndex = 257;
      this.label62.Text = "PBT";
      this.label62.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_PC.BackColor = Color.PapayaWhip;
      this.newTextBox_PC.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_PC.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_PC.IsDigit = false;
      this.newTextBox_PC.Lines = new string[0];
      this.newTextBox_PC.Location = new Point(286, 306);
      this.newTextBox_PC.MultiLine = false;
      this.newTextBox_PC.Name = "newTextBox_PC";
      this.newTextBox_PC.ReadOnly = false;
      this.newTextBox_PC.Size = new Size(282, 20);
      this.newTextBox_PC.TabIndex = 256;
      this.newTextBox_PC.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_PC.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_PC.TextForeColor = SystemColors.WindowText;
      this.newTextBox_PC.Value = "";
      this.label59.BackColor = Color.Ivory;
      this.label59.BorderStyle = BorderStyle.FixedSingle;
      this.label59.Location = new Point(6, 306);
      this.label59.Name = "label59";
      this.label59.Size = new Size(282, 20);
      this.label59.TabIndex = (int) byte.MaxValue;
      this.label59.Text = "PC";
      this.label59.TextAlign = ContentAlignment.MiddleCenter;
      this.label58.BackColor = Color.WhiteSmoke;
      this.label58.BorderStyle = BorderStyle.FixedSingle;
      this.label58.Location = new Point(286, 287);
      this.label58.Name = "label58";
      this.label58.Size = new Size(282, 20);
      this.label58.TabIndex = 254;
      this.label58.Text = "Value";
      this.label58.TextAlign = ContentAlignment.MiddleCenter;
      this.label_FamilyAbb.BackColor = Color.WhiteSmoke;
      this.label_FamilyAbb.BorderStyle = BorderStyle.FixedSingle;
      this.label_FamilyAbb.Location = new Point(6, 287);
      this.label_FamilyAbb.Name = "label_FamilyAbb";
      this.label_FamilyAbb.Size = new Size(282, 20);
      this.label_FamilyAbb.TabIndex = 253;
      this.label_FamilyAbb.Text = "재질";
      this.label_FamilyAbb.TextAlign = ContentAlignment.MiddleCenter;
      this.label_ShrinkSet.BackColor = Color.Gold;
      this.label_ShrinkSet.BorderStyle = BorderStyle.FixedSingle;
      this.label_ShrinkSet.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_ShrinkSet.ForeColor = Color.MidnightBlue;
      this.label_ShrinkSet.Location = new Point(6, 268);
      this.label_ShrinkSet.Name = "label_ShrinkSet";
      this.label_ShrinkSet.Size = new Size(562, 20);
      this.label_ShrinkSet.TabIndex = 252;
      this.label_ShrinkSet.Text = "수축률 설정";
      this.label_ShrinkSet.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_RLENS_GTemp_PMMA.BackColor = Color.PapayaWhip;
      this.newTextBox_RLENS_GTemp_PMMA.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_RLENS_GTemp_PMMA.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_RLENS_GTemp_PMMA.IsDigit = false;
      this.newTextBox_RLENS_GTemp_PMMA.Lines = new string[0];
      this.newTextBox_RLENS_GTemp_PMMA.Location = new Point(286, 211);
      this.newTextBox_RLENS_GTemp_PMMA.MultiLine = false;
      this.newTextBox_RLENS_GTemp_PMMA.Name = "newTextBox_RLENS_GTemp_PMMA";
      this.newTextBox_RLENS_GTemp_PMMA.ReadOnly = false;
      this.newTextBox_RLENS_GTemp_PMMA.Size = new Size(142, 20);
      this.newTextBox_RLENS_GTemp_PMMA.TabIndex = 244;
      this.newTextBox_RLENS_GTemp_PMMA.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_RLENS_GTemp_PMMA.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_RLENS_GTemp_PMMA.TextForeColor = SystemColors.WindowText;
      this.newTextBox_RLENS_GTemp_PMMA.Value = "";
      this.newTextBox_RBEZEL_GTemp_PC.BackColor = Color.PapayaWhip;
      this.newTextBox_RBEZEL_GTemp_PC.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_RBEZEL_GTemp_PC.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_RBEZEL_GTemp_PC.IsDigit = false;
      this.newTextBox_RBEZEL_GTemp_PC.Lines = new string[0];
      this.newTextBox_RBEZEL_GTemp_PC.Location = new Point(286, 230);
      this.newTextBox_RBEZEL_GTemp_PC.MultiLine = false;
      this.newTextBox_RBEZEL_GTemp_PC.Name = "newTextBox_RBEZEL_GTemp_PC";
      this.newTextBox_RBEZEL_GTemp_PC.ReadOnly = false;
      this.newTextBox_RBEZEL_GTemp_PC.Size = new Size(142, 20);
      this.newTextBox_RBEZEL_GTemp_PC.TabIndex = 243;
      this.newTextBox_RBEZEL_GTemp_PC.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_RBEZEL_GTemp_PC.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_RBEZEL_GTemp_PC.TextForeColor = SystemColors.WindowText;
      this.newTextBox_RBEZEL_GTemp_PC.Value = "";
      this.newTextBox_RHSG_GTemp_PCABS.BackColor = Color.PapayaWhip;
      this.newTextBox_RHSG_GTemp_PCABS.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_RHSG_GTemp_PCABS.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_RHSG_GTemp_PCABS.IsDigit = false;
      this.newTextBox_RHSG_GTemp_PCABS.Lines = new string[0];
      this.newTextBox_RHSG_GTemp_PCABS.Location = new Point(286, 249);
      this.newTextBox_RHSG_GTemp_PCABS.MultiLine = false;
      this.newTextBox_RHSG_GTemp_PCABS.Name = "newTextBox_RHSG_GTemp_PCABS";
      this.newTextBox_RHSG_GTemp_PCABS.ReadOnly = false;
      this.newTextBox_RHSG_GTemp_PCABS.Size = new Size(142, 20);
      this.newTextBox_RHSG_GTemp_PCABS.TabIndex = 242;
      this.newTextBox_RHSG_GTemp_PCABS.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_RHSG_GTemp_PCABS.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_RHSG_GTemp_PCABS.TextForeColor = SystemColors.WindowText;
      this.newTextBox_RHSG_GTemp_PCABS.Value = "";
      this.newTextBox_FBEZEL_GTemp_PC.BackColor = Color.PapayaWhip;
      this.newTextBox_FBEZEL_GTemp_PC.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_FBEZEL_GTemp_PC.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_FBEZEL_GTemp_PC.IsDigit = false;
      this.newTextBox_FBEZEL_GTemp_PC.Lines = new string[0];
      this.newTextBox_FBEZEL_GTemp_PC.Location = new Point(286, 154);
      this.newTextBox_FBEZEL_GTemp_PC.MultiLine = false;
      this.newTextBox_FBEZEL_GTemp_PC.Name = "newTextBox_FBEZEL_GTemp_PC";
      this.newTextBox_FBEZEL_GTemp_PC.ReadOnly = false;
      this.newTextBox_FBEZEL_GTemp_PC.Size = new Size(142, 20);
      this.newTextBox_FBEZEL_GTemp_PC.TabIndex = 241;
      this.newTextBox_FBEZEL_GTemp_PC.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_FBEZEL_GTemp_PC.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_FBEZEL_GTemp_PC.TextForeColor = SystemColors.WindowText;
      this.newTextBox_FBEZEL_GTemp_PC.Value = "";
      this.newTextBox_FBEZEL_GTemp_PBT.BackColor = Color.PapayaWhip;
      this.newTextBox_FBEZEL_GTemp_PBT.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_FBEZEL_GTemp_PBT.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_FBEZEL_GTemp_PBT.IsDigit = false;
      this.newTextBox_FBEZEL_GTemp_PBT.Lines = new string[0];
      this.newTextBox_FBEZEL_GTemp_PBT.Location = new Point(286, 173);
      this.newTextBox_FBEZEL_GTemp_PBT.MultiLine = false;
      this.newTextBox_FBEZEL_GTemp_PBT.Name = "newTextBox_FBEZEL_GTemp_PBT";
      this.newTextBox_FBEZEL_GTemp_PBT.ReadOnly = false;
      this.newTextBox_FBEZEL_GTemp_PBT.Size = new Size(142, 20);
      this.newTextBox_FBEZEL_GTemp_PBT.TabIndex = 240;
      this.newTextBox_FBEZEL_GTemp_PBT.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_FBEZEL_GTemp_PBT.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_FBEZEL_GTemp_PBT.TextForeColor = SystemColors.WindowText;
      this.newTextBox_FBEZEL_GTemp_PBT.Value = "";
      this.newTextBox_FHSG_GTemp_PP.BackColor = Color.PapayaWhip;
      this.newTextBox_FHSG_GTemp_PP.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_FHSG_GTemp_PP.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_FHSG_GTemp_PP.IsDigit = false;
      this.newTextBox_FHSG_GTemp_PP.Lines = new string[0];
      this.newTextBox_FHSG_GTemp_PP.Location = new Point(286, 192);
      this.newTextBox_FHSG_GTemp_PP.MultiLine = false;
      this.newTextBox_FHSG_GTemp_PP.Name = "newTextBox_FHSG_GTemp_PP";
      this.newTextBox_FHSG_GTemp_PP.ReadOnly = false;
      this.newTextBox_FHSG_GTemp_PP.Size = new Size(142, 20);
      this.newTextBox_FHSG_GTemp_PP.TabIndex = 239;
      this.newTextBox_FHSG_GTemp_PP.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_FHSG_GTemp_PP.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_FHSG_GTemp_PP.TextForeColor = SystemColors.WindowText;
      this.newTextBox_FHSG_GTemp_PP.Value = "";
      this.label_Mold_Target_Temp.BackColor = Color.Ivory;
      this.label_Mold_Target_Temp.BorderStyle = BorderStyle.FixedSingle;
      this.label_Mold_Target_Temp.Location = new Point(286, 116);
      this.label_Mold_Target_Temp.Name = "label_Mold_Target_Temp";
      this.label_Mold_Target_Temp.Size = new Size(142, 20);
      this.label_Mold_Target_Temp.TabIndex = 236;
      this.label_Mold_Target_Temp.Text = "금형목표온도";
      this.label_Mold_Target_Temp.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_FLENS_GTemp_PC.BackColor = Color.PapayaWhip;
      this.newTextBox_FLENS_GTemp_PC.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_FLENS_GTemp_PC.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_FLENS_GTemp_PC.IsDigit = false;
      this.newTextBox_FLENS_GTemp_PC.Lines = new string[0];
      this.newTextBox_FLENS_GTemp_PC.Location = new Point(286, 135);
      this.newTextBox_FLENS_GTemp_PC.MultiLine = false;
      this.newTextBox_FLENS_GTemp_PC.Name = "newTextBox_FLENS_GTemp_PC";
      this.newTextBox_FLENS_GTemp_PC.ReadOnly = false;
      this.newTextBox_FLENS_GTemp_PC.Size = new Size(142, 20);
      this.newTextBox_FLENS_GTemp_PC.TabIndex = 238;
      this.newTextBox_FLENS_GTemp_PC.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_FLENS_GTemp_PC.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_FLENS_GTemp_PC.TextForeColor = SystemColors.WindowText;
      this.newTextBox_FLENS_GTemp_PC.Value = "";
      this.label40.BackColor = SystemColors.Control;
      this.label40.BorderStyle = BorderStyle.FixedSingle;
      this.label40.Font = new Font("굴림", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 129);
      this.label40.Location = new Point(6, 249);
      this.label40.Name = "label40";
      this.label40.Size = new Size(142, 20);
      this.label40.TabIndex = 221;
      this.label40.Text = "REAR-HSG";
      this.label40.TextAlign = ContentAlignment.MiddleLeft;
      this.label41.BackColor = SystemColors.Control;
      this.label41.BorderStyle = BorderStyle.FixedSingle;
      this.label41.Font = new Font("굴림", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 129);
      this.label41.Location = new Point(6, 230);
      this.label41.Name = "label41";
      this.label41.Size = new Size(142, 20);
      this.label41.TabIndex = 222;
      this.label41.Text = "REAR-BEZEL";
      this.label41.TextAlign = ContentAlignment.MiddleLeft;
      this.label42.BackColor = SystemColors.Control;
      this.label42.BorderStyle = BorderStyle.FixedSingle;
      this.label42.Font = new Font("굴림", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 129);
      this.label42.Location = new Point(6, 211);
      this.label42.Name = "label42";
      this.label42.Size = new Size(142, 20);
      this.label42.TabIndex = 223;
      this.label42.Text = "REAR-LENS";
      this.label42.TextAlign = ContentAlignment.MiddleLeft;
      this.label44.BackColor = SystemColors.Control;
      this.label44.BorderStyle = BorderStyle.FixedSingle;
      this.label44.Font = new Font("굴림", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 129);
      this.label44.Location = new Point(6, 192);
      this.label44.Name = "label44";
      this.label44.Size = new Size(142, 20);
      this.label44.TabIndex = 224;
      this.label44.Text = "FRONT-HSG";
      this.label44.TextAlign = ContentAlignment.MiddleLeft;
      this.label45.BackColor = SystemColors.Control;
      this.label45.BorderStyle = BorderStyle.FixedSingle;
      this.label45.Font = new Font("굴림", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 129);
      this.label45.Location = new Point(6, 154);
      this.label45.Name = "label45";
      this.label45.Size = new Size(142, 39);
      this.label45.TabIndex = 225;
      this.label45.Text = "FRONT-BEZEL";
      this.label45.TextAlign = ContentAlignment.MiddleLeft;
      this.label54.BackColor = SystemColors.Control;
      this.label54.BorderStyle = BorderStyle.FixedSingle;
      this.label54.Font = new Font("굴림", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 129);
      this.label54.Location = new Point(6, 135);
      this.label54.Name = "label54";
      this.label54.Size = new Size(142, 20);
      this.label54.TabIndex = 233;
      this.label54.Text = "FRONT-LENS";
      this.label54.TextAlign = ContentAlignment.MiddleLeft;
      this.label39.BackColor = Color.Ivory;
      this.label39.BorderStyle = BorderStyle.FixedSingle;
      this.label39.Location = new Point(6, 116);
      this.label39.Name = "label39";
      this.label39.Size = new Size(142, 20);
      this.label39.TabIndex = 237;
      this.label39.TextAlign = ContentAlignment.MiddleCenter;
      this.label46.BackColor = Color.WhiteSmoke;
      this.label46.BorderStyle = BorderStyle.FixedSingle;
      this.label46.Location = new Point(146, 249);
      this.label46.Name = "label46";
      this.label46.Size = new Size(142, 20);
      this.label46.TabIndex = 226;
      this.label46.Text = "PC / ABS";
      this.label46.TextAlign = ContentAlignment.MiddleCenter;
      this.label47.BackColor = Color.WhiteSmoke;
      this.label47.BorderStyle = BorderStyle.FixedSingle;
      this.label47.Location = new Point(146, 230);
      this.label47.Name = "label47";
      this.label47.Size = new Size(142, 20);
      this.label47.TabIndex = 227;
      this.label47.Text = "PC";
      this.label47.TextAlign = ContentAlignment.MiddleCenter;
      this.label49.BackColor = Color.WhiteSmoke;
      this.label49.BorderStyle = BorderStyle.FixedSingle;
      this.label49.Location = new Point(146, 211);
      this.label49.Name = "label49";
      this.label49.Size = new Size(142, 20);
      this.label49.TabIndex = 228;
      this.label49.Text = "PMMA";
      this.label49.TextAlign = ContentAlignment.MiddleCenter;
      this.label50.BackColor = Color.WhiteSmoke;
      this.label50.BorderStyle = BorderStyle.FixedSingle;
      this.label50.Location = new Point(146, 192);
      this.label50.Name = "label50";
      this.label50.Size = new Size(142, 20);
      this.label50.TabIndex = 229;
      this.label50.Text = "PP";
      this.label50.TextAlign = ContentAlignment.MiddleCenter;
      this.label51.BackColor = Color.WhiteSmoke;
      this.label51.BorderStyle = BorderStyle.FixedSingle;
      this.label51.Location = new Point(146, 173);
      this.label51.Name = "label51";
      this.label51.Size = new Size(142, 20);
      this.label51.TabIndex = 230;
      this.label51.Text = "PBT";
      this.label51.TextAlign = ContentAlignment.MiddleCenter;
      this.label52.BackColor = Color.WhiteSmoke;
      this.label52.BorderStyle = BorderStyle.FixedSingle;
      this.label52.Location = new Point(146, 154);
      this.label52.Name = "label52";
      this.label52.Size = new Size(142, 20);
      this.label52.TabIndex = 231;
      this.label52.Text = "PC";
      this.label52.TextAlign = ContentAlignment.MiddleCenter;
      this.label53.BackColor = Color.WhiteSmoke;
      this.label53.BorderStyle = BorderStyle.FixedSingle;
      this.label53.Location = new Point(146, 135);
      this.label53.Name = "label53";
      this.label53.Size = new Size(142, 20);
      this.label53.TabIndex = 232;
      this.label53.Text = "PC";
      this.label53.TextAlign = ContentAlignment.MiddleCenter;
      this.label55.BackColor = Color.Ivory;
      this.label55.BorderStyle = BorderStyle.FixedSingle;
      this.label55.Location = new Point(426, 116);
      this.label55.Name = "label55";
      this.label55.Size = new Size(142, 20);
      this.label55.TabIndex = 234;
      this.label55.Text = "목표(%)";
      this.label55.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Suji.BackColor = Color.Ivory;
      this.label_Suji.BorderStyle = BorderStyle.FixedSingle;
      this.label_Suji.Location = new Point(146, 116);
      this.label_Suji.Name = "label_Suji";
      this.label_Suji.Size = new Size(142, 20);
      this.label_Suji.TabIndex = 235;
      this.label_Suji.Text = "수지";
      this.label_Suji.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Cooling_Efficiency_Setting.BackColor = Color.Gold;
      this.label_Cooling_Efficiency_Setting.BorderStyle = BorderStyle.FixedSingle;
      this.label_Cooling_Efficiency_Setting.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Cooling_Efficiency_Setting.ForeColor = Color.MidnightBlue;
      this.label_Cooling_Efficiency_Setting.Location = new Point(6, 97);
      this.label_Cooling_Efficiency_Setting.Name = "label_Cooling_Efficiency_Setting";
      this.label_Cooling_Efficiency_Setting.Size = new Size(562, 20);
      this.label_Cooling_Efficiency_Setting.TabIndex = 219;
      this.label_Cooling_Efficiency_Setting.Text = "냉각 효율 설정";
      this.label_Cooling_Efficiency_Setting.TextAlign = ContentAlignment.MiddleCenter;
      this.unitTextBox_CoolantTempCavity.BackColor = Color.PapayaWhip;
      this.unitTextBox_CoolantTempCavity.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_CoolantTempCavity.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_CoolantTempCavity.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_CoolantTempCavity.IsDigit = true;
      this.unitTextBox_CoolantTempCavity.Location = new Point(468, 59);
      this.unitTextBox_CoolantTempCavity.Name = "unitTextBox_CoolantTempCavity";
      this.unitTextBox_CoolantTempCavity.ReadOnly = false;
      this.unitTextBox_CoolantTempCavity.Size = new Size(100, 20);
      this.unitTextBox_CoolantTempCavity.TabIndex = 218;
      this.unitTextBox_CoolantTempCavity.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_CoolantTempCavity.Unit = "℃";
      this.unitTextBox_CoolantTempCavity.Value = "";
      this.unitTextBox_ReynoldsNumber.BackColor = Color.PapayaWhip;
      this.unitTextBox_ReynoldsNumber.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_ReynoldsNumber.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_ReynoldsNumber.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_ReynoldsNumber.IsDigit = true;
      this.unitTextBox_ReynoldsNumber.Location = new Point(468, 78);
      this.unitTextBox_ReynoldsNumber.Name = "unitTextBox_ReynoldsNumber";
      this.unitTextBox_ReynoldsNumber.ReadOnly = false;
      this.unitTextBox_ReynoldsNumber.Size = new Size(100, 20);
      this.unitTextBox_ReynoldsNumber.TabIndex = 217;
      this.unitTextBox_ReynoldsNumber.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_ReynoldsNumber.Unit = "S";
      this.unitTextBox_ReynoldsNumber.Value = "";
      this.unitTextBox_FlowRate.BackColor = Color.PapayaWhip;
      this.unitTextBox_FlowRate.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_FlowRate.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_FlowRate.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_FlowRate.IsDigit = true;
      this.unitTextBox_FlowRate.Location = new Point(188, 78);
      this.unitTextBox_FlowRate.Name = "unitTextBox_FlowRate";
      this.unitTextBox_FlowRate.ReadOnly = false;
      this.unitTextBox_FlowRate.Size = new Size(100, 20);
      this.unitTextBox_FlowRate.TabIndex = 216;
      this.unitTextBox_FlowRate.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_FlowRate.Unit = "l/min";
      this.unitTextBox_FlowRate.Value = "";
      this.unitTextBox_CoolantTempCore.BackColor = Color.PapayaWhip;
      this.unitTextBox_CoolantTempCore.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_CoolantTempCore.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_CoolantTempCore.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_CoolantTempCore.IsDigit = true;
      this.unitTextBox_CoolantTempCore.Location = new Point(188, 59);
      this.unitTextBox_CoolantTempCore.Name = "unitTextBox_CoolantTempCore";
      this.unitTextBox_CoolantTempCore.ReadOnly = false;
      this.unitTextBox_CoolantTempCore.Size = new Size(100, 20);
      this.unitTextBox_CoolantTempCore.TabIndex = 215;
      this.unitTextBox_CoolantTempCore.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_CoolantTempCore.Unit = "℃";
      this.unitTextBox_CoolantTempCore.Value = "";
      this.unitTextBox_InCavityPressure1.BackColor = Color.PapayaWhip;
      this.unitTextBox_InCavityPressure1.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_InCavityPressure1.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_InCavityPressure1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_InCavityPressure1.IsDigit = true;
      this.unitTextBox_InCavityPressure1.Location = new Point(188, 40);
      this.unitTextBox_InCavityPressure1.Name = "unitTextBox_InCavityPressure1";
      this.unitTextBox_InCavityPressure1.ReadOnly = false;
      this.unitTextBox_InCavityPressure1.Size = new Size(100, 20);
      this.unitTextBox_InCavityPressure1.TabIndex = 214;
      this.unitTextBox_InCavityPressure1.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_InCavityPressure1.Unit = "MPa";
      this.unitTextBox_InCavityPressure1.Value = "";
      this.unitTextBox_InCavityPressure2.BackColor = Color.PapayaWhip;
      this.unitTextBox_InCavityPressure2.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_InCavityPressure2.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_InCavityPressure2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_InCavityPressure2.IsDigit = true;
      this.unitTextBox_InCavityPressure2.Location = new Point(468, 40);
      this.unitTextBox_InCavityPressure2.Name = "unitTextBox_InCavityPressure2";
      this.unitTextBox_InCavityPressure2.ReadOnly = false;
      this.unitTextBox_InCavityPressure2.Size = new Size(100, 20);
      this.unitTextBox_InCavityPressure2.TabIndex = 213;
      this.unitTextBox_InCavityPressure2.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_InCavityPressure2.Unit = "MPa";
      this.unitTextBox_InCavityPressure2.Value = "";
      this.unitTextBox_InjPressure2.BackColor = Color.PapayaWhip;
      this.unitTextBox_InjPressure2.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_InjPressure2.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_InjPressure2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_InjPressure2.IsDigit = true;
      this.unitTextBox_InjPressure2.Location = new Point(468, 21);
      this.unitTextBox_InjPressure2.Name = "unitTextBox_InjPressure2";
      this.unitTextBox_InjPressure2.ReadOnly = false;
      this.unitTextBox_InjPressure2.Size = new Size(100, 20);
      this.unitTextBox_InjPressure2.TabIndex = 212;
      this.unitTextBox_InjPressure2.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_InjPressure2.Unit = "MPa";
      this.unitTextBox_InjPressure2.Value = "";
      this.label_Result.BackColor = Color.Gold;
      this.label_Result.BorderStyle = BorderStyle.FixedSingle;
      this.label_Result.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Result.ForeColor = Color.MidnightBlue;
      this.label_Result.Location = new Point(6, 2);
      this.label_Result.Name = "label_Result";
      this.label_Result.Size = new Size(562, 20);
      this.label_Result.TabIndex = 211;
      this.label_Result.Text = "결과 판정";
      this.label_Result.TextAlign = ContentAlignment.MiddleCenter;
      this.unitTextBox_InjPressure1.BackColor = Color.PapayaWhip;
      this.unitTextBox_InjPressure1.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_InjPressure1.ControlBackColor = Color.PapayaWhip;
      this.unitTextBox_InjPressure1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_InjPressure1.IsDigit = true;
      this.unitTextBox_InjPressure1.Location = new Point(188, 21);
      this.unitTextBox_InjPressure1.Name = "unitTextBox_InjPressure1";
      this.unitTextBox_InjPressure1.ReadOnly = false;
      this.unitTextBox_InjPressure1.Size = new Size(100, 20);
      this.unitTextBox_InjPressure1.TabIndex = 210;
      this.unitTextBox_InjPressure1.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_InjPressure1.Unit = "MPa";
      this.unitTextBox_InjPressure1.Value = "";
      this.label1.BackColor = Color.Ivory;
      this.label1.BorderStyle = BorderStyle.FixedSingle;
      this.label1.Location = new Point(287, 40);
      this.label1.Name = "label1";
      this.label1.Size = new Size(183, 20);
      this.label1.TabIndex = 87;
      this.label1.Text = "In-Cavity Pressure 2";
      this.label1.TextAlign = ContentAlignment.MiddleLeft;
      this.label23.BackColor = Color.Ivory;
      this.label23.BorderStyle = BorderStyle.FixedSingle;
      this.label23.Location = new Point(6, 40);
      this.label23.Name = "label23";
      this.label23.Size = new Size(183, 20);
      this.label23.TabIndex = 88;
      this.label23.Text = "In-Cavity Pressure 1";
      this.label23.TextAlign = ContentAlignment.MiddleLeft;
      this.label24.BackColor = Color.Ivory;
      this.label24.BorderStyle = BorderStyle.FixedSingle;
      this.label24.Location = new Point(287, 21);
      this.label24.Name = "label24";
      this.label24.Size = new Size(183, 20);
      this.label24.TabIndex = 89;
      this.label24.Text = "Injection Pressure 2";
      this.label24.TextAlign = ContentAlignment.MiddleLeft;
      this.label_InjPressure1.BackColor = Color.Ivory;
      this.label_InjPressure1.BorderStyle = BorderStyle.FixedSingle;
      this.label_InjPressure1.Location = new Point(6, 21);
      this.label_InjPressure1.Name = "label_InjPressure1";
      this.label_InjPressure1.Size = new Size(183, 20);
      this.label_InjPressure1.TabIndex = 90;
      this.label_InjPressure1.Text = "Injection Pressure 1";
      this.label_InjPressure1.TextAlign = ContentAlignment.MiddleLeft;
      this.label30.BackColor = Color.Ivory;
      this.label30.BorderStyle = BorderStyle.FixedSingle;
      this.label30.Location = new Point(6, 78);
      this.label30.Name = "label30";
      this.label30.Size = new Size(183, 20);
      this.label30.TabIndex = 83;
      this.label30.Text = "Circuit Flow Rate";
      this.label30.TextAlign = ContentAlignment.MiddleLeft;
      this.label31.BackColor = Color.Ivory;
      this.label31.BorderStyle = BorderStyle.FixedSingle;
      this.label31.Location = new Point(287, 59);
      this.label31.Name = "label31";
      this.label31.Size = new Size(183, 20);
      this.label31.TabIndex = 84;
      this.label31.Text = "Circuit Coolant Temp(Cavity)";
      this.label31.TextAlign = ContentAlignment.MiddleLeft;
      this.label33.BackColor = Color.Ivory;
      this.label33.BorderStyle = BorderStyle.FixedSingle;
      this.label33.Location = new Point(6, 59);
      this.label33.Name = "label33";
      this.label33.Size = new Size(183, 20);
      this.label33.TabIndex = 85;
      this.label33.Text = "Circuit Coolant Temp(Core)";
      this.label33.TextAlign = ContentAlignment.MiddleLeft;
      this.label34.BackColor = Color.Ivory;
      this.label34.BorderStyle = BorderStyle.FixedSingle;
      this.label34.Location = new Point(287, 78);
      this.label34.Name = "label34";
      this.label34.Size = new Size(183, 20);
      this.label34.TabIndex = 86;
      this.label34.Text = "Circuit Reynolds Number";
      this.label34.TextAlign = ContentAlignment.MiddleLeft;
      this.newTextBox_RLENS_Goal_PMMA.BackColor = Color.PapayaWhip;
      this.newTextBox_RLENS_Goal_PMMA.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_RLENS_Goal_PMMA.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_RLENS_Goal_PMMA.IsDigit = false;
      this.newTextBox_RLENS_Goal_PMMA.Lines = new string[0];
      this.newTextBox_RLENS_Goal_PMMA.Location = new Point(426, 211);
      this.newTextBox_RLENS_Goal_PMMA.MultiLine = false;
      this.newTextBox_RLENS_Goal_PMMA.Name = "newTextBox_RLENS_Goal_PMMA";
      this.newTextBox_RLENS_Goal_PMMA.ReadOnly = false;
      this.newTextBox_RLENS_Goal_PMMA.Size = new Size(142, 20);
      this.newTextBox_RLENS_Goal_PMMA.TabIndex = 251;
      this.newTextBox_RLENS_Goal_PMMA.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_RLENS_Goal_PMMA.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_RLENS_Goal_PMMA.TextForeColor = SystemColors.WindowText;
      this.newTextBox_RLENS_Goal_PMMA.Value = "";
      this.newTextBox_RBEZEL_Goal_PC.BackColor = Color.PapayaWhip;
      this.newTextBox_RBEZEL_Goal_PC.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_RBEZEL_Goal_PC.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_RBEZEL_Goal_PC.IsDigit = false;
      this.newTextBox_RBEZEL_Goal_PC.Lines = new string[0];
      this.newTextBox_RBEZEL_Goal_PC.Location = new Point(426, 230);
      this.newTextBox_RBEZEL_Goal_PC.MultiLine = false;
      this.newTextBox_RBEZEL_Goal_PC.Name = "newTextBox_RBEZEL_Goal_PC";
      this.newTextBox_RBEZEL_Goal_PC.ReadOnly = false;
      this.newTextBox_RBEZEL_Goal_PC.Size = new Size(142, 20);
      this.newTextBox_RBEZEL_Goal_PC.TabIndex = 250;
      this.newTextBox_RBEZEL_Goal_PC.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_RBEZEL_Goal_PC.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_RBEZEL_Goal_PC.TextForeColor = SystemColors.WindowText;
      this.newTextBox_RBEZEL_Goal_PC.Value = "";
      this.newTextBox_RHSG_Goal_PCABS.BackColor = Color.PapayaWhip;
      this.newTextBox_RHSG_Goal_PCABS.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_RHSG_Goal_PCABS.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_RHSG_Goal_PCABS.IsDigit = false;
      this.newTextBox_RHSG_Goal_PCABS.Lines = new string[0];
      this.newTextBox_RHSG_Goal_PCABS.Location = new Point(426, 249);
      this.newTextBox_RHSG_Goal_PCABS.MultiLine = false;
      this.newTextBox_RHSG_Goal_PCABS.Name = "newTextBox_RHSG_Goal_PCABS";
      this.newTextBox_RHSG_Goal_PCABS.ReadOnly = false;
      this.newTextBox_RHSG_Goal_PCABS.Size = new Size(142, 20);
      this.newTextBox_RHSG_Goal_PCABS.TabIndex = 249;
      this.newTextBox_RHSG_Goal_PCABS.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_RHSG_Goal_PCABS.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_RHSG_Goal_PCABS.TextForeColor = SystemColors.WindowText;
      this.newTextBox_RHSG_Goal_PCABS.Value = "";
      this.newTextBox_FBEZEL_Goal_PC.BackColor = Color.PapayaWhip;
      this.newTextBox_FBEZEL_Goal_PC.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_FBEZEL_Goal_PC.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_FBEZEL_Goal_PC.IsDigit = false;
      this.newTextBox_FBEZEL_Goal_PC.Lines = new string[0];
      this.newTextBox_FBEZEL_Goal_PC.Location = new Point(426, 154);
      this.newTextBox_FBEZEL_Goal_PC.MultiLine = false;
      this.newTextBox_FBEZEL_Goal_PC.Name = "newTextBox_FBEZEL_Goal_PC";
      this.newTextBox_FBEZEL_Goal_PC.ReadOnly = false;
      this.newTextBox_FBEZEL_Goal_PC.Size = new Size(142, 20);
      this.newTextBox_FBEZEL_Goal_PC.TabIndex = 248;
      this.newTextBox_FBEZEL_Goal_PC.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_FBEZEL_Goal_PC.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_FBEZEL_Goal_PC.TextForeColor = SystemColors.WindowText;
      this.newTextBox_FBEZEL_Goal_PC.Value = "";
      this.newTextBox_FBEZEL_Goal_PBT.BackColor = Color.PapayaWhip;
      this.newTextBox_FBEZEL_Goal_PBT.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_FBEZEL_Goal_PBT.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_FBEZEL_Goal_PBT.IsDigit = false;
      this.newTextBox_FBEZEL_Goal_PBT.Lines = new string[0];
      this.newTextBox_FBEZEL_Goal_PBT.Location = new Point(426, 173);
      this.newTextBox_FBEZEL_Goal_PBT.MultiLine = false;
      this.newTextBox_FBEZEL_Goal_PBT.Name = "newTextBox_FBEZEL_Goal_PBT";
      this.newTextBox_FBEZEL_Goal_PBT.ReadOnly = false;
      this.newTextBox_FBEZEL_Goal_PBT.Size = new Size(142, 20);
      this.newTextBox_FBEZEL_Goal_PBT.TabIndex = 247;
      this.newTextBox_FBEZEL_Goal_PBT.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_FBEZEL_Goal_PBT.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_FBEZEL_Goal_PBT.TextForeColor = SystemColors.WindowText;
      this.newTextBox_FBEZEL_Goal_PBT.Value = "";
      this.newTextBox_FHSG_Goal_PP.BackColor = Color.PapayaWhip;
      this.newTextBox_FHSG_Goal_PP.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_FHSG_Goal_PP.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_FHSG_Goal_PP.IsDigit = false;
      this.newTextBox_FHSG_Goal_PP.Lines = new string[0];
      this.newTextBox_FHSG_Goal_PP.Location = new Point(426, 192);
      this.newTextBox_FHSG_Goal_PP.MultiLine = false;
      this.newTextBox_FHSG_Goal_PP.Name = "newTextBox_FHSG_Goal_PP";
      this.newTextBox_FHSG_Goal_PP.ReadOnly = false;
      this.newTextBox_FHSG_Goal_PP.Size = new Size(142, 20);
      this.newTextBox_FHSG_Goal_PP.TabIndex = 246;
      this.newTextBox_FHSG_Goal_PP.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_FHSG_Goal_PP.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_FHSG_Goal_PP.TextForeColor = SystemColors.WindowText;
      this.newTextBox_FHSG_Goal_PP.Value = "";
      this.newTextBox_FLENS_Goal_PC.BackColor = Color.PapayaWhip;
      this.newTextBox_FLENS_Goal_PC.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_FLENS_Goal_PC.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_FLENS_Goal_PC.IsDigit = false;
      this.newTextBox_FLENS_Goal_PC.Lines = new string[0];
      this.newTextBox_FLENS_Goal_PC.Location = new Point(426, 135);
      this.newTextBox_FLENS_Goal_PC.MultiLine = false;
      this.newTextBox_FLENS_Goal_PC.Name = "newTextBox_FLENS_Goal_PC";
      this.newTextBox_FLENS_Goal_PC.ReadOnly = false;
      this.newTextBox_FLENS_Goal_PC.Size = new Size(142, 20);
      this.newTextBox_FLENS_Goal_PC.TabIndex = 245;
      this.newTextBox_FLENS_Goal_PC.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_FLENS_Goal_PC.TextBoxBackColor = Color.PapayaWhip;
      this.newTextBox_FLENS_Goal_PC.TextForeColor = SystemColors.WindowText;
      this.newTextBox_FLENS_Goal_PC.Value = "";
      this.dataGridView_ShrinkComp.AllowUserToAddRows = false;
      this.dataGridView_ShrinkComp.AllowUserToDeleteRows = false;
      this.dataGridView_ShrinkComp.AllowUserToResizeColumns = false;
      this.dataGridView_ShrinkComp.AllowUserToResizeRows = false;
      this.dataGridView_ShrinkComp.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_ShrinkComp.BackgroundColor = Color.White;
      gridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle6.BackColor = SystemColors.Menu;
      gridViewCellStyle6.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle6.ForeColor = SystemColors.WindowText;
      gridViewCellStyle6.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle6.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle6.WrapMode = DataGridViewTriState.True;
      this.dataGridView_ShrinkComp.ColumnHeadersDefaultCellStyle = gridViewCellStyle6;
      this.dataGridView_ShrinkComp.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_ShrinkComp.Columns.AddRange((DataGridViewColumn) this.Column1, (DataGridViewColumn) this.dataGridViewTextBoxColumn1, (DataGridViewColumn) this.Column7);
      this.dataGridView_ShrinkComp.EnableHeadersVisualStyles = false;
      this.dataGridView_ShrinkComp.Location = new Point(6, 457);
      this.dataGridView_ShrinkComp.Name = "dataGridView_ShrinkComp";
      this.dataGridView_ShrinkComp.RowHeadersVisible = false;
      this.dataGridView_ShrinkComp.RowTemplate.Height = 23;
      this.dataGridView_ShrinkComp.Size = new Size(562, 206);
      this.dataGridView_ShrinkComp.TabIndex = 270;
      this.dataGridView_ShrinkComp.TabStop = false;
      gridViewCellStyle7.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle7.BackColor = Color.Ivory;
      this.Column1.DefaultCellStyle = gridViewCellStyle7;
      this.Column1.HeaderText = "Family Abbreviation";
      this.Column1.Name = "Column1";
      this.Column1.ReadOnly = true;
      this.Column1.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.dataGridViewTextBoxColumn1.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      gridViewCellStyle8.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle8.BackColor = Color.PapayaWhip;
      this.dataGridViewTextBoxColumn1.DefaultCellStyle = gridViewCellStyle8;
      this.dataGridViewTextBoxColumn1.HeaderText = "값";
      this.dataGridViewTextBoxColumn1.Name = "dataGridViewTextBoxColumn1";
      this.dataGridViewTextBoxColumn1.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.dataGridViewTextBoxColumn1.Width = 70;
      this.Column7.HeaderText = "Filber";
      this.Column7.Name = "Column7";
      this.Column7.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.Column7.Visible = false;
      this.newButton_Apply.ButtonBackColor = Color.White;
      this.newButton_Apply.ButtonText = "적용";
      this.newButton_Apply.FlatBorderSize = 1;
      this.newButton_Apply.FlatStyle = FlatStyle.Flat;
      this.newButton_Apply.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Apply.Image = (Image) Resources.OK;
      this.newButton_Apply.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Apply.Location = new Point(6, 889);
      this.newButton_Apply.Name = "newButton_Apply";
      this.newButton_Apply.Size = new Size(572, 33);
      this.newButton_Apply.TabIndex = 23;
      this.newButton_Apply.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Apply.NewClick += new EventHandler(this.newButton_NewClick);
      this.panel1.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.panel1.BorderStyle = BorderStyle.FixedSingle;
      this.panel1.Controls.Add((Control) this.tabControl_Main);
      this.panel1.Location = new Point(6, 31);
      this.panel1.Name = "panel1";
      this.panel1.Size = new Size(572, 854);
      this.panel1.TabIndex = 22;
      this.newButton_View.ButtonBackColor = Color.White;
      this.newButton_View.ButtonText = "뷰 설정";
      this.newButton_View.FlatBorderSize = 1;
      this.newButton_View.FlatStyle = FlatStyle.Flat;
      this.newButton_View.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_View.Image = (Image) null;
      this.newButton_View.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_View.Location = new Point(126, 5);
      this.newButton_View.Name = "newButton_View";
      this.newButton_View.Size = new Size(122, 27);
      this.newButton_View.TabIndex = 20;
      this.newButton_View.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_View.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_View.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_Input.ButtonBackColor = Color.LightSteelBlue;
      this.newButton_Input.ButtonText = "입력 값";
      this.newButton_Input.FlatBorderSize = 1;
      this.newButton_Input.FlatStyle = FlatStyle.Flat;
      this.newButton_Input.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Input.Image = (Image) null;
      this.newButton_Input.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Input.Location = new Point(5, 5);
      this.newButton_Input.Name = "newButton_Input";
      this.newButton_Input.Size = new Size(122, 27);
      this.newButton_Input.TabIndex = 21;
      this.newButton_Input.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Input.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Input.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_Set.ButtonBackColor = Color.White;
      this.newButton_Set.ButtonText = "설정";
      this.newButton_Set.FlatBorderSize = 1;
      this.newButton_Set.FlatStyle = FlatStyle.Flat;
      this.newButton_Set.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Set.Image = (Image) null;
      this.newButton_Set.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Set.Location = new Point(247, 5);
      this.newButton_Set.Name = "newButton_Set";
      this.newButton_Set.Size = new Size(122, 27);
      this.newButton_Set.TabIndex = 24;
      this.newButton_Set.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Set.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Set.NewClick += new EventHandler(this.newButton_NewClick);
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(582, 926);
      this.Controls.Add((Control) this.newButton_Set);
      this.Controls.Add((Control) this.panel1);
      this.Controls.Add((Control) this.newButton_Apply);
      this.Controls.Add((Control) this.newButton_View);
      this.Controls.Add((Control) this.newButton_Input);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmSL);
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "보고서 작성";
      this.FormClosed += new FormClosedEventHandler(this.frmHDSolutions_FormClosed);
      this.Load += new EventHandler(this.frmHDSolutions_Load);
      this.tabControl_Main.ResumeLayout(false);
      this.tabPage_Input.ResumeLayout(false);
      this.tabPage_View.ResumeLayout(false);
      this.tabPage_View.PerformLayout();
      ((ISupportInitialize) this.dataGridView_View).EndInit();
      this.tabPage_Set.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_ShrinkComp).EndInit();
      this.panel1.ResumeLayout(false);
      this.ResumeLayout(false);
    }
  }
}
