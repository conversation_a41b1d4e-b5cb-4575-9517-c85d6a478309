﻿// Decompiled with JetBrains decompiler
// Type: HDMFAI.clsBigData
// Assembly: HDMFAI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: A66A47CE-D63B-4ACB-B9A4-A4F118252C4E
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFAI.dll

using HDLog4Net;
using HDMoldFlowLibrary;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Xml;

namespace HDMFAI
{
  public class clsBigData
  {
    public static string CreateAIBigData(
      string p_strAIFolder,
      string p_strProject,
      Dictionary<string, string> p_dicStudy)
    {
      string aiBigData = string.Empty;
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      Dictionary<string, string> dictionary = new Dictionary<string, string>();
      if (!Directory.Exists(p_strAIFolder))
        Directory.CreateDirectory(p_strAIFolder);
      FileInfo p_fiExport = new FileInfo(p_strAIFolder + "\\AIData.xlsx");
      try
      {
        foreach (KeyValuePair<string, string> keyValuePair in p_dicStudy)
        {
          string str = keyValuePair.Value;
          string path = p_strAIFolder + "\\" + str;
          Dictionary<string, string> studyAiData = clsBigData.GetStudyAIData(str, p_strProject);
          if (studyAiData.Count != 0)
          {
            if (!Directory.Exists(path))
              Directory.CreateDirectory(path);
            aiBigData = clsBigData.CreatJsonData(studyAiData, path + "\\AIData.json");
            clsExcel.ExportAIData(str, studyAiData, p_fiExport);
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsSummary]CreateAIBigData):" + ex.Message));
        aiBigData = ex.Message;
      }
      return aiBigData;
    }

    private static Dictionary<string, string> GetStudyAIData(
      string p_strStudy,
      string p_strDirectoryName)
    {
      Dictionary<string, string> dictionary = new Dictionary<string, string>();
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      p_strStudy.Split('_');
      clsAIDefine.g_strSaveFileName = p_strDirectoryName + "\\" + p_strStudy;
      return !File.Exists(clsAIDefine.g_strSaveFileName + "_Log.xml") ? clsBigData.GetStudyAIDataFromMoldflow(p_strStudy) : clsBigData.GetAIDataFromFile();
    }

    private static string CreatJsonData(Dictionary<string, string> p_dicData, string p_strSavePath)
    {
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      string empty3 = string.Empty;
      string empty4 = string.Empty;
      string empty5 = string.Empty;
      string p_strSendURL = string.Empty;
      Dictionary<string, string> dictionary1 = new Dictionary<string, string>();
      Dictionary<string, string> dictionary2 = new Dictionary<string, string>();
      Dictionary<string, string> dictionary3 = new Dictionary<string, string>();
      Dictionary<string, string> dictionary4 = new Dictionary<string, string>();
      JObject jobject1 = new JObject();
      JObject jobject2 = new JObject();
      JArray jarray = new JArray();
      try
      {
        FileInfo fileInfo = new FileInfo(p_strSavePath);
        if (!fileInfo.Directory.Exists)
          fileInfo.Directory.Create();
        if (fileInfo.Exists)
          fileInfo.Delete();
        JsonConvert.SerializeObject((object) p_dicData);
        jobject1.Add("Study", (JToken) p_dicData["Study"]);
        for (int i = 0; i < clsAIDefine.arr_strInput.Length; i++)
        {
          if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == clsAIDefine.arr_strInput[i])))
            jobject1.Add(clsAIDefine.arr_strInput[i], (JToken) p_dicData[clsAIDefine.arr_strInput[i]]);
        }
        IEnumerable<KeyValuePair<string, string>> keyValuePairs = p_dicData.Where<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key.Contains("Packing_Holding") && !Temp.Key.Contains("merge")));
        jobject1.Add("Packing_Num", (JToken) p_dicData["Packing_Num"]);
        foreach (KeyValuePair<string, string> keyValuePair in keyValuePairs)
        {
          KeyValuePair<string, string> kvpData = keyValuePair;
          string str = kvpData.Value;
          if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "merge_" + kvpData.Key)))
            str = str + "," + p_dicData["merge_" + kvpData.Key];
          jobject1.Add(kvpData.Key, (JToken) str);
        }
        jobject1.Add("Center_X", (JToken) p_dicData["Center_X"]);
        jobject1.Add("Center_Y", (JToken) p_dicData["Center_Y"]);
        jobject1.Add("Center_Z", (JToken) p_dicData["Center_Z"]);
        string str1 = p_dicData["Gate_Count"];
        jobject1.Add("Gate_Count", (JToken) str1);
        int int32 = Convert.ToInt32(str1);
        jarray.Clear();
        for (int i = 0; i < int32; i++)
        {
          Dictionary<string, string> dictionary5 = p_dicData.Where<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key.Contains("Gate" + (object) (i + 1) + "_"))).ToDictionary<KeyValuePair<string, string>, string, string>((System.Func<KeyValuePair<string, string>, string>) (Temp => Temp.Key), (System.Func<KeyValuePair<string, string>, string>) (Temp => Temp.Value));
          JObject jobject3 = new JObject();
          jobject3.Add("Number", (JToken) (i + 1));
          foreach (KeyValuePair<string, string> keyValuePair in (IEnumerable<KeyValuePair<string, string>>) dictionary5)
          {
            string propertyName = keyValuePair.Key.Replace("Gate" + (object) (i + 1), "").Replace("_", "");
            jobject3.Add(propertyName, (JToken) keyValuePair.Value);
          }
          jarray.Add((JToken) jobject3);
        }
        jobject1.Add("Gates", jarray.DeepClone());
        for (int i = 0; i < clsAIDefine.arr_strIntermediate.Length; i++)
        {
          if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == clsAIDefine.arr_strIntermediate[i])))
            jobject1.Add(clsAIDefine.arr_strIntermediate[i], (JToken) p_dicData[clsAIDefine.arr_strIntermediate[i]]);
        }
        jarray.Clear();
        string strKey = string.Empty;
        for (int index = 0; index < 20; ++index)
        {
          if (index != 0)
          {
            strKey = "Filling_Percentage_" + (object) (index * 5);
            Dictionary<string, string> dictionary6 = p_dicData.Where<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key.Contains(strKey + "_"))).ToDictionary<KeyValuePair<string, string>, string, string>((System.Func<KeyValuePair<string, string>, string>) (Temp => Temp.Key), (System.Func<KeyValuePair<string, string>, string>) (Temp => Temp.Value));
            JObject jobject4 = new JObject();
            jobject4.Add("Percentage", (JToken) (index * 5));
            foreach (KeyValuePair<string, string> keyValuePair in (IEnumerable<KeyValuePair<string, string>>) dictionary6)
            {
              string propertyName = keyValuePair.Key.Replace(strKey, "").Replace("_", "");
              jobject4.Add(propertyName, (JToken) keyValuePair.Value);
            }
            jarray.Add((JToken) jobject4);
          }
        }
        jobject1.Add("Filling", jarray.DeepClone());
        for (int i = 0; i < clsAIDefine.arr_strOutput.Length; i++)
        {
          if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == clsAIDefine.arr_strOutput[i])))
          {
            string str2 = p_dicData[clsAIDefine.arr_strOutput[i]];
            if (clsAIDefine.arr_strOutput[i].Contains("component"))
              str2 = str2 + "," + p_dicData["merge_" + clsAIDefine.arr_strOutput[i]];
            jobject1.Add(clsAIDefine.arr_strOutput[i], (JToken) str2);
          }
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Airtraps")))
        {
          string str3 = p_dicData["Airtraps"];
          if (!string.IsNullOrEmpty(str3))
          {
            string str4 = str3;
            char[] chArray1 = new char[1]{ '|' };
            foreach (string str5 in str4.Split(chArray1))
            {
              char[] chArray2 = new char[1]{ '/' };
              string[] strArray = str5.Split(chArray2);
              if (!dictionary1.ContainsKey(strArray[0]))
                dictionary1.Add(strArray[0], strArray[1]);
            }
          }
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Weldlines")))
        {
          string str6 = p_dicData["Weldlines"];
          if (!string.IsNullOrEmpty(str6))
          {
            string str7 = str6;
            char[] chArray3 = new char[1]{ '|' };
            foreach (string str8 in str7.Split(chArray3))
            {
              char[] chArray4 = new char[1]{ '/' };
              string[] strArray = str8.Split(chArray4);
              if (!dictionary2.ContainsKey(strArray[0]))
                dictionary2.Add(strArray[0], strArray[1]);
            }
          }
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Filltime")))
        {
          string str9 = p_dicData["Filltime"];
          if (!string.IsNullOrEmpty(str9))
          {
            string str10 = str9;
            char[] chArray5 = new char[1]{ '|' };
            foreach (string str11 in str10.Split(chArray5))
            {
              char[] chArray6 = new char[1]{ '/' };
              string[] strArray = str11.Split(chArray6);
              if (!dictionary3.ContainsKey(strArray[0]))
                dictionary3.Add(strArray[0], strArray[1]);
            }
          }
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Deflectionall")))
        {
          string str12 = p_dicData["Deflectionall"];
          if (!string.IsNullOrEmpty(str12))
          {
            string str13 = str12;
            char[] chArray7 = new char[1]{ '|' };
            foreach (string str14 in str13.Split(chArray7))
            {
              char[] chArray8 = new char[1]{ '/' };
              string[] strArray = str14.Split(chArray8);
              if (!dictionary4.ContainsKey(strArray[0]))
                dictionary4.Add(strArray[0], strArray[1]);
            }
          }
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Nodes")))
        {
          string[] strArray1 = p_dicData["Nodes"].Split('|');
          jarray.Clear();
          for (int index = 0; index < strArray1.Length; ++index)
          {
            string[] strArray2 = strArray1[index].Split('/');
            JObject jobject5 = new JObject();
            jobject5.Add("Node", (JToken) strArray2[0]);
            string[] strArray3 = strArray2[1].Split(',');
            jobject5.Add("X", (JToken) strArray3[0]);
            jobject5.Add("Y", (JToken) strArray3[1]);
            jobject5.Add("Z", (JToken) strArray3[2]);
            if (dictionary2.ContainsKey(strArray2[1]))
              jobject5.Add("Weld_Line", (JToken) dictionary2[strArray2[1]]);
            if (dictionary1.ContainsKey(strArray2[1]))
              jobject5.Add("Air_Trap", (JToken) dictionary1[strArray2[1]]);
            if (dictionary3.ContainsKey(strArray2[1]))
              jobject5.Add("Fill_Time", (JToken) dictionary3[strArray2[1]]);
            if (dictionary4.ContainsKey(strArray2[1]))
            {
              string[] strArray4 = dictionary4[strArray2[1]].Split(new char[1]
              {
                ' '
              }, StringSplitOptions.RemoveEmptyEntries);
              double num = clsUtill.ConvertToDouble(strArray4[0]) + clsUtill.ConvertToDouble(strArray3[0]);
              string str15 = num.ToString();
              jobject5.Add("X'", (JToken) str15);
              num = clsUtill.ConvertToDouble(strArray4[1]) + clsUtill.ConvertToDouble(strArray3[1]);
              string str16 = num.ToString();
              jobject5.Add("Y'", (JToken) str16);
              num = clsUtill.ConvertToDouble(strArray4[2]) + clsUtill.ConvertToDouble(strArray3[2]);
              string str17 = num.ToString();
              jobject5.Add("Z'", (JToken) str17);
            }
            jarray.Add((JToken) jobject5);
          }
          jobject1.Add("Nodes", jarray.DeepClone());
        }
        string p_strJsonData = jobject1.ToString();
        using (StreamWriter streamWriter = new StreamWriter(p_strSavePath))
        {
          streamWriter.Write(p_strJsonData);
          streamWriter.Flush();
          streamWriter.Close();
        }
        if (clsAIDefine.g_fiAICfg != null)
          p_strSendURL = clsUtill.ReadINI("Sub", "BigData", clsAIDefine.g_fiAICfg.FullName);
        if (string.IsNullOrEmpty(p_strSendURL))
          p_strSendURL = "/api/bigdata";
        return clsWeb.SendDataToWeb(p_strJsonData, p_strSendURL);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsBigData]SaveAIData):" + ex.Message));
        return ex.Message;
      }
    }

    private static Dictionary<string, string> GetAIDataFromFile()
    {
      Dictionary<string, string> p_dicData = new Dictionary<string, string>();
      try
      {
        clsBigData.GetLogDataFromFile(ref p_dicData);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsBigData]GetAIDataFromFile):" + ex.Message));
      }
      return p_dicData;
    }

    private static void GetLogDataFromFile(ref Dictionary<string, string> p_dicData)
    {
      FileInfo fileInfo = new FileInfo(clsAIDefine.g_strSaveFileName + "_Log.xml");
      try
      {
        using (XmlReader reader = XmlReader.Create(fileInfo.FullName))
        {
          XmlDocument xmlDocument = new XmlDocument();
          xmlDocument.Load(reader);
          XmlElement documentElement = xmlDocument.DocumentElement;
          if (documentElement == null)
            return;
          foreach (XmlNode childNode in documentElement.ChildNodes)
            p_dicData.Add(childNode.Name, childNode.InnerText);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsBigData]GetLogDataFromFile):" + ex.Message));
      }
    }

    private static void GetNodeDataFromFile(ref Dictionary<string, string> p_dicData)
    {
      StringBuilder stringBuilder = new StringBuilder();
      using (XmlReader reader = XmlReader.Create(new FileInfo(clsAIDefine.g_strSaveFileName + "_Nodes.xml").FullName))
      {
        XmlDocument xmlDocument = new XmlDocument();
        xmlDocument.Load(reader);
        XmlElement documentElement = xmlDocument.DocumentElement;
        if (documentElement != null)
        {
          foreach (XmlNode childNode in documentElement.ChildNodes)
          {
            if (stringBuilder.Length != 0)
              stringBuilder.Append("|");
            stringBuilder.Append(childNode.Name + "/" + childNode.InnerText);
          }
        }
      }
      p_dicData.Add("Nodes", stringBuilder.ToString());
    }

    private static Dictionary<string, string> GetStudyAIDataFromMoldflow(string p_strStudy)
    {
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      DataTable p_dtDet = new DataTable();
      List<string> p_lst_strALog = new List<string>();
      Dictionary<string, string> dictionary = new Dictionary<string, string>();
      Dictionary<string, string> p_dicData = new Dictionary<string, string>();
      try
      {
        clsHDMFLib.OpenStudy(p_strStudy);
        if (!clsHDMFLib.ExistAnalysis())
          return p_dicData;
        FileInfo fileInfo = new FileInfo(clsAIDefine.g_diTmpAI.ToString() + "\\" + p_strStudy + "\\ALog.log");
        if (!fileInfo.Exists)
          p_lst_strALog = clsHDMFLib.ExportAnalysisLog(fileInfo.FullName);
        else
          p_lst_strALog.AddRange((IEnumerable<string>) File.ReadAllLines(fileInfo.FullName, Encoding.Default));
        if (p_lst_strALog.Count > 0)
        {
          Dictionary<string, string> allNodes = clsHDMFLib.GetAllNodes(ref p_dtDet);
          string str1 = clsHDMFLib.GetGateNodeFromStudyNote();
          string[] arr_strGateNode;
          if (str1 == string.Empty)
          {
            arr_strGateNode = clsHDMFLib.GetGateNodes(allNodes, p_dtDet);
            str1 = string.Join(" ", arr_strGateNode);
            clsHDMFLib.WriteGateNodeToStudyNote(str1);
          }
          else
            arr_strGateNode = ((IEnumerable<string>) str1.Split(' ')).Where<string>((System.Func<string, bool>) (Temp => !string.IsNullOrEmpty(Temp))).ToArray<string>();
          DataTable allLayers = clsHDMFLib.GetAllLayers();
          string str2 = clsHDMFLib.GetMeshType();
          p_dicData.Add("Study", p_strStudy);
          string[] strArray = p_strStudy.Split('_');
          if (strArray.Length > 3)
            empty1 = strArray[3];
          p_dicData.Add("Study_Group", empty1);
          if (str2.ToLower() == "dual")
            str2 = "2D";
          p_dicData.Add("Mesh_Type", str2);
          clsBigData.GetLogData(p_lst_strALog, ref p_dicData);
          clsBigData.GetPlotData(allLayers, allNodes, str1, ref p_dicData);
          clsBigData.GetNodesData(p_lst_strALog, allNodes, arr_strGateNode, p_dtDet, ref p_dicData);
          clsBigData.SaveLogToXml(p_dicData);
        }
        clsHDMFLib.CloseStudy(false);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsBigData]GetStudyAIDataFromMoldflow):" + ex.Message));
      }
      return p_dicData;
    }

    private static void GetLogData(
      List<string> p_lst_strALog,
      ref Dictionary<string, string> p_dicData)
    {
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      string empty3 = string.Empty;
      Dictionary<string, string> dictionary1 = new Dictionary<string, string>();
      try
      {
        List<DataRow> p_lst_drFillPhase = new List<DataRow>((IEnumerable<DataRow>) clsHDMFLib.GetFillingPhaseData(p_lst_strALog).Rows.Cast<DataRow>().ToArray<DataRow>());
        List<DataRow> p_lst_drPhase = new List<DataRow>((IEnumerable<DataRow>) clsHDMFLib.GetPackingPhaseData(p_lst_strALog).Rows.Cast<DataRow>().ToArray<DataRow>());
        List<DataRow> p_lst_drAllPhase = new List<DataRow>();
        p_lst_drAllPhase.AddRange((IEnumerable<DataRow>) p_lst_drFillPhase.ToArray());
        p_lst_drAllPhase.AddRange((IEnumerable<DataRow>) p_lst_drPhase.ToArray());
        string switchOverDataFromLog = clsHDMFLib.GetVPSwitchOverDataFromLog(p_lst_drFillPhase);
        if (switchOverDataFromLog != string.Empty)
        {
          string[] strArray = switchOverDataFromLog.Split('|');
          p_dicData.Add("VP_time", strArray[0]);
          p_dicData.Add("VP_flow_rate", strArray[1]);
          p_dicData.Add("VP_Pressure", strArray[2]);
        }
        string fillTimeFromLog = clsHDMFLib.GetFillTimeFromLog(p_lst_strALog);
        double num1;
        if (fillTimeFromLog != "" || fillTimeFromLog.Contains("Injection"))
        {
          string[] strArray = fillTimeFromLog.Split('|');
          p_dicData.Add("Filling_control_Injection_time", strArray[1]);
        }
        else
        {
          Dictionary<string, string> dictionary2 = p_dicData;
          num1 = Math.Round(clsUtill.ConvertToDouble(p_dicData["VP_time"]), 1);
          string str = num1.ToString();
          dictionary2.Add("Filling_control_Injection_time", str);
        }
        string analysisSequence = clsHDMFLib.GetAnalysisSequence();
        p_dicData.Add("Analysis_Type", analysisSequence.Replace("|", "_").Replace(" ", string.Empty).Replace("Fill", "Flow"));
        p_dicData.Add("Melt_temperature", clsHDMFLib.GetMeltTemperatureDataFromProcessSet());
        p_dicData.Add("Mold_temperature", clsHDMFLib.GetMoldTemperatureDataFromProcessSet());
        p_dicData.Add("Velocity_pressure_switch_over", clsHDMFLib.GetVelocityPressureSwitchOverFromLog(p_lst_strALog));
        if (p_lst_drPhase.Count != 0)
          p_dicData.Add("cooling_time", clsHDMFLib.GetCoolingTimeFromPhase(p_lst_drPhase));
        string injectionLocationFromLog = clsHDMFLib.GetPressureAtInjectionLocationFromLog(p_lst_drAllPhase);
        if (injectionLocationFromLog != "")
        {
          string[] strArray1 = injectionLocationFromLog.Split('/');
          p_dicData.Add("Packing_Num", strArray1.Length.ToString());
          for (int index = 0; index < strArray1.Length; ++index)
          {
            string[] strArray2 = strArray1[index].Split('|');
            p_dicData.Add("Packing_Holding_" + (object) (index + 1), strArray2[1]);
            p_dicData.Add("merge_Packing_Holding_" + (object) (index + 1), strArray2[0]);
          }
        }
        p_dicData.Add("Total_Volume", clsHDMFLib.GetDataFromLog("Total volume", true, true, p_lst_strALog));
        p_dicData.Add("Part_Volume", clsHDMFLib.GetPartVolumeFromLog(p_lst_strALog));
        double[] modelLengths = clsHDMFLib.GetModelLengths();
        if (modelLengths.Length > 2)
        {
          p_dicData.Add("X_Length", modelLengths[0].ToString());
          p_dicData.Add("Y_Length", modelLengths[1].ToString());
          p_dicData.Add("Z_Length", modelLengths[2].ToString());
        }
        p_dicData.Add("Material_Company", clsHDMFLib.GetManufacturer());
        p_dicData.Add("Trade_Name", clsHDMFLib.GetTradeName());
        p_dicData.Add("Material_Type", clsHDMFLib.GetFamilyAbbreviation());
        p_dicData.Add("Material_Density", clsHDMFLib.GetMaterialSolidDensity());
        p_dicData.Add("Filled_time", clsHDMFLib.GetFillLastTimeFromLog(p_lst_drFillPhase));
        p_dicData.Add("Short_shot", clsHDMFLib.GetShortShotFromLog(p_lst_strALog));
        for (int p_dblPercentage = 0; p_dblPercentage < 100; ++p_dblPercentage)
        {
          if (p_dblPercentage != 0 && p_dblPercentage % 5 == 0)
          {
            string dataByPercentage = clsHDMFLib.GetFillingDataByPercentage(p_lst_drFillPhase, (double) p_dblPercentage);
            if (dataByPercentage != string.Empty)
            {
              string[] strArray = dataByPercentage.Split('|');
              if (strArray.Length > 2)
              {
                p_dicData.Add("Filling_Percentage_" + p_dblPercentage.ToString() + "_time", strArray[0]);
                p_dicData.Add("Filling_Percentage_" + p_dblPercentage.ToString() + "_flow_rate", strArray[1]);
                p_dicData.Add("Filling_Percentage_" + p_dblPercentage.ToString() + "_Pressure", strArray[2]);
              }
            }
          }
        }
        p_dicData.Add("Frozen_95_Time", clsHDMFLib.GetPercentFrozenTimeFromLog(95, p_lst_drAllPhase));
        p_dicData.Add("Frozen_99_Time", clsHDMFLib.GetPercentFrozenTimeFromLog(99, p_lst_drAllPhase));
        p_dicData.Add("Total_Project_Area", clsHDMFLib.GetDataFromLog("Total projected area", true, true, p_lst_strALog));
        string dataFromLog1 = clsHDMFLib.GetDataFromLog("Part surface temperature - minimum", true, true, p_lst_strALog);
        string dataFromLog2 = clsHDMFLib.GetDataFromLog("Part surface temperature - maximum", true, true, p_lst_strALog);
        if (dataFromLog1 != "" && dataFromLog2 != "")
        {
          double num2 = clsHDMFLibUtil.ConvertToDouble(clsHDMFLib.GetDataFromLog("Part surface temperature - minimum", true, true, p_lst_strALog));
          double num3 = clsHDMFLibUtil.ConvertToDouble(clsHDMFLib.GetDataFromLog("Part surface temperature - maximum", true, true, p_lst_strALog));
          p_dicData.Add("Minimum_Part_temperature", num2.ToString());
          p_dicData.Add("Maximum_Part_temperature", num3.ToString());
          Dictionary<string, string> dictionary3 = p_dicData;
          num1 = Math.Abs(num3 - num2);
          string str = num1.ToString();
          dictionary3.Add("Diff_Part_temperature", str);
        }
        string dataFromLog3 = clsHDMFLib.GetDataFromLog("Cavity surface temperature - minimum", true, true, p_lst_strALog);
        string dataFromLog4 = clsHDMFLib.GetDataFromLog("Cavity surface temperature - maximum", true, true, p_lst_strALog);
        if (dataFromLog3 != "" && dataFromLog4 != "")
        {
          double num4 = clsHDMFLibUtil.ConvertToDouble(clsHDMFLib.GetDataFromLog("Cavity surface temperature - minimum", true, true, p_lst_strALog));
          double num5 = clsHDMFLibUtil.ConvertToDouble(clsHDMFLib.GetDataFromLog("Cavity surface temperature - maximum", true, true, p_lst_strALog));
          p_dicData.Add("Minimum_Mold_temperature", num4.ToString());
          p_dicData.Add("Maximum_Mold_temperature", num5.ToString());
          Dictionary<string, string> dictionary4 = p_dicData;
          num1 = Math.Abs(num5 - num4);
          string str = num1.ToString();
          dictionary4.Add("Diff_Mold_temperature", str);
        }
        p_dicData.Add("Cavity_surface_temperature_average", clsHDMFLib.GetDataFromLog("Cavity surface temperature - average", true, true, p_lst_strALog));
        p_dicData.Add("Injection_pressure", clsHDMFLib.GetSpruePressureFromLog(p_lst_drAllPhase));
        p_dicData.Add("Clamp_force", clsHDMFLib.GetClampForceFromLog2(p_lst_drAllPhase));
        string shrinkageFromLog = clsHDMFLib.GetVolumetricShrinkageFromLog(p_lst_strALog);
        if (shrinkageFromLog != "")
        {
          string[] strArray = shrinkageFromLog.Split('|');
          p_dicData.Add("Minimum_Volumetric_shrinkage_at_ejection", strArray[0]);
          p_dicData.Add("Maximum_Volumetric_shrinkage_at_ejection", strArray[1]);
          p_dicData.Add("Diff_Volumetric_shrinkage_at_ejection", strArray[2]);
        }
        p_dicData.Add("Total_mass", clsHDMFLib.GetPartMassFromLog(p_lst_strALog));
        string transDataFromLog = clsHDMFLib.GetTransDataFromLog(1, p_lst_strALog);
        if (!(transDataFromLog != ""))
          return;
        string[] strArray3 = transDataFromLog.Split('|');
        string[] strArray4 = strArray3[0].Split('/');
        p_dicData.Add("Range_X_component", strArray4[0]);
        p_dicData.Add("merge_Range_X_component", strArray4[1]);
        string[] strArray5 = strArray3[1].Split('/');
        p_dicData.Add("Range_Y_component", strArray5[0]);
        p_dicData.Add("merge_Range_Y_component", strArray5[1]);
        string[] strArray6 = strArray3[2].Split('/');
        p_dicData.Add("Range_Z_component", strArray6[0]);
        p_dicData.Add("merge_Range_Z_component", strArray6[1]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsBigData]GetLogData):" + ex.Message));
      }
    }

    private static void GetPlotData(
      DataTable p_dtAllLayer,
      Dictionary<string, string> p_dicAllNodes,
      string p_strGate,
      ref Dictionary<string, string> p_dicData)
    {
      double num = 0.0;
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      string empty3 = string.Empty;
      StringBuilder stringBuilder = new StringBuilder();
      string[] strArray1 = new string[4]
      {
        "Weldlines|Weld lines|Weld lines",
        "Airtraps|Air traps|Air traps (3D)",
        "Filltime|Fill time|Fill time",
        "Deflectionall|Deflection, all effects:Deflection|Deflection, all effects"
      };
      List<string> stringList1 = new List<string>();
      List<string> stringList2 = new List<string>();
      clsHDMFLib.ChangeActiveLayer(2, p_dtAllLayer, false, 0L);
      try
      {
        string dataFromPlotFormat = clsHDMFLib.GetThicknessDataFromPlotFormat();
        if (dataFromPlotFormat != string.Empty)
        {
          string[] strArray2 = dataFromPlotFormat.Split('/')[0].Split('|');
          if (strArray2.Length > 1)
          {
            p_dicData.Add("Thickness", strArray2[0]);
            p_dicData.Add("Thickness_Percentage", strArray2[1]);
          }
        }
        p_dicData.Add("Cycle_time", clsHDMFLib.GetCycleTimeFromLog());
        string plotValue = clsHDMFLib.GetPlotValue("Temperature at flow front", "", 3);
        if (plotValue != "")
        {
          string[] strArray3 = plotValue.Split('|');
          p_dicData.Add("Minimum_Temperature_at_flow_front", strArray3[0]);
          p_dicData.Add("Maximum_Temperature_at_flow_front", strArray3[1]);
          p_dicData.Add("Diff_Temperature_at_flow_front", strArray3[2]);
        }
        p_dicData.Add("Maximum_Time_to_reach_ejection_temperature_Cool", clsHDMFLib.GetPlotValue("Time to reach ejection temperature, part", "", 1));
        p_dicData.Add("Maximum_Sink_mark_depth", clsHDMFLib.GetPlotValue("Sink marks estimate", "1310", 1));
        p_dicData.Add("Range_Deflection", clsHDMFLib.GetPlotValue("Deflection, all effects:Deflection", "", 1));
        double frozenTimeFromPlot = clsHDMFLib.GetFrozenTimeFromPlot(clsHDMFLib.GetMeshType(), "", p_strGate, p_dtAllLayer);
        if (p_dicData["Frozen_99_Time"] == string.Empty)
        {
          p_dicData["Frozen_99_Time"] = frozenTimeFromPlot.ToString();
          p_dicData["Frozen_95_Time"] = Math.Round(frozenTimeFromPlot * 0.95, 2).ToString();
        }
        p_dicData.Add("Frozen_Gate_Time", frozenTimeFromPlot.ToString());
        for (int index = 0; index < strArray1.Length; ++index)
        {
          stringBuilder.Clear();
          stringList2.Clear();
          string[] strArray4 = strArray1[index].Split('|');
          foreach (string str1 in clsHDMFLib.GetNodeDataFromPlotFormat(strArray4[0], strArray4[1], strArray4[2]))
          {
            char[] chArray = new char[1]{ '|' };
            string[] strArray5 = str1.Split(chArray);
            if (p_dicAllNodes.ContainsKey(strArray5[0]))
            {
              string pDicAllNode = p_dicAllNodes[strArray5[0]];
              if (!stringList2.Contains(strArray5[0]))
              {
                string p_strValue1 = strArray5[1];
                string str2;
                if (index == 3)
                {
                  string[] strArray6 = p_strValue1.Split(new char[1]
                  {
                    ' '
                  }, StringSplitOptions.RemoveEmptyEntries);
                  str2 = string.Empty;
                  foreach (string p_strValue2 in strArray6)
                  {
                    if (p_strValue2.Contains("e-") || p_strValue2.Contains("e+"))
                      num = Math.Round(clsHDMFLib.ParsingAnalysisValue(p_strValue2) * 1000.0, 4);
                    str2 = str2 + num.ToString() + " ";
                  }
                }
                else
                {
                  num = p_strValue1.ToLower().Contains("e-") || p_strValue1.ToLower().Contains("e+") ? Math.Round(clsHDMFLib.ParsingAnalysisValue(p_strValue1), 4) : clsUtill.ConvertToDouble(p_strValue1);
                  if (num <= 1000000.0)
                  {
                    switch (index)
                    {
                      case 0:
                        num = Math.Round(180.0 / Math.PI * num, 4);
                        break;
                      case 1:
                        if (num == 0.0)
                          continue;
                        break;
                    }
                    str2 = num.ToString();
                  }
                  else
                    continue;
                }
                stringList2.Add(strArray5[0]);
                if (stringBuilder.Length != 0)
                  stringBuilder.Append("|");
                stringBuilder.Append(pDicAllNode + "/" + str2);
              }
            }
          }
          p_dicData.Add(strArray4[0], stringBuilder.ToString());
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsBigData]GetPlotData):" + ex.Message));
      }
    }

    private static void GetNodesData(
      List<string> p_lst_strALog,
      Dictionary<string, string> p_dicAllNodes,
      string[] arr_strGateNode,
      DataTable p_dtDet,
      ref Dictionary<string, string> p_dicData)
    {
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      string empty3 = string.Empty;
      string empty4 = string.Empty;
      StringBuilder stringBuilder = new StringBuilder();
      List<string> stringList = new List<string>();
      Dictionary<string, string> p_dicAllNodes1 = new Dictionary<string, string>((IDictionary<string, string>) p_dicAllNodes);
      try
      {
        p_dicData.Add("Gate_Count", arr_strGateNode.Length.ToString());
        List<string> dataFromPlotFormat = clsHDMFLib.GetNodeDataFromPlotFormat("Filltime", "Fill time", "Fill time");
        for (int index = 0; index < arr_strGateNode.Length; ++index)
        {
          string coordFromNode = clsHDMFLib.GetCoordFromNode(arr_strGateNode[index]);
          if (coordFromNode != "")
          {
            string[] strArray = coordFromNode.Split('|');
            p_dicData.Add("Gate" + (object) (index + 1) + "_X", Math.Round(clsHDMFLibUtil.ConvertToDouble(strArray[0]), 2).ToString());
            p_dicData.Add("Gate" + (object) (index + 1) + "_Y", Math.Round(clsHDMFLibUtil.ConvertToDouble(strArray[1]), 2).ToString());
            p_dicData.Add("Gate" + (object) (index + 1) + "_Z", Math.Round(clsHDMFLibUtil.ConvertToDouble(strArray[2]), 2).ToString());
          }
          string nodeWithFillTime = clsHDMFLib.GetValveDataFromNodeWithFillTime(arr_strGateNode[index], p_lst_strALog, dataFromPlotFormat);
          if (nodeWithFillTime != "")
          {
            string[] strArray = nodeWithFillTime.Split('|');
            p_dicData.Add("Gate" + (object) (index + 1) + "_Valve_Open", strArray[0]);
            p_dicData.Add("Gate" + (object) (index + 1) + "_Valve_Close", strArray[1]);
          }
          string gateInfoFromNode = clsHDMFLib.GetGateInfoFromNode(arr_strGateNode[index], p_dicAllNodes1);
          if (gateInfoFromNode != "")
          {
            string[] strArray = gateInfoFromNode.Split('|');
            p_dicData.Add("Gate" + (object) (index + 1) + "_Property", strArray[1]);
            p_dicData.Add("Gate" + (object) (index + 1) + "_Type", strArray[2]);
            if (strArray.Length < 5)
            {
              p_dicData.Add("Gate" + (object) (index + 1) + "_Dimension", strArray[3]);
            }
            else
            {
              p_dicData.Add("Gate" + (object) (index + 1) + "_width", strArray[3]);
              p_dicData.Add("Gate" + (object) (index + 1) + "_height", strArray[4]);
            }
          }
        }
        if (arr_strGateNode.Length != 0)
        {
          string[] strArray = clsHDMFLib.GetCoreAndCavityNodeFromStudyNote();
          if (strArray.Length == 0)
          {
            strArray = clsHDMFLib.GetCavityCoreNodes(p_dicAllNodes, p_lst_strALog, arr_strGateNode[0]);
            if (strArray.Length > 1)
            {
              clsHDMFLib.WriteCoreToStudyNote(strArray[0]);
              clsHDMFLib.WriteCavityToStudyNote(strArray[1]);
            }
          }
          if (strArray.Length == 2)
          {
            empty3 = strArray[0];
            empty4 = strArray[1];
          }
        }
        string cavityCoreFromLog1 = clsHDMFLib.GetCircuitCoolantTemperatureCavityCoreFromLog(p_lst_strALog, empty3);
        if (cavityCoreFromLog1 != "")
        {
          string[] strArray = cavityCoreFromLog1.Split('|');
          p_dicData.Add("Minimum_Circuit_coolant_temperature_Core", strArray[0]);
          p_dicData.Add("Maximum_Circuit_coolant_temperature_Core", strArray[1]);
          p_dicData.Add("Diff_Circuit_coolant_temperature_Core", strArray[2]);
        }
        string cavityCoreFromLog2 = clsHDMFLib.GetCircuitCoolantTemperatureCavityCoreFromLog(p_lst_strALog, empty4);
        if (cavityCoreFromLog2 != "")
        {
          string[] strArray = cavityCoreFromLog2.Split('|');
          p_dicData.Add("Minimum_Circuit_coolant_temperature_Cavity", strArray[0]);
          p_dicData.Add("Maximum_Circuit_coolant_temperature_Cavity", strArray[1]);
          p_dicData.Add("Diff_Circuit_coolant_temperature_Cavity", strArray[2]);
        }
        foreach (DataRow row in (InternalDataCollectionBase) p_dtDet.Rows)
        {
          string str1 = "N" + row["SNode"].ToString();
          if (!stringList.Contains(str1) && !((IEnumerable<string>) arr_strGateNode).Contains<string>(str1))
            stringList.Add(str1);
          string str2 = "N" + row["ENode"].ToString();
          if (!stringList.Contains(str2) && !((IEnumerable<string>) arr_strGateNode).Contains<string>(str2))
            stringList.Add(str2);
        }
        for (int index = 0; index < stringList.Count; ++index)
        {
          if (p_dicAllNodes1.ContainsKey(stringList[index]))
            p_dicAllNodes1.Remove(stringList[index]);
        }
        double[] modelCenterPoint = clsHDMFLib.GetModelCenterPoint(p_dicAllNodes1);
        if (modelCenterPoint.Length > 2)
        {
          p_dicData.Add("Center_X", modelCenterPoint[0].ToString());
          p_dicData.Add("Center_Y", modelCenterPoint[1].ToString());
          p_dicData.Add("Center_Z", modelCenterPoint[2].ToString());
        }
        foreach (KeyValuePair<string, string> keyValuePair in p_dicAllNodes1)
        {
          if (stringBuilder.Length != 0)
            stringBuilder.Append("|");
          stringBuilder.Append(keyValuePair.Key + "/" + keyValuePair.Value);
        }
        p_dicData.Add("Nodes", stringBuilder.ToString());
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsBigData]GetNodesData):" + ex.Message));
      }
    }

    private static void SavePartNodesToXml(
      Dictionary<string, string> p_dicData,
      string[] arr_strGateNode)
    {
      string empty = string.Empty;
      Dictionary<string, string> p_dicPlotData = new Dictionary<string, string>();
      try
      {
        string str1 = p_dicData["Nodes"];
        char[] chArray1 = new char[1]{ '|' };
        foreach (string str2 in str1.Split(chArray1))
        {
          char[] chArray2 = new char[1]{ '/' };
          string[] strArray = str2.Split(chArray2);
          p_dicPlotData.Add(strArray[0], strArray[1]);
        }
        clsBigData.SaveNodesDataToXml("Nodes", p_dicPlotData);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsBigData]SavePartNodes):" + ex.Message));
      }
    }

    private static void SavePlotData(Dictionary<string, string> p_dicAllNodes)
    {
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      string[] strArray1 = new string[2]
      {
        "Weldlines|Weld lines|Weld lines",
        "Airtraps|Air traps|Air traps (3D)"
      };
      List<string> stringList = new List<string>();
      Dictionary<string, string> p_dicPlotData = new Dictionary<string, string>();
      try
      {
        for (int index = 0; index < strArray1.Length; ++index)
        {
          p_dicPlotData.Clear();
          string[] strArray2 = strArray1[index].Split('|');
          foreach (string str in clsHDMFLib.GetNodeDataFromPlotFormat(strArray2[0], strArray2[1], strArray2[2]))
          {
            char[] chArray = new char[1]{ '|' };
            string[] strArray3 = str.Split(chArray);
            if (p_dicAllNodes.ContainsKey(strArray3[0]))
            {
              string pDicAllNode = p_dicAllNodes[strArray3[0]];
              if (!p_dicPlotData.ContainsKey(strArray3[0]))
              {
                string p_strValue = strArray3[1];
                if (index == 0)
                  p_strValue = Math.Round(180.0 / Math.PI * clsUtill.ConvertToDouble(p_strValue), 4).ToString();
                else if (clsUtill.ConvertToDouble(p_strValue) == 0.0)
                  continue;
                p_dicPlotData.Add(strArray3[0], p_strValue + "|" + pDicAllNode);
              }
            }
          }
          clsBigData.SaveNodesDataToXml(strArray2[0], p_dicPlotData);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsBigData]SavePlotData):" + ex.Message));
      }
    }

    private static void SaveNodesDataToXml(
      string p_strFileName,
      Dictionary<string, string> p_dicPlotData)
    {
      try
      {
        FileInfo fileInfo = new FileInfo(clsAIDefine.g_strSaveFileName + "_" + p_strFileName + ".xml");
        if (!fileInfo.Directory.Exists)
          fileInfo.Directory.Create();
        if (fileInfo.Exists)
          fileInfo.Delete();
        using (XmlWriter xmlWriter = XmlWriter.Create(fileInfo.FullName, new XmlWriterSettings()
        {
          Indent = true,
          OmitXmlDeclaration = true,
          NewLineOnAttributes = true
        }))
        {
          xmlWriter.WriteStartDocument();
          xmlWriter.WriteStartElement("Nodes");
          xmlWriter.WriteEndElement();
          xmlWriter.Flush();
        }
        using (XmlReader reader = XmlReader.Create(fileInfo.FullName))
        {
          XmlDocument xmlDocument = new XmlDocument();
          xmlDocument.Load(reader);
          XmlElement documentElement = xmlDocument.DocumentElement;
          documentElement.RemoveAll();
          foreach (KeyValuePair<string, string> keyValuePair in p_dicPlotData)
          {
            XmlNode element = (XmlNode) xmlDocument.CreateElement(keyValuePair.Key);
            if (keyValuePair.Value.Contains<char>('|'))
            {
              string[] strArray = keyValuePair.Value.Split('|');
              XmlAttribute attribute = xmlDocument.CreateAttribute("Value");
              attribute.Value = strArray[0];
              element.Attributes.Append(attribute);
              element.InnerText = strArray[1];
            }
            else
              element.InnerText = keyValuePair.Value;
            documentElement.AppendChild(element);
          }
          reader.Close();
          xmlDocument.Save(fileInfo.FullName);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsBigData]SaveNodesDataToXml):" + ex.Message));
      }
    }

    private static void SaveLogToXml(Dictionary<string, string> p_dicData)
    {
      try
      {
        FileInfo fileInfo = new FileInfo(clsAIDefine.g_strSaveFileName + "_Log.xml");
        if (!fileInfo.Directory.Exists)
          fileInfo.Directory.Create();
        if (fileInfo.Exists)
          fileInfo.Delete();
        using (XmlWriter xmlWriter = XmlWriter.Create(fileInfo.FullName, new XmlWriterSettings()
        {
          Indent = true,
          OmitXmlDeclaration = true,
          NewLineOnAttributes = true
        }))
        {
          xmlWriter.WriteStartDocument();
          xmlWriter.WriteStartElement("Data");
          xmlWriter.WriteEndElement();
          xmlWriter.Flush();
        }
        using (XmlReader reader = XmlReader.Create(fileInfo.FullName))
        {
          XmlDocument xmlDocument = new XmlDocument();
          xmlDocument.Load(reader);
          XmlElement documentElement = xmlDocument.DocumentElement;
          documentElement.RemoveAll();
          foreach (KeyValuePair<string, string> keyValuePair in p_dicData)
          {
            XmlNode element = (XmlNode) xmlDocument.CreateElement(keyValuePair.Key);
            element.InnerText = keyValuePair.Value;
            documentElement.AppendChild(element);
          }
          reader.Close();
          xmlDocument.Save(fileInfo.FullName);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsBigData]SaveBigDataToXml):" + ex.Message));
      }
    }
  }
}
