﻿// Decompiled with JetBrains decompiler
// Type: HDMFSummary.clsSummary
// Assembly: HDMFSummary, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: ********-3B9F-45E9-9723-0DEF72C057B2
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFSummary.dll

using HDMoldFlowLibrary;
using System.Collections.Generic;
using System.Data;
using System.IO;

namespace HDMFSummary
{
  public class clsSummary
  {
    public static Dictionary<string, string> GetStudySummaryData(
      DataRow p_drStudy,
      FileInfo p_fiProject,
      bool p_isExistOutFile)
    {
      Dictionary<string, string> studySummaryData = new Dictionary<string, string>();
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      List<string> stringList = new List<string>();
      string str1 = p_drStudy["Name"].ToString();
      clsSummaryDefine.g_strSaveFileName = p_fiProject.DirectoryName + "\\" + str1;
      if (p_isExistOutFile)
      {
        string fileNameFromProject = clsHDMFLib.GetStudyFileNameFromProject(p_fiProject.FullName, str1);
        List<string> dataFromOutFiles = clsHDMFLibOutLog.GetLogDataFromOutFiles(p_fiProject.DirectoryName, fileNameFromProject);
        if (dataFromOutFiles.Count != 0)
          studySummaryData = clsSummaryData.GetStudySummaryDataFromLogData(p_drStudy, dataFromOutFiles);
      }
      else
      {
        string str2 = clsSummaryDefine.g_strSaveFileName + "_Summary.xml";
        if (File.Exists(str2))
        {
          studySummaryData = clsSummaryData.GetSummaryDataFromXml(str2);
        }
        else
        {
          List<string> p_lst_strALog = clsHDMFLib.ExportAnalysisLog(clsSummaryDefine.g_diTmpReport?.ToString() + "\\" + str1 + "\\Analysis.log");
          studySummaryData = clsSummaryData.GetSummaryDataFromMoldFlow(str1, p_lst_strALog);
        }
      }
      return studySummaryData;
    }

    public static void ExportSummary(DataRow[] p_arr_drStudy, string p_strLangType)
    {
      string p_strSummaryName = "Summary";
      if (p_arr_drStudy == null || p_arr_drStudy.Length == 0)
        return;
      if (p_strLangType != "KOR")
        p_strSummaryName = p_strSummaryName + "_" + p_strLangType;
      clsSummaryExcel.ExportSummary(p_arr_drStudy, p_strSummaryName);
    }
  }
}
