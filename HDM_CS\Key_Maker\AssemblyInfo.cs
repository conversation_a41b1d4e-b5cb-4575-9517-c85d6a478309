﻿using System.Reflection;
using System.Runtime.InteropServices;

[assembly: AssemblyT<PERSON><PERSON>("Key_Maker")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("Key_Maker")]
[assembly: AssemblyCopyright("Copyright ©  2017")]
[assembly: AssemblyTrademark("")]
[assembly: ComVisible(false)]
[assembly: Guid("6be1efdc-d980-45fe-9495-9688cfe6052d")]
[assembly: AssemblyFileVersion("*******")]
[assembly: AssemblyVersion("*******")]
