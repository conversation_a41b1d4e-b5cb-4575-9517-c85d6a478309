﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmPinGate
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDMFUserControl;
using HDMoldFlow.Properties;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public class frmPinGate : Form
  {
    private const int WS_EX_COMPOSITED = 33554432;
    public string m_strTotalLength = "0";
    private IContainer components = (IContainer) null;
    private Label label_Pin;
    private ToolTip toolTip_Hor_Angle;
    private PictureBox pictureBox_Pin;
    private NewTextBox newTextBox_PinLength4;
    private NewTextBox newTextBox_PinLength3;
    private NewTextBox newTextBox_PinLength2;
    private NewTextBox newTextBox_PinLength1;
    private NewTextBox newTextBox_PinDiameter;
    private NewButton newButton_Apply;
    private Panel panel_Pin;

    protected override CreateParams CreateParams
    {
      get
      {
        CreateParams createParams = base.CreateParams;
        createParams.ExStyle |= 33554432;
        return createParams;
      }
    }

    public string m_strPinLength1 { get; set; }

    public string m_strPinLength2 { get; set; }

    public string m_strPinLength3 { get; set; }

    public string m_strPinLength4 { get; set; }

    public string m_strPinDiameter { get; set; }

    public frmPinGate()
    {
      this.InitializeComponent();
      this.newButton_Apply.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Apply.Image);
      this.Text = LocaleControl.getInstance().GetString("IDS_PINGATE");
      this.label_Pin.Text = LocaleControl.getInstance().GetString("IDS_PINGATE");
      this.newButton_Apply.ButtonText = LocaleControl.getInstance().GetString("IDS_APPLY");
    }

    private void frmRunner_Load(object sender, EventArgs e)
    {
      this.newTextBox_PinLength1.Value = this.m_strPinLength1;
      this.newTextBox_PinLength2.Value = this.m_strPinLength2;
      this.newTextBox_PinLength3.Value = this.m_strPinLength3;
      this.newTextBox_PinLength4.Value = this.m_strPinLength4;
      this.newTextBox_PinDiameter.Value = this.m_strPinDiameter;
    }

    private void newButton_Apply_NewClick(object sender, EventArgs e)
    {
      this.m_strPinLength1 = this.newTextBox_PinLength1.Value;
      this.m_strPinLength2 = this.newTextBox_PinLength2.Value;
      this.m_strPinLength3 = this.newTextBox_PinLength3.Value;
      this.m_strPinLength4 = this.newTextBox_PinLength4.Value;
      this.m_strPinDiameter = this.newTextBox_PinDiameter.Value;
      this.m_strTotalLength = (clsUtill.ConvertToDouble(this.newTextBox_PinLength1.Value) + clsUtill.ConvertToDouble(this.newTextBox_PinLength2.Value) + clsUtill.ConvertToDouble(this.newTextBox_PinLength3.Value) + clsUtill.ConvertToDouble(this.newTextBox_PinLength4.Value)).ToString();
      this.DialogResult = DialogResult.OK;
      this.Close();
    }

    private void frmPinGate_KeyDown(object sender, KeyEventArgs e)
    {
      if (e.KeyCode != Keys.Escape)
        return;
      this.Close();
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.components = (IContainer) new System.ComponentModel.Container();
      ComponentResourceManager componentResourceManager = new ComponentResourceManager(typeof (frmPinGate));
      this.label_Pin = new Label();
      this.toolTip_Hor_Angle = new ToolTip(this.components);
      this.newTextBox_PinDiameter = new NewTextBox();
      this.newTextBox_PinLength1 = new NewTextBox();
      this.newTextBox_PinLength2 = new NewTextBox();
      this.newTextBox_PinLength3 = new NewTextBox();
      this.newTextBox_PinLength4 = new NewTextBox();
      this.panel_Pin = new Panel();
      this.pictureBox_Pin = new PictureBox();
      this.newButton_Apply = new NewButton();
      this.panel_Pin.SuspendLayout();
      ((ISupportInitialize) this.pictureBox_Pin).BeginInit();
      this.SuspendLayout();
      this.label_Pin.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Pin.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Pin.ForeColor = Color.MidnightBlue;
      this.label_Pin.Location = new Point(7, 9);
      this.label_Pin.Name = "label_Pin";
      this.label_Pin.Size = new Size(285, 20);
      this.label_Pin.TabIndex = 21;
      this.label_Pin.Text = "[핀 게이트]";
      this.label_Pin.TextAlign = ContentAlignment.MiddleCenter;
      this.toolTip_Hor_Angle.ShowAlways = true;
      this.newTextBox_PinDiameter.BackColor = SystemColors.Window;
      this.newTextBox_PinDiameter.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_PinDiameter.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_PinDiameter.IsDigit = true;
      this.newTextBox_PinDiameter.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_PinDiameter.Location = new Point(64, 113);
      this.newTextBox_PinDiameter.MultiLine = false;
      this.newTextBox_PinDiameter.Name = "newTextBox_PinDiameter";
      this.newTextBox_PinDiameter.ReadOnly = false;
      this.newTextBox_PinDiameter.Size = new Size(62, 23);
      this.newTextBox_PinDiameter.TabIndex = 5;
      this.newTextBox_PinDiameter.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_PinDiameter.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_PinDiameter.TextForeColor = SystemColors.WindowText;
      this.newTextBox_PinDiameter.Value = "0";
      this.newTextBox_PinLength1.BackColor = SystemColors.Window;
      this.newTextBox_PinLength1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_PinLength1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_PinLength1.IsDigit = true;
      this.newTextBox_PinLength1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_PinLength1.Location = new Point(203, 164);
      this.newTextBox_PinLength1.MultiLine = false;
      this.newTextBox_PinLength1.Name = "newTextBox_PinLength1";
      this.newTextBox_PinLength1.ReadOnly = false;
      this.newTextBox_PinLength1.Size = new Size(62, 23);
      this.newTextBox_PinLength1.TabIndex = 4;
      this.newTextBox_PinLength1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_PinLength1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_PinLength1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_PinLength1.Value = "0";
      this.newTextBox_PinLength2.BackColor = SystemColors.Window;
      this.newTextBox_PinLength2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_PinLength2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_PinLength2.IsDigit = true;
      this.newTextBox_PinLength2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_PinLength2.Location = new Point(203, 126);
      this.newTextBox_PinLength2.MultiLine = false;
      this.newTextBox_PinLength2.Name = "newTextBox_PinLength2";
      this.newTextBox_PinLength2.ReadOnly = false;
      this.newTextBox_PinLength2.Size = new Size(62, 23);
      this.newTextBox_PinLength2.TabIndex = 3;
      this.newTextBox_PinLength2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_PinLength2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_PinLength2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_PinLength2.Value = "0";
      this.newTextBox_PinLength3.BackColor = SystemColors.Window;
      this.newTextBox_PinLength3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_PinLength3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_PinLength3.IsDigit = true;
      this.newTextBox_PinLength3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_PinLength3.Location = new Point(203, 88);
      this.newTextBox_PinLength3.MultiLine = false;
      this.newTextBox_PinLength3.Name = "newTextBox_PinLength3";
      this.newTextBox_PinLength3.ReadOnly = false;
      this.newTextBox_PinLength3.Size = new Size(62, 23);
      this.newTextBox_PinLength3.TabIndex = 2;
      this.newTextBox_PinLength3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_PinLength3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_PinLength3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_PinLength3.Value = "0";
      this.newTextBox_PinLength4.BackColor = SystemColors.Window;
      this.newTextBox_PinLength4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_PinLength4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_PinLength4.IsDigit = true;
      this.newTextBox_PinLength4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_PinLength4.Location = new Point(203, 38);
      this.newTextBox_PinLength4.MultiLine = false;
      this.newTextBox_PinLength4.Name = "newTextBox_PinLength4";
      this.newTextBox_PinLength4.ReadOnly = false;
      this.newTextBox_PinLength4.Size = new Size(62, 23);
      this.newTextBox_PinLength4.TabIndex = 1;
      this.newTextBox_PinLength4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_PinLength4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_PinLength4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_PinLength4.Value = "0";
      this.panel_Pin.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Pin.Controls.Add((Control) this.newTextBox_PinDiameter);
      this.panel_Pin.Controls.Add((Control) this.newTextBox_PinLength4);
      this.panel_Pin.Controls.Add((Control) this.newTextBox_PinLength3);
      this.panel_Pin.Controls.Add((Control) this.newTextBox_PinLength1);
      this.panel_Pin.Controls.Add((Control) this.newTextBox_PinLength2);
      this.panel_Pin.Controls.Add((Control) this.pictureBox_Pin);
      this.panel_Pin.Location = new Point(7, 27);
      this.panel_Pin.Name = "panel_Pin";
      this.panel_Pin.Size = new Size(285, 226);
      this.panel_Pin.TabIndex = 114;
      this.pictureBox_Pin.BackgroundImage = (Image) componentResourceManager.GetObject("pictureBox_Pin.BackgroundImage");
      this.pictureBox_Pin.BackgroundImageLayout = ImageLayout.Stretch;
      this.pictureBox_Pin.Dock = DockStyle.Fill;
      this.pictureBox_Pin.Location = new Point(0, 0);
      this.pictureBox_Pin.Name = "pictureBox_Pin";
      this.pictureBox_Pin.Size = new Size(283, 224);
      this.pictureBox_Pin.TabIndex = 100;
      this.pictureBox_Pin.TabStop = false;
      this.newButton_Apply.Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.newButton_Apply.ButtonBackColor = Color.White;
      this.newButton_Apply.ButtonText = "적용";
      this.newButton_Apply.FlatBorderSize = 1;
      this.newButton_Apply.FlatStyle = FlatStyle.Flat;
      this.newButton_Apply.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Apply.Image = (Image) Resources.OK;
      this.newButton_Apply.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Apply.Location = new Point(8, 256);
      this.newButton_Apply.Name = "newButton_Apply";
      this.newButton_Apply.Size = new Size(284, 23);
      this.newButton_Apply.TabIndex = 6;
      this.newButton_Apply.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Apply.NewClick += new EventHandler(this.newButton_Apply_NewClick);
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(299, 282);
      this.Controls.Add((Control) this.label_Pin);
      this.Controls.Add((Control) this.panel_Pin);
      this.Controls.Add((Control) this.newButton_Apply);
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmPinGate);
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = nameof (frmPinGate);
      this.Load += new EventHandler(this.frmRunner_Load);
      this.KeyDown += new KeyEventHandler(this.frmPinGate_KeyDown);
      this.panel_Pin.ResumeLayout(false);
      ((ISupportInitialize) this.pictureBox_Pin).EndInit();
      this.ResumeLayout(false);
    }
  }
}
