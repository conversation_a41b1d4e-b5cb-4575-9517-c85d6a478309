<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="x14ac xr xr2 xr3" xmlns:x14ac="http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac" xmlns:xr="http://schemas.microsoft.com/office/spreadsheetml/2014/revision" xmlns:xr2="http://schemas.microsoft.com/office/spreadsheetml/2015/revision2" xmlns:xr3="http://schemas.microsoft.com/office/spreadsheetml/2016/revision3" xr:uid="{00000000-0001-0000-0500-000000000000}"><dimension ref="B3:Q31"/><sheetViews><sheetView showGridLines="0" zoomScale="55" zoomScaleNormal="55" workbookViewId="0"><selection activeCell="V15" sqref="V15"/></sheetView></sheetViews><sheetFormatPr defaultColWidth="11.42578125" defaultRowHeight="15" x14ac:dyDescent="0.25"/><cols><col min="1" max="1" width="3.28515625" customWidth="1"/><col min="12" max="12" width="3.7109375" customWidth="1"/><col min="13" max="13" width="18.28515625" bestFit="1" customWidth="1"/><col min="14" max="17" width="11.42578125" style="3"/></cols><sheetData><row r="3" spans="2:17" x14ac:dyDescent="0.25"><c r="B3" s="30"/><c r="C3" s="30"/><c r="D3" s="30"/><c r="E3" s="30"/><c r="F3" s="30"/><c r="G3" s="30"/><c r="H3" s="30"/><c r="I3" s="30"/><c r="J3" s="30"/><c r="K3" s="30"/><c r="L3" s="30"/><c r="M3" s="30"/><c r="N3" s="11"/><c r="O3" s="11"/><c r="P3" s="11"/><c r="Q3" s="11"/></row><row r="4" spans="2:17" x14ac:dyDescent="0.25"><c r="B4" s="30"/><c r="C4" s="30"/><c r="D4" s="30"/><c r="E4" s="30"/><c r="F4" s="30"/><c r="G4" s="30"/><c r="H4" s="30"/><c r="I4" s="30"/><c r="J4" s="30"/><c r="K4" s="30"/><c r="L4" s="30"/><c r="M4" s="30" t="s"><v>60</v></c><c r="N4" s="11"/><c r="O4" s="11"/><c r="P4" s="11"/><c r="Q4" s="11"/></row><row r="5" spans="2:17" x14ac:dyDescent="0.25"><c r="B5" s="30"/><c r="C5" s="30"/><c r="D5" s="30"/><c r="E5" s="30"/><c r="F5" s="30"/><c r="G5" s="30"/><c r="H5" s="30"/><c r="I5" s="30"/><c r="J5" s="30"/><c r="K5" s="30"/><c r="L5" s="30"/><c r="M5" s="30"/><c r="N5" s="71" t="s"><v>52</v></c><c r="O5" s="71" t="s"><v>53</v></c><c r="P5" s="180" t="s"><v>59</v></c><c r="Q5" s="180"/></row><row r="6" spans="2:17" x14ac:dyDescent="0.25"><c r="B6" s="30"/><c r="C6" s="30"/><c r="D6" s="30"/><c r="E6" s="30"/><c r="F6" s="30"/><c r="G6" s="30"/><c r="H6" s="30"/><c r="I6" s="30"/><c r="J6" s="30"/><c r="K6" s="30"/><c r="L6" s="30"/><c r="M6" s="72" t="s"><v>54</v></c><c r="N6" s="73"><f>'Cuadro resumen'!E15</f><v>0</v></c><c r="O6" s="73"><f>N6</f><v>0</v></c><c r="P6" s="73"><v>0</v></c><c r="Q6" s="73"><f>P6+N6</f><v>0</v></c></row><row r="7" spans="2:17" x14ac:dyDescent="0.25"><c r="B7" s="30"/><c r="C7" s="30"/><c r="D7" s="30"/><c r="E7" s="30"/><c r="F7" s="30"/><c r="G7" s="30"/><c r="H7" s="30"/><c r="I7" s="30"/><c r="J7" s="30"/><c r="K7" s="30"/><c r="L7" s="30"/><c r="M7" s="72" t="s"><v>55</v></c><c r="N7" s="73"><f>'Cuadro resumen'!E16</f><v>0</v></c><c r="O7" s="73"><f>O6+N7</f><v>0</v></c><c r="P7" s="73"><f>Q6</f><v>0</v></c><c r="Q7" s="73"><f>N7+P7</f><v>0</v></c></row><row r="8" spans="2:17" x14ac:dyDescent="0.25"><c r="B8" s="30"/><c r="C8" s="30"/><c r="D8" s="30"/><c r="E8" s="30"/><c r="F8" s="30"/><c r="G8" s="30"/><c r="H8" s="30"/><c r="I8" s="30"/><c r="J8" s="30"/><c r="K8" s="30"/><c r="L8" s="30"/><c r="M8" s="72" t="s"><v>56</v></c><c r="N8" s="73"><f>'Cuadro resumen'!E22</f><v>0</v></c><c r="O8" s="73"><f>O7+N8</f><v>0</v></c><c r="P8" s="73"><f>Q7</f><v>0</v></c><c r="Q8" s="73"><f>N8+P8</f><v>0</v></c></row><row r="9" spans="2:17" x14ac:dyDescent="0.25"><c r="B9" s="30"/><c r="C9" s="30"/><c r="D9" s="30"/><c r="E9" s="30"/><c r="F9" s="30"/><c r="G9" s="30"/><c r="H9" s="30"/><c r="I9" s="30"/><c r="J9" s="30"/><c r="K9" s="30"/><c r="L9" s="30"/><c r="M9" s="72" t="s"><v>58</v></c><c r="N9" s="73"><f>'Cuadro parámetros caso 1'!G26</f><v>0</v></c><c r="O9" s="73"><f>O8+N9</f><v>0</v></c><c r="P9" s="73"><f>Q8</f><v>0</v></c><c r="Q9" s="73"><f>N9+P9</f><v>0</v></c></row><row r="10" spans="2:17" x14ac:dyDescent="0.25"><c r="B10" s="30"/><c r="C10" s="30"/><c r="D10" s="30"/><c r="E10" s="30"/><c r="F10" s="30"/><c r="G10" s="30"/><c r="H10" s="30"/><c r="I10" s="30"/><c r="J10" s="30"/><c r="K10" s="30"/><c r="L10" s="30"/><c r="M10" s="72" t="s"><v>57</v></c><c r="N10" s="73"><f>'Cuadro parámetros caso 1'!F26</f><v>0</v></c><c r="O10" s="73"><f>O9+N10</f><v>0</v></c><c r="P10" s="73"><f>Q9</f><v>0</v></c><c r="Q10" s="73"><f>P10+N10</f><v>0</v></c></row><row r="11" spans="2:17" x14ac:dyDescent="0.25"><c r="B11" s="30"/><c r="C11" s="30"/><c r="D11" s="30"/><c r="E11" s="30"/><c r="F11" s="30"/><c r="G11" s="30"/><c r="H11" s="30"/><c r="I11" s="30"/><c r="J11" s="30"/><c r="K11" s="30"/><c r="L11" s="30"/><c r="M11" s="72" t="s"><v>51</v></c><c r="N11" s="73"><f>'Cuadro parámetros caso 1'!E26</f><v>13</v></c><c r="O11" s="73"><f>O10+N11</f><v>13</v></c><c r="P11" s="73"><f>Q10</f><v>0</v></c><c r="Q11" s="73"><f>P11+N11</f><v>13</v></c></row><row r="12" spans="2:17" x14ac:dyDescent="0.25"><c r="B12" s="30"/><c r="C12" s="30"/><c r="D12" s="30"/><c r="E12" s="30"/><c r="F12" s="30"/><c r="G12" s="30"/><c r="H12" s="30"/><c r="I12" s="30"/><c r="J12" s="30"/><c r="K12" s="30"/><c r="L12" s="30"/><c r="M12" s="30"/><c r="N12" s="74"><f>SUM(N6:N11)</f><v>13</v></c><c r="O12" s="11"/><c r="P12" s="11"/><c r="Q12" s="11"/></row><row r="13" spans="2:17" x14ac:dyDescent="0.25"><c r="B13" s="30"/><c r="C13" s="30"/><c r="D13" s="30"/><c r="E13" s="30"/><c r="F13" s="30"/><c r="G13" s="30"/><c r="H13" s="30"/><c r="I13" s="30"/><c r="J13" s="30"/><c r="K13" s="30"/><c r="L13" s="30"/><c r="M13" s="30"/><c r="N13" s="11"/><c r="O13" s="11"/><c r="P13" s="11"/><c r="Q13" s="11"/></row><row r="14" spans="2:17" x14ac:dyDescent="0.25"><c r="B14" s="30"/><c r="C14" s="30"/><c r="D14" s="30"/><c r="E14" s="30"/><c r="F14" s="30"/><c r="G14" s="30"/><c r="H14" s="30"/><c r="I14" s="30"/><c r="J14" s="30"/><c r="K14" s="30"/><c r="L14" s="30"/><c r="M14" s="30"/><c r="N14" s="11"/><c r="O14" s="11"/><c r="P14" s="11"/><c r="Q14" s="11"/></row><row r="15" spans="2:17" x14ac:dyDescent="0.25"><c r="B15" s="30"/><c r="C15" s="30"/><c r="D15" s="30"/><c r="E15" s="30"/><c r="F15" s="30"/><c r="G15" s="30"/><c r="H15" s="30"/><c r="I15" s="30"/><c r="J15" s="30"/><c r="K15" s="30"/><c r="L15" s="30"/><c r="M15" s="30"/><c r="N15" s="11"/><c r="O15" s="11"/><c r="P15" s="11"/><c r="Q15" s="11"/></row><row r="16" spans="2:17" x14ac:dyDescent="0.25"><c r="B16" s="30"/><c r="C16" s="30"/><c r="D16" s="30"/><c r="E16" s="30"/><c r="F16" s="30"/><c r="G16" s="30"/><c r="H16" s="30"/><c r="I16" s="30"/><c r="J16" s="30"/><c r="K16" s="30"/><c r="L16" s="30"/><c r="M16" s="30"/><c r="N16" s="11"/><c r="O16" s="11"/><c r="P16" s="11"/><c r="Q16" s="11"/></row><row r="17" spans="2:17" x14ac:dyDescent="0.25"><c r="B17" s="30"/><c r="C17" s="30"/><c r="D17" s="30"/><c r="E17" s="30"/><c r="F17" s="30"/><c r="G17" s="30"/><c r="H17" s="30"/><c r="I17" s="30"/><c r="J17" s="30"/><c r="K17" s="30"/><c r="L17" s="30"/><c r="M17" s="30"/><c r="N17" s="11"/><c r="O17" s="11"/><c r="P17" s="11"/><c r="Q17" s="11"/></row><row r="18" spans="2:17" x14ac:dyDescent="0.25"><c r="B18" s="30"/><c r="C18" s="30"/><c r="D18" s="30"/><c r="E18" s="30"/><c r="F18" s="30"/><c r="G18" s="30"/><c r="H18" s="30"/><c r="I18" s="30"/><c r="J18" s="30"/><c r="K18" s="30"/><c r="L18" s="30"/><c r="M18" s="30"/><c r="N18" s="11"/><c r="O18" s="11"/><c r="P18" s="11"/><c r="Q18" s="11"/></row><row r="19" spans="2:17" x14ac:dyDescent="0.25"><c r="B19" s="30"/><c r="C19" s="30"/><c r="D19" s="30"/><c r="E19" s="30"/><c r="F19" s="30"/><c r="G19" s="30"/><c r="H19" s="30"/><c r="I19" s="30"/><c r="J19" s="30"/><c r="K19" s="30"/><c r="L19" s="30"/><c r="M19" s="30"/><c r="N19" s="11"/><c r="O19" s="11"/><c r="P19" s="11"/><c r="Q19" s="11"/></row><row r="20" spans="2:17" x14ac:dyDescent="0.25"><c r="B20" s="30"/><c r="C20" s="30"/><c r="D20" s="30"/><c r="E20" s="30"/><c r="F20" s="30"/><c r="G20" s="30"/><c r="H20" s="30"/><c r="I20" s="30"/><c r="J20" s="30"/><c r="K20" s="30"/><c r="L20" s="30"/><c r="M20" s="30"/><c r="N20" s="11"/><c r="O20" s="11"/><c r="P20" s="11"/><c r="Q20" s="11"/></row><row r="21" spans="2:17" x14ac:dyDescent="0.25"><c r="B21" s="30"/><c r="C21" s="30"/><c r="D21" s="30"/><c r="E21" s="30"/><c r="F21" s="30"/><c r="G21" s="30"/><c r="H21" s="30"/><c r="I21" s="30"/><c r="J21" s="30"/><c r="K21" s="30"/><c r="L21" s="30"/><c r="M21" s="30"/><c r="N21" s="11"/><c r="O21" s="11"/><c r="P21" s="11"/><c r="Q21" s="11"/></row><row r="22" spans="2:17" x14ac:dyDescent="0.25"><c r="B22" s="30"/><c r="C22" s="30"/><c r="D22" s="30"/><c r="E22" s="30"/><c r="F22" s="30"/><c r="G22" s="30"/><c r="H22" s="30"/><c r="I22" s="30"/><c r="J22" s="30"/><c r="K22" s="30"/><c r="L22" s="30"/><c r="M22" s="30"/><c r="N22" s="11"/><c r="O22" s="11"/><c r="P22" s="11"/><c r="Q22" s="11"/></row><row r="23" spans="2:17" x14ac:dyDescent="0.25"><c r="B23" s="30"/><c r="C23" s="30"/><c r="D23" s="30"/><c r="E23" s="30"/><c r="F23" s="30"/><c r="G23" s="30"/><c r="H23" s="30"/><c r="I23" s="30"/><c r="J23" s="30"/><c r="K23" s="30"/><c r="L23" s="30"/><c r="M23" s="30"/><c r="N23" s="11"/><c r="O23" s="11"/><c r="P23" s="11"/><c r="Q23" s="11"/></row><row r="24" spans="2:17" x14ac:dyDescent="0.25"><c r="B24" s="30"/><c r="C24" s="30"/><c r="D24" s="30"/><c r="E24" s="30"/><c r="F24" s="30"/><c r="G24" s="30"/><c r="H24" s="30"/><c r="I24" s="30"/><c r="J24" s="30"/><c r="K24" s="30"/><c r="L24" s="30"/><c r="M24" s="30"/><c r="N24" s="11"/><c r="O24" s="11"/><c r="P24" s="11"/><c r="Q24" s="11"/></row><row r="25" spans="2:17" x14ac:dyDescent="0.25"><c r="B25" s="30"/><c r="C25" s="30"/><c r="D25" s="30"/><c r="E25" s="30"/><c r="F25" s="30"/><c r="G25" s="30"/><c r="H25" s="30"/><c r="I25" s="30"/><c r="J25" s="30"/><c r="K25" s="30"/><c r="L25" s="30"/><c r="M25" s="30"/><c r="N25" s="11"/><c r="O25" s="11"/><c r="P25" s="11"/><c r="Q25" s="11"/></row><row r="26" spans="2:17" x14ac:dyDescent="0.25"><c r="B26" s="30"/><c r="C26" s="30"/><c r="D26" s="30"/><c r="E26" s="30"/><c r="F26" s="30"/><c r="G26" s="30"/><c r="H26" s="30"/><c r="I26" s="30"/><c r="J26" s="30"/><c r="K26" s="30"/><c r="L26" s="30"/><c r="M26" s="30"/><c r="N26" s="11"/><c r="O26" s="11"/><c r="P26" s="11"/><c r="Q26" s="11"/></row><row r="27" spans="2:17" ht="18.75" customHeight="1" x14ac:dyDescent="0.25"><c r="B27" s="181" t="s"><v>85</v></c><c r="C27" s="181"/><c r="D27" s="181"/><c r="E27" s="181"/><c r="F27" s="181"/><c r="G27" s="30"/><c r="H27" s="182" t="s"><v>86</v></c><c r="I27" s="182"/><c r="J27" s="182"/><c r="K27" s="182"/><c r="L27" s="30"/><c r="M27" s="30"/><c r="N27" s="11"/><c r="O27" s="11"/><c r="P27" s="11"/><c r="Q27" s="11"/></row><row r="28" spans="2:17" ht="15.75" x14ac:dyDescent="0.25"><c r="B28" s="181"/><c r="C28" s="181"/><c r="D28" s="181"/><c r="E28" s="181"/><c r="F28" s="181"/><c r="G28" s="75"/><c r="H28" s="182"/><c r="I28" s="182"/><c r="J28" s="182"/><c r="K28" s="182"/><c r="L28" s="30"/><c r="M28" s="30"/><c r="N28" s="11"/><c r="O28" s="11"/><c r="P28" s="11"/><c r="Q28" s="11"/></row><row r="29" spans="2:17" ht="15.75" x14ac:dyDescent="0.25"><c r="B29" s="181"/><c r="C29" s="181"/><c r="D29" s="181"/><c r="E29" s="181"/><c r="F29" s="181"/><c r="G29" s="75"/><c r="H29" s="182"/><c r="I29" s="182"/><c r="J29" s="182"/><c r="K29" s="182"/><c r="L29" s="30"/><c r="M29" s="30"/><c r="N29" s="11"/><c r="O29" s="11"/><c r="P29" s="11"/><c r="Q29" s="11"/></row><row r="30" spans="2:17" ht="15.75" x14ac:dyDescent="0.25"><c r="B30" s="181"/><c r="C30" s="181"/><c r="D30" s="181"/><c r="E30" s="181"/><c r="F30" s="181"/><c r="G30" s="75"/><c r="H30" s="182"/><c r="I30" s="182"/><c r="J30" s="182"/><c r="K30" s="182"/><c r="L30" s="30"/><c r="M30" s="30"/><c r="N30" s="11"/><c r="O30" s="11"/><c r="P30" s="11"/><c r="Q30" s="11"/></row><row r="31" spans="2:17" x14ac:dyDescent="0.25"><c r="B31" s="30"/><c r="C31" s="30"/><c r="D31" s="30"/><c r="E31" s="30"/><c r="F31" s="30"/><c r="G31" s="30"/><c r="H31" s="30"/><c r="I31" s="30"/><c r="J31" s="30"/><c r="K31" s="30"/><c r="L31" s="30"/><c r="M31" s="30"/><c r="N31" s="11"/><c r="O31" s="11"/><c r="P31" s="11"/><c r="Q31" s="11"/></row></sheetData><mergeCells count="3"><mergeCell ref="P5:Q5"/><mergeCell ref="B27:F30"/><mergeCell ref="H27:K30"/></mergeCells><pageMargins left="0.7" right="0.7" top="0.75" bottom="0.75" header="0.3" footer="0.3"/><pageSetup paperSize="9" orientation="portrait" r:id="rId1"/><drawing r:id="rId2"/></worksheet>