﻿using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

[assembly: Extension]
[assembly: AssemblyTitle("HDMFAI")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("HDMFAI")]
[assembly: AssemblyCopyright("Copyright ©  2023")]
[assembly: AssemblyTrademark("")]
[assembly: ComVisible(false)]
[assembly: Guid("47a3fdf1-7abe-419d-8e5c-c7588a1ffa4d")]
[assembly: AssemblyFileVersion("*******")]
[assembly: AssemblyVersion("*******")]
