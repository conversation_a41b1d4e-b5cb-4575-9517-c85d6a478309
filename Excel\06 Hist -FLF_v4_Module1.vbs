Attribute VB_Name = "Module1"







Sub AddFLFDD()



    Dim FInfo As String

    Dim FilterIndex As Integer

    Dim Title As String

    Dim FileName As Variant

    Dim DotNo As Integer

    Dim SlashNo As Integer

    Dim StrLength As Integer

    Dim StudyName As String

    Dim StartRow As Variant

    Dim RanNum As Integer

    Dim Heading1 As String

    Dim Heading2 As String

    

    

   

    Set Synergy = CreateObject("Synergy.Synergy")

    Set StudyDoc = Synergy.StudyDoc

    Set Viewer = Synergy.Viewer

    

    ' Check if a Synergy is open

    If Synergy Is Nothing Then

        MsgBoxPrompt = "Synergy is not open." & vbCrLf & vbCrLf

        MsgBoxPrompt = MsgBoxPrompt + "Open a study, create and/or display an XY Plot and re-execute this script."

        MsgBoxButton = vbOKOnly + vbCritical + vbMsgBoxSetForeground

        MsgBoxTitle = "The script is closing"

        MsgBox MsgBoxPrompt, MsgBoxButton, MsgBoxTitle

        WScript.Quit

    End If

    

    'Check if a study is open

    If Viewer Is Nothing Then

        MsgBoxPrompt = "A study is not open." & vbCrLf & vbCrLf

        MsgBoxPrompt = MsgBoxPrompt + "Open a study, create and/or display an XY Plot and re-execute this script."

        MsgBoxButton = vbOKOnly + vbCritical + vbMsgBoxSetForeground

        MsgBoxTitle = "The script is closing"

        MsgBox MsgBoxPrompt, MsgBoxButton, MsgBoxTitle

        WScript.Quit

    End If

    

    Set PlotMgr = Synergy.PlotManager()

    

    

    StudyName = StudyDoc.StudyName

    

    

    

    

      'This sub section gets the Vol Shr data for all elements and puts it into an array for processing

    

        'Code determines the plot ID for the Volumetric shrinkage at ejection result

      Set lIdsVS = Synergy.CreateIntegerArray()

      countVS = PlotMgr.FindDataSetIDsByName("Volumetric shrinkage at ejection", lIdsVS)

'      dsIDVS = lIdsVS.Val(0)

      dsIDVS = 1230

    

        'Code populates an array "IndpValuesVS" with the data from the result

      Set IndpValuesVS = Synergy.CreateDoubleArray()

      count2VS = PlotMgr.GetIndpVarCount(dsIDVS)

      PlotMgr.GetIndpValues dsIDVS, IndpValuesVS        'actual command that populates the array

    

        'Code populates the array with the results for every element

      Set elementIDVS = Synergy.CreateIntegerArray()

      Set ValueVS = Synergy.CreateDoubleArray()

      PlotMgr.GetScalarData dsIDVS, IndpValuesVS, elementIDVS, ValueVS

    

        'Converts the array to a vbs array for faster processing

      EleIDVS = elementIDVS.ToVBSArray()

      ValVS = ValueVS.ToVBSArray()

    

      

      Set fso = CreateObject("Scripting.FileSystemObject")

    UpPath = fso.GetAbsolutePathName(".")

    

    ResultName = StudyName & "_" & ResultName

    ResultName = Replace(ResultName, "/", "_")

    ResultName = Replace(ResultName, "\", "_")

    ResultName = Replace(ResultName, ",", "_")

    ResultName = Replace(ResultName, ":", "_")

    

    

    PathName = "C:\Autodesk\ActiveStudyFLF.txt"

    

    'msgbox PathName

    Set f = fso.OpenTextFile(PathName, 2, True)

    

    HeadingString = ResultName

    HeadingString = HeadingString + "FLF" & vbCrLf & vbCrLf & vbCrLf

    

    f.write HeadingString

    

    For i = 0 To UBound(ValVS)

        f.write ValVS(i) & vbCrLf

    Next

    

'    Set App = WScript.CreateObject("WScript.Shell")

'    App.Run "notepad.exe " & PathName

    'msgbox PathName

    'App.Run "wordpad.exe " & PathName

    '    MsgBox Synergy.GetUnits



    Set StudyDoc = Synergy.StudyDoc()

    StudyNameFull = StudyDoc.StudyName



   

    ErrorBoolean = False

    

        CellFillTest = Range("B7").Value

    

    If CellFillTest = "" Then

    

        ErrorBoolean = False

    Else

    

        ErrorBoolean = True

        MsgBox "Delete the raw data in Column B before clicking the button again"

        

    End If

    

    Dim MySheetName2

    

    If ErrorBoolean = False Then

    



        'The code below gets the file name from the entire path

        DotNo = InStrRev(StudyNameFull, ".") - 1

        SlashNo = InStrRev(StudyNameFull, "\")

        StrLength = DotNo - SlashNo

        StudyName = Mid(StudyNameFull, SlashNo + 1, StrLength)

        StudyName = Left(StudyName, 10)

        StudyNameFull = Left(StudyNameFull, 5)

        MySheetName2 = Left(StudyNameFull, 4)

      

        ' The code below sets the Heading1 text

        Heading1 = "            Time [s]"

       

        ' The code below determines the heading2 text

'        If OptUseFile.Value = True Then

'            Heading2 = StudyName

'        Else

'            Heading2 = TextBoxHeading2.Value

'        End If

          

        Heading2 = StudyName

      'The code below sets the headings in the spreadsheet

            'and moves the active location to the start of data inport

        

        Range("B7").Activate

     

    

'        StartRow = TextBoxStartRow.Text

'

        RanNum = Int((100 * Rnd) + 1) ' This is used in the import data name to ensure a unique name



        TextFile = "C:\Autodesk\ActiveStudyFLF.txt"

            

        'The With sequence creates the imported text area

            

         With ActiveSheet.QueryTables.Add(Connection:="TEXT;" & TextFile, Destination:=ActiveCell)

                .Name = StudyName & RanNum

                .FieldNames = True

                .RowNumbers = False

                .FillAdjacentFormulas = False

                .PreserveFormatting = True

                .RefreshOnFileOpen = False

                .RefreshStyle = xlInsertDeleteCells

                .SavePassword = False

                .SaveData = True

                .AdjustColumnWidth = True

                .RefreshPeriod = 0

                .TextFilePromptOnRefresh = False

                .TextFilePlatform = 437

                .TextFileStartRow = 1

                .TextFileParseType = xlDelimited

                .TextFileTextQualifier = xlTextQualifierDoubleQuote

                .TextFileConsecutiveDelimiter = False

                .TextFileTabDelimiter = True

                .TextFileSemicolonDelimiter = False

                .TextFileCommaDelimiter = False

                .TextFileSpaceDelimiter = False

                .TextFileColumnDataTypes = Array(1, 1)

                .TextFileTrailingMinusNumbers = True

                .Refresh BackgroundQuery:=False

                

            End With

                

            Selection.CurrentRegion.NumberFormat = "#,##0.000"

            Selection.CurrentRegion.Font.Bold = False

            



            Range("G9").Value = StudyNameFull

            

            Columns("B:B").Select

            Selection.ColumnWidth = 10

            

            Dim ws2 As Worksheet



            Set ws2 = ActiveSheet

            ActiveSheet.Name = MySheetName2



'            Range("G9").Select

            

            

            

    End If



    









End Sub







Sub AddFLF3D()



    Set Synergy = CreateObject("Synergy.Synergy")

    Set StudyDoc = Synergy.StudyDoc

    Set Viewer = Synergy.Viewer

    StudyName = StudyDoc.StudyName

    

    

    

    'msgbox "dsIDVS = " & dsIDVS

    dsIDVS = 1233

    

    

'    Dim i, j, PlotMgr, IndpValues, NumTSteps, vbTS, NIDIndp, NID, NIDResult, NDTotal, TS()

    Set PlotMgr = Synergy.PlotManager()

    Set IndpValues = Synergy.CreateDoubleArray()

    'PlotMgr.GetIndpValues ResultID, IndpValues

    PlotMgr.GetIndpValues dsIDVS, IndpValues

    NumTSteps = IndpValues.Size() - 1   ' Total number of Time Steps

    vbTS = IndpValues.ToVBSArray()

    

    'MsgBox "NumTSteps = " & NumTSteps

    

    ReDim TS(NumTSteps + 1)

    For i = 0 To NumTSteps

        TS(i) = vbTS(i)

    Next

    

    

    Dim Jays

    For i = 0 To UBound(TS)

        Jays = Jays + TS(i) & vbCrLf

    Next

    

    'msgbox Jays

    

    Dim Indp, IndpValues1, PResult, EntityIndex, vbPResult, vbPEntityIndex

    ResultID = dsIDVS   ' 1629 is the Average Volumetric Shrinkage Nodal Data

        Set Indp = Synergy.CreateDoubleArray()

        PlotMgr.GetIndpValues ResultID, Indp

        Set IndpValues1 = Synergy.CreateDoubleArray()

        IndpValues1.AddDouble (Indp.Val(NumTSteps))

        Set EntityIndex = Synergy.CreateIntegerArray()

        Set PResult = Synergy.CreateDoubleArray()

        PlotMgr.GetScalarData ResultID, IndpValues1, EntityIndex, PResult

        vbPResult = PResult.ToVBSArray()

        vbPEntityIndex = EntityIndex.ToVBSArray()

    

    

      

    

    ResultName = StudyName & "_TtRET_"

    ResultName = Replace(ResultName, "/", "_")

    ResultName = Replace(ResultName, "\", "_")

    ResultName = Replace(ResultName, ",", "_")

    ResultName = Replace(ResultName, ":", "_")

    

    

    PathName = "C:\Autodesk\ActiveStudyFLF.txt"

    

    

    Set fso = CreateObject("Scripting.FileSystemObject")

    Set f = fso.OpenTextFile(PathName, 2, True)

    

    HeadingString = ResultName

    HeadingString = HeadingString + "3D_FLF" & vbCrLf & vbCrLf & vbCrLf

    

    f.write HeadingString

    

    For i = 0 To UBound(vbPResult)

        f.write vbPResult(i) & vbCrLf

    Next



    

    

    ErrorBoolean = False

    

        CellFillTest = Range("B7").Value

    

    If CellFillTest = "" Then

    

        ErrorBoolean = False

    Else

    

        ErrorBoolean = True

        MsgBox "Delete the raw data in Column B before clicking the button again"

        

    End If

    

    StudyNameFull = StudyDoc.StudyName



    

    If ErrorBoolean = False Then

    



        'The code below gets the file name from the entire path

        DotNo = InStrRev(StudyNameFull, ".") - 1

        SlashNo = InStrRev(StudyNameFull, "\")

        StrLength = DotNo - SlashNo

        StudyName = Mid(StudyNameFull, SlashNo + 1, StrLength)

        StudyName = Left(StudyName, 10)

        StudyNameFull = Left(StudyNameFull, 5)

        MySheetName2 = Left(StudyNameFull, 4)

      

        ' The code below sets the Heading1 text

        Heading1 = "            Time [s]"

       

        ' The code below determines the heading2 text

'        If OptUseFile.Value = True Then

'            Heading2 = StudyName

'        Else

'            Heading2 = TextBoxHeading2.Value

'        End If

          

        Heading2 = StudyName

      'The code below sets the headings in the spreadsheet

            'and moves the active location to the start of data inport

        

        Range("B7").Activate

     

    

'        StartRow = TextBoxStartRow.Text

'

        RanNum = Int((100 * Rnd) + 1) ' This is used in the import data name to ensure a unique name



        TextFile = "C:\Autodesk\ActiveStudyFLF.txt"

            

        'The With sequence creates the imported text area

            

         With ActiveSheet.QueryTables.Add(Connection:="TEXT;" & TextFile, Destination:=ActiveCell)

                .Name = StudyName & RanNum

                .FieldNames = True

                .RowNumbers = False

                .FillAdjacentFormulas = False

                .PreserveFormatting = True

                .RefreshOnFileOpen = False

                .RefreshStyle = xlInsertDeleteCells

                .SavePassword = False

                .SaveData = True

                .AdjustColumnWidth = True

                .RefreshPeriod = 0

                .TextFilePromptOnRefresh = False

                .TextFilePlatform = 437

                .TextFileStartRow = 1

                .TextFileParseType = xlDelimited

                .TextFileTextQualifier = xlTextQualifierDoubleQuote

                .TextFileConsecutiveDelimiter = False

                .TextFileTabDelimiter = True

                .TextFileSemicolonDelimiter = False

                .TextFileCommaDelimiter = False

                .TextFileSpaceDelimiter = False

                .TextFileColumnDataTypes = Array(1, 1)

                .TextFileTrailingMinusNumbers = True

                .Refresh BackgroundQuery:=False

                

            End With

                

            Selection.CurrentRegion.NumberFormat = "#,##0.000"

            Selection.CurrentRegion.Font.Bold = False

            



            Range("G9").Value = StudyNameFull

            

            Columns("B:B").Select

            Selection.ColumnWidth = 10

            

            Dim ws2 As Worksheet



            Set ws2 = ActiveSheet

            ActiveSheet.Name = MySheetName2



            Range("G9").Select

            

            

            

    End If



    









End Sub







