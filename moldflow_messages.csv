MSCD_CODE,PARAMETERS,MESSAGE_TEXT,SEVERITY
900001,2 0 0 0 0 0 0 19,"No material property set found.",ERROR
900002,2 0 0 0 0 0 0 19,"No nonlinear stress-strain curve data found.",ERROR
900003,3 0 0 0 0 0 0 19,"Nonlinear mechanical data ID (%d) is inconsistent with the selected material ID (%d).",ERROR
3000000,3 0 0 0 0 0 0 1,"Study Modification Utility",INFO
3000010,12 0 0 0 0 1 1,"NAME: Studymod - Modify a study File SYNOPSIS: %s <InputStudy> <OutputStudy> <ModifierFile> DESCRIPTION: <InputStudy> Name of the original study file <OutputFile> Name of the modified study file <ModifiedFile> Name of the file containing the modifications to make",INFO
3000100,3 0 0 0 0 0 0 1,"Result Extraction Utility",INFO
3000110,65 0 0 0 0 1 1,"NAME: Studyrlt - Result Extraction Utility SYNOPSIS: %s <study> -message <sequence> <message ID> <occurrence> <item> [-unit SI|Metric|English] <study> -exportoutput [<sequence>] [-output <filename>] [-unit SI|Metric|English] <study> -exportudm [-output <filename>] <study> -exportwarp <result ID> -actual|-opposite -scale <x> -output <filename> [-unit SI|Metric|English] <study> -xml <result ID> <study> -exportpatran <study> -result <result ID> -min|-max|-average|-stddev|-count|-node <node number>|-element <element number> [-layer <layer name>|-cavity|-gate|-runner|-sprue] [-component <number> [-anchor <node1> <node2> <node3>] [-unit SI|Metric|English]",INFO
3000120,2 0 0 0 0 0 0 19,"Invalid study file name.",ERROR
3000130,2 0 0 0 0 0 0 19,"Study file does not exist.",ERROR
3000140,2 0 0 0 0 0 0 19,"Cannot open study file.",ERROR
3000150,2 0 0 0 0 0 0 19,"Cannot extract required result.",ERROR
3000160,2 0 0 0 0 0 0 1,"Result value is = %v",INFO
3000161,2 0 0 0 0 1 1,"Result value written to file: %s",INFO
3000170,2 0 0 0 0 0 0 19,"Sequence is invalid.",ERROR
3000180,2 0 0 0 0 0 0 19,"Message ID is invalid.",ERROR
3000190,2 0 0 0 0 0 0 19,"Occurrence is invalid.",ERROR
3000200,2 0 0 0 0 0 0 19,"Item is invalid.",ERROR
3000205,2 0 0 0 0 0 0 19,"Output file name is invalid.",ERROR
3000207,2 0 0 0 0 0 0 19,"Unit is invalid.",ERROR
3000210,2 0 0 0 0 0 0 19,"Result ID is invalid.",ERROR
3000215,2 0 0 0 0 0 0 19,"Scale factor is invalid.",ERROR
3000220,2 0 0 0 0 0 0 19,"Result type is not supported.",ERROR
3000225,2 0 0 0 0 0 0 19,"Option is not supported.",ERROR
3000230,2 0 0 0 0 0 0 19,"Results with one or more independent variables are not supported.",ERROR
3000240,2 0 0 0 0 0 0 19,"Component index is invalid.",ERROR
3000250,2 0 0 0 0 0 0 19,"Anchor node is invalid.",ERROR
3000260,2 0 0 0 0 0 0 19,"Element %d is invalid.",ERROR
3000270,2 0 0 0 0 0 0 19,"Region is invalid.",ERROR
3000280,2 0 0 0 0 0 0 19,"Node/Element is invalid.",ERROR
3000290,2 0 0 0 0 0 0 19,"No Data Found.",ERROR
3000300,2 0 0 0 0 0 0 19,"Failed to create anchor plane.",ERROR
3000310,3 0 0 0 0 0 0 11,"An injection location has already been set at Node %d.",WARNING
3000320,3 0 0 0 0 0 0 11,"An injection location has already been set around absolute coordinates (%11.4G %11.4G %11.4G).",WARNING
3000330,3 0 0 0 0 0 0 11,"An injection location has already been set around normalized coordinates (%11.4G %11.4G %11.4G).",WARNING
3000340,4 0 0 0 0 0 0 11,"Your setting of reference TSET %d for TCOD %d is not consistent with the Moldflow definition. This setting is skipped. Please check and use correct reference TSET.",WARNING
3000400,2 0 0 0 0 0 0 19,"Material ID is missing.",ERROR
3000401,2 0 0 0 0 0 0 19,"The material database location is missing.",ERROR
3000402,3 0 0 0 0 0 0 19,"Access to the User/System material databases is only available on Windows with a compatible Autodesk Moldflow Synergy.",ERROR
3000403,2 0 0 0 0 1 19,"Database failed to load : %s",ERROR
3000404,3 0 0 0 0 0 0 19,"The material could not be found inside the User/System databases or the type of the material is not compatible with the study.",ERROR
3000405,3 0 0 0 0 0 0 19,"The material could not be found at the selected database location or the type of the material is not compatible with the study.",ERROR
3000406,2 0 0 0 0 0 0 19,"The selected injection shot is invalid.",ERROR
3000407,3 0 0 0 0 1 11,"More than one target materials are found with material ID : %s. The first one is selected.",WARNING
3000510,2 0 0 0 0 0 0 1,"Applying material changes ...",INFO
3000511,2 0 0 0 0 1 1,"Changed the output study material to material ID : %s",INFO
3000512,1 0 0 0 0 1 1,"for injection shot : %s",INFO
3000513,2 0 0 0 0 0 0 19,"Cannot open modifier file.",ERROR
3000514,2 0 0 0 0 0 0 19,"Modifier file is invalid.",ERROR
3000515,2 0 0 0 0 0 0 19,"TSet ID is missing.",WARNING
3000516,2 0 0 0 0 0 0 19,"TCode ID is missing.",WARNING
3000517,2 0 0 0 0 1 1,"TCode ID: %s is invalid.",WARNING
3000518,2 0 0 0 0 1 1,"Failed to modify TCode %s values.",INFO
3000519,2 0 0 0 0 0 0 19,"Unit system is invalid.",ERROR
3000520,2 0 0 0 0 1 1,"A new TSet: %s is create.",WARNING
3000521,2 0 0 0 0 0 0 19,"TSet ID is invalid.",WARNING
3000522,2 0 0 0 0 0 0 19,"Node ID is missing.",WARNING
3000523,2 0 0 0 0 0 0 19,"The attribute cmd is invalid.",WARNING
3000524,2 0 0 0 0 1 1,"Node ID: %s is invalid.",WARNING
3000525,2 0 0 0 0 0 0 19,"Normal vector is missing.",WARNING
3000526,2 0 0 0 0 0 0 19,"The target node for moving boundary is missing.",WARNING
3000527,2 0 0 0 0 1 19,"The target node ID: %s for moving boundary is invalid.",WARNING
3000528,2 0 0 0 0 0 0 19,"The attribute cmd is missing.",WARNING
3000550,2 0 0 0 0 0 0 19,"The analysis type is missing.",WARNING
3000551,2 0 0 0 0 1 1,"The analysis type: %s is invalid.",WARNING
3000600,2 0 0 0 0 0 0 19,"The imported file name is missing.",ERROR
3000601,2 0 0 0 0 1 1,"The imported file name: %s is invalid.",ERROR
3000602,2 0 0 0 0 1 1,"The unit for meshing: %s is invalid, default unit will be used.",WARNING
3000603,2 0 0 0 0 1 1,"Mesh type for meshing: %s is invalid.",WARNING
3000604,2 0 0 0 0 1 1,"Mesh option name: %s is invalid.",WARNING
3000605,2 0 0 0 0 0 0 19,"Mesh option name is missing.",ERROR
3000606,2 0 0 0 0 0 0 19,"Mesh option value is missing.",ERROR
4000001,4 0 0 0 0 0 0 19,"There has been a file sharing problem. You may have multiple Fatigue Wizard sessions running. Please investigate.",ERROR
4000002,2 0 0 0 0 1 5,"Start loading AGSDB model %s for Fatigue Analysis ......",INFO
4000003,2 0 0 0 0 0 0 5,"Unzipping NODES, ELEMENTS, NODE CONDITION, and ELEMENT CONDITION tables ......",INFO
4000004,2 0 0 0 0 1 5,"AGSDB File %s is not found.",ERROR
4000005,2 0 0 0 0 0 0 5,"Performing STRESS Based Calculations ......",INFO
4000006,2 0 0 0 0 0 0 5,"Performing STRAIN Based Calculations ......",INFO
4000007,2 0 0 0 0 0 0 1,"Fatigue Analysis completed.",INFO
4000008,2 0 0 0 0 0 0 1,"Reading Geometry Data ......",INFO
4000009,2 0 0 0 0 0 0 1,"Please Wait. Calculations in progress ......",INFO
4000010,2 0 0 0 0 0 0 13,"Failed convergence in Design Life Iteration",WARNING
4000011,2 0 0 0 0 0 0 1,"Reading Stress Results ......",INFO
4000012,2 0 0 0 0 0 0 5,"Reading Stress Results - Loadcase %d",INFO
4000013,3 0 0 0 0 1 19,"Incompatible .nso file format %s is incompatible NSO",ERROR
4000014,3 0 0 0 0 0 0 19,"The stress interface file for fatigue analyses was not specified.",ERROR
4000015,2 0 0 0 0 0 0 5,"Length: (m), Stress",INFO
4000016,2 0 0 0 0 0 0 5,"Performing Peak-Valley Picking ......",INFO
4000017,2 0 0 0 0 0 0 5,"Peak-Valley Picking Load Histories ......",INFO
4000018,2 0 0 0 0 0 0 19,"Load case %d is not found.",ERROR
4000019,2 0 0 0 0 0 0 19,"No stress result is found.",ERROR
4000020,2 0 0 0 0 0 0 5,"Performing RainFlow Analysis ......",INFO
4000021,2 0 0 0 0 0 0 19,"No fatigue property data were found for material (%d).",ERROR
4000022,4 0 0 0 0 0 0 19,"This material is not an isotropic metal. Fatigue analysis is not available for this type of materials.",ERROR
4000023,2 0 0 0 0 0 0 5,"Generating free surface ......",INFO
4000024,4 0 0 0 0 0 0 19,"The Ultimate Tensile Stress value (%g) is not valid. Fatigue analysis cannot proceed with this material (%d).",ERROR
4000025,2 0 0 0 0 0 0 5,"Concatenating Load Histories ......",INFO
4000026,2 0 0 0 0 0 0 5,"Sorting Load Histories ......",INFO
4000027,2 0 0 0 0 0 0 5,"Optimising Load Histories ......",INFO
4000028,2 0 0 0 0 0 0 5,"Interpolating Load Histories ......",INFO
4000029,2 0 0 0 0 0 0 19,"FATAL ERROR! - All time histories should start and finish at the same time.",ERROR
4000030,2 0 0 0 0 0 0 19,"No parameter set is found.",ERROR
4000031,3 0 0 0 0 0 0 19,"Surface extraction failed. Please check your model data and try again.",ERROR
4000032,2 0 0 0 0 0 0 19,"Material data for group %d were not found.",ERROR
4000033,2 0 0 0 0 0 0 5,"Please Wait. Sorting Max Stresses ......",INFO
4000034,2 0 0 0 0 0 0 5,"Please Wait. Resolving stresses ......",INFO
4000035,2 0 0 0 0 0 0 19,"Required Stress life (S-N) data were not found.",ERROR
4000036,2 0 0 0 0 0 0 19,"Required Strain life (E-N) data were not found.",ERROR
4000037,2 0 0 0 0 0 0 5,"Performing Transient Life Calculation ......",INFO
4000038,2 0 0 0 0 0 0 5,"Performing Multi-load Life Calculation ......",INFO
4000039,2 0 0 0 0 0 0 5,"Performing Spectrum Life Calculation ......",INFO
4000040,2 0 0 0 0 0 0 19,"Required load history data were not found.",ERROR
4000041,2 0 0 0 0 0 0 19,"Required time history data were not found.",ERROR
4000042,2 0 0 0 0 1 5,"Start reading %s for Fatigue Analysis ......",INFO
4000043,1 0 0 0 0 0 0 13,"Material (%d) is not found for element (%d).",WARNING
4000044,1 0 0 0 0 0 0 13,"Parameter set (%d) is not found for element (%d).",WARNING
4000045,23 0 0 0 0 0 0 5,"NAME: Fatigue Analysis SYNOPSIS: [-<package>] model_file [-output filePrefix] [-units]/[-to]/[-check] DESCRIPTION: -<package> to specify which package the stress result is obtained from model_file mesh model filename (with extension) -output filePrefix to specify an output file prefix -unit to specify units system used for the stress analysis -to to specify which package the fatigue result should be produced Default to 2 -check Output a mesh model for check EXAMPLE: mfatigue -units Metric -ASME -to 2 ds.asd -output myTest",INFO
4000046,4 0 0 0 0 0 0 5,"SUMMARY",INFO
4000047,1 0 0 0 0 0 0 5,"LIFE TO FAILURE",INFO
4000048,2 0 0 0 0 0 0 5,"SAFETY FACTOR FOR %d CYCLES",INFO
4000049,5 0 0 0 0 0 0 5,"MEAN STRESS CORRECTION NONE = %g GERBER = %g GOODMAN= %g",INFO
4000050,5 0 0 0 0 0 0 5,"MEAN STRAIN CORRECTION NONE = %g MORROW = %g SMITH-WATSON-TOPPER= %g",INFO
4000051,1 0 0 0 0 0 0 5,"FATIGUE SAFETY FACTOR",INFO
4000052,1 0 0 0 0 0 0 5,"GOODMAN DIAGRAM",INFO
4000053,1 0 0 0 0 0 0 5,"HAIGH DIAGRAM",INFO
4000054,3 0 0 0 0 0 0 5,"FSF (const=R) = %g FSF (const=M) = %g",INFO
4000055,2 0 0 0 0 0 0 5,"First %d values NodeID,No mean,Gerber,Goodman",INFO
4000056,2 0 0 0 0 0 0 5,"First %d values NodeID,No mean,Morrow,Smith-Watson-Topper",INFO
4000057,2 0 0 0 0 0 0 5,"First %d values NodeID,Const=R,Const=M",INFO
4000058,2 0 0 0 0 0 0 19,"No fatigue result is found. Please check your settings.",ERROR
4000059,2 0 0 0 0 0 0 19,"No fatigue method is selected. Please check your settings.",ERROR
4000060,2 0 0 0 0 1 19,"No node is found in AGSDB File %s.",ERROR
4000061,2 0 0 0 0 1 19,"No element is found in AGSDB File %s.",ERROR
4000062,3 0 0 0 0 0 0 5,"Fatigue Analysis",INFO
4000063,2 0 0 0 0 0 0 13,"Stress caused by clamping is not found.",WARNING
4000064,2 0 0 0 0 0 0 13,"Thermal stress is not found.",WARNING
4000065,2 0 0 0 0 0 0 13,"Stress caused by core-shift is not found.",WARNING
4000066,2 0 0 0 0 0 0 13,"No parameters found for group %d.",WARNING
4000067,2 0 0 0 0 0 0 19,"Required data for fatigue analysis is not found.",ERROR
4000068,4 0 0 0 0 1 19,"Error occured in Abaqus analysis: (%s...) Please fix the problem and re-run Abaqus.",ERROR
4000069,4 0 0 0 0 1 19,"Autodesk Simulation Mechanical: License is not found. (%s...) Please install Autodesk Simulation Mechanical with appropriate license and re-run Analysis.",ERROR
4000070,3 0 0 0 0 1 19,"Autodesk Simulation Mechanical is aborted. Please check %s.l file to see what error caused the abortion.",ERROR
4000071,3 0 0 0 0 1 19,"Autodesk Simulation Mechanical is not converged. Please check %s.l file to see the non-convergence messages.",ERROR
4000072,3 0 0 0 0 1 19,"%s encountered during the stress analysis with Autodesk Simulation Mechanical",ERROR
4000073,3 0 0 0 0 1 13,"Autodesk Simulation Mechanical %s",WARNING
4000074,3 0 0 0 0 0 0 5,"Fatigue analysis indicates no damage in this mold design for its desired lifecycle.",INFO
4000075,2 0 0 0 0 0 0 5,"Fatigue calculation on element group %d:",INFO
4000076,3 0 0 0 0 0 0 19,"Autodesk Simulation Mechanical is not found. Please install Autodesk Simulation Mechanical and re-run Analysis.",ERROR
4000077,2 0 0 0 0 0 0 13,"No element found connecting to node %d.",WARNING
4000078,2 0 0 0 0 0 0 19,"Node %d does not exist.",ERROR
4000079,2 0 0 0 0 0 0 19,"Element %d does not exist.",ERROR
4000080,2 0 0 0 0 0 0 19,"A non-physical value is found in user-defined S-N[%d](%g,%g).",ERROR
9800,3 0 0 0 0 0 0 1,"Coolant circuit flow-rate controller :",INFO
9810,1 3 0 0 3 1 5,"ID = %3d : %s Coolant temperature = %11.4G Coolant flow rate = %11.4G",INFO
9850,3 0 0 0 0 0 0 5,"Coolant circuit pressure controller",INFO
9860,1 3 0 0 3 1 5,"ID = %3d : %s Coolant temperature = %11.4G Coolant pressure drop = %11.4G",INFO
9880,3 0 0 0 0 0 0 5,"Coolant circuit Reynolds number controller :",INFO
9890,1 3 0 0 3 1 5,"ID = %3d : %s Coolant temperature = %11.4G Coolant Reynolds number = %11.4G",INFO
10000,1 0 0 0 0 0 0 5,"Maximum machine clamp force = %11.4E",INFO
10001,1 0 0 0 0 0 0 5,"Maximum injection volume = %11.4E",INFO
10002,1 0 0 0 0 0 0 5,"Maximum injection pressure = %11.4E",INFO
10004,1 0 0 0 0 0 0 5,"Maximum machine injection stroke = %11.4E",INFO
10005,1 0 0 0 0 0 0 5,"Maximum machine injection rate = %11.4E",INFO
10006,1 0 0 0 0 0 0 5,"Machine hydraulic response time = %11.4E",INFO
10011,1 0 0 0 0 1 5,"Trade name %s",INFO
10012,1 0 0 0 0 1 5,"Manufacturer %s",INFO
10013,1 0 0 0 0 0 0 5,"Maximum machine hydraulic pressure = %11.4E",INFO
10014,1 0 0 0 0 0 0 5,"Material/hydraulic pressure ratio = %11.4E",INFO
10015,1 0 0 0 0 0 0 5,"Machine screw diameter = %11.4E",INFO
10017,1 0 0 0 0 0 0 5,"Velocity ram control type = Linear",INFO
10018,1 0 0 0 0 0 0 5,"Velocity ram control type = Constant",INFO
10019,1 0 0 0 0 0 0 5,"Machine maximum velocity profile steps = %11d",INFO
10020,1 0 0 0 0 0 0 5,"Pressure ram control type = Linear",INFO
10021,1 0 0 0 0 0 0 5,"Pressure ram control type = Constant",INFO
10022,1 0 0 0 0 0 0 5,"Machine maximum pressure profile steps = %11d",INFO
10023,1 0 0 0 0 0 0 5,"Machine ram position ram-velocity profile support = Yes",INFO
10024,1 0 0 0 0 0 0 5,"Machine ram position ram-velocity profile support = No",INFO
10025,1 0 0 0 0 0 0 5,"Machine time-ram velocity profile support = Yes",INFO
10026,1 0 0 0 0 0 0 5,"Machine time-ram velocity profile support = No",INFO
10027,1 0 0 0 0 0 0 5,"Machine time-ram position profile support = Yes",INFO
10028,1 0 0 0 0 0 0 5,"Machine time-ram position profile support = No",INFO
10100,1 0 0 0 0 0 0 5,"Fill time = %11.4G",INFO
10110,1 0 0 0 0 0 0 5,"Fill time = Automatic",INFO
10101,1 0 0 0 0 0 0 5,"Flow Rate = %11.4G",INFO
10102,1 0 0 0 0 0 0 1,"Post-fill time = %11.4G",INFO
10104,1 0 0 0 0 0 0 5,"Timer for core or gas injection = %11.4G",INFO
10156,1 0 0 0 0 0 0 5,"Open/Close control = Instant",INFO
10158,1 0 0 0 0 0 0 5,"Open/Close control = Velocity",INFO
10162,1 0 0 0 0 0 0 5,"Stroke = %11.4G",INFO
10164,1 0 0 0 0 0 0 5,"Pin diameter = %11.4G",INFO
10168,3 1 0 0 2 0 5,"Open velocity profile: Distance increment velocity %11.4G %11.4G",INFO
10170,3 1 0 0 2 0 5,"Close velocity profile: Distance increment velocity %11.4G %11.4G",INFO
10172,3 1 0 0 2 0 5,"Re-open velocity profile: Distance increment velocity %11.4G %11.4G",INFO
10174,3 1 0 0 2 0 5,"Re-close velocity profile: Distance increment velocity %11.4G %11.4G",INFO
10180,5 0 0 0 0 1 5,"Valve gate controller name: %s Opens and closes by: Time Controller ID Open Close",INFO
10182,5 0 0 0 0 1 5,"Valve gate controller name: %s Opens and closes by: Filled volume %% Controller ID Open Close",INFO
10184,5 0 0 0 0 1 5,"Valve gate controller name: %s Opens and closes by: Ram position Controller ID Open Close",INFO
10186,7 0 0 0 0 1 5,"Valve gate controller name: %s Opens and closes by: Flow front reaches node specified Node ID: %10d Delay time: %11.4G Controller ID Open Close",INFO
10188,6 0 0 0 0 1 5,"Valve gate controller name: %s Opens and closes by: Flow front reaches gate node Delay time: %11.4G Controller ID Open Close",INFO
10190,6 0 0 0 0 1 5,"Valve gate controller name: %s Opens and closes by: Pressure at node specified Node ID: %10d Controller ID Open Close",INFO
10192,5 0 0 0 0 1 5,"Valve gate controller name: %s Opens and closes by: Pressure at gate node Controller ID Open Close",INFO
10203,1 0 0 0 2 0 5,"%5d By flow front %11.4G",INFO
10204,1 0 0 0 3 0 5,"%5d %11.4G %11.4G",INFO
10205,1 0 0 0 3 0 5,"%5d %11.4G %11.4G",INFO
10206,1 0 0 0 3 0 5,"%5d %11.4G %11.4G",INFO
10207,1 0 0 0 3 0 5,"%5d %11.4G %11.4G",INFO
10208,2 0 0 0 0 0 0 5,"Velocity/pressure switch-over by = Automatic",INFO
10209,2 0 0 0 0 0 0 5,"Velocity/pressure switch-over by ram position at = %11.4G",INFO
10300,2 0 0 0 0 0 0 5,"Velocity/pressure switch-over by %% volume = %11.4G",INFO
10301,2 0 0 0 0 0 0 5,"Velocity/pressure switch-over by clamp force = %11.4G",INFO
10302,2 0 0 0 0 0 0 5,"Velocity/pressure switch-over by injection pressure= %11.4G",INFO
10304,5 0 0 0 0 0 0 5,"Velocity/pressure switch-over by pressure control point Node = %7d Pressure = %11.4G",INFO
10305,2 0 0 0 0 0 0 5,"Velocity/pressure switch-over by injection time = %11.4G",INFO
10306,2 0 0 0 0 0 0 5,"Velocity/pressure switch-over by = Whichever comes first",INFO
10308,2 0 0 0 0 0 0 5,"Velocity/pressure switch-over by %% weight reduction = %11.4G",INFO
10310,1 0 0 0 0 0 0 5,"Reference solid part weight = %11.4G",INFO
10312,2 0 0 0 0 0 0 5,"Velocity/pressure switch-over by part weight = %11.4G",INFO
10400,1 0 0 0 0 0 0 5,"Packing/holding time = %11.4G",INFO
10600,3 1 0 0 2 0 5,"Ram speed profile (abs): stroke %% max. speed %11.4G %11.4G",INFO
10602,3 1 0 0 2 0 5,"Ram speed profile (rel): %% stroke %% ram speed %11.4G %11.4G",INFO
10604,3 1 0 0 2 0 5,"Ram speed profile (abs stroke - speed): stroke speed %11.4G %11.4G",INFO
10606,3 1 0 0 2 0 5,"Ram speed profile (time vs. stroke): time stroke %11.4G %11.4G",INFO
10608,3 1 0 0 2 0 5,"Recommended ram speed profile (rel): %% Shot volume %% Flow rate %11.4G %11.4G",INFO
10700,3 1 0 0 2 0 5,"Pack/hold pressure profile (abs): duration %% max. pressure %11.4G %11.4G",INFO
10702,3 1 0 0 2 0 5,"Pack/hold pressure profile (rel): %% time %% fill pressure %11.4G %11.4G",INFO
10704,3 1 0 0 2 0 5,"Injection pressure profile (abs pressure): time pressure %11.4G %11.4G",INFO
10800,1 0 0 0 0 0 0 5,"Pellet diameter = %11.4G",INFO
10802,1 0 0 0 0 0 0 5,"Pellet length = %11.4G",INFO
10804,1 0 0 0 0 0 0 5,"Transfer pot temperature = %11.4G",INFO
10806,1 0 0 0 0 0 0 5,"Delay time in the pot = %11.4G",INFO
10808,1 0 0 0 0 0 0 5,"Melt temperature after preconditioning = %11.4G",INFO
10810,1 0 0 0 0 0 0 5,"Melt conversion after preconditioning = %11.4G",INFO
10812,1 0 0 0 0 0 0 5,"Barrel Delay time = %11.4G",INFO
10814,2 0 0 0 0 0 0 5,"Stop flow calculation when pellet volume is smaller than filled volume? = Yes",INFO
10816,2 0 0 0 0 0 0 5,"Stop flow calculation when pellet volume is smaller than filled volume? = No",INFO
10818,1 0 0 0 0 0 0 5,"Perform preconditioning analysis = Yes",INFO
10820,1 0 0 0 0 0 0 5,"Perform preconditioning analysis = No",INFO
11000,1 0 0 0 0 0 0 5,"Ambient temperature = %11.4G",INFO
11002,1 0 0 0 0 0 0 5,"Melt temperature = %11.4G",INFO
11004,1 0 0 0 0 0 0 5,"Ideal cavity-side mold temperature = %11.4G",INFO
11006,1 0 0 0 0 0 0 5,"Ideal core-side mold temperature = %11.4G",INFO
11010,1 0 0 0 0 0 0 5,"Inlet melt conversion = %11.4G",INFO
11020,1 0 0 0 0 0 0 5,"Initial encapsulant temperature = %11.4G",INFO
11022,1 0 0 0 0 0 0 5,"Initial encapsulant conversion = %11.4G",INFO
11024,1 0 0 0 0 0 0 5,"Substrate temperature = %11.4G",INFO
11026,1 0 0 0 0 0 0 5,"Curing temperature = %11.4G",INFO
11028,1 0 0 0 0 0 0 5,"Curing time = %11.4G",INFO