﻿// Decompiled with JetBrains decompiler
// Type: HDMFReport.clsHyundai_CMPR
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using HDLog4Net;
using HDMoldFlowLibrary;
using Microsoft.Office.Core;
using Microsoft.Office.Interop.PowerPoint;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace HDMFReport
{
  internal class clsHyundai_CMPR : clsBase
  {
    private string m_strStudyName = string.Empty;

    public override void ExportCMPRReport(
      List<DataRow> p_lst_drStudy,
      string p_strLangType,
      clsHDMFLibDefine.Company p_enumCompany)
    {
      Dictionary<string, string> p_dicValue = new Dictionary<string, string>();
      Dictionary<string, string> p_dicView = new Dictionary<string, string>();
      List<string> p_lstDelete = new List<string>();
      List<CMPRData> p_lst_CMPRData = new List<CMPRData>();
      try
      {
        foreach (DataRow p_drStudy in p_lst_drStudy)
        {
          CMPRData cmprData = new CMPRData();
          if (!clsHDMFLib.OpenStudy(p_drStudy["Name"].ToString()))
            return;
          clsHyundaiData.GetReportUser(p_drStudy, out p_dicValue, out p_dicView, out p_lstDelete);
          cmprData.drStudy = p_drStudy;
          cmprData.dicValue = p_dicValue;
          cmprData.dicView = p_dicView;
          p_lst_CMPRData.Add(cmprData);
        }
        this.StartCMPRExport(p_lst_CMPRData, p_enumCompany, p_strLangType);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai_CMPR]ExportCMPRReport):" + ex.Message));
      }
    }

    protected override void StartCMPRExport(
      List<CMPRData> p_lst_CMPRData,
      clsHDMFLibDefine.Company p_enumCompany,
      string p_strLangType = "KOR")
    {
      int num = 1;
      List<Dictionary<string, string>> p_lst_dicData = new List<Dictionary<string, string>>();
      StringBuilder stringBuilder = new StringBuilder();
      foreach (CMPRData cmprData in p_lst_CMPRData)
      {
        DataRow drStudy = cmprData.drStudy;
        Dictionary<string, string> dicValue = cmprData.dicValue;
        Dictionary<string, string> dicView = cmprData.dicView;
        if (!clsHDMFLib.OpenStudy(drStudy["Name"].ToString()))
          return;
        Dictionary<string, string> analysisData = clsHyundaiData.GetAnalysisData(drStudy, dicValue, dicView);
        p_lst_dicData.Add(analysisData);
        clsHDMFLib.CloseStudy(false);
      }
      for (int index = 0; index < 2; ++index)
      {
        if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Item[User]")))
          stringBuilder.Append(p_lst_dicData[index]["Item[User]"]);
        if (index != 1)
          stringBuilder.Append(" VS ");
      }
      this.m_strStudyName = stringBuilder.ToString();
      int count = p_lst_CMPRData.Count;
      FileInfo fileInfo;
      while (true)
      {
        fileInfo = new FileInfo(clsReportDefine.g_diProject.Parent.FullName + "\\" + clsReportDefine.g_diProject.Name + "_Export\\" + p_lst_CMPRData[0].drStudy["Name"] + "_" + DateTime.Now.ToString("yyMMdd") + "_Report_" + (object) count + "-Case_Rev" + (object) num + "." + clsReportDefine.g_dicExtension["PowerPoint"]);
        if (fileInfo.Exists)
          ++num;
        else
          break;
      }
      if (!fileInfo.Directory.Exists)
        fileInfo.Directory.Create();
      try
      {
        // ISSUE: variable of a compiler-generated type
        Presentations presentations = ((_Application) Activator.CreateInstance(Marshal.GetTypeFromCLSID(new Guid("91493441-5A91-11CF-8700-00AA0060263B")))).Presentations;
        FileInfo template = clsReportData.GetTemplate(p_enumCompany, p_strLangType, count);
        template.Refresh();
        if (template.Exists)
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          Presentation presentation = presentations.Open(template.FullName, MsoTriState.msoTrue, WithWindow: MsoTriState.msoFalse);
          List<Slide> slideList = new List<Slide>();
          if (count == 2)
          {
            List<Slide> case2AllSlide = this.GetCase2AllSlide(presentation);
            this.RemoveUnusedSlides(p_lst_CMPRData[0].drStudy, p_lst_CMPRData[0].dicValue, ref case2AllSlide);
            this.SetCase2AllSlide(case2AllSlide, p_lst_dicData);
          }
          if (!fileInfo.Extension.Contains(".ppt"))
          {
            // ISSUE: reference to a compiler-generated method
            presentation.SaveAs(fileInfo.FullName.Replace(fileInfo.Extension, ".pptx"));
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            presentation.SaveAs(fileInfo.FullName);
          }
          // ISSUE: reference to a compiler-generated method
          presentation.Close();
          clsReportUtill.ReleaseComObject((object) presentation);
        }
        if (fileInfo.Extension.Contains(".ppt"))
          return;
        if (fileInfo.Exists)
          fileInfo.Delete();
        File.Move(fileInfo.FullName.Replace(fileInfo.Extension, ".pptx"), fileInfo.FullName);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai_CMPR]StartCMPRExport):" + ex.Message));
      }
    }

    private void RemoveUnusedSlides(
      DataRow p_drStudy,
      Dictionary<string, string> p_dicValue,
      ref List<Slide> p_lst_slAll)
    {
      if (!p_drStudy["Sequence"].ToString().Contains("Cool"))
      {
        List<Slide> slideList = new List<Slide>();
        foreach (Slide slide in p_lst_slAll)
        {
          if (slide.Name == "Result7" || slide.Name == "Result10")
            slideList.Add(slide);
        }
        for (int index = 0; index < slideList.Count; ++index)
        {
          // ISSUE: reference to a compiler-generated method
          slideList[index].Delete();
          p_lst_slAll.Remove(slideList[index]);
        }
      }
      if (!p_drStudy["Sequence"].ToString().Contains("Warp"))
        return;
      List<Slide> slideList1 = new List<Slide>();
      foreach (Slide slide in p_lst_slAll)
      {
        if (slide.Name.Contains("Result9_1"))
        {
          if (p_dicValue["DefAllType"] == "Best fit")
            slideList1.Add(slide);
        }
        else if (slide.Name.Contains("Result9_3"))
        {
          if (p_dicValue["DefXType"] == "Best fit")
            slideList1.Add(slide);
        }
        else if (slide.Name.Contains("Result9_5"))
        {
          if (p_dicValue["DefYType"] == "Best fit")
            slideList1.Add(slide);
        }
        else if (slide.Name.Contains("Result9_7") && p_dicValue["DefZType"] == "Best fit")
          slideList1.Add(slide);
      }
      for (int index = 0; index < slideList1.Count; ++index)
      {
        // ISSUE: reference to a compiler-generated method
        slideList1[index].Delete();
        p_lst_slAll.Remove(slideList1[index]);
      }
    }

    private List<Slide> GetCase2AllSlide(Presentation p_pptPre)
    {
      List<Slide> case2AllSlide = new List<Slide>((IEnumerable<Slide>) p_pptPre.Slides.Cast<Slide>().ToArray<Slide>());
      try
      {
        for (int index = 0; index < case2AllSlide.Count; ++index)
        {
          switch (index)
          {
            case 0:
              case2AllSlide[index].Name = "Title";
              break;
            case 1:
              case2AllSlide[index].Name = "Conditions";
              break;
            case 2:
              case2AllSlide[index].Name = "Conditions_1";
              break;
            case 3:
              case2AllSlide[index].Name = "Conditions_2";
              break;
            case 4:
              case2AllSlide[index].Name = "Result1";
              break;
            case 5:
              case2AllSlide[index].Name = "Result2";
              break;
            case 6:
              case2AllSlide[index].Name = "Result3";
              break;
            case 7:
              case2AllSlide[index].Name = "Result4";
              break;
            case 8:
              case2AllSlide[index].Name = "Result5";
              break;
            case 9:
              case2AllSlide[index].Name = "Result6";
              break;
            case 10:
              case2AllSlide[index].Name = "Result7";
              break;
            case 11:
              case2AllSlide[index].Name = "Result8";
              break;
            case 12:
              case2AllSlide[index].Name = "Result9";
              break;
            case 13:
              case2AllSlide[index].Name = "Result9_1";
              break;
            case 14:
              case2AllSlide[index].Name = "Result9_2";
              break;
            case 15:
              case2AllSlide[index].Name = "Result9_3";
              break;
            case 16:
              case2AllSlide[index].Name = "Result9_4";
              break;
            case 17:
              case2AllSlide[index].Name = "Result9_5";
              break;
            case 18:
              case2AllSlide[index].Name = "Result9_6";
              break;
            case 19:
              case2AllSlide[index].Name = "Result9_7";
              break;
            case 20:
              case2AllSlide[index].Name = "Result10";
              break;
            case 21:
              case2AllSlide[index].Name = "Result10_1";
              break;
            case 22:
              case2AllSlide[index].Name = "Finish";
              break;
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai_CMPR]GetCase2AllSlide):" + ex.Message));
      }
      return case2AllSlide;
    }

    private Dictionary<string, string> GetCase2Slide1(List<Dictionary<string, string>> p_lst_dicData)
    {
      Dictionary<string, string> case2Slide1 = new Dictionary<string, string>();
      try
      {
        for (int index = 0; index < p_lst_dicData.Count; ++index)
        {
          case2Slide1.Add("ReportType[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["ReportType[User]"]);
          case2Slide1.Add("Date[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["Date[User]"]);
          case2Slide1.Add("Engineer[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["Engineer[User]"]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetCase2Slide1):" + ex.Message));
      }
      return case2Slide1;
    }

    private Dictionary<string, string> GetCase2Slide2(List<Dictionary<string, string>> p_lst_dicData)
    {
      Dictionary<string, string> case2Slide2 = new Dictionary<string, string>();
      try
      {
        for (int index = 0; index < p_lst_dicData.Count; ++index)
        {
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Manufacturer[MF]")))
            case2Slide2.Add("Manufacturer[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["Manufacturer[MF]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TradeName[MF]")))
            case2Slide2.Add("Trade Name[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["TradeName[MF]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FamilyAbbreviation[MF]")))
            case2Slide2.Add("Family Abbreviation[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["FamilyAbbreviation[MF]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MI[MF]")))
            case2Slide2.Add("MI[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["MI[MF]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TransitionTemp[MF]")))
            case2Slide2.Add("Transition Temp[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["TransitionTemp[MF]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "EjectionTemp[MF]")))
            case2Slide2.Add("Ejection Temp[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["EjectionTemp[MF]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillingControl[MF]")))
            case2Slide2.Add("Flow Rate[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["FillingControl[MF]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MeltTemp[MF]")))
            case2Slide2.Add("Melt Temp[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["MeltTemp[MF]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CavityCoreTemp[MF]")))
            case2Slide2.Add("Cavity Core Temp[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["CavityCoreTemp[MF]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VPSwitchOver[MF]")))
            case2Slide2.Add("V/P Switch-over[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["VPSwitchOver[MF]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PackHoldingControl[MF]")))
            case2Slide2.Add("Pack/Holding Control[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["PackHoldingControl[MF]"]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetCase2Slide2):" + ex.Message));
      }
      return case2Slide2;
    }

    private Dictionary<string, string> GetCase2Slide3(List<Dictionary<string, string>> p_lst_dicData)
    {
      Dictionary<string, string> case2Slide3 = new Dictionary<string, string>();
      try
      {
        for (int index = 0; index < p_lst_dicData.Count; ++index)
        {
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "LastFillingTime[Log]")))
            case2Slide3.Add("Fill Time[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["LastFillingTime[Log]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SpruePressure[Log]")))
            case2Slide3.Add("Pressure[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["SpruePressure[Log]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ClampForce[Log]")))
            case2Slide3.Add("Clamp Force[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["ClampForce[Log]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "LastGatePressure[Log]")))
            case2Slide3.Add("Last Gate Pressure[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["LastGatePressure[Log]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureAtFlowFront[Plot]")))
          {
            string[] strArray = p_lst_dicData[index]["TemperatureAtFlowFront[Plot]"].Split('|');
            case2Slide3.Add("Temperature At Flow Front[Case" + (object) (index + 1) + "]", strArray[0] + "|" + strArray[1]);
          }
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ElementNumber[Log]")))
            case2Slide3.Add("Element Number[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["ElementNumber[Log]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MeshType[Log]")))
            case2Slide3.Add("Mesh Type[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["MeshType[Log]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Sequence[MF]")))
            case2Slide3.Add("Analysis Sequence[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["Sequence[MF]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PartVolume[Log]")))
            case2Slide3.Add("Part Volume[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["PartVolume[Log]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ProjectedArea[Log]")))
            case2Slide3.Add("Projected Area[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["ProjectedArea[Log]"]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetCase2Slide2):" + ex.Message));
      }
      return case2Slide3;
    }

    private Dictionary<string, string> GetCase2Slide4(List<Dictionary<string, string>> p_lst_dicData)
    {
      Dictionary<string, string> case2Slide4 = new Dictionary<string, string>();
      try
      {
        for (int index = 0; index < p_lst_dicData.Count; ++index)
        {
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Thickness[IMG]")))
            case2Slide4.Add("Image[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["Thickness[IMG]"]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetCase2Slide4):" + ex.Message));
      }
      return case2Slide4;
    }

    private Dictionary<string, string> GetCase2Slide5(List<Dictionary<string, string>> p_lst_dicData)
    {
      Dictionary<string, string> case2Slide5 = new Dictionary<string, string>();
      try
      {
        for (int index = 0; index < p_lst_dicData.Count; ++index)
        {
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ModelRunner[IMG]")))
            case2Slide5.Add("Image[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["ModelRunner[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Coord[Gate]")))
            case2Slide5.Add("Gate Coord[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["Coord[Gate]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Sprue[Gate]")))
            case2Slide5.Add("Sprue Coord[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["Sprue[Gate]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Dimension[Gate]")))
            case2Slide5.Add("Gate Dimension[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["Dimension[Gate]"]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetCase2Slide5):" + ex.Message));
      }
      return case2Slide5;
    }

    private Dictionary<string, string> GetCase2Slide6(List<Dictionary<string, string>> p_lst_dicData)
    {
      Dictionary<string, string> case2Slide6 = new Dictionary<string, string>();
      try
      {
        for (int index = 0; index < p_lst_dicData.Count; ++index)
        {
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern1[IMG]")))
            case2Slide6.Add("Image1[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["FlowPattern1[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern3[IMG]")))
            case2Slide6.Add("Image2[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["FlowPattern3[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern6[IMG]")))
            case2Slide6.Add("Image3[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["FlowPattern6[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern8[IMG]")))
            case2Slide6.Add("Image4[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["FlowPattern8[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillAnimation[IMG]")))
            case2Slide6.Add("Animation[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["FillAnimation[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern1[Plot]")))
            case2Slide6.Add("Time1[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["FlowPattern1[Plot]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern3[Plot]")))
            case2Slide6.Add("Time2[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["FlowPattern3[Plot]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern6[Plot]")))
            case2Slide6.Add("Time3[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["FlowPattern6[Plot]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern8[Plot]")))
            case2Slide6.Add("Time4[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["FlowPattern8[Plot]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "LastFillingTime[Log]")))
            case2Slide6.Add("Last Filling Time[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["LastFillingTime[Log]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "OpenTime[Gate]")))
            case2Slide6.Add("Gate Open Time[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["OpenTime[Gate]"]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetCase2Slide6):" + ex.Message));
      }
      return case2Slide6;
    }

    private Dictionary<string, string> GetCase2Slide7(List<Dictionary<string, string>> p_lst_dicData)
    {
      Dictionary<string, string> case2Slide7 = new Dictionary<string, string>();
      string empty = string.Empty;
      try
      {
        for (int index = 0; index < p_lst_dicData.Count; ++index)
        {
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "WeldLineOverlap[IMG]")))
            case2Slide7.Add("Image1[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["WeldLineOverlap[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "WeldLine[IMG]")))
            case2Slide7.Add("Image2[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["WeldLine[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "WeldLine[User]")))
            case2Slide7.Add("Weld Line[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["WeldLine[User]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "WeldLineScaling[Plot]")))
            case2Slide7.Add("Weld Line Temp[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["WeldLineScaling[Plot]"]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetCase2Slide7):" + ex.Message));
      }
      return case2Slide7;
    }

    private Dictionary<string, string> GetCase2Slide8(List<Dictionary<string, string>> p_lst_dicData)
    {
      Dictionary<string, string> case2Slide8 = new Dictionary<string, string>();
      try
      {
        for (int index = 0; index < p_lst_dicData.Count; ++index)
        {
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SinkMark1[IMG]")))
            case2Slide8.Add("Image1[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["SinkMark1[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SinkMark2[IMG]")))
            case2Slide8.Add("Image2[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["SinkMark2[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SinkMarkAll[Plot]")))
          {
            string[] strArray = p_lst_dicData[index]["SinkMarkAll[Plot]"].Split('|');
            case2Slide8.Add("Sink Mark1[Case" + (object) (index + 1) + "]", strArray[0] + "~" + strArray[1]);
          }
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SinkMark[Plot]")))
          {
            string[] strArray = p_lst_dicData[index]["SinkMark[Plot]"].Split('|');
            case2Slide8.Add("Sink Mark2[Case" + (object) (index + 1) + "]", strArray[0] + "~" + strArray[1]);
          }
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SinkMark[User]")))
            case2Slide8.Add("Sink Mark Comment[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["SinkMark[User]"]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetCase2Slide8):" + ex.Message));
      }
      return case2Slide8;
    }

    private Dictionary<string, string> GetCase2Slide9(List<Dictionary<string, string>> p_lst_dicData)
    {
      StringBuilder stringBuilder = new StringBuilder();
      Dictionary<string, string> case2Slide9 = new Dictionary<string, string>();
      try
      {
        for (int index = 0; index < p_lst_dicData.Count; ++index)
        {
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VolumetricShrinkage2[IMG]")))
            case2Slide9.Add("Image1[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["VolumetricShrinkage2[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VolumetricShrinkage3[IMG]")))
            case2Slide9.Add("Image2[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["VolumetricShrinkage3[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VolumetricShrinkage2[Plot]")))
          {
            string[] strArray = p_lst_dicData[index]["VolumetricShrinkage2[Plot]"].Split('|');
            case2Slide9.Add("Shrinkage1[Case" + (object) (index + 1) + "]", strArray[0] + "~" + strArray[1]);
          }
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VolumetricShrinkage3[Plot]")))
          {
            string[] strArray = p_lst_dicData[index]["VolumetricShrinkage3[Plot]"].Split('|');
            case2Slide9.Add("Shrinkage2[Case" + (object) (index + 1) + "]", strArray[0] + "~" + strArray[1]);
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetCase2Slide9):" + ex.Message));
      }
      return case2Slide9;
    }

    private Dictionary<string, string> GetCase2Slide10(
      List<Dictionary<string, string>> p_lst_dicData)
    {
      Dictionary<string, string> case2Slide10 = new Dictionary<string, string>();
      try
      {
        for (int index = 0; index < p_lst_dicData.Count; ++index)
        {
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFraction1[IMG]")))
            case2Slide10.Add("Image1[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["FrozenLayerFraction1[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFraction2[IMG]")))
            case2Slide10.Add("Image2[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["FrozenLayerFraction2[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFraction3[IMG]")))
            case2Slide10.Add("Image3[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["FrozenLayerFraction3[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFraction4[IMG]")))
            case2Slide10.Add("Image4[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["FrozenLayerFraction4[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFraction1[Plot]")))
            case2Slide10.Add("Time1[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["FrozenLayerFraction1[Plot]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFraction2[Plot]")))
            case2Slide10.Add("Time2[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["FrozenLayerFraction2[Plot]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFraction3[Plot]")))
            case2Slide10.Add("Time3[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["FrozenLayerFraction3[Plot]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFraction4[Plot]")))
            case2Slide10.Add("Time4[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["FrozenLayerFraction4[Plot]"]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetCase2Slide10):" + ex.Message));
      }
      return case2Slide10;
    }

    private Dictionary<string, string> GetCase2Slide11(
      List<Dictionary<string, string>> p_lst_dicData)
    {
      StringBuilder stringBuilder = new StringBuilder();
      Dictionary<string, string> case2Slide11 = new Dictionary<string, string>();
      try
      {
        for (int index = 0; index < p_lst_dicData.Count; ++index)
        {
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperature[IMG]")))
            case2Slide11.Add("Image1[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["CircuitCoolantTemperature[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MoldTempCavity[IMG]")))
            case2Slide11.Add("Image2[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["MoldTempCavity[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MoldTempCore[IMG]")))
            case2Slide11.Add("Image3[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["MoldTempCore[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperatureCavity[Log]")) && p_lst_dicData[index]["CircuitCoolantTemperatureCavity[Log]"] != null && p_lst_dicData[index]["CircuitCoolantTemperatureCavity[Log]"] != "")
            case2Slide11.Add("Circuit Coolant Temperature(Cavity)[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["CircuitCoolantTemperatureCavity[Log]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperatureCore[Log]")) && p_lst_dicData[index]["CircuitCoolantTemperatureCore[Log]"] != null && p_lst_dicData[index]["CircuitCoolantTemperatureCore[Log]"] != "")
            case2Slide11.Add("Circuit Coolant Temperature(Core)[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["CircuitCoolantTemperatureCore[Log]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MoldTempCavityCore[Plot]")))
            case2Slide11.Add("Temperature Mold[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["MoldTempCavityCore[Plot]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cooling[User]")))
            case2Slide11.Add("Cooling[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["Cooling[User]"]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetCase2Slide11):" + ex.Message));
      }
      return case2Slide11;
    }

    private Dictionary<string, string> GetCase2Slide12(
      List<Dictionary<string, string>> p_lst_dicData)
    {
      Dictionary<string, string> case2Slide12 = new Dictionary<string, string>();
      double num1 = 0.0;
      double num2 = 0.0;
      double num3 = 0.0;
      try
      {
        for (int index = 0; index < p_lst_dicData.Count; ++index)
        {
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperature1[IMG]")))
            case2Slide12.Add("Image1[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["TimeToReachEjectionTemperature1[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperature2[IMG]")))
            case2Slide12.Add("Image2[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["TimeToReachEjectionTemperature2[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperature[Plot]")))
            case2Slide12.Add("Time To Reach Ejection Temperature[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["TimeToReachEjectionTemperature[Plot]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillingTime[User]")))
          {
            num1 = Math.Round(clsReportUtill.ConvertToDouble(p_lst_dicData[index]["FillingTime[User]"]), 2);
            case2Slide12.Add("Injection Time[Case" + (object) (index + 1) + "]", num1.ToString());
          }
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PackingTime[User]")))
          {
            num2 = Math.Round(clsReportUtill.ConvertToDouble(p_lst_dicData[index]["PackingTime[User]"]), 2);
            case2Slide12.Add("Pack/Holding Time[Case" + (object) (index + 1) + "]", num2.ToString());
          }
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenTime100%[Log]")) && p_lst_dicData[index]["FrozenTime100%[Log]"] != string.Empty)
            num3 = Math.Round(clsReportUtill.ConvertToDouble(p_lst_dicData[index]["FrozenTime100%[Log]"]) * 0.8, 2);
          else if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperature[Plot]")))
            num3 = Math.Round(clsReportUtill.ConvertToDouble(p_lst_dicData[index]["TimeToReachEjectionTemperature[Plot]"]), 2);
          num3 = num3 - num1 - num2 <= 0.0 ? 0.0 : Math.Round(num3 - num1 - num2, 2);
          case2Slide12.Add("Cooling Time[Case" + (object) (index + 1) + "]", num3.ToString());
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Schizonepeta[User]")))
            case2Slide12.Add("Schizonepeta[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["Schizonepeta[User]"]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetCase2Slide12):" + ex.Message));
      }
      return case2Slide12;
    }

    private Dictionary<string, string> GetCase2Slide13(
      List<Dictionary<string, string>> p_lst_dicData)
    {
      Dictionary<string, string> case2Slide13 = new Dictionary<string, string>();
      try
      {
        for (int index = 0; index < p_lst_dicData.Count; ++index)
        {
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll1_BestFit[IMG]")))
            case2Slide13.Add("Image1[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefAll1_BestFit[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll2_BestFit[IMG]")))
            case2Slide13.Add("Image2[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefAll2_BestFit[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll3_BestFit[IMG]")))
            case2Slide13.Add("Image3[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefAll3_BestFit[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll_BestFit[Plot]")))
            case2Slide13.Add("DefAll[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefAll_BestFit[Plot]"]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetCase2Slide13):" + ex.Message));
      }
      return case2Slide13;
    }

    private Dictionary<string, string> GetCase2Slide14(
      List<Dictionary<string, string>> p_lst_dicData)
    {
      Dictionary<string, string> case2Slide14 = new Dictionary<string, string>();
      try
      {
        for (int index = 0; index < p_lst_dicData.Count; ++index)
        {
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll1_AnchorPlan[IMG]")))
            case2Slide14.Add("Image1[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefAll1_AnchorPlan[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll2_AnchorPlan[IMG]")))
            case2Slide14.Add("Image2[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefAll2_AnchorPlan[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll3_AnchorPlan[IMG]")))
            case2Slide14.Add("Image3[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefAll3_AnchorPlan[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll_AnchorPlan[Plot]")))
            case2Slide14.Add("DefAll[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefAll_AnchorPlan[Plot]"]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetCase2Slide14):" + ex.Message));
      }
      return case2Slide14;
    }

    private Dictionary<string, string> GetCase2Slide15(
      List<Dictionary<string, string>> p_lst_dicData)
    {
      Dictionary<string, string> case2Slide15 = new Dictionary<string, string>();
      try
      {
        for (int index = 0; index < p_lst_dicData.Count; ++index)
        {
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX1_BestFit[IMG]")))
            case2Slide15.Add("Image1[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefX1_BestFit[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX2_BestFit[IMG]")))
            case2Slide15.Add("Image2[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefX2_BestFit[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX3_BestFit[IMG]")))
            case2Slide15.Add("Image3[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefX3_BestFit[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX_BestFit[Plot]")))
            case2Slide15.Add("DefX[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefX_BestFit[Plot]"]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetCase2Slide15):" + ex.Message));
      }
      return case2Slide15;
    }

    private Dictionary<string, string> GetCase2Slide16(
      List<Dictionary<string, string>> p_lst_dicData)
    {
      Dictionary<string, string> case2Slide16 = new Dictionary<string, string>();
      try
      {
        for (int index = 0; index < p_lst_dicData.Count; ++index)
        {
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX1_AnchorPlan[IMG]")))
            case2Slide16.Add("Image1[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefX1_AnchorPlan[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX2_AnchorPlan[IMG]")))
            case2Slide16.Add("Image2[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefX2_AnchorPlan[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX3_AnchorPlan[IMG]")))
            case2Slide16.Add("Image3[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefX3_AnchorPlan[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX_AnchorPlan[Plot]")))
            case2Slide16.Add("DefX[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefX_AnchorPlan[Plot]"]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetCase2Slide16):" + ex.Message));
      }
      return case2Slide16;
    }

    private Dictionary<string, string> GetCase2Slide17(
      List<Dictionary<string, string>> p_lst_dicData)
    {
      Dictionary<string, string> case2Slide17 = new Dictionary<string, string>();
      try
      {
        for (int index = 0; index < p_lst_dicData.Count; ++index)
        {
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY1_BestFit[IMG]")))
            case2Slide17.Add("Image1[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefY1_BestFit[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY2_BestFit[IMG]")))
            case2Slide17.Add("Image2[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefY2_BestFit[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY3_BestFit[IMG]")))
            case2Slide17.Add("Image3[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefY3_BestFit[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY_BestFit[Plot]")))
            case2Slide17.Add("DefY[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefY_BestFit[Plot]"]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetCase2Slide17):" + ex.Message));
      }
      return case2Slide17;
    }

    private Dictionary<string, string> GetCase2Slide18(
      List<Dictionary<string, string>> p_lst_dicData)
    {
      Dictionary<string, string> case2Slide18 = new Dictionary<string, string>();
      try
      {
        for (int index = 0; index < p_lst_dicData.Count; ++index)
        {
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY1_AnchorPlan[IMG]")))
            case2Slide18.Add("Image1[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefY1_AnchorPlan[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY2_AnchorPlan[IMG]")))
            case2Slide18.Add("Image2[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefY2_AnchorPlan[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY3_AnchorPlan[IMG]")))
            case2Slide18.Add("Image3[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefY3_AnchorPlan[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY_AnchorPlan[Plot]")))
            case2Slide18.Add("DefY[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefY_AnchorPlan[Plot]"]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetCase2Slide20):" + ex.Message));
      }
      return case2Slide18;
    }

    private Dictionary<string, string> GetCase2Slide19(
      List<Dictionary<string, string>> p_lst_dicData)
    {
      Dictionary<string, string> case2Slide19 = new Dictionary<string, string>();
      try
      {
        for (int index = 0; index < p_lst_dicData.Count; ++index)
        {
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ1_BestFit[IMG]")))
            case2Slide19.Add("Image1[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefZ1_BestFit[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ2_BestFit[IMG]")))
            case2Slide19.Add("Image2[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefZ2_BestFit[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ3_BestFit[IMG]")))
            case2Slide19.Add("Image3[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefZ3_BestFit[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ_BestFit[Plot]")))
            case2Slide19.Add("DefZ[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefZ_BestFit[Plot]"]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetCase2Slide21):" + ex.Message));
      }
      return case2Slide19;
    }

    private Dictionary<string, string> GetCase2Slide20(
      List<Dictionary<string, string>> p_lst_dicData)
    {
      Dictionary<string, string> case2Slide20 = new Dictionary<string, string>();
      try
      {
        for (int index = 0; index < p_lst_dicData.Count; ++index)
        {
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ1_AnchorPlan[IMG]")))
            case2Slide20.Add("Image1[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefZ1_AnchorPlan[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ2_AnchorPlan[IMG]")))
            case2Slide20.Add("Image2[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefZ2_AnchorPlan[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ3_AnchorPlan[IMG]")))
            case2Slide20.Add("Image3[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefZ3_AnchorPlan[IMG]"]);
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ_AnchorPlan[Plot]")))
            case2Slide20.Add("DefZ[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["DefZ_AnchorPlan[Plot]"]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetCase2Slide22):" + ex.Message));
      }
      return case2Slide20;
    }

    private Dictionary<string, string> GetCase2Slide21(
      List<Dictionary<string, string>> p_lst_dicData)
    {
      string empty = string.Empty;
      string str = "|";
      StringBuilder stringBuilder = new StringBuilder();
      Dictionary<string, string> case2Slide21 = new Dictionary<string, string>();
      try
      {
        for (int index = 0; index < p_lst_dicData.Count; ++index)
        {
          case2Slide21.Add("Item[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["Item[User]"]);
          stringBuilder.Clear();
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperatureCavity[Log]")))
          {
            if (p_lst_dicData[index]["CircuitCoolantTemperatureCavity[Log]"] == null || p_lst_dicData[index]["CircuitCoolantTemperatureCavity[Log]"] == "")
            {
              stringBuilder.Append("||");
            }
            else
            {
              double num = clsReportUtill.ConvertToDouble(p_lst_dicData[index]["CircuitCoolantTemperatureCavity[Log]"].Split('|')[2]);
              stringBuilder.Append(num);
              if (num <= 3.0)
                stringBuilder.Append("|NO Problem|OK");
              else
                stringBuilder.Append("|Discussion|NG");
            }
            case2Slide21.Add("Circuit Coolant Temperature(Cavity)[Case" + (object) (index + 1) + "]", stringBuilder.ToString());
          }
          stringBuilder.Clear();
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperatureCore[Log]")))
          {
            if (p_lst_dicData[index]["CircuitCoolantTemperatureCore[Log]"] == null || p_lst_dicData[index]["CircuitCoolantTemperatureCore[Log]"] == "")
            {
              stringBuilder.Append("||");
            }
            else
            {
              double num = clsReportUtill.ConvertToDouble(p_lst_dicData[index]["CircuitCoolantTemperatureCore[Log]"].Split('|')[2]);
              stringBuilder.Append(num);
              if (num <= 3.0)
                stringBuilder.Append("|NO Problem|OK");
              else
                stringBuilder.Append("|Discussion|NG");
            }
            case2Slide21.Add("Circuit Coolant Temperature(Core)[Case" + (object) (index + 1) + "]", stringBuilder.ToString());
          }
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cooling[User]")))
          {
            string[] strArray = p_lst_dicData[index]["Cooling[User]"].Split('|');
            str = (!(strArray[0] == "OK") ? (!(strArray[0] == "Discussion") ? "Problem" : "Discussion") : "NO Problem") + "|" + strArray[0];
          }
          stringBuilder.Clear();
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitFlowRate[Plot]")) && p_lst_dicData[index]["CircuitFlowRate[Plot]"] != null && p_lst_dicData[index]["CircuitFlowRate[Plot]"] != "")
            stringBuilder.Append(p_lst_dicData[index]["CircuitFlowRate[Plot]"]);
          stringBuilder.Append("|");
          stringBuilder.Append(str);
          case2Slide21.Add("Circuit Flow Rate[Case" + (object) (index + 1) + "]", stringBuilder.ToString());
          stringBuilder.Clear();
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitPressure[Plot]")) && p_lst_dicData[index]["CircuitPressure[Plot]"] != null && p_lst_dicData[index]["CircuitPressure[Plot]"] != "")
            stringBuilder.Append(p_lst_dicData[index]["CircuitPressure[Plot]"]);
          stringBuilder.Append("|");
          stringBuilder.Append(str);
          case2Slide21.Add("Circuit Pressure[Case" + (object) (index + 1) + "]", stringBuilder.ToString());
          stringBuilder.Clear();
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperature[Plot]")))
          {
            if (p_lst_dicData[index]["TimeToReachEjectionTemperature[Plot]"] == null || p_lst_dicData[index]["TimeToReachEjectionTemperature[Plot]"] == "")
            {
              stringBuilder.Append("||");
            }
            else
            {
              double num1 = clsReportUtill.ConvertToDouble(p_lst_dicData[index]["TimeToReachEjectionTemperature[Plot]"]);
              stringBuilder.Append(num1);
              double num2 = !(p_lst_dicData[index]["FrozenTime95%[Log]"] == string.Empty) ? clsReportUtill.ConvertToDouble(p_lst_dicData[index]["FrozenTime95%[Log]"]) : num1;
              if (num1 >= num2)
                stringBuilder.Append("|NO Problem|OK");
              else
                stringBuilder.Append("|Discussion|NG");
            }
            case2Slide21.Add("Time To Reach Ejection Temperature[Case" + (object) (index + 1) + "]", stringBuilder.ToString());
          }
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureMold[Plot]")))
          {
            if (p_lst_dicData[index]["TemperatureMold[Plot]"] == null || p_lst_dicData[index]["TemperatureMold[Plot]"] == "")
              case2Slide21.Add("Temperature, Mold[Case" + (object) (index + 1) + "]", "||");
            else
              case2Slide21.Add("Temperature, Mold[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["TemperatureMold[Plot]"].Split('|')[1] + "||");
          }
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperaturePart[Plot]")))
          {
            if (p_lst_dicData[index]["TemperaturePart[Plot]"] == null || p_lst_dicData[index]["TemperaturePart[Plot]"] == "")
              case2Slide21.Add("Temperature, Part[Case" + (object) (index + 1) + "]", "||");
            else
              case2Slide21.Add("Temperature, Part[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["TemperaturePart[Plot]"].Split('|')[1] + "||");
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetCase2Slide21):" + ex.Message));
      }
      return case2Slide21;
    }

    private Dictionary<string, string> GetCase2Slide22(
      List<Dictionary<string, string>> p_lst_dicData)
    {
      StringBuilder stringBuilder = new StringBuilder();
      Dictionary<string, string> case2Slide22 = new Dictionary<string, string>();
      try
      {
        for (int index = 0; index < p_lst_dicData.Count; ++index)
        {
          case2Slide22.Add("Item[Case" + (object) (index + 1) + "]", p_lst_dicData[index]["Item[User]"]);
          string str1 = "";
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "LastFillingTime[Log]")))
            str1 = p_lst_dicData[index]["LastFillingTime[Log]"];
          string[] strArray1 = str1.Split(new string[1]
          {
            "|"
          }, StringSplitOptions.RemoveEmptyEntries);
          stringBuilder.Clear();
          stringBuilder.Append(strArray1[0]);
          stringBuilder.Append("|");
          if (strArray1.Length > 1)
            stringBuilder.Append(strArray1[1]);
          stringBuilder.Append("|");
          case2Slide22.Add("Balance[Case" + (object) (index + 1) + "]", stringBuilder.ToString());
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SpruePressure[Log]")))
          {
            double num = clsReportUtill.ConvertToDouble(p_lst_dicData[index]["SpruePressure[Log]"]);
            case2Slide22.Add("Pressure[Case" + (object) (index + 1) + "]", num.ToString() + "|" + (object) Math.Round(num / 0.8, 2) + "|");
          }
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ClampForce[Log]")))
          {
            double num = clsReportUtill.ConvertToDouble(p_lst_dicData[index]["ClampForce[Log]"]);
            case2Slide22.Add("Clamp Force[Case" + (object) (index + 1) + "]", num.ToString() + "|" + (object) Math.Round(num / 0.8, 2) + "|");
          }
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "WeldLine[User]")))
          {
            string[] strArray2 = p_lst_dicData[index]["WeldLine[User]"].Split('|');
            string str2 = !(strArray2[0] == "OK") ? (!(strArray2[0] == "Discussion") ? "Problem" : "Discussion") : "NO Problem";
            case2Slide22.Add("Weld Line[Case" + (object) (index + 1) + "]", strArray2[1] + " |" + str2 + "|" + strArray2[0]);
          }
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SinkMark[User]")))
          {
            string[] strArray3 = p_lst_dicData[index]["SinkMark[User]"].Split('|');
            string str3 = !(strArray3[0] == "OK") ? (!(strArray3[0] == "Discussion") ? "Problem" : "Discussion") : "NO Problem";
            case2Slide22.Add("Sink Mark[Case" + (object) (index + 1) + "]", strArray3[1] + "|" + str3 + "|" + strArray3[0]);
          }
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "AirTrap[User]")))
          {
            string[] strArray4 = p_lst_dicData[index]["AirTrap[User]"].Split('|');
            string str4 = !(strArray4[0] == "OK") ? (!(strArray4[0] == "Discussion") ? "Problem" : "Discussion") : "NO Problem";
            case2Slide22.Add("Air Trap[Case" + (object) (index + 1) + "]", strArray4[1] + "|" + str4 + "|" + strArray4[0]);
          }
          stringBuilder.Clear();
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll_BestFit[Plot]")))
            stringBuilder.Append(p_lst_dicData[index]["DefAll_BestFit[Plot]"]);
          stringBuilder.Append("/");
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll_AnchorPlan[Plot]")))
            stringBuilder.Append(p_lst_dicData[index]["DefAll_AnchorPlan[Plot]"]);
          if (stringBuilder.Length > 0)
            case2Slide22.Add("Deflection All[Case" + (object) (index + 1) + "]", stringBuilder.ToString());
          stringBuilder.Clear();
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX_BestFit[Plot]")))
            stringBuilder.Append(p_lst_dicData[index]["DefX_BestFit[Plot]"]);
          stringBuilder.Append("/");
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX_AnchorPlan[Plot]")))
            stringBuilder.Append(p_lst_dicData[index]["DefX_AnchorPlan[Plot]"]);
          if (stringBuilder.Length > 0)
            case2Slide22.Add("Deflection X[Case" + (object) (index + 1) + "]", stringBuilder.ToString());
          stringBuilder.Clear();
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY_BestFit[Plot]")))
            stringBuilder.Append(p_lst_dicData[index]["DefY_BestFit[Plot]"]);
          stringBuilder.Append("/");
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY_AnchorPlan[Plot]")))
            stringBuilder.Append(p_lst_dicData[index]["DefY_AnchorPlan[Plot]"]);
          if (stringBuilder.Length > 0)
            case2Slide22.Add("Deflection Y[Case" + (object) (index + 1) + "]", stringBuilder.ToString());
          stringBuilder.Clear();
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ_BestFit[Plot]")))
            stringBuilder.Append(p_lst_dicData[index]["DefZ_BestFit[Plot]"]);
          stringBuilder.Append("/");
          if (p_lst_dicData[index].Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ_AnchorPlan[Plot]")))
            stringBuilder.Append(p_lst_dicData[index]["DefZ_AnchorPlan[Plot]"]);
          if (stringBuilder.Length > 0)
            case2Slide22.Add("Deflection Z[Case" + (object) (index + 1) + "]", stringBuilder.ToString());
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetCase2Slide22):" + ex.Message));
      }
      return case2Slide22;
    }

    private void SetCase2AllSlide(
      List<Slide> p_lst_slAll,
      List<Dictionary<string, string>> p_lst_dicData)
    {
      foreach (Slide p_slData in p_lst_slAll)
      {
        switch (p_slData.Name)
        {
          case "Conditions":
            this.SetCase2Slide2(p_slData, this.GetCase2Slide2(p_lst_dicData));
            continue;
          case "Conditions_1":
            this.SetCase2Slide3(p_slData, this.GetCase2Slide3(p_lst_dicData));
            continue;
          case "Conditions_2":
            this.SetCase2Slide4(p_slData, this.GetCase2Slide4(p_lst_dicData));
            continue;
          case "Result1":
            this.SetCase2Slide5(p_slData, this.GetCase2Slide5(p_lst_dicData));
            continue;
          case "Result10":
            this.SetCase2Slide21(p_slData, this.GetCase2Slide21(p_lst_dicData));
            continue;
          case "Result10_1":
            this.SetCase2Slide22(p_slData, this.GetCase2Slide22(p_lst_dicData));
            continue;
          case "Result2":
            this.SetCase2Slide6(p_slData, this.GetCase2Slide6(p_lst_dicData));
            continue;
          case "Result3":
            this.SetCase2Slide7(p_slData, this.GetCase2Slide7(p_lst_dicData));
            continue;
          case "Result4":
            this.SetCase2Slide8(p_slData, this.GetCase2Slide8(p_lst_dicData));
            continue;
          case "Result5":
            this.SetCase2Slide9(p_slData, this.GetCase2Slide9(p_lst_dicData));
            continue;
          case "Result6":
            this.SetCase2Slide10(p_slData, this.GetCase2Slide10(p_lst_dicData));
            continue;
          case "Result7":
            this.SetCase2Slide11(p_slData, this.GetCase2Slide11(p_lst_dicData));
            continue;
          case "Result8":
            this.SetCase2Slide12(p_slData, this.GetCase2Slide12(p_lst_dicData));
            continue;
          case "Result9":
            this.SetCase2Slide13(p_slData, this.GetCase2Slide13(p_lst_dicData));
            continue;
          case "Result9_1":
            this.SetCase2Slide14(p_slData, this.GetCase2Slide14(p_lst_dicData));
            continue;
          case "Result9_2":
            this.SetCase2Slide15(p_slData, this.GetCase2Slide15(p_lst_dicData));
            continue;
          case "Result9_3":
            this.SetCase2Slide16(p_slData, this.GetCase2Slide16(p_lst_dicData));
            continue;
          case "Result9_4":
            this.SetCase2Slide17(p_slData, this.GetCase2Slide17(p_lst_dicData));
            continue;
          case "Result9_5":
            this.SetCase2Slide18(p_slData, this.GetCase2Slide18(p_lst_dicData));
            continue;
          case "Result9_6":
            this.SetCase2Slide19(p_slData, this.GetCase2Slide19(p_lst_dicData));
            continue;
          case "Result9_7":
            this.SetCase2Slide20(p_slData, this.GetCase2Slide20(p_lst_dicData));
            continue;
          case "Title":
            this.SetCase2Slide1(p_slData, this.GetCase2Slide1(p_lst_dicData));
            continue;
          default:
            continue;
        }
      }
    }

    private void SetCase2Slide1(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S1_Title 1")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName + " 해석결과");
          textRange.Font.Bold = MsoTriState.msoTrue;
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S1_Table 1")).FirstOrDefault<Shape>().Table;
        if (table != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ReportType[Case1]")))
        {
          int num = clsReportUtill.ConvertToInt(p_dicSData["ReportType[Case1]"]);
          for (int index = 0; index < 3; ++index)
          {
            string NewText = index != num ? "○" : "●";
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = table.Rows[index + 1].Cells[1].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter(NewText);
          }
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S1_TextBox 1")).FirstOrDefault<Shape>();
        if (shape2 != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Date[Case1]")) && p_dicSData["Date[Case1]"] != string.Empty)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape2.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(p_dicSData["Date[Case1]"]);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape3 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S1_TextBox 2")).FirstOrDefault<Shape>();
        if (shape3 == null || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Engineer[Case1]")) || !(p_dicSData["Engineer[Case1]"] != string.Empty))
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape3.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange1.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange1.InsertAfter(p_dicSData["Engineer[Case1]"]);
        textRange1.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetCase2Slide1):" + ex.Message));
      }
    }

    private void SetCase2Slide2(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string empty1 = string.Empty;
      string strFigure = string.Empty;
      string empty2 = string.Empty;
      string empty3 = string.Empty;
      string empty4 = string.Empty;
      string empty5 = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S2_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName.ToString());
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        for (int i = 0; i < 2; i++)
        {
          strFigure = i != 0 ? "S2_Table 3" : "S2_Table 1";
          // ISSUE: variable of a compiler-generated type
          Table table1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>().Table;
          if (table1 != null)
          {
            // ISSUE: variable of a compiler-generated type
            TextRange textRange1 = table1.Rows[1].Cells[3].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange1.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Manufacturer[Case" + (object) (i + 1) + "]")))
            {
              // ISSUE: reference to a compiler-generated method
              textRange1.InsertAfter(p_dicSData["Manufacturer[Case" + (object) (i + 1) + "]"]);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = table1.Rows[2].Cells[3].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange2.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Trade Name[Case" + (object) (i + 1) + "]")))
            {
              // ISSUE: reference to a compiler-generated method
              textRange2.InsertAfter(p_dicSData["Trade Name[Case" + (object) (i + 1) + "]"]);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange3 = table1.Rows[3].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange3.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Family Abbreviation[Case" + (object) (i + 1) + "]")))
            {
              // ISSUE: reference to a compiler-generated method
              textRange3.InsertAfter(p_dicSData["Family Abbreviation[Case" + (object) (i + 1) + "]"]);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange4 = table1.Rows[3].Cells[4].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange4.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Transition Temp[Case" + (object) (i + 1) + "]")))
            {
              // ISSUE: reference to a compiler-generated method
              textRange4.InsertAfter(p_dicSData["Transition Temp[Case" + (object) (i + 1) + "]"] + "℃");
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange5 = table1.Rows[4].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange5.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MI[Case" + (object) (i + 1) + "]")))
            {
              string NewText = !(p_dicSData["MI[Case" + (object) (i + 1) + "]"] == string.Empty) ? p_dicSData["MI[Case" + (object) (i + 1) + "]"] : "-";
              // ISSUE: reference to a compiler-generated method
              textRange5.InsertAfter(NewText);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange6 = table1.Rows[4].Cells[4].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange6.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Ejection Temp[Case" + (object) (i + 1) + "]")))
            {
              // ISSUE: reference to a compiler-generated method
              textRange6.InsertAfter(p_dicSData["Ejection Temp[Case" + (object) (i + 1) + "]"] + "℃");
            }
          }
          strFigure = i != 0 ? "S2_Table 4" : "S2_Table 2";
          // ISSUE: variable of a compiler-generated type
          Table table2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>().Table;
          if (table2 != null)
          {
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Flow Rate[Case" + (object) (i + 1) + "]")))
            {
              string[] strArray = p_dicSData["Flow Rate[Case" + (object) (i + 1) + "]"].Replace("\r\n", "").Split(new string[1]
              {
                "|"
              }, StringSplitOptions.RemoveEmptyEntries);
              string NewText1 = string.Empty;
              string NewText2 = string.Empty;
              if (strArray.Length != 0)
              {
                switch (strArray[0])
                {
                  case "Injection Time":
                    NewText1 = "사출시간(Injection time)[s]";
                    NewText2 = strArray[1] + strArray[2];
                    break;
                  case "Flow rate":
                    NewText1 = "사출속도(Injection time)[㎤/s]";
                    NewText2 = strArray[1] + strArray[2];
                    break;
                  case "Relative ram speed profile(Injection)":
                    NewText1 = "사출시간(Injection time)[s]";
                    NewText2 = strArray[1] + strArray[2];
                    break;
                  case "Relative ram speed profile(flow)":
                    NewText1 = "사출속도(Injection time)[㎤/s]";
                    NewText2 = strArray[1] + strArray[2];
                    break;
                  case "Absolute ram speed profile":
                    NewText1 = "다단 사출" + Environment.NewLine + "사출조건표 참고";
                    break;
                  case "Automatic":
                    NewText1 = "자동(Automatic)";
                    break;
                }
                // ISSUE: variable of a compiler-generated type
                TextRange textRange7 = table2.Rows[1].Cells[1].Shape.TextFrame.TextRange;
                // ISSUE: reference to a compiler-generated method
                textRange7.Delete();
                // ISSUE: reference to a compiler-generated method
                textRange7.InsertAfter(NewText1);
                if (NewText2 != string.Empty)
                {
                  // ISSUE: variable of a compiler-generated type
                  TextRange textRange8 = table2.Rows[1].Cells[2].Shape.TextFrame.TextRange;
                  // ISSUE: reference to a compiler-generated method
                  textRange8.Delete();
                  // ISSUE: reference to a compiler-generated method
                  textRange8.InsertAfter(NewText2);
                }
              }
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange9 = table2.Rows[2].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange9.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Melt Temp[Case" + (object) (i + 1) + "]")))
            {
              // ISSUE: reference to a compiler-generated method
              textRange9.InsertAfter(p_dicSData["Melt Temp[Case" + (object) (i + 1) + "]"] + "℃");
            }
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cavity Core Temp[Case" + (object) (i + 1) + "]")))
            {
              string[] strArray = p_dicSData["Cavity Core Temp[Case" + (object) (i + 1) + "]"].Split('|');
              // ISSUE: variable of a compiler-generated type
              TextRange textRange10 = table2.Rows[1].Cells[6].Shape.TextFrame.TextRange;
              // ISSUE: reference to a compiler-generated method
              textRange10.Delete();
              // ISSUE: reference to a compiler-generated method
              textRange10.InsertAfter(strArray[0] + "℃");
              // ISSUE: variable of a compiler-generated type
              TextRange textRange11 = table2.Rows[3].Cells[6].Shape.TextFrame.TextRange;
              // ISSUE: reference to a compiler-generated method
              textRange11.Delete();
              // ISSUE: reference to a compiler-generated method
              textRange11.InsertAfter(strArray[1] + "℃");
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange12 = table2.Rows[4].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange12.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "V/P Switch-over[Case" + (object) (i + 1) + "]")))
            {
              // ISSUE: reference to a compiler-generated method
              textRange12.InsertAfter(p_dicSData["V/P Switch-over[Case" + (object) (i + 1) + "]"] + " 충진시");
            }
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Pack/Holding Control[Case" + (object) (i + 1) + "]")))
            {
              string[] strArray1 = p_dicSData["Pack/Holding Control[Case" + (object) (i + 1) + "]"].Replace("\r\n", "").Split('/');
              string[] strArray2 = strArray1[0].Replace("\r\n", "").Split('|');
              string str1 = strArray2[1];
              string str2 = strArray2[3];
              // ISSUE: variable of a compiler-generated type
              TextRange textRange13 = table2.Rows[4].Cells[5].Shape.TextFrame.TextRange;
              // ISSUE: reference to a compiler-generated method
              textRange13.Delete();
              // ISSUE: reference to a compiler-generated method
              textRange13.InsertAfter("압력[" + str2 + "]");
              if (strArray1.Length > 1)
              {
                for (int index = 1; index < strArray1.Length && index <= 3; ++index)
                {
                  string[] strArray3 = strArray1[index].Replace("\r\n", "").Split('|');
                  // ISSUE: variable of a compiler-generated type
                  TextRange textRange14 = table2.Rows[5 + index].Cells[2].Shape.TextFrame.TextRange;
                  // ISSUE: reference to a compiler-generated method
                  textRange14.Delete();
                  // ISSUE: reference to a compiler-generated method
                  textRange14.InsertAfter(strArray3[0] + str1);
                  // ISSUE: variable of a compiler-generated type
                  TextRange textRange15 = table2.Rows[5 + index].Cells[5].Shape.TextFrame.TextRange;
                  // ISSUE: reference to a compiler-generated method
                  textRange15.Delete();
                  // ISSUE: reference to a compiler-generated method
                  textRange15.InsertAfter(strArray3[1] + str2);
                }
              }
            }
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetCase2Slide2):" + ex.Message));
      }
    }

    private void SetCase2Slide3(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      // ISSUE: variable of a compiler-generated type
      TextRange textRange1 = (TextRange) null;
      string strFigure = string.Empty;
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      string empty3 = string.Empty;
      string empty4 = string.Empty;
      string empty5 = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S3_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange2 = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange2.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange2.InsertAfter(this.m_strStudyName.ToString());
          textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        for (int i = 0; i < 2; i++)
        {
          strFigure = i != 0 ? "S3_Table 3" : "S3_Table 1";
          // ISSUE: variable of a compiler-generated type
          Table table1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>().Table;
          if (table1 != null)
          {
            // ISSUE: variable of a compiler-generated type
            TextRange textRange3 = table1.Rows[2].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange3.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Fill Time[Case" + (object) (i + 1) + "]")) && !string.IsNullOrEmpty(p_dicSData["Fill Time[Case" + (object) (i + 1) + "]"]))
            {
              string[] strArray = p_dicSData["Fill Time[Case" + (object) (i + 1) + "]"].Split(new string[1]
              {
                "|"
              }, StringSplitOptions.RemoveEmptyEntries);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange4 = textRange3.InsertAfter(strArray[0] + "s");
              if (strArray.Length > 1)
              {
                // ISSUE: reference to a compiler-generated method
                textRange1 = textRange4.InsertAfter(" + Short shot");
              }
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange5 = table1.Rows[3].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange5.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Pressure[Case" + (object) (i + 1) + "]")))
            {
              // ISSUE: reference to a compiler-generated method
              textRange1 = textRange5.InsertAfter(p_dicSData["Pressure[Case" + (object) (i + 1) + "]"] + "MPa");
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange6 = table1.Rows[4].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange6.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Clamp Force[Case" + (object) (i + 1) + "]")))
            {
              // ISSUE: reference to a compiler-generated method
              textRange1 = textRange6.InsertAfter(p_dicSData["Clamp Force[Case" + (object) (i + 1) + "]"] + "t");
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange7 = table1.Rows[5].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange7.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Last Gate Pressure[Case" + (object) (i + 1) + "]")))
            {
              string NewText = !(p_dicSData["Last Gate Pressure[Case" + (object) (i + 1) + "]"] != string.Empty) ? "-" : p_dicSData["Last Gate Pressure[Case" + (object) (i + 1) + "]"] + "MPa";
              // ISSUE: reference to a compiler-generated method
              textRange7.InsertAfter(NewText);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange8 = table1.Rows[6].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange8.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Temperature At Flow Front[Case" + (object) (i + 1) + "]")))
            {
              string[] strArray = p_dicSData["Temperature At Flow Front[Case" + (object) (i + 1) + "]"].Split('|');
              // ISSUE: reference to a compiler-generated method
              textRange1 = textRange8.InsertAfter(strArray[0] + "℃ ~ " + strArray[1] + "℃");
            }
          }
          strFigure = i != 0 ? "S3_Table 4" : "S3_Table 2";
          // ISSUE: variable of a compiler-generated type
          Table table2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>().Table;
          if (table2 != null)
          {
            // ISSUE: variable of a compiler-generated type
            TextRange textRange9 = table2.Rows[1].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange9.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Element Number[Case" + (object) (i + 1) + "]")))
            {
              // ISSUE: reference to a compiler-generated method
              textRange9.InsertAfter(p_dicSData["Element Number[Case" + (object) (i + 1) + "]"]);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange10 = table2.Rows[2].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange10.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Mesh Type[Case" + (object) (i + 1) + "]")))
            {
              // ISSUE: reference to a compiler-generated method
              textRange10.InsertAfter(p_dicSData["Mesh Type[Case" + (object) (i + 1) + "]"]);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange11 = table2.Rows[3].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange11.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Analysis Sequence[Case" + (object) (i + 1) + "]")))
            {
              // ISSUE: reference to a compiler-generated method
              textRange11.InsertAfter(p_dicSData["Analysis Sequence[Case" + (object) (i + 1) + "]"]);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange12 = table2.Rows[4].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange12.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Part Volume[Case" + (object) (i + 1) + "]")))
            {
              // ISSUE: reference to a compiler-generated method
              textRange12.InsertBefore(p_dicSData["Part Volume[Case" + (object) (i + 1) + "]"]);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange13 = table2.Rows[5].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange13.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Projected Area[Case" + (object) (i + 1) + "]")))
            {
              // ISSUE: reference to a compiler-generated method
              textRange13.InsertBefore(p_dicSData["Projected Area[Case" + (object) (i + 1) + "]"]);
            }
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetCase2Slide3):" + ex.Message));
      }
    }

    private void SetCase2Slide4(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      StringBuilder stringBuilder = new StringBuilder();
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S4_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 18.14173f, 149.6693f, 351.4961f, 291.401581f);
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image[Case2]")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 408.189f, 149.6693f, 351.4961f, 291.401581f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetCase2Slide4):" + ex.Message));
      }
    }

    private void SetCase2Slide5(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string strFigure = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S5_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          Shape shape2 = p_slData.Shapes.AddPicture(p_dicSData["Image[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 157.6063f, 200.9764f, 266.1732f, 177.4488f);
          shape2.Rotation = 270f;
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          Shape shape3 = p_slData.Shapes.AddPicture(p_dicSData["Image[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 548.7874f, 200.9764f, 266.1732f, 177.4488f);
          shape3.Rotation = 270f;
        }
        for (int i = 0; i < 2; i++)
        {
          strFigure = i != 0 ? "S5_Table 2" : "S5_Table 1";
          // ISSUE: variable of a compiler-generated type
          Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>().Table;
          if (table != null)
          {
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Gate Coord[Case" + (object) (i + 1) + "]")))
            {
              string[] strArray1 = p_dicSData["Gate Coord[Case" + (object) (i + 1) + "]"].Split(new string[1]
              {
                "/"
              }, StringSplitOptions.RemoveEmptyEntries);
              for (int index = 0; index < strArray1.Length; ++index)
              {
                string[] strArray2 = strArray1[index].Split('|');
                if (strArray2.Length >= 4)
                {
                  // ISSUE: variable of a compiler-generated type
                  TextRange textRange1 = table.Rows[3 + index].Cells[2].Shape.TextFrame.TextRange;
                  // ISSUE: reference to a compiler-generated method
                  textRange1.Delete();
                  // ISSUE: reference to a compiler-generated method
                  textRange1.InsertAfter(Math.Round(clsReportUtill.ConvertToDouble(strArray2[1]), 1).ToString());
                  // ISSUE: variable of a compiler-generated type
                  TextRange textRange2 = table.Rows[3 + index].Cells[3].Shape.TextFrame.TextRange;
                  // ISSUE: reference to a compiler-generated method
                  textRange2.Delete();
                  // ISSUE: reference to a compiler-generated method
                  textRange2.InsertAfter(Math.Round(clsReportUtill.ConvertToDouble(strArray2[2]), 1).ToString());
                }
              }
            }
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Sprue Coord[Case" + (object) (i + 1) + "]")))
            {
              string[] strArray = p_dicSData["Sprue Coord[Case" + (object) (i + 1) + "]"].Split(new string[1]
              {
                ","
              }, StringSplitOptions.RemoveEmptyEntries);
              if (strArray.Length > 1)
              {
                // ISSUE: variable of a compiler-generated type
                TextRange textRange3 = table.Rows[3].Cells[4].Shape.TextFrame.TextRange;
                // ISSUE: reference to a compiler-generated method
                textRange3.Delete();
                // ISSUE: reference to a compiler-generated method
                textRange3.InsertAfter(strArray[0]);
                // ISSUE: variable of a compiler-generated type
                TextRange textRange4 = table.Rows[3].Cells[5].Shape.TextFrame.TextRange;
                // ISSUE: reference to a compiler-generated method
                textRange4.Delete();
                // ISSUE: reference to a compiler-generated method
                textRange4.InsertAfter(strArray[0]);
              }
            }
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Gate Dimension[Case" + (object) (i + 1) + "]")))
            {
              string[] strArray3 = p_dicSData["Gate Dimension[Case" + (object) (i + 1) + "]"].Split(new string[1]
              {
                "/"
              }, StringSplitOptions.RemoveEmptyEntries);
              for (int index = 0; index < strArray3.Length; ++index)
              {
                string[] strArray4 = new string[2]
                {
                  "-",
                  "-"
                };
                string[] strArray5 = strArray3[index].Split('|');
                if (strArray5[1] == "Pin")
                {
                  // ISSUE: reference to a compiler-generated method
                  table.Rows[3 + i].Cells[6].Merge(table.Rows[3 + i].Cells[7]);
                  // ISSUE: variable of a compiler-generated type
                  TextRange textRange = table.Rows[3 + i].Cells[7].Shape.TextFrame.TextRange;
                  // ISSUE: reference to a compiler-generated method
                  textRange.Delete();
                  // ISSUE: reference to a compiler-generated method
                  textRange.InsertAfter("다이렉트게이트");
                }
                else
                {
                  if (strArray5.Length > 2)
                  {
                    strArray4[0] = strArray5[2] + "mm";
                    strArray4[1] = strArray5[3] + "mm";
                  }
                  else
                  {
                    strArray4[0] = strArray5[2] + "mm";
                    strArray4[1] = "Circle";
                  }
                  // ISSUE: variable of a compiler-generated type
                  TextRange textRange5 = table.Rows[3 + index].Cells[6].Shape.TextFrame.TextRange;
                  // ISSUE: reference to a compiler-generated method
                  textRange5.Delete();
                  // ISSUE: reference to a compiler-generated method
                  textRange5.InsertAfter(strArray4[0]);
                  // ISSUE: variable of a compiler-generated type
                  TextRange textRange6 = table.Rows[3 + index].Cells[7].Shape.TextFrame.TextRange;
                  // ISSUE: reference to a compiler-generated method
                  textRange6.Delete();
                  // ISSUE: reference to a compiler-generated method
                  textRange6.InsertAfter(strArray4[1]);
                }
              }
            }
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetCase2Slide5):" + ex.Message));
      }
    }

    private void SetCase2Slide6(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string strFigure = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S6_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 116.22f, 85.0394f, 141.1654f, 91.55906f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 116.22f, 197.0079f, 141.1654f, 91.55906f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 116.22f, 308.6929f, 141.1654f, 91.55906f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image4[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image4[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 116.22f, 420.6614f, 141.1654f, 91.55906f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Animation[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddOLEObject(314.6457f, 59.24409f, 69.73228f, 43.65354f, FileName: p_dicSData["Animation[Case1]"]);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 500.0315f, 85.0394f, 141.1654f, 91.55906f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 500.0315f, 197.0079f, 141.1654f, 91.55906f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 500.0315f, 308.6929f, 141.1654f, 91.55906f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image4[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image4[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 500.0315f, 420.6614f, 141.1654f, 91.55906f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Animation[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddOLEObject(704.9764f, 59.24409f, 69.73228f, 43.65354f, FileName: p_dicSData["Animation[Case2]"]);
        }
        for (int i = 0; i < 2; i++)
        {
          for (int j = 0; j < 4; j++)
          {
            // ISSUE: variable of a compiler-generated type
            Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S6_Rectangle " + (object) (j + 1 + i * 4))).FirstOrDefault<Shape>();
            if (shape2 != null)
            {
              // ISSUE: reference to a compiler-generated method
              shape2.ZOrder(MsoZOrderCmd.msoBringToFront);
              // ISSUE: variable of a compiler-generated type
              TextRange textRange = shape2.TextFrame.TextRange;
              // ISSUE: reference to a compiler-generated method
              textRange.Delete();
              if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Time" + (object) (j + 1) + "[Case" + (object) (i + 1) + "]")))
              {
                // ISSUE: reference to a compiler-generated method
                textRange.InsertAfter("Filling : " + p_dicSData["Time" + (object) (j + 1) + "[Case" + (object) (i + 1) + "]"] + "s");
              }
            }
          }
          // ISSUE: variable of a compiler-generated type
          Shape shape3 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S6_TextBox 1")).FirstOrDefault<Shape>();
          if (shape3 != null)
          {
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = shape3.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Last Filling Time[Case" + (object) (i + 1) + "]")))
            {
              string[] strArray = p_dicSData["Last Filling Time[Case" + (object) (i + 1) + "]"].Split(new string[1]
              {
                "/"
              }, StringSplitOptions.RemoveEmptyEntries);
              // ISSUE: reference to a compiler-generated method
              textRange.InsertAfter("* Fill Time: " + strArray[0] + "s");
            }
          }
          strFigure = i != 0 ? "S6_Table 2" : "S6_Table 1";
          // ISSUE: variable of a compiler-generated type
          Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>().Table;
          if (table != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Gate Open Time[Case" + (object) (i + 1) + "]")))
          {
            string[] strArray1 = p_dicSData["Gate Open Time[Case" + (object) (i + 1) + "]"].Split(new string[1]
            {
              "/"
            }, StringSplitOptions.RemoveEmptyEntries);
            for (int index = 0; index < strArray1.Length; ++index)
            {
              string[] strArray2 = strArray1[index].Split('|');
              // ISSUE: variable of a compiler-generated type
              TextRange textRange = table.Rows[2 + index].Cells[2].Shape.TextFrame.TextRange;
              // ISSUE: reference to a compiler-generated method
              textRange.Delete();
              if (strArray2.Length >= 2)
              {
                // ISSUE: reference to a compiler-generated method
                textRange.InsertAfter(strArray2[1] + "s");
              }
            }
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetCase2Slide6):" + ex.Message));
      }
    }

    private void SetCase2Slide7(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string strFigure = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S7_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 61.79528f, 68.88189f, 306.992126f, 214.2992f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 32.0315f, 323.1496f, 185.1024f, 134.0787f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 432.566925f, 68.88189f, 306.992126f, 214.2992f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 402.803162f, 323.1496f, 185.1024f, 134.0787f);
        }
        for (int i = 0; i < 2; i++)
        {
          strFigure = i != 0 ? "S7_TextBox 2" : "S7_TextBox 1";
          // ISSUE: variable of a compiler-generated type
          Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>();
          if (shape2 != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Weld Line[Case" + (object) (i + 1) + "]")))
          {
            string[] strArray = p_dicSData["Weld Line[Case" + (object) (i + 1) + "]"].Split('|');
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = shape2.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter(strArray[0] + Environment.NewLine + strArray[1]);
            textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetCase2Slide7):" + ex.Message));
      }
    }

    private void SetCase2Slide8(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string strFigure = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S8_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 67.1811f, 81.92126f, 303.3071f, 170.079f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 48.75591f, 295.0866f, 258.5197f, 157.8898f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 429.165344f, 81.92126f, 303.3071f, 170.079f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 410.740173f, 295.0866f, 258.5197f, 157.8898f);
        }
        for (int i = 0; i < 2; i++)
        {
          strFigure = i != 0 ? "S8_Rectangle 4" : "S8_Rectangle 1";
          // ISSUE: variable of a compiler-generated type
          Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>();
          if (shape2 != null)
          {
            // ISSUE: reference to a compiler-generated method
            shape2.ZOrder(MsoZOrderCmd.msoBringToFront);
            // ISSUE: variable of a compiler-generated type
            TextRange textRange1 = shape2.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange1.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Sink Mark1[Case" + (object) (i + 1) + "]")))
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange2 = textRange1.InsertAfter("Scale : " + p_dicSData["Sink Mark1[Case" + (object) (i + 1) + "]"]);
              textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            }
          }
          strFigure = i != 0 ? "S8_Rectangle 5" : "S8_Rectangle 2";
          // ISSUE: variable of a compiler-generated type
          Shape shape3 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>();
          if (shape3 != null)
          {
            // ISSUE: reference to a compiler-generated method
            shape3.ZOrder(MsoZOrderCmd.msoBringToFront);
            // ISSUE: variable of a compiler-generated type
            TextRange textRange3 = shape3.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange3.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Sink Mark2[Case" + (object) (i + 1) + "]")))
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange4 = textRange3.InsertAfter("Scale : " + p_dicSData["Sink Mark2[Case" + (object) (i + 1) + "]"]);
              textRange4.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            }
          }
          strFigure = i != 0 ? "S8_Rectangle 6" : "S8_Rectangle 3";
          // ISSUE: variable of a compiler-generated type
          Shape shape4 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>();
          if (shape4 != null)
          {
            // ISSUE: reference to a compiler-generated method
            shape4.ZOrder(MsoZOrderCmd.msoBringToFront);
            // ISSUE: variable of a compiler-generated type
            TextRange textRange5 = shape4.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange5.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Sink Mark Comment[Case" + (object) (i + 1) + "]")))
            {
              string[] strArray = p_dicSData["Sink Mark Comment[Case" + (object) (i + 1) + "]"].Split('|');
              // ISSUE: variable of a compiler-generated type
              TextRange textRange6 = shape4.TextFrame.TextRange;
              // ISSUE: reference to a compiler-generated method
              textRange6.Delete();
              // ISSUE: reference to a compiler-generated method
              textRange6.InsertAfter(strArray[0] + Environment.NewLine + strArray[1]);
              textRange6.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetCase2Slide8):" + ex.Message));
      }
    }

    private void SetCase2Slide9(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string strFigure = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S9_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 73.98425f, 87.59055f, 286.2992f, 170.079f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 73.98425f, 297.6378f, 286.2992f, 170.079f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 439.653534f, 87.59055f, 286.2992f, 170.079f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 439.653534f, 297.6378f, 286.2992f, 170.079f);
        }
        for (int i = 0; i < 2; i++)
        {
          strFigure = i != 0 ? "S9_Rectangle 3" : "S9_Rectangle 1";
          // ISSUE: variable of a compiler-generated type
          Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>();
          if (shape2 != null)
          {
            // ISSUE: reference to a compiler-generated method
            shape2.ZOrder(MsoZOrderCmd.msoBringToFront);
            // ISSUE: variable of a compiler-generated type
            TextRange textRange1 = shape2.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange1.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Shrinkage1[Case" + (object) (i + 1) + "]")))
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange2 = textRange1.InsertAfter("Scale : " + p_dicSData["Shrinkage1[Case" + (object) (i + 1) + "]"]);
              textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            }
          }
          strFigure = i != 0 ? "S9_Rectangle 4" : "S9_Rectangle 2";
          // ISSUE: variable of a compiler-generated type
          Shape shape3 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>();
          if (shape3 != null)
          {
            // ISSUE: reference to a compiler-generated method
            shape3.ZOrder(MsoZOrderCmd.msoBringToFront);
            // ISSUE: variable of a compiler-generated type
            TextRange textRange3 = shape3.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange3.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Shrinkage2[Case" + (object) (i + 1) + "]")))
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange4 = textRange3.InsertAfter("Scale : " + p_dicSData["Shrinkage2[Case" + (object) (i + 1) + "]"]);
              textRange4.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            }
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetCase2Slide9):" + ex.Message));
      }
    }

    private void SetCase2Slide10(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string empty = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S10_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 63.77953f, 69.16535f, 172.3465f, 102.8976f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 63.77953f, 181.1339f, 172.3465f, 102.8976f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 63.77953f, 293.385834f, 172.3465f, 102.8976f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image4[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image4[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 63.77953f, 405.6378f, 172.3465f, 102.8976f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 425.7638f, 69.16535f, 172.3465f, 102.8976f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 425.7638f, 181.1339f, 172.3465f, 102.8976f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 425.7638f, 293.385834f, 172.3465f, 102.8976f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image4[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image4[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 425.7638f, 405.6378f, 172.3465f, 102.8976f);
        }
        for (int i = 0; i < 2; i++)
        {
          for (int j = 0; j < 4; j++)
          {
            // ISSUE: variable of a compiler-generated type
            Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S10_Rectangle " + (object) (j + 1 + i * 4))).FirstOrDefault<Shape>();
            if (shape2 != null)
            {
              // ISSUE: reference to a compiler-generated method
              shape2.ZOrder(MsoZOrderCmd.msoBringToFront);
              // ISSUE: variable of a compiler-generated type
              TextRange textRange = shape2.TextFrame.TextRange;
              // ISSUE: reference to a compiler-generated method
              textRange.Delete();
              if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Time" + (object) (j + 1) + "[Case" + (object) (i + 1) + "]")))
              {
                string str = Math.Round(clsReportUtill.ConvertToDouble(p_dicSData["Time" + (object) (j + 1) + "[Case" + (object) (i + 1) + "]"]), 1).ToString();
                // ISSUE: reference to a compiler-generated method
                textRange.InsertAfter("Time " + str + "s");
              }
            }
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetCase2Slide10):" + ex.Message));
      }
    }

    private void SetCase2Slide11(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string strFigure = (string) null;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S11_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 102.047f, 68.88189f, 229.8898f, 146.2677f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 38.55118f, 320.315f, 182.8346f, 119.3386f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 213.4488f, 390.614166f, 182.8346f, 119.3386f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 466.299225f, 68.88189f, 229.8898f, 146.2677f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 403.0866f, 320.315f, 182.8346f, 119.3386f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 577.984253f, 390.614166f, 182.8346f, 119.3386f);
        }
        for (int i = 0; i < 2; i++)
        {
          strFigure = i != 0 ? "Vertical Content Placeholder 2" : "Vertical Content Placeholder 1";
          // ISSUE: variable of a compiler-generated type
          Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>();
          if (shape2 != null)
          {
            // ISSUE: variable of a compiler-generated type
            TextRange textRange1 = shape2.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange1.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Coolant Temperature(Cavity)[Case" + (object) (i + 1) + "]")))
            {
              string[] strArray = p_dicSData["Circuit Coolant Temperature(Cavity)[Case" + (object) (i + 1) + "]"].Split('|');
              // ISSUE: reference to a compiler-generated method
              textRange1.InsertAfter("냉각수(캐비티)의 온도는 ");
              textRange1.Font.Bold = MsoTriState.msoTrue;
              textRange1.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange2 = textRange1.InsertAfter(strArray[0]);
              textRange2.Font.Bold = MsoTriState.msoTrue;
              textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange3 = textRange2.InsertAfter("℃ ~ ");
              textRange3.Font.Bold = MsoTriState.msoTrue;
              textRange3.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange4 = textRange3.InsertAfter(strArray[1]);
              textRange4.Font.Bold = MsoTriState.msoTrue;
              textRange4.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange5 = textRange4.InsertAfter("℃로 편차는 ");
              textRange5.Font.Bold = MsoTriState.msoTrue;
              textRange5.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange6 = textRange5.InsertAfter(strArray[2]);
              textRange6.Font.Bold = MsoTriState.msoTrue;
              textRange6.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              // ISSUE: reference to a compiler-generated method
              textRange1 = textRange6.InsertAfter("℃입니다.");
              textRange1.Font.Bold = MsoTriState.msoTrue;
              textRange1.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            }
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Coolant Temperature(Core)[Case" + (object) (i + 1) + "]")))
            {
              if (textRange1.Text.Count<char>() > 0)
              {
                // ISSUE: reference to a compiler-generated method
                textRange1.InsertAfter(Environment.NewLine);
              }
              string[] strArray = p_dicSData["Circuit Coolant Temperature(Core)[Case" + (object) (i + 1) + "]"].Split('|');
              // ISSUE: reference to a compiler-generated method
              textRange1.InsertAfter("냉각수(코어)의 온도는 ");
              textRange1.Font.Bold = MsoTriState.msoTrue;
              textRange1.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange7 = textRange1.InsertAfter(strArray[0]);
              textRange7.Font.Bold = MsoTriState.msoTrue;
              textRange7.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange8 = textRange7.InsertAfter("℃ ~ ");
              textRange8.Font.Bold = MsoTriState.msoTrue;
              textRange8.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange9 = textRange8.InsertAfter(strArray[1]);
              textRange9.Font.Bold = MsoTriState.msoTrue;
              textRange9.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange10 = textRange9.InsertAfter("℃로 편차는 ");
              textRange10.Font.Bold = MsoTriState.msoTrue;
              textRange10.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange11 = textRange10.InsertAfter(strArray[2]);
              textRange11.Font.Bold = MsoTriState.msoTrue;
              textRange11.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              // ISSUE: reference to a compiler-generated method
              textRange1 = textRange11.InsertAfter("℃입니다.");
              textRange1.Font.Bold = MsoTriState.msoTrue;
              textRange1.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            }
            if (textRange1.Text.Count<char>() > 0)
            {
              // ISSUE: reference to a compiler-generated method
              textRange1.InsertAfter(Environment.NewLine);
            }
            // ISSUE: reference to a compiler-generated method
            textRange1.InsertAfter("이론상 냉각수 온도는 2~3℃ 이내로 관리 되는 것을 추천합니다.");
            textRange1.Font.Bold = MsoTriState.msoTrue;
            textRange1.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          }
          strFigure = i != 0 ? "S11_Rectangle 4" : "S11_Rectangle 2";
          // ISSUE: variable of a compiler-generated type
          Shape shape3 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>();
          if (shape3 != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Temperature Mold[Case" + (object) (i + 1) + "]")))
          {
            // ISSUE: reference to a compiler-generated method
            shape3.ZOrder(MsoZOrderCmd.msoBringToFront);
            string[] strArray = p_dicSData["Temperature Mold[Case" + (object) (i + 1) + "]"].Split('|');
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = shape3.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter(strArray[0] + " ~ " + strArray[1]);
          }
          strFigure = i != 0 ? "S11_Rectangle 3" : "S11_Rectangle 1";
          // ISSUE: variable of a compiler-generated type
          Shape shape4 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>();
          if (shape4 != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cooling[Case" + (object) (i + 1) + "]")))
          {
            string[] strArray = p_dicSData["Cooling[Case" + (object) (i + 1) + "]"].Split('|');
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = shape4.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter(strArray[0] + Environment.NewLine + strArray[1]);
            textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetCase2Slide11):" + ex.Message));
      }
    }

    private void SetCase2Slide12(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string empty = string.Empty;
      string strFigure = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S12_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 99.2126f, 68.88189f, 232.7244f, 138.0472f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 99.2126f, 224.7874f, 232.7244f, 138.0472f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 465.448822f, 68.88189f, 232.7244f, 138.0472f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 465.448822f, 224.7874f, 232.7244f, 138.0472f);
        }
        for (int i = 0; i < 2; i++)
        {
          strFigure = i != 0 ? "S12_Rectangle 3" : "S12_Rectangle 1";
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Time To Reach Ejection Temperature[Case" + (object) (i + 1) + "]")))
            empty = p_dicSData["Time To Reach Ejection Temperature[Case" + (object) (i + 1) + "]"];
          // ISSUE: variable of a compiler-generated type
          Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>();
          if (shape2 != null)
          {
            // ISSUE: reference to a compiler-generated method
            shape2.ZOrder(MsoZOrderCmd.msoBringToFront);
            // ISSUE: variable of a compiler-generated type
            TextRange textRange1 = shape2.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange1.Delete();
            if (empty != string.Empty)
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange2 = textRange1.InsertAfter("취출 가능한 시간 : " + empty + "s");
              textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            }
          }
          strFigure = i != 0 ? "S12_Rectangle 4" : "S12_Rectangle 2";
          // ISSUE: variable of a compiler-generated type
          Shape shape3 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>();
          if (shape3 != null)
          {
            // ISSUE: reference to a compiler-generated method
            shape3.ZOrder(MsoZOrderCmd.msoBringToFront);
            // ISSUE: variable of a compiler-generated type
            TextRange textRange3 = shape3.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange3.Delete();
            if (empty != string.Empty)
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange4 = textRange3.InsertAfter(empty + "s 이후 고화되어야 할 부위");
              textRange4.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            }
          }
          strFigure = i != 0 ? "S12_Table 2" : "S12_Table 1";
          // ISSUE: variable of a compiler-generated type
          Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>().Table;
          if (table != null)
          {
            // ISSUE: variable of a compiler-generated type
            TextRange textRange5 = table.Rows[1].Cells[2].Shape.TextFrame.TextRange;
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Injection Time[Case" + (object) (i + 1) + "]")))
            {
              // ISSUE: reference to a compiler-generated method
              textRange5.InsertAfter(p_dicSData["Injection Time[Case" + (object) (i + 1) + "]"]);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange6 = table.Rows[2].Cells[2].Shape.TextFrame.TextRange;
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Pack/Holding Time[Case" + (object) (i + 1) + "]")))
            {
              // ISSUE: reference to a compiler-generated method
              textRange6.InsertAfter(p_dicSData["Pack/Holding Time[Case" + (object) (i + 1) + "]"]);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange7 = table.Rows[3].Cells[2].Shape.TextFrame.TextRange;
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cooling Time[Case" + (object) (i + 1) + "]")))
            {
              // ISSUE: reference to a compiler-generated method
              textRange7.InsertAfter(p_dicSData["Cooling Time[Case" + (object) (i + 1) + "]"]);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange8 = table.Rows[4].Cells[2].Shape.TextFrame.TextRange;
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Schizonepeta[Case" + (object) (i + 1) + "]")))
            {
              // ISSUE: reference to a compiler-generated method
              textRange8.InsertAfter(p_dicSData["Schizonepeta[Case" + (object) (i + 1) + "]"]);
            }
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetCase2Slide12):" + ex.Message));
      }
    }

    private void SetCase2Slide13(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string empty = string.Empty;
      string strFigure = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S13_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 91.55906f, 72.28346f, 249.1654f, 165.8268f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 40.53543f, 277.5118f, 172.913f, 132.9449f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 218.8346f, 277.5118f, 172.913f, 132.9449f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 458.929138f, 72.28346f, 249.1654f, 165.8268f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 407.905518f, 277.5118f, 172.913f, 132.9449f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 586.2047f, 277.5118f, 172.913f, 132.9449f);
        }
        for (int i = 0; i < 2; i++)
        {
          strFigure = i != 0 ? "S13_Table 2" : "S13_Table 1";
          // ISSUE: variable of a compiler-generated type
          Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>().Table;
          if (table != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll[Case" + (object) (i + 1) + "]")))
          {
            string[] strArray = p_dicSData["DefAll[Case" + (object) (i + 1) + "]"].Split('|');
            // ISSUE: variable of a compiler-generated type
            TextRange textRange1 = table.Rows[1].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange1.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange1.InsertAfter(strArray[1]);
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = table.Rows[2].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange2.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange2.InsertAfter(strArray[0]);
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetCase2Slide13):" + ex.Message));
      }
    }

    private void SetCase2Slide14(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string empty = string.Empty;
      string strFigure = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S14_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 91.55906f, 72.28346f, 249.1654f, 165.8268f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 40.53543f, 277.5118f, 172.913f, 132.9449f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 218.8346f, 277.5118f, 172.913f, 132.9449f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 458.929138f, 72.28346f, 249.1654f, 165.8268f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 407.905518f, 277.5118f, 172.913f, 132.9449f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 586.2047f, 277.5118f, 172.913f, 132.9449f);
        }
        for (int i = 0; i < 2; i++)
        {
          strFigure = i != 0 ? "S14_Table 2" : "S14_Table 1";
          // ISSUE: variable of a compiler-generated type
          Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>().Table;
          if (table != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll[Case" + (object) (i + 1) + "]")))
          {
            string[] strArray = p_dicSData["DefAll[Case" + (object) (i + 1) + "]"].Split('|');
            // ISSUE: variable of a compiler-generated type
            TextRange textRange1 = table.Rows[1].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange1.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange1.InsertAfter(strArray[1]);
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = table.Rows[2].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange2.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange2.InsertAfter(strArray[0]);
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetCase2Slide14):" + ex.Message));
      }
    }

    private void SetCase2Slide15(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string empty = string.Empty;
      string strFigure = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S15_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 91.55906f, 72.28346f, 249.1654f, 165.8268f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 40.53543f, 277.5118f, 172.913f, 132.9449f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 218.8346f, 277.5118f, 172.913f, 132.9449f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 458.929138f, 72.28346f, 249.1654f, 165.8268f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 407.905518f, 277.5118f, 172.913f, 132.9449f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 586.2047f, 277.5118f, 172.913f, 132.9449f);
        }
        for (int i = 0; i < 2; i++)
        {
          strFigure = i != 0 ? "S15_Table 2" : "S15_Table 1";
          // ISSUE: variable of a compiler-generated type
          Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>().Table;
          if (table != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX[Case" + (object) (i + 1) + "]")))
          {
            string[] strArray = p_dicSData["DefX[Case" + (object) (i + 1) + "]"].Split('|');
            // ISSUE: variable of a compiler-generated type
            TextRange textRange1 = table.Rows[1].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange1.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange1.InsertAfter(strArray[1]);
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = table.Rows[2].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange2.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange2.InsertAfter(strArray[0]);
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetCase2Slide15):" + ex.Message));
      }
    }

    private void SetCase2Slide16(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string empty = string.Empty;
      string strFigure = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S16_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 91.55906f, 72.28346f, 249.1654f, 165.8268f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 40.53543f, 277.5118f, 172.913f, 132.9449f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 218.8346f, 277.5118f, 172.913f, 132.9449f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 458.929138f, 72.28346f, 249.1654f, 165.8268f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 407.905518f, 277.5118f, 172.913f, 132.9449f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 586.2047f, 277.5118f, 172.913f, 132.9449f);
        }
        for (int i = 0; i < 2; i++)
        {
          strFigure = i != 0 ? "S16_Table 2" : "S16_Table 1";
          // ISSUE: variable of a compiler-generated type
          Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>().Table;
          if (table != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX[Case" + (object) (i + 1) + "]")))
          {
            string[] strArray = p_dicSData["DefX[Case" + (object) (i + 1) + "]"].Split('|');
            // ISSUE: variable of a compiler-generated type
            TextRange textRange1 = table.Rows[1].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange1.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange1.InsertAfter(strArray[1]);
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = table.Rows[2].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange2.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange2.InsertAfter(strArray[0]);
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetCase2Slide16):" + ex.Message));
      }
    }

    private void SetCase2Slide17(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string empty = string.Empty;
      string strFigure = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S17_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 91.55906f, 72.28346f, 249.1654f, 165.8268f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 40.53543f, 277.5118f, 172.913f, 132.9449f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 218.8346f, 277.5118f, 172.913f, 132.9449f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 458.929138f, 72.28346f, 249.1654f, 165.8268f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 407.905518f, 277.5118f, 172.913f, 132.9449f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 586.2047f, 277.5118f, 172.913f, 132.9449f);
        }
        for (int i = 0; i < 2; i++)
        {
          strFigure = i != 0 ? "S17_Table 2" : "S17_Table 1";
          // ISSUE: variable of a compiler-generated type
          Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>().Table;
          if (table != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY[Case" + (object) (i + 1) + "]")))
          {
            string[] strArray = p_dicSData["DefY[Case" + (object) (i + 1) + "]"].Split('|');
            // ISSUE: variable of a compiler-generated type
            TextRange textRange1 = table.Rows[1].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange1.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange1.InsertAfter(strArray[1]);
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = table.Rows[2].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange2.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange2.InsertAfter(strArray[0]);
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetCase2Slide17):" + ex.Message));
      }
    }

    private void SetCase2Slide18(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string empty = string.Empty;
      string strFigure = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S18_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 91.55906f, 72.28346f, 249.1654f, 165.8268f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 40.53543f, 277.5118f, 172.913f, 132.9449f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 218.8346f, 277.5118f, 172.913f, 132.9449f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 458.929138f, 72.28346f, 249.1654f, 165.8268f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 407.905518f, 277.5118f, 172.913f, 132.9449f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 586.2047f, 277.5118f, 172.913f, 132.9449f);
        }
        for (int i = 0; i < 2; i++)
        {
          strFigure = i != 0 ? "S18_Table 2" : "S18_Table 1";
          // ISSUE: variable of a compiler-generated type
          Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>().Table;
          if (table != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY[Case" + (object) (i + 1) + "]")))
          {
            string[] strArray = p_dicSData["DefY[Case" + (object) (i + 1) + "]"].Split('|');
            // ISSUE: variable of a compiler-generated type
            TextRange textRange1 = table.Rows[1].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange1.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange1.InsertAfter(strArray[1]);
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = table.Rows[2].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange2.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange2.InsertAfter(strArray[0]);
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetCase2Slide18):" + ex.Message));
      }
    }

    private void SetCase2Slide19(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string empty = string.Empty;
      string strFigure = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S19_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 91.55906f, 72.28346f, 249.1654f, 165.8268f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 40.53543f, 277.5118f, 172.913f, 132.9449f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 218.8346f, 277.5118f, 172.913f, 132.9449f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 458.929138f, 72.28346f, 249.1654f, 165.8268f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 407.905518f, 277.5118f, 172.913f, 132.9449f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 586.2047f, 277.5118f, 172.913f, 132.9449f);
        }
        for (int i = 0; i < 2; i++)
        {
          strFigure = i != 0 ? "S19_Table 2" : "S19_Table 1";
          // ISSUE: variable of a compiler-generated type
          Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>().Table;
          if (table != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ[Case" + (object) (i + 1) + "]")))
          {
            string[] strArray = p_dicSData["DefZ[Case" + (object) (i + 1) + "]"].Split('|');
            // ISSUE: variable of a compiler-generated type
            TextRange textRange1 = table.Rows[1].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange1.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange1.InsertAfter(strArray[1]);
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = table.Rows[2].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange2.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange2.InsertAfter(strArray[0]);
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetCase2Slide19):" + ex.Message));
      }
    }

    private void SetCase2Slide20(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string empty = string.Empty;
      string strFigure = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S20_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 40.53543f, 72.28346f, 351.2126f, 165.8268f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 40.53543f, 244.3465f, 172.913f, 165.8268f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3[Case1]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3[Case1]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 218.8346f, 244.3465f, 172.913f, 165.8268f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 407.905518f, 72.28346f, 351.2126f, 165.8268f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 407.905518f, 244.3465f, 172.913f, 165.8268f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3[Case2]")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3[Case2]"], MsoTriState.msoFalse, MsoTriState.msoTrue, 586.2047f, 244.3465f, 172.913f, 165.8268f);
        }
        for (int i = 0; i < 2; i++)
        {
          strFigure = i != 0 ? "S20_Table 2" : "S20_Table 1";
          // ISSUE: variable of a compiler-generated type
          Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == strFigure)).FirstOrDefault<Shape>().Table;
          if (table != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ[Case" + (object) (i + 1) + "]")))
          {
            string[] strArray = p_dicSData["DefZ[Case" + (object) (i + 1) + "]"].Split('|');
            // ISSUE: variable of a compiler-generated type
            TextRange textRange1 = table.Rows[1].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange1.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange1.InsertAfter(strArray[0]);
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = table.Rows[2].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange2.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange2.InsertAfter(strArray[1]);
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetCase2Slide20):" + ex.Message));
      }
    }

    private void SetCase2Slide21(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      // ISSUE: variable of a compiler-generated type
      TextRange textRange1 = (TextRange) null;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S21_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange2 = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange2.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange2.InsertAfter(this.m_strStudyName);
          textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S21_Table 1")).FirstOrDefault<Shape>().Table;
        if (table == null)
          return;
        for (int i = 0; i < 2; i++)
        {
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Item[Case" + (object) (i + 1) + "]")))
            textRange1 = table.Rows[1].Cells[3 + 3 * i].Shape.TextFrame.TextRange;
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Coolant Temperature(Cavity)[Case" + (object) (i + 1) + "]")))
          {
            string[] strArray = p_dicSData["Circuit Coolant Temperature(Cavity)[Case" + (object) (i + 1) + "]"].Split('|');
            // ISSUE: variable of a compiler-generated type
            TextRange textRange3 = table.Rows[3].Cells[3 + 3 * i].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange3.Delete();
            if (strArray[0] != "")
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange4 = textRange3.InsertAfter(strArray[0]);
              textRange4.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange5 = textRange4.InsertAfter("℃");
              textRange5.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            }
            else
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange6 = textRange3.InsertAfter("-");
              textRange6.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange7 = table.Rows[3].Cells[4 + 3 * i].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange7.Delete();
            if (strArray[1] != "")
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange8 = textRange7.InsertAfter(strArray[1]);
              textRange8.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            else
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange9 = textRange7.InsertAfter("-");
              textRange9.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange10 = table.Rows[3].Cells[5 + 3 * i].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange10.Delete();
            if (strArray[2] != "")
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange11 = textRange10.InsertAfter(strArray[2]);
              textRange11.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            else
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange12 = textRange10.InsertAfter("-");
              textRange12.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
          }
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Coolant Temperature(Core)[Case" + (object) (i + 1) + "]")))
          {
            string[] strArray = p_dicSData["Circuit Coolant Temperature(Core)[Case" + (object) (i + 1) + "]"].Split('|');
            // ISSUE: variable of a compiler-generated type
            TextRange textRange13 = table.Rows[4].Cells[3 + 3 * i].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange13.Delete();
            if (strArray[0] != "")
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange14 = textRange13.InsertAfter(strArray[0]);
              textRange14.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange15 = textRange14.InsertAfter("℃");
              textRange15.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            }
            else
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange16 = textRange13.InsertAfter("-");
              textRange16.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange17 = table.Rows[4].Cells[4 + 3 * i].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange17.Delete();
            if (strArray[1] != "")
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange18 = textRange17.InsertAfter(strArray[1]);
              textRange18.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            else
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange19 = textRange17.InsertAfter("-");
              textRange19.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange20 = table.Rows[4].Cells[5 + 3 * i].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange20.Delete();
            if (strArray[2] != "")
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange21 = textRange20.InsertAfter(strArray[2]);
              textRange21.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            else
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange22 = textRange20.InsertAfter("-");
              textRange22.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
          }
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Flow Rate[Case" + (object) (i + 1) + "]")))
          {
            string[] strArray = p_dicSData["Circuit Flow Rate[Case" + (object) (i + 1) + "]"].Split('|');
            // ISSUE: variable of a compiler-generated type
            TextRange textRange23 = table.Rows[5].Cells[3 + 3 * i].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange23.Delete();
            if (strArray[0] != "")
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange24 = textRange23.InsertAfter(strArray[0]);
              textRange24.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange25 = textRange24.InsertAfter("lit/min");
              textRange25.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            }
            else
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange26 = textRange23.InsertAfter("-");
              textRange26.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange27 = table.Rows[5].Cells[4 + 3 * i].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange27.Delete();
            if (strArray[1] != "")
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange28 = textRange27.InsertAfter(strArray[1]);
              textRange28.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            else
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange29 = textRange27.InsertAfter("-");
              textRange29.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange30 = table.Rows[5].Cells[5 + 3 * i].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange30.Delete();
            if (strArray[2] != "")
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange31 = textRange30.InsertAfter(strArray[2]);
              textRange31.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            else
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange32 = textRange30.InsertAfter("-");
              textRange32.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
          }
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Pressure[Case" + (object) (i + 1) + "]")))
          {
            string[] strArray = p_dicSData["Circuit Pressure[Case" + (object) (i + 1) + "]"].Split('|');
            // ISSUE: variable of a compiler-generated type
            TextRange textRange33 = table.Rows[6].Cells[3 + 3 * i].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange33.Delete();
            if (strArray[0] != "")
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange34 = textRange33.InsertAfter(strArray[0]);
              textRange34.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange35 = textRange34.InsertAfter("kPa");
              textRange35.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            }
            else
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange36 = textRange33.InsertAfter("-");
              textRange36.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange37 = table.Rows[6].Cells[4 + 3 * i].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange37.Delete();
            if (strArray[1] != "")
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange38 = textRange37.InsertAfter(strArray[1]);
              textRange38.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            else
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange39 = textRange37.InsertAfter("-");
              textRange39.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange40 = table.Rows[6].Cells[5 + 3 * i].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange40.Delete();
            if (strArray[2] != "")
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange41 = textRange40.InsertAfter(strArray[2]);
              textRange41.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            else
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange42 = textRange40.InsertAfter("-");
              textRange42.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
          }
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Time To Reach Ejection Temperature[Case" + (object) (i + 1) + "]")))
          {
            string[] strArray = p_dicSData["Time To Reach Ejection Temperature[Case" + (object) (i + 1) + "]"].Split('|');
            // ISSUE: variable of a compiler-generated type
            TextRange textRange43 = table.Rows[7].Cells[3 + 3 * i].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange43.Delete();
            if (strArray[0] != "")
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange44 = textRange43.InsertAfter(strArray[0]);
              textRange44.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange45 = textRange44.InsertAfter("s");
              textRange45.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            }
            else
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange46 = textRange43.InsertAfter("-");
              textRange46.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange47 = table.Rows[7].Cells[4 + 3 * i].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange47.Delete();
            if (strArray[1] != "")
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange48 = textRange47.InsertAfter(strArray[1]);
              textRange48.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            else
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange49 = textRange47.InsertAfter("-");
              textRange49.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange50 = table.Rows[7].Cells[5 + 3 * i].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange50.Delete();
            if (strArray[2] != "")
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange51 = textRange50.InsertAfter(strArray[2]);
              textRange51.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            else
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange52 = textRange50.InsertAfter("-");
              textRange52.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
          }
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Temperature, Mold[Case" + (object) (i + 1) + "]")))
          {
            string[] strArray = p_dicSData["Temperature, Mold[Case" + (object) (i + 1) + "]"].Split('|');
            // ISSUE: variable of a compiler-generated type
            TextRange textRange53 = table.Rows[8].Cells[3 + 3 * i].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange53.Delete();
            if (strArray[0] != "")
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange54 = textRange53.InsertAfter(strArray[0]);
              textRange54.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange55 = textRange54.InsertAfter("℃");
              textRange55.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            }
            else
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange56 = textRange53.InsertAfter("-");
              textRange56.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange57 = table.Rows[8].Cells[4 + 3 * i].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange57.Delete();
            if (strArray[1] != "")
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange58 = textRange57.InsertAfter(strArray[1]);
              textRange58.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            else
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange59 = textRange57.InsertAfter("-");
              textRange59.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange60 = table.Rows[8].Cells[5 + 3 * i].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange60.Delete();
            if (strArray[2] != "")
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange61 = textRange60.InsertAfter(strArray[2]);
              textRange61.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            else
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange62 = textRange60.InsertAfter("-");
              textRange62.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
          }
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Temperature, Part[Case" + (object) (i + 1) + "]")))
          {
            string[] strArray = p_dicSData["Temperature, Part[Case" + (object) (i + 1) + "]"].Split('|');
            // ISSUE: variable of a compiler-generated type
            TextRange textRange63 = table.Rows[9].Cells[3 + 3 * i].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange63.Delete();
            if (strArray[0] != "")
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange64 = textRange63.InsertAfter(strArray[0]);
              textRange64.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange65 = textRange64.InsertAfter("℃");
              textRange65.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            }
            else
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange66 = textRange63.InsertAfter("-");
              textRange66.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange67 = table.Rows[9].Cells[4 + 3 * i].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange67.Delete();
            if (strArray[1] != "")
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange68 = textRange67.InsertAfter(strArray[1]);
              textRange68.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            else
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange69 = textRange67.InsertAfter("-");
              textRange69.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange70 = table.Rows[9].Cells[5 + 3 * i].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange70.Delete();
            if (strArray[2] != "")
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange71 = textRange70.InsertAfter(strArray[2]);
              textRange71.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
            else
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange72 = textRange70.InsertAfter("-");
              textRange72.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetCase2Slide21):" + ex.Message));
      }
    }

    private void SetCase2Slide22(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string[] arr_strKey = (string[]) null;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S22_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        for (int i = 0; i < 2; i++)
        {
          // ISSUE: variable of a compiler-generated type
          Table table1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S22_Table 1")).FirstOrDefault<Shape>().Table;
          if (table1 != null)
          {
            arr_strKey = new string[6]
            {
              "Balance",
              "Pressure",
              "Clamp Force",
              "Weld Line",
              "Sink Mark",
              "Air Trap"
            };
            for (int j = 0; j < arr_strKey.Length; j++)
            {
              string[] strArray = new string[3]
              {
                "-",
                "-",
                "-"
              };
              if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == arr_strKey[j] + "[Case" + (object) (i + 1) + "]")))
                strArray = p_dicSData[arr_strKey[j] + "[Case" + (object) (i + 1) + "]"].Split('|');
              if (strArray[1] == "")
                strArray[1] = "-";
              if (strArray[2] == "")
                strArray[2] = "-";
              // ISSUE: variable of a compiler-generated type
              TextRange textRange1 = table1.Rows[j + 3].Cells[3 + i * 3].Shape.TextFrame.TextRange;
              // ISSUE: reference to a compiler-generated method
              textRange1.Delete();
              string NewText1;
              string NewText2;
              if (j == 0)
              {
                NewText1 = strArray[0];
                NewText2 = "s";
              }
              else if (j == 1)
              {
                NewText1 = strArray[1];
                NewText2 = "MPa";
              }
              else if (j == 2)
              {
                NewText1 = strArray[1];
                NewText2 = "t";
              }
              else
              {
                NewText1 = strArray[0];
                NewText2 = "";
              }
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange2 = textRange1.InsertAfter(NewText1);
              textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              if (NewText2 != "")
              {
                // ISSUE: reference to a compiler-generated method
                // ISSUE: variable of a compiler-generated type
                TextRange textRange3 = textRange2.InsertAfter(NewText2);
                textRange3.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
              }
              // ISSUE: variable of a compiler-generated type
              TextRange textRange4 = table1.Rows[j + 3].Cells[4 + i * 3].Shape.TextFrame.TextRange;
              // ISSUE: reference to a compiler-generated method
              textRange4.Delete();
              if (j == 0)
              {
                // ISSUE: reference to a compiler-generated method
                // ISSUE: reference to a compiler-generated method
                // ISSUE: variable of a compiler-generated type
                TextRange textRange5 = !strArray[1].ToLower().Equals("short shot") ? textRange4.InsertAfter(strArray[1]) : textRange4.InsertAfter("미성형");
                textRange5.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              }
              else if (j == 1)
              {
                // ISSUE: reference to a compiler-generated method
                // ISSUE: variable of a compiler-generated type
                TextRange textRange6 = textRange4.InsertAfter(strArray[1]);
                textRange6.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
                // ISSUE: reference to a compiler-generated method
                // ISSUE: variable of a compiler-generated type
                TextRange textRange7 = textRange6.InsertAfter("MPa 이상의 사출기를 사용해야 한다.");
                textRange7.Font.Bold = MsoTriState.msoTrue;
                textRange7.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
              }
              else if (j == 2)
              {
                // ISSUE: reference to a compiler-generated method
                // ISSUE: variable of a compiler-generated type
                TextRange textRange8 = textRange4.InsertAfter(strArray[1]);
                textRange8.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
                // ISSUE: reference to a compiler-generated method
                // ISSUE: variable of a compiler-generated type
                TextRange textRange9 = textRange8.InsertAfter("t 이상의 사출기를 사용해야 한다.");
                textRange9.Font.Bold = MsoTriState.msoTrue;
                textRange9.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
              }
              else
              {
                // ISSUE: reference to a compiler-generated method
                // ISSUE: variable of a compiler-generated type
                TextRange textRange10 = textRange4.InsertAfter(strArray[1]);
                textRange10.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
              }
              // ISSUE: variable of a compiler-generated type
              TextRange textRange11 = table1.Rows[j + 3].Cells[5 + i * 3].Shape.TextFrame.TextRange;
              // ISSUE: reference to a compiler-generated method
              textRange11.Delete();
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange12 = textRange11.InsertAfter(strArray[2]);
              textRange12.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            }
          }
          // ISSUE: variable of a compiler-generated type
          Table table2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S22_Table 2")).FirstOrDefault<Shape>().Table;
          if (table2 != null)
          {
            arr_strKey = new string[4]
            {
              "Deflection All",
              "Deflection X",
              "Deflection Y",
              "Deflection Z"
            };
            for (int j = 0; j < arr_strKey.Length; j++)
            {
              string[] strArray1 = new string[2]{ "", "" };
              if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == arr_strKey[j] + "[Case" + (object) (i + 1) + "]")))
                strArray1 = p_dicSData[arr_strKey[j] + "[Case" + (object) (i + 1) + "]"].Split('/');
              // ISSUE: variable of a compiler-generated type
              TextRange textRange13 = table2.Rows[2].Cells[j * 2 + (2 + i)].Shape.TextFrame.TextRange;
              // ISSUE: reference to a compiler-generated method
              textRange13.Delete();
              if (strArray1[0] != "")
              {
                string[] strArray2 = strArray1[0].Split('|');
                // ISSUE: reference to a compiler-generated method
                textRange13.InsertAfter(strArray2[0] + " ~ " + strArray2[1] + " mm");
                textRange13.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
              }
              // ISSUE: variable of a compiler-generated type
              TextRange textRange14 = table2.Rows[3].Cells[j * 2 + (2 + i)].Shape.TextFrame.TextRange;
              // ISSUE: reference to a compiler-generated method
              textRange14.Delete();
              if (strArray1[1] != "")
              {
                string[] strArray3 = strArray1[1].Split('|');
                // ISSUE: reference to a compiler-generated method
                textRange14.InsertAfter(strArray3[0] + " ~ " + strArray3[1] + " mm");
                textRange14.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
              }
            }
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetCase2Slide22):" + ex.Message));
      }
    }
  }
}
