﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmSummaryView
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDLog4Net;
using HDMFUserControl;
using HDMoldFlow.Properties;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public class frmSummaryView : Form
  {
    private const int WS_EX_COMPOSITED = 33554432;
    private Rectangle m_recDragBoxFromMouseDown;
    private int m_iRowIndexFromMouseDown;
    private int m_iRowIndexOfItemUnderMouseToDrop;
    private IContainer components = (IContainer) null;
    private NewButton newButton_Apply;
    private Panel panel_Packing;
    private DataGridView dataGridView_SummaryView;
    private Label label_SummaryView;
    private DataGridViewTextBoxColumn Column_Section;
    private DataGridViewCheckBoxColumn Column_Use;
    private DataGridViewTextBoxColumn Column_Name;

    protected override CreateParams CreateParams
    {
      get
      {
        CreateParams createParams = base.CreateParams;
        createParams.ExStyle |= 33554432;
        return createParams;
      }
    }

    public frmSummaryView()
    {
      this.InitializeComponent();
      this.newButton_Apply.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Apply.Image);
      this.newButton_Apply.ButtonText = LocaleControl.getInstance().GetString("IDS_APPLY");
    }

    private void frmCase_Load(object sender, EventArgs e) => this.SetDefault();

    private void SetDefault()
    {
      Dictionary<string, string> dictionary = new Dictionary<string, string>();
      try
      {
        Dictionary<string, string> summaryViewDataFromIni = clsData.GetSummaryViewDataFromIni();
        if (clsDefine.g_dtSummaryView == null)
          return;
        foreach (DataRow dataRow in clsDefine.g_dtSummaryView.AsEnumerable())
        {
          DataRow drSummaryView = dataRow;
          DataGridViewRow row = this.dataGridView_SummaryView.Rows[this.dataGridView_SummaryView.Rows.Add()];
          row.Cells[0].Value = (object) drSummaryView["Section"].ToString();
          row.Cells[1].Value = (object) clsUtill.ConvertToBoolean(drSummaryView["Use"].ToString());
          row.Cells[2].Value = (object) summaryViewDataFromIni.FirstOrDefault<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Value == drSummaryView["Section"].ToString())).Key;
        }
        this.dataGridView_SummaryView.ClearSelection();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmSummaryView]SetDefault):" + ex.Message));
      }
    }

    private void newButton_Apply_NewClick(object sender, EventArgs e)
    {
      try
      {
        using (FileStream fileStream = File.Open(clsDefine.g_fiSummaryViewOptionCfg.FullName, FileMode.Open))
        {
          fileStream.SetLength(0L);
          fileStream.Close();
        }
        clsDefine.g_dtSummaryView.Rows.Clear();
        foreach (DataGridViewRow row in (IEnumerable) this.dataGridView_SummaryView.Rows)
        {
          DataRow dataRow = clsDefine.g_dtSummaryView.Rows.Add();
          dataRow["Section"] = row.Cells[0].Value;
          dataRow["Use"] = row.Cells[1].Value;
          clsUtill.WriteINI(dataRow["Section"].ToString(), "Use", dataRow["Use"].ToString(), clsDefine.g_fiSummaryViewOptionCfg.FullName);
        }
        this.DialogResult = DialogResult.OK;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmSummaryView]newButton_Apply_NewClick):" + ex.Message));
      }
      this.Close();
    }

    private void frmCase_KeyDown(object sender, KeyEventArgs e)
    {
      if (e.KeyCode != Keys.Escape)
        return;
      this.Close();
    }

    private void dataGridView_SummaryView_MouseMove(object sender, MouseEventArgs e)
    {
      if ((e.Button & MouseButtons.Left) != MouseButtons.Left || !(this.m_recDragBoxFromMouseDown != Rectangle.Empty) || this.m_recDragBoxFromMouseDown.Contains(e.X, e.Y))
        return;
      this.dataGridView_SummaryView.DoDragDrop((object) this.dataGridView_SummaryView.Rows[this.m_iRowIndexFromMouseDown], DragDropEffects.Move);
    }

    private void dataGridView_SummaryView_MouseDown(object sender, MouseEventArgs e)
    {
      this.m_iRowIndexFromMouseDown = this.dataGridView_SummaryView.HitTest(e.X, e.Y).RowIndex;
      if (this.m_iRowIndexFromMouseDown != -1)
      {
        Size dragSize = SystemInformation.DragSize;
        this.m_recDragBoxFromMouseDown = new Rectangle(new Point(e.X - dragSize.Width / 2, e.Y - dragSize.Height / 2), dragSize);
      }
      else
        this.m_recDragBoxFromMouseDown = Rectangle.Empty;
    }

    private void dataGridView_SummaryView_DragOver(object sender, DragEventArgs e) => e.Effect = DragDropEffects.Move;

    private void dataGridView_SummaryView_DragDrop(object sender, DragEventArgs e)
    {
      Point client = this.dataGridView_SummaryView.PointToClient(new Point(e.X, e.Y));
      this.m_iRowIndexOfItemUnderMouseToDrop = this.dataGridView_SummaryView.HitTest(client.X, client.Y).RowIndex;
      if (e.Effect != DragDropEffects.Move)
        return;
      DataGridViewRow data = e.Data.GetData(typeof (DataGridViewRow)) as DataGridViewRow;
      this.dataGridView_SummaryView.Rows.RemoveAt(this.m_iRowIndexFromMouseDown);
      this.dataGridView_SummaryView.Rows.Insert(this.m_iRowIndexOfItemUnderMouseToDrop, data);
      this.dataGridView_SummaryView.ClearSelection();
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      DataGridViewCellStyle gridViewCellStyle1 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle2 = new DataGridViewCellStyle();
      this.panel_Packing = new Panel();
      this.dataGridView_SummaryView = new DataGridView();
      this.Column_Section = new DataGridViewTextBoxColumn();
      this.Column_Use = new DataGridViewCheckBoxColumn();
      this.Column_Name = new DataGridViewTextBoxColumn();
      this.label_SummaryView = new Label();
      this.newButton_Apply = new NewButton();
      this.panel_Packing.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_SummaryView).BeginInit();
      this.SuspendLayout();
      this.panel_Packing.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Packing.Controls.Add((Control) this.dataGridView_SummaryView);
      this.panel_Packing.Controls.Add((Control) this.label_SummaryView);
      this.panel_Packing.Location = new Point(6, 6);
      this.panel_Packing.Name = "panel_Packing";
      this.panel_Packing.Size = new Size(208, 205);
      this.panel_Packing.TabIndex = 91;
      this.dataGridView_SummaryView.AllowDrop = true;
      this.dataGridView_SummaryView.AllowUserToAddRows = false;
      this.dataGridView_SummaryView.AllowUserToDeleteRows = false;
      this.dataGridView_SummaryView.AllowUserToResizeColumns = false;
      this.dataGridView_SummaryView.AllowUserToResizeRows = false;
      this.dataGridView_SummaryView.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_SummaryView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_SummaryView.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
      this.dataGridView_SummaryView.BackgroundColor = Color.White;
      gridViewCellStyle1.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle1.BackColor = Color.Lavender;
      gridViewCellStyle1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle1.ForeColor = SystemColors.WindowText;
      gridViewCellStyle1.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle1.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle1.WrapMode = DataGridViewTriState.True;
      this.dataGridView_SummaryView.ColumnHeadersDefaultCellStyle = gridViewCellStyle1;
      this.dataGridView_SummaryView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_SummaryView.ColumnHeadersVisible = false;
      this.dataGridView_SummaryView.Columns.AddRange((DataGridViewColumn) this.Column_Section, (DataGridViewColumn) this.Column_Use, (DataGridViewColumn) this.Column_Name);
      gridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle2.BackColor = SystemColors.Window;
      gridViewCellStyle2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle2.ForeColor = SystemColors.ControlText;
      gridViewCellStyle2.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle2.WrapMode = DataGridViewTriState.False;
      this.dataGridView_SummaryView.DefaultCellStyle = gridViewCellStyle2;
      this.dataGridView_SummaryView.EnableHeadersVisualStyles = false;
      this.dataGridView_SummaryView.Location = new Point(-2, 17);
      this.dataGridView_SummaryView.MultiSelect = false;
      this.dataGridView_SummaryView.Name = "dataGridView_SummaryView";
      this.dataGridView_SummaryView.RowHeadersVisible = false;
      this.dataGridView_SummaryView.RowTemplate.Height = 23;
      this.dataGridView_SummaryView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
      this.dataGridView_SummaryView.Size = new Size(210, 187);
      this.dataGridView_SummaryView.TabIndex = 85;
      this.dataGridView_SummaryView.DragDrop += new DragEventHandler(this.dataGridView_SummaryView_DragDrop);
      this.dataGridView_SummaryView.DragOver += new DragEventHandler(this.dataGridView_SummaryView_DragOver);
      this.dataGridView_SummaryView.MouseDown += new MouseEventHandler(this.dataGridView_SummaryView_MouseDown);
      this.dataGridView_SummaryView.MouseMove += new MouseEventHandler(this.dataGridView_SummaryView_MouseMove);
      this.Column_Section.HeaderText = "Section";
      this.Column_Section.Name = "Column_Section";
      this.Column_Section.Visible = false;
      this.Column_Use.FillWeight = 50.76142f;
      this.Column_Use.HeaderText = "Check";
      this.Column_Use.Name = "Column_Use";
      this.Column_Use.Resizable = DataGridViewTriState.False;
      this.Column_Name.FillWeight = 149.2386f;
      this.Column_Name.HeaderText = "Name";
      this.Column_Name.Name = "Column_Name";
      this.Column_Name.ReadOnly = true;
      this.Column_Name.Resizable = DataGridViewTriState.False;
      this.Column_Name.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.label_SummaryView.BackColor = Color.FromArgb(229, 238, 248);
      this.label_SummaryView.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_SummaryView.ForeColor = Color.MidnightBlue;
      this.label_SummaryView.Location = new Point(-2, -1);
      this.label_SummaryView.Name = "label_SummaryView";
      this.label_SummaryView.Size = new Size(210, 21);
      this.label_SummaryView.TabIndex = 64;
      this.label_SummaryView.Text = "Summary View";
      this.label_SummaryView.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.newButton_Apply.ButtonBackColor = Color.White;
      this.newButton_Apply.ButtonText = "적용";
      this.newButton_Apply.FlatBorderSize = 1;
      this.newButton_Apply.FlatStyle = FlatStyle.Flat;
      this.newButton_Apply.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Apply.Image = (Image) Resources.OK;
      this.newButton_Apply.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Apply.Location = new Point(6, 213);
      this.newButton_Apply.Name = "newButton_Apply";
      this.newButton_Apply.Size = new Size(208, 23);
      this.newButton_Apply.TabIndex = 18;
      this.newButton_Apply.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Apply.NewClick += new EventHandler(this.newButton_Apply_NewClick);
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(220, 238);
      this.Controls.Add((Control) this.panel_Packing);
      this.Controls.Add((Control) this.newButton_Apply);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmSummaryView);
      this.ShowInTaskbar = false;
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "Summary View";
      this.Load += new EventHandler(this.frmCase_Load);
      this.KeyDown += new KeyEventHandler(this.frmCase_KeyDown);
      this.panel_Packing.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_SummaryView).EndInit();
      this.ResumeLayout(false);
    }
  }
}
