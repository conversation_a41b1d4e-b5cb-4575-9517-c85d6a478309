﻿// Decompiled with JetBrains decompiler
// Type: HDMFReport.clsBase
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using HDMoldFlowLibrary;
using System.Collections.Generic;
using System.Data;

namespace HDMFReport
{
  internal abstract class clsBase
  {
    public virtual void ExportReport(
      DataRow p_drStudy,
      string p_strLangType,
      clsHDMFLibDefine.Company p_enumCompany)
    {
    }

    public virtual void ExportCMPRReport(
      List<DataRow> p_lst_drStudy,
      string p_strLangType,
      clsHDMFLibDefine.Company p_enumCompany)
    {
    }

    protected virtual void StartExport(
      DataRow p_drStudy,
      Dictionary<string, string> p_dicValue,
      Dictionary<string, string> p_dicView,
      Dictionary<string, string> p_dicUse,
      clsHDMFLibDefine.Company p_enumCompany,
      string p_strLangType)
    {
    }

    protected virtual void StartExport(
      DataRow p_drStudy,
      Dictionary<string, string> p_dicValue,
      Dictionary<string, string> p_dicView,
      List<string> p_lstDelete,
      clsHDMFLibDefine.Company p_enumCompany,
      string p_strLangType)
    {
    }

    protected virtual void StartCMPRExport(
      List<CMPRData> p_lst_CMPRData,
      clsHDMFLibDefine.Company p_enumCompany,
      string p_strLangType)
    {
    }
  }
}
