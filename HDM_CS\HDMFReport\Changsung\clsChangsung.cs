﻿// Decompiled with JetBrains decompiler
// Type: HDMFReport.clsChangsung
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using HDLog4Net;
using HDMoldFlowLibrary;
using Microsoft.Office.Core;
using Microsoft.Office.Interop.PowerPoint;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace HDMFReport
{
  internal class clsChangsung : clsBase
  {
    public override void ExportReport(
      DataRow p_drStudy,
      string p_strLangType,
      clsHDMFLibDefine.Company p_enumCompany)
    {
      Dictionary<string, string> p_dicValue = new Dictionary<string, string>();
      Dictionary<string, string> p_dicView = new Dictionary<string, string>();
      Dictionary<string, string> p_dicUse = new Dictionary<string, string>();
      try
      {
        p_dicValue.Clear();
        p_dicView.Clear();
        p_dicUse.Clear();
        clsChangsungData.GetReportUser(p_drStudy, out p_dicValue, out p_dicView, out p_dicUse);
        this.StartExport(p_drStudy, p_dicValue, p_dicView, p_dicUse, p_enumCompany, p_strLangType);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsChangsung]ExportReport):" + ex.Message));
      }
    }

    protected override void StartExport(
      DataRow p_drStudy,
      Dictionary<string, string> p_dicValue,
      Dictionary<string, string> p_dicView,
      Dictionary<string, string> p_dicUse,
      clsHDMFLibDefine.Company p_enumCompany,
      string p_strLangType = "KOR")
    {
      Dictionary<string, string> analysisData = clsChangsungData.GetAnalysisData(p_drStudy, p_dicValue, p_dicView);
      int num = 1;
      FileInfo fileInfo;
      while (true)
      {
        fileInfo = new FileInfo(clsReportDefine.g_diProject.Parent.FullName + "\\" + clsReportDefine.g_diProject.Name + "_Export\\" + p_drStudy["Name"] + "_" + DateTime.Now.ToString("yyMMdd") + "_Report_Rev" + (object) num + "." + clsReportDefine.g_dicExtension["PowerPoint"]);
        if (fileInfo.Exists)
          ++num;
        else
          break;
      }
      if (!fileInfo.Directory.Exists)
        fileInfo.Directory.Create();
      try
      {
        // ISSUE: variable of a compiler-generated type
        Presentations presentations = ((_Application) Activator.CreateInstance(Marshal.GetTypeFromCLSID(new Guid("91493441-5A91-11CF-8700-00AA0060263B")))).Presentations;
        FileInfo template = clsReportData.GetTemplate(p_enumCompany, p_strLangType);
        template.Refresh();
        if (template.Exists)
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          Presentation presentation = presentations.Open(template.FullName, MsoTriState.msoTrue, WithWindow: MsoTriState.msoFalse);
          List<Slide> allSlide = this.GetAllSlide(presentation);
          if (p_dicUse.Count > 0)
          {
            List<Slide> slideList = new List<Slide>();
            foreach (KeyValuePair<string, string> keyValuePair in p_dicUse)
            {
              KeyValuePair<string, string> kvpTmp = keyValuePair;
              if (!clsReportUtill.ConvertToBoolean(kvpTmp.Value))
              {
                // ISSUE: variable of a compiler-generated type
                Slide slide = allSlide.Where<Slide>((System.Func<Slide, bool>) (Temp => Temp.Name.Contains(kvpTmp.Key))).FirstOrDefault<Slide>();
                if (slide != null)
                  slideList.Add(slide);
              }
            }
            for (int index = 0; index < slideList.Count; ++index)
            {
              // ISSUE: reference to a compiler-generated method
              slideList[index].Delete();
              allSlide.Remove(slideList[index]);
            }
          }
          foreach (Slide p_slData in allSlide)
          {
            switch (p_slData.Name)
            {
              case "AirTrap":
                this.SetSlide20(p_slData, this.GetSlide20(analysisData));
                continue;
              case "AnalysisInformation":
                this.SetSlide10(p_slData, this.GetSlide10(analysisData));
                continue;
              case "CAEDR_1":
                this.SetSlide2(p_slData, this.GetSlide2(analysisData));
                continue;
              case "CAEReport":
                this.SetSlide6(p_slData, this.GetSlide6(analysisData));
                continue;
              case "Cool":
                this.SetSlide13(p_slData, this.GetSlide13(analysisData));
                continue;
              case "DefX":
                this.SetSlide24(p_slData, this.GetSlide24(analysisData));
                continue;
              case "DefY":
                this.SetSlide25(p_slData, this.GetSlide25(analysisData));
                continue;
              case "DefZ":
                this.SetSlide26(p_slData, this.GetSlide26(analysisData));
                continue;
              case "FillTime_1":
                this.SetSlide15(p_slData, this.GetSlide15(analysisData));
                continue;
              case "FillTime_2":
                this.SetSlide16(p_slData, this.GetSlide16(analysisData));
                continue;
              case "FillTime_3":
                this.SetSlide17(p_slData, this.GetSlide17(analysisData));
                continue;
              case "GateSystem":
                this.SetSlide12(p_slData, this.GetSlide12(analysisData));
                continue;
              case "PressureClamp":
                this.SetSlide18(p_slData, this.GetSlide18(analysisData));
                continue;
              case "Shrinkage":
                this.SetSlide21(p_slData, this.GetSlide21(analysisData));
                continue;
              case "SinkMark":
                this.SetSlide23(p_slData, this.GetSlide23(analysisData));
                continue;
              case "SinkMarkEstimate":
                this.SetSlide22(p_slData, this.GetSlide22(analysisData));
                continue;
              case "Summary":
                this.SetSlide8(p_slData, this.GetSlide8(analysisData));
                continue;
              case "TemperatureAtFlowFront":
                this.SetSlide19(p_slData, this.GetSlide19(analysisData));
                continue;
              case "Thickness":
                this.SetSlide11(p_slData, this.GetSlide11(analysisData));
                continue;
              case "Title":
                this.SetSlide1(p_slData, this.GetSlide1(analysisData));
                continue;
              default:
                continue;
            }
          }
          if (!fileInfo.Extension.Contains(".ppt"))
          {
            // ISSUE: reference to a compiler-generated method
            presentation.SaveAs(fileInfo.FullName.Replace(fileInfo.Extension, ".pptx"));
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            presentation.SaveAs(fileInfo.FullName);
          }
          // ISSUE: reference to a compiler-generated method
          presentation.Close();
          clsReportUtill.ReleaseComObject((object) presentation);
        }
        if (fileInfo.Extension.Contains(".ppt"))
          return;
        if (fileInfo.Exists)
          fileInfo.Delete();
        File.Move(fileInfo.FullName.Replace(fileInfo.Extension, ".pptx"), fileInfo.FullName);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsChangsung]StartExport):" + ex.Message));
      }
    }

    private List<Slide> GetAllSlide(Presentation p_pptPre)
    {
      List<Slide> allSlide = new List<Slide>((IEnumerable<Slide>) p_pptPre.Slides.Cast<Slide>().ToArray<Slide>());
      try
      {
        for (int index = 0; index < allSlide.Count; ++index)
        {
          switch (index)
          {
            case 0:
              allSlide[index].Name = "Title";
              break;
            case 1:
              allSlide[index].Name = "CAEDR_1";
              break;
            case 2:
              allSlide[index].Name = "CAEDR_2";
              break;
            case 3:
              allSlide[index].Name = "CAEDR_3";
              break;
            case 4:
              allSlide[index].Name = "CAEDR_4";
              break;
            case 5:
              allSlide[index].Name = "CAEReport";
              break;
            case 6:
              allSlide[index].Name = "Summary_Title";
              break;
            case 7:
              allSlide[index].Name = "Summary";
              break;
            case 8:
              allSlide[index].Name = "AnalysisInformation_Title";
              break;
            case 9:
              allSlide[index].Name = "AnalysisInformation";
              break;
            case 10:
              allSlide[index].Name = "Thickness";
              break;
            case 11:
              allSlide[index].Name = "GateSystem";
              break;
            case 12:
              allSlide[index].Name = "Cool";
              break;
            case 13:
              allSlide[index].Name = "AnalysisResults_Title";
              break;
            case 14:
              allSlide[index].Name = "FillTime_1";
              break;
            case 15:
              allSlide[index].Name = "FillTime_2";
              break;
            case 16:
              allSlide[index].Name = "FillTime_3";
              break;
            case 17:
              allSlide[index].Name = "PressureClamp";
              break;
            case 18:
              allSlide[index].Name = "TemperatureAtFlowFront";
              break;
            case 19:
              allSlide[index].Name = "AirTrap";
              break;
            case 20:
              allSlide[index].Name = "Shrinkage";
              break;
            case 21:
              allSlide[index].Name = "SinkMarkEstimate";
              break;
            case 22:
              allSlide[index].Name = "SinkMark";
              break;
            case 23:
              allSlide[index].Name = "DefX";
              break;
            case 24:
              allSlide[index].Name = "DefY";
              break;
            case 25:
              allSlide[index].Name = "DefZ";
              break;
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsChangsung]GetAllSlide):" + ex.Message));
      }
      return allSlide;
    }

    private Dictionary<string, string> GetSlide1(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide1 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Manager[User]")))
          slide1.Add("Manager", p_dicData["Manager[User]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Engineer[User]")))
          slide1.Add("Engineer", p_dicData["Engineer[User]"]);
        slide1.Add("Date", DateTime.Now.ToString("yyyy.MM.dd"));
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsChangsung]GetSlide1):" + ex.Message));
      }
      return slide1;
    }

    private Dictionary<string, string> GetSlide2(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide2 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FamilyName[MF]")))
          slide2.Add("FamilyName", p_dicData["FamilyName[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Model[IMG]")))
          slide2.Add("Image1", p_dicData["Model[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsChangsung]GetSlide2):" + ex.Message));
      }
      return slide2;
    }

    private Dictionary<string, string> GetSlide6(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide6 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Manager[User]")))
          slide6.Add("Manager", p_dicData["Manager[User]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Engineer[User]")))
          slide6.Add("Engineer", p_dicData["Engineer[User]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsChangsung]GetSlide6):" + ex.Message));
      }
      return slide6;
    }

    private Dictionary<string, string> GetSlide7(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide7 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime7_1[IMG]")))
          slide7.Add("Image1", p_dicData["FillTime7_1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime7_2[IMG]")))
          slide7.Add("Image2", p_dicData["FillTime7_2[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime7_3[IMG]")))
          slide7.Add("Image3", p_dicData["FillTime7_3[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime7_4[IMG]")))
          slide7.Add("Image4", p_dicData["FillTime7_4[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime7_5[IMG]")))
          slide7.Add("Image5", p_dicData["FillTime7_5[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsChangsung]GetSlide7):" + ex.Message));
      }
      return slide7;
    }

    private Dictionary<string, string> GetSlide8(Dictionary<string, string> p_dicData)
    {
      StringBuilder stringBuilder = new StringBuilder();
      Dictionary<string, string> slide8 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FamilyName[MF]")))
          slide8.Add("FamilyName", p_dicData["FamilyName[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ClampForce[Log]")))
          slide8.Add("ClampForce", p_dicData["ClampForce[Log]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SpruePressure[Log]")))
          slide8.Add("Pressure", p_dicData["SpruePressure[Log]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime[Log]")))
          slide8.Add("InjectionTime", p_dicData["FillTime[Log]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX_BestFit[Plot]")))
          slide8.Add("Deflection X", p_dicData["DefX_BestFit[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY_BestFit[Plot]")))
          slide8.Add("Deflection Y", p_dicData["DefY_BestFit[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ_BestFit[Plot]")))
          slide8.Add("Deflection Z", p_dicData["DefZ_BestFit[Plot]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsChangsung]GetSlide8):" + ex.Message));
      }
      return slide8;
    }

    private Dictionary<string, string> GetSlide10(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide10 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FamilyName[MF]")))
          slide10.Add("Polymer", p_dicData["FamilyName[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Manufacturer[MF]")))
          slide10.Add("Manufacturer", p_dicData["Manufacturer[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TradeName[MF]")))
          slide10.Add("Grade", p_dicData["TradeName[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MeltTemp[MF]")))
          slide10.Add("Temperature of resin", p_dicData["MeltTemp[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MoldTemp[MF]")))
          slide10.Add("Temperature in mold", p_dicData["MoldTemp[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillingControl[MF]")))
          slide10.Add("Injection Time", p_dicData["FillingControl[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PackHoldingControl[MF]")))
          slide10.Add("PackHoldingControl", p_dicData["PackHoldingControl[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CoolingTime[MF]")))
          slide10.Add("CoolingTime", p_dicData["CoolingTime[MF]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsChangsung]GetSlide10):" + ex.Message));
      }
      return slide10;
    }

    private Dictionary<string, string> GetSlide11(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide11 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Thickness1[IMG]")))
          slide11.Add("Image1", p_dicData["Thickness1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Thickness2[IMG]")))
          slide11.Add("Image2", p_dicData["Thickness2[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsChangsung]GetSlide11):" + ex.Message));
      }
      return slide11;
    }

    private Dictionary<string, string> GetSlide12(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide12 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Gate1[IMG]")))
          slide12.Add("Image1", p_dicData["Gate1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Gate2[IMG]")))
          slide12.Add("Image2", p_dicData["Gate2[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Gate3[IMG]")))
          slide12.Add("Image3", p_dicData["Gate3[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsChangsung]GetSlide12):" + ex.Message));
      }
      return slide12;
    }

    private Dictionary<string, string> GetSlide13(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide13 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperature[IMG]")))
          slide13.Add("Image1", p_dicData["CircuitCoolantTemperature[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperatureProbeXYPlot[IMG]")))
          slide13.Add("Image2", p_dicData["TimeToReachEjectionTemperatureProbeXYPlot[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperature1[IMG]")))
          slide13.Add("Image3", p_dicData["TimeToReachEjectionTemperature1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperature2[IMG]")))
          slide13.Add("Image4", p_dicData["TimeToReachEjectionTemperature2[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperature[Plot]")))
          slide13.Add("Time To Reach Ejection Temperature", p_dicData["TimeToReachEjectionTemperature[Plot]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsChangsung]GetSlide12):" + ex.Message));
      }
      return slide13;
    }

    private Dictionary<string, string> GetSlide15(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide15 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime15_1[IMG]")))
          slide15.Add("Image1", p_dicData["FillTime15_1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime15_2[IMG]")))
          slide15.Add("Image2", p_dicData["FillTime15_2[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime15_3[IMG]")))
          slide15.Add("Image3", p_dicData["FillTime15_3[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime15_4[IMG]")))
          slide15.Add("Image4", p_dicData["FillTime15_4[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillingControl[MF]")))
          slide15.Add("Injection Time", p_dicData["FillingControl[MF]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide15):" + ex.Message));
      }
      return slide15;
    }

    private Dictionary<string, string> GetSlide16(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide16 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime16_1[IMG]")))
          slide16.Add("Image1", p_dicData["FillTime16_1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime16_2[IMG]")))
          slide16.Add("Image2", p_dicData["FillTime16_2[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime16_3[IMG]")))
          slide16.Add("Image3", p_dicData["FillTime16_3[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime16_4[IMG]")))
          slide16.Add("Image4", p_dicData["FillTime16_4[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillingControl[MF]")))
          slide16.Add("Injection Time", p_dicData["FillingControl[MF]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide16):" + ex.Message));
      }
      return slide16;
    }

    private Dictionary<string, string> GetSlide17(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide17 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillAnimation17_1[IMG]")))
          slide17.Add("Image1", p_dicData["FillAnimation17_1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillAnimation17_2[IMG]")))
          slide17.Add("Image2", p_dicData["FillAnimation17_2[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillAnimation17_3[IMG]")))
          slide17.Add("Image3", p_dicData["FillAnimation17_3[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillAnimation17_4[IMG]")))
          slide17.Add("Image4", p_dicData["FillAnimation17_4[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillingControl[MF]")))
          slide17.Add("Injection Time", p_dicData["FillingControl[MF]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide17):" + ex.Message));
      }
      return slide17;
    }

    private Dictionary<string, string> GetSlide18(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide18 = new Dictionary<string, string>();
      string str = "PressureXY";
      string strTmp = str;
      double minValue = double.MinValue;
      bool flag = false;
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PressureAtInjectionLocation[IMG]")))
          slide18.Add("Image1", p_dicData["PressureAtInjectionLocation[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ClampForceXY[IMG]")))
          slide18.Add("Image2", p_dicData["ClampForceXY[IMG]"]);
        StringBuilder stringBuilder1 = new StringBuilder();
        StringBuilder stringBuilder2 = new StringBuilder();
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SpruePressure[Log]")))
        {
          double num = clsReportUtill.ConvertToDouble(p_dicData["SpruePressure[Log]"]);
          stringBuilder1.Append(num);
          stringBuilder2.Append(Math.Round(num * 1.25, 2));
        }
        strTmp = str;
        for (int index = 0; index < 4; ++index)
        {
          if (index > 0)
            strTmp = str + "_" + index.ToString();
          if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strTmp + "[Plot]")))
          {
            if (clsReportUtill.ConvertToDouble(p_dicData[strTmp + "[Plot]"]) > minValue)
              minValue = clsReportUtill.ConvertToDouble(p_dicData[strTmp + "[Plot]"]);
            flag = true;
          }
        }
        if (flag)
        {
          stringBuilder1.Append("|");
          stringBuilder1.Append(minValue);
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PressureXY[Plot]")))
        {
          stringBuilder1.Append("|");
          stringBuilder1.Append(p_dicData["PressureXY[Plot]"]);
        }
        if (stringBuilder1.Length > 0)
          slide18.Add("Injection Pressure1", stringBuilder1.ToString());
        if (stringBuilder2.Length > 0)
          slide18.Add("Injection Pressure2", stringBuilder2.ToString());
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ClampForce[Log]")))
        {
          double num = clsReportUtill.ConvertToDouble(p_dicData["ClampForce[Log]"]);
          slide18.Add("Clamp1", num.ToString());
          slide18.Add("Clamp2", Math.Round(num * 1.2, 2).ToString());
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide18):" + ex.Message));
      }
      return slide18;
    }

    private Dictionary<string, string> GetSlide19(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide19 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureAtFlowFront[IMG]")))
          slide19.Add("Image", p_dicData["TemperatureAtFlowFront[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureAtFlowFront[Plot]")))
          slide19.Add("Temperature At Flow Front", p_dicData["TemperatureAtFlowFront[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TransitionTemp[MF]")))
          slide19.Add("TransitionTemp", p_dicData["TransitionTemp[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MaterialStructure[MF]")))
        {
          string str1 = p_dicData["MaterialStructure[MF]"];
          string str2 = !(str1.ToUpper() == "AMORPHOUS") ? (!(str1.ToUpper() == "CRYSTALLINE") ? "0" : "5") : "10";
          slide19.Add("Material Structure", str2);
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureAtFlowFront_Min[Plot]")))
          slide19.Add("Min", p_dicData["TemperatureAtFlowFront_Min[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureAtFlowFront_Min[IMG]")))
          slide19.Add("Min Image", p_dicData["TemperatureAtFlowFront_Min[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureAtFlowFront_Max[Plot]")))
          slide19.Add("Max", p_dicData["TemperatureAtFlowFront_Max[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureAtFlowFront_Max[IMG]")))
          slide19.Add("Max Image", p_dicData["TemperatureAtFlowFront_Max[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide19):" + ex.Message));
      }
      return slide19;
    }

    private Dictionary<string, string> GetSlide20(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide20 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "AirTrap1[IMG]")))
          slide20.Add("Image1", p_dicData["AirTrap1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "AirTrap2[IMG]")))
          slide20.Add("Image2", p_dicData["AirTrap2[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "AirTrap3[IMG]")))
          slide20.Add("Image3", p_dicData["AirTrap3[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "AirTrap4[IMG]")))
          slide20.Add("Image4", p_dicData["AirTrap4[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide20):" + ex.Message));
      }
      return slide20;
    }

    private Dictionary<string, string> GetSlide21(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide21 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VolumetricShrinkage1[IMG]")))
          slide21.Add("Image1", p_dicData["VolumetricShrinkage1[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide21):" + ex.Message));
      }
      return slide21;
    }

    private Dictionary<string, string> GetSlide22(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide22 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SinkMarkEstimate[IMG]")))
          slide22.Add("Image1", p_dicData["SinkMarkEstimate[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide22):" + ex.Message));
      }
      return slide22;
    }

    private Dictionary<string, string> GetSlide23(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide23 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SinkMark1[IMG]")))
          slide23.Add("Image1", p_dicData["SinkMark1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SinkMark2[IMG]")))
          slide23.Add("Image2", p_dicData["SinkMark2[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide23):" + ex.Message));
      }
      return slide23;
    }

    private Dictionary<string, string> GetSlide24(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide24 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX_BestFit[IMG]")))
          slide24.Add("Image1", p_dicData["DefX_BestFit[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX_BestFit[Plot]")))
          slide24.Add("DefX", p_dicData["DefX_BestFit[Plot]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide24):" + ex.Message));
      }
      return slide24;
    }

    private Dictionary<string, string> GetSlide25(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide25 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY_BestFit[IMG]")))
          slide25.Add("Image1", p_dicData["DefY_BestFit[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY_BestFit[Plot]")))
          slide25.Add("DefY", p_dicData["DefY_BestFit[Plot]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide25):" + ex.Message));
      }
      return slide25;
    }

    private Dictionary<string, string> GetSlide26(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide26 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ_BestFit[IMG]")))
          slide26.Add("Image1", p_dicData["DefZ_BestFit[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ_BestFit[Plot]")))
          slide26.Add("DefZ", p_dicData["DefZ_BestFit[Plot]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide26):" + ex.Message));
      }
      return slide26;
    }

    private void SetSlide1(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S1_Title_1")).FirstOrDefault<Shape>();
        if (shape1 != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Manager")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter("제작처 : " + p_dicSData["Manager"]);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S1_Title_2")).FirstOrDefault<Shape>();
        if (shape2 != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Engineer")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape2.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter("담당 : " + p_dicSData["Engineer"]);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape3 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S1_Title_3")).FirstOrDefault<Shape>();
        if (shape3 == null || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Date")))
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape3.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange1.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange1.InsertAfter("작성일자 : " + p_dicSData["Date"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsChangsung]SetSlide1):" + ex.Message));
      }
    }

    private void SetSlide2(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 74.26772f, 311.527557f, 300.75592f, 110.8346f);
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S2_Table_1")).FirstOrDefault<Shape>().Table;
        if (table == null || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FamilyName")))
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange = table.Rows[4].Cells[1].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange.InsertAfter(p_dicSData["FamilyName"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsChangsung]SetSlide2):" + ex.Message));
      }
    }

    private void SetSlide6(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S6_Text_1")).FirstOrDefault<Shape>();
        if (shape == null || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Engineer")) || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Manager")) || !(p_dicSData["Engineer"] != "") || !(p_dicSData["Manager"] != ""))
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange = shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange.InsertAfter(p_dicSData["Manager"] + Environment.NewLine + p_dicSData["Engineer"]);
        textRange.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsChangsung]SetSlide6):" + ex.Message));
      }
    }

    private void SetSlide8(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string[] arr_strKey = (string[]) null;
      string[] strArray = (string[]) null;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Table table1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S8_Table_1")).FirstOrDefault<Shape>().Table;
        if (table1 != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FamilyName")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = table1.Rows[3].Cells[6].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(p_dicSData["FamilyName"]);
        }
        // ISSUE: variable of a compiler-generated type
        Table table2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S8_Table_2")).FirstOrDefault<Shape>().Table;
        if (table2 != null)
        {
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "InjectionTime")))
          {
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = table2.Rows[2].Cells[5].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter(p_dicSData["InjectionTime"]);
          }
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Pressure")))
          {
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = table2.Rows[2].Cells[6].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter(p_dicSData["Pressure"]);
          }
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ClampForce")))
          {
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = table2.Rows[2].Cells[7].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter(p_dicSData["ClampForce"]);
          }
        }
        // ISSUE: variable of a compiler-generated type
        Table table3 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S8_Table_3")).FirstOrDefault<Shape>().Table;
        if (table3 == null)
          return;
        arr_strKey = new string[3]
        {
          "Deflection X",
          "Deflection Y",
          "Deflection Z"
        };
        for (int i = 0; i < arr_strKey.Length; i++)
        {
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == arr_strKey[i])))
            strArray = p_dicSData[arr_strKey[i]].Split('|');
          if (strArray.Length > 1)
          {
            // ISSUE: variable of a compiler-generated type
            TextRange textRange1 = table3.Rows[3].Cells[i + 2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange1.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange1.InsertAfter(strArray[0] + " ~ " + strArray[1] + " mm");
            textRange1.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            double num = Math.Abs(clsReportUtill.ConvertToDouble(strArray[0])) + Math.Abs(clsReportUtill.ConvertToDouble(strArray[1]));
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = table3.Rows[3].Cells[i + 5].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange2.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange2.InsertAfter(num.ToString());
            textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsChangsung]SetSlide8):" + ex.Message));
      }
    }

    private void SetSlide10(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S10_Table_1")).FirstOrDefault<Shape>().Table;
        if (table == null)
          return;
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Polymer")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = table.Rows[1].Cells[5].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(p_dicSData["Polymer"]);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Manufacturer")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = table.Rows[1].Cells[10].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(p_dicSData["Manufacturer"]);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Grade")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = table.Rows[2].Cells[10].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(p_dicSData["Grade"]);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Temperature of resin")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = table.Rows[3].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(p_dicSData["Temperature of resin"] + " ℃");
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Temperature in mold")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = table.Rows[3].Cells[5].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(p_dicSData["Temperature in mold"] + " ℃");
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Injection Time")))
        {
          string[] strArray = p_dicSData["Injection Time"].Split('|');
          if (strArray.Length > 1)
          {
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = table.Rows[4].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter(strArray[1]);
          }
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PackHoldingControl")))
        {
          string[] strArray1 = p_dicSData["PackHoldingControl"].Replace("\r\n", "").Split('/');
          if (strArray1.Length > 2)
          {
            for (int index = 1; index < 3; ++index)
            {
              string[] strArray2 = strArray1[index].Replace("\r\n", "").Split('|');
              // ISSUE: variable of a compiler-generated type
              TextRange textRange1 = table.Rows[4 + (index - 1)].Cells[5].Shape.TextFrame.TextRange;
              // ISSUE: reference to a compiler-generated method
              textRange1.Delete();
              // ISSUE: reference to a compiler-generated method
              textRange1.InsertAfter(strArray2[0]);
              // ISSUE: variable of a compiler-generated type
              TextRange textRange2 = table.Rows[4 + (index - 1)].Cells[7].Shape.TextFrame.TextRange;
              // ISSUE: reference to a compiler-generated method
              textRange2.Delete();
              // ISSUE: reference to a compiler-generated method
              textRange2.InsertAfter(strArray2[1]);
            }
          }
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CoolingTime")))
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange3 = table.Rows[4].Cells[10].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange3.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange3.InsertAfter(p_dicSData["CoolingTime"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsChangsung]SetSlide10):" + ex.Message));
      }
    }

    private void SetSlide11(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 31.46457f, 261.0709f, 311.811f, 252f);
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 369.6378f, 261.0709f, 315.496063f, 255.118f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsChangsung]SetSlide11):" + ex.Message));
      }
    }

    private void SetSlide12(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 28.3465f, 324.283447f, 218.268f, 128.126f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 479.0551f, 139.4646f, 176.8819f, 174.0472f);
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 479.0551f, 333.6378f, 176.8819f, 174.0472f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsChangsung]SetSlide12):" + ex.Message));
      }
    }

    private void SetSlide13(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string empty = string.Empty;
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 24.37795f, 128.126f, 321.448822f, 184.252f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 367.0866f, 126.9921f, 326.267731f, 187.087f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 370.771637f, 380.409454f, 133.228f, 116.5039f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image4")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 533.4803f, 380.409454f, 133.228f, 116.5039f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Time To Reach Ejection Temperature")))
          empty = p_dicSData["Time To Reach Ejection Temperature"];
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S13_Rectangle_1")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange1 = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange1.Delete();
          if (empty != string.Empty)
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = textRange1.InsertAfter("취출 가능한 시간 : " + empty + "s");
            textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.White);
            textRange2.Font.Bold = MsoTriState.msoTrue;
          }
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S13_Rectangle_2")).FirstOrDefault<Shape>();
        if (shape2 == null)
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange3 = shape2.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange3.Delete();
        if (!(empty != string.Empty))
          return;
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange4 = textRange3.InsertAfter("Cycle Time " + empty + "s 이후 고화되어야 할 부위");
        textRange4.Font.Color.RGB = ColorTranslator.ToOle(Color.White);
        textRange4.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsChangsung]SetSlide13):" + ex.Message));
      }
    }

    private void SetSlide15(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 169.5118f, 163.5591f, 182.2677f, 159.0236f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 395.433075f, 163.5591f, 182.2677f, 159.0236f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 169.5118f, 347.527557f, 182.2677f, 159.0236f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image4")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image4"], MsoTriState.msoFalse, MsoTriState.msoTrue, 395.433075f, 347.527557f, 182.2677f, 159.0236f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S15_Text_1")).FirstOrDefault<Shape>();
        if (shape == null || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Injection Time")))
          return;
        string[] strArray = p_dicSData["Injection Time"].Split('|');
        if (strArray.Length <= 1)
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange = shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange.InsertAfter("Injection Time(s) : " + strArray[1]);
        textRange.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsChangsung]SetSlide15):" + ex.Message));
      }
    }

    private void SetSlide16(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 169.5118f, 163.5591f, 182.2677f, 159.0236f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 395.433075f, 163.5591f, 182.2677f, 159.0236f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 169.5118f, 347.527557f, 182.2677f, 159.0236f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image4")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image4"], MsoTriState.msoFalse, MsoTriState.msoTrue, 395.433075f, 347.527557f, 182.2677f, 159.0236f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S16_Text_1")).FirstOrDefault<Shape>();
        if (shape == null || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Injection Time")))
          return;
        string[] strArray = p_dicSData["Injection Time"].Split('|');
        if (strArray.Length <= 1)
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange = shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange.InsertAfter("Injection Time(s) : " + strArray[1]);
        textRange.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsChangsung]SetSlide16):" + ex.Message));
      }
    }

    private void SetSlide17(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 41.10236f, 150.236f, 311.811f, 170.079f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 370.4882f, 150.236f, 311.811f, 170.079f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 41.10236f, 340.7244f, 311.811f, 170.079f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image4")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image4"], MsoTriState.msoFalse, MsoTriState.msoTrue, 370.4882f, 340.7244f, 311.811f, 170.079f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S17_Text_1")).FirstOrDefault<Shape>();
        if (shape == null || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Injection Time")))
          return;
        string[] strArray = p_dicSData["Injection Time"].Split('|');
        if (strArray.Length <= 1)
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange = shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange.InsertAfter("Injection Time(s) : " + strArray[1]);
        textRange.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsChangsung]SetSlide17):" + ex.Message));
      }
    }

    private void SetSlide18(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 31.1811f, 202.6772f, 311.811f, 170.079f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 366.2362f, 202.6772f, 311.811f, 170.079f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S18_Text_1")).FirstOrDefault<Shape>();
        if (shape1 != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Injection Pressure1")))
        {
          string[] strArray = p_dicSData["Injection Pressure1"].Split('|');
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter("■ Max injection pressure = ");
          textRange.Font.Bold = MsoTriState.msoFalse;
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(strArray[0] + " Mpa");
          textRange.Font.Bold = MsoTriState.msoTrue;
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S18_Text_2")).FirstOrDefault<Shape>();
        if (shape2 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape2.TextFrame.TextRange;
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Clamp1")))
          {
            // ISSUE: reference to a compiler-generated method
            textRange.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter("■ Maximum Clamp force  = ");
            textRange.Font.Bold = MsoTriState.msoFalse;
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter(p_dicSData["Clamp1"] + " Ton");
            textRange.Font.Bold = MsoTriState.msoTrue;
          }
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Clamp2")))
          {
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter(Environment.NewLine + "■ 안전 계수 1.2 적용  = ");
            textRange.Font.Bold = MsoTriState.msoFalse;
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter(p_dicSData["Clamp2"] + " Ton");
            textRange.Font.Bold = MsoTriState.msoTrue;
          }
        }
        // ISSUE: variable of a compiler-generated type
        Table table1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S18_Table_1")).FirstOrDefault<Shape>().Table;
        if (table1 != null)
        {
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Injection Pressure1")))
          {
            string[] strArray = p_dicSData["Injection Pressure1"].Split('|');
            double num = clsReportUtill.ConvertToDouble(strArray[0]) - clsReportUtill.ConvertToDouble(strArray[1]);
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = table1.Rows[1].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter("스프루: " + strArray[0] + "MPa" + Environment.NewLine + "게이트: " + strArray[1] + "MPa" + Environment.NewLine + "스프루 - 게이트: " + (object) Math.Round(num, 2) + "MPa");
            textRange.Font.Bold = MsoTriState.msoTrue;
          }
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Injection Pressure2")))
          {
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = table1.Rows[2].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter(p_dicSData["Injection Pressure2"] + "MPa");
            textRange.Font.Bold = MsoTriState.msoTrue;
          }
        }
        // ISSUE: variable of a compiler-generated type
        Table table2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S18_Table_2")).FirstOrDefault<Shape>().Table;
        if (table2 == null)
          return;
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Clamp1")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = table2.Rows[1].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(p_dicSData["Clamp1"] + "t");
          textRange.Font.Bold = MsoTriState.msoTrue;
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Clamp2")))
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = table2.Rows[2].Cells[2].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange1.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange1.InsertAfter(p_dicSData["Clamp2"] + "t");
        textRange1.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsChangsung]SetSlide18):" + ex.Message));
      }
    }

    private void SetSlide19(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 37.70079f, 187.937f, 288f, 246.614f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S19_Text_1")).FirstOrDefault<Shape>();
        if (shape1 != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TransitionTemp")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(" ■ 천이온도 = " + p_dicSData["TransitionTemp"] + " 도");
          textRange.Font.Bold = MsoTriState.msoTrue;
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Temperature At Flow Front")))
        {
          // ISSUE: variable of a compiler-generated type
          Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S19_Table_1")).FirstOrDefault<Shape>().Table;
          if (table != null)
          {
            string[] strArray = p_dicSData["Temperature At Flow Front"].Split('|');
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = table.Rows[1].Cells[2].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter(strArray[0] + "℃ ~ " + strArray[1] + "℃");
            textRange.Font.Bold = MsoTriState.msoTrue;
          }
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S19_Text_2")).FirstOrDefault<Shape>();
        if (shape2 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange1 = shape2.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange1.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Material Structure")))
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = textRange1.InsertAfter("온도 편차는 ±" + p_dicSData["Material Structure"] + "℃ 이어야 합니다.");
            textRange2.Font.Bold = MsoTriState.msoTrue;
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Temperature At Flow Front")))
            {
              string[] strArray = p_dicSData["Temperature At Flow Front"].Split('|');
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange3 = textRange2.InsertAfter(Environment.NewLine + Environment.NewLine + "결과의 온도 편차는 ");
              textRange3.Font.Bold = MsoTriState.msoTrue;
              textRange3.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
              double num1 = Math.Round(clsReportUtill.ConvertToDouble(p_dicSData["Material Structure"]), 2);
              double num2 = Math.Round(clsReportUtill.ConvertToDouble(strArray[2]), 2);
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange4 = textRange3.InsertAfter(num2.ToString() + "℃");
              textRange4.Font.Bold = MsoTriState.msoTrue;
              double num3 = num2;
              textRange4.Font.Color.RGB = num1 >= num3 ? ColorTranslator.ToOle(Color.Black) : ColorTranslator.ToOle(Color.Red);
              // ISSUE: reference to a compiler-generated method
              textRange2 = textRange4.InsertAfter(" 입니다.");
              textRange2.Font.Bold = MsoTriState.msoTrue;
              textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            }
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange5 = textRange2.InsertAfter(Environment.NewLine + Environment.NewLine + "온도는 유동 말단보다 게이트가 낮으면 게이트가 먼저 고화되어 보압을 제대로 주지 못할 수 있습니다.(게이트가 먼저 고화될 가능성 높음)");
            textRange5.Font.Bold = MsoTriState.msoTrue;
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange6 = textRange1.InsertAfter("온도는 유동 말단보다 게이트가 낮으면 게이트가 먼저 고화되어 보압을 제대로 주지 못할 수 있습니다.(게이트가 먼저 고화될 가능성 높음)");
            textRange6.Font.Bold = MsoTriState.msoTrue;
          }
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Min Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Min Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 339.874023f, 356.8819f, 170.079f, 149.1024f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Max Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Max Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 519.874f, 356.8819f, 170.079f, 149.1024f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape3 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S19_Rectangle_1")).FirstOrDefault<Shape>();
        if (shape3 != null)
        {
          // ISSUE: reference to a compiler-generated method
          shape3.ZOrder(MsoZOrderCmd.msoBringToFront);
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Min")))
          {
            // ISSUE: variable of a compiler-generated type
            TextRange textRange7 = shape3.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange7.Delete();
            string[] strArray = p_dicSData["Min"].Split('|');
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange8 = textRange7.InsertAfter(strArray[0] + "℃ ~ " + strArray[1] + "℃");
            textRange8.Font.Color.RGB = ColorTranslator.ToOle(Color.White);
            textRange8.Font.Bold = MsoTriState.msoTrue;
          }
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape4 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S19_Rectangle_2")).FirstOrDefault<Shape>();
        if (shape4 == null)
          return;
        // ISSUE: reference to a compiler-generated method
        shape4.ZOrder(MsoZOrderCmd.msoBringToFront);
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Max")))
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange9 = shape4.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange9.Delete();
        string[] strArray1 = p_dicSData["Max"].Split('|');
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange10 = textRange9.InsertAfter(strArray1[0] + "℃ ~ " + strArray1[1] + "℃");
        textRange10.Font.Color.RGB = ColorTranslator.ToOle(Color.White);
        textRange10.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide19):" + ex.Message));
      }
    }

    private void SetSlide20(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 42.23622f, 135.4961f, 300.188965f, 170.079f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 360.566925f, 135.4961f, 300.188965f, 170.079f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 42.23622f, 329.952759f, 300.188965f, 170.079f);
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image4")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image4"], MsoTriState.msoFalse, MsoTriState.msoTrue, 360.566925f, 329.952759f, 300.188965f, 170.079f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide20):" + ex.Message));
      }
    }

    private void SetSlide21(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 44.50394f, 149.3858f, 629.2913f, 340.157f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide21):" + ex.Message));
      }
    }

    private void SetSlide22(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 44.50394f, 149.3858f, 629.2913f, 340.157f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide22):" + ex.Message));
      }
    }

    private void SetSlide23(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 31.74803f, 152.7874f, 366.2362f, 331.0866f);
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 418.960632f, 241.7953f, 241.2283f, 234.4252f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHDSolutions]SetSlide23):" + ex.Message));
      }
    }

    private void SetSlide24(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 34.86614f, 163.8425f, 314.6457f, 340.157f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S24_Text_1")).FirstOrDefault<Shape>();
        if (shape == null)
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange = shape.TextFrame.TextRange;
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX")))
          return;
        string[] strArray = p_dicSData["DefX"].Split('|');
        double num = Math.Abs(clsReportUtill.ConvertToDouble(strArray[1])) + Math.Abs(clsReportUtill.ConvertToDouble(strArray[0]));
        // ISSUE: reference to a compiler-generated method
        textRange.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange.InsertAfter("X방향 변형 – MAX : " + strArray[1] + "mm MIN : " + strArray[0] + "mm TOTAL : " + num.ToString() + "mm");
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]SetSlide24):" + ex.Message));
      }
    }

    private void SetSlide25(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 34.86614f, 163.8425f, 314.6457f, 340.157f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S25_Text_1")).FirstOrDefault<Shape>();
        if (shape == null)
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange = shape.TextFrame.TextRange;
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY")))
          return;
        string[] strArray = p_dicSData["DefY"].Split('|');
        double num = Math.Abs(clsReportUtill.ConvertToDouble(strArray[1])) + Math.Abs(clsReportUtill.ConvertToDouble(strArray[0]));
        // ISSUE: reference to a compiler-generated method
        textRange.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange.InsertAfter("Y방향 변형 – MAX : " + strArray[1] + "mm MIN : " + strArray[0] + "mm TOTAL : " + num.ToString() + "mm");
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]SetSlide25):" + ex.Message));
      }
    }

    private void SetSlide26(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 34.86614f, 163.8425f, 314.6457f, 340.157f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S26_Text_1")).FirstOrDefault<Shape>();
        if (shape == null)
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange = shape.TextFrame.TextRange;
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ")))
          return;
        string[] strArray = p_dicSData["DefZ"].Split('|');
        double num = Math.Abs(clsReportUtill.ConvertToDouble(strArray[1])) + Math.Abs(clsReportUtill.ConvertToDouble(strArray[0]));
        // ISSUE: reference to a compiler-generated method
        textRange.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange.InsertAfter("Z방향 변형 – MAX : " + strArray[1] + "mm MIN : " + strArray[0] + "mm TOTAL : " + num.ToString() + "mm");
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]SetSlide26):" + ex.Message));
      }
    }
  }
}
