Attribute VB_Name = "Sheet2"

Attribute VB_Base = "0{00020820-0000-0000-C000-000000000046}"

Attribute VB_GlobalNameSpace = False

Attribute VB_Creatable = False

Attribute VB_PredeclaredId = True

Attribute VB_Exposed = True

Attribute VB_TemplateDerived = False

Attribute VB_Customizable = True

'Dim EventsEnabled As Boolean

Private Sub Worksheet_Change(ByVal Target As Range)

'MsgBox (EventsEnabled.Value)

'If EventsEnabled Then Exit Sub

TDAY = Format(Date, "yyyy-mm-dd")

EDAY = "2025-12-31"

If TDAY > EDAY Then

    MsgBox ("사용날짜가 지났습니다. ED&C에 문의하세요." & vbCr & "02-2069-0099 / <EMAIL>")

    Exit Sub

End If

Dim sht As Worksheet

Set sht = Sheets("SRP 계산하기")



On Error Resume Next



Dim controlCell As Range

Set controlCell = sht.Range("J2")



Application.EnableEvents = False



' 보호된 시트를 잠금 해제

Dim sheetPassword As String

sheetPassword = "edncapi" ' 보호를 설정할 때 사용한 비밀번호로 바꿉니다.

Unprotect Password:=sheetPassword



Cells(7, "C").Value = Cells(6, "C").Value - Cells(5, "C").Value

Cells(13, "C").Value = Cells(10, "C").Value + Cells(11, "C").Value + Cells(12, "C").Value

Cells(6, "G").Value = Cells(5, "F").Value * Cells(6, "F").Value

Cells(5, "G").Value = (Cells(5, "F").Value * Cells(5, "F").Value) * 3.14 / 4 / 100

Cells(11, "F").Value = (Cells(10, "C").Value + Cells(11, "C").Value) * Cells(6, "C").Value

Cells(8, "F").Value = (Cells(7, "F").Value / Cells(5, "G").Value) * 10

Cells(11, "G").Value = (Cells(10, "C").Value + Cells(11, "C").Value) * Cells(5, "C").Value

Cells(11, "H").Value = (Cells(10, "C").Value + Cells(11, "C").Value) * Cells(6, "C").Value * (Cells(10, "H").Value / 100)

Cells(13, "F").Value = (10 * (Cells(11, "H").Value / (Cells(5, "G").Value * Cells(5, "C").Value * Cells(12, "F").Value))) + Cells(6, "G").Value

Cells(14, "F").Value = (10 * ((Cells(13, "C") - Cells(12, "C")) / Cells(5, "G").Value)) + Cells(6, "G")



'MsgBox ("작동 중")

Set cellG13 = Cells(13, "G")

Set cellG14 = Cells(14, "G")



Select Case Target.Address

    Case cellG13.Address

        Select Case cellG13.Value

            Case "선택"

                cellG14.Value = "해제"

                Cells(17, "E").Vlaue = Cells(13, "F").Value

            Case "해제"

                cellG14.Value = "선택"

                Cells(17, "E").Vlaue = Cells(14, "F").Value

                Cells(17, "H").Value = Cells(14, "F").Value

        End Select

    Case cellG14.Address

        Select Case cellG14.Value

            Case "선택"

                cellG13.Value = "해제"

                Cells(17, "E").Vlaue = Cells(14, "F").Value

                Cells(17, "H").Value = Cells(14, "F").Value

            Case "해제"

                cellG13.Value = "선택"

                Cells(17, "E").Vlaue = Cells(13, "F").Value

        End Select

End Select



If Cells(13, "G").Value = "선택" And Cells(14, "G").Value = "해제" Then

    Cells(17, "E").Value = Cells(13, "F").Value

    Cells(17, "H").Value = Cells(13, "F").Value

ElseIf Cells(13, "G").Value = "해제" And Cells(14, "G").Value = "선택" Then

    Cells(17, "E").Value = Cells(14, "F").Value

    Cells(17, "H").Value = Cells(14, "F").Value

    End If



Cells(17, "F") = Cells(17, "D") * Cells(7, "K").Value / 100



' 예: B17부터 시작하는 마지막 행을 찾기

lastRow = FindLastRow(17, "B")

rowCount = lastRow - 17

For i = 1 To rowCount

    If Cells(17 + i, "C") >= Cells(8, "K") Then

        Cells(17 + i, "E") = ((Cells(17, "E") - Cells(6, "G")) * ((100 - Cells(8, "K")) / 100)) + Cells(6, "G")

        Cells(17 + i, "F") = Cells(17 + i, "D") * Cells(7, "K").Value / 100

        Cells(8, "L") = ((Cells(17, "E") - Cells(6, "G")) * ((100 - Cells(8, "K")) / 100)) + Cells(6, "G")

        

    Else

        Cells(17 + i, "E") = Cells(17 + i - 1, "E") - ((Cells(17, "E").Value - Cells(6, "G").Value) * ((Cells(17 + i, "C") - Cells(17 + i - 1, "C")) / 100))

        Cells(17 + i, "F") = Cells(17 + i, "D") * Cells(7, "K").Value / 100

    End If

Next



Step_Setting = Cells(15, "I")



If Not Intersect(Target, Me.Range("I15")) Is Nothing Then

    rowCount = rowCount + 1

    

    For i = 1 To rowCount

        If i <= (rowCount / Step_Setting) Then

            Cells(16 + i, "G").Value = 1

        ElseIf i <= ((rowCount / Step_Setting) * 2) And i > (rowCount / Step_Setting) And Step_Setting >= 2 Then

            Cells(16 + i, "G").Value = 2

        ElseIf i <= ((rowCount / Step_Setting) * 3) And i > ((rowCount / Step_Setting) * 2) And Step_Setting >= 3 Then

            Cells(16 + i, "G").Value = 3

        ElseIf i <= ((rowCount / Step_Setting) * 4) And i > ((rowCount / Step_Setting) * 3) And Step_Setting >= 4 Then

            Cells(16 + i, "G").Value = 4

        ElseIf i <= ((rowCount / Step_Setting) * 5) And i > ((rowCount / Step_Setting) * 4) And Step_Setting >= 5 Then

            Cells(16 + i, "G").Value = 5

        ElseIf i <= ((rowCount / Step_Setting) * 6) And i > ((rowCount / Step_Setting) * 5) And Step_Setting >= 6 Then

            Cells(16 + i, "G").Value = 6

        ElseIf i <= ((rowCount / Step_Setting) * 7) And i > ((rowCount / Step_Setting) * 6) And Step_Setting >= 7 Then

            Cells(16 + i, "G").Value = 7

        ElseIf i <= ((rowCount / Step_Setting) * 8) And i > ((rowCount / Step_Setting) * 7) And Step_Setting >= 8 Then

            Cells(16 + i, "G").Value = 8

        ElseIf i <= ((rowCount / Step_Setting) * 9) And i > ((rowCount / Step_Setting) * 8) And Step_Setting >= 9 Then

            Cells(16 + i, "G").Value = 9

        ElseIf i < rowCount And i > ((rowCount / Step_Setting) * 9) And Step_Setting >= 10 Then

            Cells(16 + i, "G").Value = 10

        ElseIf i = rowCount Then

            Cells(16 + i, "G").Value = Step_Setting

        End If

    Next

End If



Set level_cell = Range("G17:G" & lastRow)

' Target이 level_cell 범위 내에 있는지 확인

If Not Intersect(Target, level_cell) Is Nothing Then

    For Each cell In Intersect(Target, level_cell)

        currentValue = cell.Value

        If IsNumeric(currentValue) And currentValue >= 1 And currentValue <= Step_Setting Then

            ' 앞의 셀 값 변경

            For i = cell.Row - 1 To 17 Step -1

                'MsgBox (cell.Row)

                If Cells(i, "G").Value - currentValue <= 0 Then

                    If currentValue - 1 = 0 Then Exit For

                    'Cells(i, "G").Value = currentValue - 1

                ElseIf Cells(i, "G").Value - currentValue > 0 Then

                    Cells(i, "G").Value = currentValue

                End If

            Next i

            ' 뒤의 셀 값 변경

            For i = cell.Row + 1 To lastRow

                If Cells(i, "G").Value - currentValue = 0 Then Exit For

                If Cells(i, "G").Value - currentValue < 0 Then Cells(i, "G").Value = currentValue

            Next i

        End If

    Next cell

End If



HH = 1

II = 1



For L = 0 To ((Step_Setting * 2) - 1) Step 2

    If L = 0 Then

        Cells(17 + L, "I") = 0

        ' AVERAGEIF 함수 적용

        Cells(18 + L, "I").Formula = "=AVERAGEIF(G17:G42," & II & ", F17:F42)"

        ' 일반 수식

        Cells(18 + L, "H").Formula = "=H17-IFERROR((0.5*((I18/J15)*J15^2)),0)"



    Else

        ' 배열 수식을 FormulaArray로 적용

        Cells(18 + L, "H").FormulaArray = "=MIN(IF(G17:G42=" & HH - 1 & ", E17:E42))-IFERROR(((" & Cells(17 + L, "I").Value & "*J15)+(0.5*((" & Cells(18 + L, "I").Value & "-" & Cells(17 + L, "I").Value & ")/J15)*J15^2)),0)"

        

    End If



    ' 배열 수식을 FormulaArray로 적용

    Cells(19 + L, "H").FormulaArray = "=MIN(IF(G17:G42=" & HH & ", E17:E42))"

    ' AVERAGEIF 함수 적용

    Cells(18 + L, "I").Formula = "=AVERAGEIF(G17:G42," & II & ", F17:F42)"

    Cells(19 + L, "I").Formula = "=AVERAGEIF(G17:G42," & II & ", F17:F42)"

    HH = HH + 1

    II = II + 1

Next



SR = 20 + ((Step_Setting - 1) * 2)

ER = SR + 1 + ((10 - Step_Setting) * 2)

Set Delete_Table = Range("H" & SR & ":I" & ER)

Delete_Table.ClearContents

        

If Cells(7, "K").Value > Cells(8, "F").Value * 0.85 Then

    Range("J7").Interior.ColorIndex = 3

    Range("J6").Interior.ColorIndex = 4

    Range("E8").Interior.ColorIndex = 4

    MsgBox ("시간에 의해 계산된 최대 램 속도가" & vbCr & "사출기 최대 속도의 85% 보다 큽니다." & vbCr & "사출시간 또는 사출기 최고 속도를 변경하세요.")

Else

    Range("J7").Interior.ColorIndex = 2

    Range("J6").Interior.ColorIndex = 2

    Range("E8").Interior.ColorIndex = 2

End If



'시트 보호를 다시 설정

Protect Password:=sheetPassword

Application.EnableEvents = True



End Sub



Function FindLastRow(startRow As Long, columnLetter As String) As Long

    Dim lastRow As Long

    

    ' 지정된 열의 마지막 행을 찾습니다.

    lastRow = Cells(Rows.count, columnLetter).End(xlUp).Row

    

    ' 시작 행부터 마지막 데이터를 찾습니다.

    If lastRow < startRow Then

        FindLastRow = -1 ' 시작 행 이후에 데이터가 없음을 나타내기 위해 -1 반환

    Else

        Do While IsEmpty(Cells(lastRow, columnLetter)) Or lastRow < startRow

            lastRow = lastRow - 1

        Loop

        

        If lastRow < startRow Then

            FindLastRow = -1 ' 시작 행 이후에 데이터가 없음을 나타내기 위해 -1 반환

        Else

            FindLastRow = lastRow

        End If

    End If

End Function



Function CustomFunction() As Double

    Dim E17 As Double

    Dim G6 As Double

    Dim K6 As Double

    Dim i As Integer

    Dim sumPart As Double

    Dim C As Range

    Dim D As Range

    Dim diff As Double

    

    ' 값 설정

    E17 = ThisWorkbook.Sheets("Sheet1").Range("E17").Value

    G6 = ThisWorkbook.Sheets("Sheet1").Range("G6").Value

    K6 = ThisWorkbook.Sheets("Sheet1").Range("K6").Value

    

    ' 초기 값 설정

    sumPart = 0

    

    ' C18:C42 범위와 D18:D42 범위 설정

    Set C = ThisWorkbook.Sheets("Sheet1").Range("C18:C42")

    Set D = ThisWorkbook.Sheets("Sheet1").Range("D18:D42")

    

    ' 합계 부분 계산

    For i = 1 To C.Rows.count - 1

        On Error Resume Next

        diff = (C.Cells(i + 1, 1).Value - C.Cells(i, 1).Value) / D.Cells(i + 1, 1).Value

        If Err.Number <> 0 Then

            diff = 0

            Err.Clear

        End If

        On Error GoTo 0

        sumPart = sumPart + diff

    Next i

    

    ' 최종 결과 계산

    CustomFunction = (E17 - G6) * (sumPart / K6)

End Function

