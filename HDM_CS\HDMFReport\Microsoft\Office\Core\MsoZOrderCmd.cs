﻿// Decompiled with JetBrains decompiler
// Type: Microsoft.Office.Core.MsoZOrderCmd
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

namespace Microsoft.Office.Core
{
  [CompilerGenerated]
  [TypeIdentifier("2df8d04c-5bfa-101b-bde5-00aa0044de52", "Microsoft.Office.Core.MsoZOrderCmd")]
  public enum MsoZOrderCmd
  {
    msoBringToFront,
    msoSendToBack,
    msoBringForward,
    msoSendBackward,
    msoBringInFrontOfText,
    msoSendBehindText,
  }
}
