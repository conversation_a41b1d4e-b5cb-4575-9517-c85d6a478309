﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmCase
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDLog4Net;
using HDMFUserControl;
using HDMoldFlow.Properties;
using HDMoldFlowLibrary;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public class frmCase : Form
  {
    private const int WS_EX_COMPOSITED = 33554432;
    public string m_strStudyName = string.Empty;
    public bool m_isAutoAnalysis = true;
    private Dictionary<string, string> m_dicCaseData = new Dictionary<string, string>();
    private IContainer components = (IContainer) null;
    private NewButton newButton_Apply;
    private CheckBox checkBox_Auto;
    private CheckBox checkBox_FC;
    private NewButton newButton_FC_Del;
    private NewButton newButton_FC_Add;
    private Label label_FC;
    private DataGridView dataGridView_FC;
    private Panel panel_FC;
    private Label label_FC_Default;
    private Panel panel_VP;
    private CheckBox checkBox_VP;
    private Label label_VP_Default;
    private DataGridView dataGridView_VP;
    private Label label_VP;
    private Panel panel_Cooling;
    private Label label_Cooling_Default;
    private DataGridView dataGridView_Cooling;
    private Label label_Cooling;
    private Panel panel_Melt;
    private Label label_Melt_Default;
    private DataGridView dataGridView_Melt;
    private Label label_Melt;
    private Panel panel_Mold;
    private Label label_Mold_Default;
    private DataGridView dataGridView_Mold;
    private Label label_Mold;
    private Panel panel_Packing;
    private DataGridView dataGridView_Packing;
    private Label label_Packing;
    private NewButton newButton_VP_Add;
    private NewButton newButton_VP_Del;
    private NewButton newButton_Cooling_Add;
    private NewButton newButton_Cooling_Del;
    private CheckBox checkBox_Cooling;
    private NewButton newButton_Melt_Add;
    private NewButton newButton_Melt_Del;
    private CheckBox checkBox_Melt;
    private NewButton newButton_Mold_Add;
    private NewButton newButton_Mold_Del;
    private CheckBox checkBox_Mold;
    private NewButton newButton_Packing_Add;
    private NewButton newButton_Packing_Del;
    private CheckBox checkBox_Packing;
    private Label label_Pressure3_Default;
    private Label label_Duration3_Default;
    private Label label_Pressure2_Default;
    private Label label_Duration2_Default;
    private Label label_Pressure1_Default;
    private Label label_Duration1_Default;
    private DataGridViewTextBoxColumn Column_VP;
    private DataGridViewTextBoxColumn Column_Cooling;
    private Label label_Pressure3;
    private Label label_Duration3;
    private Label label_Pressure2;
    private Label label_Duration2;
    private Label label_Pressure1;
    private Label label_Duration1;
    private DataGridViewTextBoxColumn Column_Duration1;
    private DataGridViewTextBoxColumn Column_Pressure1;
    private DataGridViewTextBoxColumn Column_Duration2;
    private DataGridViewTextBoxColumn Column_Pressure2;
    private DataGridViewTextBoxColumn Column_Duration3;
    private DataGridViewTextBoxColumn Column_Pressure3;
    private DataGridViewTextBoxColumn Column_Mold;
    private DataGridViewTextBoxColumn Column_Melt;
    private Label label_FC_Unit;
    private Label label_Melt_Unit;
    private Label label_Mold_Unit;
    private Label label_Duration1_Unit;
    private Label label_Pressure3_Unit;
    private Label label_Duration3_Unit;
    private Label label_Pressure2_Unit;
    private Label label_Duration2_Unit;
    private Label label_Pressure1_Unit;
    private Label label_VP_Unit;
    private DataGridViewTextBoxColumn Column_FC;
    private Label label_Cooling_Unit;

    protected override CreateParams CreateParams
    {
      get
      {
        CreateParams createParams = base.CreateParams;
        createParams.ExStyle |= 33554432;
        return createParams;
      }
    }

    public frmCase()
    {
      this.InitializeComponent();
      this.newButton_FC_Add.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_FC_Add.Image);
      this.newButton_FC_Del.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_FC_Del.Image);
      this.newButton_VP_Add.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_VP_Add.Image);
      this.newButton_VP_Del.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_VP_Del.Image);
      this.newButton_Melt_Add.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Melt_Add.Image);
      this.newButton_Melt_Del.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Melt_Del.Image);
      this.newButton_Mold_Add.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Mold_Add.Image);
      this.newButton_Mold_Del.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Mold_Del.Image);
      this.newButton_Cooling_Add.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Cooling_Add.Image);
      this.newButton_Cooling_Del.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Cooling_Del.Image);
      this.newButton_Packing_Add.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Packing_Add.Image);
      this.newButton_Packing_Del.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Packing_Del.Image);
      this.newButton_Apply.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Apply.Image);
      this.Text = LocaleControl.getInstance().GetString("IDS_CASE");
      this.newButton_Apply.ButtonText = LocaleControl.getInstance().GetString("IDS_APPLY");
    }

    private void frmCase_Load(object sender, EventArgs e)
    {
      this.LoadDataForCase();
      this.SetDefaultValue();
    }

    private void LoadDataForCase()
    {
      string empty = string.Empty;
      List<string> stringList = new List<string>();
      try
      {
        clsUtill.StartProgress("Open Study...", (Form) this);
        clsHDMFLib.OpenStudy(this.m_strStudyName);
        clsUtill.EndProgress();
        clsUtill.ShowForm((Form) this);
        string[] strArray1 = clsHDMFLib.GetFillingControlDataForCase().Split('|');
        this.m_dicCaseData.Add("FC_Type", strArray1[0]);
        this.m_dicCaseData.Add("FC_Default", strArray1[1]);
        int velocityPressureType = clsHDMFLib.GetVelocityPressureType();
        string velocityPressureData = clsHDMFLib.GetVelocityPressureData(velocityPressureType);
        this.m_dicCaseData.Add("VP_Type", velocityPressureType.ToString());
        this.m_dicCaseData.Add("VP_Default", velocityPressureData);
        int coolType = clsHDMFLib.GetCoolType();
        string str;
        bool flag;
        if (coolType == 1 || coolType == 2)
        {
          str = clsHDMFLib.GetCoolingTimeData(clsHDMFLib.GetCoolingTimeType());
          flag = false;
        }
        else
        {
          str = clsHDMFLib.GetInjPackCoolingtimeData(clsHDMFLib.GetInjPackCoolingtimeType());
          flag = true;
        }
        this.m_dicCaseData.Add("Cooling_Type", flag.ToString());
        this.m_dicCaseData.Add("Cooling_Default", str);
        this.m_dicCaseData.Add("Melt_Default", clsHDMFLib.GetMeltTemperatureDataFromProcessSet());
        this.m_dicCaseData.Add("Mold_Default", clsHDMFLib.GetMoldTemperatureDataFromProcessSet());
        this.m_dicCaseData.Add("Duration1_Default", "0");
        this.m_dicCaseData.Add("Pressure1_Default", "0");
        this.m_dicCaseData.Add("Duration2_Default", "0");
        this.m_dicCaseData.Add("Pressure2_Default", "0");
        this.m_dicCaseData.Add("Duration3_Default", "0");
        this.m_dicCaseData.Add("Pressure3_Default", "0");
        List<string> holdingControlData = clsHDMFLib.GetPackHoldingControlData(1);
        int num = 0;
        for (int index = 0; index < holdingControlData.Count; ++index)
        {
          if (index % 2 != 0)
          {
            string[] strArray2 = holdingControlData[index].Split('|');
            switch (num)
            {
              case 0:
                this.m_dicCaseData["Duration1_Default"] = strArray2[0];
                this.m_dicCaseData["Pressure1_Default"] = strArray2[1];
                break;
              case 1:
                this.m_dicCaseData["Duration2_Default"] = strArray2[0];
                this.m_dicCaseData["Pressure2_Default"] = strArray2[1];
                break;
              default:
                this.m_dicCaseData["Duration3_Default"] = strArray2[0];
                this.m_dicCaseData["Pressure3_Default"] = strArray2[1];
                break;
            }
            ++num;
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmCase]LoadDataForCase):" + ex.Message));
      }
    }

    private void SetDefaultValue()
    {
      try
      {
        this.label_FC_Default.Text = this.m_dicCaseData["FC_Default"];
        if (this.m_dicCaseData["FC_Type"] == "0")
          this.label_FC_Unit.Text = "S";
        else
          this.label_FC_Unit.Text = "cm\u00B3/s";
        this.label_VP_Default.Text = this.m_dicCaseData["VP_Default"];
        if (this.m_dicCaseData["VP_Type"] == "3")
          this.label_VP_Unit.Text = "mm";
        else if (this.m_dicCaseData["VP_Type"] == "2")
          this.label_VP_Unit.Text = "%";
        else
          this.label_VP_Unit.Text = "";
        this.label_Cooling_Default.Text = this.m_dicCaseData["Cooling_Default"];
        this.label_Melt_Default.Text = this.m_dicCaseData["Melt_Default"];
        this.label_Mold_Default.Text = this.m_dicCaseData["Mold_Default"];
        this.label_Duration1_Default.Text = this.m_dicCaseData["Duration1_Default"];
        this.label_Pressure1_Default.Text = this.m_dicCaseData["Pressure1_Default"];
        this.label_Duration2_Default.Text = this.m_dicCaseData["Duration2_Default"];
        this.label_Pressure2_Default.Text = this.m_dicCaseData["Pressure2_Default"];
        this.label_Duration3_Default.Text = this.m_dicCaseData["Duration3_Default"];
        this.label_Pressure3_Default.Text = this.m_dicCaseData["Pressure3_Default"];
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmCase]SetDefaultValue):" + ex.Message));
      }
    }

    private void newButton_Apply_NewClick(object sender, EventArgs e)
    {
      StringBuilder stringBuilder = new StringBuilder();
      List<string> p_lst_strData1 = new List<string>();
      List<string> p_lst_strData2 = new List<string>();
      List<string> p_lst_strData3 = new List<string>();
      List<string> p_lst_strData4 = new List<string>();
      List<string> p_lst_strData5 = new List<string>();
      List<string> p_lst_strData6 = new List<string>();
      try
      {
        clsDefine.g_dtCase.Rows.Clear();
        this.SetCaseData(ref p_lst_strData1, "FC");
        this.SetCaseData(ref p_lst_strData2, "VP");
        this.SetCaseData(ref p_lst_strData4, "Cooling");
        this.SetCaseData(ref p_lst_strData5, "Melt");
        this.SetCaseData(ref p_lst_strData6, "Mold");
        this.SetCaseData(ref p_lst_strData3, "Packing");
        this.GetAllNumberofCases(new string[6][]
        {
          p_lst_strData1.ToArray(),
          p_lst_strData2.ToArray(),
          p_lst_strData4.ToArray(),
          p_lst_strData5.ToArray(),
          p_lst_strData6.ToArray(),
          p_lst_strData3.ToArray()
        });
        this.m_isAutoAnalysis = this.checkBox_Auto.Checked;
        this.DialogResult = DialogResult.OK;
        this.Close();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmCase]SetCaseData):" + ex.Message));
      }
    }

    private void GetAllNumberofCases(string[][] p_arr_strCase, int p_iIndex = 0, string p_strValue = "")
    {
      int length = p_arr_strCase.Length;
      try
      {
        if (p_iIndex == length)
        {
          string[] strArray = p_strValue.Split(',');
          DataRow dataRow = clsDefine.g_dtCase.Rows.Add();
          dataRow["FC"] = (object) strArray[0];
          dataRow["VP"] = (object) strArray[1];
          dataRow["Cooling"] = (object) strArray[2];
          dataRow["Melt"] = (object) strArray[3];
          dataRow["Mold"] = (object) strArray[4];
          dataRow["Packing"] = (object) strArray[5];
        }
        else
        {
          string[] strArray = p_arr_strCase[p_iIndex];
          for (int index = 0; index < strArray.Length; ++index)
          {
            string str = strArray[index];
            string p_strValue1 = p_strValue + (str + ",");
            if (p_iIndex != 0 && index == length - 1)
              break;
            this.GetAllNumberofCases(p_arr_strCase, p_iIndex + 1, p_strValue1);
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmCase]GetAllCase):" + ex.Message));
      }
    }

    private void SetCaseData(ref List<string> p_lst_strData, string p_strType)
    {
      StringBuilder stringBuilder = new StringBuilder();
      string empty = string.Empty;
      try
      {
        Panel panel = this.Controls.OfType<Panel>().Where<Panel>((System.Func<Panel, bool>) (Temp => Temp.Name.Contains(p_strType))).FirstOrDefault<Panel>();
        CheckBox checkBox = panel.Controls.OfType<CheckBox>().FirstOrDefault<CheckBox>();
        DataGridView dgvData = panel.Controls.OfType<DataGridView>().FirstOrDefault<DataGridView>();
        if (checkBox.Checked && dgvData.Rows.Count > 0)
        {
          foreach (DataGridViewRow row in (IEnumerable) dgvData.Rows)
          {
            stringBuilder.Clear();
            if (p_strType == "Packing")
            {
              for (int index = 0; index < dgvData.Columns.Count; ++index)
              {
                if (row.Cells[index].Value != null)
                  stringBuilder.Append(row.Cells[index].Value.ToString());
                else
                  stringBuilder.Append(0);
                if (index % 2 == 0)
                  stringBuilder.Append("|");
                else if (index % 2 == 1 && index != dgvData.Columns.Count - 1)
                  stringBuilder.Append("/");
              }
              p_lst_strData.Add(stringBuilder.ToString());
            }
            else
            {
              if (this.m_dicCaseData.ContainsKey(p_strType + "_Type") && this.m_dicCaseData[p_strType + "_Type"] != null)
                stringBuilder.Append(this.m_dicCaseData[p_strType + "_Type"] + "|");
              if (row.Cells[0].Value != null)
                stringBuilder.Append(row.Cells[0].Value.ToString());
              else
                stringBuilder.Append("0");
              p_lst_strData.Add(stringBuilder.ToString());
            }
          }
        }
        else if (p_strType == "Packing")
        {
          stringBuilder.Clear();
          for (int i = 0; i < dgvData.Columns.Count; i++)
          {
            string str = this.m_dicCaseData.Where<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key.Contains(dgvData.Columns[i].HeaderText + "_Default"))).Select<KeyValuePair<string, string>, string>((System.Func<KeyValuePair<string, string>, string>) (Temp => Temp.Value)).FirstOrDefault<string>() ?? "0";
            stringBuilder.Append(str);
            if (i % 2 == 0)
              stringBuilder.Append("|");
            else if (i % 2 == 1 && i != dgvData.Columns.Count - 1)
              stringBuilder.Append("/");
          }
          p_lst_strData.Add(stringBuilder.ToString());
        }
        else
        {
          if (this.m_dicCaseData.ContainsKey(p_strType + "_Type") && this.m_dicCaseData[p_strType + "_Type"] != null)
            stringBuilder.Append(this.m_dicCaseData[p_strType + "_Type"] + "|");
          stringBuilder.Append(this.m_dicCaseData[p_strType + "_Default"]);
          p_lst_strData.Add(stringBuilder.ToString());
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmCase]SetCaseData):" + ex.Message));
      }
    }

    private void frmCase_KeyDown(object sender, KeyEventArgs e)
    {
      if (e.KeyCode != Keys.Escape)
        return;
      this.Close();
    }

    private void newButton_Add_NewClick(object sender, EventArgs e)
    {
      DataGridView dataGridView = (DataGridView) null;
      NewButton newButton = sender as NewButton;
      if (newButton == this.newButton_FC_Add)
        dataGridView = this.dataGridView_FC;
      else if (newButton == this.newButton_VP_Add)
        dataGridView = this.dataGridView_VP;
      else if (newButton == this.newButton_Packing_Add)
        dataGridView = this.dataGridView_Packing;
      else if (newButton == this.newButton_Melt_Add)
        dataGridView = this.dataGridView_Melt;
      else if (newButton == this.newButton_Mold_Add)
        dataGridView = this.dataGridView_Mold;
      else if (newButton == this.newButton_Cooling_Add)
        dataGridView = this.dataGridView_Cooling;
      dataGridView.Rows.Add();
      dataGridView.ClearSelection();
    }

    private void newButton_Del_NewClick(object sender, EventArgs e)
    {
      List<DataGridViewRow> dataGridViewRowList = new List<DataGridViewRow>();
      DataGridView dataGridView = (DataGridView) null;
      NewButton newButton = sender as NewButton;
      if (newButton == this.newButton_FC_Del)
        dataGridView = this.dataGridView_FC;
      else if (newButton == this.newButton_VP_Del)
        dataGridView = this.dataGridView_VP;
      else if (newButton == this.newButton_Packing_Del)
        dataGridView = this.dataGridView_Packing;
      else if (newButton == this.newButton_Melt_Del)
        dataGridView = this.dataGridView_Melt;
      else if (newButton == this.newButton_Mold_Del)
        dataGridView = this.dataGridView_Mold;
      else if (newButton == this.newButton_Cooling_Del)
        dataGridView = this.dataGridView_Cooling;
      try
      {
        foreach (DataGridViewCell selectedCell in (BaseCollection) dataGridView.SelectedCells)
        {
          if (!dataGridViewRowList.Contains(dataGridView.Rows[selectedCell.RowIndex]))
            dataGridViewRowList.Add(dataGridView.Rows[selectedCell.RowIndex]);
        }
        foreach (DataGridViewRow dataGridViewRow in dataGridViewRowList)
          dataGridView.Rows.Remove(dataGridViewRow);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmCase]newButton_Del_NewClick):" + ex.Message));
      }
      dataGridView.ClearSelection();
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      DataGridViewCellStyle gridViewCellStyle1 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle2 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle3 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle4 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle5 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle6 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle7 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle8 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle9 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle10 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle11 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle12 = new DataGridViewCellStyle();
      this.checkBox_Auto = new CheckBox();
      this.checkBox_FC = new CheckBox();
      this.label_FC = new Label();
      this.dataGridView_FC = new DataGridView();
      this.Column_FC = new DataGridViewTextBoxColumn();
      this.panel_FC = new Panel();
      this.label_FC_Unit = new Label();
      this.newButton_FC_Add = new NewButton();
      this.newButton_FC_Del = new NewButton();
      this.label_FC_Default = new Label();
      this.panel_VP = new Panel();
      this.label_VP_Unit = new Label();
      this.newButton_VP_Add = new NewButton();
      this.newButton_VP_Del = new NewButton();
      this.checkBox_VP = new CheckBox();
      this.label_VP_Default = new Label();
      this.dataGridView_VP = new DataGridView();
      this.Column_VP = new DataGridViewTextBoxColumn();
      this.label_VP = new Label();
      this.panel_Cooling = new Panel();
      this.label_Cooling_Unit = new Label();
      this.newButton_Cooling_Add = new NewButton();
      this.newButton_Cooling_Del = new NewButton();
      this.checkBox_Cooling = new CheckBox();
      this.label_Cooling_Default = new Label();
      this.dataGridView_Cooling = new DataGridView();
      this.Column_Cooling = new DataGridViewTextBoxColumn();
      this.label_Cooling = new Label();
      this.panel_Melt = new Panel();
      this.label_Melt_Unit = new Label();
      this.newButton_Melt_Add = new NewButton();
      this.newButton_Melt_Del = new NewButton();
      this.checkBox_Melt = new CheckBox();
      this.label_Melt_Default = new Label();
      this.dataGridView_Melt = new DataGridView();
      this.Column_Melt = new DataGridViewTextBoxColumn();
      this.label_Melt = new Label();
      this.panel_Mold = new Panel();
      this.label_Mold_Unit = new Label();
      this.newButton_Mold_Add = new NewButton();
      this.newButton_Mold_Del = new NewButton();
      this.checkBox_Mold = new CheckBox();
      this.label_Mold_Default = new Label();
      this.dataGridView_Mold = new DataGridView();
      this.Column_Mold = new DataGridViewTextBoxColumn();
      this.label_Mold = new Label();
      this.panel_Packing = new Panel();
      this.label_Pressure3_Unit = new Label();
      this.label_Duration3_Unit = new Label();
      this.label_Pressure2_Unit = new Label();
      this.label_Duration2_Unit = new Label();
      this.label_Pressure1_Unit = new Label();
      this.label_Duration1_Unit = new Label();
      this.label_Pressure3 = new Label();
      this.label_Duration3 = new Label();
      this.label_Pressure2 = new Label();
      this.label_Duration2 = new Label();
      this.label_Pressure1 = new Label();
      this.label_Duration1 = new Label();
      this.label_Pressure3_Default = new Label();
      this.label_Duration3_Default = new Label();
      this.label_Pressure2_Default = new Label();
      this.label_Duration2_Default = new Label();
      this.label_Pressure1_Default = new Label();
      this.label_Duration1_Default = new Label();
      this.newButton_Packing_Add = new NewButton();
      this.newButton_Packing_Del = new NewButton();
      this.checkBox_Packing = new CheckBox();
      this.dataGridView_Packing = new DataGridView();
      this.Column_Duration1 = new DataGridViewTextBoxColumn();
      this.Column_Pressure1 = new DataGridViewTextBoxColumn();
      this.Column_Duration2 = new DataGridViewTextBoxColumn();
      this.Column_Pressure2 = new DataGridViewTextBoxColumn();
      this.Column_Duration3 = new DataGridViewTextBoxColumn();
      this.Column_Pressure3 = new DataGridViewTextBoxColumn();
      this.label_Packing = new Label();
      this.newButton_Apply = new NewButton();
      ((ISupportInitialize) this.dataGridView_FC).BeginInit();
      this.panel_FC.SuspendLayout();
      this.panel_VP.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_VP).BeginInit();
      this.panel_Cooling.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_Cooling).BeginInit();
      this.panel_Melt.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_Melt).BeginInit();
      this.panel_Mold.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_Mold).BeginInit();
      this.panel_Packing.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_Packing).BeginInit();
      this.SuspendLayout();
      this.checkBox_Auto.Checked = true;
      this.checkBox_Auto.CheckState = CheckState.Checked;
      this.checkBox_Auto.Location = new Point(14, 10);
      this.checkBox_Auto.Name = "checkBox_Auto";
      this.checkBox_Auto.Size = new Size(78, 24);
      this.checkBox_Auto.TabIndex = 51;
      this.checkBox_Auto.Text = "자동 해석";
      this.checkBox_Auto.TextAlign = ContentAlignment.MiddleCenter;
      this.checkBox_Auto.UseVisualStyleBackColor = true;
      this.checkBox_FC.Location = new Point(12, 3);
      this.checkBox_FC.Name = "checkBox_FC";
      this.checkBox_FC.Size = new Size(14, 14);
      this.checkBox_FC.TabIndex = 0;
      this.checkBox_FC.TextAlign = ContentAlignment.MiddleCenter;
      this.checkBox_FC.UseVisualStyleBackColor = true;
      this.label_FC.BackColor = Color.FromArgb(229, 238, 248);
      this.label_FC.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_FC.ForeColor = Color.MidnightBlue;
      this.label_FC.Location = new Point(-1, -1);
      this.label_FC.Name = "label_FC";
      this.label_FC.Size = new Size(205, 21);
      this.label_FC.TabIndex = 64;
      this.label_FC.Text = "Filling Control";
      this.label_FC.TextAlign = ContentAlignment.MiddleCenter;
      this.dataGridView_FC.AllowUserToAddRows = false;
      this.dataGridView_FC.AllowUserToDeleteRows = false;
      this.dataGridView_FC.AllowUserToResizeColumns = false;
      this.dataGridView_FC.AllowUserToResizeRows = false;
      this.dataGridView_FC.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_FC.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_FC.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
      this.dataGridView_FC.BackgroundColor = Color.White;
      gridViewCellStyle1.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle1.BackColor = Color.Lavender;
      gridViewCellStyle1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle1.ForeColor = SystemColors.WindowText;
      gridViewCellStyle1.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle1.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle1.WrapMode = DataGridViewTriState.True;
      this.dataGridView_FC.ColumnHeadersDefaultCellStyle = gridViewCellStyle1;
      this.dataGridView_FC.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_FC.ColumnHeadersVisible = false;
      this.dataGridView_FC.Columns.AddRange((DataGridViewColumn) this.Column_FC);
      gridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle2.BackColor = SystemColors.Window;
      gridViewCellStyle2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle2.ForeColor = SystemColors.ControlText;
      gridViewCellStyle2.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle2.WrapMode = DataGridViewTriState.False;
      this.dataGridView_FC.DefaultCellStyle = gridViewCellStyle2;
      this.dataGridView_FC.EnableHeadersVisualStyles = false;
      this.dataGridView_FC.Location = new Point(-2, 38);
      this.dataGridView_FC.Name = "dataGridView_FC";
      this.dataGridView_FC.RowHeadersVisible = false;
      this.dataGridView_FC.RowTemplate.Height = 23;
      this.dataGridView_FC.Size = new Size(207, 112);
      this.dataGridView_FC.TabIndex = 85;
      this.Column_FC.HeaderText = "Filling Control";
      this.Column_FC.Name = "Column_FC";
      this.Column_FC.Resizable = DataGridViewTriState.False;
      this.Column_FC.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.panel_FC.BorderStyle = BorderStyle.FixedSingle;
      this.panel_FC.Controls.Add((Control) this.label_FC_Unit);
      this.panel_FC.Controls.Add((Control) this.checkBox_FC);
      this.panel_FC.Controls.Add((Control) this.newButton_FC_Add);
      this.panel_FC.Controls.Add((Control) this.newButton_FC_Del);
      this.panel_FC.Controls.Add((Control) this.label_FC_Default);
      this.panel_FC.Controls.Add((Control) this.label_FC);
      this.panel_FC.Controls.Add((Control) this.dataGridView_FC);
      this.panel_FC.Location = new Point(12, 40);
      this.panel_FC.Name = "panel_FC";
      this.panel_FC.Size = new Size(205, 150);
      this.panel_FC.TabIndex = 86;
      this.label_FC_Unit.BackColor = Color.LavenderBlush;
      this.label_FC_Unit.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_FC_Unit.ForeColor = SystemColors.ControlText;
      this.label_FC_Unit.Location = new Point(159, 20);
      this.label_FC_Unit.Name = "label_FC_Unit";
      this.label_FC_Unit.Size = new Size(45, 18);
      this.label_FC_Unit.TabIndex = 87;
      this.label_FC_Unit.Text = "S";
      this.label_FC_Unit.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_FC_Add.BackColor = Color.Lavender;
      this.newButton_FC_Add.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_FC_Add.ButtonText = "";
      this.newButton_FC_Add.FlatBorderSize = 0;
      this.newButton_FC_Add.FlatStyle = FlatStyle.Flat;
      this.newButton_FC_Add.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_FC_Add.Image = (Image) Resources.Add;
      this.newButton_FC_Add.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_FC_Add.Location = new Point(166, 2);
      this.newButton_FC_Add.Name = "newButton_FC_Add";
      this.newButton_FC_Add.Size = new Size(15, 15);
      this.newButton_FC_Add.TabIndex = 7;
      this.newButton_FC_Add.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_FC_Add.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_FC_Add.NewClick += new EventHandler(this.newButton_Add_NewClick);
      this.newButton_FC_Del.BackColor = Color.Lavender;
      this.newButton_FC_Del.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_FC_Del.ButtonText = "";
      this.newButton_FC_Del.FlatBorderSize = 0;
      this.newButton_FC_Del.FlatStyle = FlatStyle.Flat;
      this.newButton_FC_Del.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_FC_Del.Image = (Image) Resources.Del;
      this.newButton_FC_Del.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_FC_Del.Location = new Point(185, 2);
      this.newButton_FC_Del.Name = "newButton_FC_Del";
      this.newButton_FC_Del.Size = new Size(15, 15);
      this.newButton_FC_Del.TabIndex = 8;
      this.newButton_FC_Del.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_FC_Del.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_FC_Del.NewClick += new EventHandler(this.newButton_Del_NewClick);
      this.label_FC_Default.BackColor = Color.LavenderBlush;
      this.label_FC_Default.BorderStyle = BorderStyle.FixedSingle;
      this.label_FC_Default.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_FC_Default.ForeColor = SystemColors.ControlText;
      this.label_FC_Default.Location = new Point(-1, 19);
      this.label_FC_Default.Name = "label_FC_Default";
      this.label_FC_Default.Size = new Size(205, 21);
      this.label_FC_Default.TabIndex = 86;
      this.label_FC_Default.Text = "0";
      this.label_FC_Default.TextAlign = ContentAlignment.MiddleCenter;
      this.panel_VP.BorderStyle = BorderStyle.FixedSingle;
      this.panel_VP.Controls.Add((Control) this.label_VP_Unit);
      this.panel_VP.Controls.Add((Control) this.newButton_VP_Add);
      this.panel_VP.Controls.Add((Control) this.newButton_VP_Del);
      this.panel_VP.Controls.Add((Control) this.checkBox_VP);
      this.panel_VP.Controls.Add((Control) this.label_VP_Default);
      this.panel_VP.Controls.Add((Control) this.dataGridView_VP);
      this.panel_VP.Controls.Add((Control) this.label_VP);
      this.panel_VP.Location = new Point(12, 189);
      this.panel_VP.Name = "panel_VP";
      this.panel_VP.Size = new Size(205, 150);
      this.panel_VP.TabIndex = 87;
      this.label_VP_Unit.BackColor = Color.LavenderBlush;
      this.label_VP_Unit.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_VP_Unit.ForeColor = SystemColors.ControlText;
      this.label_VP_Unit.Location = new Point(159, 20);
      this.label_VP_Unit.Name = "label_VP_Unit";
      this.label_VP_Unit.Size = new Size(45, 18);
      this.label_VP_Unit.TabIndex = 88;
      this.label_VP_Unit.Text = "%";
      this.label_VP_Unit.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_VP_Add.BackColor = Color.FromArgb(229, 238, 248);
      this.newButton_VP_Add.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_VP_Add.ButtonText = "";
      this.newButton_VP_Add.FlatBorderSize = 0;
      this.newButton_VP_Add.FlatStyle = FlatStyle.Flat;
      this.newButton_VP_Add.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_VP_Add.Image = (Image) Resources.Add;
      this.newButton_VP_Add.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_VP_Add.Location = new Point(166, 2);
      this.newButton_VP_Add.Name = "newButton_VP_Add";
      this.newButton_VP_Add.Size = new Size(15, 15);
      this.newButton_VP_Add.TabIndex = 7;
      this.newButton_VP_Add.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_VP_Add.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_VP_Add.NewClick += new EventHandler(this.newButton_Add_NewClick);
      this.newButton_VP_Del.BackColor = Color.FromArgb(229, 238, 248);
      this.newButton_VP_Del.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_VP_Del.ButtonText = "";
      this.newButton_VP_Del.FlatBorderSize = 0;
      this.newButton_VP_Del.FlatStyle = FlatStyle.Flat;
      this.newButton_VP_Del.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_VP_Del.Image = (Image) Resources.Del;
      this.newButton_VP_Del.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_VP_Del.Location = new Point(185, 2);
      this.newButton_VP_Del.Name = "newButton_VP_Del";
      this.newButton_VP_Del.Size = new Size(15, 15);
      this.newButton_VP_Del.TabIndex = 8;
      this.newButton_VP_Del.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_VP_Del.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_VP_Del.NewClick += new EventHandler(this.newButton_Del_NewClick);
      this.checkBox_VP.Location = new Point(12, 3);
      this.checkBox_VP.Name = "checkBox_VP";
      this.checkBox_VP.Size = new Size(14, 14);
      this.checkBox_VP.TabIndex = 0;
      this.checkBox_VP.TextAlign = ContentAlignment.MiddleCenter;
      this.checkBox_VP.UseVisualStyleBackColor = true;
      this.label_VP_Default.BackColor = Color.LavenderBlush;
      this.label_VP_Default.BorderStyle = BorderStyle.FixedSingle;
      this.label_VP_Default.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_VP_Default.ForeColor = SystemColors.ControlText;
      this.label_VP_Default.Location = new Point(-1, 19);
      this.label_VP_Default.Name = "label_VP_Default";
      this.label_VP_Default.Size = new Size(205, 21);
      this.label_VP_Default.TabIndex = 86;
      this.label_VP_Default.Text = "0";
      this.label_VP_Default.TextAlign = ContentAlignment.MiddleCenter;
      this.dataGridView_VP.AllowUserToAddRows = false;
      this.dataGridView_VP.AllowUserToDeleteRows = false;
      this.dataGridView_VP.AllowUserToResizeColumns = false;
      this.dataGridView_VP.AllowUserToResizeRows = false;
      this.dataGridView_VP.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_VP.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_VP.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
      this.dataGridView_VP.BackgroundColor = Color.White;
      gridViewCellStyle3.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle3.BackColor = Color.Lavender;
      gridViewCellStyle3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle3.ForeColor = SystemColors.WindowText;
      gridViewCellStyle3.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle3.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle3.WrapMode = DataGridViewTriState.True;
      this.dataGridView_VP.ColumnHeadersDefaultCellStyle = gridViewCellStyle3;
      this.dataGridView_VP.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_VP.ColumnHeadersVisible = false;
      this.dataGridView_VP.Columns.AddRange((DataGridViewColumn) this.Column_VP);
      gridViewCellStyle4.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle4.BackColor = SystemColors.Window;
      gridViewCellStyle4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle4.ForeColor = SystemColors.ControlText;
      gridViewCellStyle4.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle4.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle4.WrapMode = DataGridViewTriState.False;
      this.dataGridView_VP.DefaultCellStyle = gridViewCellStyle4;
      this.dataGridView_VP.EnableHeadersVisualStyles = false;
      this.dataGridView_VP.Location = new Point(-2, 38);
      this.dataGridView_VP.Name = "dataGridView_VP";
      this.dataGridView_VP.RowHeadersVisible = false;
      this.dataGridView_VP.RowTemplate.Height = 23;
      this.dataGridView_VP.Size = new Size(207, 112);
      this.dataGridView_VP.TabIndex = 85;
      this.Column_VP.HeaderText = "V/P Switch";
      this.Column_VP.Name = "Column_VP";
      this.Column_VP.Resizable = DataGridViewTriState.False;
      this.Column_VP.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.label_VP.BackColor = Color.FromArgb(229, 238, 248);
      this.label_VP.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_VP.ForeColor = Color.MidnightBlue;
      this.label_VP.Location = new Point(-1, -1);
      this.label_VP.Name = "label_VP";
      this.label_VP.Size = new Size(205, 21);
      this.label_VP.TabIndex = 64;
      this.label_VP.Text = "V/P Switch";
      this.label_VP.TextAlign = ContentAlignment.MiddleCenter;
      this.panel_Cooling.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Cooling.Controls.Add((Control) this.label_Cooling_Unit);
      this.panel_Cooling.Controls.Add((Control) this.newButton_Cooling_Add);
      this.panel_Cooling.Controls.Add((Control) this.newButton_Cooling_Del);
      this.panel_Cooling.Controls.Add((Control) this.checkBox_Cooling);
      this.panel_Cooling.Controls.Add((Control) this.label_Cooling_Default);
      this.panel_Cooling.Controls.Add((Control) this.dataGridView_Cooling);
      this.panel_Cooling.Controls.Add((Control) this.label_Cooling);
      this.panel_Cooling.Location = new Point(420, 40);
      this.panel_Cooling.Name = "panel_Cooling";
      this.panel_Cooling.Size = new Size(205, 150);
      this.panel_Cooling.TabIndex = 88;
      this.label_Cooling_Unit.BackColor = Color.LavenderBlush;
      this.label_Cooling_Unit.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Cooling_Unit.ForeColor = SystemColors.ControlText;
      this.label_Cooling_Unit.Location = new Point(159, 20);
      this.label_Cooling_Unit.Name = "label_Cooling_Unit";
      this.label_Cooling_Unit.Size = new Size(45, 18);
      this.label_Cooling_Unit.TabIndex = 89;
      this.label_Cooling_Unit.Text = "S";
      this.label_Cooling_Unit.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Cooling_Add.BackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Cooling_Add.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Cooling_Add.ButtonText = "";
      this.newButton_Cooling_Add.FlatBorderSize = 0;
      this.newButton_Cooling_Add.FlatStyle = FlatStyle.Flat;
      this.newButton_Cooling_Add.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Cooling_Add.Image = (Image) Resources.Add;
      this.newButton_Cooling_Add.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Cooling_Add.Location = new Point(166, 2);
      this.newButton_Cooling_Add.Name = "newButton_Cooling_Add";
      this.newButton_Cooling_Add.Size = new Size(15, 15);
      this.newButton_Cooling_Add.TabIndex = 7;
      this.newButton_Cooling_Add.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Cooling_Add.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Cooling_Add.NewClick += new EventHandler(this.newButton_Add_NewClick);
      this.newButton_Cooling_Del.BackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Cooling_Del.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Cooling_Del.ButtonText = "";
      this.newButton_Cooling_Del.FlatBorderSize = 0;
      this.newButton_Cooling_Del.FlatStyle = FlatStyle.Flat;
      this.newButton_Cooling_Del.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Cooling_Del.Image = (Image) Resources.Del;
      this.newButton_Cooling_Del.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Cooling_Del.Location = new Point(185, 2);
      this.newButton_Cooling_Del.Name = "newButton_Cooling_Del";
      this.newButton_Cooling_Del.Size = new Size(15, 15);
      this.newButton_Cooling_Del.TabIndex = 8;
      this.newButton_Cooling_Del.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Cooling_Del.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Cooling_Del.NewClick += new EventHandler(this.newButton_Del_NewClick);
      this.checkBox_Cooling.Location = new Point(12, 3);
      this.checkBox_Cooling.Name = "checkBox_Cooling";
      this.checkBox_Cooling.Size = new Size(14, 14);
      this.checkBox_Cooling.TabIndex = 0;
      this.checkBox_Cooling.TextAlign = ContentAlignment.MiddleCenter;
      this.checkBox_Cooling.UseVisualStyleBackColor = true;
      this.label_Cooling_Default.BackColor = Color.LavenderBlush;
      this.label_Cooling_Default.BorderStyle = BorderStyle.FixedSingle;
      this.label_Cooling_Default.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Cooling_Default.ForeColor = SystemColors.ControlText;
      this.label_Cooling_Default.Location = new Point(-1, 19);
      this.label_Cooling_Default.Name = "label_Cooling_Default";
      this.label_Cooling_Default.Size = new Size(205, 21);
      this.label_Cooling_Default.TabIndex = 86;
      this.label_Cooling_Default.Text = "0";
      this.label_Cooling_Default.TextAlign = ContentAlignment.MiddleCenter;
      this.dataGridView_Cooling.AllowUserToAddRows = false;
      this.dataGridView_Cooling.AllowUserToDeleteRows = false;
      this.dataGridView_Cooling.AllowUserToResizeColumns = false;
      this.dataGridView_Cooling.AllowUserToResizeRows = false;
      this.dataGridView_Cooling.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_Cooling.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_Cooling.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
      this.dataGridView_Cooling.BackgroundColor = Color.White;
      gridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle5.BackColor = Color.Lavender;
      gridViewCellStyle5.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle5.ForeColor = SystemColors.WindowText;
      gridViewCellStyle5.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle5.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle5.WrapMode = DataGridViewTriState.True;
      this.dataGridView_Cooling.ColumnHeadersDefaultCellStyle = gridViewCellStyle5;
      this.dataGridView_Cooling.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_Cooling.ColumnHeadersVisible = false;
      this.dataGridView_Cooling.Columns.AddRange((DataGridViewColumn) this.Column_Cooling);
      gridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle6.BackColor = SystemColors.Window;
      gridViewCellStyle6.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle6.ForeColor = SystemColors.ControlText;
      gridViewCellStyle6.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle6.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle6.WrapMode = DataGridViewTriState.False;
      this.dataGridView_Cooling.DefaultCellStyle = gridViewCellStyle6;
      this.dataGridView_Cooling.EnableHeadersVisualStyles = false;
      this.dataGridView_Cooling.Location = new Point(-2, 38);
      this.dataGridView_Cooling.Name = "dataGridView_Cooling";
      this.dataGridView_Cooling.RowHeadersVisible = false;
      this.dataGridView_Cooling.RowTemplate.Height = 23;
      this.dataGridView_Cooling.Size = new Size(207, 112);
      this.dataGridView_Cooling.TabIndex = 85;
      this.Column_Cooling.HeaderText = "Cooling Time";
      this.Column_Cooling.Name = "Column_Cooling";
      this.Column_Cooling.Resizable = DataGridViewTriState.False;
      this.Column_Cooling.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.label_Cooling.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Cooling.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Cooling.ForeColor = Color.MidnightBlue;
      this.label_Cooling.Location = new Point(-1, -1);
      this.label_Cooling.Name = "label_Cooling";
      this.label_Cooling.Size = new Size(205, 21);
      this.label_Cooling.TabIndex = 64;
      this.label_Cooling.Text = "Cooling Time";
      this.label_Cooling.TextAlign = ContentAlignment.MiddleCenter;
      this.panel_Melt.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Melt.Controls.Add((Control) this.label_Melt_Unit);
      this.panel_Melt.Controls.Add((Control) this.newButton_Melt_Add);
      this.panel_Melt.Controls.Add((Control) this.newButton_Melt_Del);
      this.panel_Melt.Controls.Add((Control) this.checkBox_Melt);
      this.panel_Melt.Controls.Add((Control) this.label_Melt_Default);
      this.panel_Melt.Controls.Add((Control) this.dataGridView_Melt);
      this.panel_Melt.Controls.Add((Control) this.label_Melt);
      this.panel_Melt.Location = new Point(216, 40);
      this.panel_Melt.Name = "panel_Melt";
      this.panel_Melt.Size = new Size(205, 150);
      this.panel_Melt.TabIndex = 89;
      this.label_Melt_Unit.BackColor = Color.LavenderBlush;
      this.label_Melt_Unit.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Melt_Unit.ForeColor = SystemColors.ControlText;
      this.label_Melt_Unit.Location = new Point(159, 20);
      this.label_Melt_Unit.Name = "label_Melt_Unit";
      this.label_Melt_Unit.Size = new Size(45, 18);
      this.label_Melt_Unit.TabIndex = 88;
      this.label_Melt_Unit.Text = "℃";
      this.label_Melt_Unit.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Melt_Add.BackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Melt_Add.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Melt_Add.ButtonText = "";
      this.newButton_Melt_Add.FlatBorderSize = 0;
      this.newButton_Melt_Add.FlatStyle = FlatStyle.Flat;
      this.newButton_Melt_Add.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Melt_Add.Image = (Image) Resources.Add;
      this.newButton_Melt_Add.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Melt_Add.Location = new Point(166, 2);
      this.newButton_Melt_Add.Name = "newButton_Melt_Add";
      this.newButton_Melt_Add.Size = new Size(15, 15);
      this.newButton_Melt_Add.TabIndex = 7;
      this.newButton_Melt_Add.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Melt_Add.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Melt_Add.NewClick += new EventHandler(this.newButton_Add_NewClick);
      this.newButton_Melt_Del.BackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Melt_Del.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Melt_Del.ButtonText = "";
      this.newButton_Melt_Del.FlatBorderSize = 0;
      this.newButton_Melt_Del.FlatStyle = FlatStyle.Flat;
      this.newButton_Melt_Del.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Melt_Del.Image = (Image) Resources.Del;
      this.newButton_Melt_Del.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Melt_Del.Location = new Point(185, 2);
      this.newButton_Melt_Del.Name = "newButton_Melt_Del";
      this.newButton_Melt_Del.Size = new Size(15, 15);
      this.newButton_Melt_Del.TabIndex = 8;
      this.newButton_Melt_Del.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Melt_Del.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Melt_Del.NewClick += new EventHandler(this.newButton_Del_NewClick);
      this.checkBox_Melt.Location = new Point(12, 3);
      this.checkBox_Melt.Name = "checkBox_Melt";
      this.checkBox_Melt.Size = new Size(14, 14);
      this.checkBox_Melt.TabIndex = 0;
      this.checkBox_Melt.TextAlign = ContentAlignment.MiddleCenter;
      this.checkBox_Melt.UseVisualStyleBackColor = true;
      this.label_Melt_Default.BackColor = Color.LavenderBlush;
      this.label_Melt_Default.BorderStyle = BorderStyle.FixedSingle;
      this.label_Melt_Default.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Melt_Default.ForeColor = SystemColors.ControlText;
      this.label_Melt_Default.Location = new Point(-1, 19);
      this.label_Melt_Default.Name = "label_Melt_Default";
      this.label_Melt_Default.Size = new Size(205, 21);
      this.label_Melt_Default.TabIndex = 86;
      this.label_Melt_Default.Text = "0";
      this.label_Melt_Default.TextAlign = ContentAlignment.MiddleCenter;
      this.dataGridView_Melt.AllowUserToAddRows = false;
      this.dataGridView_Melt.AllowUserToDeleteRows = false;
      this.dataGridView_Melt.AllowUserToResizeColumns = false;
      this.dataGridView_Melt.AllowUserToResizeRows = false;
      this.dataGridView_Melt.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_Melt.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_Melt.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
      this.dataGridView_Melt.BackgroundColor = Color.White;
      gridViewCellStyle7.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle7.BackColor = Color.Lavender;
      gridViewCellStyle7.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle7.ForeColor = SystemColors.WindowText;
      gridViewCellStyle7.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle7.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle7.WrapMode = DataGridViewTriState.True;
      this.dataGridView_Melt.ColumnHeadersDefaultCellStyle = gridViewCellStyle7;
      this.dataGridView_Melt.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_Melt.ColumnHeadersVisible = false;
      this.dataGridView_Melt.Columns.AddRange((DataGridViewColumn) this.Column_Melt);
      gridViewCellStyle8.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle8.BackColor = SystemColors.Window;
      gridViewCellStyle8.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle8.ForeColor = SystemColors.ControlText;
      gridViewCellStyle8.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle8.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle8.WrapMode = DataGridViewTriState.False;
      this.dataGridView_Melt.DefaultCellStyle = gridViewCellStyle8;
      this.dataGridView_Melt.EnableHeadersVisualStyles = false;
      this.dataGridView_Melt.Location = new Point(-2, 38);
      this.dataGridView_Melt.Name = "dataGridView_Melt";
      this.dataGridView_Melt.RowHeadersVisible = false;
      this.dataGridView_Melt.RowTemplate.Height = 23;
      this.dataGridView_Melt.Size = new Size(207, 112);
      this.dataGridView_Melt.TabIndex = 85;
      this.Column_Melt.HeaderText = "Melt Temp";
      this.Column_Melt.Name = "Column_Melt";
      this.Column_Melt.Resizable = DataGridViewTriState.False;
      this.Column_Melt.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.label_Melt.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Melt.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Melt.ForeColor = Color.MidnightBlue;
      this.label_Melt.Location = new Point(-1, -1);
      this.label_Melt.Name = "label_Melt";
      this.label_Melt.Size = new Size(205, 21);
      this.label_Melt.TabIndex = 64;
      this.label_Melt.Text = "Melt Temp";
      this.label_Melt.TextAlign = ContentAlignment.MiddleCenter;
      this.panel_Mold.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Mold.Controls.Add((Control) this.label_Mold_Unit);
      this.panel_Mold.Controls.Add((Control) this.newButton_Mold_Add);
      this.panel_Mold.Controls.Add((Control) this.newButton_Mold_Del);
      this.panel_Mold.Controls.Add((Control) this.checkBox_Mold);
      this.panel_Mold.Controls.Add((Control) this.label_Mold_Default);
      this.panel_Mold.Controls.Add((Control) this.dataGridView_Mold);
      this.panel_Mold.Controls.Add((Control) this.label_Mold);
      this.panel_Mold.Location = new Point(216, 189);
      this.panel_Mold.Name = "panel_Mold";
      this.panel_Mold.Size = new Size(205, 150);
      this.panel_Mold.TabIndex = 90;
      this.label_Mold_Unit.BackColor = Color.LavenderBlush;
      this.label_Mold_Unit.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Mold_Unit.ForeColor = SystemColors.ControlText;
      this.label_Mold_Unit.Location = new Point(159, 20);
      this.label_Mold_Unit.Name = "label_Mold_Unit";
      this.label_Mold_Unit.Size = new Size(45, 18);
      this.label_Mold_Unit.TabIndex = 89;
      this.label_Mold_Unit.Text = "℃";
      this.label_Mold_Unit.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Mold_Add.BackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Mold_Add.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Mold_Add.ButtonText = "";
      this.newButton_Mold_Add.FlatBorderSize = 0;
      this.newButton_Mold_Add.FlatStyle = FlatStyle.Flat;
      this.newButton_Mold_Add.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Mold_Add.Image = (Image) Resources.Add;
      this.newButton_Mold_Add.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Mold_Add.Location = new Point(166, 2);
      this.newButton_Mold_Add.Name = "newButton_Mold_Add";
      this.newButton_Mold_Add.Size = new Size(15, 15);
      this.newButton_Mold_Add.TabIndex = 7;
      this.newButton_Mold_Add.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Mold_Add.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Mold_Add.NewClick += new EventHandler(this.newButton_Add_NewClick);
      this.newButton_Mold_Del.BackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Mold_Del.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Mold_Del.ButtonText = "";
      this.newButton_Mold_Del.FlatBorderSize = 0;
      this.newButton_Mold_Del.FlatStyle = FlatStyle.Flat;
      this.newButton_Mold_Del.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Mold_Del.Image = (Image) Resources.Del;
      this.newButton_Mold_Del.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Mold_Del.Location = new Point(185, 2);
      this.newButton_Mold_Del.Name = "newButton_Mold_Del";
      this.newButton_Mold_Del.Size = new Size(15, 15);
      this.newButton_Mold_Del.TabIndex = 8;
      this.newButton_Mold_Del.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Mold_Del.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Mold_Del.NewClick += new EventHandler(this.newButton_Del_NewClick);
      this.checkBox_Mold.Location = new Point(12, 3);
      this.checkBox_Mold.Name = "checkBox_Mold";
      this.checkBox_Mold.Size = new Size(14, 14);
      this.checkBox_Mold.TabIndex = 0;
      this.checkBox_Mold.TextAlign = ContentAlignment.MiddleCenter;
      this.checkBox_Mold.UseVisualStyleBackColor = true;
      this.label_Mold_Default.BackColor = Color.LavenderBlush;
      this.label_Mold_Default.BorderStyle = BorderStyle.FixedSingle;
      this.label_Mold_Default.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Mold_Default.ForeColor = SystemColors.ControlText;
      this.label_Mold_Default.Location = new Point(-1, 19);
      this.label_Mold_Default.Name = "label_Mold_Default";
      this.label_Mold_Default.Size = new Size(205, 21);
      this.label_Mold_Default.TabIndex = 86;
      this.label_Mold_Default.Text = "0";
      this.label_Mold_Default.TextAlign = ContentAlignment.MiddleCenter;
      this.dataGridView_Mold.AllowUserToAddRows = false;
      this.dataGridView_Mold.AllowUserToDeleteRows = false;
      this.dataGridView_Mold.AllowUserToResizeColumns = false;
      this.dataGridView_Mold.AllowUserToResizeRows = false;
      this.dataGridView_Mold.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_Mold.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_Mold.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
      this.dataGridView_Mold.BackgroundColor = Color.White;
      gridViewCellStyle9.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle9.BackColor = Color.Lavender;
      gridViewCellStyle9.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle9.ForeColor = SystemColors.WindowText;
      gridViewCellStyle9.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle9.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle9.WrapMode = DataGridViewTriState.True;
      this.dataGridView_Mold.ColumnHeadersDefaultCellStyle = gridViewCellStyle9;
      this.dataGridView_Mold.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_Mold.ColumnHeadersVisible = false;
      this.dataGridView_Mold.Columns.AddRange((DataGridViewColumn) this.Column_Mold);
      gridViewCellStyle10.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle10.BackColor = SystemColors.Window;
      gridViewCellStyle10.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle10.ForeColor = SystemColors.ControlText;
      gridViewCellStyle10.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle10.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle10.WrapMode = DataGridViewTriState.False;
      this.dataGridView_Mold.DefaultCellStyle = gridViewCellStyle10;
      this.dataGridView_Mold.EnableHeadersVisualStyles = false;
      this.dataGridView_Mold.Location = new Point(-2, 38);
      this.dataGridView_Mold.Name = "dataGridView_Mold";
      this.dataGridView_Mold.RowHeadersVisible = false;
      this.dataGridView_Mold.RowTemplate.Height = 23;
      this.dataGridView_Mold.Size = new Size(207, 112);
      this.dataGridView_Mold.TabIndex = 85;
      this.Column_Mold.HeaderText = "Mold Temp";
      this.Column_Mold.Name = "Column_Mold";
      this.Column_Mold.Resizable = DataGridViewTriState.False;
      this.Column_Mold.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.label_Mold.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Mold.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Mold.ForeColor = Color.MidnightBlue;
      this.label_Mold.Location = new Point(-1, -1);
      this.label_Mold.Name = "label_Mold";
      this.label_Mold.Size = new Size(205, 21);
      this.label_Mold.TabIndex = 64;
      this.label_Mold.Text = "Mold Temp";
      this.label_Mold.TextAlign = ContentAlignment.MiddleCenter;
      this.panel_Packing.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Packing.Controls.Add((Control) this.label_Pressure3_Unit);
      this.panel_Packing.Controls.Add((Control) this.label_Duration3_Unit);
      this.panel_Packing.Controls.Add((Control) this.label_Pressure2_Unit);
      this.panel_Packing.Controls.Add((Control) this.label_Duration2_Unit);
      this.panel_Packing.Controls.Add((Control) this.label_Pressure1_Unit);
      this.panel_Packing.Controls.Add((Control) this.label_Duration1_Unit);
      this.panel_Packing.Controls.Add((Control) this.label_Pressure3);
      this.panel_Packing.Controls.Add((Control) this.label_Duration3);
      this.panel_Packing.Controls.Add((Control) this.label_Pressure2);
      this.panel_Packing.Controls.Add((Control) this.label_Duration2);
      this.panel_Packing.Controls.Add((Control) this.label_Pressure1);
      this.panel_Packing.Controls.Add((Control) this.label_Duration1);
      this.panel_Packing.Controls.Add((Control) this.label_Pressure3_Default);
      this.panel_Packing.Controls.Add((Control) this.label_Duration3_Default);
      this.panel_Packing.Controls.Add((Control) this.label_Pressure2_Default);
      this.panel_Packing.Controls.Add((Control) this.label_Duration2_Default);
      this.panel_Packing.Controls.Add((Control) this.label_Pressure1_Default);
      this.panel_Packing.Controls.Add((Control) this.label_Duration1_Default);
      this.panel_Packing.Controls.Add((Control) this.newButton_Packing_Add);
      this.panel_Packing.Controls.Add((Control) this.newButton_Packing_Del);
      this.panel_Packing.Controls.Add((Control) this.checkBox_Packing);
      this.panel_Packing.Controls.Add((Control) this.dataGridView_Packing);
      this.panel_Packing.Controls.Add((Control) this.label_Packing);
      this.panel_Packing.Location = new Point(12, 338);
      this.panel_Packing.Name = "panel_Packing";
      this.panel_Packing.Size = new Size(613, 193);
      this.panel_Packing.TabIndex = 91;
      this.label_Pressure3_Unit.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Right;
      this.label_Pressure3_Unit.BackColor = Color.LavenderBlush;
      this.label_Pressure3_Unit.Font = new Font("Segoe UI", 8.25f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Pressure3_Unit.Location = new Point(588, 40);
      this.label_Pressure3_Unit.Name = "label_Pressure3_Unit";
      this.label_Pressure3_Unit.Size = new Size(23, 19);
      this.label_Pressure3_Unit.TabIndex = 115;
      this.label_Pressure3_Unit.Text = "%";
      this.label_Pressure3_Unit.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Duration3_Unit.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Right;
      this.label_Duration3_Unit.BackColor = Color.LavenderBlush;
      this.label_Duration3_Unit.Font = new Font("Segoe UI", 8.25f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Duration3_Unit.Location = new Point(486, 40);
      this.label_Duration3_Unit.Name = "label_Duration3_Unit";
      this.label_Duration3_Unit.Size = new Size(23, 19);
      this.label_Duration3_Unit.TabIndex = 114;
      this.label_Duration3_Unit.Text = "S";
      this.label_Duration3_Unit.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pressure2_Unit.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Right;
      this.label_Pressure2_Unit.BackColor = Color.LavenderBlush;
      this.label_Pressure2_Unit.Font = new Font("Segoe UI", 8.25f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Pressure2_Unit.Location = new Point(384, 40);
      this.label_Pressure2_Unit.Name = "label_Pressure2_Unit";
      this.label_Pressure2_Unit.Size = new Size(23, 19);
      this.label_Pressure2_Unit.TabIndex = 113;
      this.label_Pressure2_Unit.Text = "%";
      this.label_Pressure2_Unit.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Duration2_Unit.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Right;
      this.label_Duration2_Unit.BackColor = Color.LavenderBlush;
      this.label_Duration2_Unit.Font = new Font("Segoe UI", 8.25f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Duration2_Unit.Location = new Point(282, 40);
      this.label_Duration2_Unit.Name = "label_Duration2_Unit";
      this.label_Duration2_Unit.Size = new Size(23, 19);
      this.label_Duration2_Unit.TabIndex = 112;
      this.label_Duration2_Unit.Text = "S";
      this.label_Duration2_Unit.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pressure1_Unit.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Right;
      this.label_Pressure1_Unit.BackColor = Color.LavenderBlush;
      this.label_Pressure1_Unit.Font = new Font("Segoe UI", 8.25f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Pressure1_Unit.Location = new Point(180, 40);
      this.label_Pressure1_Unit.Name = "label_Pressure1_Unit";
      this.label_Pressure1_Unit.Size = new Size(23, 19);
      this.label_Pressure1_Unit.TabIndex = 111;
      this.label_Pressure1_Unit.Text = "%";
      this.label_Pressure1_Unit.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Duration1_Unit.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Right;
      this.label_Duration1_Unit.BackColor = Color.LavenderBlush;
      this.label_Duration1_Unit.Font = new Font("Segoe UI", 8.25f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Duration1_Unit.Location = new Point(78, 40);
      this.label_Duration1_Unit.Name = "label_Duration1_Unit";
      this.label_Duration1_Unit.Size = new Size(23, 19);
      this.label_Duration1_Unit.TabIndex = 110;
      this.label_Duration1_Unit.Text = "S";
      this.label_Duration1_Unit.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pressure3.BackColor = Color.Lavender;
      this.label_Pressure3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pressure3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Pressure3.ForeColor = SystemColors.ControlText;
      this.label_Pressure3.Location = new Point(509, 19);
      this.label_Pressure3.Name = "label_Pressure3";
      this.label_Pressure3.Size = new Size(103, 21);
      this.label_Pressure3.TabIndex = 109;
      this.label_Pressure3.Text = "Pressure3";
      this.label_Pressure3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Duration3.BackColor = Color.Lavender;
      this.label_Duration3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Duration3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Duration3.ForeColor = SystemColors.ControlText;
      this.label_Duration3.Location = new Point(407, 19);
      this.label_Duration3.Name = "label_Duration3";
      this.label_Duration3.Size = new Size(103, 21);
      this.label_Duration3.TabIndex = 108;
      this.label_Duration3.Text = "Duration3";
      this.label_Duration3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pressure2.BackColor = Color.Lavender;
      this.label_Pressure2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pressure2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Pressure2.ForeColor = SystemColors.ControlText;
      this.label_Pressure2.Location = new Point(305, 19);
      this.label_Pressure2.Name = "label_Pressure2";
      this.label_Pressure2.Size = new Size(103, 21);
      this.label_Pressure2.TabIndex = 107;
      this.label_Pressure2.Text = "Pressure2";
      this.label_Pressure2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Duration2.BackColor = Color.Lavender;
      this.label_Duration2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Duration2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Duration2.ForeColor = SystemColors.ControlText;
      this.label_Duration2.Location = new Point(203, 19);
      this.label_Duration2.Name = "label_Duration2";
      this.label_Duration2.Size = new Size(103, 21);
      this.label_Duration2.TabIndex = 106;
      this.label_Duration2.Text = "Duration2";
      this.label_Duration2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pressure1.BackColor = Color.Lavender;
      this.label_Pressure1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pressure1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Pressure1.ForeColor = SystemColors.ControlText;
      this.label_Pressure1.Location = new Point(101, 19);
      this.label_Pressure1.Name = "label_Pressure1";
      this.label_Pressure1.Size = new Size(103, 21);
      this.label_Pressure1.TabIndex = 105;
      this.label_Pressure1.Text = "Pressure1";
      this.label_Pressure1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Duration1.BackColor = Color.Lavender;
      this.label_Duration1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Duration1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Duration1.ForeColor = SystemColors.ControlText;
      this.label_Duration1.Location = new Point(-1, 19);
      this.label_Duration1.Name = "label_Duration1";
      this.label_Duration1.Size = new Size(103, 21);
      this.label_Duration1.TabIndex = 104;
      this.label_Duration1.Text = "Duration1";
      this.label_Duration1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pressure3_Default.BackColor = Color.LavenderBlush;
      this.label_Pressure3_Default.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pressure3_Default.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Pressure3_Default.ForeColor = SystemColors.ControlText;
      this.label_Pressure3_Default.Location = new Point(509, 39);
      this.label_Pressure3_Default.Name = "label_Pressure3_Default";
      this.label_Pressure3_Default.Size = new Size(103, 21);
      this.label_Pressure3_Default.TabIndex = 103;
      this.label_Pressure3_Default.Text = "0";
      this.label_Pressure3_Default.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Duration3_Default.BackColor = Color.LavenderBlush;
      this.label_Duration3_Default.BorderStyle = BorderStyle.FixedSingle;
      this.label_Duration3_Default.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Duration3_Default.ForeColor = SystemColors.ControlText;
      this.label_Duration3_Default.Location = new Point(407, 39);
      this.label_Duration3_Default.Name = "label_Duration3_Default";
      this.label_Duration3_Default.Size = new Size(103, 21);
      this.label_Duration3_Default.TabIndex = 102;
      this.label_Duration3_Default.Text = "0";
      this.label_Duration3_Default.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pressure2_Default.BackColor = Color.LavenderBlush;
      this.label_Pressure2_Default.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pressure2_Default.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Pressure2_Default.ForeColor = SystemColors.ControlText;
      this.label_Pressure2_Default.Location = new Point(305, 39);
      this.label_Pressure2_Default.Name = "label_Pressure2_Default";
      this.label_Pressure2_Default.Size = new Size(103, 21);
      this.label_Pressure2_Default.TabIndex = 101;
      this.label_Pressure2_Default.Text = "0";
      this.label_Pressure2_Default.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Duration2_Default.BackColor = Color.LavenderBlush;
      this.label_Duration2_Default.BorderStyle = BorderStyle.FixedSingle;
      this.label_Duration2_Default.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Duration2_Default.ForeColor = SystemColors.ControlText;
      this.label_Duration2_Default.Location = new Point(203, 39);
      this.label_Duration2_Default.Name = "label_Duration2_Default";
      this.label_Duration2_Default.Size = new Size(103, 21);
      this.label_Duration2_Default.TabIndex = 100;
      this.label_Duration2_Default.Text = "0";
      this.label_Duration2_Default.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pressure1_Default.BackColor = Color.LavenderBlush;
      this.label_Pressure1_Default.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pressure1_Default.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Pressure1_Default.ForeColor = SystemColors.ControlText;
      this.label_Pressure1_Default.Location = new Point(101, 39);
      this.label_Pressure1_Default.Name = "label_Pressure1_Default";
      this.label_Pressure1_Default.Size = new Size(103, 21);
      this.label_Pressure1_Default.TabIndex = 99;
      this.label_Pressure1_Default.Text = "0";
      this.label_Pressure1_Default.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Duration1_Default.BackColor = Color.LavenderBlush;
      this.label_Duration1_Default.BorderStyle = BorderStyle.FixedSingle;
      this.label_Duration1_Default.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Duration1_Default.ForeColor = SystemColors.ControlText;
      this.label_Duration1_Default.Location = new Point(-1, 39);
      this.label_Duration1_Default.Name = "label_Duration1_Default";
      this.label_Duration1_Default.Size = new Size(103, 21);
      this.label_Duration1_Default.TabIndex = 98;
      this.label_Duration1_Default.Text = "0";
      this.label_Duration1_Default.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Packing_Add.BackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Packing_Add.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Packing_Add.ButtonText = "";
      this.newButton_Packing_Add.FlatBorderSize = 0;
      this.newButton_Packing_Add.FlatStyle = FlatStyle.Flat;
      this.newButton_Packing_Add.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Packing_Add.Image = (Image) Resources.Add;
      this.newButton_Packing_Add.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Packing_Add.Location = new Point(574, 2);
      this.newButton_Packing_Add.Name = "newButton_Packing_Add";
      this.newButton_Packing_Add.Size = new Size(15, 15);
      this.newButton_Packing_Add.TabIndex = 7;
      this.newButton_Packing_Add.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Packing_Add.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Packing_Add.NewClick += new EventHandler(this.newButton_Add_NewClick);
      this.newButton_Packing_Del.BackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Packing_Del.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Packing_Del.ButtonText = "";
      this.newButton_Packing_Del.FlatBorderSize = 0;
      this.newButton_Packing_Del.FlatStyle = FlatStyle.Flat;
      this.newButton_Packing_Del.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Packing_Del.Image = (Image) Resources.Del;
      this.newButton_Packing_Del.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Packing_Del.Location = new Point(593, 2);
      this.newButton_Packing_Del.Name = "newButton_Packing_Del";
      this.newButton_Packing_Del.Size = new Size(15, 15);
      this.newButton_Packing_Del.TabIndex = 8;
      this.newButton_Packing_Del.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Packing_Del.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Packing_Del.NewClick += new EventHandler(this.newButton_Del_NewClick);
      this.checkBox_Packing.Location = new Point(12, 3);
      this.checkBox_Packing.Name = "checkBox_Packing";
      this.checkBox_Packing.Size = new Size(14, 14);
      this.checkBox_Packing.TabIndex = 0;
      this.checkBox_Packing.TextAlign = ContentAlignment.MiddleCenter;
      this.checkBox_Packing.UseVisualStyleBackColor = true;
      this.dataGridView_Packing.AllowUserToAddRows = false;
      this.dataGridView_Packing.AllowUserToDeleteRows = false;
      this.dataGridView_Packing.AllowUserToResizeColumns = false;
      this.dataGridView_Packing.AllowUserToResizeRows = false;
      this.dataGridView_Packing.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_Packing.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_Packing.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
      this.dataGridView_Packing.BackgroundColor = Color.White;
      gridViewCellStyle11.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle11.BackColor = Color.Lavender;
      gridViewCellStyle11.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle11.ForeColor = SystemColors.WindowText;
      gridViewCellStyle11.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle11.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle11.WrapMode = DataGridViewTriState.True;
      this.dataGridView_Packing.ColumnHeadersDefaultCellStyle = gridViewCellStyle11;
      this.dataGridView_Packing.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_Packing.ColumnHeadersVisible = false;
      this.dataGridView_Packing.Columns.AddRange((DataGridViewColumn) this.Column_Duration1, (DataGridViewColumn) this.Column_Pressure1, (DataGridViewColumn) this.Column_Duration2, (DataGridViewColumn) this.Column_Pressure2, (DataGridViewColumn) this.Column_Duration3, (DataGridViewColumn) this.Column_Pressure3);
      gridViewCellStyle12.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle12.BackColor = SystemColors.Window;
      gridViewCellStyle12.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle12.ForeColor = SystemColors.ControlText;
      gridViewCellStyle12.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle12.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle12.WrapMode = DataGridViewTriState.False;
      this.dataGridView_Packing.DefaultCellStyle = gridViewCellStyle12;
      this.dataGridView_Packing.EnableHeadersVisualStyles = false;
      this.dataGridView_Packing.Location = new Point(-2, 58);
      this.dataGridView_Packing.Name = "dataGridView_Packing";
      this.dataGridView_Packing.RowHeadersVisible = false;
      this.dataGridView_Packing.RowTemplate.Height = 23;
      this.dataGridView_Packing.Size = new Size(615, 134);
      this.dataGridView_Packing.TabIndex = 85;
      this.Column_Duration1.HeaderText = "Duration1";
      this.Column_Duration1.Name = "Column_Duration1";
      this.Column_Duration1.Resizable = DataGridViewTriState.False;
      this.Column_Duration1.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.Column_Pressure1.HeaderText = "Pressure1";
      this.Column_Pressure1.Name = "Column_Pressure1";
      this.Column_Pressure1.Resizable = DataGridViewTriState.False;
      this.Column_Pressure1.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.Column_Duration2.HeaderText = "Duration2";
      this.Column_Duration2.Name = "Column_Duration2";
      this.Column_Duration2.Resizable = DataGridViewTriState.False;
      this.Column_Duration2.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.Column_Pressure2.HeaderText = "Pressure2";
      this.Column_Pressure2.Name = "Column_Pressure2";
      this.Column_Pressure2.Resizable = DataGridViewTriState.False;
      this.Column_Pressure2.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.Column_Duration3.HeaderText = "Duration3";
      this.Column_Duration3.Name = "Column_Duration3";
      this.Column_Duration3.Resizable = DataGridViewTriState.False;
      this.Column_Duration3.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.Column_Pressure3.HeaderText = "Pressure3";
      this.Column_Pressure3.Name = "Column_Pressure3";
      this.Column_Pressure3.Resizable = DataGridViewTriState.False;
      this.Column_Pressure3.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.label_Packing.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Packing.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Packing.ForeColor = Color.MidnightBlue;
      this.label_Packing.Location = new Point(-2, -1);
      this.label_Packing.Name = "label_Packing";
      this.label_Packing.Size = new Size(615, 21);
      this.label_Packing.TabIndex = 64;
      this.label_Packing.Text = "Packing Control";
      this.label_Packing.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.newButton_Apply.ButtonBackColor = Color.White;
      this.newButton_Apply.ButtonText = "적용";
      this.newButton_Apply.FlatBorderSize = 1;
      this.newButton_Apply.FlatStyle = FlatStyle.Flat;
      this.newButton_Apply.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Apply.Image = (Image) Resources.OK;
      this.newButton_Apply.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Apply.Location = new Point(12, 537);
      this.newButton_Apply.Name = "newButton_Apply";
      this.newButton_Apply.Size = new Size(613, 23);
      this.newButton_Apply.TabIndex = 18;
      this.newButton_Apply.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Apply.NewClick += new EventHandler(this.newButton_Apply_NewClick);
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(636, 564);
      this.Controls.Add((Control) this.panel_Packing);
      this.Controls.Add((Control) this.panel_Mold);
      this.Controls.Add((Control) this.panel_Melt);
      this.Controls.Add((Control) this.panel_Cooling);
      this.Controls.Add((Control) this.panel_VP);
      this.Controls.Add((Control) this.panel_FC);
      this.Controls.Add((Control) this.checkBox_Auto);
      this.Controls.Add((Control) this.newButton_Apply);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmCase);
      this.ShowInTaskbar = false;
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "케이스";
      this.Load += new EventHandler(this.frmCase_Load);
      this.KeyDown += new KeyEventHandler(this.frmCase_KeyDown);
      ((ISupportInitialize) this.dataGridView_FC).EndInit();
      this.panel_FC.ResumeLayout(false);
      this.panel_VP.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_VP).EndInit();
      this.panel_Cooling.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_Cooling).EndInit();
      this.panel_Melt.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_Melt).EndInit();
      this.panel_Mold.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_Mold).EndInit();
      this.panel_Packing.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_Packing).EndInit();
      this.ResumeLayout(false);
    }
  }
}
