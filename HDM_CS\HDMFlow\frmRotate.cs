﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmRotate
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDLog4Net;
using HDMFUserControl;
using HDMoldFlow.Properties;
using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public class frmRotate : Form
  {
    private string[] m_arr_strDirection = new string[6]
    {
      "-X",
      "+X",
      "-Y",
      "+Y",
      "-Z",
      "+Z"
    };
    private IContainer components = (IContainer) null;
    private NewButton newButton_Add;
    private NewButton newButton_Del;
    private DataGridView dataGridView_Rotate;
    private Label label_Rotate;
    private NewButton newButton_Apply;
    private Panel panel_Packing;
    private Label label_Rotate3;
    private Label label_Rotate2;
    private Label label_Rotate1;
    private Label label_Item;
    private DataGridViewTextBoxColumn Column_Item;
    private DataGridViewComboBoxColumn Column_Rotate1_Dir;
    private DataGridViewTextBoxColumn Column_Rotate1_Value;
    private DataGridViewComboBoxColumn Column_Rotate2_Dir;
    private DataGridViewTextBoxColumn Column_Rotate2_Value;
    private DataGridViewComboBoxColumn Column_Rotate3_Dir;
    private DataGridViewTextBoxColumn Column_Rotate3_Value;

    public frmRotate()
    {
      this.InitializeComponent();
      this.newButton_Apply.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Apply.Image);
      this.newButton_Add.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Add.Image);
      this.newButton_Del.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Del.Image);
      this.Text = LocaleControl.getInstance().GetString("IDS_AUTOMATIC_ROTATE");
      this.label_Rotate.Text = LocaleControl.getInstance().GetString("IDS_AUTOMATIC_ROTATE_SETTING");
      this.label_Item.Text = LocaleControl.getInstance().GetString("IDS_ITEM_NAME");
      this.label_Rotate1.Text = LocaleControl.getInstance().GetString("IDS_ROTATE") + "1";
      this.label_Rotate2.Text = LocaleControl.getInstance().GetString("IDS_ROTATE") + "2";
      this.label_Rotate3.Text = LocaleControl.getInstance().GetString("IDS_ROTATE") + "3";
      this.newButton_Apply.ButtonText = LocaleControl.getInstance().GetString("IDS_APPLY");
    }

    private void frmRotate_Load(object sender, EventArgs e)
    {
      this.ActiveControl = (Control) this.label_Rotate;
      for (int index = 0; index < this.dataGridView_Rotate.ColumnCount; ++index)
      {
        if (this.dataGridView_Rotate.Columns[index] is DataGridViewComboBoxColumn column)
          column.Items.AddRange((object[]) this.m_arr_strDirection);
      }
      this.SetRotateData();
    }

    private void SetRotateData()
    {
      try
      {
        if (clsDefine.g_dtRotate == null)
          return;
        DataColumn[] array = clsDefine.g_dtRotate.Columns.Cast<DataColumn>().Where<DataColumn>((System.Func<DataColumn, bool>) (Temp => Temp.ColumnName.Contains("Value"))).ToArray<DataColumn>();
        foreach (DataRow row1 in (InternalDataCollectionBase) clsDefine.g_dtRotate.Rows)
        {
          DataGridViewRow row2 = this.dataGridView_Rotate.Rows[this.dataGridView_Rotate.Rows.Add()];
          row2.Cells[0].Value = (object) row1["Section"].ToString();
          for (int index1 = 0; index1 < array.Length; ++index1)
          {
            if (row1["Value" + (object) (index1 + 1)] != null && !(row1["Value" + (object) (index1 + 1)].ToString() == string.Empty))
            {
              DataGridViewComboBoxCell cell = (DataGridViewComboBoxCell) row2.Cells[index1 * 2 + 1];
              int index2 = clsUtill.ConvertToInt(row1["Direction" + (object) (index1 + 1)].ToString());
              row2.Cells[index1 * 2 + 1].Value = cell.Items[index2];
              row2.Cells[index1 * 2 + 2].Value = (object) row1["Value" + (object) (index1 + 1)].ToString();
            }
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRotate]SetRotateData):" + ex.Message));
      }
    }

    private void frmRotate_KeyDown(object sender, KeyEventArgs e)
    {
      if (e.KeyCode != Keys.Escape)
        return;
      this.Close();
    }

    private void newButton_Apply_NewClick(object sender, EventArgs e)
    {
      clsDefine.g_dtRotate.Clear();
      if (clsDefine.g_fiRotateCfg.Exists)
        clsDefine.g_fiRotateCfg.Delete();
      try
      {
        foreach (DataGridViewRow row in (IEnumerable) this.dataGridView_Rotate.Rows)
        {
          DataRow dataRow = clsDefine.g_dtRotate.Rows.Add();
          string p_strSection = row.Cells[0].Value.ToString();
          dataRow["Section"] = (object) p_strSection;
          for (int index = 0; index < 3; ++index)
          {
            if (row.Cells[index * 2 + 1].Value != null && row.Cells[index * 2 + 2].Value != null)
            {
              int num = Array.IndexOf<string>(this.m_arr_strDirection, row.Cells[index * 2 + 1].Value.ToString());
              dataRow["Direction" + (object) (index + 1)] = (object) num;
              dataRow["Value" + (object) (index + 1)] = (object) row.Cells[index * 2 + 2].Value.ToString();
              clsUtill.WriteINI(p_strSection, "Direction" + (object) (index + 1), num.ToString(), clsDefine.g_fiRotateCfg.FullName);
              clsUtill.WriteINI(p_strSection, "Value" + (object) (index + 1), row.Cells[index * 2 + 2].Value.ToString(), clsDefine.g_fiRotateCfg.FullName);
            }
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRotate]SetRotateData):" + ex.Message));
      }
      this.Close();
    }

    private void newButton_NewClick(object sender, EventArgs e)
    {
      NewButton newButton = sender as NewButton;
      if (newButton == this.newButton_Add)
      {
        this.dataGridView_Rotate.Rows.Add();
        this.dataGridView_Rotate.ClearSelection();
      }
      else
      {
        if (newButton != this.newButton_Del)
          return;
        foreach (DataGridViewRow selectedRow in (BaseCollection) this.dataGridView_Rotate.SelectedRows)
          this.dataGridView_Rotate.Rows.Remove(selectedRow);
        this.dataGridView_Rotate.ClearSelection();
      }
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      DataGridViewCellStyle gridViewCellStyle1 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle2 = new DataGridViewCellStyle();
      this.newButton_Add = new NewButton();
      this.newButton_Del = new NewButton();
      this.dataGridView_Rotate = new DataGridView();
      this.Column_Item = new DataGridViewTextBoxColumn();
      this.Column_Rotate1_Dir = new DataGridViewComboBoxColumn();
      this.Column_Rotate1_Value = new DataGridViewTextBoxColumn();
      this.Column_Rotate2_Dir = new DataGridViewComboBoxColumn();
      this.Column_Rotate2_Value = new DataGridViewTextBoxColumn();
      this.Column_Rotate3_Dir = new DataGridViewComboBoxColumn();
      this.Column_Rotate3_Value = new DataGridViewTextBoxColumn();
      this.label_Rotate = new Label();
      this.newButton_Apply = new NewButton();
      this.panel_Packing = new Panel();
      this.label_Rotate3 = new Label();
      this.label_Rotate2 = new Label();
      this.label_Rotate1 = new Label();
      this.label_Item = new Label();
      ((ISupportInitialize) this.dataGridView_Rotate).BeginInit();
      this.panel_Packing.SuspendLayout();
      this.SuspendLayout();
      this.newButton_Add.BackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Add.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Add.ButtonText = "";
      this.newButton_Add.FlatBorderSize = 0;
      this.newButton_Add.FlatStyle = FlatStyle.Flat;
      this.newButton_Add.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Add.Image = (Image) Resources.Add;
      this.newButton_Add.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Add.Location = new Point(579, 8);
      this.newButton_Add.Name = "newButton_Add";
      this.newButton_Add.Size = new Size(15, 15);
      this.newButton_Add.TabIndex = 7;
      this.newButton_Add.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Add.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Add.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_Del.BackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Del.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Del.ButtonText = "";
      this.newButton_Del.FlatBorderSize = 0;
      this.newButton_Del.FlatStyle = FlatStyle.Flat;
      this.newButton_Del.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Del.Image = (Image) Resources.Del;
      this.newButton_Del.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Del.Location = new Point(600, 8);
      this.newButton_Del.Name = "newButton_Del";
      this.newButton_Del.Size = new Size(15, 15);
      this.newButton_Del.TabIndex = 8;
      this.newButton_Del.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Del.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Del.NewClick += new EventHandler(this.newButton_NewClick);
      this.dataGridView_Rotate.AllowUserToAddRows = false;
      this.dataGridView_Rotate.AllowUserToDeleteRows = false;
      this.dataGridView_Rotate.AllowUserToResizeColumns = false;
      this.dataGridView_Rotate.AllowUserToResizeRows = false;
      this.dataGridView_Rotate.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_Rotate.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_Rotate.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
      this.dataGridView_Rotate.BackgroundColor = Color.White;
      gridViewCellStyle1.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle1.BackColor = Color.Lavender;
      gridViewCellStyle1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle1.ForeColor = SystemColors.WindowText;
      gridViewCellStyle1.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle1.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle1.WrapMode = DataGridViewTriState.True;
      this.dataGridView_Rotate.ColumnHeadersDefaultCellStyle = gridViewCellStyle1;
      this.dataGridView_Rotate.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_Rotate.ColumnHeadersVisible = false;
      this.dataGridView_Rotate.Columns.AddRange((DataGridViewColumn) this.Column_Item, (DataGridViewColumn) this.Column_Rotate1_Dir, (DataGridViewColumn) this.Column_Rotate1_Value, (DataGridViewColumn) this.Column_Rotate2_Dir, (DataGridViewColumn) this.Column_Rotate2_Value, (DataGridViewColumn) this.Column_Rotate3_Dir, (DataGridViewColumn) this.Column_Rotate3_Value);
      gridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle2.BackColor = SystemColors.Window;
      gridViewCellStyle2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle2.ForeColor = SystemColors.ControlText;
      gridViewCellStyle2.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle2.WrapMode = DataGridViewTriState.False;
      this.dataGridView_Rotate.DefaultCellStyle = gridViewCellStyle2;
      this.dataGridView_Rotate.EnableHeadersVisualStyles = false;
      this.dataGridView_Rotate.Location = new Point(-1, 18);
      this.dataGridView_Rotate.Name = "dataGridView_Rotate";
      this.dataGridView_Rotate.RowHeadersVisible = false;
      this.dataGridView_Rotate.RowTemplate.Height = 23;
      this.dataGridView_Rotate.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
      this.dataGridView_Rotate.Size = new Size(615, 94);
      this.dataGridView_Rotate.TabIndex = 85;
      this.Column_Item.HeaderText = "Item";
      this.Column_Item.Name = "Column_Item";
      this.Column_Item.Resizable = DataGridViewTriState.False;
      this.Column_Item.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.Column_Rotate1_Dir.HeaderText = "Direction";
      this.Column_Rotate1_Dir.Name = "Column_Rotate1_Dir";
      this.Column_Rotate1_Value.HeaderText = "Value";
      this.Column_Rotate1_Value.Name = "Column_Rotate1_Value";
      this.Column_Rotate1_Value.Resizable = DataGridViewTriState.False;
      this.Column_Rotate1_Value.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.Column_Rotate2_Dir.HeaderText = "Direction";
      this.Column_Rotate2_Dir.Name = "Column_Rotate2_Dir";
      this.Column_Rotate2_Value.HeaderText = "Value";
      this.Column_Rotate2_Value.Name = "Column_Rotate2_Value";
      this.Column_Rotate2_Value.Resizable = DataGridViewTriState.False;
      this.Column_Rotate2_Value.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.Column_Rotate3_Dir.HeaderText = "Direction";
      this.Column_Rotate3_Dir.Name = "Column_Rotate3_Dir";
      this.Column_Rotate3_Value.HeaderText = "Value";
      this.Column_Rotate3_Value.Name = "Column_Rotate3_Value";
      this.Column_Rotate3_Value.Resizable = DataGridViewTriState.False;
      this.Column_Rotate3_Value.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.label_Rotate.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Rotate.BorderStyle = BorderStyle.FixedSingle;
      this.label_Rotate.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Rotate.ForeColor = Color.MidnightBlue;
      this.label_Rotate.Location = new Point(9, 5);
      this.label_Rotate.Name = "label_Rotate";
      this.label_Rotate.Size = new Size(613, 21);
      this.label_Rotate.TabIndex = 64;
      this.label_Rotate.Text = "자동 회전 설정";
      this.label_Rotate.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.ButtonBackColor = Color.White;
      this.newButton_Apply.ButtonText = "적용";
      this.newButton_Apply.FlatBorderSize = 1;
      this.newButton_Apply.FlatStyle = FlatStyle.Flat;
      this.newButton_Apply.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Apply.Image = (Image) Resources.OK;
      this.newButton_Apply.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Apply.Location = new Point(9, 140);
      this.newButton_Apply.Name = "newButton_Apply";
      this.newButton_Apply.Size = new Size(613, 23);
      this.newButton_Apply.TabIndex = 92;
      this.newButton_Apply.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Apply.NewClick += new EventHandler(this.newButton_Apply_NewClick);
      this.panel_Packing.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Packing.Controls.Add((Control) this.label_Rotate3);
      this.panel_Packing.Controls.Add((Control) this.label_Rotate2);
      this.panel_Packing.Controls.Add((Control) this.label_Rotate1);
      this.panel_Packing.Controls.Add((Control) this.label_Item);
      this.panel_Packing.Controls.Add((Control) this.dataGridView_Rotate);
      this.panel_Packing.Location = new Point(9, 25);
      this.panel_Packing.Name = "panel_Packing";
      this.panel_Packing.Size = new Size(613, 112);
      this.panel_Packing.TabIndex = 93;
      this.label_Rotate3.BackColor = Color.Lavender;
      this.label_Rotate3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Rotate3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Rotate3.ForeColor = SystemColors.ControlText;
      this.label_Rotate3.Location = new Point(436, -1);
      this.label_Rotate3.Name = "label_Rotate3";
      this.label_Rotate3.Size = new Size(176, 21);
      this.label_Rotate3.TabIndex = 107;
      this.label_Rotate3.Text = "회전3";
      this.label_Rotate3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Rotate2.BackColor = Color.Lavender;
      this.label_Rotate2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Rotate2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Rotate2.ForeColor = SystemColors.ControlText;
      this.label_Rotate2.Location = new Point(262, -1);
      this.label_Rotate2.Name = "label_Rotate2";
      this.label_Rotate2.Size = new Size(176, 21);
      this.label_Rotate2.TabIndex = 106;
      this.label_Rotate2.Text = "회전2";
      this.label_Rotate2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Rotate1.BackColor = Color.Lavender;
      this.label_Rotate1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Rotate1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Rotate1.ForeColor = SystemColors.ControlText;
      this.label_Rotate1.Location = new Point(87, -1);
      this.label_Rotate1.Name = "label_Rotate1";
      this.label_Rotate1.Size = new Size(176, 21);
      this.label_Rotate1.TabIndex = 105;
      this.label_Rotate1.Text = "회전1";
      this.label_Rotate1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Item.BackColor = Color.Lavender;
      this.label_Item.BorderStyle = BorderStyle.FixedSingle;
      this.label_Item.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.label_Item.ForeColor = SystemColors.ControlText;
      this.label_Item.Location = new Point(-1, -1);
      this.label_Item.Name = "label_Item";
      this.label_Item.Size = new Size(89, 21);
      this.label_Item.TabIndex = 104;
      this.label_Item.Text = "아이템";
      this.label_Item.TextAlign = ContentAlignment.MiddleCenter;
      this.AutoScaleDimensions = new SizeF(7f, 15f);
      this.AutoScaleMode = AutoScaleMode.Font;
      this.BackColor = Color.White;
      this.ClientSize = new Size(630, 168);
      this.Controls.Add((Control) this.newButton_Del);
      this.Controls.Add((Control) this.newButton_Add);
      this.Controls.Add((Control) this.panel_Packing);
      this.Controls.Add((Control) this.newButton_Apply);
      this.Controls.Add((Control) this.label_Rotate);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.Margin = new Padding(3, 4, 3, 4);
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmRotate);
      this.ShowInTaskbar = false;
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "자동 회전";
      this.Load += new EventHandler(this.frmRotate_Load);
      this.KeyDown += new KeyEventHandler(this.frmRotate_KeyDown);
      ((ISupportInitialize) this.dataGridView_Rotate).EndInit();
      this.panel_Packing.ResumeLayout(false);
      this.ResumeLayout(false);
    }
  }
}
