﻿using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Security.Permissions;

[assembly: Extension]
[assembly: AssemblyTitle("HDMFlow")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("HDMoldFlow")]
[assembly: AssemblyCopyright("Copyright ©  2022")]
[assembly: AssemblyTrademark("")]
[assembly: ComVisible(false)]
[assembly: Guid("186d27a8-65e7-46a6-b48e-58536d643c35")]
[assembly: AssemblyFileVersion("*******")]
[assembly: AssemblyVersion("*******")]
[assembly: SecurityPermission(SecurityAction.RequestMinimum, SkipVerification = true)]
