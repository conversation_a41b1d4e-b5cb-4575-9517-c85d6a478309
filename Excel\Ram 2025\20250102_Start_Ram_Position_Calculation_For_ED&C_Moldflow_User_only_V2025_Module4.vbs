Attribute VB_Name = "Module4"

Sub import_Moldflow()

    TDAY = Format(Date, "yyyy-mm-dd")

    EDAY = "2025-12-31"

    If TDAY > EDAY Then

        MsgBox ("사용날짜가 지났습니다. ED&C에 문의하세요." & vbCr & "02-2069-0099 / <EMAIL>")

        Exit Sub

    End If

    Dim SynergyGetter, Synergy

    On Error Resume Next

    Set SynergyGetter = GetObject(CreateObject("WScript.Shell").ExpandEnvironmentStrings("%SAInstance%"))

    On Error GoTo 0

    If (Not IsEmpty(SynergyGetter)) Then

        Set Synergy = SynergyGetter.GetSASynergy

    Else

        Set Synergy = CreateObject("synergy.Synergy")

    End If

    Synergy.SetUnits "Metric"

    Set PropEd = Synergy.PropertyEditor()

    

    If Cells(5, "J").Value = "Ram speed vs Ram position" Then

        Set Prop = PropEd.FindProperty(30007, 1)

        Set DVec = Synergy.CreateDoubleArray()

        DVec.AddDouble Cells(7, "F").Value

        Prop.FieldValues 10005, DVec

        Set DVec = Synergy.CreateDoubleArray()

        DVec.AddDouble Cells(5, "F").Value

        Prop.FieldValues 10008, DVec

        PropEd.CommitChanges "Process Conditions"

        

        Set Prop = PropEd.FindProperty(30011, 1)

        Set DVec = Synergy.CreateDoubleArray()

        DVec.AddDouble 6

        Prop.FieldValues 10109, DVec

        Set DVec = Synergy.CreateDoubleArray()

        DVec.AddDouble 3

        Prop.FieldValues 10603, DVec

        Set DVec = Synergy.CreateDoubleArray()

        DVec.AddDouble Cells(6, "G").Value

        DVec.AddDouble Cells(17, "E").Value

        Prop.FieldValues 10306, DVec

        Set DVec = Synergy.CreateDoubleArray()

        Prop.FieldValues 10604, DVec

        Set DVec = Synergy.CreateDoubleArray()

        DVec.AddDouble 8

        Prop.FieldValues 10310, DVec

        Set DVec = Synergy.CreateDoubleArray()

        DVec.AddDouble Cells(8, "L")

        Prop.FieldValues 10313, DVec

        Set DVec = Synergy.CreateDoubleArray()

        For i = 17 To 42

            If Cells(i, "E") = "" Then Exit For

            DVec.AddDouble Cells(i, "E")

            DVec.AddDouble Cells(i, "F")

        Next i

        Prop.FieldValues 10604, DVec

        

    Else

    

        Set Prop = PropEd.FindProperty(30011, 1)

        Set DVec = Synergy.CreateDoubleArray()

        DVec.AddDouble 5

        Prop.FieldValues 10109, DVec

        Set DVec = Synergy.CreateDoubleArray()

        DVec.AddDouble 1

        Prop.FieldValues 10602, DVec

        Set DVec = Synergy.CreateDoubleArray()

        DVec.AddDouble 1

        Prop.FieldValues 10110, DVec

        Set DVec = Synergy.CreateDoubleArray()

        DVec.AddDouble Cells(6, "K").Value

        Prop.FieldValues 10100, DVec

        Set DVec = Synergy.CreateDoubleArray()

        DVec.AddDouble 1

        Prop.FieldValues 10310, DVec

        Set DVec = Synergy.CreateDoubleArray()

        DVec.AddDouble Cells(8, "K")

        Prop.FieldValues 10308, DVec

        Set DVec = Synergy.CreateDoubleArray()

        For i = 17 To 42

            If Cells(i, "C") = "" Then Exit For

            DVec.AddDouble Cells(i, "C")

            DVec.AddDouble Cells(i, "D")

        Next i

        Prop.FieldValues 10618, DVec

        

    End If

    PropEd.CommitChanges "Process Conditions"

    

End Sub



Function FindLastRow(startRow As Long, col As String) As Long

    FindLastRow = Cells(Rows.count, col).End(xlUp).Row

End Function



