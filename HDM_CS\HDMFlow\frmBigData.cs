﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmBigData
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDMFUserControl;
using HDMoldFlow.Properties;
using Microsoft.WindowsAPICodePack.Dialogs;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public class frmBigData : Form
  {
    public List<FileInfo> m_lst_fiProject = new List<FileInfo>();
    public string m_strBigData = "";
    private IContainer components = (IContainer) null;
    private NewTextBox newTextBox_BigDataPath;
    private Label label_BigData_Path;
    private NewButton newButton_BigDatapath;
    private NewButton newButton_Apply;
    private Label label_Project;
    private NewButton newButton_Project_Del;
    private NewButton newButton_Project_Add;
    private DataGridView dataGridView_Project;
    private DataGridViewTextBoxColumn Column_Name;
    private DataGridViewTextBoxColumn Column_Path;

    public frmBigData()
    {
      this.InitializeComponent();
      this.newButton_Apply.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Apply.Image);
      this.newButton_Project_Add.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Project_Add.Image);
      this.newButton_Project_Del.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Project_Del.Image);
      this.Text = "BigData " + LocaleControl.getInstance().GetString("IDS_CREATE");
      this.label_BigData_Path.Text = LocaleControl.getInstance().GetString("IDS_CREATE_PATH");
      this.newButton_BigDatapath.ButtonText = LocaleControl.getInstance().GetString("IDS_CHANGE");
      this.newButton_Apply.ButtonText = LocaleControl.getInstance().GetString("IDS_CREATE");
    }

    private void frmBigData_Load(object sender, EventArgs e)
    {
      this.ActiveControl = (Control) this.label_BigData_Path;
      this.newTextBox_BigDataPath.Value = clsDefine.g_diBigData.FullName;
    }

    private void newButton_BigDatapath_NewClick(object sender, EventArgs e)
    {
      CommonOpenFileDialog commonOpenFileDialog = new CommonOpenFileDialog()
      {
        IsFolderPicker = true
      };
      commonOpenFileDialog.DefaultFileName = this.newTextBox_BigDataPath.Value;
      if (commonOpenFileDialog.ShowDialog() != CommonFileDialogResult.Ok)
        return;
      clsDefine.g_diBigData = new DirectoryInfo(commonOpenFileDialog.FileName);
      this.newTextBox_BigDataPath.Value = commonOpenFileDialog.FileName;
      clsUtill.WriteINI("BigData", "Path", clsDefine.g_diBigData.FullName, clsDefine.g_fiBigDataCfg.FullName);
    }

    private void newButton_Apply_NewClick(object sender, EventArgs e)
    {
      this.m_strBigData = this.newTextBox_BigDataPath.Value;
      this.m_lst_fiProject.Clear();
      foreach (DataGridViewRow row in (IEnumerable) this.dataGridView_Project.Rows)
        this.m_lst_fiProject.Add(new FileInfo(row.Cells["Column_Path"].Value.ToString()));
      this.DialogResult = DialogResult.OK;
      this.Close();
    }

    private void frmBigData_KeyDown(object sender, KeyEventArgs e)
    {
      if (e.KeyCode != Keys.Escape)
        return;
      this.Close();
    }

    private void newButton_Project_NewClick(object sender, EventArgs e)
    {
      NewButton newButton = sender as NewButton;
      if (newButton == this.newButton_Project_Add)
      {
        this.AddProject();
      }
      else
      {
        if (newButton != this.newButton_Project_Del)
          return;
        this.DeleteProject();
      }
    }

    private void AddProject()
    {
      string empty = string.Empty;
      OpenFileDialog openFileDialog = new OpenFileDialog();
      openFileDialog.Filter = "Autodesk Moldflow Insight Projects (*.mpi)|*mpi";
      if (openFileDialog.ShowDialog() == DialogResult.OK)
      {
        DataGridViewRow row = this.dataGridView_Project.Rows[this.dataGridView_Project.Rows.Add()];
        row.Cells["Column_Name"].Value = (object) Path.GetFileNameWithoutExtension(openFileDialog.FileName);
        row.Cells["Column_Path"].Value = (object) openFileDialog.FileName;
      }
      this.dataGridView_Project.ClearSelection();
    }

    private void DeleteProject()
    {
      List<DataGridViewRow> dataGridViewRowList = new List<DataGridViewRow>();
      foreach (DataGridViewRow selectedRow in (BaseCollection) this.dataGridView_Project.SelectedRows)
        dataGridViewRowList.Add(selectedRow);
      foreach (DataGridViewRow dataGridViewRow in dataGridViewRowList)
        this.dataGridView_Project.Rows.Remove(dataGridViewRow);
      this.dataGridView_Project.ClearSelection();
    }

    private void dataGridView_Project_MouseDown(object sender, MouseEventArgs e)
    {
      DataGridView.HitTestInfo hitTestInfo = this.dataGridView_Project.HitTest(e.X, e.Y);
      if (hitTestInfo.RowIndex >= 0 && hitTestInfo.ColumnIndex >= 0)
        return;
      this.dataGridView_Project.ClearSelection();
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.label_BigData_Path = new Label();
      this.newButton_Apply = new NewButton();
      this.newButton_BigDatapath = new NewButton();
      this.newTextBox_BigDataPath = new NewTextBox();
      this.label_Project = new Label();
      this.newButton_Project_Del = new NewButton();
      this.newButton_Project_Add = new NewButton();
      this.dataGridView_Project = new DataGridView();
      this.Column_Name = new DataGridViewTextBoxColumn();
      this.Column_Path = new DataGridViewTextBoxColumn();
      ((ISupportInitialize) this.dataGridView_Project).BeginInit();
      this.SuspendLayout();
      this.label_BigData_Path.BackColor = Color.FromArgb(229, 238, 248);
      this.label_BigData_Path.BorderStyle = BorderStyle.FixedSingle;
      this.label_BigData_Path.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_BigData_Path.ForeColor = Color.MidnightBlue;
      this.label_BigData_Path.Location = new Point(5, 4);
      this.label_BigData_Path.Name = "label_BigData_Path";
      this.label_BigData_Path.Size = new Size(406, 20);
      this.label_BigData_Path.TabIndex = 22;
      this.label_BigData_Path.Text = "생성 경로";
      this.label_BigData_Path.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.ButtonBackColor = Color.White;
      this.newButton_Apply.ButtonText = "생성";
      this.newButton_Apply.FlatBorderSize = 1;
      this.newButton_Apply.FlatStyle = FlatStyle.Flat;
      this.newButton_Apply.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Apply.Image = (Image) Resources.OK;
      this.newButton_Apply.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Apply.Location = new Point(5, 272);
      this.newButton_Apply.Name = "newButton_Apply";
      this.newButton_Apply.Size = new Size(406, 23);
      this.newButton_Apply.TabIndex = 25;
      this.newButton_Apply.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Apply.NewClick += new EventHandler(this.newButton_Apply_NewClick);
      this.newButton_BigDatapath.ButtonBackColor = Color.White;
      this.newButton_BigDatapath.ButtonText = "변경";
      this.newButton_BigDatapath.FlatBorderSize = 1;
      this.newButton_BigDatapath.FlatStyle = FlatStyle.Flat;
      this.newButton_BigDatapath.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_BigDatapath.Image = (Image) null;
      this.newButton_BigDatapath.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_BigDatapath.Location = new Point(362, 23);
      this.newButton_BigDatapath.Name = "newButton_BigDatapath";
      this.newButton_BigDatapath.Size = new Size(49, 23);
      this.newButton_BigDatapath.TabIndex = 23;
      this.newButton_BigDatapath.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_BigDatapath.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_BigDatapath.NewClick += new EventHandler(this.newButton_BigDatapath_NewClick);
      this.newTextBox_BigDataPath.BackColor = Color.WhiteSmoke;
      this.newTextBox_BigDataPath.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_BigDataPath.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_BigDataPath.IsDigit = false;
      this.newTextBox_BigDataPath.Lines = new string[0];
      this.newTextBox_BigDataPath.Location = new Point(5, 23);
      this.newTextBox_BigDataPath.MultiLine = false;
      this.newTextBox_BigDataPath.Name = "newTextBox_BigDataPath";
      this.newTextBox_BigDataPath.ReadOnly = false;
      this.newTextBox_BigDataPath.Size = new Size(358, 23);
      this.newTextBox_BigDataPath.TabIndex = 0;
      this.newTextBox_BigDataPath.TextAlign = HorizontalAlignment.Left;
      this.newTextBox_BigDataPath.TextBoxBackColor = Color.WhiteSmoke;
      this.newTextBox_BigDataPath.TextForeColor = SystemColors.WindowText;
      this.newTextBox_BigDataPath.Value = "";
      this.label_Project.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Project.BorderStyle = BorderStyle.FixedSingle;
      this.label_Project.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Project.ForeColor = Color.MidnightBlue;
      this.label_Project.Location = new Point(5, 49);
      this.label_Project.Name = "label_Project";
      this.label_Project.Size = new Size(406, 20);
      this.label_Project.TabIndex = 27;
      this.label_Project.Text = "프로젝트";
      this.label_Project.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Project_Del.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Project_Del.ButtonText = "";
      this.newButton_Project_Del.FlatBorderSize = 0;
      this.newButton_Project_Del.FlatStyle = FlatStyle.Flat;
      this.newButton_Project_Del.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Project_Del.Image = (Image) Resources.Del;
      this.newButton_Project_Del.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Project_Del.Location = new Point(387, 50);
      this.newButton_Project_Del.Name = "newButton_Project_Del";
      this.newButton_Project_Del.Size = new Size(19, 17);
      this.newButton_Project_Del.TabIndex = 28;
      this.newButton_Project_Del.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Project_Del.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Project_Del.NewClick += new EventHandler(this.newButton_Project_NewClick);
      this.newButton_Project_Add.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Project_Add.ButtonText = "";
      this.newButton_Project_Add.FlatBorderSize = 0;
      this.newButton_Project_Add.FlatStyle = FlatStyle.Flat;
      this.newButton_Project_Add.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Project_Add.Image = (Image) Resources.Add;
      this.newButton_Project_Add.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Project_Add.Location = new Point(362, 50);
      this.newButton_Project_Add.Name = "newButton_Project_Add";
      this.newButton_Project_Add.Size = new Size(19, 17);
      this.newButton_Project_Add.TabIndex = 29;
      this.newButton_Project_Add.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Project_Add.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Project_Add.NewClick += new EventHandler(this.newButton_Project_NewClick);
      this.dataGridView_Project.AllowUserToAddRows = false;
      this.dataGridView_Project.AllowUserToDeleteRows = false;
      this.dataGridView_Project.AllowUserToResizeColumns = false;
      this.dataGridView_Project.AllowUserToResizeRows = false;
      this.dataGridView_Project.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_Project.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
      this.dataGridView_Project.BackgroundColor = Color.White;
      this.dataGridView_Project.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
      this.dataGridView_Project.ColumnHeadersVisible = false;
      this.dataGridView_Project.Columns.AddRange((DataGridViewColumn) this.Column_Name, (DataGridViewColumn) this.Column_Path);
      this.dataGridView_Project.Location = new Point(5, 68);
      this.dataGridView_Project.Name = "dataGridView_Project";
      this.dataGridView_Project.RowHeadersVisible = false;
      this.dataGridView_Project.RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.DisableResizing;
      this.dataGridView_Project.RowTemplate.Height = 23;
      this.dataGridView_Project.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
      this.dataGridView_Project.Size = new Size(406, 198);
      this.dataGridView_Project.TabIndex = 30;
      this.dataGridView_Project.MouseDown += new MouseEventHandler(this.dataGridView_Project_MouseDown);
      this.Column_Name.FillWeight = 111.9289f;
      this.Column_Name.HeaderText = "Name";
      this.Column_Name.Name = "Column_Name";
      this.Column_Name.ReadOnly = true;
      this.Column_Name.Resizable = DataGridViewTriState.False;
      this.Column_Name.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.Column_Path.HeaderText = "Path";
      this.Column_Path.Name = "Column_Path";
      this.Column_Path.ReadOnly = true;
      this.Column_Path.Resizable = DataGridViewTriState.False;
      this.Column_Path.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.Column_Path.Visible = false;
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(417, 302);
      this.Controls.Add((Control) this.dataGridView_Project);
      this.Controls.Add((Control) this.newButton_Project_Del);
      this.Controls.Add((Control) this.newButton_Project_Add);
      this.Controls.Add((Control) this.label_Project);
      this.Controls.Add((Control) this.newButton_Apply);
      this.Controls.Add((Control) this.newButton_BigDatapath);
      this.Controls.Add((Control) this.label_BigData_Path);
      this.Controls.Add((Control) this.newTextBox_BigDataPath);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmBigData);
      this.ShowInTaskbar = false;
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "BigData 생성";
      this.Load += new EventHandler(this.frmBigData_Load);
      this.KeyDown += new KeyEventHandler(this.frmBigData_KeyDown);
      ((ISupportInitialize) this.dataGridView_Project).EndInit();
      this.ResumeLayout(false);
    }
  }
}
