'@ 
'@ DESCRIPTION
'@ Create Percentage Fill Time results by scaling the existing Fill Time results
'@ Versión específica para Ficosa con frames de llenado en lugar de contornos
'@ Muestra frames específicos (25,33,50,75,83,90,95,98,100%)
'@ Incluye tiempo en segundos y porcentaje en la leyenda
'@ También funciona para resultados de sobremoldeo
'@
'@ SYNTAX
'@ FillTimePercentage_Ficosa_01
'@
'@ PARAMETERS
'@ Ninguno - Usa valores predeterminados para Ficosa
'@
'@ DEPENDENCIES/LIMITATIONS
'@ El análisis de llenado debe haberse ejecutado previamente
'@
'@@
'@ Written by: Roo
'@ Version: 4.0 - Frames de llenado específicos para Ficosa (25,33,50,75,83,90,95,98,100%)
'@                Incluye tiempo en segundos y porcentaje en la leyenda
'@
Option Explicit  ' Force variable declaration
Dim ErrorCode

' Definir los contornos específicos para Ficosa (25,33,50,75,83,90,95,98,100%)
Dim FicosaPercentages
FicosaPercentages = Array(25, 33, 50, 75, 83, 90, 95, 98, 100)

' Definir los colores personalizados para Ficosa (formato RGB)
' Azul oscuro -> Azul -> Azul claro -> Verde claro -> Verde -> Amarillo -> Naranja claro -> Naranja -> Rojo
Dim FicosaColors
FicosaColors = Array(Array(0, 0, 128), Array(0, 0, 200), Array(0, 128, 255), Array(0, 200, 128), Array(0, 128, 0), Array(255, 255, 0), Array(255, 200, 0), Array(255, 165, 0), Array(255, 0, 0))

' Crear el grafico de tiempo de llenado con contornos personalizados
ErrorCode = CreateFillTimePercentagePlot(1610, "Ficosa - Tiempo de llenado", FicosaPercentages, FicosaColors) 

If ErrorCode = 1 Then
  QuitWithError "No se encontraron datos de tiempo de llenado" & vbCRLF & "Verifique que el analisis de llenado se haya ejecutado." & vbCRLF & "Saliendo..."
ElseIf ErrorCode = 2 Then
  QuitWithError "Error al crear el grafico personalizado." & vbCRLF & "Saliendo..."
End if

' Intentar crear gráfico para sobremoldeo si existe
Dim CoverErrorCode
CoverErrorCode = CreateFillTimePercentagePlot(11610, "Ficosa - Tiempo de llenado (sobremoldeo)", FicosaPercentages, FicosaColors)
' No mostrar error si no hay datos de sobremoldeo (CoverErrorCode = 1)
If CoverErrorCode = 2 Then
  QuitWithError "Error al crear el grafico de sobremoldeo." & vbCRLF & "Saliendo..."
End If

Function CreateFillTimePercentagePlot(DataSetID, NewPlotName, Percentages, Colors)
  ' Declare all variables
  Dim Synergy, BuildCode, PlotManager
  Dim IndpValues, nodeIDs, FillTimeValues
  
  On Error Resume Next
  ' Add logging
  WScript.Echo "Iniciando CreateFillTimePercentagePlot"
  WScript.Echo "DataSetID: " & DataSetID
  WScript.Echo "Nombre del grafico: " & NewPlotName
  On Error GoTo 0
  SetLocale("es-es")  ' Configurar para español
  BuildCode = GetBuildCode()
  Set Synergy = CreateObject(BuildCode & ".Synergy")
  Synergy.SetUnits "Metric"
  Set PlotManager = Synergy.PlotManager()
  
  Dim DataExists, iNode
  Set IndpValues = Synergy.CreateDoubleArray() ' Will hold original time values
  Set nodeIDs = Synergy.CreateIntegerArray()
  Set FillTimeValues = Synergy.CreateDoubleArray() ' Will hold scaled percentage values
  
  ' Get original Fill Time data (DataSetID 1610)
  On Error Resume Next
  DataExists = PlotManager.GetScalarData(DataSetID, IndpValues, nodeIDs, FillTimeValues)
  
  If Err.Number <> 0 Then
    WScript.Echo "Error getting scalar data: " & Err.Description
    CreateFillTimePercentagePlot = 1
    Exit Function
  End If
  On Error Goto 0
  
  If Not DataExists Then
    WScript.Echo "No Fill Time data found for DataSetID: " & DataSetID
    CreateFillTimePercentagePlot = 1 ' Indicate data not found
    Exit Function
  End If
  
  ' Work with VB Arrays for performance
  Dim FillTimeVBArr, OriginalTimeVBArr
  FillTimeVBArr = FillTimeValues.ToVBSArray() ' Copy for scaling
  OriginalTimeVBArr = FillTimeValues.ToVBSArray() ' Keep original times
  
  ' Get the maximum Fill Time
  Dim MaxFillTime
  MaxFillTime = -1.0
  For iNode = 0 to UBound(FillTimeVBArr)
    If MaxFillTime < FillTimeVBArr(iNode) Then
      MaxFillTime = FillTimeVBArr(iNode)
    End if
  Next
  
  ' Avoid division by zero if MaxFillTime is 0 or less
  If MaxFillTime <= 0 Then MaxFillTime = 1.0 

  ' Scale the Fill Time Data to Percentage
  For iNode = 0 to UBound(FillTimeVBArr)
    FillTimeVBArr(iNode) = 100.0 * FillTimeVBArr(iNode) / MaxFillTime
  Next
  FillTimeValues.FromVBSArray(FillTimeVBArr) ' Put scaled percentage data back into Synergy array
  
  ' Crear un resultado de usuario para el tiempo de llenado con frames específicos
  Dim UserPlot, Plot, PlotSettings
  Set UserPlot = PlotManager.CreateUserPlot()
  ' Modificar el nombre del gráfico para incluir el tiempo máximo de llenado
  Dim PlotNameWithTime
  PlotNameWithTime = NewPlotName & " (Tiempo max: " & FormatNumber(MaxFillTime, 2) & " s)"
  UserPlot.SetName PlotNameWithTime
  UserPlot.SetDataType "NDDT"
  
  ' Configurar para mostrar tiempo en segundos en la leyenda
  UserPlot.SetDeptUnitName "s"
  
  ' Aplicar configuración de datos escalares - usamos los valores originales de tiempo
  ' en lugar de los porcentajes para poder mostrar los frames correctamente
  Set FillTimeValues = Synergy.CreateDoubleArray()
  FillTimeValues.FromVBSArray(OriginalTimeVBArr)
  UserPlot.SetScalarData nodeIDs, FillTimeValues
  
  ' Construir el gráfico
  Set Plot = UserPlot.Build()
  
  If Plot Is Nothing Then
    WScript.Echo "Error al crear el grafico: El objeto Plot es Nothing"
    CreateFillTimePercentagePlot = 2 ' Indicar fallo en la creacion del grafico
    Exit Function
  End If
  
  ' Configurar frames de llenado específicos en lugar de contornos
  If Not IsNull(Percentages) And Not IsEmpty(Percentages) Then
    ' Configurar el número de frames y sus valores
    Dim FrameCount, i
    FrameCount = UBound(Percentages) + 1
    
    ' Crear array para los tiempos correspondientes (para registro y visualización)
    Dim TimeValues
    ReDim TimeValues(UBound(Percentages))
    
    ' Calcular los tiempos correspondientes para cada porcentaje
    For i = 0 To UBound(Percentages)
      TimeValues(i) = (Percentages(i) / 100.0) * MaxFillTime
    Next
    
    ' Configurar el gráfico para mostrar frames de llenado en lugar de contornos
    ' Establecer el tipo de visualización para animación de llenado
    Plot.SetPlotMethod 2 ' Método de visualización como sombreado
    
    ' Configurar el número de frames para la animación
    ' Usamos el número de porcentajes especificados
    Plot.SetNumberOfFrames FrameCount
    
    ' Establecer valores mínimos y máximos para el rango de visualización
    Plot.SetMinValue 0
    Plot.SetMaxValue MaxFillTime
    
    ' Configurar explícitamente el número de bandas de colores para que coincida con el número de porcentajes
    ' Esto es crucial para que la leyenda muestre todos los valores
    Plot.SetColorBands FrameCount
    WScript.Echo "Configurando " & FrameCount & " bandas de colores para la leyenda"
    
    ' Configurar la leyenda dual con valores de tiempo como segunda unidad
    On Error Resume Next
    
    ' Primero intentar el método directo para configurar la leyenda dual
    Plot.SetDualLegend True
    
    ' Crear un array de Synergy para los tiempos
    Dim SynergyTimeValues
    Set SynergyTimeValues = Synergy.CreateDoubleArray()
    
    ' Llenar el array con los valores de tiempo y mostrar en el log para depuración
    For i = 0 To UBound(TimeValues)
      SynergyTimeValues.PushBack TimeValues(i)
      WScript.Echo "Añadiendo valor de tiempo a la leyenda: " & FormatNumber(TimeValues(i), 4) & " s"
    Next
    
    ' Asegurarse de que todos los valores se pasen correctamente a la leyenda
    WScript.Echo "Total de valores de tiempo para la leyenda: " & SynergyTimeValues.Size()
    
    ' Configurar explícitamente los valores de la leyenda secundaria
    Plot.SetSecondaryLegendValues SynergyTimeValues
    Plot.SetSecondaryLegendUnit "s"
    
    ' Asegurarse de que la leyenda sea visible
    Plot.SetLegendVisible True
    
    ' Configurar el número de divisiones de la leyenda para que coincida con el número de porcentajes
    On Error Resume Next
    ' Forzar a mostrar todos los valores (9) en la leyenda
    Plot.SetNumberOfLegendDivisions UBound(Percentages) + 1
    ' Intentar configurar explícitamente 9 divisiones si el método anterior falla
    If Err.Number <> 0 Then
      WScript.Echo "Intentando configurar explícitamente 9 divisiones en la leyenda..."
      Plot.SetNumberOfLegendDivisions 9
      If Err.Number <> 0 Then
        WScript.Echo "No se pudo configurar el número de divisiones de la leyenda: " & Err.Description
        Err.Clear
      End If
    End If
    
    ' Si hay error, intentar el método alternativo usando PlotManager.AddDefaultPlots
    If Err.Number <> 0 Then
      WScript.Echo "Intentando método alternativo para configurar leyenda dual..."
      Err.Clear
      
      ' Aplicar el método sugerido por el usuario
      PlotManager.AddDefaultPlots
      
      ' Volver a intentar configurar la leyenda dual después de AddDefaultPlots
      Plot.SetDualLegend True
      
      ' Crear un nuevo array de Synergy para los tiempos en el método alternativo
      Dim SynergyTimeValues2
      Set SynergyTimeValues2 = Synergy.CreateDoubleArray()
      
      ' Llenar el array con los valores de tiempo nuevamente
      For i = 0 To UBound(TimeValues)
        SynergyTimeValues2.PushBack TimeValues(i)
        WScript.Echo "Método alternativo: Añadiendo valor de tiempo a la leyenda: " & FormatNumber(TimeValues(i), 4) & " s"
      Next
      
      ' Usar el array de Synergy en lugar del array de VBS
      Plot.SetSecondaryLegendValues SynergyTimeValues2
      Plot.SetSecondaryLegendUnit "s"
      
      ' Forzar la actualización de la leyenda
      Plot.SetLegendVisible True
      
      ' Intentar configurar explícitamente el número de divisiones de la leyenda nuevamente
      Plot.SetNumberOfLegendDivisions 9
      
      If Err.Number <> 0 Then
        WScript.Echo "Advertencia: No se pudo configurar la leyenda dual: " & Err.Description
        Err.Clear
      Else
        WScript.Echo "Leyenda dual configurada correctamente usando método alternativo"
        
        ' Intentar configurar explícitamente el número de divisiones de la leyenda secundaria
        On Error Resume Next
        Plot.SetNumberOfSecondaryLegendDivisions 9
        If Err.Number <> 0 Then
          WScript.Echo "No se pudo configurar el número de divisiones de la leyenda secundaria: " & Err.Description
          Err.Clear
        Else
          WScript.Echo "Número de divisiones de la leyenda secundaria configurado correctamente"
        End If
        On Error GoTo 0
      End If
    Else
      WScript.Echo "Leyenda dual configurada correctamente"
    End If
    On Error GoTo 0
    
    ' Regenerar el gráfico para aplicar los cambios
    Plot.Regenerate
    
    ' Mostrar cada frame específico según los porcentajes definidos
    WScript.Echo "Frames configurados: " & FrameCount
    
    ' Crear una colección de frames para los porcentajes específicos
    Dim FrameIndex
    
    ' Mostrar información de los frames específicos en el registro
    For i = 0 To UBound(Percentages)
      ' Calcular el tiempo correspondiente al porcentaje
      Dim FrameTime
      FrameTime = (Percentages(i) / 100.0) * MaxFillTime
      
      ' Configurar el frame actual para mostrar el tiempo específico
      FrameIndex = i
      
      ' Registrar información del frame
      WScript.Echo "Frame " & i & ": " & Percentages(i) & "% = " & FormatNumber(FrameTime, 4) & " s"
    Next
    
    ' Asegurar que el gráfico se muestre
    Dim Viewer
    Set Viewer = Synergy.Viewer()
    
    ' Mostrar el primer frame por defecto (25%)
    Viewer.ShowPlotFrame Plot, 0
  End If
  
  WScript.Echo "Grafico creado exitosamente"
  
  Viewer.ShowPlot Plot
  
  ' Exportar imágenes de cada frame específico automáticamente
  On Error Resume Next
  Dim ExportPath, CurrentTime, TextFilePath, FrameFolder
  CurrentTime = Replace(Replace(Replace(Now(), "/", "-"), ":", "-"), " ", "_")
  FrameFolder = "C:\Ficosa_Reports\" & Replace(NewPlotName, " ", "_") & "_Frames_" & CurrentTime
  TextFilePath = "C:\Ficosa_Reports\" & Replace(NewPlotName, " ", "_") & "_Valores_" & CurrentTime & ".txt"
  
  ' Intentar crear los directorios si no existen
  Dim FSO, ExportFolder, TextFile
  Set FSO = CreateObject("Scripting.FileSystemObject")
  ExportFolder = "C:\Ficosa_Reports"
  
  If Not FSO.FolderExists(ExportFolder) Then
    FSO.CreateFolder(ExportFolder)
  End If
  
  ' Crear carpeta específica para los frames
  If Not FSO.FolderExists(FrameFolder) Then
    FSO.CreateFolder(FrameFolder)
  End If
  
  ' Crear un archivo de texto con la correlación entre porcentajes y tiempos
  If Not IsNull(Percentages) And Not IsEmpty(Percentages) Then
    Set TextFile = FSO.CreateTextFile(TextFilePath, True)
    TextFile.WriteLine "VALORES DE REFERENCIA PARA " & NewPlotName
    TextFile.WriteLine "======================================="
    TextFile.WriteLine "Tiempo máximo de llenado: " & FormatNumber(MaxFillTime, 4) & " segundos"
    TextFile.WriteLine "======================================="
    TextFile.WriteLine "Porcentaje (%)    Tiempo (segundos)"
    TextFile.WriteLine "--------------------------------------"
    
    Dim j
    For j = 0 To UBound(Percentages)
      Dim TimeVal
      TimeVal = (Percentages(j) / 100) * MaxFillTime
      TextFile.WriteLine FormatNumber(Percentages(j), 2) & "%" & Space(15 - Len(FormatNumber(Percentages(j), 2) & "%")) & FormatNumber(TimeVal, 4) & " s"
    Next
    
    TextFile.WriteLine "======================================="
    TextFile.WriteLine "Generado por FillTimePercentage_Ficosa_01.vbs v3.0"
    TextFile.WriteLine "Fecha: " & Now()
    TextFile.Close
    
    WScript.Echo "Archivo de valores de referencia creado en: " & TextFilePath
  End If
  
  ' Exportar imágenes de cada frame específico
  If Err.Number = 0 And Not IsNull(Percentages) And Not IsEmpty(Percentages) Then
    ' Exportar cada frame como una imagen separada
    On Error Resume Next
    
    Dim FrameImagePaths
    ReDim FrameImagePaths(UBound(Percentages))
    
    For i = 0 To UBound(Percentages)
      ' Configurar el frame actual para mostrar el tiempo específico
      Viewer.ShowPlotFrame Plot, i
      
      ' Crear nombre de archivo para este frame
      Dim FrameFileName
      FrameFileName = "Frame_" & Percentages(i) & "pct.png"
      ExportPath = FrameFolder & "\" & FrameFileName
      FrameImagePaths(i) = ExportPath
      
      ' Método 1: Intentar usar el método CaptureImage si está disponible
      Viewer.CaptureImage ExportPath
      
      If Err.Number <> 0 Then
        Err.Clear
        ' Método 2: Intentar usar ExportPlot si está disponible
        Plot.ExportPlot ExportPath
      End If
      
      If Err.Number <> 0 Then
        WScript.Echo "No se pudo exportar el frame " & Percentages(i) & "%: " & Err.Description
        Err.Clear
      Else
        WScript.Echo "Frame " & Percentages(i) & "% exportado a: " & ExportPath
      End If
    Next
    
    ' Volver a mostrar el primer frame
    Viewer.ShowPlotFrame Plot, 0
    
    On Error GoTo 0
  End If
  On Error GoTo 0
  
  ' Limpiar objetos
  Set FSO = Nothing
  Set Plot = Nothing
  Set UserPlot = Nothing
  Set PlotSettings = Nothing
  Set FillTimeValues = Nothing
  Set nodeIDs = Nothing
  Set IndpValues = Nothing
  Set PlotManager = Nothing
  Set Synergy = Nothing
  
  ' Generar informe HTML si es posible
  GenerateHTMLReport FrameImagePaths, TextFilePath, NewPlotName, MaxFillTime, DataSetID, Percentages, FrameFolder
  
  WScript.Echo "CreateFillTimePercentagePlot completado exitosamente"
  CreateFillTimePercentagePlot = 0 ' Indicar éxito
End Function

' Función exclusiva de Ficosa para generar un informe HTML con los resultados
Sub GenerateHTMLReport(FrameImagePaths, TextFilePath, PlotName, MaxFillTime, DataSetID, Percentages, FrameFolder)
  On Error Resume Next
  
  ' Verificar si tenemos frames válidos
  If IsEmpty(FrameImagePaths) Then Exit Sub
  
  Dim FSO, HTMLFile, HTMLPath, ReportFolder
  Set FSO = CreateObject("Scripting.FileSystemObject")
  
  ' Crear carpeta de informes si no existe
  ReportFolder = "C:\Ficosa_Reports"
  If Not FSO.FolderExists(ReportFolder) Then
    FSO.CreateFolder(ReportFolder)
  End If
  
  ' Crear nombre de archivo HTML basado en el nombre del gráfico
  Dim CurrentTime, HTMLFileName
  CurrentTime = Replace(Replace(Replace(Now(), "/", "-"), ":", "-"), " ", "_")
  HTMLFileName = Replace(PlotName, " ", "_") & "_Frames_" & CurrentTime & ".html"
  HTMLPath = ReportFolder & "\" & HTMLFileName
  
  ' Crear archivo HTML
  Set HTMLFile = FSO.CreateTextFile(HTMLPath, True)
  
  ' Escribir contenido HTML
  HTMLFile.WriteLine "<!DOCTYPE html>"
  HTMLFile.WriteLine "<html lang='es'>"
  HTMLFile.WriteLine "<head>"
  HTMLFile.WriteLine "  <meta charset='UTF-8'>"
  HTMLFile.WriteLine "  <meta name='viewport' content='width=device-width, initial-scale=1.0'>"
  HTMLFile.WriteLine "  <title>Informe Ficosa - " & PlotName & " - Frames de Llenado</title>"
  HTMLFile.WriteLine "  <style>"
  HTMLFile.WriteLine "    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #333; }"
  HTMLFile.WriteLine "    .container { max-width: 1200px; margin: 0 auto; }"
  HTMLFile.WriteLine "    .header { background-color: #003366; color: white; padding: 20px; text-align: center; }"
  HTMLFile.WriteLine "    .content { padding: 20px; }"
  HTMLFile.WriteLine "    .result-image { max-width: 100%; height: auto; display: block; margin: 20px auto; border: 1px solid #ddd; }"
  HTMLFile.WriteLine "    .frame-gallery { display: flex; flex-wrap: wrap; justify-content: center; gap: 20px; margin: 30px 0; }"
  HTMLFile.WriteLine "    .frame-item { width: 30%; min-width: 300px; margin-bottom: 20px; border: 1px solid #ddd; padding: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }"
  HTMLFile.WriteLine "    .frame-item img { width: 100%; height: auto; }"
  HTMLFile.WriteLine "    .frame-caption { text-align: center; margin-top: 10px; font-weight: bold; }"
  HTMLFile.WriteLine "    table { width: 100%; border-collapse: collapse; margin: 20px 0; }"
  HTMLFile.WriteLine "    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }"
  HTMLFile.WriteLine "    th { background-color: #f2f2f2; }"
  HTMLFile.WriteLine "    .footer { margin-top: 30px; text-align: center; font-size: 0.8em; color: #666; }"
  HTMLFile.WriteLine "  </style>"
  HTMLFile.WriteLine "</head>"
  HTMLFile.WriteLine "<body>"
  HTMLFile.WriteLine "  <div class='container'>"
  HTMLFile.WriteLine "    <div class='header'>"
  HTMLFile.WriteLine "      <h1>Informe de Análisis Moldflow</h1>"
  HTMLFile.WriteLine "      <h2>" & PlotName & " - Frames de Llenado</h2>"
  HTMLFile.WriteLine "    </div>"
  HTMLFile.WriteLine "    <div class='content'>"
  HTMLFile.WriteLine "      <h3>Resultados del Análisis</h3>"
  HTMLFile.WriteLine "      <p>Este informe muestra los frames específicos del proceso de llenado (25,33,50,75,83,90,95,98,100%) con sus tiempos correspondientes en segundos.</p>"
  
  ' Información del análisis
  HTMLFile.WriteLine "      <h3>Información del Análisis</h3>"
  HTMLFile.WriteLine "      <table>"
  HTMLFile.WriteLine "        <tr><th>Parámetro</th><th>Valor</th></tr>"
  HTMLFile.WriteLine "        <tr><td>ID del Conjunto de Datos</td><td>" & DataSetID & "</td></tr>"
  HTMLFile.WriteLine "        <tr><td>Tiempo Máximo de Llenado</td><td>" & FormatNumber(MaxFillTime, 4) & " segundos</td></tr>"
  HTMLFile.WriteLine "        <tr><td>Fecha y Hora del Análisis</td><td>" & Now() & "</td></tr>"
  HTMLFile.WriteLine "      </table>"
  
  ' Tabla de frames con explicación adicional
  HTMLFile.WriteLine "      <h3>Frames de Tiempo de Llenado</h3>"
  HTMLFile.WriteLine "      <p><strong>Nota:</strong> Cada frame muestra el estado del llenado en un momento específico del proceso, expresado como porcentaje del tiempo total y en segundos.</p>"
  HTMLFile.WriteLine "      <table>"
  HTMLFile.WriteLine "        <tr><th>Frame</th><th>Porcentaje (%)</th><th>Tiempo (segundos)</th><th>Interpretación</th></tr>"
  
  ' Generar filas para cada frame
  Dim i, Interpretaciones
  Interpretaciones = Array("Inicio del llenado", "Llenado muy temprano", "Llenado temprano", "Llenado medio-temprano", "Llenado medio", "Llenado medio-avanzado", "Llenado avanzado", "Llenado casi completo", "Llenado completo")
  
  For i = 0 To UBound(Percentages)
    Dim TimeValue, Interpretacion
    TimeValue = (Percentages(i) / 100) * MaxFillTime
    
    If i <= UBound(Interpretaciones) Then
      Interpretacion = Interpretaciones(i)
    Else
      Interpretacion = "Fase " & (i + 1)
    End If
    
    HTMLFile.WriteLine "        <tr><td>" & (i + 1) & "</td><td>" & Percentages(i) & "%</td><td>" & FormatNumber(TimeValue, 4) & "</td><td>" & Interpretacion & "</td></tr>"
  Next
  
  HTMLFile.WriteLine "      </table>"
  
  ' Galería de frames de llenado
  HTMLFile.WriteLine "      <h3>Visualización de Frames de Llenado</h3>"
  HTMLFile.WriteLine "      <p><strong>Tiempo máximo de llenado:</strong> " & FormatNumber(MaxFillTime, 2) & " segundos</p>"
  HTMLFile.WriteLine "      <div class='frame-gallery'>"
  
  ' Verificar que la carpeta de frames existe
  If FSO.FolderExists(FrameFolder) Then
    ' Mostrar cada frame en la galería
    For i = 0 To UBound(FrameImagePaths)
      If FSO.FileExists(FrameImagePaths(i)) Then
        ' Obtener ruta relativa para la imagen
        Dim RelativePath, FrameTime
        RelativePath = FSO.GetFileName(FrameImagePaths(i))
        FrameTime = (Percentages(i) / 100.0) * MaxFillTime
        
        ' Crear elemento de galería para este frame
        HTMLFile.WriteLine "        <div class='frame-item'>"
        HTMLFile.WriteLine "          <img src='" & "Frames_" & CurrentTime & "\\" & RelativePath & "' alt='Frame " & Percentages(i) & "%'>"
        HTMLFile.WriteLine "          <div class='frame-caption'>"
        HTMLFile.WriteLine "            Frame: " & Percentages(i) & "% - " & FormatNumber(FrameTime, 2) & " segundos"
        HTMLFile.WriteLine "          </div>"
        HTMLFile.WriteLine "        </div>"
      End If
    Next
  End If
  
  HTMLFile.WriteLine "      </div>"
  HTMLFile.WriteLine "      <p><em>Imágenes: Visualización de los frames específicos del proceso de llenado. El tiempo máximo de llenado es " & FormatNumber(MaxFillTime, 2) & " segundos.</em></p>"
  
  ' Recomendaciones (específicas de Ficosa)
  HTMLFile.WriteLine "      <h3>Recomendaciones Ficosa</h3>"
  HTMLFile.WriteLine "      <ul>"
  HTMLFile.WriteLine "        <li>Analizar la secuencia de frames para verificar que el patrón de llenado sea uniforme y balanceado.</li>"
  HTMLFile.WriteLine "        <li>Prestar atención a los frames finales (>90%) para identificar áreas con llenado tardío que puedan causar problemas de calidad.</li>"
  HTMLFile.WriteLine "        <li>Comparar los frames intermedios (50-75%) para evaluar la simetría del flujo y detectar posibles desequilibrios.</li>"
  HTMLFile.WriteLine "        <li>Para piezas con requisitos estéticos, verificar en qué momento del llenado (frame) se completan las áreas visibles.</li>"
  HTMLFile.WriteLine "        <li>Utilizar los frames para identificar posibles atrapamientos de aire o líneas de soldadura.</li>"
  HTMLFile.WriteLine "      </ul>"
  
  HTMLFile.WriteLine "      <h3>Nota sobre la interpretación de resultados</h3>"
  HTMLFile.WriteLine "      <p>Para facilitar la interpretación de los resultados, se ha generado un archivo de texto con la correlación entre porcentajes y tiempos reales en segundos. Este archivo se encuentra en: <code>" & TextFilePath & "</code></p>"
  HTMLFile.WriteLine "      <p>Además, cada frame muestra el porcentaje de llenado y su tiempo correspondiente en segundos para una referencia visual rápida.</p>"
  HTMLFile.WriteLine "      <p>Los frames se han guardado como imágenes individuales en la carpeta: <code>" & FrameFolder & "</code></p>"
  
  HTMLFile.WriteLine "    </div>"
  HTMLFile.WriteLine "    <div class='footer'>"
  HTMLFile.WriteLine "      <p>Informe generado automáticamente por FillTimePercentage_Ficosa_01.vbs v4.0 - Visualización de Frames de Llenado</p>"
  HTMLFile.WriteLine "      <p>© " & Year(Now()) & " Ficosa International</p>"
  HTMLFile.WriteLine "    </div>"
  HTMLFile.WriteLine "  </div>"
  HTMLFile.WriteLine "</body>"
  HTMLFile.WriteLine "</html>"
  
  HTMLFile.Close
  
  If Err.Number = 0 Then
    WScript.Echo "Informe HTML generado en: " & HTMLPath
  Else
    WScript.Echo "Error al generar informe HTML: " & Err.Description
  End If
  
  Set FSO = Nothing
  Set HTMLFile = Nothing
End Sub

Private Sub QuitWithError(ErrorMsg)
  ' Mostrar mensaje de error y registrar en archivo de log
  Dim Str, FSO, LogFile, LogPath
  Str = ErrorMsg
  
  ' Registrar error en archivo de log
  On Error Resume Next
  Set FSO = CreateObject("Scripting.FileSystemObject")
  LogPath = "C:\Ficosa_Reports\ErrorLog.txt"
  
  ' Crear directorio si no existe
  If Not FSO.FolderExists("C:\Ficosa_Reports") Then
    FSO.CreateFolder("C:\Ficosa_Reports")
  End If
  
  ' Escribir en el archivo de log
  Set LogFile = FSO.OpenTextFile(LogPath, 8, True) ' 8 = ForAppending, True = Create if not exists
  If Err.Number = 0 Then
    LogFile.WriteLine Now() & " - " & WScript.ScriptName & ": " & Str
    LogFile.Close
  End If
  Set FSO = Nothing
  Set LogFile = Nothing
  On Error GoTo 0
  
  ' Mostrar mensaje de error
  MsgBox Str, vbCritical, WScript.ScriptName
  WScript.Quit
End Sub

Function GetBuildCode()
  On Error Resume Next
  Dim wshShell
  Set wshShell = CreateObject("WScript.Shell")
  
  Dim buildCode
  If Instr(1, wshShell.ExpandEnvironmentStrings("%MF_BUILDCODE%"), "scandium", 1) Then
    buildCode = "Scandium"
  Else
    buildCode = "Synergy"
  End If
  
  ' Clean up
  Set wshShell = Nothing
  
  If Err.Number <> 0 Then
    WScript.Echo "Error in GetBuildCode: " & Err.Description
    buildCode = "Synergy"  ' Default to Synergy on error
  End If
  On Error Goto 0
  
  GetBuildCode = buildCode
End Function