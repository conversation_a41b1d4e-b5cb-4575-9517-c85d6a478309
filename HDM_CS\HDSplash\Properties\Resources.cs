﻿// Decompiled with JetBrains decompiler
// Type: HDSplash.Properties.Resources
// Assembly: HDSplash, Version=1.0.1.0, Culture=neutral, PublicKeyToken=null
// MVID: 57BF8E78-0319-4ECF-A8F5-B637E39756A4
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDSplash.dll

using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;

namespace HDSplash.Properties
{
  [GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
  [DebuggerNonUserCode]
  [CompilerGenerated]
  internal class Resources
  {
    private static ResourceManager resourceMan;
    private static CultureInfo resourceCulture;

    internal Resources()
    {
    }

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    internal static ResourceManager ResourceManager
    {
      get
      {
        if (HDSplash.Properties.Resources.resourceMan == null)
          HDSplash.Properties.Resources.resourceMan = new ResourceManager("HDSplash.Properties.Resources", typeof (HDSplash.Properties.Resources).Assembly);
        return HDSplash.Properties.Resources.resourceMan;
      }
    }

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    internal static CultureInfo Culture
    {
      get => HDSplash.Properties.Resources.resourceCulture;
      set => HDSplash.Properties.Resources.resourceCulture = value;
    }
  }
}
