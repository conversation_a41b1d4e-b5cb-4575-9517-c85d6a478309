﻿// Decompiled with JetBrains decompiler
// Type: Microsoft.Office.Interop.PowerPoint.Shapes
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using Microsoft.Office.Core;
using System.Collections;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

namespace Microsoft.Office.Interop.PowerPoint
{
  [CompilerGenerated]
  [Guid("91493475-5A91-11CF-8700-00AA0060263B")]
  [DefaultMember("Item")]
  [TypeIdentifier]
  [ComImport]
  public interface Shapes : IEnumerable
  {
    [SpecialName]
    [MethodImpl(MethodCodeType = MethodCodeType.Runtime)]
    sealed extern void _VtblGap1_11();

    [DispId(15)]
    [MethodImpl(MethodImplOptions.InternalCall, MethodCodeType = MethodCodeType.Runtime)]
    [return: MarshalAs(UnmanagedType.Interface)]
    Shape AddPicture(
      [MarshalAs(UnmanagedType.BStr), In] string FileName,
      [In] MsoTriState LinkToFile,
      [In] MsoTriState SaveWithDocument,
      [In] float Left,
      [In] float Top,
      [In] float Width = -1f,
      [In] float Height = -1f);

    [SpecialName]
    [MethodImpl(MethodCodeType = MethodCodeType.Runtime)]
    sealed extern void _VtblGap2_3();

    [DispId(19)]
    [MethodImpl(MethodImplOptions.InternalCall, MethodCodeType = MethodCodeType.Runtime)]
    [return: MarshalAs(UnmanagedType.Interface)]
    Shape AddTextbox(
      [In] MsoTextOrientation Orientation,
      [In] float Left,
      [In] float Top,
      [In] float Width,
      [In] float Height);

    [SpecialName]
    [MethodImpl(MethodCodeType = MethodCodeType.Runtime)]
    sealed extern void _VtblGap3_7();

    [DispId(2008)]
    [MethodImpl(MethodImplOptions.InternalCall, MethodCodeType = MethodCodeType.Runtime)]
    [return: MarshalAs(UnmanagedType.Interface)]
    Shape AddOLEObject(
      [In] float Left = 0.0f,
      [In] float Top = 0.0f,
      [In] float Width = -1f,
      [In] float Height = -1f,
      [MarshalAs(UnmanagedType.BStr), In] string ClassName = "",
      [MarshalAs(UnmanagedType.BStr), In] string FileName = "",
      [In] MsoTriState DisplayAsIcon = MsoTriState.msoFalse,
      [MarshalAs(UnmanagedType.BStr), In] string IconFileName = "",
      [In] int IconIndex = 0,
      [MarshalAs(UnmanagedType.BStr), In] string IconLabel = "",
      [In] MsoTriState Link = MsoTriState.msoFalse);

    [SpecialName]
    [MethodImpl(MethodCodeType = MethodCodeType.Runtime)]
    sealed extern void _VtblGap4_1();

    [DispId(2010)]
    [MethodImpl(MethodImplOptions.InternalCall, MethodCodeType = MethodCodeType.Runtime)]
    [return: MarshalAs(UnmanagedType.Interface)]
    Shape AddPlaceholder(
      [In] PpPlaceholderType Type,
      [In] float Left = -1f,
      [In] float Top = -1f,
      [In] float Width = -1f,
      [In] float Height = -1f);
  }
}
