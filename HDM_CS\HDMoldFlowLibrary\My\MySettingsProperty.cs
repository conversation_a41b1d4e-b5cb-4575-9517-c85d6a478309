﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlowLibrary.My.MySettingsProperty
// Assembly: HDMoldFlowLibrary, Version=2.24.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 16E5D508-B892-4B5D-A02C-2C1930D27867
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMoldFlowLibrary.dll

using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System.ComponentModel.Design;
using System.Diagnostics;
using System.Runtime.CompilerServices;

#nullable disable
namespace HDMoldFlowLibrary.My
{
  [StandardModule]
  [HideModuleName]
  [DebuggerNonUserCode]
  [CompilerGenerated]
  internal sealed class MySettingsProperty
  {
    [HelpKeyword("My.Settings")]
    internal static MySettings Settings => MySettings.Default;
  }
}
