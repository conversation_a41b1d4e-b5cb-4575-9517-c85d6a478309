<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ribbonButton_Set_SummaryView.SmallImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAjQIAAAKJUE5HDQoaCgAAAA1JSERSAAAADgAAAA4IBgAAAB9ILdEAAAAEZ0FNQQAAsY8L/GEFAAAAIGNIUk0AAHomAACAhAAA+gAAAIDoAAB1MAAA6mAAADqYAAAXcJy6UTwAAAAJcEhZcwAADsQAAA7EAZUrDhsAAAIDSURBVDhPdZLtTtNQGMcXb8BL8E68CS/DeAUaojG+wBfiYlQwwYXNMAjdQlzcCCIhjODWQtzKZDqIHS/Z5l462q51bbefXZmpRPh/OOc55zm/87ycE+IKOa5DrVZDlEQs02Jf3qfZbuK67vgE/AcaPQO1q45X0B/0xxZUKhXa7bZvXwJ1w0BRFIb2kIbZYMfYIVVPkddFtIGG2lI5OzvzI18CC4UCVaWKObB4dxphrhZho72B8CvBh1YKY2jQ7XTRdC0AdV2nXC5jGzafOxs8O5zixDq98Lk6QlPgwCzTN/tIkhSAR0dH5HI5315sxXlaeo6D7a9HSncyyEbJt2OxWACO8k4kEhwUv1F0ZCaqjxA1CXtoe5FPmKm95YdbYXtzG0n8J+JI2WyW7NaWdwtEm3Hu/3zIpDLF4+MnfDG8bLz9EdTVuhfguaZRPVaQ5BKZzEfmI3PwG6q9U/KWRLPfwupaxONxwuEwjuMQcr1BLsl8bcFdL9jkPuwWy6wkBWZfz7KX2+X9fIyFhQXS6TQ9s+dnF+p4D7r3/ZA76T63ZxrcfNXjhXhOYnkJQRCIRqP+nM/nUdXgY/hg+tM697wSbr3pcWMGHiQLaGoHd+BSr9ex7aC7fxVyPGdSWCa5tsm0qDOxIrO4tIxrB1/tKvnNMU2T9bVVXoanWV/16jB033m94A+POsWel1JTTAAAAABJRU5ErkJgggs=</value>
  </data>
  <data name="ribbonButton5.Image" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton_Set_Rotate.LargeImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton_OpenProject.SmallImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAcQAAAAKJUE5HDQoaCgAAAA1JSERSAAAAEAAAABAIBgAAAB/z/2EAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAE0lEQVQ4T2MYBaNgFIwCMGBgAAAEEAABp0R8YwAAAABJRU5ErkJgggs=</value>
  </data>
  <data name="ribbonButton_Set_Injection.LargeImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="$this.Icon" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>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</value>
  </data>
  <data name="ribbonButton_Set_Gate.Image" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton10.SmallImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAcQAAAAKJUE5HDQoaCgAAAA1JSERSAAAAEAAAABAIBgAAAB/z/2EAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAE0lEQVQ4T2MYBaNgFIwCMGBgAAAEEAABp0R8YwAAAABJRU5ErkJgggs=</value>
  </data>
  <data name="ribbonButton_Set_Runner.Image" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton_Set_InputDB.LargeImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton_Set_Runner.LargeImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton4.LargeImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton5.LargeImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton_Set_Mesh.Image" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton_Set_MidResult.LargeImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton_Set_InputDB.Image" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton_Set_Valve.LargeImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton_Set_MidResult.Image" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton5.SmallImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAcQAAAAKJUE5HDQoaCgAAAA1JSERSAAAAEAAAABAIBgAAAB/z/2EAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAE0lEQVQ4T2MYBaNgFIwCMGBgAAAEEAABp0R8YwAAAABJRU5ErkJgggs=</value>
  </data>
  <data name="ribbonButton6.LargeImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton_Set_SummaryView.Image" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton_Set_Lang.Image" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton7.LargeImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton_Set_Lang.SmallImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAjQIAAAKJUE5HDQoaCgAAAA1JSERSAAAADgAAAA4IBgAAAB9ILdEAAAAEZ0FNQQAAsY8L/GEFAAAAIGNIUk0AAHomAACAhAAA+gAAAIDoAAB1MAAA6mAAADqYAAAXcJy6UTwAAAAJcEhZcwAADsQAAA7EAZUrDhsAAAIDSURBVDhPdZLtTtNQGMcXb8BL8E68CS/DeAUaojG+wBfiYlQwwYXNMAjdQlzcCCIhjODWQtzKZDqIHS/Z5l462q51bbefXZmpRPh/OOc55zm/87ycE+IKOa5DrVZDlEQs02Jf3qfZbuK67vgE/AcaPQO1q45X0B/0xxZUKhXa7bZvXwJ1w0BRFIb2kIbZYMfYIVVPkddFtIGG2lI5OzvzI18CC4UCVaWKObB4dxphrhZho72B8CvBh1YKY2jQ7XTRdC0AdV2nXC5jGzafOxs8O5zixDq98Lk6QlPgwCzTN/tIkhSAR0dH5HI5315sxXlaeo6D7a9HSncyyEbJt2OxWACO8k4kEhwUv1F0ZCaqjxA1CXtoe5FPmKm95YdbYXtzG0n8J+JI2WyW7NaWdwtEm3Hu/3zIpDLF4+MnfDG8bLz9EdTVuhfguaZRPVaQ5BKZzEfmI3PwG6q9U/KWRLPfwupaxONxwuEwjuMQcr1BLsl8bcFdL9jkPuwWy6wkBWZfz7KX2+X9fIyFhQXS6TQ9s+dnF+p4D7r3/ZA76T63ZxrcfNXjhXhOYnkJQRCIRqP+nM/nUdXgY/hg+tM697wSbr3pcWMGHiQLaGoHd+BSr9ex7aC7fxVyPGdSWCa5tsm0qDOxIrO4tIxrB1/tKvnNMU2T9bVVXoanWV/16jB033m94A+POsWel1JTTAAAAABJRU5ErkJgggs=</value>
  </data>
  <data name="ribbonButton7.SmallImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAcQAAAAKJUE5HDQoaCgAAAA1JSERSAAAAEAAAABAIBgAAAB/z/2EAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAE0lEQVQ4T2MYBaNgFIwCMGBgAAAEEAABp0R8YwAAAABJRU5ErkJgggs=</value>
  </data>
  <data name="ribbonButton2.Image" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton_Set_Lang.LargeImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton1.SmallImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAcQAAAAKJUE5HDQoaCgAAAA1JSERSAAAAEAAAABAIBgAAAB/z/2EAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAE0lEQVQ4T2MYBaNgFIwCMGBgAAAEEAABp0R8YwAAAABJRU5ErkJgggs=</value>
  </data>
  <data name="ribbonButton8.LargeImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton_Set_MidResult.SmallImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAjQIAAAKJUE5HDQoaCgAAAA1JSERSAAAADgAAAA4IBgAAAB9ILdEAAAAEZ0FNQQAAsY8L/GEFAAAAIGNIUk0AAHomAACAhAAA+gAAAIDoAAB1MAAA6mAAADqYAAAXcJy6UTwAAAAJcEhZcwAADsQAAA7EAZUrDhsAAAIDSURBVDhPdZLtTtNQGMcXb8BL8E68CS/DeAUaojG+wBfiYlQwwYXNMAjdQlzcCCIhjODWQtzKZDqIHS/Z5l462q51bbefXZmpRPh/OOc55zm/87ycE+IKOa5DrVZDlEQs02Jf3qfZbuK67vgE/AcaPQO1q45X0B/0xxZUKhXa7bZvXwJ1w0BRFIb2kIbZYMfYIVVPkddFtIGG2lI5OzvzI18CC4UCVaWKObB4dxphrhZho72B8CvBh1YKY2jQ7XTRdC0AdV2nXC5jGzafOxs8O5zixDq98Lk6QlPgwCzTN/tIkhSAR0dH5HI5315sxXlaeo6D7a9HSncyyEbJt2OxWACO8k4kEhwUv1F0ZCaqjxA1CXtoe5FPmKm95YdbYXtzG0n8J+JI2WyW7NaWdwtEm3Hu/3zIpDLF4+MnfDG8bLz9EdTVuhfguaZRPVaQ5BKZzEfmI3PwG6q9U/KWRLPfwupaxONxwuEwjuMQcr1BLsl8bcFdL9jkPuwWy6wkBWZfz7KX2+X9fIyFhQXS6TQ9s+dnF+p4D7r3/ZA76T63ZxrcfNXjhXhOYnkJQRCIRqP+nM/nUdXgY/hg+tM697wSbr3pcWMGHiQLaGoHd+BSr9ex7aC7fxVyPGdSWCa5tsm0qDOxIrO4tIxrB1/tKvnNMU2T9bVVXoanWV/16jB033m94A+POsWel1JTTAAAAABJRU5ErkJgggs=</value>
  </data>
  <data name="ribbonButton9.LargeImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton4.SmallImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAcQAAAAKJUE5HDQoaCgAAAA1JSERSAAAAEAAAABAIBgAAAB/z/2EAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAE0lEQVQ4T2MYBaNgFIwCMGBgAAAEEAABp0R8YwAAAABJRU5ErkJgggs=</value>
  </data>
  <data name="ribbonButton_Set_ProcDB.Image" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton3.SmallImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAcQAAAAKJUE5HDQoaCgAAAA1JSERSAAAAEAAAABAIBgAAAB/z/2EAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAE0lEQVQ4T2MYBaNgFIwCMGBgAAAEEAABp0R8YwAAAABJRU5ErkJgggs=</value>
  </data>
  <data name="ribbonButton_Set_Valve.Image" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton3.Image" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton6.Image" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton_Set_Rotate.Image" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton6.SmallImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAcQAAAAKJUE5HDQoaCgAAAA1JSERSAAAAEAAAABAIBgAAAB/z/2EAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAE0lEQVQ4T2MYBaNgFIwCMGBgAAAEEAABp0R8YwAAAABJRU5ErkJgggs=</value>
  </data>
  <data name="ribbonButton8.Image" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton_Set_Gate.LargeImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="newButton_Mesh.Image" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAXwMAAAKJUE5HDQoaCgAAAA1JSERSAAAAEAAAABAIBgAAAB/z/2EAAAAEZ0FNQQAAsY8L/GEFAAAAIGNIUk0AAHomAACAhAAA+gAAAIDoAAB1MAAA6mAAADqYAAAXcJy6UTwAAAAJcEhZcwAADsQAAA7EAZUrDhsAAALVSURBVDhPXZFZTxNhFIb5N0bjhdErr9QLE4MxamJijBoE1IAxGi9IFEFAEUVcUDGoUaQgLggKCNLKIlJogU5b2o5iV0qkpbaAltLFCjzOtGXRJzkzyfud953znUljmUVYiCwRDsQJfo+t1JxHqsk48944izGp6T9WAuLBRdzNUcwVYUzXQ5jKpCqdx1wcojtrgr4iO/2mWeo0kwTmYinXmoCAGMVUMofvMYjlIYSrAbQFPgYvTmGodmF2zvBwNMgt9TRtRh9/FpLTrAT4TWEsRb/QFUzRf8OG8NaKusGM+GEc748INcZflGvC3Bqco7DVgeabL+FbDTBHEC+HaD9uwTmQPFym0/6bO8YFzrb/5OQbD4UqPwUtVgKh2L8B5kvztGSI2Aa8KRWGnCEyFRaya42cb58k+4me9Px6tp2uolDxae0VIhjyQzQdMWNXT6VUmAr+YdQbpUX/nbx3dvaXtXKveRCbZwa3P7hmieYougtBGg8ZcahXJ4ClxFPvmibn5Ri7i9/S0D2a0GT+mUCQAw7rsak9CW1pMWmW0bkC5LwYI72omXqlISlKx2l91RZ671lQV1oxXpulNfcLymsGuist9NwXk40SI84AJ+pFdhU2UafSJ7QlOaCjRODJvj40pR6EUj+ai360V7w83f+ZoWfORKPMiNNPVu0oO/NfovgopFQpIDIb53luH7V7NbQdstKZ6UKxV0t/5erXZUwT0xx7qmdHXj0KpS6lpnbgHvnBo4MqGg7oeLZHS0feMH5PmDe6cRqHXDQNu6hSfeXcKwtbz9RQ1zmSMMsklyjdRWi0U7FdWtDhHibEn9Ro3ZQrHdzsclChtFH6fozcWgMbsqtoGbAkbDIrf2F+Ooqm7itGlYNT99tYl1HJppxqtpx8wOYTVWzMusv6jNscLXvNuG825VoTsEw4FmdQnKBLsNKrd9Ar2KS3nS6dlSHRzUwwkuqUgb/K4W5WU+QVGwAAAABJRU5ErkJgggs=</value>
  </data>
  <data name="ribbonButton9.SmallImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAcQAAAAKJUE5HDQoaCgAAAA1JSERSAAAAEAAAABAIBgAAAB/z/2EAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAE0lEQVQ4T2MYBaNgFIwCMGBgAAAEEAABp0R8YwAAAABJRU5ErkJgggs=</value>
  </data>
  <data name="ribbonButton2.SmallImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAcQAAAAKJUE5HDQoaCgAAAA1JSERSAAAAEAAAABAIBgAAAB/z/2EAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAE0lEQVQ4T2MYBaNgFIwCMGBgAAAEEAABp0R8YwAAAABJRU5ErkJgggs=</value>
  </data>
  <data name="ribbonButton7.Image" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton_CreateBigData.SmallImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAcQAAAAKJUE5HDQoaCgAAAA1JSERSAAAAEAAAABAIBgAAAB/z/2EAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAE0lEQVQ4T2MYBaNgFIwCMGBgAAAEEAABp0R8YwAAAABJRU5ErkJgggs=</value>
  </data>
  <data name="ribbonButton9.Image" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton_Set_ProcDB.LargeImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton8.SmallImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAcQAAAAKJUE5HDQoaCgAAAA1JSERSAAAAEAAAABAIBgAAAB/z/2EAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAE0lEQVQ4T2MYBaNgFIwCMGBgAAAEEAABp0R8YwAAAABJRU5ErkJgggs=</value>
  </data>
  <data name="ribbonButton_NewProject.SmallImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAcQAAAAKJUE5HDQoaCgAAAA1JSERSAAAAEAAAABAIBgAAAB/z/2EAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAE0lEQVQ4T2MYBaNgFIwCMGBgAAAEEAABp0R8YwAAAABJRU5ErkJgggs=</value>
  </data>
  <data name="ribbonButton_Close.SmallImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAcQAAAAKJUE5HDQoaCgAAAA1JSERSAAAAEAAAABAIBgAAAB/z/2EAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAE0lEQVQ4T2MYBaNgFIwCMGBgAAAEEAABp0R8YwAAAABJRU5ErkJgggs=</value>
  </data>
  <data name="ribbonButton10.Image" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton10.LargeImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton_Set_ProcDB.SmallImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAjQIAAAKJUE5HDQoaCgAAAA1JSERSAAAADgAAAA4IBgAAAB9ILdEAAAAEZ0FNQQAAsY8L/GEFAAAAIGNIUk0AAHomAACAhAAA+gAAAIDoAAB1MAAA6mAAADqYAAAXcJy6UTwAAAAJcEhZcwAADsQAAA7EAZUrDhsAAAIDSURBVDhPdZLtTtNQGMcXb8BL8E68CS/DeAUaojG+wBfiYlQwwYXNMAjdQlzcCCIhjODWQtzKZDqIHS/Z5l462q51bbefXZmpRPh/OOc55zm/87ycE+IKOa5DrVZDlEQs02Jf3qfZbuK67vgE/AcaPQO1q45X0B/0xxZUKhXa7bZvXwJ1w0BRFIb2kIbZYMfYIVVPkddFtIGG2lI5OzvzI18CC4UCVaWKObB4dxphrhZho72B8CvBh1YKY2jQ7XTRdC0AdV2nXC5jGzafOxs8O5zixDq98Lk6QlPgwCzTN/tIkhSAR0dH5HI5315sxXlaeo6D7a9HSncyyEbJt2OxWACO8k4kEhwUv1F0ZCaqjxA1CXtoe5FPmKm95YdbYXtzG0n8J+JI2WyW7NaWdwtEm3Hu/3zIpDLF4+MnfDG8bLz9EdTVuhfguaZRPVaQ5BKZzEfmI3PwG6q9U/KWRLPfwupaxONxwuEwjuMQcr1BLsl8bcFdL9jkPuwWy6wkBWZfz7KX2+X9fIyFhQXS6TQ9s+dnF+p4D7r3/ZA76T63ZxrcfNXjhXhOYnkJQRCIRqP+nM/nUdXgY/hg+tM697wSbr3pcWMGHiQLaGoHd+BSr9ex7aC7fxVyPGdSWCa5tsm0qDOxIrO4tIxrB1/tKvnNMU2T9bVVXoanWV/16jB033m94A+POsWel1JTTAAAAABJRU5ErkJgggs=</value>
  </data>
  <data name="ribbonButton_Set_MeshStat.Image" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton_Info.SmallImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAcQAAAAKJUE5HDQoaCgAAAA1JSERSAAAAEAAAABAIBgAAAB/z/2EAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAE0lEQVQ4T2MYBaNgFIwCMGBgAAAEEAABp0R8YwAAAABJRU5ErkJgggs=</value>
  </data>
  <data name="ribbonButton_Set_SummaryView.LargeImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton1.LargeImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton_Set_Injection.Image" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton2.LargeImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton1.Image" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton_Set_Runner.SmallImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAjQIAAAKJUE5HDQoaCgAAAA1JSERSAAAADgAAAA4IBgAAAB9ILdEAAAAEZ0FNQQAAsY8L/GEFAAAAIGNIUk0AAHomAACAhAAA+gAAAIDoAAB1MAAA6mAAADqYAAAXcJy6UTwAAAAJcEhZcwAADsQAAA7EAZUrDhsAAAIDSURBVDhPdZLtTtNQGMcXb8BL8E68CS/DeAUaojG+wBfiYlQwwYXNMAjdQlzcCCIhjODWQtzKZDqIHS/Z5l462q51bbefXZmpRPh/OOc55zm/87ycE+IKOa5DrVZDlEQs02Jf3qfZbuK67vgE/AcaPQO1q45X0B/0xxZUKhXa7bZvXwJ1w0BRFIb2kIbZYMfYIVVPkddFtIGG2lI5OzvzI18CC4UCVaWKObB4dxphrhZho72B8CvBh1YKY2jQ7XTRdC0AdV2nXC5jGzafOxs8O5zixDq98Lk6QlPgwCzTN/tIkhSAR0dH5HI5315sxXlaeo6D7a9HSncyyEbJt2OxWACO8k4kEhwUv1F0ZCaqjxA1CXtoe5FPmKm95YdbYXtzG0n8J+JI2WyW7NaWdwtEm3Hu/3zIpDLF4+MnfDG8bLz9EdTVuhfguaZRPVaQ5BKZzEfmI3PwG6q9U/KWRLPfwupaxONxwuEwjuMQcr1BLsl8bcFdL9jkPuwWy6wkBWZfz7KX2+X9fIyFhQXS6TQ9s+dnF+p4D7r3/ZA76T63ZxrcfNXjhXhOYnkJQRCIRqP+nM/nUdXgY/hg+tM697wSbr3pcWMGHiQLaGoHd+BSr9ex7aC7fxVyPGdSWCa5tsm0qDOxIrO4tIxrB1/tKvnNMU2T9bVVXoanWV/16jB033m94A+POsWel1JTTAAAAABJRU5ErkJgggs=</value>
  </data>
  <data name="ribbonButton4.Image" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton3.LargeImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton_Set_MeshStat.LargeImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
  <data name="ribbonButton_Set_Mesh.LargeImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABVTeXN0ZW0uRHJhd2luZy5CaXRtYXABAAAABERhdGEHAgIAAAAJAwAAAA8DAAAAeAAAAAKJUE5HDQoaCgAAAA1JSERSAAAAIAAAACAIBgAAAHN6evQAAAAEZ0FNQQAAsY8L/GEFAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGklEQVRYR+3BAQEAAACCIP+vbkhAAAAAwLkaECAAAZ14SUMAAAAASUVORK5CYIIL</value>
  </data>
</root>