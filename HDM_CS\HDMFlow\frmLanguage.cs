﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmLanguage
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDMFUserControl;
using HDMoldFlow.Properties;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public class frmLanguage : Form
  {
    private const int WS_EX_COMPOSITED = 33554432;
    public string m_strLa;
    private IContainer components = (IContainer) null;
    private NewButton newButton_Apply;
    private Label label_Language;
    private NewComboBox newComboBox_LangType;

    protected override CreateParams CreateParams
    {
      get
      {
        CreateParams createParams = base.CreateParams;
        createParams.ExStyle |= 33554432;
        return createParams;
      }
    }

    public frmLanguage()
    {
      this.InitializeComponent();
      this.Text = LocaleControl.getInstance().GetString("IDS_LANG_SETTING");
      this.label_Language.Text = LocaleControl.getInstance().GetString("IDS_LANG");
      this.newButton_Apply.ButtonText = LocaleControl.getInstance().GetString("IDS_APPLY");
    }

    private void frmInfo_Load(object sender, EventArgs e)
    {
      this.newComboBox_LangType.Items.AddRange((object[]) clsDefine.g_dicLangType.Values.ToArray<string>());
      string key = clsUtill.ReadINI("Option", "Lang", clsDefine.g_fiLangCfg.FullName);
      this.newComboBox_LangType.SelectedIndex = this.newComboBox_LangType.Items.IndexOf((object) clsDefine.g_dicLangType[key]);
    }

    private void frmInfo_KeyDown(object sender, KeyEventArgs e)
    {
      if (e.KeyCode == Keys.Escape)
      {
        this.Close();
      }
      else
      {
        if (e.KeyCode != Keys.Return)
          return;
        this.SetLangType();
        this.DialogResult = DialogResult.OK;
        this.Close();
      }
    }

    private void newButton_Apply_NewClick(object sender, EventArgs e)
    {
      this.SetLangType();
      this.DialogResult = DialogResult.OK;
      this.Close();
    }

    private void SetLangType() => clsUtill.WriteINI("Option", "Lang", clsDefine.g_dicLangType.FirstOrDefault<KeyValuePair<string, string>>((Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Value == this.newComboBox_LangType.Value)).Key, clsDefine.g_fiLangCfg.FullName);

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.label_Language = new Label();
      this.newComboBox_LangType = new NewComboBox();
      this.newButton_Apply = new NewButton();
      this.SuspendLayout();
      this.label_Language.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Language.BorderStyle = BorderStyle.FixedSingle;
      this.label_Language.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Language.ForeColor = Color.MidnightBlue;
      this.label_Language.Location = new Point(4, 4);
      this.label_Language.Name = "label_Language";
      this.label_Language.Size = new Size(68, 24);
      this.label_Language.TabIndex = 25;
      this.label_Language.Text = "언어";
      this.label_Language.TextAlign = ContentAlignment.MiddleCenter;
      this.newComboBox_LangType.BackColor = Color.White;
      this.newComboBox_LangType.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_LangType.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_LangType.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_LangType.isSameSelect = false;
      this.newComboBox_LangType.Location = new Point(69, 4);
      this.newComboBox_LangType.Name = "newComboBox_LangType";
      this.newComboBox_LangType.SelectedIndex = -1;
      this.newComboBox_LangType.Size = new Size(143, 24);
      this.newComboBox_LangType.TabIndex = 26;
      this.newComboBox_LangType.TextAlign = HorizontalAlignment.Center;
      this.newComboBox_LangType.Value = "";
      this.newButton_Apply.ButtonBackColor = Color.White;
      this.newButton_Apply.ButtonText = "적용";
      this.newButton_Apply.FlatBorderSize = 1;
      this.newButton_Apply.FlatStyle = FlatStyle.Flat;
      this.newButton_Apply.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Apply.Image = (Image) Resources.OK;
      this.newButton_Apply.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Apply.Location = new Point(4, 27);
      this.newButton_Apply.Name = "newButton_Apply";
      this.newButton_Apply.Size = new Size(208, 24);
      this.newButton_Apply.TabIndex = 22;
      this.newButton_Apply.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Apply.NewClick += new EventHandler(this.newButton_Apply_NewClick);
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(216, 54);
      this.Controls.Add((Control) this.label_Language);
      this.Controls.Add((Control) this.newButton_Apply);
      this.Controls.Add((Control) this.newComboBox_LangType);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmLanguage);
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "언어 설정";
      this.Load += new EventHandler(this.frmInfo_Load);
      this.KeyDown += new KeyEventHandler(this.frmInfo_KeyDown);
      this.ResumeLayout(false);
    }
  }
}
