﻿// Decompiled with JetBrains decompiler
// Type: HDMFUserControl.NewComboBox
// Assembly: HDMFUserControl, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 4651D75A-87CE-415F-80A5-EBC4E2EC2106
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFUserControl.dll

using System;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace HDMFUserControl
{
  public class NewComboBox : UserControl
  {
    private const int WS_EX_COMPOSITED = 33554432;
    private string strCbValue = "";
    private bool m_isSameSelect;
    private HorizontalAlignment cbTextAlign;
    private IContainer components;
    private ComboBox comboBox_Main;
    private Label label_Focus;

    protected override CreateParams CreateParams
    {
      get
      {
        CreateParams createParams = base.CreateParams;
        createParams.ExStyle |= 33554432;
        return createParams;
      }
    }

    public ComboBox ComboBox => this.comboBox_Main;

    public NewComboBox()
    {
      this.InitializeComponent();
      this.cbTextAlign = HorizontalAlignment.Left;
      this.comboBox_Main.SelectedValueChanged += new EventHandler(this.ComboBox_Main_SelectedValueChanged);
      this.comboBox_Main.SelectedIndexChanged += new EventHandler(this.ComboBox_Main_SelectedIndexChanged);
      this.comboBox_Main.DrawItem += new DrawItemEventHandler(this.ComboBox_Main_DrawItem);
    }

    public event EventHandler SelectedValueChanged;

    private void ComboBox_Main_SelectedValueChanged(object sender, EventArgs e)
    {
      if (this.SelectedValueChanged != null)
        this.SelectedValueChanged((object) this, e);
      this.ActiveControl = (Control) this.label_Focus;
    }

    public event EventHandler SelectedIndexChanged;

    private void ComboBox_Main_SelectedIndexChanged(object sender, EventArgs e)
    {
      if (this.SelectedIndexChanged != null)
      {
        if (this.m_isSameSelect && this.strCbValue == this.comboBox_Main.Text)
          return;
        this.strCbValue = this.comboBox_Main.Text;
        this.SelectedIndexChanged((object) this, e);
      }
      this.ActiveControl = (Control) this.label_Focus;
    }

    public bool isSameSelect
    {
      get => this.m_isSameSelect;
      set => this.m_isSameSelect = value;
    }

    public Color ComboBoxBackColor
    {
      get => this.comboBox_Main.BackColor;
      set => this.comboBox_Main.BackColor = value;
    }

    public int SelectedIndex
    {
      get => this.comboBox_Main.SelectedIndex;
      set => this.comboBox_Main.SelectedIndex = value;
    }

    public ComboBox.ObjectCollection Items
    {
      get => this.comboBox_Main.Items;
      set => this.comboBox_Main.Items.AddRange(value.Cast<object>().ToArray<object>());
    }

    public string Value
    {
      get => this.comboBox_Main.Text;
      set => this.comboBox_Main.Text = value;
    }

    public HorizontalAlignment TextAlign
    {
      get => this.cbTextAlign;
      set => this.cbTextAlign = value;
    }

    private void ComboBox_Main_DrawItem(object sender, DrawItemEventArgs e)
    {
      if (!(sender is ComboBox comboBox))
        return;
      e.DrawBackground();
      if (e.Index < 0)
        return;
      StringFormat format = new StringFormat();
      format.LineAlignment = StringAlignment.Center;
      format.Alignment = this.cbTextAlign != HorizontalAlignment.Left ? (this.cbTextAlign != HorizontalAlignment.Center ? StringAlignment.Far : StringAlignment.Center) : StringAlignment.Near;
      Brush brush = (Brush) new SolidBrush(comboBox.ForeColor);
      if ((e.State & DrawItemState.Selected) == DrawItemState.Selected)
        brush = SystemBrushes.HighlightText;
      e.Graphics.DrawString(comboBox.Items[e.Index].ToString(), comboBox.Font, brush, (RectangleF) e.Bounds, format);
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.comboBox_Main = new ComboBox();
      this.label_Focus = new Label();
      this.SuspendLayout();
      this.comboBox_Main.Dock = DockStyle.Fill;
      this.comboBox_Main.DrawMode = DrawMode.OwnerDrawFixed;
      this.comboBox_Main.DropDownStyle = ComboBoxStyle.DropDownList;
      this.comboBox_Main.FlatStyle = FlatStyle.Flat;
      this.comboBox_Main.FormattingEnabled = true;
      this.comboBox_Main.Location = new Point(0, 0);
      this.comboBox_Main.Name = "comboBox_Main";
      this.comboBox_Main.Size = new Size(150, 24);
      this.comboBox_Main.TabIndex = 0;
      this.comboBox_Main.TabStop = false;
      this.label_Focus.AutoSize = true;
      this.label_Focus.Location = new Point(78, 3);
      this.label_Focus.Name = "label_Focus";
      this.label_Focus.Size = new Size(38, 15);
      this.label_Focus.TabIndex = 1;
      this.label_Focus.Text = "label1";
      this.label_Focus.Visible = false;
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.Controls.Add((Control) this.comboBox_Main);
      this.Controls.Add((Control) this.label_Focus);
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.Name = nameof (NewComboBox);
      this.Size = new Size(150, 24);
      this.ResumeLayout(false);
      this.PerformLayout();
    }
  }
}
