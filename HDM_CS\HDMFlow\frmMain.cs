﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmMain
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDLog4Net;
using HDMFAI;
using HDMFReport;
using HDMFSummary;
using HDMFUserControl;
using HDMoldFlow.Properties;
using HDMoldFlowLibrary;
using HDSplash;
using Microsoft.WindowsAPICodePack.Dialogs;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Windows.Forms.Layout;

namespace HDMoldFlow
{
  public class frmMain : Form
  {
    private const int WS_EX_COMPOSITED = 33554432;
    public FrmSplash FormSplash = new FrmSplash();
    public object m_Lock = new object();
    private Dictionary<string, DataRow> m_lst_dicComparison = new Dictionary<string, DataRow>();
    private const string m_strPastCases = "PAST_Cases";
    private bool isNodeClick = false;
    private IContainer components = (IContainer) null;
    private RibbonButton ribbonButton2;
    private RibbonButton ribbonButton3;
    private RibbonButton ribbonButton4;
    private RibbonButton ribbonButton5;
    private RibbonButton ribbonButton6;
    private RibbonButton ribbonButton7;
    private RibbonButton ribbonButton8;
    private RibbonButton ribbonButton9;
    private RibbonPanel ribbonPanel2;
    private RibbonButton ribbonButton10;
    private Ribbon ribbon_Main;
    private RibbonTab ribbonTab_Home;
    private RibbonPanel ribbonPanel1;
    private RibbonButton ribbonButton_NewProject;
    private RibbonButton ribbonButton_OpenProject;
    private RibbonPanel ribbonPanel3;
    private RibbonButton ribbonButton_Info;
    private RibbonButton ribbonButton_Close;
    private RibbonTab ribbonTab_Setting;
    private RibbonPanel ribbonPanel4;
    private RibbonButton ribbonButton_Set_Mesh;
    private RibbonButton ribbonButton_Set_Runner;
    private RibbonButton ribbonButton_Set_MidResult;
    private RibbonButton ribbonButton_Set_SummaryView;
    private RibbonButton ribbonButton_Set_ProcDB;
    private SplitContainer splitContainer1;
    private Label label_Focus;
    private Label label_Monitoring_Analysis;
    private SplitContainer splitContainer2;
    private TreeView treeView_Product;
    private DataGridViewCheckBoxColumn dataGridViewCheckBoxColumn1;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn1;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn2;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn3;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn4;
    private Panel panel4;
    private DataGridView dataGridView_Monitor;
    private ImageList imageList_TreeView;
    private TabControl tabControl_Main;
    private TabPage tabPage_Study;
    private TabPage tabPage_Report;
    private SplitContainer splitContainer4;
    private DataGridView dataGridView_Dual;
    private DataGridView dataGridView_3D;
    private Panel panel_HDMFlow;
    private Label label3;
    private NewButton newButton_Dual_Del;
    private NewButton newButton_Dual_Add;
    private Panel panel2;
    private Panel panel3;
    private Label label1;
    private NewButton newButton_3D_Del;
    private NewButton newButton_3D_Add;
    private SplitContainer splitContainer3;
    private Panel panel_Mesh;
    private SplitContainer splitContainer6;
    private SplitContainer splitContainer_Report;
    private NewButton newButton_Report;
    private DataGridViewTextBoxColumn Column_Monitor_Product;
    private DataGridViewTextBoxColumn Column_Monitor_Study;
    private DataGridViewTextBoxColumn Column_Monitor_Mesh;
    private DataGridViewTextBoxColumn Column_Monitor_Sequence;
    private DataGridViewTextBoxColumn Column_Monitor_Status;
    private RibbonButton ribbonButton_Set_Injection;
    private SplitContainer splitContainer8;
    private NewButton newButton_Mesh;
    private SplitContainer splitContainer5;
    private Label label5;
    private Label label_Dual;
    private Label label_3D;
    private Label label6;
    private Label label_STD_Study;
    private NewTextBox newTextBox_RotAng;
    private NewComboBox newComboBox_RotateAxis;
    private NewButton newButton_Rotate;
    private Label label_Rotate_Model;
    private RibbonButton ribbonButton_Set_Lang;
    private PictureBox pictureBox_HDSolutions;
    private TabPage tabPage_Comparison;
    private SplitContainer splitContainer7;
    private SplitContainer splitContainer9;
    private Panel panel5;
    private Label label2;
    private DataGridView dataGridView_List;
    private Panel panel7;
    private Label label4;
    private RadioButton radioButton_Case3;
    private RadioButton radioButton_Case2;
    private NewButton newButton_Comparison;
    private Label label8;
    private DataGridView dataGridView1;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn7;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn8;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn9;
    private TableLayoutPanel tableLayoutPanel_CMPR;
    private Panel panel13;
    private DataGridView dataGridView5;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn14;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn15;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn16;
    private Panel panel8;
    private DataGridView dataGridView2;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn5;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn6;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn10;
    private Label label10;
    private Label label9;
    private Panel panel_Compare1;
    private TabPage tabPage_PastCases;
    private Panel panel_Report;
    private Panel panel6;
    private NewButton newButton1;
    private TextBox textBox3;
    private TextBox textBox2;
    private TextBox textBox4;
    private TextBox textBox1;
    private DataGridView dataGridView3;
    private DataGridViewTextBoxColumn Column4;
    private DataGridViewTextBoxColumn Column5;
    private DataGridViewTextBoxColumn Column6;
    private DataGridViewTextBoxColumn Column7;
    private DataGridViewTextBoxColumn Column8;
    private DataGridViewTextBoxColumn Column9;
    private CheckBox checkBox1;
    private Label label17;
    private Label label16;
    private Label label7;
    private Label label15;
    private Panel panel9;
    private Label label11;
    private NewButton newButton_Past_Del;
    private NewButton newButton_Past_Add;
    private SplitContainer splitContainer10;
    private Panel panel_PastCases;
    private NewButton newButton_Past_Report;
    private Panel panel11;
    private NewButton newButton2;
    private TextBox textBox5;
    private TextBox textBox6;
    private TextBox textBox7;
    private TextBox textBox8;
    private DataGridView dataGridView4;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn11;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn12;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn13;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn17;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn18;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn19;
    private CheckBox checkBox2;
    private Label label12;
    private Label label13;
    private Label label14;
    private Label label18;
    private Label label_Template;
    private DataGridViewTextBoxColumn Column_Template;
    private DataGridViewTextBoxColumn Column_Study;
    private DataGridViewTextBoxColumn Column_Mesh;
    private DataGridViewTextBoxColumn Column_Sequence;
    private RibbonButton ribbonButton_Set_Gate;
    private RibbonButton ribbonButton_Set_MeshStat;
    private RibbonButton ribbonButton_Set_Rotate;
    private Label label_Dual_MeshQuality;
    private RibbonButton ribbonButton_Set_Valve;
    private RibbonButton ribbonButton1;
    private RibbonTab ribbonTab_AI;
    private RibbonButton ribbonButton_CreateBigData;
    private RibbonPanel ribbonPanel5;
    private NewButton newButton_Dual_Update;
    private NewButton newButton_3D_Update;
    private RibbonPanel ribbonPanel6;
    private RibbonButton ribbonButton_Set_InputDB;
    private TabPage tabPage_AI_Study;
    private SplitContainer splitContainer11;
    private Panel panel10;
    private Label label20;
    private DataGridView dataGridView_AI_Dual;
    private Panel panel12;
    private Label label21;
    private DataGridView dataGridView_AI_3D;
    private NewButton newButton_Analysis;
    private SplitContainer splitContainer12;
    private NewButton newButton_AISolution;
    private TabPage tabPage_AI_Result;
    private SplitContainer splitContainer13;
    private Panel panel_AIResult;
    private Panel panel15;
    private NewButton newButton3;
    private TextBox textBox9;
    private TextBox textBox10;
    private TextBox textBox11;
    private TextBox textBox12;
    private CheckBox checkBox3;
    private Label label19;
    private Label label22;
    private Label label23;
    private Label label24;
    private NewButton newButton_AIResult;
    private Panel panel_Rotate;

    protected override CreateParams CreateParams
    {
      get
      {
        CreateParams createParams = base.CreateParams;
        createParams.ExStyle |= 33554432;
        return createParams;
      }
    }

    public frmMain()
    {
      this.InitializeComponent();
      clsDefine.g_isNetworkLicense = clsLicense.CheckLicenseType();
      if (clsDefine.g_isNetworkLicense)
        clsLicense.CheckNetworkLience((Form) this);
      else
        clsLicense.CheckLicense();
      clsDefine.g_isAILicense = clsLicense.CheckAILicense();
      this.FormSplash.Controls["ProductNameLabel"].ForeColor = Color.White;
      this.FormSplash.showSplash("HDMoldFlow", Application.StartupPath + "\\Splash.png");
      this.ribbonButton_NewProject.LargeImage = clsUtill.ChangeSyncBackgroundImage(this.ribbonButton_NewProject.LargeImage);
      this.ribbonButton_OpenProject.LargeImage = clsUtill.ChangeSyncBackgroundImage(this.ribbonButton_OpenProject.LargeImage);
      this.ribbonButton_Info.LargeImage = clsUtill.ChangeSyncBackgroundImage(this.ribbonButton_Info.LargeImage);
      this.ribbonButton_Close.LargeImage = clsUtill.ChangeSyncBackgroundImage(this.ribbonButton_Close.LargeImage);
      this.ribbonButton_Set_Mesh.SmallImage = clsUtill.ChangeSyncBackgroundImage(this.ribbonButton_Set_Mesh.SmallImage);
      this.ribbonButton_Set_Runner.SmallImage = clsUtill.ChangeSyncBackgroundImage(this.ribbonButton_Set_Runner.SmallImage);
      this.ribbonButton_Set_Injection.SmallImage = clsUtill.ChangeSyncBackgroundImage(this.ribbonButton_Set_Injection.SmallImage);
      this.ribbonButton_Set_MidResult.SmallImage = clsUtill.ChangeSyncBackgroundImage(this.ribbonButton_Set_MidResult.SmallImage);
      this.ribbonButton_Set_SummaryView.SmallImage = clsUtill.ChangeSyncBackgroundImage(this.ribbonButton_Set_SummaryView.SmallImage);
      this.ribbonButton_Set_ProcDB.SmallImage = clsUtill.ChangeSyncBackgroundImage(this.ribbonButton_Set_ProcDB.SmallImage);
      this.newButton_Dual_Add.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Dual_Add.Image);
      this.newButton_Dual_Del.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Dual_Del.Image);
      this.newButton_Dual_Update.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Dual_Update.Image);
      this.newButton_Mesh.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Mesh.Image);
      this.newButton_3D_Add.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_3D_Add.Image);
      this.newButton_3D_Del.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_3D_Del.Image);
      this.newButton_3D_Update.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_3D_Update.Image);
      this.newButton_Analysis.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Analysis.Image);
      this.newButton_Report.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Report.Image);
      this.newButton_Past_Add.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Past_Add.Image);
      this.newButton_Past_Del.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Past_Del.Image);
      this.newButton_Rotate.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Rotate.Image);
      this.SetLocale();
    }

    private void frmMain_Load(object sender, EventArgs e)
    {
      this.ActiveControl = (Control) this.label_Focus;
      try
      {
        this.imageList_TreeView.Images.Add((Image) Resources.AllProduct);
        this.imageList_TreeView.Images.Add((Image) Resources.Product);
        this.treeView_Product.ImageList = this.imageList_TreeView;
        clsDefine.g_dicLangType = clsUtill.GetINIDataFromSection(clsDefine.g_fiLangCfg.FullName, "LangType");
        clsHDMFLibDefine.m_strMFDPath = clsDefine.g_strPath;
        clsHDMFLibDefine.m_strTmpDPath = clsDefine.g_strPath + "\\Temp";
        if (clsDefine.g_diTmpReport.Exists)
          clsDefine.g_diTmpReport.Delete(true);
        clsDefine.g_diTmpReport.Create();
        DataGridView dataGridView1 = (DataGridView) null;
        string str1 = "";
        for (int index = 0; index < 2; ++index)
        {
          DataGridView dataGridView2;
          string str2;
          if (index == 0)
          {
            dataGridView2 = this.dataGridView_Dual;
            str2 = "Dual";
          }
          else
          {
            dataGridView2 = this.dataGridView_3D;
            str2 = "3D";
          }
          DataGridViewCheckBoxColumn viewCheckBoxColumn = new DataGridViewCheckBoxColumn();
          viewCheckBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
          viewCheckBoxColumn.Frozen = true;
          viewCheckBoxColumn.HeaderText = "";
          viewCheckBoxColumn.Name = "Column_" + str2 + "_Check";
          viewCheckBoxColumn.SortMode = DataGridViewColumnSortMode.NotSortable;
          viewCheckBoxColumn.Width = 20;
          dataGridView2.Columns.Add((DataGridViewColumn) viewCheckBoxColumn);
          DataGridViewTextBoxColumn viewTextBoxColumn1 = new DataGridViewTextBoxColumn();
          viewTextBoxColumn1.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
          viewTextBoxColumn1.Frozen = true;
          viewTextBoxColumn1.HeaderText = LocaleControl.getInstance().GetString("IDS_PRODUCT");
          viewTextBoxColumn1.Name = "Column_" + str2 + "_Product";
          viewTextBoxColumn1.SortMode = DataGridViewColumnSortMode.NotSortable;
          viewTextBoxColumn1.Width = 129;
          viewTextBoxColumn1.ReadOnly = true;
          dataGridView2.Columns.Add((DataGridViewColumn) viewTextBoxColumn1);
          DataGridViewTextBoxColumn viewTextBoxColumn2 = new DataGridViewTextBoxColumn();
          viewTextBoxColumn2.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
          viewTextBoxColumn2.Frozen = true;
          viewTextBoxColumn2.HeaderText = LocaleControl.getInstance().GetString("IDS_STUDY");
          viewTextBoxColumn2.Name = "Column_" + str2 + "_Study";
          viewTextBoxColumn2.SortMode = DataGridViewColumnSortMode.NotSortable;
          viewTextBoxColumn2.Width = 611;
          viewTextBoxColumn2.ReadOnly = true;
          dataGridView2.Columns.Add((DataGridViewColumn) viewTextBoxColumn2);
          DataGridViewTextBoxColumn viewTextBoxColumn3 = new DataGridViewTextBoxColumn();
          viewTextBoxColumn3.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
          viewTextBoxColumn3.Frozen = true;
          viewTextBoxColumn3.HeaderText = LocaleControl.getInstance().GetString("IDS_SEQUENCE");
          viewTextBoxColumn3.Name = "Column_" + str2 + "_Sequence";
          viewTextBoxColumn3.SortMode = DataGridViewColumnSortMode.NotSortable;
          viewTextBoxColumn3.Width = 100;
          viewTextBoxColumn3.ReadOnly = true;
          dataGridView2.Columns.Add((DataGridViewColumn) viewTextBoxColumn3);
          DataGridViewTextBoxColumn viewTextBoxColumn4 = new DataGridViewTextBoxColumn();
          viewTextBoxColumn4.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
          viewTextBoxColumn4.Frozen = true;
          viewTextBoxColumn4.HeaderText = LocaleControl.getInstance().GetString("IDS_STATUS");
          viewTextBoxColumn4.Name = "Column_" + str2 + "_Status";
          viewTextBoxColumn4.SortMode = DataGridViewColumnSortMode.NotSortable;
          viewTextBoxColumn4.Width = 100;
          viewTextBoxColumn4.ReadOnly = true;
          dataGridView2.Columns.Add((DataGridViewColumn) viewTextBoxColumn4);
          DataGridViewDisableButtonColumn disableButtonColumn1 = new DataGridViewDisableButtonColumn();
          disableButtonColumn1.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
          disableButtonColumn1.Frozen = true;
          disableButtonColumn1.HeaderText = "Process";
          disableButtonColumn1.Name = "Column_" + str2 + "_Process";
          disableButtonColumn1.SortMode = DataGridViewColumnSortMode.NotSortable;
          disableButtonColumn1.Width = 60;
          dataGridView2.Columns.Add((DataGridViewColumn) disableButtonColumn1);
          DataGridViewDisableButtonColumn disableButtonColumn2 = new DataGridViewDisableButtonColumn();
          disableButtonColumn2.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
          disableButtonColumn2.Frozen = true;
          disableButtonColumn2.HeaderText = "Cool";
          disableButtonColumn2.Name = "Column_" + str2 + "_Cool";
          disableButtonColumn2.SortMode = DataGridViewColumnSortMode.NotSortable;
          disableButtonColumn2.Width = 60;
          dataGridView2.Columns.Add((DataGridViewColumn) disableButtonColumn2);
          DataGridViewDisableButtonColumn disableButtonColumn3 = new DataGridViewDisableButtonColumn();
          disableButtonColumn3.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
          disableButtonColumn3.Frozen = true;
          disableButtonColumn3.HeaderText = "Runner";
          disableButtonColumn3.Name = "Column_" + str2 + "_Runner";
          disableButtonColumn3.SortMode = DataGridViewColumnSortMode.NotSortable;
          disableButtonColumn3.Width = 60;
          dataGridView2.Columns.Add((DataGridViewColumn) disableButtonColumn3);
          DataGridViewTextBoxColumn viewTextBoxColumn5 = new DataGridViewTextBoxColumn();
          viewTextBoxColumn5.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
          viewTextBoxColumn5.Frozen = true;
          viewTextBoxColumn5.HeaderText = LocaleControl.getInstance().GetString("IDS_ANALYSIS");
          viewTextBoxColumn5.Name = "Column_" + str2 + "_Analysis";
          viewTextBoxColumn5.SortMode = DataGridViewColumnSortMode.NotSortable;
          viewTextBoxColumn5.Width = 100;
          viewTextBoxColumn5.ReadOnly = true;
          dataGridView2.Columns.Add((DataGridViewColumn) viewTextBoxColumn5);
          dataGridView2.Columns[2].Frozen = false;
          dataGridView2.Columns[2].AutoSizeMode = DataGridViewAutoSizeColumnMode.NotSet;
        }
        dataGridView1 = (DataGridView) null;
        str1 = "";
        for (int index = 0; index < 2; ++index)
        {
          DataGridView dataGridView3;
          string str3;
          if (index == 0)
          {
            dataGridView3 = this.dataGridView_AI_Dual;
            str3 = "Dual";
          }
          else
          {
            dataGridView3 = this.dataGridView_AI_3D;
            str3 = "3D";
          }
          DataGridViewCheckBoxColumn viewCheckBoxColumn = new DataGridViewCheckBoxColumn();
          viewCheckBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
          viewCheckBoxColumn.Frozen = true;
          viewCheckBoxColumn.HeaderText = "";
          viewCheckBoxColumn.Name = "Column_AI_" + str3 + "_Check";
          viewCheckBoxColumn.SortMode = DataGridViewColumnSortMode.NotSortable;
          viewCheckBoxColumn.Width = 20;
          dataGridView3.Columns.Add((DataGridViewColumn) viewCheckBoxColumn);
          DataGridViewTextBoxColumn viewTextBoxColumn6 = new DataGridViewTextBoxColumn();
          viewTextBoxColumn6.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
          viewTextBoxColumn6.Frozen = true;
          viewTextBoxColumn6.HeaderText = LocaleControl.getInstance().GetString("IDS_PRODUCT");
          viewTextBoxColumn6.Name = "Column_AI_" + str3 + "_Product";
          viewTextBoxColumn6.SortMode = DataGridViewColumnSortMode.NotSortable;
          viewTextBoxColumn6.Width = 129;
          viewTextBoxColumn6.ReadOnly = true;
          dataGridView3.Columns.Add((DataGridViewColumn) viewTextBoxColumn6);
          DataGridViewTextBoxColumn viewTextBoxColumn7 = new DataGridViewTextBoxColumn();
          viewTextBoxColumn7.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
          viewTextBoxColumn7.Frozen = true;
          viewTextBoxColumn7.HeaderText = LocaleControl.getInstance().GetString("IDS_STUDY");
          viewTextBoxColumn7.Name = "Column_AI_" + str3 + "_Study";
          viewTextBoxColumn7.SortMode = DataGridViewColumnSortMode.NotSortable;
          viewTextBoxColumn7.Width = 611;
          viewTextBoxColumn7.ReadOnly = true;
          dataGridView3.Columns.Add((DataGridViewColumn) viewTextBoxColumn7);
          DataGridViewDisableButtonColumn disableButtonColumn = new DataGridViewDisableButtonColumn();
          disableButtonColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
          disableButtonColumn.Frozen = true;
          disableButtonColumn.HeaderText = "Input";
          disableButtonColumn.Name = "Column_AI_" + str3 + "_Input";
          disableButtonColumn.SortMode = DataGridViewColumnSortMode.NotSortable;
          disableButtonColumn.Width = 200;
          dataGridView3.Columns.Add((DataGridViewColumn) disableButtonColumn);
          dataGridView3.Columns[2].Frozen = false;
          dataGridView3.Columns[2].AutoSizeMode = DataGridViewAutoSizeColumnMode.NotSet;
        }
        this.SetFunctionByLicense();
        clsHDMFLib.SetMoldflowLocale(clsDefine.g_strMoldflowFPath);
        clsData.LoadInjection();
        clsData.LoadProject();
        clsData.LoadBigData();
        clsData.LoadMesh();
        clsData.LoadMeshStat();
        clsData.LoadRotate();
        clsData.LoadGate();
        clsData.LoadValve();
        clsData.LoadMaterial();
        clsData.LoadRunnerDB();
        clsData.LoadMidResult();
        clsData.LoadCase();
        clsData.LoadProcessDB();
        clsData.LoadUDB();
        clsData.LoadReportView();
        clsData.LoadTemplate();
        clsData.LoadSummary();
        clsData.LoadSummaryView();
        clsData.LoadAI();
        clsData.LoadInputDB();
        clsData.LoadExtension();
        this.newComboBox_RotateAxis.Items.Add((object) "-X");
        this.newComboBox_RotateAxis.Items.Add((object) "+X");
        this.newComboBox_RotateAxis.Items.Add((object) "-Y");
        this.newComboBox_RotateAxis.Items.Add((object) "+Y");
        this.newComboBox_RotateAxis.Items.Add((object) "-Z");
        this.newComboBox_RotateAxis.Items.Add((object) "+Z");
        this.newComboBox_RotateAxis.SelectedIndex = 0;
        clsSummaryDefine.g_dicExtension = clsDefine.g_dicExtension;
        clsSummaryDefine.g_diTemplate = clsDefine.g_diTemplate;
        clsSummaryDefine.g_diTmpReport = clsDefine.g_diTmpReport;
        clsReportDefine.g_diCfg = clsDefine.g_diCfg;
        clsReportDefine.g_diTmpReport = clsDefine.g_diTmpReport;
        clsReportDefine.g_diTemplate = clsDefine.g_diTemplate;
        clsReportDefine.g_dicExtension = clsDefine.g_dicExtension;
        clsReportDefine.enumLicLevel = (clsReportDefine.LicLevel) clsDefine.enumLicLevel;
        clsAIDefine.g_diTemplate = clsDefine.g_diTemplate;
        clsAIDefine.g_diTmpAI = clsDefine.g_diTmpAI;
        clsAIDefine.g_fiAICfg = clsDefine.g_fiAICfg;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]frmMain_Load):" + ex.Message));
      }
      this.FormSplash.closeSplash();
      clsUtill.ShowForm((Form) this);
    }

    private void SetFunctionByLicense()
    {
      this.tabControl_Main.TabPages.Remove(this.tabPage_Study);
      this.tabControl_Main.TabPages.Remove(this.tabPage_Report);
      this.tabControl_Main.TabPages.Remove(this.tabPage_Comparison);
      this.tabControl_Main.TabPages.Remove(this.tabPage_PastCases);
      this.tabControl_Main.TabPages.Remove(this.tabPage_AI_Study);
      this.tabControl_Main.TabPages.Remove(this.tabPage_AI_Result);
      this.tabControl_Main.SetDoubleBuffered(true);
      this.ribbon_Main.ActiveTab = this.ribbonTab_Home;
      if (!clsDefine.g_isAILicense)
        this.ribbonTab_AI.Visible = false;
      if (clsDefine.enumLicLevel < clsDefine.LicLevel.Premium)
      {
        this.splitContainer8.Visible = false;
        this.splitContainer6.Panel2Collapsed = true;
        this.ribbonButton_NewProject.Visible = false;
        this.newButton_Dual_Add.Visible = false;
        this.newButton_Dual_Del.Visible = false;
        this.newButton_Dual_Update.Visible = false;
        this.newButton_3D_Add.Visible = false;
        this.newButton_3D_Del.Visible = false;
        this.newButton_3D_Update.Visible = false;
        this.newButton_Analysis.Visible = false;
        this.ribbonTab_Setting.Visible = false;
      }
      if (clsDefine.g_isNetworkLicense)
        return;
      if (clsDefine.g_dicEnabledVersion.Count == 0)
      {
        this.ribbonPanel1.Enabled = false;
        if (!clsDefine.g_isAILicense)
        {
          int num = (int) clsUtill.ShowMessageBox((Form) this, LocaleControl.getInstance().GetString("IDS_License_1"), this.Text, p_msgIcon: MessageBoxIcon.Hand);
        }
      }
      else if (!this.CheckEnabledVersion())
      {
        this.ribbonPanel1.Enabled = false;
        if (!clsDefine.g_isAILicense)
        {
          int num = (int) clsUtill.ShowMessageBox((Form) this, LocaleControl.getInstance().GetString("IDS_NOTMATCH_LICENSEVERSION"), this.Text, p_msgIcon: MessageBoxIcon.Exclamation);
        }
      }
    }

    private void ribbonButton_OpenProject_Click(object sender, EventArgs e)
    {
      List<string> p_lst_strDualMeshData = new List<string>();
      if (!this.CheckEnabledVersion())
      {
        this.ribbonPanel1.Enabled = false;
        int num = (int) clsUtill.ShowMessageBox((Form) this, LocaleControl.getInstance().GetString("IDS_NOTMATCH_LICENSEVERSION"), this.Text, p_msgIcon: MessageBoxIcon.Hand);
      }
      else
      {
        OpenFileDialog openFileDialog = new OpenFileDialog();
        openFileDialog.Filter = "Autodesk Moldflow Insight Projects (*.mpi)|*mpi";
        if (openFileDialog.ShowDialog((IWin32Window) this) != DialogResult.OK)
          return;
        clsDefine.g_dsProduct = new DataSet();
        if (clsDefine.g_diTmpReport.Exists)
          clsDefine.g_diTmpReport.Delete(true);
        clsDefine.g_diTmpReport.Create();
        this.IniteUI();
        this.Hide();
        clsUtill.StartProgress("open Project...", (Form) this);
        try
        {
          clsDefine.g_fiProject = new FileInfo(openFileDialog.FileName);
          bool flag = clsHDMFLib.OpenProject(clsDefine.g_fiProject.FullName);
          clsDefine.g_diProject = new DirectoryInfo(clsDefine.g_fiProject.Directory.FullName);
          clsSummaryDefine.g_fiProject = clsDefine.g_fiProject;
          if (flag)
          {
            TreeNode taskData = clsHDMFLib.GetTaskData(clsDefine.g_fiProject.FullName);
            if (taskData != null)
            {
              for (int index = 0; index < taskData.Nodes.Count; ++index)
              {
                TreeNode node = taskData.Nodes[index];
                clsUtill.UpdateProgress("open Project(" + (object) (index + 1) + "/" + (object) taskData.Nodes.Count + ")...");
                string[] studyFromProduct = clsHDMFLib.GetStudyFromProduct(node);
                if (studyFromProduct.Length != 0)
                {
                  DataTable table = new DataTable();
                  table.TableName = node.Text;
                  table.Columns.Add("Name");
                  table.Columns.Add("FName");
                  table.Columns.Add("Mesh");
                  table.Columns.Add("Check");
                  table.Columns.Add("Sequence");
                  table.Columns.Add("Summary");
                  table.Columns.Add("Status");
                  table.Columns.Add("Analysis");
                  table.Columns.Add("JobID");
                  table.Columns.Add("Compare");
                  table.Columns.Add("Template");
                  table.Columns.Add("AIData");
                  string p_strStudyName1 = ((IEnumerable<string>) studyFromProduct).Where<string>((System.Func<string, bool>) (Temp => !Temp.Contains("_Rev") && !Temp.Contains("_Tetra") && !Temp.Contains("_Past"))).FirstOrDefault<string>();
                  if (p_strStudyName1 != null && p_strStudyName1 != "")
                  {
                    clsHDMFLib.OpenStudy(p_strStudyName1);
                    DataRow dataRow = table.Rows.Add();
                    dataRow["Name"] = (object) p_strStudyName1;
                    dataRow["FName"] = (object) clsHDMFLib.GetStudyFileName().Replace(".sdy", "");
                    dataRow["Mesh"] = (object) "Dual";
                    dataRow["Sequence"] = (object) clsHDMFLib.GetStudySequence();
                    dataRow["Summary"] = (object) DBNull.Value;
                    dataRow["Status"] = (object) DBNull.Value;
                    dataRow["Analysis"] = (object) DBNull.Value;
                    dataRow["JobID"] = (object) DBNull.Value;
                    dataRow["Compare"] = (object) false;
                    dataRow["Template"] = (object) string.Empty;
                    dataRow["AIData"] = (object) DBNull.Value;
                    if (((IEnumerable<string>) studyFromProduct).Any<string>((System.Func<string, bool>) (Temp => Temp.Contains("_Dual_Rev"))))
                      dataRow["Check"] = (object) true;
                    else if (!clsHDMFLib.ExistDualMesh())
                    {
                      dataRow["Check"] = (object) false;
                    }
                    else
                    {
                      clsUtill.UpdateProgress("open Project(" + (object) (index + 1) + "/" + (object) taskData.Nodes.Count + ") => verify Dual Mesh...");
                      double p_dblAspectRatio = clsUtill.ConvertToDouble(clsDefine.g_dicMeshStat["Value"]);
                      p_lst_strDualMeshData.AddRange((IEnumerable<string>) clsHDMFLib.GetDualDomainMeshData());
                      dataRow["Check"] = (object) clsHDMFLib.VerificationDualMesh(p_lst_strDualMeshData, p_dblAspectRatio);
                    }
                    clsHDMFLib.CloseStudy();
                  }
                  string p_strStudyName2 = ((IEnumerable<string>) studyFromProduct).Where<string>((System.Func<string, bool>) (Temp => !Temp.Contains("_Rev") && Temp.Contains("_Tetra") && !Temp.Contains("_Past"))).FirstOrDefault<string>();
                  if (p_strStudyName2 != null && p_strStudyName2 != "")
                  {
                    clsUtill.UpdateProgress("open Project(" + (object) (index + 1) + "/" + (object) taskData.Nodes.Count + ") => check 3D Mesh...");
                    clsHDMFLib.OpenStudy(p_strStudyName2);
                    DataRow dataRow = table.Rows.Add();
                    dataRow["Name"] = (object) p_strStudyName2;
                    dataRow["FName"] = (object) clsHDMFLib.GetStudyFileName().Replace(".sdy", "");
                    dataRow["Mesh"] = (object) "3D";
                    dataRow["Sequence"] = (object) clsHDMFLib.GetStudySequence();
                    dataRow["Summary"] = (object) DBNull.Value;
                    dataRow["Status"] = (object) DBNull.Value;
                    dataRow["Analysis"] = (object) DBNull.Value;
                    dataRow["JobID"] = (object) DBNull.Value;
                    dataRow["Compare"] = (object) false;
                    dataRow["Template"] = (object) string.Empty;
                    dataRow["AIData"] = (object) DBNull.Value;
                    string[] source = studyFromProduct;
                    dataRow["Check"] = !((IEnumerable<string>) source).Any<string>((System.Func<string, bool>) (Temp => Temp.Contains("_3D_Rev"))) ? (object) clsHDMFLib.Exist3DMesh() : (object) true;
                    clsHDMFLib.CloseStudy();
                  }
                  clsUtill.UpdateProgress("open Project(" + (object) (index + 1) + "/" + (object) taskData.Nodes.Count + ") => check Rev Study...");
                  foreach (string str1 in studyFromProduct)
                  {
                    if (str1.Contains("_Rev") || str1.Contains("_Past"))
                    {
                      string str2 = !str1.Contains("_Dual_Rev") ? "3D" : "Dual";
                      if (str1.Contains("_Past") && !str1.Contains("_Rev"))
                      {
                        clsHDMFLib.OpenStudy(str1);
                        str2 = clsHDMFLib.GetMeshType();
                        clsHDMFLib.CloseStudy();
                      }
                      DataRow p_drStudy = table.Rows.Add();
                      p_drStudy["Name"] = (object) str1;
                      p_drStudy["FName"] = (object) clsHDMFLib.GetStudyFileNameFromProject(clsDefine.g_fiProject.FullName, str1);
                      p_drStudy["Mesh"] = (object) str2;
                      p_drStudy["Check"] = (object) DBNull.Value;
                      p_drStudy["Sequence"] = (object) string.Empty;
                      p_drStudy["Summary"] = (object) DBNull.Value;
                      p_drStudy["Status"] = (object) DBNull.Value;
                      p_drStudy["Analysis"] = (object) DBNull.Value;
                      p_drStudy["JobID"] = (object) DBNull.Value;
                      p_drStudy["Compare"] = (object) false;
                      p_drStudy["Template"] = (object) string.Empty;
                      p_drStudy["AIData"] = (object) DBNull.Value;
                      bool p_isExistOutFile = clsHDMFLib.ExistOutFile(clsDefine.g_fiProject.DirectoryName, p_drStudy["FName"].ToString());
                      if (!p_isExistOutFile)
                      {
                        clsHDMFLib.OpenStudy(str1);
                        if (!clsHDMFLib.ExistAnalysis())
                        {
                          p_drStudy["Status"] = clsHDMFLib.ExistFolderFromFolderManager("Runner System") ? (object) clsDefine.Status.READY : (object) clsDefine.Status.NOT_READY;
                        }
                        else
                        {
                          p_drStudy["Status"] = (object) clsDefine.Status.COMPLETED;
                          p_drStudy["Summary"] = (object) JsonConvert.SerializeObject((object) clsSummary.GetStudySummaryData(p_drStudy, clsDefine.g_fiProject, p_isExistOutFile));
                        }
                        p_drStudy["Sequence"] = (object) clsHDMFLib.GetStudySequence();
                        clsHDMFLib.SaveStudy();
                        clsHDMFLib.CloseStudy();
                      }
                      else
                      {
                        p_drStudy["Status"] = (object) clsDefine.Status.COMPLETED;
                        p_drStudy["Summary"] = (object) JsonConvert.SerializeObject((object) clsSummary.GetStudySummaryData(p_drStudy, clsDefine.g_fiProject, p_isExistOutFile));
                      }
                    }
                  }
                  clsDefine.g_dsProduct.Tables.Add(table);
                }
              }
              if (clsDefine.enumLicLevel > clsDefine.LicLevel.Standard)
                clsSummary.ExportSummary(clsData.GetStudyDataRow(true), clsDefine.g_strLanguageType);
            }
          }
        }
        catch (Exception ex)
        {
          HDLog.Instance().Error((object) ("Exception([frmMain]ribbonButton_OpenProject_Click):" + ex.Message));
        }
        clsUtill.EndProgress();
        clsUtill.ShowForm((Form) this);
        this.RefreshTreeView();
      }
    }

    private void ribbonButton_CreateBigData_Click(object sender, EventArgs e)
    {
      string str = string.Empty;
      frmBigData frmBigData = new frmBigData();
      if (frmBigData.ShowDialog() != DialogResult.OK)
        return;
      this.Hide();
      clsUtill.StartProgress("Create AI Big Data", (Form) this);
      string strBigData = frmBigData.m_strBigData;
      List<FileInfo> lstFiProject = frmBigData.m_lst_fiProject;
      Dictionary<string, string> dictionary = new Dictionary<string, string>();
      foreach (FileInfo fileInfo in lstFiProject)
      {
        Dictionary<string, string> fileNameFromProject = clsHDMFLib.GetAllStudyFileNameFromProject(fileInfo.FullName);
        clsHDMFLib.OpenProject(fileInfo.FullName);
        str = clsBigData.CreateAIBigData(strBigData + "\\" + Path.GetFileNameWithoutExtension(fileInfo.FullName), fileInfo.DirectoryName, fileNameFromProject);
      }
      clsUtill.EndProgress();
      clsUtill.ShowForm((Form) this);
      if (!string.IsNullOrEmpty(str))
      {
        int num = (int) clsUtill.ShowMessageBox((Form) this, "Big Data creation Error: " + str, "Error", p_msgIcon: MessageBoxIcon.Hand);
      }
    }

    private void SetLocale()
    {
      this.ribbonTab_Home.Text = LocaleControl.getInstance().GetString("IDS_HOME");
      this.ribbonTab_Setting.Text = LocaleControl.getInstance().GetString("IDS_SETTING");
      this.ribbonButton_NewProject.Text = LocaleControl.getInstance().GetString("IDS_CREATE_PROJECT");
      this.ribbonButton_OpenProject.Text = LocaleControl.getInstance().GetString("IDS_OPEN_PROJECT");
      this.ribbonButton_CreateBigData.Text = "BigData " + LocaleControl.getInstance().GetString("IDS_CREATE");
      this.ribbonButton_Info.Text = "HDMFlow " + LocaleControl.getInstance().GetString("IDS_INFO");
      this.ribbonButton_Close.Text = LocaleControl.getInstance().GetString("IDS_EXIT");
      this.ribbonButton_Set_Mesh.Text = LocaleControl.getInstance().GetString("IDS_CREATE_MESH");
      this.ribbonButton_Set_Runner.Text = LocaleControl.getInstance().GetString("IDS_RUNNER") + " DB";
      this.ribbonButton_Set_Injection.Text = LocaleControl.getInstance().GetString("IDS_SET_INJ");
      this.ribbonButton_Set_MidResult.Text = LocaleControl.getInstance().GetString("IDS_MID_RESULT");
      this.ribbonButton_Set_ProcDB.Text = LocaleControl.getInstance().GetString("IDS_PROCESS") + " DB";
      this.ribbonButton_Set_Lang.Text = LocaleControl.getInstance().GetString("IDS_LANG");
      this.ribbonButton_Set_Gate.Text = LocaleControl.getInstance().GetString("IDS_GATE");
      this.ribbonButton_Set_MeshStat.Text = LocaleControl.getInstance().GetString("IDS_MESH_STATISTICS");
      this.ribbonButton_Set_Rotate.Text = LocaleControl.getInstance().GetString("IDS_AUTOMATIC_ROTATE");
      this.ribbonButton_Set_Valve.Text = LocaleControl.getInstance().GetString("IDS_SET_VALVE");
      this.label_STD_Study.Text = "[" + LocaleControl.getInstance().GetString("IDS_STD_STUDY") + "]";
      this.newButton_Mesh.ButtonText = LocaleControl.getInstance().GetString("IDS_UPDATE_MESH");
      this.label_Rotate_Model.Text = "[" + LocaleControl.getInstance().GetString("IDS_ROTATE_MODEL") + "]";
      this.newButton_Rotate.ButtonText = LocaleControl.getInstance().GetString("IDS_ROTATE");
      this.label_Monitoring_Analysis.Text = "[" + LocaleControl.getInstance().GetString("IDS_MONITORING") + "(" + LocaleControl.getInstance().GetString("IDS_ANALYSIS") + ")]";
      this.dataGridView_Monitor.Columns[0].HeaderText = LocaleControl.getInstance().GetString("IDS_PRODUCT");
      this.dataGridView_Monitor.Columns[1].HeaderText = LocaleControl.getInstance().GetString("IDS_STUDY");
      this.dataGridView_Monitor.Columns[2].HeaderText = LocaleControl.getInstance().GetString("IDS_MESH");
      this.dataGridView_Monitor.Columns[3].HeaderText = LocaleControl.getInstance().GetString("IDS_SEQUENCE");
      this.dataGridView_Monitor.Columns[4].HeaderText = LocaleControl.getInstance().GetString("IDS_STATUS");
      this.tabPage_Study.Text = LocaleControl.getInstance().GetString("IDS_STUDY");
      this.tabPage_Report.Text = LocaleControl.getInstance().GetString("IDS_REPORT");
      this.tabPage_PastCases.Text = LocaleControl.getInstance().GetString("IDS_PASTCASES");
      this.tabPage_Comparison.Text = LocaleControl.getInstance().GetString("IDS_COMPARISONREPORT");
      this.newButton_Analysis.ButtonText = LocaleControl.getInstance().GetString("IDS_EXECUTE_ANALYSIS");
      this.newButton_Report.ButtonText = LocaleControl.getInstance().GetString("IDS_EXPORT_REPORT");
      this.newButton_Past_Report.ButtonText = LocaleControl.getInstance().GetString("IDS_EXPORT_REPORT");
      this.newButton_Comparison.ButtonText = LocaleControl.getInstance().GetString("IDS_EXPORT_REPORT");
      this.radioButton_Case2.Text = LocaleControl.getInstance().GetString("IDS_CASE2");
      this.radioButton_Case3.Text = LocaleControl.getInstance().GetString("IDS_CASE3");
      this.tabPage_AI_Study.Text = LocaleControl.getInstance().GetString("IDS_STUDY");
      this.tabPage_AI_Result.Text = "AI" + LocaleControl.getInstance().GetString("IDS_RESULT");
      this.newButton_AIResult.ButtonText = LocaleControl.getInstance().GetString("IDS_RESULT") + " " + LocaleControl.getInstance().GetString("IDS_APPLY");
      if (this.dataGridView_Dual.Columns.Count > 7)
      {
        this.dataGridView_Dual.Columns[1].HeaderText = LocaleControl.getInstance().GetString("IDS_PRODUCT");
        this.dataGridView_Dual.Columns[2].HeaderText = LocaleControl.getInstance().GetString("IDS_STUDY");
        this.dataGridView_Dual.Columns[3].HeaderText = LocaleControl.getInstance().GetString("IDS_SEQUENCE");
        this.dataGridView_Dual.Columns[4].HeaderText = LocaleControl.getInstance().GetString("IDS_STATUS");
        this.dataGridView_Dual.Columns[8].HeaderText = LocaleControl.getInstance().GetString("IDS_ANALYSIS");
      }
      if (this.dataGridView_3D.Columns.Count <= 7)
        return;
      this.dataGridView_3D.Columns[1].HeaderText = LocaleControl.getInstance().GetString("IDS_PRODUCT");
      this.dataGridView_3D.Columns[2].HeaderText = LocaleControl.getInstance().GetString("IDS_STUDY");
      this.dataGridView_3D.Columns[3].HeaderText = LocaleControl.getInstance().GetString("IDS_SEQUENCE");
      this.dataGridView_3D.Columns[4].HeaderText = LocaleControl.getInstance().GetString("IDS_STATUS");
      this.dataGridView_3D.Columns[8].HeaderText = LocaleControl.getInstance().GetString("IDS_ANALYSIS");
    }

    private bool CheckEnabledVersion()
    {
      bool flag = true;
      try
      {
        if (clsDefine.g_isNetworkLicense)
          return flag;
        clsHDMFLib.GetMoldflowVersion();
        string key = clsHDMFLibDefine.m_strVersion.Split('.', ' ')[0];
        if (!clsDefine.g_dicEnabledVersion.ContainsKey(key) && !clsDefine.g_dicEnabledVersion.ContainsKey("Demo"))
          return false;
        switch (key)
        {
          case "2021":
            clsDefine.g_strMaterial = "MaterialData.xml";
            clsDefine.g_strUdbFolder = "My AMC 2021.1 Projects";
            clsDefine.g_strScmPort = "43100";
            break;
          case "2023":
            clsDefine.g_strMaterial = "MaterialData_v2023.xml";
            clsDefine.g_strUdbFolder = "My AMC 2023 Projects";
            clsDefine.g_strScmPort = "44100";
            break;
          case "2024":
            clsDefine.g_strMaterial = "MaterialData_v2023.xml";
            clsDefine.g_strUdbFolder = "My AMC 2024 Projects";
            clsDefine.g_strScmPort = "44100";
            break;
        }
        clsHDMFLibDefine.m_strUserUDBFolder = (object) clsDefine.g_strUdbFolder;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]CheckEnabledVersion):" + ex.Message));
      }
      return flag;
    }

    private void RefreshTreeView(TreeNode tnSelectNode = null)
    {
      this.treeView_Product.Nodes.Clear();
      try
      {
        TreeNode taskData = clsHDMFLib.GetTaskData(clsDefine.g_fiProject.FullName);
        if (taskData == null)
          return;
        taskData.ImageIndex = 0;
        taskData.SelectedImageIndex = 0;
        foreach (TreeNode node in taskData.Nodes)
        {
          node.ImageIndex = 1;
          node.SelectedImageIndex = 1;
          node.Nodes.Clear();
          if (node.Text.ToLower().Contains("past"))
            node.Nodes.Remove(node);
        }
        this.treeView_Product.Nodes.Add(taskData);
        if (tnSelectNode != null)
          this.treeView_Product.SelectedNode = tnSelectNode;
        this.treeView_Product.ExpandAll();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]RefreshTreeView):" + ex.Message));
      }
    }

    private void ribbonButton_Info_Click(object sender, EventArgs e)
    {
      FileInfo fileInfo = new FileInfo(Application.StartupPath + "\\Version.ini");
      if (!fileInfo.Exists)
        return;
      try
      {
        string str = "v" + clsUtill.ReadINI("Version", "Major", fileInfo.FullName) + "." + clsUtill.ReadINI("Version", "Minor", fileInfo.FullName) + "." + clsUtill.ReadINI("Version", "Release", fileInfo.FullName) + "." + clsUtill.ReadINI("Version", "Build", fileInfo.FullName);
        frmInfo frmInfo = new frmInfo();
        frmInfo.m_strVersion = str;
        frmInfo.Icon = this.Icon;
        int num = (int) frmInfo.ShowDialog((IWin32Window) this);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]ribbonButton_Info_Click):" + ex.Message));
      }
    }

    private void ribbonButton_Close_Click(object sender, EventArgs e) => this.Close();

    private void ribbonButton_Set_Click(object sender, EventArgs e)
    {
      string name = (sender as RibbonButton).Name;
      // ISSUE: reference to a compiler-generated method
      switch (\u003CPrivateImplementationDetails\u003E.ComputeStringHash(name))
      {
        case 330570624:
          if (!(name == "ribbonButton_Set_Rotate"))
            break;
          int num1 = (int) new frmRotate().ShowDialog((IWin32Window) this);
          break;
        case 483653806:
          if (!(name == "ribbonButton_Set_MidResult"))
            break;
          int num2 = (int) new frmMidResult().ShowDialog((IWin32Window) this);
          break;
        case 958540216:
          if (!(name == "ribbonButton_Set_Injection"))
            break;
          int num3 = (int) new frmInjection().ShowDialog((IWin32Window) this);
          break;
        case 1282825328:
          if (!(name == "ribbonButton_Set_SummaryView") || new frmSummaryView().ShowDialog((IWin32Window) this) != DialogResult.OK || this.tabControl_Main.SelectedTab != this.tabPage_Report)
            break;
          this.RefreshReportTab();
          break;
        case 1557363844:
          if (!(name == "ribbonButton_Set_Gate"))
            break;
          int num4 = (int) new frmGate().ShowDialog((IWin32Window) this);
          break;
        case 2278936377:
          if (!(name == "ribbonButton_Set_Runner"))
            break;
          int num5 = (int) new frmRunnerDB().ShowDialog((IWin32Window) this);
          break;
        case 2316291212:
          if (!(name == "ribbonButton_Set_MeshStat"))
            break;
          int num6 = (int) new frmMeshStat().ShowDialog((IWin32Window) this);
          break;
        case 2334696535:
          if (!(name == "ribbonButton_Set_InputDB"))
            break;
          int num7 = (int) new frmInputDB().ShowDialog((IWin32Window) this);
          break;
        case 2800494619:
          if (!(name == "ribbonButton_Set_Valve"))
            break;
          int num8 = (int) new frmValve().ShowDialog((IWin32Window) this);
          break;
        case **********:
          if (!(name == "ribbonButton_Set_ProcDB"))
            break;
          if (clsDefine.enumLicLevel == clsDefine.LicLevel.Light)
          {
            int num9 = (int) clsUtill.ShowMessageBox((Form) this, LocaleControl.getInstance().GetString("IDS_CANT_NOT_BE_USED") + " -> " + LocaleControl.getInstance().GetString("IDS_CHECK_LICENSE"), "HDMoldFlow", p_msgIcon: MessageBoxIcon.Exclamation);
            break;
          }
          int num10 = (int) new frmProcessDB().ShowDialog((IWin32Window) this);
          break;
        case **********:
          if (!(name == "ribbonButton_Set_Lang") || new frmLanguage().ShowDialog((IWin32Window) this) != DialogResult.OK)
            break;
          clsData.ChangeLocale();
          this.SetLocale();
          if (this.tabControl_Main.SelectedTab == this.tabPage_Study)
            this.RefreshStudyTab();
          else if (this.tabControl_Main.SelectedTab == this.tabPage_Report)
            this.RefreshReportTab();
          else if (this.tabControl_Main.SelectedTab == this.tabPage_Comparison)
            this.RefreshComparisonTab();
          else if (this.tabControl_Main.SelectedTab == this.tabPage_PastCases)
            this.RefreshPastCasesTab();
          else if (this.tabControl_Main.SelectedTab == this.tabPage_AI_Study)
            this.RefreshAIStudyTab();
          else if (this.tabControl_Main.SelectedTab == this.tabPage_AI_Result)
            this.RefreshAIResultTab();
          break;
        case 3736241430:
          if (!(name == "ribbonButton_Set_Mesh"))
            break;
          int num11 = (int) new frmMesh().ShowDialog((IWin32Window) this);
          break;
      }
    }

    private void ribbonButton_NewProject_Click(object sender, EventArgs e)
    {
      string strItem = "";
      double[] p_arr_dblCenterMove = new double[3];
      List<TreeNode> treeNodeList = new List<TreeNode>();
      List<string> p_lst_strDualMeshData = new List<string>();
      if (!this.CheckEnabledVersion())
      {
        clsLicense.CheckLicense();
        this.ribbonPanel1.Enabled = false;
        this.SetFunctionByLicense();
        int num = (int) clsUtill.ShowMessageBox((Form) this, LocaleControl.getInstance().GetString("IDS_NOTMATCH_LICENSEVERSION"), this.Text, p_msgIcon: MessageBoxIcon.Hand);
      }
      else
      {
        frmProject frmProject = new frmProject();
        if (frmProject.ShowDialog((IWin32Window) this) != DialogResult.OK)
          return;
        this.IniteUI();
        this.Hide();
        try
        {
          string str1 = frmProject.m_strProject;
          clsDefine.g_isMoveZero = frmProject.m_isMoveZero;
          clsDefine.g_isCreate3D = frmProject.m_isCreate3D;
          if (str1 == "")
          {
            FileInfo fileInfo = new FileInfo(frmProject.m_lst_strCAD[0]);
            string[] strArray = Path.GetFileNameWithoutExtension(fileInfo.Name).Split('_');
            if (strArray.Length < 2)
              str1 = fileInfo.Directory.Name;
            else
              str1 = strArray[0] + "_" + strArray[1] + "_" + strArray[2];
          }
          int num = 1;
          while (true)
          {
            if (Directory.Exists(clsDefine.g_diBasicProject.ToString() + "\\" + str1 + "_Rev" + (object) num + "_Project"))
              ++num;
            else
              break;
          }
          string str2 = str1 + "_Rev" + (object) num;
          clsDefine.g_diProject = clsHDMFLib.CreateProject(clsDefine.g_diBasicProject.ToString() + "\\" + str2);
          clsDefine.g_fiProject = new FileInfo(clsDefine.g_diProject.FullName + "\\" + clsDefine.g_diProject.Name + ".mpi");
          clsDefine.g_dsProduct = new DataSet();
          if (clsDefine.g_diProject != null)
          {
            clsUtill.StartProgress("create Project => import CAD...", (Form) this);
            List<DataRow> source = new List<DataRow>();
            for (int index = 0; index < frmProject.m_lst_strCAD.Count; ++index)
            {
              string str3 = frmProject.m_lst_strCAD[index];
              string[] strArray = Path.GetFileNameWithoutExtension(str3).Split('_');
              if (strArray.Length >= 2)
              {
                string str4 = strArray[1];
                strItem = strArray[1];
                if (strArray.Length > 2)
                  str4 = str4 + "_" + strArray[2];
                clsHDMFLib.CreateFolder("", str4);
                clsDefine.g_dsProduct.Tables.Add(new DataTable(str4));
                DataTable table = clsDefine.g_dsProduct.Tables[str4];
                table.Columns.Add("Name");
                table.Columns.Add("FName");
                table.Columns.Add("Mesh");
                table.Columns.Add("Check");
                table.Columns.Add("Sequence");
                table.Columns.Add("Summary");
                table.Columns.Add("Status");
                table.Columns.Add("JobID");
                table.Columns.Add("Analysis");
                table.Columns.Add("Compare");
                table.Columns.Add("Template");
                table.Columns.Add("AIData");
                clsUtill.UpdateProgress("create Project => import CAD(" + (object) (index + 1) + "/" + (object) frmProject.m_lst_strCAD.Count + ")...");
                string str5 = clsHDMFLib.ImportCAD(str3);
                DataRow dataRow = table.Rows.Add();
                dataRow["Name"] = (object) str5;
                dataRow["FName"] = (object) clsHDMFLib.GetStudyFileName().Replace(".sdy", "");
                dataRow["Mesh"] = (object) "Dual";
                dataRow["Check"] = (object) false;
                dataRow["Sequence"] = (object) clsHDMFLib.GetStudySequence();
                dataRow["Summary"] = (object) DBNull.Value;
                dataRow["Status"] = (object) DBNull.Value;
                dataRow["JobID"] = (object) DBNull.Value;
                dataRow["Analysis"] = (object) DBNull.Value;
                dataRow["Compare"] = (object) false;
                dataRow["Template"] = (object) string.Empty;
                dataRow["AIData"] = (object) DBNull.Value;
                DataRow p_drProcessDB = (DataRow) null;
                if (clsDefine.g_dtProcessDB.Rows.Count > 0)
                {
                  string strName = dataRow["Name"].ToString().Split('_')[0];
                  strItem = dataRow["Name"].ToString().Split('_')[1];
                  DataRow[] array = clsDefine.g_dtProcessDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp =>
                  {
                    if (!(Temp["Name"].ToString().Split('_')[0] == strName))
                    {
                      if (!(Temp["Name"].ToString().Split('_')[0].ToLower() == "basic"))
                        return false;
                    }
                    return Temp["Name"].ToString().Split('_')[1] == strItem;
                  })).ToArray<DataRow>();
                  if (array.Length != 0)
                    p_drProcessDB = ((IEnumerable<DataRow>) array).Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString().Contains("_Default"))).FirstOrDefault<DataRow>() ?? array[0];
                }
                if (p_drProcessDB != null)
                {
                  this.ApplyProcessDBToStudy(p_drProcessDB);
                  dataRow["Sequence"] = (object) clsHDMFLib.GetAnalysisSequence();
                }
                clsHDMFLib.SaveStudy();
                clsHDMFLib.CloseStudy();
                source.Add(dataRow);
              }
            }
            clsUtill.EndProgress();
            clsMesh.ShowUpdateMesh(source.ToArray(), (Form) this, "Dual");
            clsMesh.UpdateDualMesh(source.ToArray());
            clsMesh.CloseUpdateMesh();
            clsUtill.StartProgress("create Project => Check Dual Mesh...", (Form) this);
            List<string[]> strArrayList = new List<string[]>();
            List<string> stringList1 = new List<string>();
            for (int index1 = 0; index1 < source.Count; ++index1)
            {
              p_lst_strDualMeshData.Clear();
              clsUtill.UpdateProgress("Check Dual Mesh(" + (object) (index1 + 1) + "/" + (object) source.Count + ")...");
              DataRow dataRow1 = source[index1];
              clsHDMFLib.OpenStudy(dataRow1["Name"].ToString());
              stringList1.Add(clsHDMFLib.GetGlobalEdgeLengthFromLog().ToString());
              p_lst_strDualMeshData.AddRange((IEnumerable<string>) clsHDMFLib.GetDualDomainMeshData());
              double p_dblAspectRatio = clsUtill.ConvertToDouble(clsDefine.g_dicMeshStat["Value"]);
              dataRow1["Check"] = (object) clsHDMFLib.VerificationDualMesh(p_lst_strDualMeshData, p_dblAspectRatio);
              clsHDMFLib.CommitPartSufaceProperty();
              clsHDMFLib.ExecutePurgeNodes();
              if (clsDefine.g_dtRotate.Rows.Count > 0)
              {
                strItem = dataRow1["Name"].ToString().Split('_')[1];
                DataRow dataRow2 = clsDefine.g_dtRotate.Rows.Cast<DataRow>().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Section"].ToString() == strItem)).FirstOrDefault<DataRow>();
                DataColumn[] array = clsDefine.g_dtRotate.Columns.Cast<DataColumn>().Where<DataColumn>((System.Func<DataColumn, bool>) (Temp => Temp.ColumnName.Contains("Value"))).ToArray<DataColumn>();
                if (dataRow2 != null)
                {
                  clsUtill.UpdateProgress("create Project => Automatic Rotate(" + (object) (index1 + 1) + "/" + (object) frmProject.m_lst_strCAD.Count + ")...");
                  for (int index2 = 0; index2 < array.Length; ++index2)
                  {
                    if (dataRow2["Value" + (object) (index2 + 1)] != null && dataRow2["Value" + (object) (index2 + 1)].ToString() != string.Empty)
                      clsHDMFLib.RotateModel(clsUtill.ConvertToInt(dataRow2["Direction" + (object) (index2 + 1)].ToString()), (double) clsUtill.ConvertToInt(dataRow2["Value" + (object) (index2 + 1)].ToString()));
                  }
                }
              }
              if (clsDefine.g_isMoveZero)
                p_arr_dblCenterMove = clsHDMFLib.MoveCenterPointToZero();
              double[] modelCenterPoint = clsHDMFLib.GetModelCenterPoint();
              double[] modelLengths = clsHDMFLib.GetModelLengths();
              clsHDMFLib.WriteCenterPointToStudyNote(modelCenterPoint);
              clsHDMFLib.WriteCenterMoveToStudyNote(p_arr_dblCenterMove);
              clsHDMFLib.WriteModelLengthsToStudyNote(modelLengths);
              clsHDMFLib.SaveStudy();
              clsHDMFLib.CloseStudy();
              strArrayList.Add(p_lst_strDualMeshData.ToArray());
            }
            clsUtill.EndProgress();
            if (source.Any<DataRow>((System.Func<DataRow, bool>) (Temp => Convert.ToBoolean(Temp["Check"]))) && clsDefine.g_isCreate3D)
            {
              clsUtill.StartProgress("check Dual Study...", (Form) this);
              List<DataRow> dataRowList = new List<DataRow>();
              List<string> stringList2 = new List<string>();
              for (int index = 0; index < source.Count; ++index)
              {
                clsUtill.UpdateProgress("check and Copy Study(" + (object) (index + 1) + "/" + (object) source.Count + ")...");
                DataRow dataRow3 = source[index];
                if (Convert.ToBoolean(dataRow3["Check"]))
                {
                  if (clsUtill.ConvertToDouble(((IEnumerable<string>) strArrayList[index]).Last<string>()) >= 2.0)
                  {
                    clsUtill.HideProgress();
                    DialogResult dialogResult = clsUtill.ShowMessageBox((Form) this, LocaleControl.getInstance().GetString("IDS_CHECK_CONNECTIVITY_REGIONS"), "create 3D Mesh", MessageBoxButtons.YesNo, MessageBoxIcon.Asterisk);
                    clsUtill.ShowProgress();
                    if (dialogResult != DialogResult.Yes)
                      continue;
                  }
                  if (clsHDMFLib.CopyStudy(dataRow3["Name"].ToString(), dataRow3["Name"].ToString() + "_Tetra", "", true, false))
                  {
                    DataRow dataRow4 = dataRow3.Table.Rows.Add();
                    dataRow4["Name"] = (object) (dataRow3["Name"].ToString() + "_Tetra");
                    dataRow4["FName"] = (object) clsHDMFLib.GetStudyFileName().Replace(".sdy", "");
                    dataRow4["Mesh"] = (object) "3D";
                    dataRow4["Check"] = (object) false;
                    dataRow4["Sequence"] = dataRow3["Sequence"];
                    dataRow4["Summary"] = (object) DBNull.Value;
                    dataRow4["Status"] = (object) DBNull.Value;
                    dataRow4["JobID"] = (object) DBNull.Value;
                    dataRow4["Analysis"] = (object) DBNull.Value;
                    dataRow4["Compare"] = (object) DBNull.Value;
                    dataRow4["Template"] = (object) string.Empty;
                    dataRowList.Add(dataRow4);
                    stringList2.Add(stringList1[index]);
                  }
                }
              }
              clsUtill.EndProgress();
              clsMesh.ShowUpdateMesh(dataRowList.ToArray(), (Form) this, "3D");
              clsMesh.Update3DMesh(dataRowList.ToArray(), stringList2.ToArray());
              clsMesh.CloseUpdateMesh();
              clsUtill.StartProgress("check 3D Mesh and save Study...", (Form) this);
              for (int index = 0; index < dataRowList.Count; ++index)
              {
                clsUtill.UpdateProgress("apply Process DB and save Study(" + (object) (index + 1) + "/" + (object) dataRowList.Count + ")...");
                DataRow row = dataRowList[index];
                if (!Convert.ToBoolean(row["Check"]))
                {
                  clsHDMFLib.DeleteStudy(row["Name"].ToString());
                  row.Table.Rows.Remove(row);
                }
                clsHDMFLib.SaveStudy();
                clsHDMFLib.CloseStudy();
              }
              clsUtill.EndProgress();
            }
            this.RefreshTreeView();
          }
        }
        catch (Exception ex)
        {
          HDLog.Instance().Error((object) ("Exception([frmMain]ribbonButton_NewProject_Click):" + ex.Message));
        }
        clsUtill.ShowForm((Form) this);
      }
    }

    private void pictureBox_HDSolutions_Click(object sender, EventArgs e) => Process.Start("https://hd-solutions.co.kr/");

    private void dataGridView_Monitor_SelectionChanged(object sender, EventArgs e)
    {
      if (this.dataGridView_Monitor.CurrentCell == null)
        return;
      this.dataGridView_Monitor.CurrentCell = (DataGridViewCell) null;
      this.dataGridView_Monitor.ClearSelection();
    }

    private void IniteUI()
    {
      this.treeView_Product.Nodes.Clear();
      this.dataGridView_Dual.Rows.Clear();
      this.dataGridView_3D.Rows.Clear();
      this.dataGridView_Monitor.Rows.Clear();
      this.panel_Report.Controls.Clear();
      this.dataGridView_List.Rows.Clear();
      this.dataGridView_List.ColumnHeadersVisible = false;
      this.tableLayoutPanel_CMPR.Controls.Clear();
    }

    private void treeView_Product_AfterSelect(object sender, TreeViewEventArgs e) => this.RefreshTab();

    private void RefreshTab()
    {
      if (this.tabControl_Main.SelectedTab == this.tabPage_Study)
        this.RefreshStudyTab();
      else if (this.tabControl_Main.SelectedTab == this.tabPage_Report)
        this.RefreshReportTab();
      else if (this.tabControl_Main.SelectedTab == this.tabPage_Comparison)
        this.RefreshComparisonTab();
      else if (this.tabControl_Main.SelectedTab == this.tabPage_AI_Study)
      {
        this.RefreshAIStudyTab();
      }
      else
      {
        if (this.tabControl_Main.SelectedTab != this.tabPage_AI_Result)
          return;
        this.RefreshAIResultTab();
      }
    }

    public void RefreshStudyTab()
    {
      List<DataRow> dataRowList = new List<DataRow>();
      this.dataGridView_Dual.Rows.Clear();
      this.dataGridView_3D.Rows.Clear();
      this.panel_Mesh.Enabled = false;
      this.newButton_Mesh.Enabled = false;
      this.label_Dual.Text = "-";
      this.label_3D.Text = "-";
      this.label_Dual_MeshQuality.Text = string.Empty;
      TreeNode tnSelect = this.treeView_Product.SelectedNode;
      if (tnSelect == null)
        return;
      try
      {
        if (tnSelect.Level == 0)
        {
          foreach (DataTable table in (InternalDataCollectionBase) clsDefine.g_dsProduct.Tables)
          {
            foreach (DataRow dataRow in table.AsEnumerable())
            {
              if (dataRow["Check"] == DBNull.Value && !dataRow["Name"].ToString().Contains("Past"))
                dataRowList.Add(dataRow);
            }
          }
        }
        else
        {
          DataTable source = clsDefine.g_dsProduct.Tables.Cast<DataTable>().Where<DataTable>((System.Func<DataTable, bool>) (Temp => Temp.TableName == tnSelect.Text)).FirstOrDefault<DataTable>();
          if (source != null)
          {
            this.panel_Mesh.Enabled = true;
            DataRow dataRow = source.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Check"] != DBNull.Value && Temp["Mesh"].ToString() == "Dual")).FirstOrDefault<DataRow>();
            DataRow row = source.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Check"] != DBNull.Value && Temp["Mesh"].ToString() == "3D")).FirstOrDefault<DataRow>();
            if (dataRow != null && row == null)
            {
              this.panel_Mesh.Enabled = true;
              if (!Convert.ToBoolean(dataRow["Check"]))
              {
                this.label_Dual.Text = "Problem";
                this.label_Dual_MeshQuality.Text = "Mesh Quality Problem";
              }
              else
              {
                this.label_Dual.Text = "It's OK";
                this.label_Dual_MeshQuality.Text = "Mesh Quality OK";
              }
              this.newButton_Mesh.Enabled = true;
            }
            else if (dataRow == null && row != null)
            {
              clsHDMFLib.DeleteStudy(row["Name"].ToString());
              source.Rows.Remove(row);
            }
            else if (dataRow != null && row != null)
            {
              bool flag1 = false;
              bool flag2 = false;
              if (!Convert.ToBoolean(dataRow["Check"]))
              {
                this.label_Dual.Text = "Problem";
                this.label_Dual_MeshQuality.Text = "Mesh Quality Problem";
              }
              else
              {
                this.label_Dual.Text = "It's OK";
                this.label_Dual_MeshQuality.Text = "Mesh Quality OK";
                flag1 = true;
              }
              if (!Convert.ToBoolean(row["Check"]))
              {
                this.label_3D.Text = "Problem";
              }
              else
              {
                this.label_3D.Text = "It's OK";
                flag2 = true;
              }
              if (flag1 | flag2)
                this.newButton_Mesh.Enabled = false;
              else
                this.newButton_Mesh.Enabled = true;
            }
            dataRowList.AddRange((IEnumerable<DataRow>) source.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Check"] == DBNull.Value)).ToArray<DataRow>());
          }
        }
        foreach (DataRow dataRow in dataRowList)
        {
          DataGridView dataGridView = !(dataRow["Mesh"].ToString() == "Dual") ? this.dataGridView_3D : this.dataGridView_Dual;
          DataGridViewRow row = dataGridView.Rows[dataGridView.Rows.Add()];
          row.Cells[0].Value = (object) false;
          row.Cells[1].Value = (object) dataRow.Table.TableName;
          row.Cells[2].Value = dataRow["Name"];
          row.Cells[3].Value = dataRow["Sequence"];
          row.Cells[4].Value = dataRow["Status"];
          DataGridViewDisableButtonCell cell1 = (DataGridViewDisableButtonCell) row.Cells[5];
          cell1.Value = (object) "Process";
          string str1 = dataRow["Status"].ToString();
          clsDefine.Status status = clsDefine.Status.COMPLETED;
          string str2 = status.ToString();
          if (str1 == str2)
            cell1.Enabled = false;
          DataGridViewDisableButtonCell cell2 = (DataGridViewDisableButtonCell) row.Cells[6];
          cell2.Value = (object) "Cool";
          int num;
          if (dataRow["Sequence"].ToString().Contains("Cool"))
          {
            string str3 = dataRow["Status"].ToString();
            status = clsDefine.Status.COMPLETED;
            string str4 = status.ToString();
            num = str3 == str4 ? 1 : 0;
          }
          else
            num = 1;
          if (num != 0)
            cell2.Enabled = false;
          DataGridViewDisableButtonCell cell3 = (DataGridViewDisableButtonCell) row.Cells[7];
          cell3.Value = (object) "Runner";
          string str5 = dataRow["Status"].ToString();
          status = clsDefine.Status.COMPLETED;
          string str6 = status.ToString();
          if (str5 == str6)
            cell3.Enabled = false;
          row.Cells[8].Value = dataRow["Analysis"] != DBNull.Value ? dataRow["Analysis"] : (object) "-";
        }
        this.dataGridView_Dual.ClearSelection();
        this.dataGridView_3D.ClearSelection();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]RefreshStudyTab):" + ex.Message));
      }
    }

    private void RefreshReportTab()
    {
      int num = 1;
      int y = 0;
      Dictionary<string, string> source = new Dictionary<string, string>();
      Dictionary<string, string> dictionary = new Dictionary<string, string>();
      this.panel_Report.Controls.Clear();
      try
      {
        DataRow[] selectedProductStudy = this.GetSelectedProductStudy();
        if (selectedProductStudy.Length == 0)
          return;
        foreach (DataRow dataRow1 in selectedProductStudy)
        {
          if (dataRow1["Check"] == DBNull.Value && !dataRow1["Name"].ToString().Contains("Past"))
          {
            FileInfo fileInfo = new FileInfo(clsDefine.g_diTmpReport.ToString() + "\\" + dataRow1["Name"] + "\\Report.ini");
            source.Clear();
            Dictionary<string, string> summaryViewDataFromIni = clsData.GetSummaryViewDataFromIni();
            if (dataRow1["Summary"] != DBNull.Value && dataRow1["Summary"].ToString() != "")
              source = JsonConvert.DeserializeObject<Dictionary<string, string>>(dataRow1["Summary"].ToString());
            Panel panel = new Panel();
            panel.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            panel.BackColor = Color.White;
            panel.BorderStyle = BorderStyle.FixedSingle;
            panel.Location = new Point(0, y);
            panel.Size = new Size(1160, 101);
            y += panel.Height - 1;
            CheckBox checkBox = new CheckBox();
            checkBox.Location = new Point(2, 36);
            checkBox.Name = "cbReport";
            checkBox.Size = new Size(14, 14);
            checkBox.Text = "";
            if (clsDefine.enumLicLevel == clsDefine.LicLevel.Light)
              checkBox.Enabled = false;
            panel.Controls.Add((Control) checkBox);
            NewButton newButton = new NewButton();
            newButton.ButtonBackColor = fileInfo.Exists ? Color.LavenderBlush : Color.White;
            newButton.ButtonText = LocaleControl.getInstance().GetString("IDS_RESULT") + (object) num;
            newButton.Location = new Point(17, -1);
            newButton.Size = new Size(41, 101);
            newButton.NewClick += new EventHandler(this.NbReport_NewClick);
            newButton.NewMouseUp += new MouseEventHandler(this.NbReport_MouseUp);
            panel.Controls.Add((Control) newButton);
            Label label1 = new Label();
            label1.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            label1.AutoSize = false;
            label1.BackColor = Color.Lavender;
            label1.BorderStyle = BorderStyle.FixedSingle;
            label1.Location = new Point(57, -1);
            label1.Size = new Size(120, 23);
            label1.Text = LocaleControl.getInstance().GetString("IDS_PRODUCT");
            label1.TextAlign = ContentAlignment.MiddleCenter;
            panel.Controls.Add((Control) label1);
            NewTextBox newTextBox1 = new NewTextBox();
            newTextBox1.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            newTextBox1.Location = new Point(57, 21);
            newTextBox1.ReadOnly = true;
            newTextBox1.Size = new Size(120, 23);
            newTextBox1.TextAlign = HorizontalAlignment.Center;
            newTextBox1.TextBoxBackColor = Color.LavenderBlush;
            newTextBox1.Value = dataRow1.Table.TableName;
            panel.Controls.Add((Control) newTextBox1);
            Label label2 = new Label();
            label2.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            label2.AutoSize = false;
            label2.BackColor = Color.Lavender;
            label2.BorderStyle = BorderStyle.FixedSingle;
            label2.Location = new Point(176, -1);
            label2.Size = new Size(772, 23);
            label2.Text = LocaleControl.getInstance().GetString("IDS_STUDY");
            label2.TextAlign = ContentAlignment.MiddleCenter;
            panel.Controls.Add((Control) label2);
            NewTextBox newTextBox2 = new NewTextBox();
            newTextBox2.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            newTextBox2.Location = new Point(176, 21);
            newTextBox2.ReadOnly = true;
            newTextBox2.Size = new Size(772, 23);
            newTextBox2.TextAlign = HorizontalAlignment.Center;
            newTextBox2.TextBoxBackColor = Color.LavenderBlush;
            newTextBox2.Name = "ntbStudy";
            newTextBox2.Value = dataRow1["Name"].ToString();
            panel.Controls.Add((Control) newTextBox2);
            Label label3 = new Label();
            label3.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label3.AutoSize = false;
            label3.BackColor = Color.Lavender;
            label3.BorderStyle = BorderStyle.FixedSingle;
            label3.Location = new Point(947, -1);
            label3.Size = new Size(78, 23);
            label3.Text = LocaleControl.getInstance().GetString("IDS_MESH");
            label3.TextAlign = ContentAlignment.MiddleCenter;
            panel.Controls.Add((Control) label3);
            NewTextBox newTextBox3 = new NewTextBox();
            newTextBox3.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            newTextBox3.Location = new Point(947, 21);
            newTextBox3.ReadOnly = true;
            newTextBox3.Size = new Size(78, 23);
            newTextBox3.TextAlign = HorizontalAlignment.Center;
            newTextBox3.TextBoxBackColor = Color.LavenderBlush;
            newTextBox3.Value = dataRow1["Mesh"].ToString();
            panel.Controls.Add((Control) newTextBox3);
            Label label4 = new Label();
            label4.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label4.AutoSize = false;
            label4.BackColor = Color.Lavender;
            label4.BorderStyle = BorderStyle.FixedSingle;
            label4.Location = new Point(1024, -1);
            label4.Size = new Size(135, 23);
            label4.Text = LocaleControl.getInstance().GetString("IDS_SEQUENCE");
            label4.TextAlign = ContentAlignment.MiddleCenter;
            panel.Controls.Add((Control) label4);
            NewTextBox newTextBox4 = new NewTextBox();
            newTextBox4.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            newTextBox4.Location = new Point(1024, 21);
            newTextBox4.ReadOnly = true;
            newTextBox4.Size = new Size(135, 23);
            newTextBox4.TextAlign = HorizontalAlignment.Center;
            newTextBox4.TextBoxBackColor = Color.LavenderBlush;
            newTextBox4.Value = dataRow1["Sequence"].ToString();
            panel.Controls.Add((Control) newTextBox4);
            DataGridView dataGridView = new DataGridView();
            dataGridView.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
            dataGridView.ScrollBars = ScrollBars.Horizontal;
            dataGridView.AllowUserToAddRows = false;
            dataGridView.AllowUserToDeleteRows = false;
            dataGridView.AllowUserToOrderColumns = false;
            dataGridView.AllowUserToResizeColumns = false;
            dataGridView.AllowUserToResizeRows = false;
            dataGridView.BackgroundColor = Color.White;
            dataGridView.BorderStyle = BorderStyle.None;
            dataGridView.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridView.DefaultCellStyle.BackColor = Color.LavenderBlush;
            dataGridView.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.Lavender;
            dataGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridView.EnableHeadersVisualStyles = false;
            dataGridView.ReadOnly = true;
            dataGridView.Location = new Point(57, 43);
            dataGridView.RowHeadersVisible = false;
            dataGridView.Size = new Size(1102, 57);
            dataGridView.SelectionChanged += new EventHandler(this.DgvReport_SelectionChanged);
            foreach (DataRow dataRow2 in clsDefine.g_dtSummaryView.AsEnumerable())
            {
              DataRow drSummary = dataRow2;
              if (clsUtill.ConvertToBoolean(drSummary["Use"].ToString()))
              {
                KeyValuePair<string, string> keyValuePair = summaryViewDataFromIni.Where<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Value.Equals(drSummary["Section"].ToString()))).FirstOrDefault<KeyValuePair<string, string>>();
                DataGridViewColumn column = dataGridView.Columns[dataGridView.Columns.Add(keyValuePair.Value, keyValuePair.Key)];
                column.SortMode = DataGridViewColumnSortMode.NotSortable;
                column.AutoSizeMode = DataGridViewAutoSizeColumnMode.NotSet;
                column.Width = 184;
              }
            }
            if (source.Count > 0 && dataGridView.Columns.Count > 0)
            {
              DataGridViewRow row = dataGridView.Rows[dataGridView.Rows.Add()];
              foreach (DataGridViewColumn column in (BaseCollection) dataGridView.Columns)
              {
                DataGridViewColumn dgvcReport = column;
                if (source.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == dgvcReport.Name)))
                {
                  string str = source[dgvcReport.Name];
                  if (dgvcReport.Name == "ShortShot")
                    str = !(str == "X") ? LocaleControl.getInstance().GetString("IDS_SHORT") : LocaleControl.getInstance().GetString("IDS_NO_SHORT");
                  if (str.Contains("|"))
                  {
                    string[] strArray = str.Split('|');
                    str = strArray[0] + " ~ " + strArray[1];
                  }
                  row.Cells[dgvcReport.Name].Value = (object) str;
                }
              }
            }
            panel.Controls.Add((Control) dataGridView);
            this.panel_Report.Controls.Add((Control) panel);
            ++num;
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]RefreshReportTab):" + ex.Message));
      }
    }

    private void RefreshPastCasesTab()
    {
      int num = 1;
      int y = -1;
      Dictionary<string, string> source = new Dictionary<string, string>();
      Dictionary<string, string> dictionary = new Dictionary<string, string>();
      this.panel_PastCases.Controls.Clear();
      try
      {
        if (clsDefine.g_dsProduct == null || clsDefine.g_dsProduct.Tables["PAST_Cases"] == null)
          return;
        DataRow[] array = clsDefine.g_dsProduct.Tables["PAST_Cases"].AsEnumerable().ToArray<DataRow>();
        if (array == null || array.Length == 0)
          return;
        foreach (DataRow dataRow1 in array)
        {
          if (dataRow1["Check"] == DBNull.Value)
          {
            FileInfo fileInfo = new FileInfo(clsDefine.g_diTmpReport.ToString() + "\\" + dataRow1["Name"] + "\\Report.ini");
            source.Clear();
            Dictionary<string, string> summaryViewDataFromIni = clsData.GetSummaryViewDataFromIni();
            if (dataRow1["Summary"] != DBNull.Value && dataRow1["Summary"].ToString() != "")
              source = JsonConvert.DeserializeObject<Dictionary<string, string>>(dataRow1["Summary"].ToString());
            Panel panel = new Panel();
            panel.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            panel.BackColor = Color.White;
            panel.BorderStyle = BorderStyle.FixedSingle;
            panel.Location = new Point(0, y);
            panel.Size = new Size(1160, 101);
            y += panel.Height - 1;
            CheckBox checkBox = new CheckBox();
            checkBox.Location = new Point(2, 36);
            checkBox.Name = "cbReport";
            checkBox.Size = new Size(14, 14);
            checkBox.Text = "";
            if (clsDefine.enumLicLevel == clsDefine.LicLevel.Light)
              checkBox.Enabled = false;
            panel.Controls.Add((Control) checkBox);
            NewButton newButton = new NewButton();
            newButton.ButtonBackColor = fileInfo.Exists ? Color.LavenderBlush : Color.White;
            newButton.ButtonText = LocaleControl.getInstance().GetString("IDS_RESULT") + (object) num;
            newButton.Location = new Point(17, -1);
            newButton.Size = new Size(41, 101);
            newButton.NewClick += new EventHandler(this.NbReport_NewClick);
            newButton.NewMouseUp += new MouseEventHandler(this.NbReport_MouseUp);
            if (clsDefine.enumLicLevel == clsDefine.LicLevel.Light)
              newButton.Enabled = false;
            panel.Controls.Add((Control) newButton);
            Label label1 = new Label();
            label1.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            label1.AutoSize = false;
            label1.BackColor = Color.Lavender;
            label1.BorderStyle = BorderStyle.FixedSingle;
            label1.Location = new Point(57, -1);
            label1.Size = new Size(892, 23);
            label1.Text = LocaleControl.getInstance().GetString("IDS_STUDY");
            label1.TextAlign = ContentAlignment.MiddleCenter;
            panel.Controls.Add((Control) label1);
            NewTextBox newTextBox1 = new NewTextBox();
            newTextBox1.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            newTextBox1.Location = new Point(57, 21);
            newTextBox1.ReadOnly = true;
            newTextBox1.Size = new Size(892, 23);
            newTextBox1.TextAlign = HorizontalAlignment.Center;
            newTextBox1.TextBoxBackColor = Color.LavenderBlush;
            newTextBox1.Name = "ntbStudy";
            newTextBox1.Value = dataRow1["Name"].ToString();
            panel.Controls.Add((Control) newTextBox1);
            Label label2 = new Label();
            label2.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label2.AutoSize = false;
            label2.BackColor = Color.Lavender;
            label2.BorderStyle = BorderStyle.FixedSingle;
            label2.Location = new Point(947, -1);
            label2.Size = new Size(78, 23);
            label2.Text = LocaleControl.getInstance().GetString("IDS_MESH");
            label2.TextAlign = ContentAlignment.MiddleCenter;
            panel.Controls.Add((Control) label2);
            NewTextBox newTextBox2 = new NewTextBox();
            newTextBox2.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            newTextBox2.Location = new Point(947, 21);
            newTextBox2.ReadOnly = true;
            newTextBox2.Size = new Size(78, 23);
            newTextBox2.TextAlign = HorizontalAlignment.Center;
            newTextBox2.TextBoxBackColor = Color.LavenderBlush;
            newTextBox2.Value = dataRow1["Mesh"].ToString();
            panel.Controls.Add((Control) newTextBox2);
            Label label3 = new Label();
            label3.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label3.AutoSize = false;
            label3.BackColor = Color.Lavender;
            label3.BorderStyle = BorderStyle.FixedSingle;
            label3.Location = new Point(1024, -1);
            label3.Size = new Size(135, 23);
            label3.Text = LocaleControl.getInstance().GetString("IDS_SEQUENCE");
            label3.TextAlign = ContentAlignment.MiddleCenter;
            panel.Controls.Add((Control) label3);
            NewTextBox newTextBox3 = new NewTextBox();
            newTextBox3.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            newTextBox3.Location = new Point(1024, 21);
            newTextBox3.ReadOnly = true;
            newTextBox3.Size = new Size(135, 23);
            newTextBox3.TextAlign = HorizontalAlignment.Center;
            newTextBox3.TextBoxBackColor = Color.LavenderBlush;
            newTextBox3.Value = dataRow1["Sequence"].ToString();
            panel.Controls.Add((Control) newTextBox3);
            DataGridView dataGridView = new DataGridView();
            dataGridView.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
            dataGridView.ScrollBars = ScrollBars.Horizontal;
            dataGridView.AllowUserToAddRows = false;
            dataGridView.AllowUserToDeleteRows = false;
            dataGridView.AllowUserToOrderColumns = false;
            dataGridView.AllowUserToResizeColumns = false;
            dataGridView.AllowUserToResizeRows = false;
            dataGridView.BackgroundColor = Color.White;
            dataGridView.BorderStyle = BorderStyle.None;
            dataGridView.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridView.DefaultCellStyle.BackColor = Color.LavenderBlush;
            dataGridView.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.Lavender;
            dataGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridView.EnableHeadersVisualStyles = false;
            dataGridView.ReadOnly = true;
            dataGridView.Location = new Point(57, 43);
            dataGridView.RowHeadersVisible = false;
            dataGridView.Size = new Size(1102, 57);
            dataGridView.SelectionChanged += new EventHandler(this.DgvReport_SelectionChanged);
            foreach (DataRow dataRow2 in clsDefine.g_dtSummaryView.AsEnumerable())
            {
              DataRow drSummary = dataRow2;
              if (clsUtill.ConvertToBoolean(drSummary["Use"].ToString()))
              {
                KeyValuePair<string, string> keyValuePair = summaryViewDataFromIni.Where<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Value.Equals(drSummary["Section"].ToString()))).FirstOrDefault<KeyValuePair<string, string>>();
                DataGridViewColumn column = dataGridView.Columns[dataGridView.Columns.Add(keyValuePair.Value, keyValuePair.Key)];
                column.SortMode = DataGridViewColumnSortMode.NotSortable;
                column.AutoSizeMode = DataGridViewAutoSizeColumnMode.NotSet;
                column.Width = 184;
              }
            }
            if (source.Count > 0 && dataGridView.Columns.Count > 0)
            {
              DataGridViewRow row = dataGridView.Rows[dataGridView.Rows.Add()];
              foreach (DataGridViewColumn column in (BaseCollection) dataGridView.Columns)
              {
                DataGridViewColumn dgvcReport = column;
                if (source.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == dgvcReport.Name)))
                {
                  string str = source[dgvcReport.Name];
                  if (dgvcReport.Name == "ShortShot")
                    str = !(str == "X") ? LocaleControl.getInstance().GetString("IDS_SHORT") : LocaleControl.getInstance().GetString("IDS_NO_SHORT");
                  if (str.Contains("|"))
                  {
                    string[] strArray = str.Split('|');
                    str = strArray[0] + " ~ " + strArray[1];
                  }
                  row.Cells[dgvcReport.Name].Value = (object) str;
                }
              }
            }
            panel.Controls.Add((Control) dataGridView);
            this.panel_PastCases.Controls.Add((Control) panel);
            ++num;
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]RefreshPastCasesTab):" + ex.Message));
      }
    }

    private void RefreshComparisonTab()
    {
      List<DataRow> dataRowList = new List<DataRow>();
      string empty = string.Empty;
      this.dataGridView_List.Rows.Clear();
      this.tableLayoutPanel_CMPR.Controls.Clear();
      this.label_Template.Visible = false;
      this.dataGridView_List.ColumnHeadersVisible = false;
      this.m_lst_dicComparison = new Dictionary<string, DataRow>();
      try
      {
        DataRow[] first = this.GetSelectedProductStudy();
        if (clsDefine.g_dsProduct == null)
          return;
        if (clsDefine.g_dsProduct.Tables["PAST_Cases"] != null)
        {
          DataRow[] array = clsDefine.g_dsProduct.Tables["PAST_Cases"].AsEnumerable().ToArray<DataRow>();
          first = ((IEnumerable<DataRow>) first).Concat<DataRow>((IEnumerable<DataRow>) array).ToArray<DataRow>();
        }
        if (first.Length == 0)
          return;
        foreach (DataRow dataRow in first)
        {
          if (dataRow["Check"] == DBNull.Value && clsUtill.ConvertToBoolean(dataRow["Compare"].ToString()))
          {
            clsReportUtill.ReadINI("Template", "Company", new FileInfo(clsReportDefine.g_diTmpReport.FullName + "\\" + dataRow["Name"] + "\\Report.ini").FullName);
            DataGridViewRow row = this.dataGridView_List.Rows[this.dataGridView_List.Rows.Add()];
            row.Cells[0].Value = dataRow["Template"];
            row.Cells[1].Value = dataRow["Name"];
            row.Cells[2].Value = dataRow["Mesh"];
            row.Cells[3].Value = dataRow["Sequence"];
          }
        }
        this.dataGridView_List.Sort(this.dataGridView_List.Columns[0], ListSortDirection.Ascending);
        if (this.dataGridView_List.Rows.Count > 0)
          this.dataGridView_List.ColumnHeadersVisible = true;
        string path = clsReportDefine.g_diTemplate.FullName + "\\" + clsDefine.g_enumCompany.ToString() + "\\Template_3-Case.hdp";
        int x1 = this.label_Template.Location.X;
        bool flag = true;
        int x2;
        if (File.Exists(path) || clsDefine.g_enumCompany == clsHDMFLibDefine.Company.admin)
        {
          x2 = this.radioButton_Case3.Location.X + this.radioButton_Case3.Width + this.radioButton_Case3.Margin.Right;
        }
        else
        {
          x2 = this.radioButton_Case2.Location.X + this.radioButton_Case2.Width + this.radioButton_Case2.Margin.Right;
          this.radioButton_Case2.Checked = true;
          flag = false;
        }
        this.label_Template.Location = new Point(x2, this.label_Template.Location.Y);
        this.radioButton_Case3.Visible = flag;
        if (this.radioButton_Case2.Checked)
          this.tableLayoutPanel_CMPR.ColumnCount = 2;
        else if (this.radioButton_Case3.Checked)
          this.tableLayoutPanel_CMPR.ColumnCount = 3;
        for (int column1 = 0; column1 < this.tableLayoutPanel_CMPR.ColumnCount; ++column1)
        {
          Panel panel = new Panel();
          panel.Dock = DockStyle.Fill;
          panel.BackColor = Color.White;
          panel.BorderStyle = BorderStyle.FixedSingle;
          Label label = new Label();
          label.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
          label.AutoSize = false;
          label.BackColor = Color.FromArgb(229, 238, 248);
          label.ForeColor = Color.MidnightBlue;
          label.BorderStyle = BorderStyle.FixedSingle;
          label.Font = new Font("Segoe UI", 9f, FontStyle.Bold);
          label.Location = new Point(-1, -1);
          label.Size = new Size(panel.Width + 2, 20);
          label.Text = "[Compare" + (object) (column1 + 1) + "]";
          label.TextAlign = ContentAlignment.MiddleCenter;
          panel.Controls.Add((Control) label);
          DataGridView dataGridView = new DataGridView();
          dataGridView.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
          dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
          dataGridView.ScrollBars = ScrollBars.Horizontal;
          dataGridView.AllowUserToAddRows = false;
          dataGridView.AllowUserToDeleteRows = false;
          dataGridView.AllowUserToOrderColumns = false;
          dataGridView.AllowUserToResizeColumns = false;
          dataGridView.AllowUserToResizeRows = false;
          dataGridView.AllowDrop = true;
          dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
          dataGridView.BackgroundColor = Color.White;
          dataGridView.BorderStyle = BorderStyle.None;
          dataGridView.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
          dataGridView.DefaultCellStyle.BackColor = Color.LavenderBlush;
          dataGridView.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
          dataGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.Lavender;
          dataGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
          dataGridView.EnableHeadersVisualStyles = false;
          dataGridView.ReadOnly = true;
          dataGridView.Location = new Point(-1, 18);
          dataGridView.RowHeadersVisible = false;
          dataGridView.Size = new Size(panel.Width + 2, 68);
          dataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
          dataGridView.Name = "Report" + (object) (column1 + 1);
          dataGridView.MouseDown += new MouseEventHandler(this.dataGridView_Compare_MouseDown);
          dataGridView.DragDrop += new DragEventHandler(this.datagridView_Compare_DragDrop);
          dataGridView.DragEnter += new DragEventHandler(this.datagridView_Compare_DragEnter);
          DataGridViewColumn column2 = dataGridView.Columns[dataGridView.Columns.Add("Column_Template", LocaleControl.getInstance().GetString("IDS_TEMPLATE"))];
          column2.SortMode = DataGridViewColumnSortMode.NotSortable;
          column2.AutoSizeMode = DataGridViewAutoSizeColumnMode.NotSet;
          column2.Visible = false;
          column2.Width = 280;
          DataGridViewColumn column3 = dataGridView.Columns[dataGridView.Columns.Add("Column_Study", LocaleControl.getInstance().GetString("IDS_STUDY"))];
          column3.SortMode = DataGridViewColumnSortMode.NotSortable;
          column3.AutoSizeMode = DataGridViewAutoSizeColumnMode.NotSet;
          column3.Width = 280;
          DataGridViewColumn column4 = dataGridView.Columns[dataGridView.Columns.Add("Column_Mesh", LocaleControl.getInstance().GetString("IDS_MESH"))];
          column4.SortMode = DataGridViewColumnSortMode.NotSortable;
          column4.AutoSizeMode = DataGridViewAutoSizeColumnMode.NotSet;
          column4.Width = 47;
          DataGridViewColumn column5 = dataGridView.Columns[dataGridView.Columns.Add("Column_Sequence", LocaleControl.getInstance().GetString("IDS_SEQUENCE"))];
          column5.SortMode = DataGridViewColumnSortMode.NotSortable;
          column5.AutoSizeMode = DataGridViewAutoSizeColumnMode.NotSet;
          column5.Width = 52;
          panel.Controls.Add((Control) dataGridView);
          this.tableLayoutPanel_CMPR.Controls.Add((Control) panel, column1, 0);
        }
        this.dataGridView_List.ClearSelection();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]RefreshStudyTab):" + ex.Message));
      }
    }

    private void RefreshAIStudyTab()
    {
      List<DataRow> dataRowList = new List<DataRow>();
      this.dataGridView_AI_Dual.Rows.Clear();
      this.dataGridView_AI_3D.Rows.Clear();
      TreeNode tnSelect = this.treeView_Product.SelectedNode;
      if (tnSelect == null)
        return;
      try
      {
        if (tnSelect.Level == 0)
        {
          foreach (DataTable table in (InternalDataCollectionBase) clsDefine.g_dsProduct.Tables)
          {
            foreach (DataRow dataRow in table.AsEnumerable())
            {
              if (dataRow["Check"] == DBNull.Value && !dataRow["Name"].ToString().Contains("Past"))
                dataRowList.Add(dataRow);
            }
          }
        }
        else
        {
          DataTable source = clsDefine.g_dsProduct.Tables.Cast<DataTable>().Where<DataTable>((System.Func<DataTable, bool>) (Temp => Temp.TableName == tnSelect.Text)).FirstOrDefault<DataTable>();
          if (source != null)
            dataRowList.AddRange((IEnumerable<DataRow>) source.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Check"] != DBNull.Value)).ToArray<DataRow>());
        }
        foreach (DataRow dataRow in dataRowList)
        {
          DataGridView dataGridView = !dataRow["Name"].ToString().Contains("Tetra") ? this.dataGridView_AI_Dual : this.dataGridView_AI_3D;
          DataGridViewRow row = dataGridView.Rows[dataGridView.Rows.Add()];
          row.Cells[0].Value = (object) false;
          row.Cells[1].Value = (object) dataRow.Table.TableName;
          row.Cells[2].Value = dataRow["Name"];
          row.Cells[3].Value = (object) "Input Data";
        }
        this.dataGridView_AI_Dual.ClearSelection();
        this.dataGridView_AI_3D.ClearSelection();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]RefreshAIStudyTab):" + ex.Message));
      }
    }

    private void RefreshAIResultTab()
    {
      int num = 1;
      int y = 0;
      Dictionary<string, string> dictionary = new Dictionary<string, string>();
      this.panel_AIResult.Controls.Clear();
      try
      {
        DataRow[] productStudyForAi = this.GetSelectedProductStudyForAI();
        if (productStudyForAi.Length == 0)
          return;
        foreach (DataRow dataRow in productStudyForAi)
        {
          if (dataRow["AIData"] != DBNull.Value)
          {
            FileInfo fileInfo = new FileInfo(clsDefine.g_diTmpAI.ToString() + "\\" + dataRow["Name"] + "\\AIResult.ini");
            Panel panel = new Panel();
            panel.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            panel.BackColor = Color.White;
            panel.BorderStyle = BorderStyle.FixedSingle;
            panel.Location = new Point(0, y);
            panel.Size = new Size(1160, 45);
            y += panel.Height - 1;
            CheckBox checkBox = new CheckBox();
            checkBox.Location = new Point(2, 14);
            checkBox.Name = "cbAIResult";
            checkBox.Size = new Size(14, 14);
            checkBox.Text = "";
            panel.Controls.Add((Control) checkBox);
            NewButton newButton = new NewButton();
            newButton.ButtonBackColor = fileInfo.Exists ? Color.LavenderBlush : Color.White;
            newButton.ButtonText = LocaleControl.getInstance().GetString("IDS_RESULT") + (object) num;
            newButton.Location = new Point(17, -1);
            newButton.Size = new Size(41, 45);
            newButton.NewClick += new EventHandler(this.NbAIResult_NewClick);
            panel.Controls.Add((Control) newButton);
            Label label1 = new Label();
            label1.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            label1.AutoSize = false;
            label1.BackColor = Color.Lavender;
            label1.BorderStyle = BorderStyle.FixedSingle;
            label1.Location = new Point(57, -1);
            label1.Size = new Size(120, 23);
            label1.Text = LocaleControl.getInstance().GetString("IDS_PRODUCT");
            label1.TextAlign = ContentAlignment.MiddleCenter;
            panel.Controls.Add((Control) label1);
            NewTextBox newTextBox1 = new NewTextBox();
            newTextBox1.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            newTextBox1.Location = new Point(57, 21);
            newTextBox1.ReadOnly = true;
            newTextBox1.Size = new Size(120, 23);
            newTextBox1.TextAlign = HorizontalAlignment.Center;
            newTextBox1.TextBoxBackColor = Color.LavenderBlush;
            newTextBox1.Value = dataRow.Table.TableName;
            panel.Controls.Add((Control) newTextBox1);
            Label label2 = new Label();
            label2.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            label2.AutoSize = false;
            label2.BackColor = Color.Lavender;
            label2.BorderStyle = BorderStyle.FixedSingle;
            label2.Location = new Point(176, -1);
            label2.Size = new Size(772, 23);
            label2.Text = LocaleControl.getInstance().GetString("IDS_STUDY");
            label2.TextAlign = ContentAlignment.MiddleCenter;
            panel.Controls.Add((Control) label2);
            NewTextBox newTextBox2 = new NewTextBox();
            newTextBox2.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            newTextBox2.Location = new Point(176, 21);
            newTextBox2.ReadOnly = true;
            newTextBox2.Size = new Size(772, 23);
            newTextBox2.TextAlign = HorizontalAlignment.Center;
            newTextBox2.TextBoxBackColor = Color.LavenderBlush;
            newTextBox2.Name = "ntbStudy";
            newTextBox2.Value = dataRow["Name"].ToString();
            panel.Controls.Add((Control) newTextBox2);
            Label label3 = new Label();
            label3.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label3.AutoSize = false;
            label3.BackColor = Color.Lavender;
            label3.BorderStyle = BorderStyle.FixedSingle;
            label3.Location = new Point(947, -1);
            label3.Size = new Size(78, 23);
            label3.Text = LocaleControl.getInstance().GetString("IDS_MESH");
            label3.TextAlign = ContentAlignment.MiddleCenter;
            panel.Controls.Add((Control) label3);
            NewTextBox newTextBox3 = new NewTextBox();
            newTextBox3.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            newTextBox3.Location = new Point(947, 21);
            newTextBox3.ReadOnly = true;
            newTextBox3.Size = new Size(78, 23);
            newTextBox3.TextAlign = HorizontalAlignment.Center;
            newTextBox3.TextBoxBackColor = Color.LavenderBlush;
            newTextBox3.Value = dataRow["Mesh"].ToString();
            panel.Controls.Add((Control) newTextBox3);
            Label label4 = new Label();
            label4.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label4.AutoSize = false;
            label4.BackColor = Color.Lavender;
            label4.BorderStyle = BorderStyle.FixedSingle;
            label4.Location = new Point(1024, -1);
            label4.Size = new Size(135, 23);
            label4.Text = LocaleControl.getInstance().GetString("IDS_SEQUENCE");
            label4.TextAlign = ContentAlignment.MiddleCenter;
            panel.Controls.Add((Control) label4);
            NewTextBox newTextBox4 = new NewTextBox();
            newTextBox4.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            newTextBox4.Location = new Point(1024, 21);
            newTextBox4.ReadOnly = true;
            newTextBox4.Size = new Size(135, 23);
            newTextBox4.TextAlign = HorizontalAlignment.Center;
            newTextBox4.TextBoxBackColor = Color.LavenderBlush;
            newTextBox4.Value = dataRow["Sequence"].ToString();
            panel.Controls.Add((Control) newTextBox4);
            this.panel_AIResult.Controls.Add((Control) panel);
            ++num;
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]RefreshAIResultTab):" + ex.Message));
      }
    }

    private void DgvAIResult_SelectionChanged(object sender, EventArgs e)
    {
      DataGridView dataGridView = sender as DataGridView;
      if (dataGridView.CurrentCell == null)
        return;
      dataGridView.ClearSelection();
    }

    private void NbAIResult_NewClick(object sender, EventArgs e)
    {
      Dictionary<string, string> dictionary1 = new Dictionary<string, string>();
      NewButton newButton = sender as NewButton;
      DataRow studyFromName = this.GetStudyFromName(((NewTextBox) newButton.Parent.Controls["ntbStudy"]).Value);
      this.Hide();
      try
      {
        Dictionary<string, string> dictionary2 = JsonConvert.DeserializeObject<Dictionary<string, string>>(studyFromName["AIData"].ToString());
        int num = (int) new frmAISolution()
        {
          m_dicAIData = dictionary2,
          m_drStudy = studyFromName
        }.ShowDialog();
        if (new FileInfo(clsDefine.g_diTmpAI.ToString() + "\\" + studyFromName["Name"] + "\\AIResult.ini").Exists)
          newButton.ButtonBackColor = Color.LavenderBlush;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMoldFlow][frmMain]NbAIResult_NewClick):" + ex.Message));
      }
      clsUtill.ShowForm((Form) this);
      this.RefreshAIResultTab();
    }

    private void DgvReport_SelectionChanged(object sender, EventArgs e)
    {
      DataGridView dataGridView = sender as DataGridView;
      if (dataGridView.CurrentCell == null)
        return;
      dataGridView.ClearSelection();
    }

    private void NbReport_NewClick(object sender, EventArgs e)
    {
      NewButton newButton = sender as NewButton;
      DataRow studyFromName = this.GetStudyFromName(((NewTextBox) newButton.Parent.Controls["ntbStudy"]).Value);
      this.Hide();
      try
      {
        clsReportDefine.g_dtInjectionDB = clsDefine.g_dtInjectionDB;
        clsReportDefine.enumLicLevel = (clsReportDefine.LicLevel) clsDefine.enumLicLevel;
        clsHDMFLib.OpenStudy(studyFromName["Name"].ToString());
        if (string.IsNullOrEmpty(studyFromName["Sequence"].ToString()))
          this.UpdateStudy(studyFromName);
        clsReport.ShowReportForm((Form) this, studyFromName, clsDefine.g_strLanguageType, clsDefine.g_enumCompany);
        FileInfo fileInfo = new FileInfo(clsDefine.g_diTmpReport.ToString() + "\\" + studyFromName["Name"] + "\\Report.ini");
        if (fileInfo.Exists)
        {
          newButton.ButtonBackColor = Color.LavenderBlush;
          studyFromName["Template"] = (object) clsUtill.ReadINI("Template", "Company", fileInfo.FullName);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMoldFlow][frmMain]NbReport_NewClick):" + ex.Message));
      }
      clsUtill.ShowForm((Form) this);
      this.RefreshReportTab();
    }

    private void NbReport_MouseUp(object sender, MouseEventArgs e)
    {
      NewButton newButton = sender as NewButton;
      if (e.Button != MouseButtons.Right)
        return;
      ContextMenu contextMenu = new ContextMenu();
      MenuItem menuItem = new MenuItem();
      menuItem.Text = LocaleControl.getInstance().GetString("IDS_ADD_COMPARISONREPORT");
      menuItem.Click += new EventHandler(this.MiAddComparisonList_Click);
      contextMenu.MenuItems.Add(menuItem);
      contextMenu.Show((Control) newButton, e.Location);
    }

    private void MiAddComparisonList_Click(object sender, EventArgs e)
    {
      try
      {
        DataRow studyFromName = this.GetStudyFromName(((NewTextBox) (((sender as MenuItem).Parent as ContextMenu).SourceControl as NewButton).Parent.Controls["ntbStudy"]).Value);
        if (studyFromName["Template"] == null || studyFromName["Template"].ToString() == string.Empty)
        {
          int num = (int) clsUtill.ShowMessageBox((Form) this, LocaleControl.getInstance().GetString("IDS_SET_REPORT"), this.Text, p_msgIcon: MessageBoxIcon.Exclamation);
          studyFromName["Compare"] = (object) false;
        }
        else
          studyFromName["Compare"] = (object) true;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMoldFlow][frmMain]MiAddCompareList_Click):" + ex.Message));
      }
    }

    private void dataGridView_Study_SelectionChanged(object sender, EventArgs e)
    {
      DataGridView dataGridView = sender as DataGridView;
      if (dataGridView.CurrentCell == null || dataGridView.CurrentCell.ColumnIndex == 5 || dataGridView.CurrentCell.ColumnIndex == 6 || dataGridView.CurrentCell.ColumnIndex == 7)
        return;
      dataGridView.ClearSelection();
    }

    private void dataGridView_AI_Study_SelectionChanged(object sender, EventArgs e)
    {
      DataGridView dataGridView = sender as DataGridView;
      if (dataGridView.CurrentCell == null || dataGridView.CurrentCell.ColumnIndex == 3)
        return;
      dataGridView.ClearSelection();
    }

    private void newButton_NewClick(object sender, EventArgs e)
    {
      NewButton newButton = sender as NewButton;
      if (newButton.Name.Contains("Add") && !newButton.Name.Contains("Past"))
        this.AddStudy(newButton.Name.Replace("newButton_", "").Replace("_Add", ""));
      else if (newButton.Name.Contains("Del") && !newButton.Name.Contains("Past"))
        this.DelStudy(newButton.Name.Replace("newButton_", "").Replace("_Del", ""));
      else if (newButton.Name.Contains("Update"))
        this.UpdateStudy(newButton.Name.Replace("newButton_", "").Replace("_Update", ""));
      else if (newButton == this.newButton_Mesh)
        this.UpdateMesh();
      else if (newButton == this.newButton_Analysis)
        this.ExecuteAnalysis();
      else if (newButton.Name.Contains("Report"))
        this.ExportReport(newButton.Name.Replace("newButton_", ""));
      else if (newButton == this.newButton_Rotate)
        this.RotateModel();
      else if (newButton == this.newButton_Comparison)
        this.ExportCMPRReport();
      else if (newButton == this.newButton_Past_Add)
        this.AddPastCases();
      else if (newButton == this.newButton_Past_Del)
        this.DelPastCases();
      else if (newButton == this.newButton_AISolution)
      {
        this.ExecuteAISolution();
      }
      else
      {
        if (newButton != this.newButton_AIResult)
          return;
        this.ExecuteAIResult();
      }
    }

    private void AddStudy(string p_strMesh)
    {
      TreeNode selectedNode = this.treeView_Product.SelectedNode;
      if (selectedNode == null || selectedNode.Level == 0)
        return;
      DataTable table = clsDefine.g_dsProduct.Tables[selectedNode.Text];
      if (table == null)
        return;
      DataRow drBStudy = table.Rows.Cast<DataRow>().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Mesh"].ToString() == p_strMesh && Temp["Check"] != DBNull.Value && Convert.ToBoolean(Temp["Check"]))).FirstOrDefault<DataRow>();
      if (drBStudy == null)
        return;
      this.Hide();
      clsUtill.StartProgress("copy and save Study...");
      try
      {
        int iRev = 1;
        while (true)
        {
          if (table.Rows.Cast<DataRow>().Any<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == drBStudy["Name"].ToString() + "_" + p_strMesh + "_Rev" + (object) iRev)))
            iRev++;
          else
            break;
        }
        if (!clsHDMFLib.ExistFolder(table.TableName + "_" + p_strMesh))
          clsHDMFLib.CreateFolder(table.TableName, table.TableName + "_" + p_strMesh);
        if (clsHDMFLib.CopyStudy(drBStudy["Name"].ToString(), drBStudy["Name"].ToString() + "_" + p_strMesh + "_Rev" + (object) iRev, table.TableName + "_" + p_strMesh, true, false))
        {
          clsHDMFLib.SaveStudy();
          DataRow dataRow = table.Rows.Add();
          dataRow["Name"] = (object) (drBStudy["Name"].ToString() + "_" + p_strMesh + "_Rev" + (object) iRev);
          dataRow["Mesh"] = (object) p_strMesh;
          dataRow["Check"] = (object) DBNull.Value;
          dataRow["Sequence"] = drBStudy["Sequence"];
          dataRow["Summary"] = (object) DBNull.Value;
          dataRow["Status"] = (object) clsDefine.Status.NOT_READY;
          dataRow["JobID"] = (object) DBNull.Value;
          dataRow["Analysis"] = (object) DBNull.Value;
          dataRow["Compare"] = (object) DBNull.Value;
          dataRow["Template"] = (object) string.Empty;
          this.RefreshStudyTab();
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]AddStudy):" + ex.Message));
      }
      clsUtill.EndProgress();
      clsUtill.ShowForm((Form) this);
    }

    private void DelStudy(string p_strMesh)
    {
      DataGridView dataGridView = !(p_strMesh == "Dual") ? this.dataGridView_3D : this.dataGridView_Dual;
      List<DataRow> source = new List<DataRow>();
      foreach (DataGridViewRow row in (IEnumerable) dataGridView.Rows)
      {
        if (Convert.ToBoolean(row.Cells[0].Value))
          source.Add(clsData.GetStudy(row.Cells[2].Value.ToString()));
      }
      if (source.Count == 0)
        return;
      this.Hide();
      clsUtill.StartProgress("delete Study...");
      try
      {
        while (true)
        {
          if (source.Count != 0)
          {
            DataRow row = source.First<DataRow>();
            source.Remove(row);
            clsHDMFLib.DeleteStudy(row["Name"].ToString());
            DataTable table = row.Table;
            table.Rows.Remove(row);
            if (!table.Rows.Cast<DataRow>().Any<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Check"] == DBNull.Value && Temp["Mesh"].ToString() == p_strMesh)))
              clsHDMFLib.DeleteFolder(table.TableName + "_" + p_strMesh);
          }
          else
            break;
        }
        this.RefreshStudyTab();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]DelStudy):" + ex.Message));
      }
      clsUtill.EndProgress();
      clsUtill.ShowForm((Form) this);
    }

    private void UpdateStudy(string p_strMesh)
    {
      DataGridView dataGridView = !(p_strMesh == "Dual") ? this.dataGridView_3D : this.dataGridView_Dual;
      List<DataRow> dataRowList = new List<DataRow>();
      foreach (DataGridViewRow row in (IEnumerable) dataGridView.Rows)
      {
        if (Convert.ToBoolean(row.Cells[0].Value))
          dataRowList.Add(clsData.GetStudy(row.Cells[2].Value.ToString()));
      }
      if (dataRowList.Count == 0)
        return;
      this.Hide();
      clsUtill.StartProgress("update Study Data...");
      try
      {
        for (int index = 0; index < dataRowList.Count; ++index)
        {
          DataRow dataRow = dataRowList[index];
          clsHDMFLib.OpenStudy(dataRow["Name"].ToString());
          dataRow["Sequence"] = (object) clsHDMFLib.GetStudySequence();
          if (dataRow["Status"].ToString() != "COMPLETED")
            dataRow["Status"] = clsHDMFLib.ExistFolderFromFolderManager("Runner System") ? (object) clsDefine.Status.READY : (object) clsDefine.Status.NOT_READY;
          clsHDMFLib.CloseStudy();
        }
        this.RefreshStudyTab();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]UpdateStudy):" + ex.Message));
      }
      clsUtill.EndProgress();
      clsUtill.ShowForm((Form) this);
    }

    private void UpdateStudy(DataRow p_drStudy)
    {
      try
      {
        p_drStudy["Sequence"] = (object) clsHDMFLib.GetStudySequence();
        if (!(p_drStudy["Status"].ToString() != "COMPLETED"))
          return;
        p_drStudy["Status"] = clsHDMFLib.ExistFolderFromFolderManager("Runner System") ? (object) clsDefine.Status.READY : (object) clsDefine.Status.NOT_READY;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]UpdateStudy):" + ex.Message));
      }
    }

    private void dataGridView_Study_CellClick(object sender, DataGridViewCellEventArgs e)
    {
      DataGridView dataGridView = sender as DataGridView;
      if (e.RowIndex == -1 || e.ColumnIndex < 5 || e.ColumnIndex > 7)
        return;
      DataGridViewRow row = dataGridView.Rows[e.RowIndex];
      if (!((DataGridViewDisableButtonCell) row.Cells[e.ColumnIndex]).Enabled)
        return;
      DataRow study = clsData.GetStudy(row.Cells[2].Value.ToString());
      if (study == null || study["Analysis"].ToString() == "Analyzing")
        return;
      this.Hide();
      try
      {
        clsUtill.StartProgress("open Study...", (Form) this);
        clsHDMFLib.OpenStudy(study["Name"].ToString());
        clsUtill.EndProgress();
        if (string.IsNullOrEmpty(study["Sequence"].ToString()))
          this.UpdateStudy(study);
        bool flag = false;
        switch (e.ColumnIndex)
        {
          case 5:
            frmProcess frmProcess = new frmProcess();
            frmProcess.m_drStudy = study;
            frmProcess.Icon = this.Icon;
            if (frmProcess.ShowDialog((IWin32Window) this) == DialogResult.OK)
            {
              clsUtill.StartProgress("save Study...", (Form) this);
              clsHDMFLib.SaveStudy();
              clsUtill.EndProgress();
              flag = true;
              break;
            }
            break;
          case 6:
            frmCool frmCool = new frmCool();
            frmCool.m_drStudy = study;
            frmCool.Icon = this.Icon;
            if (frmCool.ShowDialog((IWin32Window) this) == DialogResult.OK)
            {
              clsUtill.StartProgress("save Study...", (Form) this);
              clsHDMFLib.SaveStudy();
              clsUtill.EndProgress();
              break;
            }
            break;
          case 7:
            frmRunner frmRunner = new frmRunner();
            frmRunner.m_drStudy = study;
            frmRunner.Icon = this.Icon;
            if (frmRunner.ShowDialog((IWin32Window) this) == DialogResult.OK)
            {
              clsUtill.StartProgress("save Study...", (Form) this);
              clsHDMFLib.SaveStudy();
              clsUtill.EndProgress();
              study["Status"] = (object) clsDefine.Status.READY;
              flag = true;
              break;
            }
            break;
        }
        if (flag)
          this.RefreshStudyTab();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]dataGridView_Study_CellClick):" + ex.Message));
      }
      clsUtill.ShowForm((Form) this);
    }

    private void dataGridView_AI_Study_CellClick(object sender, DataGridViewCellEventArgs e)
    {
      DataGridView dataGridView = sender as DataGridView;
      if (e.RowIndex == -1 || e.ColumnIndex < 3 || e.ColumnIndex > 5)
        return;
      DataGridViewRow row = dataGridView.Rows[e.RowIndex];
      if (!((DataGridViewDisableButtonCell) row.Cells[e.ColumnIndex]).Enabled)
        return;
      DataRow study = clsData.GetStudy(row.Cells[2].Value.ToString());
      if (study == null)
        return;
      this.Hide();
      try
      {
        clsUtill.StartProgress("open Study...", (Form) this);
        string p_strStudyName = study["Name"].ToString();
        if (p_strStudyName.Contains("_Tetra"))
          p_strStudyName = p_strStudyName.Replace("_Tetra", "");
        clsHDMFLib.OpenStudy(p_strStudyName);
        clsUtill.EndProgress();
        bool flag = false;
        if (e.ColumnIndex == 3)
        {
          frmInput frmInput = new frmInput();
          frmInput.m_drStudy = study;
          frmInput.Icon = this.Icon;
          if (frmInput.ShowDialog((IWin32Window) this) == DialogResult.OK)
          {
            clsUtill.StartProgress("save Study...", (Form) this);
            clsHDMFLib.SaveStudy();
            clsUtill.EndProgress();
            flag = true;
          }
        }
        if (flag)
          this.RefreshAIStudyTab();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]dataGridView_AI_Study_CellClick):" + ex.Message));
      }
      clsUtill.ShowForm((Form) this);
    }

    public void ApplyProcessDBToStudy(DataRow p_drProcessDB)
    {
      DataRow p_drMaterial = (DataRow) null;
      try
      {
        if (p_drProcessDB["Mat_UDB"] == DBNull.Value || p_drProcessDB["Mat_UDB"].ToString() == "")
        {
          DataRow[] array = clsDefine.g_dsMaterial.Tables["System"].AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Manufacturer"].ToString() == p_drProcessDB["Mat_Manufacturer"].ToString() && Temp["TradeName"].ToString() == p_drProcessDB["Mat_TradeName"].ToString())).ToArray<DataRow>();
          for (int index = 0; index < array.Length; ++index)
          {
            if (clsHDMFLib.CheckSuji(array[index], array[index]["Manufacturer"].ToString() + ".21000.udb"))
            {
              p_drMaterial = array[index];
              break;
            }
          }
        }
        else
        {
          FileInfo fiUserUDB = new FileInfo(clsDefine.g_diProcessUDBCfg.ToString() + "\\" + p_drProcessDB["Mat_UDB"] + ".21000.udb");
          if (fiUserUDB.Exists)
          {
            if (!((IEnumerable<FileInfo>) clsDefine.g_diUserUDB.GetFiles("*.21000.udb")).Any<FileInfo>((System.Func<FileInfo, bool>) (Temp => Temp.Name == fiUserUDB.Name)))
            {
              fiUserUDB.CopyTo(clsDefine.g_diUserUDB.FullName + "\\" + fiUserUDB.Name);
              DataTable table = clsDefine.g_dsMaterial.Tables.Cast<DataTable>().Where<DataTable>((System.Func<DataTable, bool>) (Temp => Temp.TableName == "User")).FirstOrDefault<DataTable>();
              if (table != null)
                clsDefine.g_dsMaterial.Tables.Remove(table);
              DataTable materialsFromName = clsHDMFLib.GetUserMaterialsFromName(p_drProcessDB["Mat_UDB"].ToString());
              if (materialsFromName != null)
                clsDefine.g_dsMaterial.Tables.Add(materialsFromName);
            }
            DataRow[] array = clsDefine.g_dsMaterial.Tables["User"].AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Manufacturer"].ToString() == p_drProcessDB["Mat_Manufacturer"].ToString() && Temp["TradeName"].ToString() == p_drProcessDB["Mat_TradeName"].ToString())).ToArray<DataRow>();
            for (int index = 0; index < array.Length; ++index)
            {
              if (clsHDMFLib.CheckSuji(array[index], fiUserUDB.Name))
              {
                p_drMaterial = array[index];
                break;
              }
            }
          }
        }
        clsHDMFLib.SelectSuji(p_drMaterial);
        clsHDMFLib.SetProcessSet(p_drProcessDB);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]ApplyProcessDB):" + ex.Message));
      }
    }

    private DataRow[] GetSelectedProductStudy()
    {
      List<DataRow> dataRowList = new List<DataRow>();
      try
      {
        if (this.treeView_Product.Nodes.Count > 0)
        {
          TreeNode selectedNode = this.treeView_Product.SelectedNode;
          if (selectedNode != null)
          {
            foreach (DataTable table in (InternalDataCollectionBase) clsDefine.g_dsProduct.Tables)
            {
              if (selectedNode.Level != 1 || !(selectedNode.Text != table.TableName))
              {
                foreach (DataRow dataRow in table.AsEnumerable())
                {
                  if (dataRow["Status"] != DBNull.Value && !(dataRow["Status"].ToString() != clsDefine.Status.COMPLETED.ToString()))
                    dataRowList.Add(dataRow);
                }
              }
            }
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]GetSelectedProductStudy):" + ex.Message));
      }
      return dataRowList.ToArray();
    }

    private DataRow[] GetSelectedProductStudyForAI()
    {
      List<DataRow> dataRowList = new List<DataRow>();
      try
      {
        if (this.treeView_Product.Nodes.Count > 0)
        {
          TreeNode selectedNode = this.treeView_Product.SelectedNode;
          if (selectedNode != null)
          {
            foreach (DataTable table in (InternalDataCollectionBase) clsDefine.g_dsProduct.Tables)
            {
              if (selectedNode.Level != 1 || !(selectedNode.Text != table.TableName))
              {
                foreach (DataRow dataRow in table.AsEnumerable())
                {
                  if (dataRow["AIData"] != DBNull.Value)
                    dataRowList.Add(dataRow);
                }
              }
            }
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]GetSelectedProductStudyForAI):" + ex.Message));
      }
      return dataRowList.ToArray();
    }

    private DataRow GetStudyFromName(string p_strStudy)
    {
      DataRow studyFromName = (DataRow) null;
      try
      {
        foreach (DataTable table in (InternalDataCollectionBase) clsDefine.g_dsProduct.Tables)
        {
          foreach (DataRow dataRow in table.AsEnumerable())
          {
            if (dataRow["Name"].ToString() == p_strStudy)
            {
              studyFromName = dataRow;
              break;
            }
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]GetStudyFromName):" + ex.Message));
      }
      return studyFromName;
    }

    private void tabControl_Main_SelectedIndexChanged(object sender, EventArgs e)
    {
      if (this.tabControl_Main.SelectedTab == this.tabPage_Study)
        this.RefreshStudyTab();
      else if (this.tabControl_Main.SelectedTab == this.tabPage_Report)
        this.RefreshReportTab();
      else if (this.tabControl_Main.SelectedTab == this.tabPage_PastCases)
        this.RefreshPastCasesTab();
      else if (this.tabControl_Main.SelectedTab == this.tabPage_Comparison)
        this.RefreshComparisonTab();
      else if (this.tabControl_Main.SelectedTab == this.tabPage_AI_Study)
      {
        this.RefreshAIStudyTab();
      }
      else
      {
        if (this.tabControl_Main.SelectedTab != this.tabPage_AI_Result)
          return;
        this.RefreshAIResultTab();
      }
    }

    private void UpdateCase(string p_strMesh, DataGridViewRow p_dgvrStudy, bool p_isAutoAnalysis = true)
    {
      List<string[]> p_lst_arr_strData = new List<string[]>();
      List<string[]> strArrayList = new List<string[]>();
      StringBuilder stringBuilder = new StringBuilder();
      DataGridView dataGridView = (DataGridView) null;
      DataRow drStudy = (DataRow) null;
      List<DataRow> dataRowList1 = new List<DataRow>();
      List<DataRow> dataRowList2 = new List<DataRow>();
      if (clsDefine.enumLicLevel == clsDefine.LicLevel.Light)
        return;
      dataGridView = !p_strMesh.Equals("Dual") ? this.dataGridView_3D : this.dataGridView_Dual;
      if (!(bool) p_dgvrStudy.Cells["Column_" + p_strMesh + "_Check"].EditedFormattedValue)
        return;
      drStudy = clsData.GetStudy(p_dgvrStudy.Cells["Column_" + p_strMesh + "_Study"].Value.ToString());
      if (drStudy == null)
        return;
      this.Hide();
      clsUtill.StartProgress("copy Study for Case...", (Form) this);
      if (drStudy["Status"].ToString() == clsDefine.Status.NOT_READY.ToString())
        p_isAutoAnalysis = false;
      try
      {
        for (int index = 0; index < clsDefine.g_dtCase.Rows.Count; ++index)
        {
          DataRow row = clsDefine.g_dtCase.Rows[index];
          int iRev = 1;
          while (true)
          {
            if (drStudy.Table.AsEnumerable().Any<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == drStudy["Name"].ToString() + "_Case" + (object) iRev)))
              iRev++;
            else
              break;
          }
          string p_strStudyNameDest = drStudy["Name"].ToString() + "_Case" + (object) iRev;
          clsUtill.UpdateProgress("copy Study for Case(" + (object) (index + 1) + "/" + (object) clsDefine.g_dtCase.Rows.Count + ") => Case" + (object) (index + 1) + "...");
          clsHDMFLib.CopyStudy(drStudy["Name"].ToString(), p_strStudyNameDest, drStudy.Table.TableName + "_" + p_strMesh, true, false);
          clsUtill.UpdateProgress("setting Study for Case(" + (object) (index + 1) + "/" + (object) clsDefine.g_dtCase.Rows.Count + ") => Case" + (object) (index + 1) + "...");
          string[] strArray1 = row["FC"].ToString().Split('|');
          double p_dblValue1 = clsUtill.ConvertToDouble(strArray1[1]);
          clsHDMFLib.SetFillingControlDataForCase(strArray1[0], p_dblValue1);
          string[] strArray2 = row["VP"].ToString().Split('|');
          double p_dblValue2 = clsUtill.ConvertToDouble(strArray2[1]);
          clsHDMFLib.SetVPSwitchDataForCase(strArray2[0], p_dblValue2);
          p_lst_arr_strData.Clear();
          string[] strArray3 = row["Packing"].ToString().Split('/');
          string[] strArray4 = strArray3[0].Split('|');
          if (strArray4[0] == "")
            strArray4[0] = "0";
          if (strArray4[1] == "")
            strArray4[1] = "0";
          p_lst_arr_strData.Add(new string[2]
          {
            "0",
            strArray4[1]
          });
          p_lst_arr_strData.Add(new string[2]
          {
            strArray4[0],
            strArray4[1]
          });
          string[] strArray5 = strArray3[1].Split('|');
          if (strArray5[0] == "")
            strArray5[0] = "0";
          if (strArray5[1] == "")
            strArray5[1] = "0";
          p_lst_arr_strData.Add(new string[2]
          {
            "0",
            strArray5[1]
          });
          p_lst_arr_strData.Add(new string[2]
          {
            strArray5[0],
            strArray5[1]
          });
          string[] strArray6 = strArray3[2].Split('|');
          if (strArray6[0] == "")
            strArray6[0] = "0";
          if (strArray6[1] == "")
            strArray6[1] = "0";
          p_lst_arr_strData.Add(new string[2]
          {
            "0",
            strArray6[1]
          });
          p_lst_arr_strData.Add(new string[2]
          {
            strArray6[0],
            strArray6[1]
          });
          if (p_lst_arr_strData.Count > 0)
            clsHDMFLib.SetPackHoldingControlDataForCase(p_lst_arr_strData, 0);
          string[] strArray7 = row["Cooling"].ToString().Split('|');
          double p_dblValue3 = clsUtill.ConvertToDouble(strArray7[1]);
          clsHDMFLib.SetCoolingControlDataForCase(strArray7[0], p_dblValue3);
          clsHDMFLib.SetMeltTemperatureDataToProcessSet(clsUtill.ConvertToDouble(row["Melt"].ToString()));
          clsHDMFLib.SetMoldTemperatureDataToProcessSet(clsUtill.ConvertToDouble(row["Mold"].ToString()));
          DataRow dataRow = drStudy.Table.Rows.Add();
          dataRow["Name"] = (object) p_strStudyNameDest;
          dataRow["Mesh"] = (object) p_strMesh;
          dataRow["Check"] = (object) DBNull.Value;
          dataRow["Sequence"] = drStudy["Sequence"];
          dataRow["Status"] = (object) clsDefine.Status.READY;
          dataRow["JobID"] = (object) DBNull.Value;
          dataRow["Analysis"] = (object) DBNull.Value;
          clsUtill.UpdateProgress("save Study for Case(" + (object) (index + 1) + "/" + (object) clsDefine.g_dtCase.Rows.Count + ") => Case" + (object) (index + 1) + "...");
          clsHDMFLib.SaveStudy();
          if (p_isAutoAnalysis)
            dataRowList2.Add(dataRow);
        }
        this.RefreshStudyTab();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]UpdateCase):" + ex.Message));
      }
      clsUtill.EndProgress();
      clsUtill.ShowForm((Form) this);
      if (dataRowList2.Count <= 0)
        return;
      this.ExecuteAnalysis(dataRowList2.ToArray());
    }

    private void UpdateMesh()
    {
      bool flag1 = false;
      bool flag2 = false;
      bool flag3 = false;
      bool flag4 = true;
      bool flag5 = true;
      string str1 = "";
      double[] p_arr_dblCenterMove = new double[3];
      DataRow dr3DStudy = (DataRow) null;
      DataRow drStudy = (DataRow) null;
      List<string> stringList = new List<string>();
      List<string> p_lst_strDualMeshData = new List<string>();
      List<string[]> strArrayList = new List<string[]>();
      this.Hide();
      try
      {
        if (this.label_Dual.Text != "It's OK")
          flag4 = false;
        if (this.label_3D.Text != "It's OK")
          flag5 = false;
        if ((!flag4 || !flag5 || !clsDefine.g_isCreate3D) && clsUtill.ShowMessageBox((Form) this, LocaleControl.getInstance().GetString("IDS_CHECK_CREATE_3D_MESH"), "Update Mesh", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
          flag1 = true;
        DataTable source = clsDefine.g_dsProduct.Tables.Cast<DataTable>().Where<DataTable>((System.Func<DataTable, bool>) (Temp => Temp.TableName == this.treeView_Product.SelectedNode.Text)).FirstOrDefault<DataTable>();
        if (source == null)
          return;
        if (!flag4)
        {
          drStudy = source.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Mesh"].ToString() == "Dual" && Temp["Check"] != DBNull.Value)).FirstOrDefault<DataRow>();
          if (drStudy != null)
          {
            clsUtill.StartProgress("update Dual Mesh => open Study...", (Form) this);
            clsHDMFLib.OpenStudy(drStudy["Name"].ToString());
            clsUtill.UpdateProgress("update Dual Mesh => check Mesh...");
            bool flag6 = clsHDMFLib.ExistDualMesh();
            clsUtill.EndProgress();
            if (!flag6)
              flag2 = true;
            else if (new frmDualMesh()
            {
              m_drStudy = drStudy
            }.ShowDialog((IWin32Window) this) == DialogResult.OK)
              flag2 = true;
            if (flag2)
            {
              clsMesh.ShowUpdateMesh(new DataRow[1]
              {
                drStudy
              }, (Form) this, "Dual");
              clsMesh.UpdateDualMesh(new DataRow[1]
              {
                drStudy
              });
              clsMesh.CloseUpdateMesh();
              if (clsHDMFLib.ExistDualMesh())
              {
                str1 = clsHDMFLib.GetGlobalEdgeLengthFromLog().ToString();
                clsUtill.StartProgress("update Dual Mesh => apply ProcessDB...", (Form) this);
                DataRow p_drProcessDB = (DataRow) null;
                if (clsDefine.g_dtProcessDB.Rows.Count > 0)
                {
                  DataRow[] array = clsDefine.g_dtProcessDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp =>
                  {
                    if (!(Temp["Name"].ToString().Split('_')[0].ToUpper() == drStudy["Name"].ToString().Split('_')[0].ToUpper()))
                      return false;
                    return Temp["Name"].ToString().Split('_')[1].ToUpper() == drStudy["Name"].ToString().Split('_')[1].ToUpper();
                  })).ToArray<DataRow>();
                  if (array.Length != 0)
                    p_drProcessDB = ((IEnumerable<DataRow>) array).Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString().ToUpper().Contains("_DEFAULT"))).FirstOrDefault<DataRow>() ?? array[0];
                }
                if (p_drProcessDB != null)
                {
                  this.ApplyProcessDBToStudy(p_drProcessDB);
                  drStudy["Sequence"] = (object) clsHDMFLib.GetAnalysisSequence();
                }
                clsUtill.UpdateProgress("update Dual Mesh => verify Dual Mesh...");
                p_lst_strDualMeshData.AddRange((IEnumerable<string>) clsHDMFLib.GetDualDomainMeshData());
                double p_dblAspectRatio = clsUtill.ConvertToDouble(clsDefine.g_dicMeshStat["Value"]);
                drStudy["Check"] = (object) clsHDMFLib.VerificationDualMesh(p_lst_strDualMeshData, p_dblAspectRatio);
                if (clsDefine.g_isMoveZero)
                  p_arr_dblCenterMove = clsHDMFLib.MoveCenterPointToZero();
                double[] modelCenterPoint = clsHDMFLib.GetModelCenterPoint();
                double[] modelLengths = clsHDMFLib.GetModelLengths();
                clsHDMFLib.WriteCenterPointToStudyNote(modelCenterPoint);
                clsHDMFLib.WriteCenterMoveToStudyNote(p_arr_dblCenterMove);
                clsHDMFLib.WriteModelLengthsToStudyNote(modelLengths);
                strArrayList.Add(p_lst_strDualMeshData.ToArray());
                clsUtill.EndProgress();
                flag4 = Convert.ToBoolean(drStudy["Check"]);
              }
            }
            clsUtill.StartProgress("update Dual Mesh => save Study...", (Form) this);
            clsHDMFLib.SaveStudy();
            clsUtill.EndProgress();
          }
        }
        if (flag4 & flag1)
        {
          DataRow dataRow = source.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Mesh"].ToString() == "Dual" && Temp["Check"] != DBNull.Value)).FirstOrDefault<DataRow>();
          clsUtill.StartProgress("update 3D Mesh =>check Connectivity Regions...", (Form) this);
          List<string> dualDomainMeshData = clsHDMFLib.GetDualDomainMeshData(dataRow["Name"].ToString());
          string str2 = clsHDMFLib.GetGlobalEdgeLengthFromLog().ToString();
          clsHDMFLib.CloseStudy();
          bool flag7 = false;
          if (clsUtill.ConvertToDouble(dualDomainMeshData.Last<string>()) >= 2.0)
          {
            clsUtill.HideProgress();
            DialogResult dialogResult = clsUtill.ShowMessageBox((Form) this, LocaleControl.getInstance().GetString("IDS_CHECK_CONNECTIVITY_REGIONS"), "create 3D Mesh", MessageBoxButtons.YesNo, MessageBoxIcon.Asterisk);
            clsUtill.ShowProgress();
            if (dialogResult == DialogResult.Yes)
              flag7 = true;
          }
          else
            flag7 = true;
          clsUtill.EndProgress();
          if (flag7)
          {
            clsUtill.StartProgress("update 3D Mesh =>copy Study...", (Form) this);
            flag3 = clsHDMFLib.CopyStudy(dataRow["Name"].ToString(), dataRow["Name"].ToString() + "_Tetra", "", true, false);
            clsUtill.EndProgress();
            dr3DStudy = dataRow.Table.Rows.Add();
            dr3DStudy["Name"] = (object) (dataRow["Name"].ToString() + "_Tetra");
            dr3DStudy["Mesh"] = (object) "3D";
            dr3DStudy["Check"] = (object) false;
            dr3DStudy["Sequence"] = dataRow["Sequence"];
            dr3DStudy["Summary"] = (object) DBNull.Value;
            dr3DStudy["Status"] = (object) DBNull.Value;
            dr3DStudy["JobID"] = (object) DBNull.Value;
            dr3DStudy["Analysis"] = (object) DBNull.Value;
            clsMesh.ShowUpdateMesh(new DataRow[1]
            {
              dr3DStudy
            }, (Form) this, "3D");
            clsMesh.Update3DMesh(new DataRow[1]{ dr3DStudy }, new string[1]
            {
              str2
            });
            clsMesh.CloseUpdateMesh();
            clsUtill.StartProgress("update 3D Mesh => check Study...", (Form) this);
            if (!Convert.ToBoolean(dr3DStudy["Check"]))
            {
              clsUtill.UpdateProgress("update 3D Mesh => delete Study...");
              clsHDMFLib.DeleteStudy(dr3DStudy["Name"].ToString());
              dr3DStudy.Table.Rows.Remove(dr3DStudy);
            }
            else
            {
              clsUtill.UpdateProgress("update 3D Mesh => open Study...");
              clsHDMFLib.OpenStudy(dr3DStudy["Name"].ToString());
              clsUtill.UpdateProgress("update 3D Mesh => apply ProcessDB...");
              DataRow p_drProcessDB = (DataRow) null;
              if (clsDefine.g_dtProcessDB.Rows.Count > 0)
              {
                DataRow[] array = clsDefine.g_dtProcessDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp =>
                {
                  if (!(Temp["Name"].ToString().Split('_')[0].ToUpper() == dr3DStudy["Name"].ToString().Split('_')[0].ToUpper()))
                    return false;
                  return Temp["Name"].ToString().Split('_')[1].ToUpper() == dr3DStudy["Name"].ToString().Split('_')[1].ToUpper();
                })).ToArray<DataRow>();
                if (array.Length != 0)
                  p_drProcessDB = ((IEnumerable<DataRow>) array).Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString().ToUpper().Contains("_DEFAULT"))).FirstOrDefault<DataRow>() ?? array[0];
              }
              if (p_drProcessDB != null)
              {
                this.ApplyProcessDBToStudy(p_drProcessDB);
                dr3DStudy["Sequence"] = (object) clsHDMFLib.GetAnalysisSequence();
              }
              clsHDMFLib.SaveStudy();
              clsHDMFLib.CloseStudy();
            }
            clsUtill.EndProgress();
          }
        }
        this.RefreshStudyTab();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]UpdateMesh):" + ex.Message));
      }
      clsUtill.ShowForm((Form) this);
    }

    private void ExecuteAnalysis()
    {
      string str = "";
      List<JToken> jtokenList1 = new List<JToken>();
      List<JToken> jtokenList2 = new List<JToken>();
      if (this.treeView_Product.Nodes.Count == 0 || this.treeView_Product.SelectedNode == null)
        return;
      List<DataRow> dataRowList = new List<DataRow>();
      foreach (DataGridViewRow row in (IEnumerable) this.dataGridView_Dual.Rows)
      {
        if ((bool) row.Cells["Column_Dual_Check"].FormattedValue && !(row.Cells["Column_Dual_Status"].Value.ToString() != clsDefine.Status.READY.ToString()))
          dataRowList.Add(clsData.GetStudy(row.Cells["Column_Dual_Study"].Value.ToString()));
      }
      foreach (DataGridViewRow row in (IEnumerable) this.dataGridView_3D.Rows)
      {
        if ((bool) row.Cells["Column_3D_Check"].FormattedValue && !(row.Cells["Column_3D_Status"].Value.ToString() != clsDefine.Status.READY.ToString()))
          dataRowList.Add(clsData.GetStudy(row.Cells["Column_3D_Study"].Value.ToString()));
      }
      if (dataRowList.Count == 0)
        return;
      this.Hide();
      clsUtill.StartProgress("start Analysis...", (Form) this);
      try
      {
        for (int index = 0; index < dataRowList.Count; ++index)
        {
          clsUtill.UpdateProgress("start Analysis(" + (object) (index + 1) + "/" + (object) dataRowList.Count + ")");
          if (clsHDMFLib.OpenStudy(dataRowList[index]["Name"].ToString()))
          {
            if (dataRowList[index]["Mesh"].ToString() == "3D")
              clsHDMFLib.SetMidResult(clsDefine.g_dicMidResult);
            dataRowList[index]["Analysis"] = (object) DBNull.Value;
            if (dataRowList[index]["Mesh"].ToString() == "Dual")
              clsHDMFLib.ExecuteOrientAll();
            clsHDMFLib.ExecutePurgeNodes();
            clsHDMFLib.ExecuteGlobalMerge();
            List<JToken> jobData1 = clsSCM.GetJobData();
            if (clsHDMFLib.RunAnalysis(""))
            {
              str = clsHDMFLib.GetStudyName();
              JToken jtoken = (JToken) null;
              List<JToken> jobData2;
              while (true)
              {
                jobData2 = clsSCM.GetJobData();
                if (jobData2.Count != 0)
                {
                  if (jobData1.Count == jobData2.Count)
                    Thread.Sleep(10);
                  else
                    break;
                }
                else
                  goto label_33;
              }
              jtoken = clsSCM.GetLastDataFromTwoList(jobData1, jobData2);
label_33:
              if (jtoken != null)
              {
                dataRowList[index]["JobID"] = (object) jtoken[(object) "payload"][(object) "xref"];
                dataRowList[index]["Analysis"] = (object) "Analyzing";
                new Thread(new ParameterizedThreadStart(this.Thread_Monitorig))
                {
                  IsBackground = true
                }.Start((object) dataRowList[index]);
              }
            }
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]ExecuteAnalysis):" + ex.Message));
      }
      clsUtill.EndProgress();
      clsUtill.ShowForm((Form) this);
      this.RefreshStudyTab();
    }

    private void ExecuteAnalysis(DataRow[] p_drStudy)
    {
      string str = "";
      List<JToken> jtokenList1 = new List<JToken>();
      List<JToken> jtokenList2 = new List<JToken>();
      if (p_drStudy.Length == 0)
        return;
      this.Hide();
      clsUtill.StartProgress("start Analysis...", (Form) this);
      try
      {
        for (int index = 0; index < p_drStudy.Length; ++index)
        {
          clsUtill.UpdateProgress("start Analysis(" + (object) (index + 1) + "/" + (object) p_drStudy.Length + ")");
          if (clsHDMFLib.OpenStudy(p_drStudy[index]["Name"].ToString()))
          {
            if (p_drStudy[index]["Mesh"].ToString() == "3D")
              clsHDMFLib.SetMidResult(clsDefine.g_dicMidResult);
            p_drStudy[index]["Analysis"] = (object) DBNull.Value;
            List<JToken> jobData1 = clsSCM.GetJobData();
            if (clsHDMFLib.RunAnalysis(""))
            {
              str = clsHDMFLib.GetStudyName();
              List<JToken> jobData2;
              while (true)
              {
                jobData2 = clsSCM.GetJobData();
                if (jobData1.Count == jobData2.Count)
                  Thread.Sleep(10);
                else
                  break;
              }
              JToken lastDataFromTwoList = clsSCM.GetLastDataFromTwoList(jobData1, jobData2);
              p_drStudy[index]["JobID"] = (object) lastDataFromTwoList[(object) "payload"][(object) "xref"];
              p_drStudy[index]["Analysis"] = (object) "Analyzing";
              new Thread(new ParameterizedThreadStart(this.Thread_Monitorig))
              {
                IsBackground = true
              }.Start((object) p_drStudy[index]);
            }
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]ExecuteAnalysis):" + ex.Message));
      }
      clsUtill.EndProgress();
      clsUtill.ShowForm((Form) this);
      this.RefreshStudyTab();
    }

    private void Thread_Monitorig(object p_objStudy)
    {
      bool isStop = false;
      while (true)
      {
        this.InvokeIFNeeded((Action) (() => isStop = this.UpdateMonitoring((DataRow) p_objStudy)));
        if (!isStop)
          Thread.Sleep(1000);
        else
          break;
      }
    }

    private bool UpdateMonitoring(DataRow p_drStudy)
    {
      bool flag = false;
      try
      {
        string jobStatus = clsSCM.GetJobStatus(p_drStudy["JobID"].ToString());
        if (jobStatus == "")
          return flag;
        if (jobStatus.Contains("FAILED") || jobStatus.Contains("CANCELED") || jobStatus.Contains("COMPLETE"))
        {
          p_drStudy["JobID"] = (object) DBNull.Value;
          if (jobStatus.Contains("COMPLETE"))
            p_drStudy["Status"] = (object) clsDefine.Status.COMPLETED;
          p_drStudy["Analysis"] = (object) jobStatus;
          DataGridViewRow dataGridViewRow = this.dataGridView_Monitor.Rows.Cast<DataGridViewRow>().Where<DataGridViewRow>((System.Func<DataGridViewRow, bool>) (Temp => Temp.Cells["Column_Monitor_Study"].Value.ToString() == p_drStudy["Name"].ToString())).FirstOrDefault<DataGridViewRow>();
          if (dataGridViewRow != null)
            this.dataGridView_Monitor.Rows.Remove(dataGridViewRow);
          flag = true;
        }
        else
        {
          DataGridViewRow dataGridViewRow = this.dataGridView_Monitor.Rows.Cast<DataGridViewRow>().Where<DataGridViewRow>((System.Func<DataGridViewRow, bool>) (Temp => Temp.Cells["Column_Monitor_Study"].Value.ToString() == p_drStudy["Name"].ToString())).FirstOrDefault<DataGridViewRow>();
          if (dataGridViewRow == null)
          {
            dataGridViewRow = this.dataGridView_Monitor.Rows[this.dataGridView_Monitor.Rows.Add()];
            dataGridViewRow.Cells["Column_Monitor_Product"].Value = (object) p_drStudy.Table.TableName;
            dataGridViewRow.Cells["Column_Monitor_Study"].Value = p_drStudy["Name"];
            dataGridViewRow.Cells["Column_Monitor_Mesh"].Value = p_drStudy["Mesh"];
            dataGridViewRow.Cells["Column_Monitor_Sequence"].Value = (object) p_drStudy["Sequence"].ToString().Replace("|", " + ");
          }
          dataGridViewRow.Cells["Column_Monitor_Status"].Value = (object) jobStatus;
        }
        if (flag)
        {
          lock (this.m_Lock)
          {
            if (p_drStudy["Status"].ToString() == clsDefine.Status.COMPLETED.ToString())
            {
              clsHDMFLib.OpenStudy(p_drStudy["Name"].ToString());
              bool p_isExistOutFile = clsHDMFLib.ExistOutFile(clsDefine.g_fiProject.DirectoryName, p_drStudy["FName"].ToString());
              p_drStudy["Summary"] = (object) JsonConvert.SerializeObject((object) clsSummary.GetStudySummaryData(p_drStudy, clsDefine.g_fiProject, p_isExistOutFile));
              if (clsDefine.enumLicLevel > clsDefine.LicLevel.Standard)
                clsSummary.ExportSummary(new DataRow[1]
                {
                  p_drStudy
                }, clsDefine.g_strLanguageType);
            }
            if (this.tabControl_Main.SelectedTab == this.tabPage_Study)
              this.RefreshStudyTab();
            else if (this.tabControl_Main.SelectedTab == this.tabPage_Report)
              this.RefreshReportTab();
            else if (this.tabControl_Main.SelectedTab == this.tabPage_Comparison)
              this.RefreshComparisonTab();
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]UpdateMonitoring):" + ex.Message));
      }
      return flag;
    }

    private void ExportReport(string p_strReportType)
    {
      List<DataRow> p_lst_drStudy = new List<DataRow>();
      try
      {
        if (clsDefine.enumLicLevel == clsDefine.LicLevel.Light)
        {
          int num = (int) clsUtill.ShowMessageBox((Form) this, LocaleControl.getInstance().GetString("IDS_CANT_NOT_BE_USED") + " -> " + LocaleControl.getInstance().GetString("IDS_CHECK_LICENSE"), "HDMoldFlow", p_msgIcon: MessageBoxIcon.Exclamation);
        }
        else
        {
          foreach (Control control in (ArrangedElementCollection) (!p_strReportType.Contains("Past") ? (Control) this.panel_Report : (Control) this.panel_PastCases).Controls)
          {
            if ((control.Controls["cbReport"] as CheckBox).Checked)
              p_lst_drStudy.Add(this.GetStudyFromName((control.Controls["ntbStudy"] as NewTextBox).Value));
          }
          if (p_lst_drStudy.Count == 0)
            return;
          this.Hide();
          clsUtill.StartProgress("exporting Report...", (Form) this);
          clsReportDefine.g_diProject = clsDefine.g_diProject;
          clsReport.ExportReport(p_lst_drStudy, clsDefine.g_strLanguageType, clsDefine.g_enumCompany);
          clsUtill.EndProgress();
          clsUtill.ShowForm((Form) this);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]ExportReport):" + ex.Message));
      }
    }

    private void ExecuteAISolution()
    {
      string empty = string.Empty;
      StringBuilder stringBuilder = new StringBuilder();
      List<JToken> jtokenList1 = new List<JToken>();
      List<JToken> jtokenList2 = new List<JToken>();
      if (this.treeView_Product.Nodes.Count == 0 || this.treeView_Product.SelectedNode == null)
        return;
      List<DataRow> dataRowList = new List<DataRow>();
      foreach (DataGridViewRow row in (IEnumerable) this.dataGridView_AI_Dual.Rows)
      {
        if ((bool) row.Cells["Column_AI_Dual_Check"].FormattedValue)
          dataRowList.Add(clsData.GetStudy(row.Cells["Column_AI_Dual_Study"].Value.ToString()));
      }
      foreach (DataGridViewRow row in (IEnumerable) this.dataGridView_AI_3D.Rows)
      {
        if ((bool) row.Cells["Column_AI_3D_Check"].FormattedValue)
          dataRowList.Add(clsData.GetStudy(row.Cells["Column_AI_3D_Study"].Value.ToString()));
      }
      if (dataRowList.Count == 0)
        return;
      this.Hide();
      clsUtill.StartProgress("Send Input Data...", (Form) this);
      try
      {
        for (int index = 0; index < dataRowList.Count; ++index)
        {
          clsUtill.UpdateProgress("Send Input Data(" + (object) (index + 1) + "/" + (object) dataRowList.Count + ")");
          string web = clsInputData.SendInputDataToWeb(dataRowList[index]["Name"].ToString());
          if (!string.IsNullOrEmpty(web))
          {
            HDLog.Instance().Error((object) ("Exception([frmMain]SendInputDataToWeb):" + web));
            stringBuilder.Append(dataRowList[index]["Name"].ToString() + "is failed" + Environment.NewLine);
          }
          else
            new Thread(new ParameterizedThreadStart(this.Thread_AIMonitoring))
            {
              IsBackground = true
            }.Start((object) dataRowList[index]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]ExecuteAISolution):" + ex.Message));
      }
      clsUtill.EndProgress();
      clsUtill.ShowForm((Form) this);
      this.RefreshAIStudyTab();
      if (stringBuilder.Length == 0)
        return;
      int num = (int) clsUtill.ShowMessageBox((Form) this, stringBuilder.ToString(), "Execute AI Error", p_msgIcon: MessageBoxIcon.Hand);
    }

    private void Thread_AIMonitoring(object p_objStudy)
    {
      bool isStop = false;
      while (true)
      {
        this.InvokeIFNeeded((Action) (() => isStop = this.UpdateAIMonitoring((DataRow) p_objStudy)));
        if (!isStop)
          Thread.Sleep(1000);
        else
          break;
      }
    }

    private bool UpdateAIMonitoring(DataRow p_drStudy)
    {
      bool flag = false;
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      try
      {
        string aiResult = clsAISolution.ReceiveAIResult(p_drStudy, clsDefine.g_diProject, ref empty1);
        if (aiResult != string.Empty)
        {
          int num = (int) clsUtill.ShowMessageBox((Form) this, "[" + p_drStudy["Name"].ToString() + "] AISolution Error: " + aiResult, "AI Solution Error", p_msgIcon: MessageBoxIcon.Hand);
          return true;
        }
        if (empty1 == string.Empty)
          return flag;
        p_drStudy["AIData"] = (object) empty1;
        flag = true;
        if (flag)
        {
          lock (this.m_Lock)
          {
            if (this.tabControl_Main.SelectedTab == this.tabPage_AI_Result)
              this.RefreshAIResultTab();
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]UpdateMonitoring):" + ex.Message));
      }
      return flag;
    }

    private void ExecuteAIResult()
    {
      string strMesh = string.Empty;
      string empty = string.Empty;
      List<DataRow> lst_drStudy = new List<DataRow>();
      Dictionary<string, string> dictionary = new Dictionary<string, string>();
      foreach (Control control in (ArrangedElementCollection) this.panel_AIResult.Controls)
      {
        if ((control.Controls["cbAIResult"] as CheckBox).Checked)
          lst_drStudy.Add(this.GetStudyFromName((control.Controls["ntbStudy"] as NewTextBox).Value));
      }
      if (lst_drStudy.Count == 0)
        return;
      this.Hide();
      clsUtill.StartProgress("copy Study for AI Case...", (Form) this);
      try
      {
        for (int i = 0; i < lst_drStudy.Count; i++)
        {
          clsUtill.UpdateProgress("setting Study for AI Case(" + (object) (i + 1) + "/" + (object) lst_drStudy.Count + ") => Case" + (object) (i + 1) + "...");
          strMesh = lst_drStudy[i]["Mesh"].ToString();
          int iRev = 1;
          while (true)
          {
            if (lst_drStudy[i].Table.AsEnumerable().Any<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == lst_drStudy[i]["Name"].ToString() + "_" + strMesh + "_Rev" + (object) iRev)))
              iRev++;
            else
              break;
          }
          string p_strAIStudy = lst_drStudy[i]["Name"].ToString() + "_" + strMesh + "_Rev" + (object) iRev;
          string p_strSequence = lst_drStudy[i]["Sequence"].ToString();
          clsAISolution.CreateAICase(lst_drStudy[i], p_strAIStudy, clsDefine.g_dtProcessDB, clsDefine.g_dsMaterial, clsDefine.g_diProcessDBCfg.FullName, ref p_strSequence);
          DataRow dataRow = lst_drStudy[i].Table.Rows.Add();
          dataRow["Name"] = (object) p_strAIStudy;
          dataRow["Mesh"] = (object) strMesh;
          dataRow["Check"] = (object) DBNull.Value;
          dataRow["Sequence"] = (object) p_strSequence;
          dataRow["Status"] = (object) clsDefine.Status.READY;
          dataRow["JobID"] = (object) DBNull.Value;
          dataRow["Analysis"] = (object) DBNull.Value;
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]ExecuteAIResult):" + ex.Message));
      }
      clsUtill.EndProgress();
      clsUtill.ShowForm((Form) this);
    }

    private void RotateModel()
    {
      this.Hide();
      try
      {
        DialogResult dialogResult = clsUtill.ShowMessageBox((Form) this, LocaleControl.getInstance().GetString("IDS_CHECK_MOVECENTERPOINT"), this.Text, MessageBoxButtons.YesNo, MessageBoxIcon.Question);
        clsUtill.StartProgress("rotate Model...", (Form) this);
        clsHDMFLib.RotateModel(this.newComboBox_RotateAxis.SelectedIndex, clsUtill.ConvertToDouble(this.newTextBox_RotAng.Value));
        if (dialogResult == DialogResult.Yes)
          clsHDMFLib.MoveCenterPointToZero();
        double[] modelCenterPoint = clsHDMFLib.GetModelCenterPoint();
        double[] modelLengths = clsHDMFLib.GetModelLengths();
        clsHDMFLib.WriteCenterPointToStudyNote(modelCenterPoint);
        clsHDMFLib.WriteModelLengthsToStudyNote(modelLengths);
        clsUtill.EndProgress();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]RotateModel):" + ex.Message));
      }
      clsUtill.ShowForm((Form) this);
    }

    private void ExportCMPRReport()
    {
      List<DataRow> dataRowList = new List<DataRow>();
      try
      {
        if (clsDefine.enumLicLevel == clsDefine.LicLevel.Light)
        {
          int num = (int) clsUtill.ShowMessageBox((Form) this, LocaleControl.getInstance().GetString("IDS_CANT_NOT_BE_USED") + " -> " + LocaleControl.getInstance().GetString("IDS_CHECK_LICENSE"), "HDMoldFlow", p_msgIcon: MessageBoxIcon.Exclamation);
        }
        else
        {
          this.Hide();
          clsUtill.StartProgress("exporting Report...", (Form) this);
          clsReportDefine.g_diProject = clsDefine.g_diProject;
          clsReport.ExportReportForComparison(this.m_lst_dicComparison.OrderBy<KeyValuePair<string, DataRow>, string>((System.Func<KeyValuePair<string, DataRow>, string>) (pair => pair.Key)).Select<KeyValuePair<string, DataRow>, DataRow>((System.Func<KeyValuePair<string, DataRow>, DataRow>) (pair => pair.Value)).ToList<DataRow>(), clsDefine.g_strLanguageType);
          clsUtill.EndProgress();
          clsUtill.ShowForm((Form) this);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]ExportCMPRReport):" + ex.Message));
      }
    }

    private void AddPastCases()
    {
      bool flag = false;
      string strStudyFile = "";
      string strStudyName = "";
      List<string> stringList = new List<string>();
      FileInfo[] fileInfoArray = (FileInfo[]) null;
      if (this.treeView_Product.Nodes.Count == 0)
        return;
      OpenFileDialog openFileDialog = new OpenFileDialog();
      openFileDialog.Filter = "Autodesk Moldflow Study Files (*.sdy)|*sdy";
      try
      {
        if (openFileDialog.ShowDialog((IWin32Window) this) == DialogResult.OK)
        {
          strStudyFile = openFileDialog.FileName;
          DirectoryInfo directoryInfo = new DirectoryInfo(Path.GetDirectoryName(strStudyFile));
          strStudyName = clsHDMFLib.GetStudyNameFromProject(((IEnumerable<FileInfo>) directoryInfo.GetFiles()).Where<FileInfo>((System.Func<FileInfo, bool>) (Temp => Path.GetExtension(Temp.Name).ToLower().Equals(".mpi"))).FirstOrDefault<FileInfo>().FullName, Path.GetFileName(strStudyFile));
          if (this.GetStudyFromName(strStudyName) != null)
          {
            int num = (int) clsUtill.ShowMessageBox((Form) this, LocaleControl.getInstance().GetString("IDS_SAMENAME_STUDY"), this.Text, p_msgIcon: MessageBoxIcon.Hand);
            return;
          }
          fileInfoArray = ((IEnumerable<FileInfo>) directoryInfo.GetFiles()).Where<FileInfo>((System.Func<FileInfo, bool>) (Temp => Path.GetFileNameWithoutExtension(Temp.Name).Split('~')[0].Equals(Path.GetFileNameWithoutExtension(strStudyFile)) && Path.GetExtension(Temp.Name).ToLower().Equals(".out"))).ToArray<FileInfo>();
          if (fileInfoArray.Length == 0)
          {
            int num = (int) clsUtill.ShowMessageBox((Form) this, LocaleControl.getInstance().GetString("IDS_UNINTERPRETED_STUDY"), this.Text, p_msgIcon: MessageBoxIcon.Hand);
            return;
          }
          flag = true;
        }
        if (flag)
        {
          this.Hide();
          clsUtill.StartProgress("Import Past Cases...");
          DataTable table;
          if (!clsDefine.g_dsProduct.Tables.Contains("PAST_Cases"))
          {
            clsHDMFLib.CreateFolder("", "PAST_Cases");
            table = new DataTable();
            table.TableName = "PAST_Cases";
            table.Columns.Add("Name");
            table.Columns.Add("FName");
            table.Columns.Add("Mesh");
            table.Columns.Add("Check");
            table.Columns.Add("Sequence");
            table.Columns.Add("Summary");
            table.Columns.Add("Status");
            table.Columns.Add("JobID");
            table.Columns.Add("Analysis");
            table.Columns.Add("Compare");
            table.Columns.Add("Template");
            table.Columns.Add("AIData");
            clsDefine.g_dsProduct.Tables.Add(table);
          }
          else
            table = clsDefine.g_dsProduct.Tables["PAST_Cases"];
          int iRev = 1;
          while (true)
          {
            if (table.Rows.Cast<DataRow>().Any<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == strStudyName + "_Past" + (object) iRev)))
              iRev++;
            else
              break;
          }
          string str1 = strStudyName + "_Past" + (object) iRev;
          clsHDMFLib.ImportFile(strStudyFile, str1, "PAST_Cases");
          string fileNameFromProject = clsHDMFLib.GetStudyFileNameFromProject(clsDefine.g_fiProject.FullName, str1);
          foreach (FileInfo fileInfo in fileInfoArray)
          {
            string str2 = fileInfo.Name.Split('~')[1];
            fileInfo.CopyTo(clsDefine.g_fiProject.DirectoryName + "\\" + fileNameFromProject + "~" + str2, true);
          }
          clsHDMFLib.OpenStudy(str1);
          string meshType = clsHDMFLib.GetMeshType();
          DataRow p_drStudy = table.Rows.Add();
          p_drStudy["Name"] = (object) str1;
          p_drStudy["FName"] = (object) Path.GetFileNameWithoutExtension(str1);
          p_drStudy["Mesh"] = (object) meshType;
          p_drStudy["Check"] = (object) DBNull.Value;
          p_drStudy["Sequence"] = (object) clsHDMFLib.GetStudySequence();
          p_drStudy["Summary"] = (object) DBNull.Value;
          p_drStudy["Status"] = (object) DBNull.Value;
          p_drStudy["Analysis"] = (object) DBNull.Value;
          p_drStudy["JobID"] = (object) DBNull.Value;
          p_drStudy["Compare"] = (object) false;
          p_drStudy["Template"] = (object) string.Empty;
          p_drStudy["AIData"] = (object) DBNull.Value;
          if (!clsHDMFLib.ExistAnalysis())
          {
            p_drStudy["Status"] = clsHDMFLib.ExistFolderFromFolderManager("Runner System") ? (object) clsDefine.Status.READY : (object) clsDefine.Status.NOT_READY;
          }
          else
          {
            p_drStudy["Status"] = (object) clsDefine.Status.COMPLETED;
            p_drStudy["Summary"] = (object) JsonConvert.SerializeObject((object) clsSummary.GetStudySummaryData(p_drStudy, clsDefine.g_fiProject, false));
          }
          clsHDMFLib.SaveStudy();
          clsHDMFLib.CloseStudy();
        }
        this.RefreshTreeView();
        this.RefreshPastCasesTab();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]AddPastCases):" + ex.Message));
      }
      clsUtill.EndProgress();
      clsUtill.ShowForm((Form) this);
    }

    private void DelPastCases()
    {
      List<DataRow> dataRowList = new List<DataRow>();
      try
      {
        foreach (Control control in (ArrangedElementCollection) this.panel_PastCases.Controls)
        {
          if ((control.Controls["cbReport"] as CheckBox).Checked)
            dataRowList.Add(this.GetStudyFromName((control.Controls["ntbStudy"] as NewTextBox).Value));
        }
        if (dataRowList.Count == 0)
          return;
        DataTable table = clsDefine.g_dsProduct.Tables["PAST_Cases"];
        foreach (DataRow row in dataRowList)
        {
          FileInfo fileInfo = new FileInfo(clsDefine.g_diTmpReport.ToString() + "\\" + row["Name"] + "\\Report.ini");
          if (fileInfo.Exists)
            fileInfo.Delete();
          clsHDMFLib.DeleteStudy(row["Name"].ToString());
          table.Rows.Remove(row);
        }
        if (table.Rows.Count == 0)
        {
          clsHDMFLib.DeleteFolder(table.TableName);
          clsDefine.g_dsProduct.Tables.Remove(table);
        }
        this.RefreshPastCasesTab();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]DelPastCases):" + ex.Message));
      }
    }

    private void MiCase_Click(string p_strMesh, DataGridViewRow p_dgvrStudy)
    {
      frmCase frmCase = new frmCase();
      frmCase.m_strStudyName = p_dgvrStudy.Cells["Column_" + p_strMesh + "_Study"].Value.ToString();
      if (frmCase.ShowDialog((IWin32Window) this) != DialogResult.OK)
        return;
      this.UpdateCase(p_strMesh, p_dgvrStudy, frmCase.m_isAutoAnalysis);
    }

    private void dataGridView_Study_CellMouseUp(object sender, DataGridViewCellMouseEventArgs e)
    {
      if (e.Button != MouseButtons.Right)
        return;
      if (clsDefine.enumLicLevel != clsDefine.LicLevel.Ultimate || e.RowIndex < 0)
        return;
      DataGridView dataGridView = sender as DataGridView;
      DataGridViewRow dgvrStudy = dataGridView.Rows[e.RowIndex];
      Point client = dataGridView.PointToClient(Control.MousePosition);
      string strMesh = "Dual";
      strMesh = dataGridView != this.dataGridView_Dual ? "3D" : "Dual";
      if (dataGridView.Rows.Cast<DataGridViewRow>().Where<DataGridViewRow>((System.Func<DataGridViewRow, bool>) (Temp => clsUtill.ConvertToBoolean(Temp.Cells["Column_" + strMesh + "_Check"].EditedFormattedValue.ToString()))).ToList<DataGridViewRow>().Count != 1)
        return;
      ContextMenu contextMenu = new ContextMenu();
      MenuItem menuItem = new MenuItem();
      menuItem.Text = LocaleControl.getInstance().GetString("IDS_CASE");
      menuItem.Click += (EventHandler) ((Menu_sender, Menu_e) => this.MiCase_Click(strMesh, dgvrStudy));
      contextMenu.MenuItems.Add(menuItem);
      contextMenu.Show((Control) dataGridView, client);
    }

    private void treeView_Product_MouseUp(object sender, MouseEventArgs e)
    {
      if (this.treeView_Product.Nodes.Count == 0 || e.Button != MouseButtons.Right)
        return;
      if (!this.isNodeClick)
      {
        ContextMenu contextMenu = new ContextMenu();
        MenuItem menuItem = new MenuItem();
        menuItem.Text = "Import";
        menuItem.Click += new EventHandler(this.MiImport_Click);
        contextMenu.MenuItems.Add(menuItem);
        contextMenu.Show((Control) this.treeView_Product, e.Location);
      }
      this.isNodeClick = false;
    }

    private void MiImport_Click(object sender, EventArgs e)
    {
      string strProduct = "";
      string strItem = "";
      double[] p_arr_dblCenterMove = new double[3];
      List<string> stringList1 = new List<string>();
      OpenFileDialog openFileDialog = new OpenFileDialog();
      if (openFileDialog.ShowDialog((IWin32Window) this) != DialogResult.OK)
        return;
      DialogResult dialogResult = clsUtill.ShowMessageBox((Form) this, LocaleControl.getInstance().GetString("IDS_CHECK_MOVECENTERPOINT"), "HDMFlow", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
      try
      {
        string[] strArray = Path.GetFileNameWithoutExtension(openFileDialog.FileName).Split('_');
        if (strArray.Length < 2)
          return;
        strProduct = strArray[1];
        strItem = strArray[1];
        if (strArray.Length > 2)
          strProduct = strProduct + "_" + strArray[2];
        if (clsDefine.g_dsProduct.Tables.Cast<DataTable>().Any<DataTable>((System.Func<DataTable, bool>) (Temp => Temp.TableName == strProduct)))
          return;
        this.Hide();
        clsUtill.StartProgress("create Study...", (Form) this);
        clsHDMFLib.CreateFolder("", strProduct);
        clsDefine.g_dsProduct.Tables.Add(new DataTable(strProduct));
        DataTable table = clsDefine.g_dsProduct.Tables[strProduct];
        table.Columns.Add("Name");
        table.Columns.Add("FName");
        table.Columns.Add("Mesh");
        table.Columns.Add("Check");
        table.Columns.Add("Sequence");
        table.Columns.Add("Summary");
        table.Columns.Add("Status");
        table.Columns.Add("JobID");
        table.Columns.Add("Analysis");
        table.Columns.Add("Compare");
        table.Columns.Add("Template");
        table.Columns.Add("AIData");
        clsUtill.UpdateProgress("create Study => import CAD...");
        string str1 = clsHDMFLib.ImportCAD(openFileDialog.FileName);
        List<DataRow> dataRowList1 = new List<DataRow>();
        DataRow dataRow1 = table.Rows.Add();
        dataRow1["Name"] = (object) str1;
        dataRow1["FName"] = (object) clsHDMFLib.GetStudyFileName().Replace(".sdy", "");
        dataRow1["Mesh"] = (object) "Dual";
        dataRow1["Check"] = (object) false;
        dataRow1["Sequence"] = (object) clsHDMFLib.GetStudySequence();
        dataRow1["Status"] = (object) DBNull.Value;
        dataRow1["Summary"] = (object) DBNull.Value;
        dataRow1["JobID"] = (object) DBNull.Value;
        dataRow1["Analysis"] = (object) DBNull.Value;
        dataRow1["Compare"] = (object) false;
        dataRow1["Template"] = (object) string.Empty;
        dataRow1["AIData"] = (object) DBNull.Value;
        DataRow p_drProcessDB = (DataRow) null;
        if (clsDefine.enumLicLevel != 0)
        {
          if (clsDefine.g_dtProcessDB.Rows.Count > 0)
          {
            string strName = dataRow1["Name"].ToString().Split('_')[0];
            strItem = dataRow1["Name"].ToString().Split('_')[1];
            DataRow[] array = clsDefine.g_dtProcessDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp =>
            {
              if (!(Temp["Name"].ToString().Split('_')[0] == strName))
              {
                if (!(Temp["Name"].ToString().Split('_')[0].ToLower() == "basic"))
                  return false;
              }
              return Temp["Name"].ToString().Split('_')[1] == strItem;
            })).ToArray<DataRow>();
            if (array.Length != 0)
              p_drProcessDB = ((IEnumerable<DataRow>) array).Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString().Contains("_Default"))).FirstOrDefault<DataRow>() ?? array[0];
          }
          if (p_drProcessDB != null)
          {
            this.ApplyProcessDBToStudy(p_drProcessDB);
            dataRow1["Sequence"] = (object) clsHDMFLib.GetAnalysisSequence();
          }
          clsHDMFLib.SaveStudy();
        }
        dataRowList1.Add(dataRow1);
        clsUtill.EndProgress();
        if (!clsHDMFLib.ExistDualMesh())
        {
          clsMesh.ShowUpdateMesh(dataRowList1.ToArray(), (Form) this, "Dual");
          clsMesh.UpdateDualMesh(dataRowList1.ToArray());
          clsMesh.CloseUpdateMesh();
        }
        clsUtill.StartProgress("create Study => Get Dual Mesh Data...", (Form) this);
        stringList1.AddRange((IEnumerable<string>) clsHDMFLib.GetDualDomainMeshData());
        string str2 = clsHDMFLib.GetGlobalEdgeLengthFromLog().ToString();
        double p_dblAspectRatio = clsUtill.ConvertToDouble(clsDefine.g_dicMeshStat["Value"]);
        dataRow1["Check"] = (object) clsHDMFLib.VerificationDualMesh(stringList1, p_dblAspectRatio);
        clsHDMFLib.CommitPartSufaceProperty();
        clsHDMFLib.ExecutePurgeNodes();
        if (clsDefine.g_dtRotate.Rows.Count > 0)
        {
          DataRow dataRow2 = clsDefine.g_dtRotate.Rows.Cast<DataRow>().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Section"].ToString() == strItem)).FirstOrDefault<DataRow>();
          if (dataRow2 != null)
          {
            clsUtill.UpdateProgress("create Project => Automatic Rotate...");
            for (int index = 0; index < 3; ++index)
            {
              if (dataRow2["Value" + (object) (index + 1)].ToString() != string.Empty)
                clsHDMFLib.RotateModel(clsUtill.ConvertToInt(dataRow2["Direction" + (object) (index + 1)].ToString()), (double) clsUtill.ConvertToInt(dataRow2["Value" + (object) (index + 1)].ToString()));
            }
          }
        }
        if (dialogResult == DialogResult.Yes)
          p_arr_dblCenterMove = clsHDMFLib.MoveCenterPointToZero();
        double[] modelCenterPoint = clsHDMFLib.GetModelCenterPoint();
        double[] modelLengths = clsHDMFLib.GetModelLengths();
        clsHDMFLib.WriteCenterPointToStudyNote(modelCenterPoint);
        clsHDMFLib.WriteCenterMoveToStudyNote(p_arr_dblCenterMove);
        clsHDMFLib.WriteModelLengthsToStudyNote(modelLengths);
        clsUtill.UpdateProgress("create Study => Save and Close Study...");
        clsHDMFLib.SaveStudy();
        clsHDMFLib.CloseStudy();
        clsUtill.EndProgress();
        if (Convert.ToBoolean(dataRow1["Check"]))
        {
          clsUtill.StartProgress("check Dual Study...", (Form) this);
          List<DataRow> dataRowList2 = new List<DataRow>();
          List<string> stringList2 = new List<string>();
          DataRow dataRow3 = dataRow1;
          if (clsUtill.ConvertToDouble(stringList1.Last<string>()) >= 2.0 && clsUtill.ShowMessageBox((Form) this, LocaleControl.getInstance().GetString("IDS_CHECK_CONNECTIVITY_REGIONS"), "create 3D Mesh", MessageBoxButtons.YesNo, MessageBoxIcon.Asterisk) != DialogResult.Yes)
            return;
          clsUtill.UpdateProgress("check and Copy Study...");
          if (!clsHDMFLib.CopyStudy(dataRow3["Name"].ToString(), dataRow3["Name"].ToString() + "_Tetra", "", true, false))
            return;
          DataRow row = dataRow3.Table.Rows.Add();
          row["Name"] = (object) (dataRow3["Name"].ToString() + "_Tetra");
          row["FName"] = (object) clsHDMFLib.GetStudyFileName().Replace(".sdy", "");
          row["Mesh"] = (object) "3D";
          row["Check"] = (object) false;
          row["Sequence"] = dataRow3["Sequence"];
          row["Summary"] = (object) DBNull.Value;
          row["Status"] = (object) DBNull.Value;
          row["JobID"] = (object) DBNull.Value;
          row["Compare"] = (object) false;
          row["AIData"] = (object) DBNull.Value;
          dataRowList2.Add(row);
          stringList2.Add(str2);
          clsUtill.EndProgress();
          clsMesh.ShowUpdateMesh(dataRowList2.ToArray(), (Form) this, "3D");
          clsMesh.Update3DMesh(dataRowList2.ToArray(), stringList2.ToArray());
          clsMesh.CloseUpdateMesh();
          clsUtill.StartProgress("check 3D Mesh and save Study...", (Form) this);
          if (!Convert.ToBoolean(row["Check"]))
          {
            clsHDMFLib.DeleteStudy(row["Name"].ToString());
            row.Table.Rows.Remove(row);
          }
          clsHDMFLib.SaveStudy();
          clsHDMFLib.CloseStudy();
          clsUtill.EndProgress();
        }
        this.RefreshTreeView();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]MiImport_Click):" + ex.Message));
      }
      clsUtill.EndProgress();
      clsUtill.ShowForm((Form) this);
    }

    private void treeView_Product_NodeMouseClick(object sender, TreeNodeMouseClickEventArgs e)
    {
      if (e.Button != MouseButtons.Right || e.Node.Level != 1)
        return;
      this.treeView_Product.SelectedNode = e.Node;
      ContextMenu contextMenu = new ContextMenu();
      MenuItem menuItem = new MenuItem();
      menuItem.Text = "Export";
      menuItem.Click += new EventHandler(this.MiExport_Click);
      contextMenu.MenuItems.Add(menuItem);
      contextMenu.Show((Control) this.treeView_Product, e.Location);
      this.isNodeClick = true;
    }

    private void MiExport_Click(object sender, EventArgs e)
    {
      CommonOpenFileDialog commonOpenFileDialog = new CommonOpenFileDialog()
      {
        IsFolderPicker = true
      };
      if (commonOpenFileDialog.ShowDialog() != CommonFileDialogResult.Ok)
        return;
      clsUtill.StartProgress("export Product...", (Form) this);
      try
      {
        TreeNode tn = this.treeView_Product.SelectedNode;
        DataTable p_dtProduct = clsDefine.g_dsProduct.Tables.Cast<DataTable>().Where<DataTable>((System.Func<DataTable, bool>) (Temp => Temp.TableName == tn.Text)).FirstOrDefault<DataTable>();
        if (p_dtProduct == null)
          return;
        clsHDMFLib.ExportProduct(p_dtProduct, commonOpenFileDialog.FileName);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]MiExport_Click):" + ex.Message));
      }
      clsUtill.EndProgress();
    }

    private void radioButton_Case2_CheckedChanged(object sender, EventArgs e) => this.RefreshComparisonTab();

    private void dataGridView_Compare_MouseDown(object sender, MouseEventArgs e)
    {
      if (e.Button != MouseButtons.Left || !(sender is DataGridView dataGridView))
        return;
      DataGridView.HitTestInfo hitTestInfo = dataGridView.HitTest(e.X, e.Y);
      if (hitTestInfo.RowIndex >= 0)
      {
        int num = (int) this.dataGridView_List.DoDragDrop((object) dataGridView.Rows[hitTestInfo.RowIndex], DragDropEffects.Move);
      }
    }

    private void datagridView_Compare_DragDrop(object sender, DragEventArgs e)
    {
      try
      {
        DataGridView dataGridView1 = sender as DataGridView;
        DataGridViewRow data = e.Data.GetData(typeof (DataGridViewRow)) as DataGridViewRow;
        DataGridView dataGridView2 = data.DataGridView;
        if (dataGridView1 == dataGridView2 || dataGridView1 != this.dataGridView_List && dataGridView1.Rows.Count >= 1)
          return;
        DataRow studyFromName = this.GetStudyFromName(data.Cells[1].Value.ToString());
        if (dataGridView1 != this.dataGridView_List && this.m_lst_dicComparison.Count > 0)
        {
          DataRow dataRow = this.m_lst_dicComparison.FirstOrDefault<KeyValuePair<string, DataRow>>().Value;
          if (!studyFromName["Template"].Equals(dataRow["Template"]))
          {
            int num = (int) clsUtill.ShowMessageBox((Form) this, LocaleControl.getInstance().GetString("IDS_NOTMATCH_TEMPLATE"), this.Text, p_msgIcon: MessageBoxIcon.Hand);
            return;
          }
          if (!studyFromName["Mesh"].Equals(dataRow["Mesh"]))
          {
            int num = (int) clsUtill.ShowMessageBox((Form) this, LocaleControl.getInstance().GetString("IDS_NOTMATCH_MESH"), this.Text, p_msgIcon: MessageBoxIcon.Hand);
            return;
          }
          if (!studyFromName["Sequence"].Equals(dataRow["Sequence"]))
          {
            int num = (int) clsUtill.ShowMessageBox((Form) this, LocaleControl.getInstance().GetString("IDS_NOTMATCH_SEQUENCE"), this.Text, p_msgIcon: MessageBoxIcon.Hand);
            return;
          }
        }
        DataGridViewRow row = dataGridView1.Rows[dataGridView1.Rows.Add()];
        row.Cells[0].Value = studyFromName["Template"];
        row.Cells[1].Value = studyFromName["Name"];
        row.Cells[2].Value = studyFromName["Mesh"];
        row.Cells[3].Value = studyFromName["Sequence"];
        dataGridView2.Rows.Remove(data);
        if (dataGridView1 == this.dataGridView_List)
          this.m_lst_dicComparison.Remove(dataGridView2.Name);
        else
          this.m_lst_dicComparison.Add(dataGridView1.Name, studyFromName);
        if (this.m_lst_dicComparison.Count != 0)
        {
          this.label_Template.Text = "[" + this.m_lst_dicComparison.FirstOrDefault<KeyValuePair<string, DataRow>>().Value["Template"].ToString() + "]";
          this.label_Template.Visible = true;
        }
        else
          this.label_Template.Visible = false;
        this.dataGridView_List.Sort(this.dataGridView_List.Columns[0], ListSortDirection.Ascending);
        dataGridView2.ClearSelection();
        dataGridView1.ClearSelection();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]datagridView_Compare_DragDrop):" + ex.Message));
      }
    }

    private void datagridView_Compare_DragEnter(object sender, DragEventArgs e) => e.Effect = DragDropEffects.Move;

    private void frmMain_FormClosed(object sender, FormClosedEventArgs e)
    {
      if (!clsDefine.g_isNetworkLicense)
        return;
      clsLicense.ExitNetworkLicense();
    }

    private void ribbon_Main_ActiveTabChanged(object sender, EventArgs e)
    {
      if (this.ribbon_Main.ActiveTab == this.ribbonTab_Home)
      {
        if (clsDefine.g_dicEnabledVersion.Count == 0 && !clsDefine.g_isNetworkLicense)
          return;
        if (!this.tabControl_Main.TabPages.Contains(this.tabPage_Study))
          this.tabControl_Main.TabPages.Add(this.tabPage_Study);
        if (!this.tabControl_Main.TabPages.Contains(this.tabPage_Report))
          this.tabControl_Main.TabPages.Add(this.tabPage_Report);
        if (clsDefine.enumLicLevel > clsDefine.LicLevel.Light)
        {
          if (!this.tabControl_Main.TabPages.Contains(this.tabPage_Comparison))
            this.tabControl_Main.TabPages.Add(this.tabPage_Comparison);
          if (!this.tabControl_Main.TabPages.Contains(this.tabPage_PastCases))
            this.tabControl_Main.TabPages.Add(this.tabPage_PastCases);
        }
        if (this.tabControl_Main.TabPages.Contains(this.tabPage_AI_Study))
          this.tabControl_Main.TabPages.Remove(this.tabPage_AI_Study);
        if (this.tabControl_Main.TabPages.Contains(this.tabPage_AI_Result))
          this.tabControl_Main.TabPages.Remove(this.tabPage_AI_Result);
      }
      else if (this.ribbon_Main.ActiveTab == this.ribbonTab_AI)
      {
        if (!clsDefine.g_isAILicense)
          return;
        if (this.tabControl_Main.TabPages.Contains(this.tabPage_Study))
          this.tabControl_Main.TabPages.Remove(this.tabPage_Study);
        if (this.tabControl_Main.TabPages.Contains(this.tabPage_Report))
          this.tabControl_Main.TabPages.Remove(this.tabPage_Report);
        if (this.tabControl_Main.TabPages.Contains(this.tabPage_Comparison))
          this.tabControl_Main.TabPages.Remove(this.tabPage_Comparison);
        if (this.tabControl_Main.TabPages.Contains(this.tabPage_PastCases))
          this.tabControl_Main.TabPages.Remove(this.tabPage_PastCases);
        if (!this.tabControl_Main.TabPages.Contains(this.tabPage_AI_Study))
          this.tabControl_Main.TabPages.Add(this.tabPage_AI_Study);
        if (!this.tabControl_Main.TabPages.Contains(this.tabPage_AI_Result))
          this.tabControl_Main.TabPages.Add(this.tabPage_AI_Result);
      }
      this.RefreshTab();
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.components = (IContainer) new System.ComponentModel.Container();
      ComponentResourceManager componentResourceManager = new ComponentResourceManager(typeof (frmMain));
      DataGridViewCellStyle gridViewCellStyle1 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle2 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle3 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle4 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle5 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle6 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle7 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle8 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle9 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle10 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle11 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle12 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle13 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle14 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle15 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle16 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle17 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle18 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle19 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle20 = new DataGridViewCellStyle();
      this.ribbonPanel2 = new RibbonPanel();
      this.ribbonButton10 = new RibbonButton();
      this.ribbon_Main = new Ribbon();
      this.ribbonTab_Home = new RibbonTab();
      this.ribbonPanel1 = new RibbonPanel();
      this.ribbonButton_NewProject = new RibbonButton();
      this.ribbonButton_OpenProject = new RibbonButton();
      this.ribbonPanel3 = new RibbonPanel();
      this.ribbonButton_Info = new RibbonButton();
      this.ribbonButton1 = new RibbonButton();
      this.ribbonButton_Close = new RibbonButton();
      this.ribbonTab_AI = new RibbonTab();
      this.ribbonPanel5 = new RibbonPanel();
      this.ribbonButton_CreateBigData = new RibbonButton();
      this.ribbonTab_Setting = new RibbonTab();
      this.ribbonPanel4 = new RibbonPanel();
      this.ribbonButton_Set_Mesh = new RibbonButton();
      this.ribbonButton_Set_Runner = new RibbonButton();
      this.ribbonButton_Set_Injection = new RibbonButton();
      this.ribbonButton_Set_MidResult = new RibbonButton();
      this.ribbonButton_Set_ProcDB = new RibbonButton();
      this.ribbonButton_Set_Lang = new RibbonButton();
      this.ribbonButton_Set_SummaryView = new RibbonButton();
      this.ribbonButton_Set_Gate = new RibbonButton();
      this.ribbonButton_Set_MeshStat = new RibbonButton();
      this.ribbonButton_Set_Rotate = new RibbonButton();
      this.ribbonButton_Set_Valve = new RibbonButton();
      this.ribbonPanel6 = new RibbonPanel();
      this.ribbonButton_Set_InputDB = new RibbonButton();
      this.splitContainer1 = new SplitContainer();
      this.splitContainer2 = new SplitContainer();
      this.splitContainer6 = new SplitContainer();
      this.treeView_Product = new TreeView();
      this.splitContainer8 = new SplitContainer();
      this.panel_Mesh = new Panel();
      this.newButton_Mesh = new NewButton();
      this.splitContainer5 = new SplitContainer();
      this.label5 = new Label();
      this.label_Dual = new Label();
      this.label_3D = new Label();
      this.label6 = new Label();
      this.label_STD_Study = new Label();
      this.newTextBox_RotAng = new NewTextBox();
      this.newComboBox_RotateAxis = new NewComboBox();
      this.newButton_Rotate = new NewButton();
      this.label_Rotate_Model = new Label();
      this.panel_HDMFlow = new Panel();
      this.tabControl_Main = new TabControl();
      this.tabPage_Study = new TabPage();
      this.splitContainer3 = new SplitContainer();
      this.splitContainer4 = new SplitContainer();
      this.panel2 = new Panel();
      this.newButton_Dual_Update = new NewButton();
      this.label_Dual_MeshQuality = new Label();
      this.label3 = new Label();
      this.newButton_Dual_Del = new NewButton();
      this.newButton_Dual_Add = new NewButton();
      this.dataGridView_Dual = new DataGridView();
      this.panel3 = new Panel();
      this.newButton_3D_Update = new NewButton();
      this.label1 = new Label();
      this.newButton_3D_Del = new NewButton();
      this.newButton_3D_Add = new NewButton();
      this.dataGridView_3D = new DataGridView();
      this.newButton_Analysis = new NewButton();
      this.tabPage_Report = new TabPage();
      this.splitContainer_Report = new SplitContainer();
      this.panel_Report = new Panel();
      this.panel6 = new Panel();
      this.newButton1 = new NewButton();
      this.textBox3 = new TextBox();
      this.textBox2 = new TextBox();
      this.textBox4 = new TextBox();
      this.textBox1 = new TextBox();
      this.dataGridView3 = new DataGridView();
      this.Column4 = new DataGridViewTextBoxColumn();
      this.Column5 = new DataGridViewTextBoxColumn();
      this.Column6 = new DataGridViewTextBoxColumn();
      this.Column7 = new DataGridViewTextBoxColumn();
      this.Column8 = new DataGridViewTextBoxColumn();
      this.Column9 = new DataGridViewTextBoxColumn();
      this.checkBox1 = new CheckBox();
      this.label17 = new Label();
      this.label16 = new Label();
      this.label7 = new Label();
      this.label15 = new Label();
      this.newButton_Report = new NewButton();
      this.tabPage_PastCases = new TabPage();
      this.splitContainer10 = new SplitContainer();
      this.panel_PastCases = new Panel();
      this.panel11 = new Panel();
      this.newButton2 = new NewButton();
      this.textBox5 = new TextBox();
      this.textBox6 = new TextBox();
      this.textBox7 = new TextBox();
      this.textBox8 = new TextBox();
      this.dataGridView4 = new DataGridView();
      this.dataGridViewTextBoxColumn11 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn12 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn13 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn17 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn18 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn19 = new DataGridViewTextBoxColumn();
      this.checkBox2 = new CheckBox();
      this.label12 = new Label();
      this.label13 = new Label();
      this.label14 = new Label();
      this.label18 = new Label();
      this.newButton_Past_Report = new NewButton();
      this.panel9 = new Panel();
      this.label11 = new Label();
      this.newButton_Past_Del = new NewButton();
      this.newButton_Past_Add = new NewButton();
      this.tabPage_Comparison = new TabPage();
      this.splitContainer7 = new SplitContainer();
      this.splitContainer9 = new SplitContainer();
      this.panel5 = new Panel();
      this.label2 = new Label();
      this.dataGridView_List = new DataGridView();
      this.Column_Template = new DataGridViewTextBoxColumn();
      this.Column_Study = new DataGridViewTextBoxColumn();
      this.Column_Mesh = new DataGridViewTextBoxColumn();
      this.Column_Sequence = new DataGridViewTextBoxColumn();
      this.tableLayoutPanel_CMPR = new TableLayoutPanel();
      this.panel13 = new Panel();
      this.label10 = new Label();
      this.dataGridView5 = new DataGridView();
      this.dataGridViewTextBoxColumn14 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn15 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn16 = new DataGridViewTextBoxColumn();
      this.panel8 = new Panel();
      this.label9 = new Label();
      this.dataGridView2 = new DataGridView();
      this.dataGridViewTextBoxColumn5 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn6 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn10 = new DataGridViewTextBoxColumn();
      this.panel_Compare1 = new Panel();
      this.dataGridView1 = new DataGridView();
      this.dataGridViewTextBoxColumn7 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn8 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn9 = new DataGridViewTextBoxColumn();
      this.label8 = new Label();
      this.panel7 = new Panel();
      this.label_Template = new Label();
      this.radioButton_Case3 = new RadioButton();
      this.radioButton_Case2 = new RadioButton();
      this.label4 = new Label();
      this.newButton_Comparison = new NewButton();
      this.tabPage_AI_Study = new TabPage();
      this.splitContainer12 = new SplitContainer();
      this.splitContainer11 = new SplitContainer();
      this.panel10 = new Panel();
      this.label20 = new Label();
      this.dataGridView_AI_Dual = new DataGridView();
      this.panel12 = new Panel();
      this.label21 = new Label();
      this.dataGridView_AI_3D = new DataGridView();
      this.newButton_AISolution = new NewButton();
      this.tabPage_AI_Result = new TabPage();
      this.splitContainer13 = new SplitContainer();
      this.panel_AIResult = new Panel();
      this.panel15 = new Panel();
      this.newButton3 = new NewButton();
      this.textBox9 = new TextBox();
      this.textBox10 = new TextBox();
      this.textBox11 = new TextBox();
      this.textBox12 = new TextBox();
      this.checkBox3 = new CheckBox();
      this.label19 = new Label();
      this.label22 = new Label();
      this.label23 = new Label();
      this.label24 = new Label();
      this.newButton_AIResult = new NewButton();
      this.panel4 = new Panel();
      this.dataGridView_Monitor = new DataGridView();
      this.Column_Monitor_Product = new DataGridViewTextBoxColumn();
      this.Column_Monitor_Study = new DataGridViewTextBoxColumn();
      this.Column_Monitor_Mesh = new DataGridViewTextBoxColumn();
      this.Column_Monitor_Sequence = new DataGridViewTextBoxColumn();
      this.Column_Monitor_Status = new DataGridViewTextBoxColumn();
      this.label_Monitoring_Analysis = new Label();
      this.dataGridViewCheckBoxColumn1 = new DataGridViewCheckBoxColumn();
      this.dataGridViewTextBoxColumn1 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn2 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn3 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn4 = new DataGridViewTextBoxColumn();
      this.label_Focus = new Label();
      this.imageList_TreeView = new ImageList(this.components);
      this.pictureBox_HDSolutions = new PictureBox();
      this.ribbonButton2 = new RibbonButton();
      this.ribbonButton3 = new RibbonButton();
      this.ribbonButton4 = new RibbonButton();
      this.ribbonButton5 = new RibbonButton();
      this.ribbonButton6 = new RibbonButton();
      this.ribbonButton7 = new RibbonButton();
      this.ribbonButton8 = new RibbonButton();
      this.ribbonButton9 = new RibbonButton();
      this.panel_Rotate = new Panel();
      this.splitContainer1.BeginInit();
      this.splitContainer1.Panel1.SuspendLayout();
      this.splitContainer1.Panel2.SuspendLayout();
      this.splitContainer1.SuspendLayout();
      this.splitContainer2.BeginInit();
      this.splitContainer2.Panel1.SuspendLayout();
      this.splitContainer2.Panel2.SuspendLayout();
      this.splitContainer2.SuspendLayout();
      this.splitContainer6.BeginInit();
      this.splitContainer6.Panel1.SuspendLayout();
      this.splitContainer6.Panel2.SuspendLayout();
      this.splitContainer6.SuspendLayout();
      this.splitContainer8.BeginInit();
      this.splitContainer8.Panel1.SuspendLayout();
      this.splitContainer8.Panel2.SuspendLayout();
      this.splitContainer8.SuspendLayout();
      this.panel_Mesh.SuspendLayout();
      this.splitContainer5.BeginInit();
      this.splitContainer5.Panel1.SuspendLayout();
      this.splitContainer5.Panel2.SuspendLayout();
      this.splitContainer5.SuspendLayout();
      this.panel_HDMFlow.SuspendLayout();
      this.tabControl_Main.SuspendLayout();
      this.tabPage_Study.SuspendLayout();
      this.splitContainer3.BeginInit();
      this.splitContainer3.Panel1.SuspendLayout();
      this.splitContainer3.Panel2.SuspendLayout();
      this.splitContainer3.SuspendLayout();
      this.splitContainer4.BeginInit();
      this.splitContainer4.Panel1.SuspendLayout();
      this.splitContainer4.Panel2.SuspendLayout();
      this.splitContainer4.SuspendLayout();
      this.panel2.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_Dual).BeginInit();
      this.panel3.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_3D).BeginInit();
      this.tabPage_Report.SuspendLayout();
      this.splitContainer_Report.BeginInit();
      this.splitContainer_Report.Panel1.SuspendLayout();
      this.splitContainer_Report.Panel2.SuspendLayout();
      this.splitContainer_Report.SuspendLayout();
      this.panel_Report.SuspendLayout();
      this.panel6.SuspendLayout();
      ((ISupportInitialize) this.dataGridView3).BeginInit();
      this.tabPage_PastCases.SuspendLayout();
      this.splitContainer10.BeginInit();
      this.splitContainer10.Panel1.SuspendLayout();
      this.splitContainer10.Panel2.SuspendLayout();
      this.splitContainer10.SuspendLayout();
      this.panel_PastCases.SuspendLayout();
      this.panel11.SuspendLayout();
      ((ISupportInitialize) this.dataGridView4).BeginInit();
      this.panel9.SuspendLayout();
      this.tabPage_Comparison.SuspendLayout();
      this.splitContainer7.BeginInit();
      this.splitContainer7.Panel1.SuspendLayout();
      this.splitContainer7.Panel2.SuspendLayout();
      this.splitContainer7.SuspendLayout();
      this.splitContainer9.BeginInit();
      this.splitContainer9.Panel1.SuspendLayout();
      this.splitContainer9.Panel2.SuspendLayout();
      this.splitContainer9.SuspendLayout();
      this.panel5.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_List).BeginInit();
      this.tableLayoutPanel_CMPR.SuspendLayout();
      this.panel13.SuspendLayout();
      ((ISupportInitialize) this.dataGridView5).BeginInit();
      this.panel8.SuspendLayout();
      ((ISupportInitialize) this.dataGridView2).BeginInit();
      this.panel_Compare1.SuspendLayout();
      ((ISupportInitialize) this.dataGridView1).BeginInit();
      this.panel7.SuspendLayout();
      this.tabPage_AI_Study.SuspendLayout();
      this.splitContainer12.BeginInit();
      this.splitContainer12.Panel1.SuspendLayout();
      this.splitContainer12.Panel2.SuspendLayout();
      this.splitContainer12.SuspendLayout();
      this.splitContainer11.BeginInit();
      this.splitContainer11.Panel1.SuspendLayout();
      this.splitContainer11.Panel2.SuspendLayout();
      this.splitContainer11.SuspendLayout();
      this.panel10.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_AI_Dual).BeginInit();
      this.panel12.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_AI_3D).BeginInit();
      this.tabPage_AI_Result.SuspendLayout();
      this.splitContainer13.BeginInit();
      this.splitContainer13.Panel1.SuspendLayout();
      this.splitContainer13.Panel2.SuspendLayout();
      this.splitContainer13.SuspendLayout();
      this.panel_AIResult.SuspendLayout();
      this.panel15.SuspendLayout();
      this.panel4.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_Monitor).BeginInit();
      ((ISupportInitialize) this.pictureBox_HDSolutions).BeginInit();
      this.panel_Rotate.SuspendLayout();
      this.SuspendLayout();
      this.ribbonPanel2.ButtonMoreVisible = false;
      this.ribbonPanel2.Items.Add((RibbonItem) this.ribbonButton10);
      this.ribbonPanel2.Name = "ribbonPanel2";
      this.ribbonPanel2.Text = "";
      this.ribbonPanel2.Visible = false;
      this.ribbonButton10.Image = (Image) componentResourceManager.GetObject("ribbonButton10.Image");
      this.ribbonButton10.LargeImage = (Image) componentResourceManager.GetObject("ribbonButton10.LargeImage");
      this.ribbonButton10.Name = "ribbonButton10";
      this.ribbonButton10.SmallImage = (Image) componentResourceManager.GetObject("ribbonButton10.SmallImage");
      this.ribbonButton10.Text = "도움말";
      this.ribbonButton10.Visible = false;
      this.ribbon_Main.CaptionBarVisible = false;
      this.ribbon_Main.Font = new Font("Segoe UI", 8f);
      this.ribbon_Main.Location = new Point(0, 0);
      this.ribbon_Main.Minimized = false;
      this.ribbon_Main.Name = "ribbon_Main";
      this.ribbon_Main.OrbDropDown.BorderRoundness = 8;
      this.ribbon_Main.OrbDropDown.Location = new Point(0, 0);
      this.ribbon_Main.OrbDropDown.Name = "";
      this.ribbon_Main.OrbDropDown.Size = new Size(527, 447);
      this.ribbon_Main.OrbDropDown.TabIndex = 0;
      this.ribbon_Main.OrbStyle = RibbonOrbStyle.Office_2010;
      this.ribbon_Main.OrbVisible = false;
      this.ribbon_Main.RibbonTabFont = new Font("Segoe UI", 8f);
      this.ribbon_Main.Size = new Size(1384, 90);
      this.ribbon_Main.TabIndex = 0;
      this.ribbon_Main.Tabs.Add(this.ribbonTab_Home);
      this.ribbon_Main.Tabs.Add(this.ribbonTab_AI);
      this.ribbon_Main.Tabs.Add(this.ribbonTab_Setting);
      this.ribbon_Main.TabSpacing = 3;
      this.ribbon_Main.ThemeColor = RibbonTheme.Blue_2010;
      this.ribbon_Main.ActiveTabChanged += new EventHandler(this.ribbon_Main_ActiveTabChanged);
      this.ribbonTab_Home.Name = "ribbonTab_Home";
      this.ribbonTab_Home.Panels.Add(this.ribbonPanel1);
      this.ribbonTab_Home.Panels.Add(this.ribbonPanel3);
      this.ribbonTab_Home.Text = "홈(Home)";
      this.ribbonPanel1.ButtonMoreVisible = false;
      this.ribbonPanel1.Items.Add((RibbonItem) this.ribbonButton_NewProject);
      this.ribbonPanel1.Items.Add((RibbonItem) this.ribbonButton_OpenProject);
      this.ribbonPanel1.Name = "ribbonPanel1";
      this.ribbonPanel1.Text = "Start";
      this.ribbonButton_NewProject.Image = (Image) Resources.NewProject;
      this.ribbonButton_NewProject.LargeImage = (Image) Resources.NewProject;
      this.ribbonButton_NewProject.Name = "ribbonButton_NewProject";
      this.ribbonButton_NewProject.SmallImage = (Image) componentResourceManager.GetObject("ribbonButton_NewProject.SmallImage");
      this.ribbonButton_NewProject.Text = "프로젝트   생성";
      this.ribbonButton_NewProject.TextAlignment = RibbonItem.RibbonItemTextAlignment.Center;
      this.ribbonButton_NewProject.Click += new EventHandler(this.ribbonButton_NewProject_Click);
      this.ribbonButton_OpenProject.Image = (Image) Resources.OpenProject;
      this.ribbonButton_OpenProject.LargeImage = (Image) Resources.OpenProject;
      this.ribbonButton_OpenProject.Name = "ribbonButton_OpenProject";
      this.ribbonButton_OpenProject.SmallImage = (Image) componentResourceManager.GetObject("ribbonButton_OpenProject.SmallImage");
      this.ribbonButton_OpenProject.Text = "프로젝트 열기";
      this.ribbonButton_OpenProject.Click += new EventHandler(this.ribbonButton_OpenProject_Click);
      this.ribbonPanel3.ButtonMoreVisible = false;
      this.ribbonPanel3.Items.Add((RibbonItem) this.ribbonButton_Info);
      this.ribbonPanel3.Items.Add((RibbonItem) this.ribbonButton_Close);
      this.ribbonPanel3.Name = "ribbonPanel3";
      this.ribbonPanel3.Text = "Etc";
      this.ribbonButton_Info.DropDownItems.Add((RibbonItem) this.ribbonButton1);
      this.ribbonButton_Info.Image = (Image) Resources.Help;
      this.ribbonButton_Info.LargeImage = (Image) Resources.Help;
      this.ribbonButton_Info.Name = "ribbonButton_Info";
      this.ribbonButton_Info.SmallImage = (Image) componentResourceManager.GetObject("ribbonButton_Info.SmallImage");
      this.ribbonButton_Info.Text = "HDMFlow 정보";
      this.ribbonButton_Info.Click += new EventHandler(this.ribbonButton_Info_Click);
      this.ribbonButton1.Image = (Image) componentResourceManager.GetObject("ribbonButton1.Image");
      this.ribbonButton1.LargeImage = (Image) componentResourceManager.GetObject("ribbonButton1.LargeImage");
      this.ribbonButton1.Name = "ribbonButton1";
      this.ribbonButton1.SmallImage = (Image) componentResourceManager.GetObject("ribbonButton1.SmallImage");
      this.ribbonButton1.Text = "ribbonButton1";
      this.ribbonButton_Close.Image = (Image) Resources.Close;
      this.ribbonButton_Close.LargeImage = (Image) Resources.Close;
      this.ribbonButton_Close.Name = "ribbonButton_Close";
      this.ribbonButton_Close.SmallImage = (Image) componentResourceManager.GetObject("ribbonButton_Close.SmallImage");
      this.ribbonButton_Close.Text = "종료";
      this.ribbonButton_Close.Click += new EventHandler(this.ribbonButton_Close_Click);
      this.ribbonTab_AI.Name = "ribbonTab_AI";
      this.ribbonTab_AI.Panels.Add(this.ribbonPanel5);
      this.ribbonTab_AI.Text = "AI";
      this.ribbonPanel5.ButtonMoreVisible = false;
      this.ribbonPanel5.Items.Add((RibbonItem) this.ribbonButton_CreateBigData);
      this.ribbonPanel5.Name = "ribbonPanel5";
      this.ribbonPanel5.Text = "AI";
      this.ribbonButton_CreateBigData.Image = (Image) Resources.Datas;
      this.ribbonButton_CreateBigData.LargeImage = (Image) Resources.Datas;
      this.ribbonButton_CreateBigData.Name = "ribbonButton_CreateBigData";
      this.ribbonButton_CreateBigData.SmallImage = (Image) componentResourceManager.GetObject("ribbonButton_CreateBigData.SmallImage");
      this.ribbonButton_CreateBigData.Text = "BigData 생성";
      this.ribbonButton_CreateBigData.Click += new EventHandler(this.ribbonButton_CreateBigData_Click);
      this.ribbonTab_Setting.Name = "ribbonTab_Setting";
      this.ribbonTab_Setting.Panels.Add(this.ribbonPanel4);
      this.ribbonTab_Setting.Panels.Add(this.ribbonPanel6);
      this.ribbonTab_Setting.Text = "설정(Setting)";
      this.ribbonPanel4.ButtonMoreVisible = false;
      this.ribbonPanel4.Items.Add((RibbonItem) this.ribbonButton_Set_Mesh);
      this.ribbonPanel4.Items.Add((RibbonItem) this.ribbonButton_Set_Runner);
      this.ribbonPanel4.Items.Add((RibbonItem) this.ribbonButton_Set_Injection);
      this.ribbonPanel4.Items.Add((RibbonItem) this.ribbonButton_Set_MidResult);
      this.ribbonPanel4.Items.Add((RibbonItem) this.ribbonButton_Set_ProcDB);
      this.ribbonPanel4.Items.Add((RibbonItem) this.ribbonButton_Set_Lang);
      this.ribbonPanel4.Items.Add((RibbonItem) this.ribbonButton_Set_SummaryView);
      this.ribbonPanel4.Items.Add((RibbonItem) this.ribbonButton_Set_Gate);
      this.ribbonPanel4.Items.Add((RibbonItem) this.ribbonButton_Set_MeshStat);
      this.ribbonPanel4.Items.Add((RibbonItem) this.ribbonButton_Set_Rotate);
      this.ribbonPanel4.Items.Add((RibbonItem) this.ribbonButton_Set_Valve);
      this.ribbonPanel4.Name = "ribbonPanel4";
      this.ribbonPanel4.Text = "HDMFlow Settings";
      this.ribbonButton_Set_Mesh.Image = (Image) componentResourceManager.GetObject("ribbonButton_Set_Mesh.Image");
      this.ribbonButton_Set_Mesh.LargeImage = (Image) componentResourceManager.GetObject("ribbonButton_Set_Mesh.LargeImage");
      this.ribbonButton_Set_Mesh.MaxSizeMode = RibbonElementSizeMode.Medium;
      this.ribbonButton_Set_Mesh.Name = "ribbonButton_Set_Mesh";
      this.ribbonButton_Set_Mesh.SmallImage = (Image) Resources.Setting;
      this.ribbonButton_Set_Mesh.Text = "메쉬 생성(Mesh)";
      this.ribbonButton_Set_Mesh.Click += new EventHandler(this.ribbonButton_Set_Click);
      this.ribbonButton_Set_Runner.Image = (Image) componentResourceManager.GetObject("ribbonButton_Set_Runner.Image");
      this.ribbonButton_Set_Runner.LargeImage = (Image) componentResourceManager.GetObject("ribbonButton_Set_Runner.LargeImage");
      this.ribbonButton_Set_Runner.MaxSizeMode = RibbonElementSizeMode.Medium;
      this.ribbonButton_Set_Runner.Name = "ribbonButton_Set_Runner";
      this.ribbonButton_Set_Runner.SmallImage = (Image) componentResourceManager.GetObject("ribbonButton_Set_Runner.SmallImage");
      this.ribbonButton_Set_Runner.Text = "런너 DB(Runner)";
      this.ribbonButton_Set_Runner.Click += new EventHandler(this.ribbonButton_Set_Click);
      this.ribbonButton_Set_Injection.Image = (Image) componentResourceManager.GetObject("ribbonButton_Set_Injection.Image");
      this.ribbonButton_Set_Injection.LargeImage = (Image) componentResourceManager.GetObject("ribbonButton_Set_Injection.LargeImage");
      this.ribbonButton_Set_Injection.MaxSizeMode = RibbonElementSizeMode.Medium;
      this.ribbonButton_Set_Injection.Name = "ribbonButton_Set_Injection";
      this.ribbonButton_Set_Injection.SmallImage = (Image) Resources.Setting;
      this.ribbonButton_Set_Injection.Text = "사출기 설정(Injection)";
      this.ribbonButton_Set_Injection.Click += new EventHandler(this.ribbonButton_Set_Click);
      this.ribbonButton_Set_MidResult.Image = (Image) componentResourceManager.GetObject("ribbonButton_Set_MidResult.Image");
      this.ribbonButton_Set_MidResult.LargeImage = (Image) componentResourceManager.GetObject("ribbonButton_Set_MidResult.LargeImage");
      this.ribbonButton_Set_MidResult.MaxSizeMode = RibbonElementSizeMode.Medium;
      this.ribbonButton_Set_MidResult.Name = "ribbonButton_Set_MidResult";
      this.ribbonButton_Set_MidResult.SmallImage = (Image) componentResourceManager.GetObject("ribbonButton_Set_MidResult.SmallImage");
      this.ribbonButton_Set_MidResult.Text = "중간 결과 수(Result)";
      this.ribbonButton_Set_MidResult.Click += new EventHandler(this.ribbonButton_Set_Click);
      this.ribbonButton_Set_ProcDB.Image = (Image) componentResourceManager.GetObject("ribbonButton_Set_ProcDB.Image");
      this.ribbonButton_Set_ProcDB.LargeImage = (Image) componentResourceManager.GetObject("ribbonButton_Set_ProcDB.LargeImage");
      this.ribbonButton_Set_ProcDB.MaxSizeMode = RibbonElementSizeMode.Medium;
      this.ribbonButton_Set_ProcDB.Name = "ribbonButton_Set_ProcDB";
      this.ribbonButton_Set_ProcDB.SmallImage = (Image) componentResourceManager.GetObject("ribbonButton_Set_ProcDB.SmallImage");
      this.ribbonButton_Set_ProcDB.Text = "프로세스 세팅 DB(Process Set)";
      this.ribbonButton_Set_ProcDB.Click += new EventHandler(this.ribbonButton_Set_Click);
      this.ribbonButton_Set_Lang.Image = (Image) componentResourceManager.GetObject("ribbonButton_Set_Lang.Image");
      this.ribbonButton_Set_Lang.LargeImage = (Image) componentResourceManager.GetObject("ribbonButton_Set_Lang.LargeImage");
      this.ribbonButton_Set_Lang.MaxSizeMode = RibbonElementSizeMode.Medium;
      this.ribbonButton_Set_Lang.Name = "ribbonButton_Set_Lang";
      this.ribbonButton_Set_Lang.SmallImage = (Image) componentResourceManager.GetObject("ribbonButton_Set_Lang.SmallImage");
      this.ribbonButton_Set_Lang.Text = "언어(Language)";
      this.ribbonButton_Set_Lang.Click += new EventHandler(this.ribbonButton_Set_Click);
      this.ribbonButton_Set_SummaryView.Image = (Image) componentResourceManager.GetObject("ribbonButton_Set_SummaryView.Image");
      this.ribbonButton_Set_SummaryView.LargeImage = (Image) componentResourceManager.GetObject("ribbonButton_Set_SummaryView.LargeImage");
      this.ribbonButton_Set_SummaryView.MaxSizeMode = RibbonElementSizeMode.Medium;
      this.ribbonButton_Set_SummaryView.Name = "ribbonButton_Set_SummaryView";
      this.ribbonButton_Set_SummaryView.SmallImage = (Image) componentResourceManager.GetObject("ribbonButton_Set_SummaryView.SmallImage");
      this.ribbonButton_Set_SummaryView.Text = "SummaryView";
      this.ribbonButton_Set_SummaryView.Click += new EventHandler(this.ribbonButton_Set_Click);
      this.ribbonButton_Set_Gate.Image = (Image) componentResourceManager.GetObject("ribbonButton_Set_Gate.Image");
      this.ribbonButton_Set_Gate.LargeImage = (Image) componentResourceManager.GetObject("ribbonButton_Set_Gate.LargeImage");
      this.ribbonButton_Set_Gate.MaxSizeMode = RibbonElementSizeMode.Medium;
      this.ribbonButton_Set_Gate.Name = "ribbonButton_Set_Gate";
      this.ribbonButton_Set_Gate.SmallImage = (Image) Resources.Setting;
      this.ribbonButton_Set_Gate.Text = "게이트(Gate)";
      this.ribbonButton_Set_Gate.Click += new EventHandler(this.ribbonButton_Set_Click);
      this.ribbonButton_Set_MeshStat.Image = (Image) componentResourceManager.GetObject("ribbonButton_Set_MeshStat.Image");
      this.ribbonButton_Set_MeshStat.LargeImage = (Image) componentResourceManager.GetObject("ribbonButton_Set_MeshStat.LargeImage");
      this.ribbonButton_Set_MeshStat.MaxSizeMode = RibbonElementSizeMode.Medium;
      this.ribbonButton_Set_MeshStat.Name = "ribbonButton_Set_MeshStat";
      this.ribbonButton_Set_MeshStat.SmallImage = (Image) Resources.Setting;
      this.ribbonButton_Set_MeshStat.Text = "메쉬 통계(Mesh Statistics)";
      this.ribbonButton_Set_MeshStat.Click += new EventHandler(this.ribbonButton_Set_Click);
      this.ribbonButton_Set_Rotate.Image = (Image) componentResourceManager.GetObject("ribbonButton_Set_Rotate.Image");
      this.ribbonButton_Set_Rotate.LargeImage = (Image) componentResourceManager.GetObject("ribbonButton_Set_Rotate.LargeImage");
      this.ribbonButton_Set_Rotate.MaxSizeMode = RibbonElementSizeMode.Medium;
      this.ribbonButton_Set_Rotate.Name = "ribbonButton_Set_Rotate";
      this.ribbonButton_Set_Rotate.SmallImage = (Image) Resources.Setting;
      this.ribbonButton_Set_Rotate.Text = "자동 회전(Automatic Rotate)";
      this.ribbonButton_Set_Rotate.Click += new EventHandler(this.ribbonButton_Set_Click);
      this.ribbonButton_Set_Valve.Image = (Image) componentResourceManager.GetObject("ribbonButton_Set_Valve.Image");
      this.ribbonButton_Set_Valve.LargeImage = (Image) componentResourceManager.GetObject("ribbonButton_Set_Valve.LargeImage");
      this.ribbonButton_Set_Valve.MaxSizeMode = RibbonElementSizeMode.Medium;
      this.ribbonButton_Set_Valve.Name = "ribbonButton_Set_Valve";
      this.ribbonButton_Set_Valve.SmallImage = (Image) Resources.Setting;
      this.ribbonButton_Set_Valve.Text = "밸브 설정(Valve)";
      this.ribbonButton_Set_Valve.Click += new EventHandler(this.ribbonButton_Set_Click);
      this.ribbonPanel6.ButtonMoreVisible = false;
      this.ribbonPanel6.Items.Add((RibbonItem) this.ribbonButton_Set_InputDB);
      this.ribbonPanel6.Name = "ribbonPanel6";
      this.ribbonPanel6.Text = "AI Settings";
      this.ribbonButton_Set_InputDB.Image = (Image) componentResourceManager.GetObject("ribbonButton_Set_InputDB.Image");
      this.ribbonButton_Set_InputDB.LargeImage = (Image) componentResourceManager.GetObject("ribbonButton_Set_InputDB.LargeImage");
      this.ribbonButton_Set_InputDB.MaxSizeMode = RibbonElementSizeMode.Medium;
      this.ribbonButton_Set_InputDB.Name = "ribbonButton_Set_InputDB";
      this.ribbonButton_Set_InputDB.SmallImage = (Image) Resources.Setting;
      this.ribbonButton_Set_InputDB.Text = "Input DB";
      this.ribbonButton_Set_InputDB.Click += new EventHandler(this.ribbonButton_Set_Click);
      this.splitContainer1.Dock = DockStyle.Fill;
      this.splitContainer1.Location = new Point(0, 90);
      this.splitContainer1.Name = "splitContainer1";
      this.splitContainer1.Orientation = Orientation.Horizontal;
      this.splitContainer1.Panel1.Controls.Add((Control) this.splitContainer2);
      this.splitContainer1.Panel2.Controls.Add((Control) this.panel4);
      this.splitContainer1.Size = new Size(1384, 711);
      this.splitContainer1.SplitterDistance = 524;
      this.splitContainer1.SplitterWidth = 1;
      this.splitContainer1.TabIndex = 1;
      this.splitContainer2.Dock = DockStyle.Fill;
      this.splitContainer2.Location = new Point(0, 0);
      this.splitContainer2.Name = "splitContainer2";
      this.splitContainer2.Panel1.Controls.Add((Control) this.splitContainer6);
      this.splitContainer2.Panel2.Controls.Add((Control) this.panel_HDMFlow);
      this.splitContainer2.Size = new Size(1384, 524);
      this.splitContainer2.SplitterDistance = 207;
      this.splitContainer2.SplitterWidth = 1;
      this.splitContainer2.TabIndex = 0;
      this.splitContainer6.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.splitContainer6.FixedPanel = FixedPanel.Panel2;
      this.splitContainer6.Location = new Point(1, 1);
      this.splitContainer6.Name = "splitContainer6";
      this.splitContainer6.Orientation = Orientation.Horizontal;
      this.splitContainer6.Panel1.Controls.Add((Control) this.treeView_Product);
      this.splitContainer6.Panel2.Controls.Add((Control) this.splitContainer8);
      this.splitContainer6.Size = new Size(205, 523);
      this.splitContainer6.SplitterDistance = 357;
      this.splitContainer6.TabIndex = 4;
      this.treeView_Product.BorderStyle = BorderStyle.FixedSingle;
      this.treeView_Product.Dock = DockStyle.Fill;
      this.treeView_Product.HideSelection = false;
      this.treeView_Product.Location = new Point(0, 0);
      this.treeView_Product.Name = "treeView_Product";
      this.treeView_Product.Size = new Size(205, 357);
      this.treeView_Product.TabIndex = 2;
      this.treeView_Product.AfterSelect += new TreeViewEventHandler(this.treeView_Product_AfterSelect);
      this.treeView_Product.NodeMouseClick += new TreeNodeMouseClickEventHandler(this.treeView_Product_NodeMouseClick);
      this.treeView_Product.MouseUp += new MouseEventHandler(this.treeView_Product_MouseUp);
      this.splitContainer8.Dock = DockStyle.Fill;
      this.splitContainer8.Location = new Point(0, 0);
      this.splitContainer8.Name = "splitContainer8";
      this.splitContainer8.Orientation = Orientation.Horizontal;
      this.splitContainer8.Panel1.Controls.Add((Control) this.panel_Mesh);
      this.splitContainer8.Panel2.Controls.Add((Control) this.panel_Rotate);
      this.splitContainer8.Size = new Size(205, 162);
      this.splitContainer8.SplitterDistance = 89;
      this.splitContainer8.TabIndex = 0;
      this.panel_Mesh.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Mesh.Controls.Add((Control) this.newButton_Mesh);
      this.panel_Mesh.Controls.Add((Control) this.splitContainer5);
      this.panel_Mesh.Controls.Add((Control) this.label_STD_Study);
      this.panel_Mesh.Dock = DockStyle.Fill;
      this.panel_Mesh.Enabled = false;
      this.panel_Mesh.Location = new Point(0, 0);
      this.panel_Mesh.Name = "panel_Mesh";
      this.panel_Mesh.Size = new Size(205, 89);
      this.panel_Mesh.TabIndex = 3;
      this.newButton_Mesh.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
      this.newButton_Mesh.ButtonBackColor = Color.White;
      this.newButton_Mesh.ButtonText = "업데이트(메쉬)";
      this.newButton_Mesh.Enabled = false;
      this.newButton_Mesh.FlatBorderSize = 1;
      this.newButton_Mesh.FlatStyle = FlatStyle.Flat;
      this.newButton_Mesh.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Mesh.Image = (Image) componentResourceManager.GetObject("newButton_Mesh.Image");
      this.newButton_Mesh.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Mesh.Location = new Point(1, 60);
      this.newButton_Mesh.Name = "newButton_Mesh";
      this.newButton_Mesh.Size = new Size(201, 26);
      this.newButton_Mesh.TabIndex = 9;
      this.newButton_Mesh.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Mesh.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Mesh.NewClick += new EventHandler(this.newButton_NewClick);
      this.splitContainer5.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
      this.splitContainer5.Location = new Point(-2, 20);
      this.splitContainer5.Name = "splitContainer5";
      this.splitContainer5.Panel1.Controls.Add((Control) this.label5);
      this.splitContainer5.Panel1.Controls.Add((Control) this.label_Dual);
      this.splitContainer5.Panel2.Controls.Add((Control) this.label_3D);
      this.splitContainer5.Panel2.Controls.Add((Control) this.label6);
      this.splitContainer5.Size = new Size(207, 39);
      this.splitContainer5.SplitterDistance = 100;
      this.splitContainer5.TabIndex = 8;
      this.label5.BackColor = Color.Lavender;
      this.label5.BorderStyle = BorderStyle.FixedSingle;
      this.label5.Dock = DockStyle.Top;
      this.label5.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label5.ForeColor = Color.MidnightBlue;
      this.label5.Location = new Point(0, 0);
      this.label5.Name = "label5";
      this.label5.Size = new Size(100, 20);
      this.label5.TabIndex = 6;
      this.label5.Text = "Dual";
      this.label5.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Dual.BackColor = Color.LavenderBlush;
      this.label_Dual.BorderStyle = BorderStyle.FixedSingle;
      this.label_Dual.Dock = DockStyle.Bottom;
      this.label_Dual.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Dual.ForeColor = Color.MidnightBlue;
      this.label_Dual.Location = new Point(0, 19);
      this.label_Dual.Name = "label_Dual";
      this.label_Dual.Size = new Size(100, 20);
      this.label_Dual.TabIndex = 6;
      this.label_Dual.Text = "-";
      this.label_Dual.TextAlign = ContentAlignment.MiddleCenter;
      this.label_3D.BackColor = Color.LavenderBlush;
      this.label_3D.BorderStyle = BorderStyle.FixedSingle;
      this.label_3D.Dock = DockStyle.Bottom;
      this.label_3D.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_3D.ForeColor = Color.MidnightBlue;
      this.label_3D.Location = new Point(0, 19);
      this.label_3D.Name = "label_3D";
      this.label_3D.Size = new Size(103, 20);
      this.label_3D.TabIndex = 6;
      this.label_3D.Text = "-";
      this.label_3D.TextAlign = ContentAlignment.MiddleCenter;
      this.label6.BackColor = Color.Lavender;
      this.label6.BorderStyle = BorderStyle.FixedSingle;
      this.label6.Dock = DockStyle.Top;
      this.label6.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label6.ForeColor = Color.MidnightBlue;
      this.label6.Location = new Point(0, 0);
      this.label6.Name = "label6";
      this.label6.Size = new Size(103, 20);
      this.label6.TabIndex = 6;
      this.label6.Text = "3D";
      this.label6.TextAlign = ContentAlignment.MiddleCenter;
      this.label_STD_Study.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
      this.label_STD_Study.BackColor = Color.FromArgb(229, 238, 248);
      this.label_STD_Study.BorderStyle = BorderStyle.FixedSingle;
      this.label_STD_Study.ForeColor = Color.MidnightBlue;
      this.label_STD_Study.Location = new Point(-1, -1);
      this.label_STD_Study.Name = "label_STD_Study";
      this.label_STD_Study.Size = new Size(205, 20);
      this.label_STD_Study.TabIndex = 4;
      this.label_STD_Study.Text = "[기준스터디]";
      this.label_STD_Study.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_RotAng.Anchor = AnchorStyles.Top | AnchorStyles.Right;
      this.newTextBox_RotAng.BackColor = SystemColors.Window;
      this.newTextBox_RotAng.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_RotAng.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_RotAng.IsDigit = true;
      this.newTextBox_RotAng.Lines = new string[1]{ "0" };
      this.newTextBox_RotAng.Location = new Point(149, 20);
      this.newTextBox_RotAng.MultiLine = false;
      this.newTextBox_RotAng.Name = "newTextBox_RotAng";
      this.newTextBox_RotAng.ReadOnly = false;
      this.newTextBox_RotAng.Size = new Size(56, 23);
      this.newTextBox_RotAng.TabIndex = 9;
      this.newTextBox_RotAng.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_RotAng.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_RotAng.TextForeColor = SystemColors.WindowText;
      this.newTextBox_RotAng.Value = "0";
      this.newComboBox_RotateAxis.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
      this.newComboBox_RotateAxis.BackColor = Color.White;
      this.newComboBox_RotateAxis.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_RotateAxis.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_RotateAxis.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_RotateAxis.isSameSelect = false;
      this.newComboBox_RotateAxis.Location = new Point(0, 20);
      this.newComboBox_RotateAxis.Name = "newComboBox_RotateAxis";
      this.newComboBox_RotateAxis.SelectedIndex = -1;
      this.newComboBox_RotateAxis.Size = new Size(150, 23);
      this.newComboBox_RotateAxis.TabIndex = 8;
      this.newComboBox_RotateAxis.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_RotateAxis.Value = "";
      this.newButton_Rotate.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
      this.newButton_Rotate.BorderStyle = BorderStyle.FixedSingle;
      this.newButton_Rotate.ButtonBackColor = Color.White;
      this.newButton_Rotate.ButtonText = "회전";
      this.newButton_Rotate.FlatBorderSize = 1;
      this.newButton_Rotate.FlatStyle = FlatStyle.Flat;
      this.newButton_Rotate.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Rotate.Image = (Image) Resources.Rotate;
      this.newButton_Rotate.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Rotate.Location = new Point(-1, 41);
      this.newButton_Rotate.Name = "newButton_Rotate";
      this.newButton_Rotate.Size = new Size(207, 29);
      this.newButton_Rotate.TabIndex = 7;
      this.newButton_Rotate.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Rotate.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Rotate.NewClick += new EventHandler(this.newButton_NewClick);
      this.label_Rotate_Model.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
      this.label_Rotate_Model.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Rotate_Model.BorderStyle = BorderStyle.FixedSingle;
      this.label_Rotate_Model.ForeColor = Color.MidnightBlue;
      this.label_Rotate_Model.Location = new Point(0, 0);
      this.label_Rotate_Model.Name = "label_Rotate_Model";
      this.label_Rotate_Model.Size = new Size(205, 21);
      this.label_Rotate_Model.TabIndex = 4;
      this.label_Rotate_Model.Text = "[모델 회전]";
      this.label_Rotate_Model.TextAlign = ContentAlignment.MiddleCenter;
      this.panel_HDMFlow.BorderStyle = BorderStyle.FixedSingle;
      this.panel_HDMFlow.Controls.Add((Control) this.tabControl_Main);
      this.panel_HDMFlow.Dock = DockStyle.Fill;
      this.panel_HDMFlow.Location = new Point(0, 0);
      this.panel_HDMFlow.Name = "panel_HDMFlow";
      this.panel_HDMFlow.Size = new Size(1176, 524);
      this.panel_HDMFlow.TabIndex = 6;
      this.tabControl_Main.Controls.Add((Control) this.tabPage_Study);
      this.tabControl_Main.Controls.Add((Control) this.tabPage_Report);
      this.tabControl_Main.Controls.Add((Control) this.tabPage_PastCases);
      this.tabControl_Main.Controls.Add((Control) this.tabPage_Comparison);
      this.tabControl_Main.Controls.Add((Control) this.tabPage_AI_Study);
      this.tabControl_Main.Controls.Add((Control) this.tabPage_AI_Result);
      this.tabControl_Main.Dock = DockStyle.Fill;
      this.tabControl_Main.ItemSize = new Size(115, 20);
      this.tabControl_Main.Location = new Point(0, 0);
      this.tabControl_Main.Name = "tabControl_Main";
      this.tabControl_Main.SelectedIndex = 0;
      this.tabControl_Main.Size = new Size(1174, 522);
      this.tabControl_Main.SizeMode = TabSizeMode.Fixed;
      this.tabControl_Main.TabIndex = 5;
      this.tabControl_Main.SelectedIndexChanged += new EventHandler(this.tabControl_Main_SelectedIndexChanged);
      this.tabPage_Study.Controls.Add((Control) this.splitContainer3);
      this.tabPage_Study.Location = new Point(4, 24);
      this.tabPage_Study.Name = "tabPage_Study";
      this.tabPage_Study.Padding = new Padding(3);
      this.tabPage_Study.Size = new Size(1166, 494);
      this.tabPage_Study.TabIndex = 0;
      this.tabPage_Study.Text = "스터디(Study)";
      this.tabPage_Study.UseVisualStyleBackColor = true;
      this.splitContainer3.Dock = DockStyle.Fill;
      this.splitContainer3.FixedPanel = FixedPanel.Panel2;
      this.splitContainer3.IsSplitterFixed = true;
      this.splitContainer3.Location = new Point(3, 3);
      this.splitContainer3.Name = "splitContainer3";
      this.splitContainer3.Orientation = Orientation.Horizontal;
      this.splitContainer3.Panel1.Controls.Add((Control) this.splitContainer4);
      this.splitContainer3.Panel2.Controls.Add((Control) this.newButton_Analysis);
      this.splitContainer3.Size = new Size(1160, 488);
      this.splitContainer3.SplitterDistance = 458;
      this.splitContainer3.TabIndex = 8;
      this.splitContainer4.Dock = DockStyle.Fill;
      this.splitContainer4.IsSplitterFixed = true;
      this.splitContainer4.Location = new Point(0, 0);
      this.splitContainer4.Name = "splitContainer4";
      this.splitContainer4.Orientation = Orientation.Horizontal;
      this.splitContainer4.Panel1.Controls.Add((Control) this.panel2);
      this.splitContainer4.Panel1.Controls.Add((Control) this.dataGridView_Dual);
      this.splitContainer4.Panel2.Controls.Add((Control) this.panel3);
      this.splitContainer4.Panel2.Controls.Add((Control) this.dataGridView_3D);
      this.splitContainer4.Size = new Size(1160, 458);
      this.splitContainer4.SplitterDistance = 226;
      this.splitContainer4.TabIndex = 6;
      this.panel2.BackColor = Color.FromArgb(229, 238, 248);
      this.panel2.BorderStyle = BorderStyle.FixedSingle;
      this.panel2.Controls.Add((Control) this.newButton_Dual_Update);
      this.panel2.Controls.Add((Control) this.label_Dual_MeshQuality);
      this.panel2.Controls.Add((Control) this.label3);
      this.panel2.Controls.Add((Control) this.newButton_Dual_Del);
      this.panel2.Controls.Add((Control) this.newButton_Dual_Add);
      this.panel2.Dock = DockStyle.Top;
      this.panel2.Location = new Point(0, 0);
      this.panel2.Name = "panel2";
      this.panel2.Size = new Size(1160, 20);
      this.panel2.TabIndex = 6;
      this.newButton_Dual_Update.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Dual_Update.ButtonText = "";
      this.newButton_Dual_Update.FlatBorderSize = 0;
      this.newButton_Dual_Update.FlatStyle = FlatStyle.Flat;
      this.newButton_Dual_Update.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Dual_Update.Image = (Image) Resources.Update;
      this.newButton_Dual_Update.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Dual_Update.Location = new Point(97, 1);
      this.newButton_Dual_Update.Name = "newButton_Dual_Update";
      this.newButton_Dual_Update.Size = new Size(19, 17);
      this.newButton_Dual_Update.TabIndex = 8;
      this.newButton_Dual_Update.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Dual_Update.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Dual_Update.NewClick += new EventHandler(this.newButton_NewClick);
      this.label_Dual_MeshQuality.BackColor = Color.Transparent;
      this.label_Dual_MeshQuality.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Dual_MeshQuality.ForeColor = Color.Red;
      this.label_Dual_MeshQuality.Location = new Point(121, -1);
      this.label_Dual_MeshQuality.Name = "label_Dual_MeshQuality";
      this.label_Dual_MeshQuality.Size = new Size(197, 20);
      this.label_Dual_MeshQuality.TabIndex = 7;
      this.label_Dual_MeshQuality.TextAlign = ContentAlignment.MiddleLeft;
      this.label3.BackColor = Color.FromArgb(229, 238, 248);
      this.label3.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label3.ForeColor = Color.MidnightBlue;
      this.label3.Location = new Point(0, 0);
      this.label3.Name = "label3";
      this.label3.Size = new Size(41, 20);
      this.label3.TabIndex = 4;
      this.label3.Text = "[Dual] ";
      this.label3.TextAlign = ContentAlignment.MiddleLeft;
      this.newButton_Dual_Del.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Dual_Del.ButtonText = "";
      this.newButton_Dual_Del.FlatBorderSize = 0;
      this.newButton_Dual_Del.FlatStyle = FlatStyle.Flat;
      this.newButton_Dual_Del.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Dual_Del.Image = (Image) Resources.Del;
      this.newButton_Dual_Del.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Dual_Del.Location = new Point(72, 1);
      this.newButton_Dual_Del.Name = "newButton_Dual_Del";
      this.newButton_Dual_Del.Size = new Size(19, 17);
      this.newButton_Dual_Del.TabIndex = 5;
      this.newButton_Dual_Del.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Dual_Del.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Dual_Del.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_Dual_Add.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Dual_Add.ButtonText = "";
      this.newButton_Dual_Add.FlatBorderSize = 0;
      this.newButton_Dual_Add.FlatStyle = FlatStyle.Flat;
      this.newButton_Dual_Add.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Dual_Add.Image = (Image) Resources.Add;
      this.newButton_Dual_Add.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Dual_Add.Location = new Point(47, 1);
      this.newButton_Dual_Add.Name = "newButton_Dual_Add";
      this.newButton_Dual_Add.Size = new Size(19, 17);
      this.newButton_Dual_Add.TabIndex = 5;
      this.newButton_Dual_Add.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Dual_Add.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Dual_Add.NewClick += new EventHandler(this.newButton_NewClick);
      this.dataGridView_Dual.AllowUserToAddRows = false;
      this.dataGridView_Dual.AllowUserToDeleteRows = false;
      this.dataGridView_Dual.AllowUserToResizeColumns = false;
      this.dataGridView_Dual.AllowUserToResizeRows = false;
      this.dataGridView_Dual.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_Dual.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_Dual.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
      this.dataGridView_Dual.BackgroundColor = Color.White;
      this.dataGridView_Dual.BorderStyle = BorderStyle.None;
      gridViewCellStyle1.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle1.BackColor = Color.Lavender;
      gridViewCellStyle1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle1.ForeColor = SystemColors.WindowText;
      gridViewCellStyle1.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle1.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle1.WrapMode = DataGridViewTriState.True;
      this.dataGridView_Dual.ColumnHeadersDefaultCellStyle = gridViewCellStyle1;
      this.dataGridView_Dual.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      gridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle2.BackColor = SystemColors.Window;
      gridViewCellStyle2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle2.ForeColor = SystemColors.ControlText;
      gridViewCellStyle2.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle2.WrapMode = DataGridViewTriState.False;
      this.dataGridView_Dual.DefaultCellStyle = gridViewCellStyle2;
      this.dataGridView_Dual.EnableHeadersVisualStyles = false;
      this.dataGridView_Dual.Location = new Point(0, 19);
      this.dataGridView_Dual.Name = "dataGridView_Dual";
      this.dataGridView_Dual.RowHeadersVisible = false;
      this.dataGridView_Dual.RowTemplate.Height = 23;
      this.dataGridView_Dual.Size = new Size(1160, 206);
      this.dataGridView_Dual.TabIndex = 0;
      this.dataGridView_Dual.CellClick += new DataGridViewCellEventHandler(this.dataGridView_Study_CellClick);
      this.dataGridView_Dual.CellMouseUp += new DataGridViewCellMouseEventHandler(this.dataGridView_Study_CellMouseUp);
      this.dataGridView_Dual.SelectionChanged += new EventHandler(this.dataGridView_Study_SelectionChanged);
      this.panel3.BackColor = Color.FromArgb(229, 238, 248);
      this.panel3.BorderStyle = BorderStyle.FixedSingle;
      this.panel3.Controls.Add((Control) this.newButton_3D_Update);
      this.panel3.Controls.Add((Control) this.label1);
      this.panel3.Controls.Add((Control) this.newButton_3D_Del);
      this.panel3.Controls.Add((Control) this.newButton_3D_Add);
      this.panel3.Dock = DockStyle.Top;
      this.panel3.Location = new Point(0, 0);
      this.panel3.Name = "panel3";
      this.panel3.Size = new Size(1160, 20);
      this.panel3.TabIndex = 7;
      this.newButton_3D_Update.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_3D_Update.ButtonText = "";
      this.newButton_3D_Update.FlatBorderSize = 0;
      this.newButton_3D_Update.FlatStyle = FlatStyle.Flat;
      this.newButton_3D_Update.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_3D_Update.Image = (Image) Resources.Update;
      this.newButton_3D_Update.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_3D_Update.Location = new Point(97, 1);
      this.newButton_3D_Update.Name = "newButton_3D_Update";
      this.newButton_3D_Update.Size = new Size(19, 17);
      this.newButton_3D_Update.TabIndex = 9;
      this.newButton_3D_Update.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_3D_Update.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_3D_Update.NewClick += new EventHandler(this.newButton_NewClick);
      this.label1.BackColor = Color.FromArgb(229, 238, 248);
      this.label1.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label1.ForeColor = Color.MidnightBlue;
      this.label1.Location = new Point(0, 0);
      this.label1.Name = "label1";
      this.label1.Size = new Size(41, 20);
      this.label1.TabIndex = 4;
      this.label1.Text = "[3D] ";
      this.label1.TextAlign = ContentAlignment.MiddleLeft;
      this.newButton_3D_Del.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_3D_Del.ButtonText = "";
      this.newButton_3D_Del.FlatBorderSize = 0;
      this.newButton_3D_Del.FlatStyle = FlatStyle.Flat;
      this.newButton_3D_Del.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_3D_Del.Image = (Image) Resources.Del;
      this.newButton_3D_Del.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_3D_Del.Location = new Point(72, 1);
      this.newButton_3D_Del.Name = "newButton_3D_Del";
      this.newButton_3D_Del.Size = new Size(19, 17);
      this.newButton_3D_Del.TabIndex = 5;
      this.newButton_3D_Del.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_3D_Del.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_3D_Del.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_3D_Add.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_3D_Add.ButtonText = "";
      this.newButton_3D_Add.FlatBorderSize = 0;
      this.newButton_3D_Add.FlatStyle = FlatStyle.Flat;
      this.newButton_3D_Add.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_3D_Add.Image = (Image) Resources.Add;
      this.newButton_3D_Add.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_3D_Add.Location = new Point(47, 1);
      this.newButton_3D_Add.Name = "newButton_3D_Add";
      this.newButton_3D_Add.Size = new Size(19, 17);
      this.newButton_3D_Add.TabIndex = 5;
      this.newButton_3D_Add.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_3D_Add.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_3D_Add.NewClick += new EventHandler(this.newButton_NewClick);
      this.dataGridView_3D.AllowUserToAddRows = false;
      this.dataGridView_3D.AllowUserToDeleteRows = false;
      this.dataGridView_3D.AllowUserToResizeColumns = false;
      this.dataGridView_3D.AllowUserToResizeRows = false;
      this.dataGridView_3D.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_3D.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_3D.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
      this.dataGridView_3D.BackgroundColor = Color.White;
      this.dataGridView_3D.BorderStyle = BorderStyle.None;
      gridViewCellStyle3.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle3.BackColor = Color.Lavender;
      gridViewCellStyle3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle3.ForeColor = SystemColors.WindowText;
      gridViewCellStyle3.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle3.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle3.WrapMode = DataGridViewTriState.True;
      this.dataGridView_3D.ColumnHeadersDefaultCellStyle = gridViewCellStyle3;
      this.dataGridView_3D.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      gridViewCellStyle4.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle4.BackColor = SystemColors.Window;
      gridViewCellStyle4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle4.ForeColor = SystemColors.ControlText;
      gridViewCellStyle4.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle4.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle4.WrapMode = DataGridViewTriState.False;
      this.dataGridView_3D.DefaultCellStyle = gridViewCellStyle4;
      this.dataGridView_3D.EnableHeadersVisualStyles = false;
      this.dataGridView_3D.Location = new Point(0, 19);
      this.dataGridView_3D.Name = "dataGridView_3D";
      this.dataGridView_3D.RowHeadersVisible = false;
      this.dataGridView_3D.RowTemplate.Height = 23;
      this.dataGridView_3D.Size = new Size(1160, 208);
      this.dataGridView_3D.TabIndex = 1;
      this.dataGridView_3D.CellClick += new DataGridViewCellEventHandler(this.dataGridView_Study_CellClick);
      this.dataGridView_3D.CellMouseUp += new DataGridViewCellMouseEventHandler(this.dataGridView_Study_CellMouseUp);
      this.dataGridView_3D.SelectionChanged += new EventHandler(this.dataGridView_Study_SelectionChanged);
      this.newButton_Analysis.ButtonBackColor = Color.White;
      this.newButton_Analysis.ButtonText = "해석 실행";
      this.newButton_Analysis.Dock = DockStyle.Fill;
      this.newButton_Analysis.FlatBorderSize = 1;
      this.newButton_Analysis.FlatStyle = FlatStyle.Flat;
      this.newButton_Analysis.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Analysis.Image = (Image) Resources.Analysis;
      this.newButton_Analysis.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Analysis.Location = new Point(0, 0);
      this.newButton_Analysis.Name = "newButton_Analysis";
      this.newButton_Analysis.Size = new Size(1160, 26);
      this.newButton_Analysis.TabIndex = 7;
      this.newButton_Analysis.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Analysis.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Analysis.NewClick += new EventHandler(this.newButton_NewClick);
      this.tabPage_Report.Controls.Add((Control) this.splitContainer_Report);
      this.tabPage_Report.Location = new Point(4, 24);
      this.tabPage_Report.Name = "tabPage_Report";
      this.tabPage_Report.Padding = new Padding(3);
      this.tabPage_Report.Size = new Size(1166, 494);
      this.tabPage_Report.TabIndex = 1;
      this.tabPage_Report.Text = "레포트(Report)";
      this.tabPage_Report.UseVisualStyleBackColor = true;
      this.splitContainer_Report.Dock = DockStyle.Fill;
      this.splitContainer_Report.FixedPanel = FixedPanel.Panel2;
      this.splitContainer_Report.IsSplitterFixed = true;
      this.splitContainer_Report.Location = new Point(3, 3);
      this.splitContainer_Report.Name = "splitContainer_Report";
      this.splitContainer_Report.Orientation = Orientation.Horizontal;
      this.splitContainer_Report.Panel1.Controls.Add((Control) this.panel_Report);
      this.splitContainer_Report.Panel2.Controls.Add((Control) this.newButton_Report);
      this.splitContainer_Report.Size = new Size(1160, 488);
      this.splitContainer_Report.SplitterDistance = 458;
      this.splitContainer_Report.TabIndex = 0;
      this.panel_Report.AutoScroll = true;
      this.panel_Report.Controls.Add((Control) this.panel6);
      this.panel_Report.Dock = DockStyle.Fill;
      this.panel_Report.Location = new Point(0, 0);
      this.panel_Report.Name = "panel_Report";
      this.panel_Report.Size = new Size(1160, 458);
      this.panel_Report.TabIndex = 0;
      this.panel6.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
      this.panel6.BackColor = Color.White;
      this.panel6.BorderStyle = BorderStyle.FixedSingle;
      this.panel6.Controls.Add((Control) this.newButton1);
      this.panel6.Controls.Add((Control) this.textBox3);
      this.panel6.Controls.Add((Control) this.textBox2);
      this.panel6.Controls.Add((Control) this.textBox4);
      this.panel6.Controls.Add((Control) this.textBox1);
      this.panel6.Controls.Add((Control) this.dataGridView3);
      this.panel6.Controls.Add((Control) this.checkBox1);
      this.panel6.Controls.Add((Control) this.label17);
      this.panel6.Controls.Add((Control) this.label16);
      this.panel6.Controls.Add((Control) this.label7);
      this.panel6.Controls.Add((Control) this.label15);
      this.panel6.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.panel6.Location = new Point(0, 0);
      this.panel6.Name = "panel6";
      this.panel6.Size = new Size(1160, 86);
      this.panel6.TabIndex = 2;
      this.newButton1.ButtonBackColor = Color.White;
      this.newButton1.ButtonText = "결과 1";
      this.newButton1.FlatBorderSize = 1;
      this.newButton1.FlatStyle = FlatStyle.Flat;
      this.newButton1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton1.Image = (Image) null;
      this.newButton1.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton1.Location = new Point(17, -1);
      this.newButton1.Name = "newButton1";
      this.newButton1.Size = new Size(41, 86);
      this.newButton1.TabIndex = 3;
      this.newButton1.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton1.TextImageRelocation = TextImageRelation.Overlay;
      this.textBox3.Anchor = AnchorStyles.Top | AnchorStyles.Right;
      this.textBox3.BackColor = Color.White;
      this.textBox3.Location = new Point(1024, 21);
      this.textBox3.Name = "textBox3";
      this.textBox3.ReadOnly = true;
      this.textBox3.Size = new Size(135, 23);
      this.textBox3.TabIndex = 1;
      this.textBox2.Anchor = AnchorStyles.Top | AnchorStyles.Right;
      this.textBox2.BackColor = Color.White;
      this.textBox2.Location = new Point(947, 21);
      this.textBox2.Name = "textBox2";
      this.textBox2.ReadOnly = true;
      this.textBox2.Size = new Size(78, 23);
      this.textBox2.TabIndex = 1;
      this.textBox4.BackColor = Color.White;
      this.textBox4.Location = new Point(57, 21);
      this.textBox4.Name = "textBox4";
      this.textBox4.ReadOnly = true;
      this.textBox4.Size = new Size(120, 23);
      this.textBox4.TabIndex = 1;
      this.textBox1.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
      this.textBox1.BackColor = Color.White;
      this.textBox1.Location = new Point(176, 21);
      this.textBox1.Name = "textBox1";
      this.textBox1.ReadOnly = true;
      this.textBox1.Size = new Size(772, 23);
      this.textBox1.TabIndex = 1;
      this.dataGridView3.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView3.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView3.BackgroundColor = Color.White;
      this.dataGridView3.BorderStyle = BorderStyle.None;
      gridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle5.BackColor = Color.Lavender;
      gridViewCellStyle5.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle5.ForeColor = SystemColors.WindowText;
      gridViewCellStyle5.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle5.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle5.WrapMode = DataGridViewTriState.True;
      this.dataGridView3.ColumnHeadersDefaultCellStyle = gridViewCellStyle5;
      this.dataGridView3.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView3.Columns.AddRange((DataGridViewColumn) this.Column4, (DataGridViewColumn) this.Column5, (DataGridViewColumn) this.Column6, (DataGridViewColumn) this.Column7, (DataGridViewColumn) this.Column8, (DataGridViewColumn) this.Column9);
      this.dataGridView3.Enabled = false;
      this.dataGridView3.EnableHeadersVisualStyles = false;
      this.dataGridView3.Location = new Point(57, 43);
      this.dataGridView3.Name = "dataGridView3";
      this.dataGridView3.RowHeadersVisible = false;
      this.dataGridView3.RowTemplate.Height = 23;
      this.dataGridView3.Size = new Size(1102, 42);
      this.dataGridView3.TabIndex = 2;
      this.Column4.HeaderText = "Fill time";
      this.Column4.Name = "Column4";
      this.Column4.ReadOnly = true;
      this.Column5.HeaderText = "Pressure";
      this.Column5.Name = "Column5";
      this.Column5.ReadOnly = true;
      this.Column6.HeaderText = "Column6";
      this.Column6.Name = "Column6";
      this.Column7.HeaderText = "Column7";
      this.Column7.Name = "Column7";
      this.Column8.HeaderText = "Column8";
      this.Column8.Name = "Column8";
      this.Column9.HeaderText = "Column9";
      this.Column9.Name = "Column9";
      this.checkBox1.Location = new Point(2, 36);
      this.checkBox1.Name = "checkBox1";
      this.checkBox1.Size = new Size(14, 14);
      this.checkBox1.TabIndex = 1;
      this.checkBox1.TextAlign = ContentAlignment.MiddleCenter;
      this.checkBox1.UseVisualStyleBackColor = true;
      this.label17.Anchor = AnchorStyles.Top | AnchorStyles.Right;
      this.label17.BackColor = Color.Lavender;
      this.label17.BorderStyle = BorderStyle.FixedSingle;
      this.label17.ForeColor = Color.Black;
      this.label17.Location = new Point(1024, -1);
      this.label17.Name = "label17";
      this.label17.Size = new Size(135, 23);
      this.label17.TabIndex = 1;
      this.label17.Text = "시퀀스";
      this.label17.TextAlign = ContentAlignment.MiddleCenter;
      this.label16.Anchor = AnchorStyles.Top | AnchorStyles.Right;
      this.label16.BackColor = Color.Lavender;
      this.label16.BorderStyle = BorderStyle.FixedSingle;
      this.label16.ForeColor = Color.Black;
      this.label16.Location = new Point(947, -1);
      this.label16.Name = "label16";
      this.label16.Size = new Size(78, 23);
      this.label16.TabIndex = 1;
      this.label16.Text = "메쉬";
      this.label16.TextAlign = ContentAlignment.MiddleCenter;
      this.label7.BackColor = Color.Lavender;
      this.label7.BorderStyle = BorderStyle.FixedSingle;
      this.label7.ForeColor = Color.Black;
      this.label7.Location = new Point(57, -1);
      this.label7.Name = "label7";
      this.label7.Size = new Size(120, 23);
      this.label7.TabIndex = 1;
      this.label7.Text = "제품";
      this.label7.TextAlign = ContentAlignment.MiddleCenter;
      this.label15.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
      this.label15.BackColor = Color.Lavender;
      this.label15.BorderStyle = BorderStyle.FixedSingle;
      this.label15.ForeColor = Color.Black;
      this.label15.Location = new Point(176, -1);
      this.label15.Name = "label15";
      this.label15.Size = new Size(772, 23);
      this.label15.TabIndex = 1;
      this.label15.Text = "스터디";
      this.label15.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Report.ButtonBackColor = Color.White;
      this.newButton_Report.ButtonText = "레포트 출력";
      this.newButton_Report.Dock = DockStyle.Fill;
      this.newButton_Report.FlatBorderSize = 1;
      this.newButton_Report.FlatStyle = FlatStyle.Flat;
      this.newButton_Report.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Report.Image = (Image) Resources.Write;
      this.newButton_Report.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Report.Location = new Point(0, 0);
      this.newButton_Report.Name = "newButton_Report";
      this.newButton_Report.Size = new Size(1160, 26);
      this.newButton_Report.TabIndex = 8;
      this.newButton_Report.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Report.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Report.NewClick += new EventHandler(this.newButton_NewClick);
      this.tabPage_PastCases.Controls.Add((Control) this.splitContainer10);
      this.tabPage_PastCases.Controls.Add((Control) this.panel9);
      this.tabPage_PastCases.Location = new Point(4, 24);
      this.tabPage_PastCases.Name = "tabPage_PastCases";
      this.tabPage_PastCases.Padding = new Padding(3);
      this.tabPage_PastCases.Size = new Size(1166, 494);
      this.tabPage_PastCases.TabIndex = 3;
      this.tabPage_PastCases.Text = "과거 사례(Past Cases)";
      this.tabPage_PastCases.UseVisualStyleBackColor = true;
      this.splitContainer10.Dock = DockStyle.Fill;
      this.splitContainer10.FixedPanel = FixedPanel.Panel2;
      this.splitContainer10.IsSplitterFixed = true;
      this.splitContainer10.Location = new Point(3, 23);
      this.splitContainer10.Name = "splitContainer10";
      this.splitContainer10.Orientation = Orientation.Horizontal;
      this.splitContainer10.Panel1.Controls.Add((Control) this.panel_PastCases);
      this.splitContainer10.Panel2.Controls.Add((Control) this.newButton_Past_Report);
      this.splitContainer10.Size = new Size(1160, 468);
      this.splitContainer10.SplitterDistance = 438;
      this.splitContainer10.TabIndex = 8;
      this.panel_PastCases.AutoScroll = true;
      this.panel_PastCases.Controls.Add((Control) this.panel11);
      this.panel_PastCases.Dock = DockStyle.Fill;
      this.panel_PastCases.Location = new Point(0, 0);
      this.panel_PastCases.Name = "panel_PastCases";
      this.panel_PastCases.Size = new Size(1160, 438);
      this.panel_PastCases.TabIndex = 0;
      this.panel11.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
      this.panel11.BackColor = Color.White;
      this.panel11.BorderStyle = BorderStyle.FixedSingle;
      this.panel11.Controls.Add((Control) this.newButton2);
      this.panel11.Controls.Add((Control) this.textBox5);
      this.panel11.Controls.Add((Control) this.textBox6);
      this.panel11.Controls.Add((Control) this.textBox7);
      this.panel11.Controls.Add((Control) this.textBox8);
      this.panel11.Controls.Add((Control) this.dataGridView4);
      this.panel11.Controls.Add((Control) this.checkBox2);
      this.panel11.Controls.Add((Control) this.label12);
      this.panel11.Controls.Add((Control) this.label13);
      this.panel11.Controls.Add((Control) this.label14);
      this.panel11.Controls.Add((Control) this.label18);
      this.panel11.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.panel11.Location = new Point(0, -1);
      this.panel11.Name = "panel11";
      this.panel11.Size = new Size(1160, 86);
      this.panel11.TabIndex = 3;
      this.newButton2.ButtonBackColor = Color.White;
      this.newButton2.ButtonText = "결과 1";
      this.newButton2.FlatBorderSize = 1;
      this.newButton2.FlatStyle = FlatStyle.Flat;
      this.newButton2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton2.Image = (Image) null;
      this.newButton2.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton2.Location = new Point(17, -1);
      this.newButton2.Name = "newButton2";
      this.newButton2.Size = new Size(41, 86);
      this.newButton2.TabIndex = 3;
      this.newButton2.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton2.TextImageRelocation = TextImageRelation.Overlay;
      this.textBox5.Anchor = AnchorStyles.Top | AnchorStyles.Right;
      this.textBox5.BackColor = Color.White;
      this.textBox5.Location = new Point(1024, 21);
      this.textBox5.Name = "textBox5";
      this.textBox5.ReadOnly = true;
      this.textBox5.Size = new Size(135, 23);
      this.textBox5.TabIndex = 1;
      this.textBox6.Anchor = AnchorStyles.Top | AnchorStyles.Right;
      this.textBox6.BackColor = Color.White;
      this.textBox6.Location = new Point(947, 21);
      this.textBox6.Name = "textBox6";
      this.textBox6.ReadOnly = true;
      this.textBox6.Size = new Size(78, 23);
      this.textBox6.TabIndex = 1;
      this.textBox7.BackColor = Color.White;
      this.textBox7.Location = new Point(57, 21);
      this.textBox7.Name = "textBox7";
      this.textBox7.ReadOnly = true;
      this.textBox7.Size = new Size(120, 23);
      this.textBox7.TabIndex = 1;
      this.textBox8.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
      this.textBox8.BackColor = Color.White;
      this.textBox8.Location = new Point(176, 21);
      this.textBox8.Name = "textBox8";
      this.textBox8.ReadOnly = true;
      this.textBox8.Size = new Size(772, 23);
      this.textBox8.TabIndex = 1;
      this.dataGridView4.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView4.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView4.BackgroundColor = Color.White;
      this.dataGridView4.BorderStyle = BorderStyle.None;
      gridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle6.BackColor = Color.Lavender;
      gridViewCellStyle6.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle6.ForeColor = SystemColors.WindowText;
      gridViewCellStyle6.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle6.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle6.WrapMode = DataGridViewTriState.True;
      this.dataGridView4.ColumnHeadersDefaultCellStyle = gridViewCellStyle6;
      this.dataGridView4.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView4.Columns.AddRange((DataGridViewColumn) this.dataGridViewTextBoxColumn11, (DataGridViewColumn) this.dataGridViewTextBoxColumn12, (DataGridViewColumn) this.dataGridViewTextBoxColumn13, (DataGridViewColumn) this.dataGridViewTextBoxColumn17, (DataGridViewColumn) this.dataGridViewTextBoxColumn18, (DataGridViewColumn) this.dataGridViewTextBoxColumn19);
      this.dataGridView4.Enabled = false;
      this.dataGridView4.EnableHeadersVisualStyles = false;
      this.dataGridView4.Location = new Point(57, 43);
      this.dataGridView4.Name = "dataGridView4";
      this.dataGridView4.RowHeadersVisible = false;
      this.dataGridView4.RowTemplate.Height = 23;
      this.dataGridView4.Size = new Size(1102, 42);
      this.dataGridView4.TabIndex = 2;
      this.dataGridViewTextBoxColumn11.HeaderText = "Fill time";
      this.dataGridViewTextBoxColumn11.Name = "dataGridViewTextBoxColumn11";
      this.dataGridViewTextBoxColumn11.ReadOnly = true;
      this.dataGridViewTextBoxColumn12.HeaderText = "Pressure";
      this.dataGridViewTextBoxColumn12.Name = "dataGridViewTextBoxColumn12";
      this.dataGridViewTextBoxColumn12.ReadOnly = true;
      this.dataGridViewTextBoxColumn13.HeaderText = "Column6";
      this.dataGridViewTextBoxColumn13.Name = "dataGridViewTextBoxColumn13";
      this.dataGridViewTextBoxColumn17.HeaderText = "Column7";
      this.dataGridViewTextBoxColumn17.Name = "dataGridViewTextBoxColumn17";
      this.dataGridViewTextBoxColumn18.HeaderText = "Column8";
      this.dataGridViewTextBoxColumn18.Name = "dataGridViewTextBoxColumn18";
      this.dataGridViewTextBoxColumn19.HeaderText = "Column9";
      this.dataGridViewTextBoxColumn19.Name = "dataGridViewTextBoxColumn19";
      this.checkBox2.Location = new Point(2, 36);
      this.checkBox2.Name = "checkBox2";
      this.checkBox2.Size = new Size(14, 14);
      this.checkBox2.TabIndex = 1;
      this.checkBox2.TextAlign = ContentAlignment.MiddleCenter;
      this.checkBox2.UseVisualStyleBackColor = true;
      this.label12.Anchor = AnchorStyles.Top | AnchorStyles.Right;
      this.label12.BackColor = Color.Lavender;
      this.label12.BorderStyle = BorderStyle.FixedSingle;
      this.label12.ForeColor = Color.Black;
      this.label12.Location = new Point(1024, -1);
      this.label12.Name = "label12";
      this.label12.Size = new Size(135, 23);
      this.label12.TabIndex = 1;
      this.label12.Text = "시퀀스";
      this.label12.TextAlign = ContentAlignment.MiddleCenter;
      this.label13.Anchor = AnchorStyles.Top | AnchorStyles.Right;
      this.label13.BackColor = Color.Lavender;
      this.label13.BorderStyle = BorderStyle.FixedSingle;
      this.label13.ForeColor = Color.Black;
      this.label13.Location = new Point(947, -1);
      this.label13.Name = "label13";
      this.label13.Size = new Size(78, 23);
      this.label13.TabIndex = 1;
      this.label13.Text = "메쉬";
      this.label13.TextAlign = ContentAlignment.MiddleCenter;
      this.label14.BackColor = Color.Lavender;
      this.label14.BorderStyle = BorderStyle.FixedSingle;
      this.label14.ForeColor = Color.Black;
      this.label14.Location = new Point(57, -1);
      this.label14.Name = "label14";
      this.label14.Size = new Size(120, 23);
      this.label14.TabIndex = 1;
      this.label14.Text = "제품";
      this.label14.TextAlign = ContentAlignment.MiddleCenter;
      this.label18.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
      this.label18.BackColor = Color.Lavender;
      this.label18.BorderStyle = BorderStyle.FixedSingle;
      this.label18.ForeColor = Color.Black;
      this.label18.Location = new Point(176, -1);
      this.label18.Name = "label18";
      this.label18.Size = new Size(772, 23);
      this.label18.TabIndex = 1;
      this.label18.Text = "스터디";
      this.label18.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Past_Report.ButtonBackColor = Color.White;
      this.newButton_Past_Report.ButtonText = "레포트 출력";
      this.newButton_Past_Report.Dock = DockStyle.Fill;
      this.newButton_Past_Report.FlatBorderSize = 1;
      this.newButton_Past_Report.FlatStyle = FlatStyle.Flat;
      this.newButton_Past_Report.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Past_Report.Image = (Image) Resources.Write;
      this.newButton_Past_Report.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Past_Report.Location = new Point(0, 0);
      this.newButton_Past_Report.Name = "newButton_Past_Report";
      this.newButton_Past_Report.Size = new Size(1160, 26);
      this.newButton_Past_Report.TabIndex = 8;
      this.newButton_Past_Report.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Past_Report.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Past_Report.NewClick += new EventHandler(this.newButton_NewClick);
      this.panel9.BackColor = Color.FromArgb(229, 238, 248);
      this.panel9.BorderStyle = BorderStyle.FixedSingle;
      this.panel9.Controls.Add((Control) this.label11);
      this.panel9.Controls.Add((Control) this.newButton_Past_Del);
      this.panel9.Controls.Add((Control) this.newButton_Past_Add);
      this.panel9.Dock = DockStyle.Top;
      this.panel9.Location = new Point(3, 3);
      this.panel9.Name = "panel9";
      this.panel9.Size = new Size(1160, 20);
      this.panel9.TabIndex = 7;
      this.label11.BackColor = Color.FromArgb(229, 238, 248);
      this.label11.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label11.ForeColor = Color.MidnightBlue;
      this.label11.Location = new Point(0, 0);
      this.label11.Name = "label11";
      this.label11.Size = new Size(72, 20);
      this.label11.TabIndex = 4;
      this.label11.Text = "[Past Cases] ";
      this.label11.TextAlign = ContentAlignment.MiddleLeft;
      this.newButton_Past_Del.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Past_Del.ButtonText = "";
      this.newButton_Past_Del.FlatBorderSize = 0;
      this.newButton_Past_Del.FlatStyle = FlatStyle.Flat;
      this.newButton_Past_Del.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Past_Del.Image = (Image) Resources.Del;
      this.newButton_Past_Del.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Past_Del.Location = new Point(98, 1);
      this.newButton_Past_Del.Name = "newButton_Past_Del";
      this.newButton_Past_Del.Size = new Size(19, 17);
      this.newButton_Past_Del.TabIndex = 5;
      this.newButton_Past_Del.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Past_Del.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Past_Del.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_Past_Add.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_Past_Add.ButtonText = "";
      this.newButton_Past_Add.FlatBorderSize = 0;
      this.newButton_Past_Add.FlatStyle = FlatStyle.Flat;
      this.newButton_Past_Add.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Past_Add.Image = (Image) Resources.Add;
      this.newButton_Past_Add.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Past_Add.Location = new Point(73, 1);
      this.newButton_Past_Add.Name = "newButton_Past_Add";
      this.newButton_Past_Add.Size = new Size(19, 17);
      this.newButton_Past_Add.TabIndex = 5;
      this.newButton_Past_Add.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Past_Add.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Past_Add.NewClick += new EventHandler(this.newButton_NewClick);
      this.tabPage_Comparison.Controls.Add((Control) this.splitContainer7);
      this.tabPage_Comparison.Location = new Point(4, 24);
      this.tabPage_Comparison.Name = "tabPage_Comparison";
      this.tabPage_Comparison.Padding = new Padding(3);
      this.tabPage_Comparison.Size = new Size(1166, 494);
      this.tabPage_Comparison.TabIndex = 2;
      this.tabPage_Comparison.Text = "비교 레포트(Comparison)";
      this.tabPage_Comparison.UseVisualStyleBackColor = true;
      this.splitContainer7.Dock = DockStyle.Fill;
      this.splitContainer7.FixedPanel = FixedPanel.Panel2;
      this.splitContainer7.IsSplitterFixed = true;
      this.splitContainer7.Location = new Point(3, 3);
      this.splitContainer7.Name = "splitContainer7";
      this.splitContainer7.Orientation = Orientation.Horizontal;
      this.splitContainer7.Panel1.Controls.Add((Control) this.splitContainer9);
      this.splitContainer7.Panel2.Controls.Add((Control) this.newButton_Comparison);
      this.splitContainer7.Size = new Size(1160, 488);
      this.splitContainer7.SplitterDistance = 458;
      this.splitContainer7.TabIndex = 9;
      this.splitContainer9.Dock = DockStyle.Fill;
      this.splitContainer9.Location = new Point(0, 0);
      this.splitContainer9.Name = "splitContainer9";
      this.splitContainer9.Orientation = Orientation.Horizontal;
      this.splitContainer9.Panel1.Controls.Add((Control) this.panel5);
      this.splitContainer9.Panel1.Controls.Add((Control) this.dataGridView_List);
      this.splitContainer9.Panel2.Controls.Add((Control) this.tableLayoutPanel_CMPR);
      this.splitContainer9.Panel2.Controls.Add((Control) this.panel7);
      this.splitContainer9.Size = new Size(1160, 458);
      this.splitContainer9.SplitterDistance = 342;
      this.splitContainer9.TabIndex = 6;
      this.panel5.BackColor = Color.FromArgb(229, 238, 248);
      this.panel5.BorderStyle = BorderStyle.FixedSingle;
      this.panel5.Controls.Add((Control) this.label2);
      this.panel5.Dock = DockStyle.Top;
      this.panel5.Location = new Point(0, 0);
      this.panel5.Name = "panel5";
      this.panel5.Size = new Size(1160, 20);
      this.panel5.TabIndex = 6;
      this.label2.BackColor = Color.FromArgb(229, 238, 248);
      this.label2.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label2.ForeColor = Color.MidnightBlue;
      this.label2.Location = new Point(0, 0);
      this.label2.Name = "label2";
      this.label2.Size = new Size(41, 20);
      this.label2.TabIndex = 4;
      this.label2.Text = "[List] ";
      this.label2.TextAlign = ContentAlignment.MiddleLeft;
      this.dataGridView_List.AllowDrop = true;
      this.dataGridView_List.AllowUserToAddRows = false;
      this.dataGridView_List.AllowUserToDeleteRows = false;
      this.dataGridView_List.AllowUserToResizeColumns = false;
      this.dataGridView_List.AllowUserToResizeRows = false;
      this.dataGridView_List.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_List.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_List.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
      this.dataGridView_List.BackgroundColor = Color.White;
      this.dataGridView_List.BorderStyle = BorderStyle.None;
      gridViewCellStyle7.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle7.BackColor = Color.Lavender;
      gridViewCellStyle7.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle7.ForeColor = SystemColors.WindowText;
      gridViewCellStyle7.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle7.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle7.WrapMode = DataGridViewTriState.True;
      this.dataGridView_List.ColumnHeadersDefaultCellStyle = gridViewCellStyle7;
      this.dataGridView_List.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_List.Columns.AddRange((DataGridViewColumn) this.Column_Template, (DataGridViewColumn) this.Column_Study, (DataGridViewColumn) this.Column_Mesh, (DataGridViewColumn) this.Column_Sequence);
      gridViewCellStyle8.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle8.BackColor = SystemColors.Window;
      gridViewCellStyle8.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle8.ForeColor = SystemColors.ControlText;
      gridViewCellStyle8.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle8.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle8.WrapMode = DataGridViewTriState.False;
      this.dataGridView_List.DefaultCellStyle = gridViewCellStyle8;
      this.dataGridView_List.EnableHeadersVisualStyles = false;
      this.dataGridView_List.Location = new Point(0, 19);
      this.dataGridView_List.Name = "dataGridView_List";
      this.dataGridView_List.RowHeadersVisible = false;
      this.dataGridView_List.RowTemplate.Height = 23;
      this.dataGridView_List.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
      this.dataGridView_List.Size = new Size(1160, 322);
      this.dataGridView_List.TabIndex = 0;
      this.dataGridView_List.DragDrop += new DragEventHandler(this.datagridView_Compare_DragDrop);
      this.dataGridView_List.DragEnter += new DragEventHandler(this.datagridView_Compare_DragEnter);
      this.dataGridView_List.MouseDown += new MouseEventHandler(this.dataGridView_Compare_MouseDown);
      this.Column_Template.FillWeight = 70f;
      this.Column_Template.HeaderText = "템플릿";
      this.Column_Template.Name = "Column_Template";
      this.Column_Template.ReadOnly = true;
      this.Column_Template.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.Column_Study.FillWeight = 341.2725f;
      this.Column_Study.HeaderText = "스터디";
      this.Column_Study.Name = "Column_Study";
      this.Column_Study.ReadOnly = true;
      this.Column_Study.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.Column_Mesh.FillWeight = 58.45266f;
      this.Column_Mesh.HeaderText = "메쉬";
      this.Column_Mesh.Name = "Column_Mesh";
      this.Column_Mesh.ReadOnly = true;
      this.Column_Mesh.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.Column_Sequence.FillWeight = 63.90414f;
      this.Column_Sequence.HeaderText = "시퀀스";
      this.Column_Sequence.Name = "Column_Sequence";
      this.Column_Sequence.ReadOnly = true;
      this.Column_Sequence.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.tableLayoutPanel_CMPR.ColumnCount = 3;
      this.tableLayoutPanel_CMPR.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33333f));
      this.tableLayoutPanel_CMPR.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33333f));
      this.tableLayoutPanel_CMPR.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33333f));
      this.tableLayoutPanel_CMPR.Controls.Add((Control) this.panel13, 2, 0);
      this.tableLayoutPanel_CMPR.Controls.Add((Control) this.panel8, 1, 0);
      this.tableLayoutPanel_CMPR.Controls.Add((Control) this.panel_Compare1, 0, 0);
      this.tableLayoutPanel_CMPR.Dock = DockStyle.Fill;
      this.tableLayoutPanel_CMPR.Location = new Point(0, 20);
      this.tableLayoutPanel_CMPR.Name = "tableLayoutPanel_CMPR";
      this.tableLayoutPanel_CMPR.RowCount = 1;
      this.tableLayoutPanel_CMPR.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
      this.tableLayoutPanel_CMPR.Size = new Size(1160, 92);
      this.tableLayoutPanel_CMPR.TabIndex = 8;
      this.panel13.BorderStyle = BorderStyle.FixedSingle;
      this.panel13.Controls.Add((Control) this.label10);
      this.panel13.Controls.Add((Control) this.dataGridView5);
      this.panel13.Location = new Point(775, 3);
      this.panel13.Name = "panel13";
      this.panel13.Size = new Size(380, 86);
      this.panel13.TabIndex = 11;
      this.label10.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
      this.label10.BackColor = Color.FromArgb(229, 238, 248);
      this.label10.BorderStyle = BorderStyle.FixedSingle;
      this.label10.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label10.ForeColor = Color.MidnightBlue;
      this.label10.Location = new Point(-1, -1);
      this.label10.Name = "label10";
      this.label10.Size = new Size(380, 20);
      this.label10.TabIndex = 10;
      this.label10.Text = "[Compare3] ";
      this.label10.TextAlign = ContentAlignment.MiddleCenter;
      this.dataGridView5.AllowUserToAddRows = false;
      this.dataGridView5.AllowUserToDeleteRows = false;
      this.dataGridView5.AllowUserToResizeColumns = false;
      this.dataGridView5.AllowUserToResizeRows = false;
      this.dataGridView5.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView5.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView5.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
      this.dataGridView5.BackgroundColor = Color.White;
      this.dataGridView5.BorderStyle = BorderStyle.None;
      gridViewCellStyle9.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle9.BackColor = Color.Lavender;
      gridViewCellStyle9.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle9.ForeColor = SystemColors.WindowText;
      gridViewCellStyle9.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle9.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle9.WrapMode = DataGridViewTriState.True;
      this.dataGridView5.ColumnHeadersDefaultCellStyle = gridViewCellStyle9;
      this.dataGridView5.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView5.Columns.AddRange((DataGridViewColumn) this.dataGridViewTextBoxColumn14, (DataGridViewColumn) this.dataGridViewTextBoxColumn15, (DataGridViewColumn) this.dataGridViewTextBoxColumn16);
      gridViewCellStyle10.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle10.BackColor = SystemColors.Window;
      gridViewCellStyle10.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle10.ForeColor = SystemColors.ControlText;
      gridViewCellStyle10.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle10.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle10.WrapMode = DataGridViewTriState.False;
      this.dataGridView5.DefaultCellStyle = gridViewCellStyle10;
      this.dataGridView5.EnableHeadersVisualStyles = false;
      this.dataGridView5.Location = new Point(-1, 18);
      this.dataGridView5.Name = "dataGridView5";
      this.dataGridView5.RowHeadersVisible = false;
      this.dataGridView5.RowTemplate.Height = 23;
      this.dataGridView5.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
      this.dataGridView5.Size = new Size(380, 68);
      this.dataGridView5.TabIndex = 8;
      this.dataGridViewTextBoxColumn14.FillWeight = 341.2725f;
      this.dataGridViewTextBoxColumn14.HeaderText = "스터디";
      this.dataGridViewTextBoxColumn14.Name = "dataGridViewTextBoxColumn14";
      this.dataGridViewTextBoxColumn14.ReadOnly = true;
      this.dataGridViewTextBoxColumn14.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.dataGridViewTextBoxColumn15.FillWeight = 58.45266f;
      this.dataGridViewTextBoxColumn15.HeaderText = "메쉬";
      this.dataGridViewTextBoxColumn15.Name = "dataGridViewTextBoxColumn15";
      this.dataGridViewTextBoxColumn15.ReadOnly = true;
      this.dataGridViewTextBoxColumn15.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.dataGridViewTextBoxColumn16.FillWeight = 63.90414f;
      this.dataGridViewTextBoxColumn16.HeaderText = "시퀀스";
      this.dataGridViewTextBoxColumn16.Name = "dataGridViewTextBoxColumn16";
      this.dataGridViewTextBoxColumn16.ReadOnly = true;
      this.dataGridViewTextBoxColumn16.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.panel8.BorderStyle = BorderStyle.FixedSingle;
      this.panel8.Controls.Add((Control) this.label9);
      this.panel8.Controls.Add((Control) this.dataGridView2);
      this.panel8.Location = new Point(389, 3);
      this.panel8.Name = "panel8";
      this.panel8.Size = new Size(380, 86);
      this.panel8.TabIndex = 10;
      this.label9.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
      this.label9.BackColor = Color.FromArgb(229, 238, 248);
      this.label9.BorderStyle = BorderStyle.FixedSingle;
      this.label9.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label9.ForeColor = Color.MidnightBlue;
      this.label9.Location = new Point(-1, -1);
      this.label9.Name = "label9";
      this.label9.Size = new Size(380, 20);
      this.label9.TabIndex = 9;
      this.label9.Text = "[Compare2] ";
      this.label9.TextAlign = ContentAlignment.MiddleCenter;
      this.dataGridView2.AllowUserToAddRows = false;
      this.dataGridView2.AllowUserToDeleteRows = false;
      this.dataGridView2.AllowUserToResizeColumns = false;
      this.dataGridView2.AllowUserToResizeRows = false;
      this.dataGridView2.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView2.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView2.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
      this.dataGridView2.BackgroundColor = Color.White;
      this.dataGridView2.BorderStyle = BorderStyle.None;
      gridViewCellStyle11.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle11.BackColor = Color.Lavender;
      gridViewCellStyle11.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle11.ForeColor = SystemColors.WindowText;
      gridViewCellStyle11.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle11.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle11.WrapMode = DataGridViewTriState.True;
      this.dataGridView2.ColumnHeadersDefaultCellStyle = gridViewCellStyle11;
      this.dataGridView2.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView2.Columns.AddRange((DataGridViewColumn) this.dataGridViewTextBoxColumn5, (DataGridViewColumn) this.dataGridViewTextBoxColumn6, (DataGridViewColumn) this.dataGridViewTextBoxColumn10);
      gridViewCellStyle12.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle12.BackColor = SystemColors.Window;
      gridViewCellStyle12.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle12.ForeColor = SystemColors.ControlText;
      gridViewCellStyle12.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle12.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle12.WrapMode = DataGridViewTriState.False;
      this.dataGridView2.DefaultCellStyle = gridViewCellStyle12;
      this.dataGridView2.EnableHeadersVisualStyles = false;
      this.dataGridView2.Location = new Point(-1, 18);
      this.dataGridView2.Name = "dataGridView2";
      this.dataGridView2.RowHeadersVisible = false;
      this.dataGridView2.RowTemplate.Height = 23;
      this.dataGridView2.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
      this.dataGridView2.Size = new Size(380, 68);
      this.dataGridView2.TabIndex = 8;
      this.dataGridViewTextBoxColumn5.FillWeight = 341.2725f;
      this.dataGridViewTextBoxColumn5.HeaderText = "스터디";
      this.dataGridViewTextBoxColumn5.Name = "dataGridViewTextBoxColumn5";
      this.dataGridViewTextBoxColumn5.ReadOnly = true;
      this.dataGridViewTextBoxColumn5.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.dataGridViewTextBoxColumn6.FillWeight = 58.45266f;
      this.dataGridViewTextBoxColumn6.HeaderText = "메쉬";
      this.dataGridViewTextBoxColumn6.Name = "dataGridViewTextBoxColumn6";
      this.dataGridViewTextBoxColumn6.ReadOnly = true;
      this.dataGridViewTextBoxColumn6.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.dataGridViewTextBoxColumn10.FillWeight = 63.90414f;
      this.dataGridViewTextBoxColumn10.HeaderText = "시퀀스";
      this.dataGridViewTextBoxColumn10.Name = "dataGridViewTextBoxColumn10";
      this.dataGridViewTextBoxColumn10.ReadOnly = true;
      this.dataGridViewTextBoxColumn10.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.panel_Compare1.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Compare1.Controls.Add((Control) this.dataGridView1);
      this.panel_Compare1.Controls.Add((Control) this.label8);
      this.panel_Compare1.Location = new Point(3, 3);
      this.panel_Compare1.Name = "panel_Compare1";
      this.panel_Compare1.Size = new Size(380, 86);
      this.panel_Compare1.TabIndex = 9;
      this.dataGridView1.AllowUserToAddRows = false;
      this.dataGridView1.AllowUserToDeleteRows = false;
      this.dataGridView1.AllowUserToResizeColumns = false;
      this.dataGridView1.AllowUserToResizeRows = false;
      this.dataGridView1.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView1.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
      this.dataGridView1.BackgroundColor = Color.White;
      this.dataGridView1.BorderStyle = BorderStyle.None;
      gridViewCellStyle13.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle13.BackColor = Color.Lavender;
      gridViewCellStyle13.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle13.ForeColor = SystemColors.WindowText;
      gridViewCellStyle13.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle13.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle13.WrapMode = DataGridViewTriState.True;
      this.dataGridView1.ColumnHeadersDefaultCellStyle = gridViewCellStyle13;
      this.dataGridView1.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView1.Columns.AddRange((DataGridViewColumn) this.dataGridViewTextBoxColumn7, (DataGridViewColumn) this.dataGridViewTextBoxColumn8, (DataGridViewColumn) this.dataGridViewTextBoxColumn9);
      gridViewCellStyle14.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle14.BackColor = SystemColors.Window;
      gridViewCellStyle14.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle14.ForeColor = SystemColors.ControlText;
      gridViewCellStyle14.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle14.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle14.WrapMode = DataGridViewTriState.False;
      this.dataGridView1.DefaultCellStyle = gridViewCellStyle14;
      this.dataGridView1.EnableHeadersVisualStyles = false;
      this.dataGridView1.Location = new Point(-1, 18);
      this.dataGridView1.Name = "dataGridView1";
      this.dataGridView1.RowHeadersVisible = false;
      this.dataGridView1.RowTemplate.Height = 23;
      this.dataGridView1.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
      this.dataGridView1.Size = new Size(380, 68);
      this.dataGridView1.TabIndex = 8;
      this.dataGridViewTextBoxColumn7.FillWeight = 341.2725f;
      this.dataGridViewTextBoxColumn7.HeaderText = "스터디";
      this.dataGridViewTextBoxColumn7.Name = "dataGridViewTextBoxColumn7";
      this.dataGridViewTextBoxColumn7.ReadOnly = true;
      this.dataGridViewTextBoxColumn7.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.dataGridViewTextBoxColumn8.FillWeight = 58.45266f;
      this.dataGridViewTextBoxColumn8.HeaderText = "메쉬";
      this.dataGridViewTextBoxColumn8.Name = "dataGridViewTextBoxColumn8";
      this.dataGridViewTextBoxColumn8.ReadOnly = true;
      this.dataGridViewTextBoxColumn8.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.dataGridViewTextBoxColumn9.FillWeight = 63.90414f;
      this.dataGridViewTextBoxColumn9.HeaderText = "시퀀스";
      this.dataGridViewTextBoxColumn9.Name = "dataGridViewTextBoxColumn9";
      this.dataGridViewTextBoxColumn9.ReadOnly = true;
      this.dataGridViewTextBoxColumn9.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.label8.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
      this.label8.BackColor = Color.FromArgb(229, 238, 248);
      this.label8.BorderStyle = BorderStyle.FixedSingle;
      this.label8.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label8.ForeColor = Color.MidnightBlue;
      this.label8.Location = new Point(-1, -1);
      this.label8.Name = "label8";
      this.label8.Size = new Size(380, 20);
      this.label8.TabIndex = 4;
      this.label8.Text = "[Compare1] ";
      this.label8.TextAlign = ContentAlignment.MiddleCenter;
      this.panel7.BackColor = Color.FromArgb(229, 238, 248);
      this.panel7.BorderStyle = BorderStyle.FixedSingle;
      this.panel7.Controls.Add((Control) this.label_Template);
      this.panel7.Controls.Add((Control) this.radioButton_Case3);
      this.panel7.Controls.Add((Control) this.radioButton_Case2);
      this.panel7.Controls.Add((Control) this.label4);
      this.panel7.Dock = DockStyle.Top;
      this.panel7.Location = new Point(0, 0);
      this.panel7.Name = "panel7";
      this.panel7.Size = new Size(1160, 20);
      this.panel7.TabIndex = 7;
      this.label_Template.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Template.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Template.ForeColor = Color.Red;
      this.label_Template.Location = new Point(261, -1);
      this.label_Template.Name = "label_Template";
      this.label_Template.Size = new Size(132, 20);
      this.label_Template.TabIndex = 8;
      this.label_Template.Text = "[HDSolutions]";
      this.label_Template.TextAlign = ContentAlignment.MiddleLeft;
      this.label_Template.Visible = false;
      this.radioButton_Case3.AutoSize = true;
      this.radioButton_Case3.Location = new Point(188, 1);
      this.radioButton_Case3.Name = "radioButton_Case3";
      this.radioButton_Case3.Size = new Size(70, 19);
      this.radioButton_Case3.TabIndex = 7;
      this.radioButton_Case3.Text = "3개 비교";
      this.radioButton_Case3.UseVisualStyleBackColor = true;
      this.radioButton_Case2.AutoSize = true;
      this.radioButton_Case2.Checked = true;
      this.radioButton_Case2.Location = new Point(112, 1);
      this.radioButton_Case2.Name = "radioButton_Case2";
      this.radioButton_Case2.Size = new Size(70, 19);
      this.radioButton_Case2.TabIndex = 6;
      this.radioButton_Case2.TabStop = true;
      this.radioButton_Case2.Text = "2개 비교";
      this.radioButton_Case2.UseVisualStyleBackColor = true;
      this.radioButton_Case2.CheckedChanged += new EventHandler(this.radioButton_Case2_CheckedChanged);
      this.label4.BackColor = Color.FromArgb(229, 238, 248);
      this.label4.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label4.ForeColor = Color.MidnightBlue;
      this.label4.Location = new Point(0, 0);
      this.label4.Name = "label4";
      this.label4.Size = new Size(107, 20);
      this.label4.TabIndex = 4;
      this.label4.Text = "[Comparison List] ";
      this.label4.TextAlign = ContentAlignment.MiddleLeft;
      this.newButton_Comparison.ButtonBackColor = Color.White;
      this.newButton_Comparison.ButtonText = "레포트 출력";
      this.newButton_Comparison.Dock = DockStyle.Fill;
      this.newButton_Comparison.FlatBorderSize = 1;
      this.newButton_Comparison.FlatStyle = FlatStyle.Flat;
      this.newButton_Comparison.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Comparison.Image = (Image) Resources.Write;
      this.newButton_Comparison.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Comparison.Location = new Point(0, 0);
      this.newButton_Comparison.Name = "newButton_Comparison";
      this.newButton_Comparison.Size = new Size(1160, 26);
      this.newButton_Comparison.TabIndex = 9;
      this.newButton_Comparison.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Comparison.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Comparison.NewClick += new EventHandler(this.newButton_NewClick);
      this.tabPage_AI_Study.Controls.Add((Control) this.splitContainer12);
      this.tabPage_AI_Study.Location = new Point(4, 24);
      this.tabPage_AI_Study.Name = "tabPage_AI_Study";
      this.tabPage_AI_Study.Padding = new Padding(3);
      this.tabPage_AI_Study.Size = new Size(1166, 494);
      this.tabPage_AI_Study.TabIndex = 4;
      this.tabPage_AI_Study.Text = "스터디(Study)";
      this.tabPage_AI_Study.UseVisualStyleBackColor = true;
      this.splitContainer12.Dock = DockStyle.Fill;
      this.splitContainer12.FixedPanel = FixedPanel.Panel2;
      this.splitContainer12.IsSplitterFixed = true;
      this.splitContainer12.Location = new Point(3, 3);
      this.splitContainer12.Name = "splitContainer12";
      this.splitContainer12.Orientation = Orientation.Horizontal;
      this.splitContainer12.Panel1.Controls.Add((Control) this.splitContainer11);
      this.splitContainer12.Panel2.Controls.Add((Control) this.newButton_AISolution);
      this.splitContainer12.Size = new Size(1160, 488);
      this.splitContainer12.SplitterDistance = 458;
      this.splitContainer12.TabIndex = 9;
      this.splitContainer11.Dock = DockStyle.Fill;
      this.splitContainer11.IsSplitterFixed = true;
      this.splitContainer11.Location = new Point(0, 0);
      this.splitContainer11.Name = "splitContainer11";
      this.splitContainer11.Orientation = Orientation.Horizontal;
      this.splitContainer11.Panel1.Controls.Add((Control) this.panel10);
      this.splitContainer11.Panel1.Controls.Add((Control) this.dataGridView_AI_Dual);
      this.splitContainer11.Panel2.Controls.Add((Control) this.panel12);
      this.splitContainer11.Panel2.Controls.Add((Control) this.dataGridView_AI_3D);
      this.splitContainer11.Size = new Size(1160, 458);
      this.splitContainer11.SplitterDistance = 225;
      this.splitContainer11.TabIndex = 7;
      this.panel10.BackColor = Color.FromArgb(229, 238, 248);
      this.panel10.BorderStyle = BorderStyle.FixedSingle;
      this.panel10.Controls.Add((Control) this.label20);
      this.panel10.Dock = DockStyle.Top;
      this.panel10.Location = new Point(0, 0);
      this.panel10.Name = "panel10";
      this.panel10.Size = new Size(1160, 20);
      this.panel10.TabIndex = 6;
      this.label20.BackColor = Color.FromArgb(229, 238, 248);
      this.label20.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label20.ForeColor = Color.MidnightBlue;
      this.label20.Location = new Point(0, 0);
      this.label20.Name = "label20";
      this.label20.Size = new Size(41, 20);
      this.label20.TabIndex = 4;
      this.label20.Text = "[Dual] ";
      this.label20.TextAlign = ContentAlignment.MiddleLeft;
      this.dataGridView_AI_Dual.AllowUserToAddRows = false;
      this.dataGridView_AI_Dual.AllowUserToDeleteRows = false;
      this.dataGridView_AI_Dual.AllowUserToResizeColumns = false;
      this.dataGridView_AI_Dual.AllowUserToResizeRows = false;
      this.dataGridView_AI_Dual.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_AI_Dual.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_AI_Dual.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
      this.dataGridView_AI_Dual.BackgroundColor = Color.White;
      this.dataGridView_AI_Dual.BorderStyle = BorderStyle.None;
      gridViewCellStyle15.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle15.BackColor = Color.Lavender;
      gridViewCellStyle15.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle15.ForeColor = SystemColors.WindowText;
      gridViewCellStyle15.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle15.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle15.WrapMode = DataGridViewTriState.True;
      this.dataGridView_AI_Dual.ColumnHeadersDefaultCellStyle = gridViewCellStyle15;
      this.dataGridView_AI_Dual.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      gridViewCellStyle16.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle16.BackColor = SystemColors.Window;
      gridViewCellStyle16.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle16.ForeColor = SystemColors.ControlText;
      gridViewCellStyle16.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle16.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle16.WrapMode = DataGridViewTriState.False;
      this.dataGridView_AI_Dual.DefaultCellStyle = gridViewCellStyle16;
      this.dataGridView_AI_Dual.EnableHeadersVisualStyles = false;
      this.dataGridView_AI_Dual.Location = new Point(0, 19);
      this.dataGridView_AI_Dual.Name = "dataGridView_AI_Dual";
      this.dataGridView_AI_Dual.RowHeadersVisible = false;
      this.dataGridView_AI_Dual.RowTemplate.Height = 23;
      this.dataGridView_AI_Dual.Size = new Size(1160, 205);
      this.dataGridView_AI_Dual.TabIndex = 0;
      this.dataGridView_AI_Dual.CellClick += new DataGridViewCellEventHandler(this.dataGridView_AI_Study_CellClick);
      this.dataGridView_AI_Dual.SelectionChanged += new EventHandler(this.dataGridView_AI_Study_SelectionChanged);
      this.panel12.BackColor = Color.FromArgb(229, 238, 248);
      this.panel12.BorderStyle = BorderStyle.FixedSingle;
      this.panel12.Controls.Add((Control) this.label21);
      this.panel12.Dock = DockStyle.Top;
      this.panel12.Location = new Point(0, 0);
      this.panel12.Name = "panel12";
      this.panel12.Size = new Size(1160, 20);
      this.panel12.TabIndex = 7;
      this.label21.BackColor = Color.FromArgb(229, 238, 248);
      this.label21.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label21.ForeColor = Color.MidnightBlue;
      this.label21.Location = new Point(0, 0);
      this.label21.Name = "label21";
      this.label21.Size = new Size(41, 20);
      this.label21.TabIndex = 4;
      this.label21.Text = "[3D] ";
      this.label21.TextAlign = ContentAlignment.MiddleLeft;
      this.dataGridView_AI_3D.AllowUserToAddRows = false;
      this.dataGridView_AI_3D.AllowUserToDeleteRows = false;
      this.dataGridView_AI_3D.AllowUserToResizeColumns = false;
      this.dataGridView_AI_3D.AllowUserToResizeRows = false;
      this.dataGridView_AI_3D.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_AI_3D.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_AI_3D.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
      this.dataGridView_AI_3D.BackgroundColor = Color.White;
      this.dataGridView_AI_3D.BorderStyle = BorderStyle.None;
      gridViewCellStyle17.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle17.BackColor = Color.Lavender;
      gridViewCellStyle17.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle17.ForeColor = SystemColors.WindowText;
      gridViewCellStyle17.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle17.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle17.WrapMode = DataGridViewTriState.True;
      this.dataGridView_AI_3D.ColumnHeadersDefaultCellStyle = gridViewCellStyle17;
      this.dataGridView_AI_3D.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      gridViewCellStyle18.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle18.BackColor = SystemColors.Window;
      gridViewCellStyle18.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle18.ForeColor = SystemColors.ControlText;
      gridViewCellStyle18.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle18.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle18.WrapMode = DataGridViewTriState.False;
      this.dataGridView_AI_3D.DefaultCellStyle = gridViewCellStyle18;
      this.dataGridView_AI_3D.EnableHeadersVisualStyles = false;
      this.dataGridView_AI_3D.Location = new Point(0, 19);
      this.dataGridView_AI_3D.Name = "dataGridView_AI_3D";
      this.dataGridView_AI_3D.RowHeadersVisible = false;
      this.dataGridView_AI_3D.RowTemplate.Height = 23;
      this.dataGridView_AI_3D.Size = new Size(1160, 209);
      this.dataGridView_AI_3D.TabIndex = 1;
      this.dataGridView_AI_3D.CellClick += new DataGridViewCellEventHandler(this.dataGridView_AI_Study_CellClick);
      this.dataGridView_AI_3D.SelectionChanged += new EventHandler(this.dataGridView_AI_Study_SelectionChanged);
      this.newButton_AISolution.ButtonBackColor = Color.White;
      this.newButton_AISolution.ButtonText = "AI Solution";
      this.newButton_AISolution.Dock = DockStyle.Fill;
      this.newButton_AISolution.FlatBorderSize = 1;
      this.newButton_AISolution.FlatStyle = FlatStyle.Flat;
      this.newButton_AISolution.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_AISolution.Image = (Image) Resources.Analysis;
      this.newButton_AISolution.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_AISolution.Location = new Point(0, 0);
      this.newButton_AISolution.Name = "newButton_AISolution";
      this.newButton_AISolution.Size = new Size(1160, 26);
      this.newButton_AISolution.TabIndex = 7;
      this.newButton_AISolution.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_AISolution.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_AISolution.NewClick += new EventHandler(this.newButton_NewClick);
      this.tabPage_AI_Result.Controls.Add((Control) this.splitContainer13);
      this.tabPage_AI_Result.Location = new Point(4, 24);
      this.tabPage_AI_Result.Name = "tabPage_AI_Result";
      this.tabPage_AI_Result.Padding = new Padding(3);
      this.tabPage_AI_Result.Size = new Size(1166, 494);
      this.tabPage_AI_Result.TabIndex = 5;
      this.tabPage_AI_Result.Text = "AI 결과";
      this.tabPage_AI_Result.UseVisualStyleBackColor = true;
      this.splitContainer13.Dock = DockStyle.Fill;
      this.splitContainer13.FixedPanel = FixedPanel.Panel2;
      this.splitContainer13.IsSplitterFixed = true;
      this.splitContainer13.Location = new Point(3, 3);
      this.splitContainer13.Name = "splitContainer13";
      this.splitContainer13.Orientation = Orientation.Horizontal;
      this.splitContainer13.Panel1.Controls.Add((Control) this.panel_AIResult);
      this.splitContainer13.Panel2.Controls.Add((Control) this.newButton_AIResult);
      this.splitContainer13.Size = new Size(1160, 488);
      this.splitContainer13.SplitterDistance = 458;
      this.splitContainer13.TabIndex = 1;
      this.panel_AIResult.AutoScroll = true;
      this.panel_AIResult.Controls.Add((Control) this.panel15);
      this.panel_AIResult.Dock = DockStyle.Fill;
      this.panel_AIResult.Location = new Point(0, 0);
      this.panel_AIResult.Name = "panel_AIResult";
      this.panel_AIResult.Size = new Size(1160, 458);
      this.panel_AIResult.TabIndex = 0;
      this.panel15.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
      this.panel15.BackColor = Color.White;
      this.panel15.BorderStyle = BorderStyle.FixedSingle;
      this.panel15.Controls.Add((Control) this.newButton3);
      this.panel15.Controls.Add((Control) this.textBox9);
      this.panel15.Controls.Add((Control) this.textBox10);
      this.panel15.Controls.Add((Control) this.textBox11);
      this.panel15.Controls.Add((Control) this.textBox12);
      this.panel15.Controls.Add((Control) this.checkBox3);
      this.panel15.Controls.Add((Control) this.label19);
      this.panel15.Controls.Add((Control) this.label22);
      this.panel15.Controls.Add((Control) this.label23);
      this.panel15.Controls.Add((Control) this.label24);
      this.panel15.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.panel15.Location = new Point(0, 0);
      this.panel15.Name = "panel15";
      this.panel15.Size = new Size(1160, 45);
      this.panel15.TabIndex = 2;
      this.newButton3.ButtonBackColor = Color.White;
      this.newButton3.ButtonText = "결과 1";
      this.newButton3.FlatBorderSize = 1;
      this.newButton3.FlatStyle = FlatStyle.Flat;
      this.newButton3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton3.Image = (Image) null;
      this.newButton3.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton3.Location = new Point(17, -1);
      this.newButton3.Name = "newButton3";
      this.newButton3.Size = new Size(41, 45);
      this.newButton3.TabIndex = 3;
      this.newButton3.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton3.TextImageRelocation = TextImageRelation.Overlay;
      this.textBox9.Anchor = AnchorStyles.Top | AnchorStyles.Right;
      this.textBox9.BackColor = Color.White;
      this.textBox9.Location = new Point(1024, 21);
      this.textBox9.Name = "textBox9";
      this.textBox9.ReadOnly = true;
      this.textBox9.Size = new Size(135, 23);
      this.textBox9.TabIndex = 1;
      this.textBox10.Anchor = AnchorStyles.Top | AnchorStyles.Right;
      this.textBox10.BackColor = Color.White;
      this.textBox10.Location = new Point(947, 21);
      this.textBox10.Name = "textBox10";
      this.textBox10.ReadOnly = true;
      this.textBox10.Size = new Size(78, 23);
      this.textBox10.TabIndex = 1;
      this.textBox11.BackColor = Color.White;
      this.textBox11.Location = new Point(57, 21);
      this.textBox11.Name = "textBox11";
      this.textBox11.ReadOnly = true;
      this.textBox11.Size = new Size(120, 23);
      this.textBox11.TabIndex = 1;
      this.textBox12.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
      this.textBox12.BackColor = Color.White;
      this.textBox12.Location = new Point(176, 21);
      this.textBox12.Name = "textBox12";
      this.textBox12.ReadOnly = true;
      this.textBox12.Size = new Size(772, 23);
      this.textBox12.TabIndex = 1;
      this.checkBox3.Location = new Point(2, 14);
      this.checkBox3.Name = "checkBox3";
      this.checkBox3.Size = new Size(14, 14);
      this.checkBox3.TabIndex = 1;
      this.checkBox3.TextAlign = ContentAlignment.MiddleCenter;
      this.checkBox3.UseVisualStyleBackColor = true;
      this.label19.Anchor = AnchorStyles.Top | AnchorStyles.Right;
      this.label19.BackColor = Color.Lavender;
      this.label19.BorderStyle = BorderStyle.FixedSingle;
      this.label19.ForeColor = Color.Black;
      this.label19.Location = new Point(1024, -1);
      this.label19.Name = "label19";
      this.label19.Size = new Size(135, 23);
      this.label19.TabIndex = 1;
      this.label19.Text = "시퀀스";
      this.label19.TextAlign = ContentAlignment.MiddleCenter;
      this.label22.Anchor = AnchorStyles.Top | AnchorStyles.Right;
      this.label22.BackColor = Color.Lavender;
      this.label22.BorderStyle = BorderStyle.FixedSingle;
      this.label22.ForeColor = Color.Black;
      this.label22.Location = new Point(947, -1);
      this.label22.Name = "label22";
      this.label22.Size = new Size(78, 23);
      this.label22.TabIndex = 1;
      this.label22.Text = "메쉬";
      this.label22.TextAlign = ContentAlignment.MiddleCenter;
      this.label23.BackColor = Color.Lavender;
      this.label23.BorderStyle = BorderStyle.FixedSingle;
      this.label23.ForeColor = Color.Black;
      this.label23.Location = new Point(57, -1);
      this.label23.Name = "label23";
      this.label23.Size = new Size(120, 23);
      this.label23.TabIndex = 1;
      this.label23.Text = "제품";
      this.label23.TextAlign = ContentAlignment.MiddleCenter;
      this.label24.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
      this.label24.BackColor = Color.Lavender;
      this.label24.BorderStyle = BorderStyle.FixedSingle;
      this.label24.ForeColor = Color.Black;
      this.label24.Location = new Point(176, -1);
      this.label24.Name = "label24";
      this.label24.Size = new Size(772, 23);
      this.label24.TabIndex = 1;
      this.label24.Text = "스터디";
      this.label24.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_AIResult.ButtonBackColor = Color.White;
      this.newButton_AIResult.ButtonText = "결과 적용";
      this.newButton_AIResult.Dock = DockStyle.Fill;
      this.newButton_AIResult.FlatBorderSize = 1;
      this.newButton_AIResult.FlatStyle = FlatStyle.Flat;
      this.newButton_AIResult.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_AIResult.Image = (Image) Resources.Analysis;
      this.newButton_AIResult.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_AIResult.Location = new Point(0, 0);
      this.newButton_AIResult.Name = "newButton_AIResult";
      this.newButton_AIResult.Size = new Size(1160, 26);
      this.newButton_AIResult.TabIndex = 8;
      this.newButton_AIResult.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_AIResult.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_AIResult.NewClick += new EventHandler(this.newButton_NewClick);
      this.panel4.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.panel4.BorderStyle = BorderStyle.FixedSingle;
      this.panel4.Controls.Add((Control) this.dataGridView_Monitor);
      this.panel4.Controls.Add((Control) this.label_Monitoring_Analysis);
      this.panel4.Location = new Point(1, 0);
      this.panel4.Name = "panel4";
      this.panel4.Size = new Size(1382, 365);
      this.panel4.TabIndex = 1;
      this.dataGridView_Monitor.AllowUserToAddRows = false;
      this.dataGridView_Monitor.AllowUserToDeleteRows = false;
      this.dataGridView_Monitor.AllowUserToOrderColumns = true;
      this.dataGridView_Monitor.AllowUserToResizeColumns = false;
      this.dataGridView_Monitor.AllowUserToResizeRows = false;
      this.dataGridView_Monitor.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_Monitor.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_Monitor.BackgroundColor = Color.White;
      this.dataGridView_Monitor.BorderStyle = BorderStyle.None;
      gridViewCellStyle19.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle19.BackColor = Color.Lavender;
      gridViewCellStyle19.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle19.ForeColor = SystemColors.WindowText;
      gridViewCellStyle19.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle19.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle19.WrapMode = DataGridViewTriState.True;
      this.dataGridView_Monitor.ColumnHeadersDefaultCellStyle = gridViewCellStyle19;
      this.dataGridView_Monitor.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_Monitor.Columns.AddRange((DataGridViewColumn) this.Column_Monitor_Product, (DataGridViewColumn) this.Column_Monitor_Study, (DataGridViewColumn) this.Column_Monitor_Mesh, (DataGridViewColumn) this.Column_Monitor_Sequence, (DataGridViewColumn) this.Column_Monitor_Status);
      gridViewCellStyle20.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle20.BackColor = SystemColors.Window;
      gridViewCellStyle20.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle20.ForeColor = SystemColors.ControlText;
      gridViewCellStyle20.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle20.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle20.WrapMode = DataGridViewTriState.False;
      this.dataGridView_Monitor.DefaultCellStyle = gridViewCellStyle20;
      this.dataGridView_Monitor.EnableHeadersVisualStyles = false;
      this.dataGridView_Monitor.Location = new Point(-2, 21);
      this.dataGridView_Monitor.Name = "dataGridView_Monitor";
      this.dataGridView_Monitor.RowHeadersVisible = false;
      this.dataGridView_Monitor.RowTemplate.Height = 23;
      this.dataGridView_Monitor.Size = new Size(1384, 343);
      this.dataGridView_Monitor.TabIndex = 2;
      this.dataGridView_Monitor.SelectionChanged += new EventHandler(this.dataGridView_Monitor_SelectionChanged);
      this.Column_Monitor_Product.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      this.Column_Monitor_Product.Frozen = true;
      this.Column_Monitor_Product.HeaderText = "제품";
      this.Column_Monitor_Product.Name = "Column_Monitor_Product";
      this.Column_Monitor_Product.ReadOnly = true;
      this.Column_Monitor_Product.Width = 129;
      this.Column_Monitor_Study.HeaderText = "스터디";
      this.Column_Monitor_Study.Name = "Column_Monitor_Study";
      this.Column_Monitor_Study.ReadOnly = true;
      this.Column_Monitor_Mesh.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      this.Column_Monitor_Mesh.HeaderText = "메쉬";
      this.Column_Monitor_Mesh.Name = "Column_Monitor_Mesh";
      this.Column_Monitor_Mesh.ReadOnly = true;
      this.Column_Monitor_Mesh.Width = 80;
      this.Column_Monitor_Sequence.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      this.Column_Monitor_Sequence.HeaderText = "시퀀스";
      this.Column_Monitor_Sequence.Name = "Column_Monitor_Sequence";
      this.Column_Monitor_Sequence.ReadOnly = true;
      this.Column_Monitor_Sequence.Width = 120;
      this.Column_Monitor_Status.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      this.Column_Monitor_Status.HeaderText = "상태";
      this.Column_Monitor_Status.Name = "Column_Monitor_Status";
      this.Column_Monitor_Status.ReadOnly = true;
      this.Column_Monitor_Status.Width = 110;
      this.label_Monitoring_Analysis.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Monitoring_Analysis.Dock = DockStyle.Top;
      this.label_Monitoring_Analysis.ForeColor = Color.MidnightBlue;
      this.label_Monitoring_Analysis.Location = new Point(0, 0);
      this.label_Monitoring_Analysis.Name = "label_Monitoring_Analysis";
      this.label_Monitoring_Analysis.Size = new Size(1380, 21);
      this.label_Monitoring_Analysis.TabIndex = 1;
      this.label_Monitoring_Analysis.Text = "[모니터링(해석)]";
      this.label_Monitoring_Analysis.TextAlign = ContentAlignment.MiddleLeft;
      this.dataGridViewCheckBoxColumn1.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      this.dataGridViewCheckBoxColumn1.Frozen = true;
      this.dataGridViewCheckBoxColumn1.HeaderText = "";
      this.dataGridViewCheckBoxColumn1.Name = "dataGridViewCheckBoxColumn1";
      this.dataGridViewCheckBoxColumn1.Width = 20;
      this.dataGridViewTextBoxColumn1.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      this.dataGridViewTextBoxColumn1.Frozen = true;
      this.dataGridViewTextBoxColumn1.HeaderText = "제품";
      this.dataGridViewTextBoxColumn1.Name = "dataGridViewTextBoxColumn1";
      this.dataGridViewTextBoxColumn1.ReadOnly = true;
      this.dataGridViewTextBoxColumn1.Width = 129;
      this.dataGridViewTextBoxColumn2.HeaderText = "스터디";
      this.dataGridViewTextBoxColumn2.Name = "dataGridViewTextBoxColumn2";
      this.dataGridViewTextBoxColumn2.ReadOnly = true;
      this.dataGridViewTextBoxColumn3.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      this.dataGridViewTextBoxColumn3.HeaderText = "시퀀스";
      this.dataGridViewTextBoxColumn3.Name = "dataGridViewTextBoxColumn3";
      this.dataGridViewTextBoxColumn3.ReadOnly = true;
      this.dataGridViewTextBoxColumn4.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      this.dataGridViewTextBoxColumn4.HeaderText = "상태";
      this.dataGridViewTextBoxColumn4.Name = "dataGridViewTextBoxColumn4";
      this.dataGridViewTextBoxColumn4.ReadOnly = true;
      this.dataGridViewTextBoxColumn4.Width = 70;
      this.label_Focus.Anchor = AnchorStyles.Top | AnchorStyles.Right;
      this.label_Focus.AutoSize = true;
      this.label_Focus.Location = new Point(1077, 50);
      this.label_Focus.Name = "label_Focus";
      this.label_Focus.Size = new Size(38, 15);
      this.label_Focus.TabIndex = 3;
      this.label_Focus.Text = "label2";
      this.label_Focus.Visible = false;
      this.imageList_TreeView.ColorDepth = ColorDepth.Depth32Bit;
      this.imageList_TreeView.ImageSize = new Size(10, 12);
      this.imageList_TreeView.TransparentColor = Color.Transparent;
      this.pictureBox_HDSolutions.Anchor = AnchorStyles.Top | AnchorStyles.Right;
      this.pictureBox_HDSolutions.BackColor = Color.FromArgb(226, 236, 247);
      this.pictureBox_HDSolutions.Image = (Image) Resources.HDSolutions;
      this.pictureBox_HDSolutions.Location = new Point(1189, 36);
      this.pictureBox_HDSolutions.Name = "pictureBox_HDSolutions";
      this.pictureBox_HDSolutions.Size = new Size(186, 42);
      this.pictureBox_HDSolutions.SizeMode = PictureBoxSizeMode.AutoSize;
      this.pictureBox_HDSolutions.TabIndex = 2;
      this.pictureBox_HDSolutions.TabStop = false;
      this.pictureBox_HDSolutions.Click += new EventHandler(this.pictureBox_HDSolutions_Click);
      this.ribbonButton2.Image = (Image) componentResourceManager.GetObject("ribbonButton2.Image");
      this.ribbonButton2.LargeImage = (Image) componentResourceManager.GetObject("ribbonButton2.LargeImage");
      this.ribbonButton2.Name = "ribbonButton2";
      this.ribbonButton2.SmallImage = (Image) componentResourceManager.GetObject("ribbonButton2.SmallImage");
      this.ribbonButton2.Text = "런너 DB(Runner)";
      this.ribbonButton3.Image = (Image) componentResourceManager.GetObject("ribbonButton3.Image");
      this.ribbonButton3.LargeImage = (Image) componentResourceManager.GetObject("ribbonButton3.LargeImage");
      this.ribbonButton3.Name = "ribbonButton3";
      this.ribbonButton3.SmallImage = (Image) componentResourceManager.GetObject("ribbonButton3.SmallImage");
      this.ribbonButton3.Text = "모델 (Model)";
      this.ribbonButton4.Image = (Image) componentResourceManager.GetObject("ribbonButton4.Image");
      this.ribbonButton4.LargeImage = (Image) componentResourceManager.GetObject("ribbonButton4.LargeImage");
      this.ribbonButton4.Name = "ribbonButton4";
      this.ribbonButton4.SmallImage = (Image) componentResourceManager.GetObject("ribbonButton4.SmallImage");
      this.ribbonButton4.Text = "변형율 (Shrinkage)";
      this.ribbonButton5.Image = (Image) componentResourceManager.GetObject("ribbonButton5.Image");
      this.ribbonButton5.LargeImage = (Image) componentResourceManager.GetObject("ribbonButton5.LargeImage");
      this.ribbonButton5.Name = "ribbonButton5";
      this.ribbonButton5.SmallImage = (Image) componentResourceManager.GetObject("ribbonButton5.SmallImage");
      this.ribbonButton5.Text = "장비 (Workstation)";
      this.ribbonButton6.Image = (Image) componentResourceManager.GetObject("ribbonButton6.Image");
      this.ribbonButton6.LargeImage = (Image) componentResourceManager.GetObject("ribbonButton6.LargeImage");
      this.ribbonButton6.Name = "ribbonButton6";
      this.ribbonButton6.SmallImage = (Image) componentResourceManager.GetObject("ribbonButton6.SmallImage");
      this.ribbonButton6.Text = "케이스  (Case)";
      this.ribbonButton7.Image = (Image) componentResourceManager.GetObject("ribbonButton7.Image");
      this.ribbonButton7.LargeImage = (Image) componentResourceManager.GetObject("ribbonButton7.LargeImage");
      this.ribbonButton7.Name = "ribbonButton7";
      this.ribbonButton7.SmallImage = (Image) componentResourceManager.GetObject("ribbonButton7.SmallImage");
      this.ribbonButton7.Text = "프로세스 (Process)";
      this.ribbonButton8.Image = (Image) componentResourceManager.GetObject("ribbonButton8.Image");
      this.ribbonButton8.LargeImage = (Image) componentResourceManager.GetObject("ribbonButton8.LargeImage");
      this.ribbonButton8.Name = "ribbonButton8";
      this.ribbonButton8.SmallImage = (Image) componentResourceManager.GetObject("ribbonButton8.SmallImage");
      this.ribbonButton8.Text = "템플릿 (Template)";
      this.ribbonButton9.Image = (Image) componentResourceManager.GetObject("ribbonButton9.Image");
      this.ribbonButton9.LargeImage = (Image) componentResourceManager.GetObject("ribbonButton9.LargeImage");
      this.ribbonButton9.Name = "ribbonButton9";
      this.ribbonButton9.SmallImage = (Image) componentResourceManager.GetObject("ribbonButton9.SmallImage");
      this.ribbonButton9.Text = "회전 (Rotation)";
      this.panel_Rotate.Controls.Add((Control) this.newTextBox_RotAng);
      this.panel_Rotate.Controls.Add((Control) this.label_Rotate_Model);
      this.panel_Rotate.Controls.Add((Control) this.newComboBox_RotateAxis);
      this.panel_Rotate.Controls.Add((Control) this.newButton_Rotate);
      this.panel_Rotate.Dock = DockStyle.Fill;
      this.panel_Rotate.Location = new Point(0, 0);
      this.panel_Rotate.Name = "panel_Rotate";
      this.panel_Rotate.Size = new Size(205, 69);
      this.panel_Rotate.TabIndex = 8;
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(1384, 801);
      this.Controls.Add((Control) this.label_Focus);
      this.Controls.Add((Control) this.pictureBox_HDSolutions);
      this.Controls.Add((Control) this.splitContainer1);
      this.Controls.Add((Control) this.ribbon_Main);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.Icon = (Icon) componentResourceManager.GetObject("$this.Icon");
      this.KeyPreview = true;
      this.Name = nameof (frmMain);
      this.StartPosition = FormStartPosition.CenterScreen;
      this.Text = "HDMFlow";
      this.FormClosed += new FormClosedEventHandler(this.frmMain_FormClosed);
      this.Load += new EventHandler(this.frmMain_Load);
      this.splitContainer1.Panel1.ResumeLayout(false);
      this.splitContainer1.Panel2.ResumeLayout(false);
      this.splitContainer1.EndInit();
      this.splitContainer1.ResumeLayout(false);
      this.splitContainer2.Panel1.ResumeLayout(false);
      this.splitContainer2.Panel2.ResumeLayout(false);
      this.splitContainer2.EndInit();
      this.splitContainer2.ResumeLayout(false);
      this.splitContainer6.Panel1.ResumeLayout(false);
      this.splitContainer6.Panel2.ResumeLayout(false);
      this.splitContainer6.EndInit();
      this.splitContainer6.ResumeLayout(false);
      this.splitContainer8.Panel1.ResumeLayout(false);
      this.splitContainer8.Panel2.ResumeLayout(false);
      this.splitContainer8.EndInit();
      this.splitContainer8.ResumeLayout(false);
      this.panel_Mesh.ResumeLayout(false);
      this.splitContainer5.Panel1.ResumeLayout(false);
      this.splitContainer5.Panel2.ResumeLayout(false);
      this.splitContainer5.EndInit();
      this.splitContainer5.ResumeLayout(false);
      this.panel_HDMFlow.ResumeLayout(false);
      this.tabControl_Main.ResumeLayout(false);
      this.tabPage_Study.ResumeLayout(false);
      this.splitContainer3.Panel1.ResumeLayout(false);
      this.splitContainer3.Panel2.ResumeLayout(false);
      this.splitContainer3.EndInit();
      this.splitContainer3.ResumeLayout(false);
      this.splitContainer4.Panel1.ResumeLayout(false);
      this.splitContainer4.Panel2.ResumeLayout(false);
      this.splitContainer4.EndInit();
      this.splitContainer4.ResumeLayout(false);
      this.panel2.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_Dual).EndInit();
      this.panel3.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_3D).EndInit();
      this.tabPage_Report.ResumeLayout(false);
      this.splitContainer_Report.Panel1.ResumeLayout(false);
      this.splitContainer_Report.Panel2.ResumeLayout(false);
      this.splitContainer_Report.EndInit();
      this.splitContainer_Report.ResumeLayout(false);
      this.panel_Report.ResumeLayout(false);
      this.panel6.ResumeLayout(false);
      this.panel6.PerformLayout();
      ((ISupportInitialize) this.dataGridView3).EndInit();
      this.tabPage_PastCases.ResumeLayout(false);
      this.splitContainer10.Panel1.ResumeLayout(false);
      this.splitContainer10.Panel2.ResumeLayout(false);
      this.splitContainer10.EndInit();
      this.splitContainer10.ResumeLayout(false);
      this.panel_PastCases.ResumeLayout(false);
      this.panel11.ResumeLayout(false);
      this.panel11.PerformLayout();
      ((ISupportInitialize) this.dataGridView4).EndInit();
      this.panel9.ResumeLayout(false);
      this.tabPage_Comparison.ResumeLayout(false);
      this.splitContainer7.Panel1.ResumeLayout(false);
      this.splitContainer7.Panel2.ResumeLayout(false);
      this.splitContainer7.EndInit();
      this.splitContainer7.ResumeLayout(false);
      this.splitContainer9.Panel1.ResumeLayout(false);
      this.splitContainer9.Panel2.ResumeLayout(false);
      this.splitContainer9.EndInit();
      this.splitContainer9.ResumeLayout(false);
      this.panel5.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_List).EndInit();
      this.tableLayoutPanel_CMPR.ResumeLayout(false);
      this.panel13.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView5).EndInit();
      this.panel8.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView2).EndInit();
      this.panel_Compare1.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView1).EndInit();
      this.panel7.ResumeLayout(false);
      this.panel7.PerformLayout();
      this.tabPage_AI_Study.ResumeLayout(false);
      this.splitContainer12.Panel1.ResumeLayout(false);
      this.splitContainer12.Panel2.ResumeLayout(false);
      this.splitContainer12.EndInit();
      this.splitContainer12.ResumeLayout(false);
      this.splitContainer11.Panel1.ResumeLayout(false);
      this.splitContainer11.Panel2.ResumeLayout(false);
      this.splitContainer11.EndInit();
      this.splitContainer11.ResumeLayout(false);
      this.panel10.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_AI_Dual).EndInit();
      this.panel12.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_AI_3D).EndInit();
      this.tabPage_AI_Result.ResumeLayout(false);
      this.splitContainer13.Panel1.ResumeLayout(false);
      this.splitContainer13.Panel2.ResumeLayout(false);
      this.splitContainer13.EndInit();
      this.splitContainer13.ResumeLayout(false);
      this.panel_AIResult.ResumeLayout(false);
      this.panel15.ResumeLayout(false);
      this.panel15.PerformLayout();
      this.panel4.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_Monitor).EndInit();
      ((ISupportInitialize) this.pictureBox_HDSolutions).EndInit();
      this.panel_Rotate.ResumeLayout(false);
      this.ResumeLayout(false);
      this.PerformLayout();
    }
  }
}
