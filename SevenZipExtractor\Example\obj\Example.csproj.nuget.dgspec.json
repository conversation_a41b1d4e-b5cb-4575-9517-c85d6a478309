{"format": 1, "restore": {"C:\\Moldflow\\SevenZipExtractor\\Example\\Example.csproj": {}}, "projects": {"C:\\Moldflow\\SevenZipExtractor\\Example\\Example.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Moldflow\\SevenZipExtractor\\Example\\Example.csproj", "projectName": "ConsoleApplication86", "projectPath": "C:\\Moldflow\\SevenZipExtractor\\Example\\Example.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Moldflow\\SevenZipExtractor\\Example\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net45"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net45": {"targetAlias": "net45", "projectReferences": {"C:\\Moldflow\\SevenZipExtractor\\SevenZipExtractor\\SevenZipExtractor.csproj": {"projectPath": "C:\\Moldflow\\SevenZipExtractor\\SevenZipExtractor\\SevenZipExtractor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net45": {"targetAlias": "net45", "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.204\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}, "C:\\Moldflow\\SevenZipExtractor\\SevenZipExtractor\\SevenZipExtractor.csproj": {"version": "1.0.19", "restore": {"projectUniqueName": "C:\\Moldflow\\SevenZipExtractor\\SevenZipExtractor\\SevenZipExtractor.csproj", "projectName": "SevenZipExtractor", "projectPath": "C:\\Moldflow\\SevenZipExtractor\\SevenZipExtractor\\SevenZipExtractor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Moldflow\\SevenZipExtractor\\SevenZipExtractor\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net45", "netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net45": {"targetAlias": "net45", "projectReferences": {}}, "netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net45": {"targetAlias": "net45", "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.204\\RuntimeIdentifierGraph.json"}, "netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.204\\RuntimeIdentifierGraph.json"}}}}}