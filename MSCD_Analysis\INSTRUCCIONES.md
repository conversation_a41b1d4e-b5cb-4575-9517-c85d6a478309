# Instrucciones para el Extractor de Códigos MSCD de Moldflow

Este conjunto de herramientas permite extraer y analizar los códigos MSCD de Moldflow, facilitando la identificación y extracción de valores correspondientes en archivos de resultados (.out).

## Contenido del Directorio

- `mscd_codes.csv`: Archivo CSV con todos los códigos MSCD extraídos del archivo cmmesage.dat
- `README.md`: Información sobre la estructura del CSV
- `mscd_extractor.py`: Script Python para extraer códigos MSCD del archivo cmmesage.dat
- `mscd_extractor_out.py`: Script Python para extraer valores de códigos MSCD de archivos .out
- `buscar_mscd.py`: Script Python para buscar códigos MSCD específicos por código o texto

## Estructura de los Códigos MSCD

Cada código MSCD tiene la siguiente estructura en el archivo cmmesage.dat:

```
MSCD [código] [parámetros...] [mensaje]
```

Donde:
- **código**: Identificador único numérico (ej. 300, 1004, etc.)
- **parámetros**: Valores numéricos que definen el formato y comportamiento del mensaje
- **mensaje**: Texto del mensaje, que puede incluir marcadores de formato (%s, %d, etc.)

## Cómo Usar las Herramientas

### 1. Extracción de Códigos MSCD

El archivo `mscd_codes.csv` ya contiene todos los códigos extraídos del archivo cmmesage.dat. Si necesita regenerarlo:

```
python mscd_extractor.py
```

### 2. Extracción de Valores de Archivos .out

Para extraer valores de códigos MSCD de un archivo de resultados .out:

```
python mscd_extractor_out.py ruta_al_archivo.out
```

Ejemplo:

```
python mscd_extractor_out.py C:\Moldflow\resultados.out
```

Esto generará un archivo CSV con los códigos MSCD encontrados, sus mensajes y los valores extraídos.

### 3. Búsqueda de Códigos MSCD

Para buscar códigos MSCD específicos por código o texto en el mensaje:

```
python buscar_mscd.py
```

Este script proporciona una interfaz interactiva que permite:
- Buscar por código MSCD específico
- Buscar por texto contenido en los mensajes
- Ver detalles completos de los códigos encontrados

## Interpretación de los Resultados

El archivo CSV generado por `mscd_extractor_out.py` contiene:

- **mscd_code**: El código MSCD identificado
- **message**: El mensaje asociado con ese código
- **value**: El valor extraído del archivo .out
- **original_line**: La línea original donde se encontró el código

## Notas Importantes

1. Los códigos MSCD pueden tener diferentes formatos en los archivos .out, por lo que el script de extracción puede necesitar ajustes según el formato específico de sus archivos.

2. Algunos códigos MSCD pueden no aparecer en los archivos .out o pueden tener un formato diferente al esperado.

3. Para análisis más complejos, considere modificar el script `mscd_extractor_out.py` para adaptarlo a sus necesidades específicas.

## Ejemplo de Flujo de Trabajo

1. Ejecute `mscd_extractor.py` para generar el archivo CSV con todos los códigos MSCD (ya realizado).
2. Ejecute `mscd_extractor_out.py` con su archivo .out para extraer los valores.
3. Abra el archivo CSV resultante para analizar los valores extraídos.
4. Utilice estos datos para su análisis o para generar reportes personalizados.