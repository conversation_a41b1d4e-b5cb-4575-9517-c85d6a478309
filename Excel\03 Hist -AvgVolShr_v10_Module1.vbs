Attribute VB_Name = "Module1"

Sub AddVolShr()







Set Synergy = CreateObject("Synergy.Synergy")

Set StudyDoc = Synergy.StudyDoc

Set Viewer = Synergy.Viewer



' Check if a Synergy is open

If Synergy Is Nothing Then

    MsgBoxPrompt = "Synergy is not open." & vbCrLf & vbCrLf

    MsgBoxPrompt = MsgBoxPrompt + "Open a study, create and/or display an XY Plot and re-execute this script."

    MsgBoxButton = vbOKOnly + vbCritical + vbMsgBoxSetForeground

    MsgBoxTitle = "The script is closing"

    MsgBox MsgBoxPrompt, MsgBoxButton, MsgBoxTitle

    WScript.Quit

End If



'Check if a study is open

If Viewer Is Nothing Then

    MsgBoxPrompt = "A study is not open." & vbCrLf & vbCrLf

    MsgBoxPrompt = MsgBoxPrompt + "Open a study, create and/or display an XY Plot and re-execute this script."

    MsgBoxButton = vbOKOnly + vbCritical + vbMsgBoxSetForeground

    MsgBoxTitle = "The script is closing"

    MsgBox MsgBoxPrompt, MsgBoxButton, MsgBoxTitle

    WScript.Quit

End If



Set PlotMgr = Synergy.PlotManager()



Set Plot = PlotMgr.findplotbyname("Average volumetric shrinkage")

 

'Check if a plot is displayed

  If Plot Is Nothing Then

    MsgBoxPrompt = "The study does not have a Average Volumetric Shrinkage result plot. Please run an analysis"

    MsgBoxPrompt = MsgBoxPrompt + vbCrLf & vbCrLf & "Exiting Script!"

    MsgBoxButton = vbOKOnly + vbCritical + vbMsgBoxSetForeground

    MsgBoxTitle = "Error"

    MsgBoxOutput = MsgBox(MsgBoxPrompt, MsgBoxButton, MsgBoxTitle)

    WScript.Quit

  End If



StudyName = StudyDoc.StudyName





'ResultID = 1629    ' 1629 is the Average Volumetric Shrinkage Nodal Data



Set lIdsVS = Synergy.CreateIntegerArray()

countVS = PlotMgr.FindDataSetIDsByName("Average volumetric shrinkage", lIdsVS)

dsIDVS = lIdsVS.Val(0)



'msgbox "dsIDVS = " & dsIDVS

dsIDVS = 1629





'Dim i, j, PlotMgr, IndpValues, NumTSteps, vbTS, NIDIndp, NID, NIDResult, NDTotal, TS()

Set PlotMgr = Synergy.PlotManager()

Set IndpValues = Synergy.CreateDoubleArray()

'PlotMgr.GetIndpValues ResultID, IndpValues

PlotMgr.GetIndpValues dsIDVS, IndpValues

NumTSteps = IndpValues.Size() - 1   ' Total number of Time Steps

vbTS = IndpValues.ToVBSArray()



'MsgBox "NumTSteps = " & NumTSteps



ReDim TS(NumTSteps + 1)

For i = 0 To NumTSteps

    TS(i) = vbTS(i)

Next





Dim Jays

For i = 0 To UBound(TS)

    Jays = Jays + TS(i) & vbCrLf

Next



'msgbox Jays



Dim Indp, IndpValues1, PResult, EntityIndex, vbPResult, vbPEntityIndex

ResultID = 1629 ' 1629 is the Average Volumetric Shrinkage Nodal Data

'For i = 0 To NumTSteps

    Set Indp = Synergy.CreateDoubleArray()

    PlotMgr.GetIndpValues ResultID, Indp

    Set IndpValues1 = Synergy.CreateDoubleArray()

    IndpValues1.AddDouble (Indp.Val(NumTSteps))

    Set EntityIndex = Synergy.CreateIntegerArray()

    Set PResult = Synergy.CreateDoubleArray()

    PlotMgr.GetScalarData ResultID, IndpValues1, EntityIndex, PResult

    vbPResult = PResult.ToVBSArray()

    vbPEntityIndex = EntityIndex.ToVBSArray()

    ' Retrieve required data from results.

    ' For j = 0 to Ubound(vbPEntityIndex) -1

        ' PD(j,NumTSteps) = vbPResult(j)

' '     PDNL(j,i) = vbPEntityIndex(j)

'    Next

'Next











  

  Set fso = CreateObject("Scripting.FileSystemObject")

UpPath = fso.GetAbsolutePathName(".")



ResultName = StudyName & "_" & ResultName

ResultName = Replace(ResultName, "/", "_")

ResultName = Replace(ResultName, "\", "_")

ResultName = Replace(ResultName, ",", "_")

ResultName = Replace(ResultName, ":", "_")





PathName = "C:\Autodesk\ActiveStudyVolShr.txt"



Set f = fso.OpenTextFile(PathName, 2, True)



HeadingString = ResultName

HeadingString = HeadingString + "_Avg Vol Shr" & vbCrLf & vbCrLf & vbCrLf



f.write HeadingString



For i = 0 To UBound(vbPResult)

    f.write vbPResult(i) & vbCrLf

Next



    Set StudyDoc = Synergy.StudyDoc()

    StudyNameFull = StudyDoc.StudyName



'   MsgBox StudyNameFull



    

    

    ErrorBoolean = False

    

        CellFillTest = Range("B7").Value

    

    If CellFillTest = "" Then

    

        ErrorBoolean = False

    Else

    

        ErrorBoolean = True

        MsgBox "Delete the raw data in Column B before clicking the button again"

        

    End If

    

    

    If ErrorBoolean = False Then

    

     '   TextFile = "C:\Autodesk\ActiveStudyPartWeight.txt"



        'The code below gets the file name from the entire path

        DotNo = InStrRev(StudyNameFull, ".") - 1

        SlashNo = InStrRev(StudyNameFull, "\")

        StrLength = DotNo - SlashNo

        StudyName = Mid(StudyNameFull, SlashNo + 1, StrLength)

        StudyName = Left(StudyName, 5)

        MySheetName = Left(StudyNameFull, 4)

        

        ' The code below sets the Heading1 text

        Heading1 = "            Time [s]"

       

          

        Heading2 = StudyName

      'The code below sets the headings in the spreadsheet

            'and moves the active location to the start of data inport

        

        Range("B7").Activate

     

    

'        StartRow = TextBoxStartRow.Text

'

        RanNum = Int((100 * Rnd) + 1) ' This is used in the import data name to ensure a unique name



        TextFile = "C:\Autodesk\ActiveStudyVolShr.txt"

            

        'The With sequence creates the imported text area

            

         With ActiveSheet.QueryTables.Add(Connection:="TEXT;" & TextFile, Destination:=ActiveCell)

                .Name = StudyName & RanNum

                .FieldNames = True

                .RowNumbers = False

                .FillAdjacentFormulas = False

                .PreserveFormatting = True

                .RefreshOnFileOpen = False

                .RefreshStyle = xlInsertDeleteCells

                .SavePassword = False

                .SaveData = True

                .AdjustColumnWidth = True

                .RefreshPeriod = 0

                .TextFilePromptOnRefresh = False

                .TextFilePlatform = 437

                .TextFileStartRow = 1

                .TextFileParseType = xlDelimited

                .TextFileTextQualifier = xlTextQualifierDoubleQuote

                .TextFileConsecutiveDelimiter = False

                .TextFileTabDelimiter = True

                .TextFileSemicolonDelimiter = False

                .TextFileCommaDelimiter = False

                .TextFileSpaceDelimiter = False

                .TextFileColumnDataTypes = Array(1, 1)

                .TextFileTrailingMinusNumbers = True

                .Refresh BackgroundQuery:=False

                

            End With

                

            Selection.CurrentRegion.NumberFormat = "#,##0.000"

            Selection.CurrentRegion.Font.Bold = False

            



            Range("G9").Value = StudyName

            

            Columns("B:B").Select

            Selection.ColumnWidth = 10

            

            Dim ws As Worksheet



            Set ws = ActiveSheet

            ActiveSheet.Name = MySheetName

           

            

            

            

    End If



    









End Sub





