﻿// Decompiled with JetBrains decompiler
// Type: HDMFAI.ControlHelper
// Assembly: HDMFAI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: A66A47CE-D63B-4ACB-B9A4-A4F118252C4E
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFAI.dll

using System.Reflection;
using System.Windows.Forms;

namespace HDMFAI
{
  internal static class ControlHelper
  {
    public static void SetDoubleBuffered(this Control contorl, bool setting) => contorl.GetType().GetProperty("DoubleBuffered", BindingFlags.Instance | BindingFlags.NonPublic).SetValue((object) contorl, (object) setting, (object[]) null);
  }
}
