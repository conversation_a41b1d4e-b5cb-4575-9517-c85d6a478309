// C#: Conexión y extracción del tiempo de llenado en Synergy Moldflow 2025
using System;
using System.Runtime.InteropServices;

class Program
{
    static void Main()
    {
        try
        {
            Type synergyType = Type.GetTypeFromProgID("Synergy.Application");
            dynamic synergy = Activator.CreateInstance(synergyType);
            string version = synergy.Version;
            if (!version.Contains("2025"))
                Console.WriteLine("Advertencia: No es la versión 2025. Versión detectada: " + version);
            else
                Console.WriteLine("Conectado a Synergy Moldflow versión: " + version);

            dynamic studyDoc = synergy.StudyDoc();
            if (studyDoc == null)
            {
                Console.WriteLine("No hay estudio abierto en Synergy.");
                return;
            }
            try
            {
                var fillTime = studyDoc.GetResultValue("Fill Time");
                Console.WriteLine($"Tiempo de llenado del estudio activo: {fillTime} s");
            }
            catch (Exception ex)
            {
                Console.WriteLine("No se pudo obtener el tiempo de llenado (Fill Time): " + ex.Message);
            }
        }
        catch (COMException ex)
        {
            Console.WriteLine("No se pudo iniciar Synergy: " + ex.Message);
        }
    }
}
