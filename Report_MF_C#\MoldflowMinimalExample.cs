// Minimal C# example: Connect to Moldflow, open a study, run analysis, read result.
// Requires Moldflow installed and registered for COM interop.
// Place this file in a C# Console App project and add reference to Moldflow COM (mfcom.tlb).

using System;

namespace MoldflowMinimalExample
{
    class Program
    {
        static void Main(string[] args)
        {
            dynamic mfApp = null;
            dynamic study = null;
            try
            {
                // 1. Start Moldflow application via COM
                mfApp = Activator.CreateInstance(Type.GetTypeFromProgID("MfWorks.Application"));
                mfApp.Visible = true; // Optional: show UI

                // 2. Open an existing study file (update path as needed)
                string studyPath = @"C:\Path\To\Your\Study.sdy";
                study = mfApp.Studies.Open(studyPath, false);

                // 3. Run a simple analysis (e.g., Fill)
                var analysis = study.Analyses.Item("Fill");
                analysis.Run();

                // 4. Read a basic result (e.g., max pressure)
                var results = analysis.Results;
                double maxPressure = results.GetMaximumValue("Pressure");
                Console.WriteLine("Max Pressure: " + maxPressure);
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error: " + ex.Message);
            }
            finally
            {
                // Clean up
                if (study != null) study.Close();
                if (mfApp != null) mfApp.Quit();
            }
        }
    }
}