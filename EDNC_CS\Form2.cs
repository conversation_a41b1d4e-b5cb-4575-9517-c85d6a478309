﻿// Decompiled with JetBrains decompiler
// Type: Moldflow_Esay_Tool_Kit.Form2
// Assembly: Moldflow Esay Tool Kit, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: EE4F1197-F36A-4D67-AE33-DA541A327629
// Assembly location: C:\Users\<USER>\Documents\20210315_Moldflow Esay Tool Kit_V3.exe

using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

#nullable disable
namespace Moldflow_Esay_Tool_Kit
{
  [DesignerGenerated]
  public class Form2 : Form
  {
    private static List<WeakReference> __ENCList = new List<WeakReference>();
    private IContainer components;
    [AccessedThroughProperty("Process_Setting_View_Table")]
    private TableLayoutPanel _Process_Setting_View_Table;
    [AccessedThroughProperty("Process_Setting_View_Label1")]
    private Label _Process_Setting_View_Label1;
    [AccessedThroughProperty("Process_Setting_View")]
    private Button _Process_Setting_View;
    [AccessedThroughProperty("Process_Setting_View_Label2")]
    private Label _Process_Setting_View_Label2;
    [AccessedThroughProperty("Process_Setting_View_Label3")]
    private Label _Process_Setting_View_Label3;
    [AccessedThroughProperty("Process_Setting_View_Label6")]
    private Label _Process_Setting_View_Label6;
    [AccessedThroughProperty("Process_Setting_View_Label7")]
    private Label _Process_Setting_View_Label7;
    [AccessedThroughProperty("Process_Setting_View_Label8")]
    private Label _Process_Setting_View_Label8;
    [AccessedThroughProperty("Process_Setting_View_Label9")]
    private Label _Process_Setting_View_Label9;
    [AccessedThroughProperty("Process_Setting_View_Label10")]
    private Label _Process_Setting_View_Label10;
    [AccessedThroughProperty("Process_Setting_View_Label11")]
    private Label _Process_Setting_View_Label11;
    [AccessedThroughProperty("Process_Setting_View_Label12")]
    private Label _Process_Setting_View_Label12;
    [AccessedThroughProperty("Process_Setting_View_Label13")]
    private Label _Process_Setting_View_Label13;
    [AccessedThroughProperty("Process_Setting_View_Label14")]
    private Label _Process_Setting_View_Label14;
    [AccessedThroughProperty("Process_Setting_View_Label15")]
    private Label _Process_Setting_View_Label15;
    [AccessedThroughProperty("Process_Setting_View_Label16")]
    private Label _Process_Setting_View_Label16;
    [AccessedThroughProperty("Fill_Profile")]
    private GroupBox _Fill_Profile;
    [AccessedThroughProperty("Fill_Pofile_Table")]
    private TableLayoutPanel _Fill_Pofile_Table;
    [AccessedThroughProperty("Fill_Profile_Label12")]
    private Label _Fill_Profile_Label12;
    [AccessedThroughProperty("Fill_Profile_Label11")]
    private Label _Fill_Profile_Label11;
    [AccessedThroughProperty("Fill_Profile_Label10")]
    private Label _Fill_Profile_Label10;
    [AccessedThroughProperty("Fill_Profile_Label9")]
    private Label _Fill_Profile_Label9;
    [AccessedThroughProperty("Fill_Profile_Label8")]
    private Label _Fill_Profile_Label8;
    [AccessedThroughProperty("Fill_Profile_Label7")]
    private Label _Fill_Profile_Label7;
    [AccessedThroughProperty("Fill_Profile_Label6")]
    private Label _Fill_Profile_Label6;
    [AccessedThroughProperty("Fill_Profile_Label5")]
    private Label _Fill_Profile_Label5;
    [AccessedThroughProperty("Fill_Profile_Label4")]
    private Label _Fill_Profile_Label4;
    [AccessedThroughProperty("Fill_Profile_Label3")]
    private Label _Fill_Profile_Label3;
    [AccessedThroughProperty("Fill_Profile_Label2")]
    private Label _Fill_Profile_Label2;
    [AccessedThroughProperty("Fill_Profile_Label1")]
    private Label _Fill_Profile_Label1;
    [AccessedThroughProperty("Pack_Profile")]
    private GroupBox _Pack_Profile;
    [AccessedThroughProperty("TableLayoutPanel1")]
    private TableLayoutPanel _TableLayoutPanel1;
    [AccessedThroughProperty("Pack_Profile_Label12")]
    private Label _Pack_Profile_Label12;
    [AccessedThroughProperty("Pack_Profile_Label11")]
    private Label _Pack_Profile_Label11;
    [AccessedThroughProperty("Pack_Profile_Label10")]
    private Label _Pack_Profile_Label10;
    [AccessedThroughProperty("Pack_Profile_Label9")]
    private Label _Pack_Profile_Label9;
    [AccessedThroughProperty("Pack_Profile_Label8")]
    private Label _Pack_Profile_Label8;
    [AccessedThroughProperty("Pack_Profile_Label7")]
    private Label _Pack_Profile_Label7;
    [AccessedThroughProperty("Pack_Profile_Label6")]
    private Label _Pack_Profile_Label6;
    [AccessedThroughProperty("Pack_Profile_Label5")]
    private Label _Pack_Profile_Label5;
    [AccessedThroughProperty("Pack_Profile_Label4")]
    private Label _Pack_Profile_Label4;
    [AccessedThroughProperty("Pack_Profile_Label3")]
    private Label _Pack_Profile_Label3;
    [AccessedThroughProperty("Pack_Profile_Label2")]
    private Label _Pack_Profile_Label2;
    [AccessedThroughProperty("Pack_Profile_Label1")]
    private Label _Pack_Profile_Label1;
    [AccessedThroughProperty("PSSV_Study_Name_Label")]
    private Label _PSSV_Study_Name_Label;
    [AccessedThroughProperty("PSSV_Mesh_Type_Label")]
    private Label _PSSV_Mesh_Type_Label;
    [AccessedThroughProperty("PSSV_Analysis_Sequence_Label")]
    private Label _PSSV_Analysis_Sequence_Label;
    [AccessedThroughProperty("PSSV_Fill_Control_Label")]
    private Label _PSSV_Fill_Control_Label;
    [AccessedThroughProperty("PSSV_Fill_TIme_Label")]
    private Label _PSSV_Fill_TIme_Label;
    [AccessedThroughProperty("PSSV_Flow_Rate_Label")]
    private Label _PSSV_Flow_Rate_Label;
    [AccessedThroughProperty("PSSV_Packing_VP_Control_Label")]
    private Label _PSSV_Packing_VP_Control_Label;
    [AccessedThroughProperty("PSSV_Packing_VP_Label")]
    private Label _PSSV_Packing_VP_Label;
    [AccessedThroughProperty("PSSV_Packing_Control_Label")]
    private Label _PSSV_Packing_Control_Label;
    [AccessedThroughProperty("PSSV_Cooling_Control_Label")]
    private Label _PSSV_Cooling_Control_Label;
    [AccessedThroughProperty("PSSV_Cooling_Time_Label")]
    private Label _PSSV_Cooling_Time_Label;
    [AccessedThroughProperty("PSSV_Material_Manufacturer_Label")]
    private Label _PSSV_Material_Manufacturer_Label;
    [AccessedThroughProperty("PSSV_Material_Name_Label")]
    private Label _PSSV_Material_Name_Label;
    [AccessedThroughProperty("PSSV_Material_Family_Label")]
    private Label _PSSV_Material_Family_Label;
    [AccessedThroughProperty("PSSV_Fill_Profile_1_1_Label")]
    private Label _PSSV_Fill_Profile_1_1_Label;
    [AccessedThroughProperty("PSSV_Fill_Profile_2_1_Label")]
    private Label _PSSV_Fill_Profile_2_1_Label;
    [AccessedThroughProperty("PSSV_Fill_Profile_3_1_Label")]
    private Label _PSSV_Fill_Profile_3_1_Label;
    [AccessedThroughProperty("PSSV_Fill_Profile_4_1_Label")]
    private Label _PSSV_Fill_Profile_4_1_Label;
    [AccessedThroughProperty("PSSV_Fill_Profile_5_1_Label")]
    private Label _PSSV_Fill_Profile_5_1_Label;
    [AccessedThroughProperty("PSSV_Fill_Profile_6_1_Label")]
    private Label _PSSV_Fill_Profile_6_1_Label;
    [AccessedThroughProperty("PSSV_Fill_Profile_7_1_Label")]
    private Label _PSSV_Fill_Profile_7_1_Label;
    [AccessedThroughProperty("PSSV_Fill_Profile_8_1_Label")]
    private Label _PSSV_Fill_Profile_8_1_Label;
    [AccessedThroughProperty("PSSV_Fill_Profile_9_1_Label")]
    private Label _PSSV_Fill_Profile_9_1_Label;
    [AccessedThroughProperty("PSSV_Fill_Profile_10_1_Label")]
    private Label _PSSV_Fill_Profile_10_1_Label;
    [AccessedThroughProperty("PSSV_Fill_Profile_1_2_Label")]
    private Label _PSSV_Fill_Profile_1_2_Label;
    [AccessedThroughProperty("PSSV_Fill_Profile_2_2_Label")]
    private Label _PSSV_Fill_Profile_2_2_Label;
    [AccessedThroughProperty("PSSV_Fill_Profile_3_2_Label")]
    private Label _PSSV_Fill_Profile_3_2_Label;
    [AccessedThroughProperty("PSSV_Fill_Profile_4_2_Label")]
    private Label _PSSV_Fill_Profile_4_2_Label;
    [AccessedThroughProperty("PSSV_Fill_Profile_5_2_Label")]
    private Label _PSSV_Fill_Profile_5_2_Label;
    [AccessedThroughProperty("PSSV_Fill_Profile_6_2_Label")]
    private Label _PSSV_Fill_Profile_6_2_Label;
    [AccessedThroughProperty("PSSV_Fill_Profile_7_2_Label")]
    private Label _PSSV_Fill_Profile_7_2_Label;
    [AccessedThroughProperty("PSSV_Fill_Profile_8_2_Label")]
    private Label _PSSV_Fill_Profile_8_2_Label;
    [AccessedThroughProperty("PSSV_Fill_Profile_9_2_Label")]
    private Label _PSSV_Fill_Profile_9_2_Label;
    [AccessedThroughProperty("PSSV_Fill_Profile_10_2_Label")]
    private Label _PSSV_Fill_Profile_10_2_Label;
    [AccessedThroughProperty("PSSV_Packing_Profile_1_1_Label")]
    private Label _PSSV_Packing_Profile_1_1_Label;
    [AccessedThroughProperty("PSSV_Packing_Profile_2_1_Label")]
    private Label _PSSV_Packing_Profile_2_1_Label;
    [AccessedThroughProperty("PSSV_Packing_Profile_3_1_Label")]
    private Label _PSSV_Packing_Profile_3_1_Label;
    [AccessedThroughProperty("PSSV_Packing_Profile_4_1_Label")]
    private Label _PSSV_Packing_Profile_4_1_Label;
    [AccessedThroughProperty("PSSV_Packing_Profile_5_1_Label")]
    private Label _PSSV_Packing_Profile_5_1_Label;
    [AccessedThroughProperty("PSSV_Packing_Profile_6_1_Label")]
    private Label _PSSV_Packing_Profile_6_1_Label;
    [AccessedThroughProperty("PSSV_Packing_Profile_7_1_Label")]
    private Label _PSSV_Packing_Profile_7_1_Label;
    [AccessedThroughProperty("PSSV_Packing_Profile_8_1_Label")]
    private Label _PSSV_Packing_Profile_8_1_Label;
    [AccessedThroughProperty("PSSV_Packing_Profile_9_1_Label")]
    private Label _PSSV_Packing_Profile_9_1_Label;
    [AccessedThroughProperty("PSSV_Packing_Profile_10_1_Label")]
    private Label _PSSV_Packing_Profile_10_1_Label;
    [AccessedThroughProperty("PSSV_Packing_Profile_1_2_Label")]
    private Label _PSSV_Packing_Profile_1_2_Label;
    [AccessedThroughProperty("PSSV_Packing_Profile_2_2_Label")]
    private Label _PSSV_Packing_Profile_2_2_Label;
    [AccessedThroughProperty("PSSV_Packing_Profile_3_2_Label")]
    private Label _PSSV_Packing_Profile_3_2_Label;
    [AccessedThroughProperty("PSSV_Packing_Profile_4_2_Label")]
    private Label _PSSV_Packing_Profile_4_2_Label;
    [AccessedThroughProperty("PSSV_Packing_Profile_5_2_Label")]
    private Label _PSSV_Packing_Profile_5_2_Label;
    [AccessedThroughProperty("PSSV_Packing_Profile_6_2_Label")]
    private Label _PSSV_Packing_Profile_6_2_Label;
    [AccessedThroughProperty("PSSV_Packing_Profile_7_2_Label")]
    private Label _PSSV_Packing_Profile_7_2_Label;
    [AccessedThroughProperty("PSSV_Packing_Profile_8_2_Label")]
    private Label _PSSV_Packing_Profile_8_2_Label;
    [AccessedThroughProperty("PSSV_Packing_Profile_9_2_Label")]
    private Label _PSSV_Packing_Profile_9_2_Label;
    [AccessedThroughProperty("PSSV_Packing_Profile_10_2_Label")]
    private Label _PSSV_Packing_Profile_10_2_Label;
    [AccessedThroughProperty("Process_Setting_View_Label4")]
    private Label _Process_Setting_View_Label4;
    [AccessedThroughProperty("Process_Setting_View_Label5")]
    private Label _Process_Setting_View_Label5;
    [AccessedThroughProperty("PSSV_Mold_Surface_Temperature_Label")]
    private Label _PSSV_Mold_Surface_Temperature_Label;
    [AccessedThroughProperty("PSSV_Melt_Temperature_Label")]
    private Label _PSSV_Melt_Temperature_Label;
    [AccessedThroughProperty("Fill_Profile_Label13")]
    private Label _Fill_Profile_Label13;
    [AccessedThroughProperty("PSSV_Fill_Profile_11_1_Label")]
    private Label _PSSV_Fill_Profile_11_1_Label;
    [AccessedThroughProperty("PSSV_Fill_Profile_11_2_Label")]
    private Label _PSSV_Fill_Profile_11_2_Label;

    [DebuggerNonUserCode]
    static Form2()
    {
    }

    [DebuggerNonUserCode]
    public Form2()
    {
      Form2.__ENCAddToList((object) this);
      this.InitializeComponent();
    }

    [DebuggerNonUserCode]
    private static void __ENCAddToList(object value)
    {
      lock (Form2.__ENCList)
      {
        if (Form2.__ENCList.Count == Form2.__ENCList.Capacity)
        {
          int index1 = 0;
          int num = checked (Form2.__ENCList.Count - 1);
          int index2 = 0;
          while (index2 <= num)
          {
            if (Form2.__ENCList[index2].IsAlive)
            {
              if (index2 != index1)
                Form2.__ENCList[index1] = Form2.__ENCList[index2];
              checked { ++index1; }
            }
            checked { ++index2; }
          }
          Form2.__ENCList.RemoveRange(index1, checked (Form2.__ENCList.Count - index1));
          Form2.__ENCList.Capacity = Form2.__ENCList.Count;
        }
        Form2.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
      }
    }

    [DebuggerNonUserCode]
    protected override void Dispose(bool disposing)
    {
      try
      {
        if (!disposing || this.components == null)
          return;
        this.components.Dispose();
      }
      finally
      {
        base.Dispose(disposing);
      }
    }

    [DebuggerStepThrough]
    private void InitializeComponent()
    {
      ComponentResourceManager componentResourceManager = new ComponentResourceManager(typeof (Form2));
      this.Process_Setting_View_Table = new TableLayoutPanel();
      this.Process_Setting_View_Label1 = new Label();
      this.Process_Setting_View_Label2 = new Label();
      this.Process_Setting_View_Label3 = new Label();
      this.PSSV_Study_Name_Label = new Label();
      this.PSSV_Mesh_Type_Label = new Label();
      this.PSSV_Analysis_Sequence_Label = new Label();
      this.Process_Setting_View_Label16 = new Label();
      this.Process_Setting_View_Label15 = new Label();
      this.Process_Setting_View_Label14 = new Label();
      this.Process_Setting_View_Label13 = new Label();
      this.Process_Setting_View_Label12 = new Label();
      this.Process_Setting_View_Label11 = new Label();
      this.Process_Setting_View_Label10 = new Label();
      this.Process_Setting_View_Label9 = new Label();
      this.Process_Setting_View_Label8 = new Label();
      this.Process_Setting_View_Label7 = new Label();
      this.Process_Setting_View_Label6 = new Label();
      this.PSSV_Material_Family_Label = new Label();
      this.PSSV_Material_Name_Label = new Label();
      this.PSSV_Material_Manufacturer_Label = new Label();
      this.PSSV_Cooling_Time_Label = new Label();
      this.PSSV_Cooling_Control_Label = new Label();
      this.PSSV_Packing_Control_Label = new Label();
      this.PSSV_Packing_VP_Label = new Label();
      this.PSSV_Packing_VP_Control_Label = new Label();
      this.PSSV_Flow_Rate_Label = new Label();
      this.PSSV_Fill_TIme_Label = new Label();
      this.PSSV_Fill_Control_Label = new Label();
      this.Process_Setting_View_Label4 = new Label();
      this.Process_Setting_View_Label5 = new Label();
      this.PSSV_Mold_Surface_Temperature_Label = new Label();
      this.PSSV_Melt_Temperature_Label = new Label();
      this.Process_Setting_View = new Button();
      this.Fill_Profile = new GroupBox();
      this.Fill_Pofile_Table = new TableLayoutPanel();
      this.Fill_Profile_Label12 = new Label();
      this.Fill_Profile_Label11 = new Label();
      this.Fill_Profile_Label10 = new Label();
      this.Fill_Profile_Label9 = new Label();
      this.Fill_Profile_Label8 = new Label();
      this.Fill_Profile_Label7 = new Label();
      this.Fill_Profile_Label6 = new Label();
      this.Fill_Profile_Label5 = new Label();
      this.Fill_Profile_Label4 = new Label();
      this.Fill_Profile_Label3 = new Label();
      this.Fill_Profile_Label2 = new Label();
      this.Fill_Profile_Label1 = new Label();
      this.PSSV_Fill_Profile_1_1_Label = new Label();
      this.PSSV_Fill_Profile_2_1_Label = new Label();
      this.PSSV_Fill_Profile_3_1_Label = new Label();
      this.PSSV_Fill_Profile_4_1_Label = new Label();
      this.PSSV_Fill_Profile_5_1_Label = new Label();
      this.PSSV_Fill_Profile_6_1_Label = new Label();
      this.PSSV_Fill_Profile_7_1_Label = new Label();
      this.PSSV_Fill_Profile_8_1_Label = new Label();
      this.PSSV_Fill_Profile_9_1_Label = new Label();
      this.PSSV_Fill_Profile_10_1_Label = new Label();
      this.PSSV_Fill_Profile_1_2_Label = new Label();
      this.PSSV_Fill_Profile_2_2_Label = new Label();
      this.PSSV_Fill_Profile_3_2_Label = new Label();
      this.PSSV_Fill_Profile_4_2_Label = new Label();
      this.PSSV_Fill_Profile_5_2_Label = new Label();
      this.PSSV_Fill_Profile_6_2_Label = new Label();
      this.PSSV_Fill_Profile_7_2_Label = new Label();
      this.PSSV_Fill_Profile_8_2_Label = new Label();
      this.PSSV_Fill_Profile_9_2_Label = new Label();
      this.PSSV_Fill_Profile_10_2_Label = new Label();
      this.Fill_Profile_Label13 = new Label();
      this.PSSV_Fill_Profile_11_1_Label = new Label();
      this.PSSV_Fill_Profile_11_2_Label = new Label();
      this.Pack_Profile = new GroupBox();
      this.TableLayoutPanel1 = new TableLayoutPanel();
      this.Pack_Profile_Label12 = new Label();
      this.Pack_Profile_Label11 = new Label();
      this.Pack_Profile_Label10 = new Label();
      this.Pack_Profile_Label9 = new Label();
      this.Pack_Profile_Label8 = new Label();
      this.Pack_Profile_Label7 = new Label();
      this.Pack_Profile_Label6 = new Label();
      this.Pack_Profile_Label5 = new Label();
      this.Pack_Profile_Label4 = new Label();
      this.Pack_Profile_Label3 = new Label();
      this.Pack_Profile_Label2 = new Label();
      this.Pack_Profile_Label1 = new Label();
      this.PSSV_Packing_Profile_1_1_Label = new Label();
      this.PSSV_Packing_Profile_2_1_Label = new Label();
      this.PSSV_Packing_Profile_3_1_Label = new Label();
      this.PSSV_Packing_Profile_4_1_Label = new Label();
      this.PSSV_Packing_Profile_5_1_Label = new Label();
      this.PSSV_Packing_Profile_6_1_Label = new Label();
      this.PSSV_Packing_Profile_7_1_Label = new Label();
      this.PSSV_Packing_Profile_8_1_Label = new Label();
      this.PSSV_Packing_Profile_9_1_Label = new Label();
      this.PSSV_Packing_Profile_10_1_Label = new Label();
      this.PSSV_Packing_Profile_1_2_Label = new Label();
      this.PSSV_Packing_Profile_2_2_Label = new Label();
      this.PSSV_Packing_Profile_3_2_Label = new Label();
      this.PSSV_Packing_Profile_4_2_Label = new Label();
      this.PSSV_Packing_Profile_5_2_Label = new Label();
      this.PSSV_Packing_Profile_6_2_Label = new Label();
      this.PSSV_Packing_Profile_7_2_Label = new Label();
      this.PSSV_Packing_Profile_8_2_Label = new Label();
      this.PSSV_Packing_Profile_9_2_Label = new Label();
      this.PSSV_Packing_Profile_10_2_Label = new Label();
      this.Process_Setting_View_Table.SuspendLayout();
      this.Fill_Profile.SuspendLayout();
      this.Fill_Pofile_Table.SuspendLayout();
      this.Pack_Profile.SuspendLayout();
      this.TableLayoutPanel1.SuspendLayout();
      this.SuspendLayout();
      this.Process_Setting_View_Table.CellBorderStyle = TableLayoutPanelCellBorderStyle.InsetDouble;
      this.Process_Setting_View_Table.ColumnCount = 2;
      this.Process_Setting_View_Table.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50f));
      this.Process_Setting_View_Table.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50f));
      this.Process_Setting_View_Table.Controls.Add((Control) this.Process_Setting_View_Label1, 0, 0);
      this.Process_Setting_View_Table.Controls.Add((Control) this.Process_Setting_View_Label2, 0, 1);
      this.Process_Setting_View_Table.Controls.Add((Control) this.Process_Setting_View_Label3, 0, 2);
      this.Process_Setting_View_Table.Controls.Add((Control) this.PSSV_Study_Name_Label, 1, 0);
      this.Process_Setting_View_Table.Controls.Add((Control) this.PSSV_Mesh_Type_Label, 1, 1);
      this.Process_Setting_View_Table.Controls.Add((Control) this.PSSV_Analysis_Sequence_Label, 1, 2);
      this.Process_Setting_View_Table.Controls.Add((Control) this.Process_Setting_View_Label16, 0, 15);
      this.Process_Setting_View_Table.Controls.Add((Control) this.Process_Setting_View_Label15, 0, 14);
      this.Process_Setting_View_Table.Controls.Add((Control) this.Process_Setting_View_Label14, 0, 13);
      this.Process_Setting_View_Table.Controls.Add((Control) this.Process_Setting_View_Label13, 0, 12);
      this.Process_Setting_View_Table.Controls.Add((Control) this.Process_Setting_View_Label12, 0, 11);
      this.Process_Setting_View_Table.Controls.Add((Control) this.Process_Setting_View_Label11, 0, 10);
      this.Process_Setting_View_Table.Controls.Add((Control) this.Process_Setting_View_Label10, 0, 9);
      this.Process_Setting_View_Table.Controls.Add((Control) this.Process_Setting_View_Label9, 0, 8);
      this.Process_Setting_View_Table.Controls.Add((Control) this.Process_Setting_View_Label8, 0, 7);
      this.Process_Setting_View_Table.Controls.Add((Control) this.Process_Setting_View_Label7, 0, 6);
      this.Process_Setting_View_Table.Controls.Add((Control) this.Process_Setting_View_Label6, 0, 5);
      this.Process_Setting_View_Table.Controls.Add((Control) this.PSSV_Material_Family_Label, 1, 15);
      this.Process_Setting_View_Table.Controls.Add((Control) this.PSSV_Material_Name_Label, 1, 14);
      this.Process_Setting_View_Table.Controls.Add((Control) this.PSSV_Material_Manufacturer_Label, 1, 13);
      this.Process_Setting_View_Table.Controls.Add((Control) this.PSSV_Cooling_Time_Label, 1, 12);
      this.Process_Setting_View_Table.Controls.Add((Control) this.PSSV_Cooling_Control_Label, 1, 11);
      this.Process_Setting_View_Table.Controls.Add((Control) this.PSSV_Packing_Control_Label, 1, 10);
      this.Process_Setting_View_Table.Controls.Add((Control) this.PSSV_Packing_VP_Label, 1, 9);
      this.Process_Setting_View_Table.Controls.Add((Control) this.PSSV_Packing_VP_Control_Label, 1, 8);
      this.Process_Setting_View_Table.Controls.Add((Control) this.PSSV_Flow_Rate_Label, 1, 7);
      this.Process_Setting_View_Table.Controls.Add((Control) this.PSSV_Fill_TIme_Label, 1, 6);
      this.Process_Setting_View_Table.Controls.Add((Control) this.PSSV_Fill_Control_Label, 1, 5);
      this.Process_Setting_View_Table.Controls.Add((Control) this.Process_Setting_View_Label4, 0, 3);
      this.Process_Setting_View_Table.Controls.Add((Control) this.Process_Setting_View_Label5, 0, 4);
      this.Process_Setting_View_Table.Controls.Add((Control) this.PSSV_Mold_Surface_Temperature_Label, 1, 3);
      this.Process_Setting_View_Table.Controls.Add((Control) this.PSSV_Melt_Temperature_Label, 1, 4);
      TableLayoutPanel settingViewTable1 = this.Process_Setting_View_Table;
      Point point1 = new Point(0, 62);
      Point point2 = point1;
      settingViewTable1.Location = point2;
      this.Process_Setting_View_Table.Name = "Process_Setting_View_Table";
      this.Process_Setting_View_Table.RowCount = 16;
      this.Process_Setting_View_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 6.249999f));
      this.Process_Setting_View_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 6.249999f));
      this.Process_Setting_View_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 6.249999f));
      this.Process_Setting_View_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 6.249999f));
      this.Process_Setting_View_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 6.249999f));
      this.Process_Setting_View_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 6.249999f));
      this.Process_Setting_View_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 6.249999f));
      this.Process_Setting_View_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 6.249999f));
      this.Process_Setting_View_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 6.249999f));
      this.Process_Setting_View_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 6.249999f));
      this.Process_Setting_View_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 6.249999f));
      this.Process_Setting_View_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 6.249999f));
      this.Process_Setting_View_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 6.249999f));
      this.Process_Setting_View_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 6.249999f));
      this.Process_Setting_View_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 6.249999f));
      this.Process_Setting_View_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 6.249999f));
      this.Process_Setting_View_Table.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
      TableLayoutPanel settingViewTable2 = this.Process_Setting_View_Table;
      Size size1 = new Size(398, 393);
      Size size2 = size1;
      settingViewTable2.Size = size2;
      this.Process_Setting_View_Table.TabIndex = 0;
      this.Process_Setting_View_Label1.AutoSize = true;
      this.Process_Setting_View_Label1.Dock = DockStyle.Fill;
      Label settingViewLabel1_1 = this.Process_Setting_View_Label1;
      point1 = new Point(6, 3);
      Point point3 = point1;
      settingViewLabel1_1.Location = point3;
      this.Process_Setting_View_Label1.Name = "Process_Setting_View_Label1";
      Label settingViewLabel1_2 = this.Process_Setting_View_Label1;
      size1 = new Size(188, 21);
      Size size3 = size1;
      settingViewLabel1_2.Size = size3;
      this.Process_Setting_View_Label1.TabIndex = 0;
      this.Process_Setting_View_Label1.Text = "스터디 이름";
      this.Process_Setting_View_Label1.TextAlign = ContentAlignment.MiddleCenter;
      this.Process_Setting_View_Label2.AutoSize = true;
      this.Process_Setting_View_Label2.Dock = DockStyle.Fill;
      Label settingViewLabel2_1 = this.Process_Setting_View_Label2;
      point1 = new Point(6, 27);
      Point point4 = point1;
      settingViewLabel2_1.Location = point4;
      this.Process_Setting_View_Label2.Name = "Process_Setting_View_Label2";
      Label settingViewLabel2_2 = this.Process_Setting_View_Label2;
      size1 = new Size(188, 21);
      Size size4 = size1;
      settingViewLabel2_2.Size = size4;
      this.Process_Setting_View_Label2.TabIndex = 1;
      this.Process_Setting_View_Label2.Text = "메쉬 타입";
      this.Process_Setting_View_Label2.TextAlign = ContentAlignment.MiddleCenter;
      this.Process_Setting_View_Label3.AutoSize = true;
      this.Process_Setting_View_Label3.Dock = DockStyle.Fill;
      Label settingViewLabel3_1 = this.Process_Setting_View_Label3;
      point1 = new Point(6, 51);
      Point point5 = point1;
      settingViewLabel3_1.Location = point5;
      this.Process_Setting_View_Label3.Name = "Process_Setting_View_Label3";
      Label settingViewLabel3_2 = this.Process_Setting_View_Label3;
      size1 = new Size(188, 21);
      Size size5 = size1;
      settingViewLabel3_2.Size = size5;
      this.Process_Setting_View_Label3.TabIndex = 2;
      this.Process_Setting_View_Label3.Text = "해석 시퀀스";
      this.Process_Setting_View_Label3.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Study_Name_Label.AutoSize = true;
      this.PSSV_Study_Name_Label.Dock = DockStyle.Fill;
      Label pssvStudyNameLabel1 = this.PSSV_Study_Name_Label;
      point1 = new Point(203, 3);
      Point point6 = point1;
      pssvStudyNameLabel1.Location = point6;
      this.PSSV_Study_Name_Label.Name = "PSSV_Study_Name_Label";
      Label pssvStudyNameLabel2 = this.PSSV_Study_Name_Label;
      size1 = new Size(189, 21);
      Size size6 = size1;
      pssvStudyNameLabel2.Size = size6;
      this.PSSV_Study_Name_Label.TabIndex = 15;
      this.PSSV_Study_Name_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Mesh_Type_Label.AutoSize = true;
      this.PSSV_Mesh_Type_Label.Dock = DockStyle.Fill;
      Label pssvMeshTypeLabel1 = this.PSSV_Mesh_Type_Label;
      point1 = new Point(203, 27);
      Point point7 = point1;
      pssvMeshTypeLabel1.Location = point7;
      this.PSSV_Mesh_Type_Label.Name = "PSSV_Mesh_Type_Label";
      Label pssvMeshTypeLabel2 = this.PSSV_Mesh_Type_Label;
      size1 = new Size(189, 21);
      Size size7 = size1;
      pssvMeshTypeLabel2.Size = size7;
      this.PSSV_Mesh_Type_Label.TabIndex = 16;
      this.PSSV_Mesh_Type_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Analysis_Sequence_Label.AutoSize = true;
      this.PSSV_Analysis_Sequence_Label.Dock = DockStyle.Fill;
      Label analysisSequenceLabel1 = this.PSSV_Analysis_Sequence_Label;
      point1 = new Point(203, 51);
      Point point8 = point1;
      analysisSequenceLabel1.Location = point8;
      this.PSSV_Analysis_Sequence_Label.Name = "PSSV_Analysis_Sequence_Label";
      Label analysisSequenceLabel2 = this.PSSV_Analysis_Sequence_Label;
      size1 = new Size(189, 21);
      Size size8 = size1;
      analysisSequenceLabel2.Size = size8;
      this.PSSV_Analysis_Sequence_Label.TabIndex = 17;
      this.PSSV_Analysis_Sequence_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.Process_Setting_View_Label16.AutoSize = true;
      this.Process_Setting_View_Label16.Dock = DockStyle.Fill;
      Label settingViewLabel16_1 = this.Process_Setting_View_Label16;
      point1 = new Point(6, 363);
      Point point9 = point1;
      settingViewLabel16_1.Location = point9;
      this.Process_Setting_View_Label16.Name = "Process_Setting_View_Label16";
      Label settingViewLabel16_2 = this.Process_Setting_View_Label16;
      size1 = new Size(188, 27);
      Size size9 = size1;
      settingViewLabel16_2.Size = size9;
      this.Process_Setting_View_Label16.TabIndex = 13;
      this.Process_Setting_View_Label16.Text = "선택 수지 종류";
      this.Process_Setting_View_Label16.TextAlign = ContentAlignment.MiddleCenter;
      this.Process_Setting_View_Label15.AutoSize = true;
      this.Process_Setting_View_Label15.Dock = DockStyle.Fill;
      Label settingViewLabel15_1 = this.Process_Setting_View_Label15;
      point1 = new Point(6, 339);
      Point point10 = point1;
      settingViewLabel15_1.Location = point10;
      this.Process_Setting_View_Label15.Name = "Process_Setting_View_Label15";
      Label settingViewLabel15_2 = this.Process_Setting_View_Label15;
      size1 = new Size(188, 21);
      Size size10 = size1;
      settingViewLabel15_2.Size = size10;
      this.Process_Setting_View_Label15.TabIndex = 12;
      this.Process_Setting_View_Label15.Text = "선택 수지 이름";
      this.Process_Setting_View_Label15.TextAlign = ContentAlignment.MiddleCenter;
      this.Process_Setting_View_Label14.AutoSize = true;
      this.Process_Setting_View_Label14.Dock = DockStyle.Fill;
      Label settingViewLabel14_1 = this.Process_Setting_View_Label14;
      point1 = new Point(6, 315);
      Point point11 = point1;
      settingViewLabel14_1.Location = point11;
      this.Process_Setting_View_Label14.Name = "Process_Setting_View_Label14";
      Label settingViewLabel14_2 = this.Process_Setting_View_Label14;
      size1 = new Size(188, 21);
      Size size11 = size1;
      settingViewLabel14_2.Size = size11;
      this.Process_Setting_View_Label14.TabIndex = 11;
      this.Process_Setting_View_Label14.Text = "선택 수지 회사";
      this.Process_Setting_View_Label14.TextAlign = ContentAlignment.MiddleCenter;
      this.Process_Setting_View_Label13.AutoSize = true;
      this.Process_Setting_View_Label13.Dock = DockStyle.Fill;
      Label settingViewLabel13_1 = this.Process_Setting_View_Label13;
      point1 = new Point(6, 291);
      Point point12 = point1;
      settingViewLabel13_1.Location = point12;
      this.Process_Setting_View_Label13.Name = "Process_Setting_View_Label13";
      Label settingViewLabel13_2 = this.Process_Setting_View_Label13;
      size1 = new Size(188, 21);
      Size size12 = size1;
      settingViewLabel13_2.Size = size12;
      this.Process_Setting_View_Label13.TabIndex = 10;
      this.Process_Setting_View_Label13.Text = "냉각 시간";
      this.Process_Setting_View_Label13.TextAlign = ContentAlignment.MiddleCenter;
      this.Process_Setting_View_Label12.AutoSize = true;
      this.Process_Setting_View_Label12.Dock = DockStyle.Fill;
      Label settingViewLabel12_1 = this.Process_Setting_View_Label12;
      point1 = new Point(6, 267);
      Point point13 = point1;
      settingViewLabel12_1.Location = point13;
      this.Process_Setting_View_Label12.Name = "Process_Setting_View_Label12";
      Label settingViewLabel12_2 = this.Process_Setting_View_Label12;
      size1 = new Size(188, 21);
      Size size13 = size1;
      settingViewLabel12_2.Size = size13;
      this.Process_Setting_View_Label12.TabIndex = 9;
      this.Process_Setting_View_Label12.Text = "냉각 컨트롤";
      this.Process_Setting_View_Label12.TextAlign = ContentAlignment.MiddleLeft;
      this.Process_Setting_View_Label11.AutoSize = true;
      this.Process_Setting_View_Label11.Dock = DockStyle.Fill;
      Label settingViewLabel11_1 = this.Process_Setting_View_Label11;
      point1 = new Point(6, 243);
      Point point14 = point1;
      settingViewLabel11_1.Location = point14;
      this.Process_Setting_View_Label11.Name = "Process_Setting_View_Label11";
      Label settingViewLabel11_2 = this.Process_Setting_View_Label11;
      size1 = new Size(188, 21);
      Size size14 = size1;
      settingViewLabel11_2.Size = size14;
      this.Process_Setting_View_Label11.TabIndex = 8;
      this.Process_Setting_View_Label11.Text = "보압 컨트롤";
      this.Process_Setting_View_Label11.TextAlign = ContentAlignment.MiddleLeft;
      this.Process_Setting_View_Label10.AutoSize = true;
      this.Process_Setting_View_Label10.Dock = DockStyle.Fill;
      Label settingViewLabel10_1 = this.Process_Setting_View_Label10;
      point1 = new Point(6, 219);
      Point point15 = point1;
      settingViewLabel10_1.Location = point15;
      this.Process_Setting_View_Label10.Name = "Process_Setting_View_Label10";
      Label settingViewLabel10_2 = this.Process_Setting_View_Label10;
      size1 = new Size(188, 21);
      Size size15 = size1;
      settingViewLabel10_2.Size = size15;
      this.Process_Setting_View_Label10.TabIndex = 7;
      this.Process_Setting_View_Label10.Text = "보압 전환점";
      this.Process_Setting_View_Label10.TextAlign = ContentAlignment.MiddleCenter;
      this.Process_Setting_View_Label9.AutoSize = true;
      this.Process_Setting_View_Label9.Dock = DockStyle.Fill;
      Label settingViewLabel9_1 = this.Process_Setting_View_Label9;
      point1 = new Point(6, 195);
      Point point16 = point1;
      settingViewLabel9_1.Location = point16;
      this.Process_Setting_View_Label9.Name = "Process_Setting_View_Label9";
      Label settingViewLabel9_2 = this.Process_Setting_View_Label9;
      size1 = new Size(188, 21);
      Size size16 = size1;
      settingViewLabel9_2.Size = size16;
      this.Process_Setting_View_Label9.TabIndex = 6;
      this.Process_Setting_View_Label9.Text = "보압 전환 컨트롤";
      this.Process_Setting_View_Label9.TextAlign = ContentAlignment.MiddleLeft;
      this.Process_Setting_View_Label8.AutoSize = true;
      this.Process_Setting_View_Label8.Dock = DockStyle.Fill;
      Label settingViewLabel8_1 = this.Process_Setting_View_Label8;
      point1 = new Point(6, 171);
      Point point17 = point1;
      settingViewLabel8_1.Location = point17;
      this.Process_Setting_View_Label8.Name = "Process_Setting_View_Label8";
      Label settingViewLabel8_2 = this.Process_Setting_View_Label8;
      size1 = new Size(188, 21);
      Size size17 = size1;
      settingViewLabel8_2.Size = size17;
      this.Process_Setting_View_Label8.TabIndex = 5;
      this.Process_Setting_View_Label8.Text = "사출 유량";
      this.Process_Setting_View_Label8.TextAlign = ContentAlignment.MiddleCenter;
      this.Process_Setting_View_Label7.AutoSize = true;
      this.Process_Setting_View_Label7.Dock = DockStyle.Fill;
      Label settingViewLabel7_1 = this.Process_Setting_View_Label7;
      point1 = new Point(6, 147);
      Point point18 = point1;
      settingViewLabel7_1.Location = point18;
      this.Process_Setting_View_Label7.Name = "Process_Setting_View_Label7";
      Label settingViewLabel7_2 = this.Process_Setting_View_Label7;
      size1 = new Size(188, 21);
      Size size18 = size1;
      settingViewLabel7_2.Size = size18;
      this.Process_Setting_View_Label7.TabIndex = 4;
      this.Process_Setting_View_Label7.Text = "사출 시간";
      this.Process_Setting_View_Label7.TextAlign = ContentAlignment.MiddleCenter;
      this.Process_Setting_View_Label6.AutoSize = true;
      this.Process_Setting_View_Label6.Dock = DockStyle.Fill;
      Label settingViewLabel6_1 = this.Process_Setting_View_Label6;
      point1 = new Point(6, 123);
      Point point19 = point1;
      settingViewLabel6_1.Location = point19;
      this.Process_Setting_View_Label6.Name = "Process_Setting_View_Label6";
      Label settingViewLabel6_2 = this.Process_Setting_View_Label6;
      size1 = new Size(188, 21);
      Size size19 = size1;
      settingViewLabel6_2.Size = size19;
      this.Process_Setting_View_Label6.TabIndex = 3;
      this.Process_Setting_View_Label6.Text = "사출 컨트롤";
      this.Process_Setting_View_Label6.TextAlign = ContentAlignment.MiddleLeft;
      this.PSSV_Material_Family_Label.AutoSize = true;
      this.PSSV_Material_Family_Label.Dock = DockStyle.Fill;
      Label materialFamilyLabel1 = this.PSSV_Material_Family_Label;
      point1 = new Point(203, 363);
      Point point20 = point1;
      materialFamilyLabel1.Location = point20;
      this.PSSV_Material_Family_Label.Name = "PSSV_Material_Family_Label";
      Label materialFamilyLabel2 = this.PSSV_Material_Family_Label;
      size1 = new Size(189, 27);
      Size size20 = size1;
      materialFamilyLabel2.Size = size20;
      this.PSSV_Material_Family_Label.TabIndex = 28;
      this.PSSV_Material_Family_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Material_Name_Label.AutoSize = true;
      this.PSSV_Material_Name_Label.Dock = DockStyle.Fill;
      Label materialNameLabel1 = this.PSSV_Material_Name_Label;
      point1 = new Point(203, 339);
      Point point21 = point1;
      materialNameLabel1.Location = point21;
      this.PSSV_Material_Name_Label.Name = "PSSV_Material_Name_Label";
      Label materialNameLabel2 = this.PSSV_Material_Name_Label;
      size1 = new Size(189, 21);
      Size size21 = size1;
      materialNameLabel2.Size = size21;
      this.PSSV_Material_Name_Label.TabIndex = 27;
      this.PSSV_Material_Name_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Material_Manufacturer_Label.AutoSize = true;
      this.PSSV_Material_Manufacturer_Label.Dock = DockStyle.Fill;
      Label manufacturerLabel1 = this.PSSV_Material_Manufacturer_Label;
      point1 = new Point(203, 315);
      Point point22 = point1;
      manufacturerLabel1.Location = point22;
      this.PSSV_Material_Manufacturer_Label.Name = "PSSV_Material_Manufacturer_Label";
      Label manufacturerLabel2 = this.PSSV_Material_Manufacturer_Label;
      size1 = new Size(189, 21);
      Size size22 = size1;
      manufacturerLabel2.Size = size22;
      this.PSSV_Material_Manufacturer_Label.TabIndex = 26;
      this.PSSV_Material_Manufacturer_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Cooling_Time_Label.AutoSize = true;
      this.PSSV_Cooling_Time_Label.Dock = DockStyle.Fill;
      Label coolingTimeLabel1 = this.PSSV_Cooling_Time_Label;
      point1 = new Point(203, 291);
      Point point23 = point1;
      coolingTimeLabel1.Location = point23;
      this.PSSV_Cooling_Time_Label.Name = "PSSV_Cooling_Time_Label";
      Label coolingTimeLabel2 = this.PSSV_Cooling_Time_Label;
      size1 = new Size(189, 21);
      Size size23 = size1;
      coolingTimeLabel2.Size = size23;
      this.PSSV_Cooling_Time_Label.TabIndex = 25;
      this.PSSV_Cooling_Time_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Cooling_Control_Label.AutoSize = true;
      this.PSSV_Cooling_Control_Label.Dock = DockStyle.Fill;
      Label coolingControlLabel1 = this.PSSV_Cooling_Control_Label;
      point1 = new Point(203, 267);
      Point point24 = point1;
      coolingControlLabel1.Location = point24;
      this.PSSV_Cooling_Control_Label.Name = "PSSV_Cooling_Control_Label";
      Label coolingControlLabel2 = this.PSSV_Cooling_Control_Label;
      size1 = new Size(189, 21);
      Size size24 = size1;
      coolingControlLabel2.Size = size24;
      this.PSSV_Cooling_Control_Label.TabIndex = 24;
      this.PSSV_Cooling_Control_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Packing_Control_Label.AutoSize = true;
      this.PSSV_Packing_Control_Label.Dock = DockStyle.Fill;
      Label packingControlLabel1 = this.PSSV_Packing_Control_Label;
      point1 = new Point(203, 243);
      Point point25 = point1;
      packingControlLabel1.Location = point25;
      this.PSSV_Packing_Control_Label.Name = "PSSV_Packing_Control_Label";
      Label packingControlLabel2 = this.PSSV_Packing_Control_Label;
      size1 = new Size(189, 21);
      Size size25 = size1;
      packingControlLabel2.Size = size25;
      this.PSSV_Packing_Control_Label.TabIndex = 23;
      this.PSSV_Packing_Control_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Packing_VP_Label.AutoSize = true;
      this.PSSV_Packing_VP_Label.Dock = DockStyle.Fill;
      Label pssvPackingVpLabel1 = this.PSSV_Packing_VP_Label;
      point1 = new Point(203, 219);
      Point point26 = point1;
      pssvPackingVpLabel1.Location = point26;
      this.PSSV_Packing_VP_Label.Name = "PSSV_Packing_VP_Label";
      Label pssvPackingVpLabel2 = this.PSSV_Packing_VP_Label;
      size1 = new Size(189, 21);
      Size size26 = size1;
      pssvPackingVpLabel2.Size = size26;
      this.PSSV_Packing_VP_Label.TabIndex = 22;
      this.PSSV_Packing_VP_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Packing_VP_Control_Label.AutoSize = true;
      this.PSSV_Packing_VP_Control_Label.Dock = DockStyle.Fill;
      Label packingVpControlLabel1 = this.PSSV_Packing_VP_Control_Label;
      point1 = new Point(203, 195);
      Point point27 = point1;
      packingVpControlLabel1.Location = point27;
      this.PSSV_Packing_VP_Control_Label.Name = "PSSV_Packing_VP_Control_Label";
      Label packingVpControlLabel2 = this.PSSV_Packing_VP_Control_Label;
      size1 = new Size(189, 21);
      Size size27 = size1;
      packingVpControlLabel2.Size = size27;
      this.PSSV_Packing_VP_Control_Label.TabIndex = 21;
      this.PSSV_Packing_VP_Control_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Flow_Rate_Label.AutoSize = true;
      this.PSSV_Flow_Rate_Label.Dock = DockStyle.Fill;
      Label pssvFlowRateLabel1 = this.PSSV_Flow_Rate_Label;
      point1 = new Point(203, 171);
      Point point28 = point1;
      pssvFlowRateLabel1.Location = point28;
      this.PSSV_Flow_Rate_Label.Name = "PSSV_Flow_Rate_Label";
      Label pssvFlowRateLabel2 = this.PSSV_Flow_Rate_Label;
      size1 = new Size(189, 21);
      Size size28 = size1;
      pssvFlowRateLabel2.Size = size28;
      this.PSSV_Flow_Rate_Label.TabIndex = 20;
      this.PSSV_Flow_Rate_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Fill_TIme_Label.AutoSize = true;
      this.PSSV_Fill_TIme_Label.Dock = DockStyle.Fill;
      Label pssvFillTimeLabel1 = this.PSSV_Fill_TIme_Label;
      point1 = new Point(203, 147);
      Point point29 = point1;
      pssvFillTimeLabel1.Location = point29;
      this.PSSV_Fill_TIme_Label.Name = "PSSV_Fill_TIme_Label";
      Label pssvFillTimeLabel2 = this.PSSV_Fill_TIme_Label;
      size1 = new Size(189, 21);
      Size size29 = size1;
      pssvFillTimeLabel2.Size = size29;
      this.PSSV_Fill_TIme_Label.TabIndex = 19;
      this.PSSV_Fill_TIme_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Fill_Control_Label.AutoSize = true;
      this.PSSV_Fill_Control_Label.Dock = DockStyle.Fill;
      Label fillControlLabel1 = this.PSSV_Fill_Control_Label;
      point1 = new Point(203, 123);
      Point point30 = point1;
      fillControlLabel1.Location = point30;
      this.PSSV_Fill_Control_Label.Name = "PSSV_Fill_Control_Label";
      Label fillControlLabel2 = this.PSSV_Fill_Control_Label;
      size1 = new Size(189, 21);
      Size size30 = size1;
      fillControlLabel2.Size = size30;
      this.PSSV_Fill_Control_Label.TabIndex = 18;
      this.PSSV_Fill_Control_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.Process_Setting_View_Label4.AutoSize = true;
      this.Process_Setting_View_Label4.Dock = DockStyle.Fill;
      Label settingViewLabel4_1 = this.Process_Setting_View_Label4;
      point1 = new Point(6, 75);
      Point point31 = point1;
      settingViewLabel4_1.Location = point31;
      this.Process_Setting_View_Label4.Name = "Process_Setting_View_Label4";
      Label settingViewLabel4_2 = this.Process_Setting_View_Label4;
      size1 = new Size(188, 21);
      Size size31 = size1;
      settingViewLabel4_2.Size = size31;
      this.Process_Setting_View_Label4.TabIndex = 30;
      this.Process_Setting_View_Label4.Text = "금형 표면 온도";
      this.Process_Setting_View_Label4.TextAlign = ContentAlignment.MiddleCenter;
      this.Process_Setting_View_Label5.AutoSize = true;
      this.Process_Setting_View_Label5.Dock = DockStyle.Fill;
      Label settingViewLabel5_1 = this.Process_Setting_View_Label5;
      point1 = new Point(6, 99);
      Point point32 = point1;
      settingViewLabel5_1.Location = point32;
      this.Process_Setting_View_Label5.Name = "Process_Setting_View_Label5";
      Label settingViewLabel5_2 = this.Process_Setting_View_Label5;
      size1 = new Size(188, 21);
      Size size32 = size1;
      settingViewLabel5_2.Size = size32;
      this.Process_Setting_View_Label5.TabIndex = 31;
      this.Process_Setting_View_Label5.Text = "수지 용융 온도";
      this.Process_Setting_View_Label5.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Mold_Surface_Temperature_Label.AutoSize = true;
      this.PSSV_Mold_Surface_Temperature_Label.Dock = DockStyle.Fill;
      Label temperatureLabel1 = this.PSSV_Mold_Surface_Temperature_Label;
      point1 = new Point(203, 75);
      Point point33 = point1;
      temperatureLabel1.Location = point33;
      this.PSSV_Mold_Surface_Temperature_Label.Name = "PSSV_Mold_Surface_Temperature_Label";
      Label temperatureLabel2 = this.PSSV_Mold_Surface_Temperature_Label;
      size1 = new Size(189, 21);
      Size size33 = size1;
      temperatureLabel2.Size = size33;
      this.PSSV_Mold_Surface_Temperature_Label.TabIndex = 32;
      this.PSSV_Mold_Surface_Temperature_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Melt_Temperature_Label.AutoSize = true;
      this.PSSV_Melt_Temperature_Label.Dock = DockStyle.Fill;
      Label temperatureLabel3 = this.PSSV_Melt_Temperature_Label;
      point1 = new Point(203, 99);
      Point point34 = point1;
      temperatureLabel3.Location = point34;
      this.PSSV_Melt_Temperature_Label.Name = "PSSV_Melt_Temperature_Label";
      Label temperatureLabel4 = this.PSSV_Melt_Temperature_Label;
      size1 = new Size(189, 21);
      Size size34 = size1;
      temperatureLabel4.Size = size34;
      this.PSSV_Melt_Temperature_Label.TabIndex = 33;
      this.PSSV_Melt_Temperature_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.Process_Setting_View.Font = new Font("굴림", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 129);
      Button processSettingView1 = this.Process_Setting_View;
      point1 = new Point(0, 12);
      Point point35 = point1;
      processSettingView1.Location = point35;
      this.Process_Setting_View.Name = "Process_Setting_View";
      Button processSettingView2 = this.Process_Setting_View;
      size1 = new Size(398, 44);
      Size size35 = size1;
      processSettingView2.Size = size35;
      this.Process_Setting_View.TabIndex = 1;
      this.Process_Setting_View.Text = "공정조건 확인하기";
      this.Process_Setting_View.UseVisualStyleBackColor = true;
      this.Fill_Profile.Controls.Add((Control) this.Fill_Pofile_Table);
      GroupBox fillProfile1 = this.Fill_Profile;
      point1 = new Point(405, 12);
      Point point36 = point1;
      fillProfile1.Location = point36;
      this.Fill_Profile.Name = "Fill_Profile";
      GroupBox fillProfile2 = this.Fill_Profile;
      size1 = new Size(182, 221);
      Size size36 = size1;
      fillProfile2.Size = size36;
      this.Fill_Profile.TabIndex = 2;
      this.Fill_Profile.TabStop = false;
      this.Fill_Profile.Text = "Fill_Profile";
      this.Fill_Pofile_Table.CellBorderStyle = TableLayoutPanelCellBorderStyle.InsetDouble;
      this.Fill_Pofile_Table.ColumnCount = 3;
      this.Fill_Pofile_Table.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20f));
      this.Fill_Pofile_Table.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 40f));
      this.Fill_Pofile_Table.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 40f));
      this.Fill_Pofile_Table.Controls.Add((Control) this.Fill_Profile_Label12, 2, 0);
      this.Fill_Pofile_Table.Controls.Add((Control) this.Fill_Profile_Label11, 1, 0);
      this.Fill_Pofile_Table.Controls.Add((Control) this.Fill_Profile_Label10, 0, 10);
      this.Fill_Pofile_Table.Controls.Add((Control) this.Fill_Profile_Label9, 0, 9);
      this.Fill_Pofile_Table.Controls.Add((Control) this.Fill_Profile_Label8, 0, 8);
      this.Fill_Pofile_Table.Controls.Add((Control) this.Fill_Profile_Label7, 0, 7);
      this.Fill_Pofile_Table.Controls.Add((Control) this.Fill_Profile_Label6, 0, 6);
      this.Fill_Pofile_Table.Controls.Add((Control) this.Fill_Profile_Label5, 0, 5);
      this.Fill_Pofile_Table.Controls.Add((Control) this.Fill_Profile_Label4, 0, 4);
      this.Fill_Pofile_Table.Controls.Add((Control) this.Fill_Profile_Label3, 0, 3);
      this.Fill_Pofile_Table.Controls.Add((Control) this.Fill_Profile_Label2, 0, 2);
      this.Fill_Pofile_Table.Controls.Add((Control) this.Fill_Profile_Label1, 0, 1);
      this.Fill_Pofile_Table.Controls.Add((Control) this.PSSV_Fill_Profile_1_1_Label, 1, 1);
      this.Fill_Pofile_Table.Controls.Add((Control) this.PSSV_Fill_Profile_2_1_Label, 1, 2);
      this.Fill_Pofile_Table.Controls.Add((Control) this.PSSV_Fill_Profile_3_1_Label, 1, 3);
      this.Fill_Pofile_Table.Controls.Add((Control) this.PSSV_Fill_Profile_4_1_Label, 1, 4);
      this.Fill_Pofile_Table.Controls.Add((Control) this.PSSV_Fill_Profile_5_1_Label, 1, 5);
      this.Fill_Pofile_Table.Controls.Add((Control) this.PSSV_Fill_Profile_6_1_Label, 1, 6);
      this.Fill_Pofile_Table.Controls.Add((Control) this.PSSV_Fill_Profile_7_1_Label, 1, 7);
      this.Fill_Pofile_Table.Controls.Add((Control) this.PSSV_Fill_Profile_8_1_Label, 1, 8);
      this.Fill_Pofile_Table.Controls.Add((Control) this.PSSV_Fill_Profile_9_1_Label, 1, 9);
      this.Fill_Pofile_Table.Controls.Add((Control) this.PSSV_Fill_Profile_10_1_Label, 1, 10);
      this.Fill_Pofile_Table.Controls.Add((Control) this.PSSV_Fill_Profile_1_2_Label, 2, 1);
      this.Fill_Pofile_Table.Controls.Add((Control) this.PSSV_Fill_Profile_2_2_Label, 2, 2);
      this.Fill_Pofile_Table.Controls.Add((Control) this.PSSV_Fill_Profile_3_2_Label, 2, 3);
      this.Fill_Pofile_Table.Controls.Add((Control) this.PSSV_Fill_Profile_4_2_Label, 2, 4);
      this.Fill_Pofile_Table.Controls.Add((Control) this.PSSV_Fill_Profile_5_2_Label, 2, 5);
      this.Fill_Pofile_Table.Controls.Add((Control) this.PSSV_Fill_Profile_6_2_Label, 2, 6);
      this.Fill_Pofile_Table.Controls.Add((Control) this.PSSV_Fill_Profile_7_2_Label, 2, 7);
      this.Fill_Pofile_Table.Controls.Add((Control) this.PSSV_Fill_Profile_8_2_Label, 2, 8);
      this.Fill_Pofile_Table.Controls.Add((Control) this.PSSV_Fill_Profile_9_2_Label, 2, 9);
      this.Fill_Pofile_Table.Controls.Add((Control) this.PSSV_Fill_Profile_10_2_Label, 2, 10);
      this.Fill_Pofile_Table.Controls.Add((Control) this.Fill_Profile_Label13, 0, 11);
      this.Fill_Pofile_Table.Controls.Add((Control) this.PSSV_Fill_Profile_11_1_Label, 1, 11);
      this.Fill_Pofile_Table.Controls.Add((Control) this.PSSV_Fill_Profile_11_2_Label, 2, 11);
      this.Fill_Pofile_Table.Dock = DockStyle.Fill;
      TableLayoutPanel fillPofileTable1 = this.Fill_Pofile_Table;
      point1 = new Point(3, 17);
      Point point37 = point1;
      fillPofileTable1.Location = point37;
      this.Fill_Pofile_Table.Name = "Fill_Pofile_Table";
      this.Fill_Pofile_Table.RowCount = 12;
      this.Fill_Pofile_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 8.333333f));
      this.Fill_Pofile_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 8.333333f));
      this.Fill_Pofile_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 8.333333f));
      this.Fill_Pofile_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 8.333333f));
      this.Fill_Pofile_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 8.333333f));
      this.Fill_Pofile_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 8.333333f));
      this.Fill_Pofile_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 8.333333f));
      this.Fill_Pofile_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 8.333333f));
      this.Fill_Pofile_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 8.333333f));
      this.Fill_Pofile_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 8.333333f));
      this.Fill_Pofile_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 8.333333f));
      this.Fill_Pofile_Table.RowStyles.Add(new RowStyle(SizeType.Percent, 8.333333f));
      TableLayoutPanel fillPofileTable2 = this.Fill_Pofile_Table;
      size1 = new Size(176, 201);
      Size size37 = size1;
      fillPofileTable2.Size = size37;
      this.Fill_Pofile_Table.TabIndex = 0;
      this.Fill_Profile_Label12.AutoSize = true;
      this.Fill_Profile_Label12.Dock = DockStyle.Fill;
      Label fillProfileLabel12_1 = this.Fill_Profile_Label12;
      point1 = new Point(109, 3);
      Point point38 = point1;
      fillProfileLabel12_1.Location = point38;
      this.Fill_Profile_Label12.Name = "Fill_Profile_Label12";
      Label fillProfileLabel12_2 = this.Fill_Profile_Label12;
      size1 = new Size(61, 13);
      Size size38 = size1;
      fillProfileLabel12_2.Size = size38;
      this.Fill_Profile_Label12.TabIndex = 11;
      this.Fill_Profile_Label12.Text = "속도/유량";
      this.Fill_Profile_Label12.TextAlign = ContentAlignment.MiddleCenter;
      this.Fill_Profile_Label11.AutoSize = true;
      this.Fill_Profile_Label11.Dock = DockStyle.Fill;
      Label fillProfileLabel11_1 = this.Fill_Profile_Label11;
      point1 = new Point(41, 3);
      Point point39 = point1;
      fillProfileLabel11_1.Location = point39;
      this.Fill_Profile_Label11.Name = "Fill_Profile_Label11";
      Label fillProfileLabel11_2 = this.Fill_Profile_Label11;
      size1 = new Size(59, 13);
      Size size39 = size1;
      fillProfileLabel11_2.Size = size39;
      this.Fill_Profile_Label11.TabIndex = 10;
      this.Fill_Profile_Label11.Text = "위치/부피";
      this.Fill_Profile_Label11.TextAlign = ContentAlignment.MiddleCenter;
      this.Fill_Profile_Label10.AutoSize = true;
      this.Fill_Profile_Label10.Dock = DockStyle.Fill;
      this.Fill_Profile_Label10.Font = new Font("굴림", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 129);
      Label fillProfileLabel10_1 = this.Fill_Profile_Label10;
      point1 = new Point(6, 163);
      Point point40 = point1;
      fillProfileLabel10_1.Location = point40;
      this.Fill_Profile_Label10.Name = "Fill_Profile_Label10";
      Label fillProfileLabel10_2 = this.Fill_Profile_Label10;
      size1 = new Size(26, 13);
      Size size40 = size1;
      fillProfileLabel10_2.Size = size40;
      this.Fill_Profile_Label10.TabIndex = 9;
      this.Fill_Profile_Label10.Text = "10";
      this.Fill_Profile_Label10.TextAlign = ContentAlignment.MiddleCenter;
      this.Fill_Profile_Label9.AutoSize = true;
      this.Fill_Profile_Label9.Dock = DockStyle.Fill;
      Label fillProfileLabel9_1 = this.Fill_Profile_Label9;
      point1 = new Point(6, 147);
      Point point41 = point1;
      fillProfileLabel9_1.Location = point41;
      this.Fill_Profile_Label9.Name = "Fill_Profile_Label9";
      Label fillProfileLabel9_2 = this.Fill_Profile_Label9;
      size1 = new Size(26, 13);
      Size size41 = size1;
      fillProfileLabel9_2.Size = size41;
      this.Fill_Profile_Label9.TabIndex = 8;
      this.Fill_Profile_Label9.Text = "9";
      this.Fill_Profile_Label9.TextAlign = ContentAlignment.MiddleCenter;
      this.Fill_Profile_Label8.AutoSize = true;
      this.Fill_Profile_Label8.Dock = DockStyle.Fill;
      Label fillProfileLabel8_1 = this.Fill_Profile_Label8;
      point1 = new Point(6, 131);
      Point point42 = point1;
      fillProfileLabel8_1.Location = point42;
      this.Fill_Profile_Label8.Name = "Fill_Profile_Label8";
      Label fillProfileLabel8_2 = this.Fill_Profile_Label8;
      size1 = new Size(26, 13);
      Size size42 = size1;
      fillProfileLabel8_2.Size = size42;
      this.Fill_Profile_Label8.TabIndex = 7;
      this.Fill_Profile_Label8.Text = "8";
      this.Fill_Profile_Label8.TextAlign = ContentAlignment.MiddleCenter;
      this.Fill_Profile_Label7.AutoSize = true;
      this.Fill_Profile_Label7.Dock = DockStyle.Fill;
      Label fillProfileLabel7_1 = this.Fill_Profile_Label7;
      point1 = new Point(6, 115);
      Point point43 = point1;
      fillProfileLabel7_1.Location = point43;
      this.Fill_Profile_Label7.Name = "Fill_Profile_Label7";
      Label fillProfileLabel7_2 = this.Fill_Profile_Label7;
      size1 = new Size(26, 13);
      Size size43 = size1;
      fillProfileLabel7_2.Size = size43;
      this.Fill_Profile_Label7.TabIndex = 6;
      this.Fill_Profile_Label7.Text = "7";
      this.Fill_Profile_Label7.TextAlign = ContentAlignment.MiddleCenter;
      this.Fill_Profile_Label6.AutoSize = true;
      this.Fill_Profile_Label6.Dock = DockStyle.Fill;
      Label fillProfileLabel6_1 = this.Fill_Profile_Label6;
      point1 = new Point(6, 99);
      Point point44 = point1;
      fillProfileLabel6_1.Location = point44;
      this.Fill_Profile_Label6.Name = "Fill_Profile_Label6";
      Label fillProfileLabel6_2 = this.Fill_Profile_Label6;
      size1 = new Size(26, 13);
      Size size44 = size1;
      fillProfileLabel6_2.Size = size44;
      this.Fill_Profile_Label6.TabIndex = 5;
      this.Fill_Profile_Label6.Text = "6";
      this.Fill_Profile_Label6.TextAlign = ContentAlignment.MiddleCenter;
      this.Fill_Profile_Label5.AutoSize = true;
      this.Fill_Profile_Label5.Dock = DockStyle.Fill;
      Label fillProfileLabel5_1 = this.Fill_Profile_Label5;
      point1 = new Point(6, 83);
      Point point45 = point1;
      fillProfileLabel5_1.Location = point45;
      this.Fill_Profile_Label5.Name = "Fill_Profile_Label5";
      Label fillProfileLabel5_2 = this.Fill_Profile_Label5;
      size1 = new Size(26, 13);
      Size size45 = size1;
      fillProfileLabel5_2.Size = size45;
      this.Fill_Profile_Label5.TabIndex = 4;
      this.Fill_Profile_Label5.Text = "5";
      this.Fill_Profile_Label5.TextAlign = ContentAlignment.MiddleCenter;
      this.Fill_Profile_Label4.AutoSize = true;
      this.Fill_Profile_Label4.Dock = DockStyle.Fill;
      Label fillProfileLabel4_1 = this.Fill_Profile_Label4;
      point1 = new Point(6, 67);
      Point point46 = point1;
      fillProfileLabel4_1.Location = point46;
      this.Fill_Profile_Label4.Name = "Fill_Profile_Label4";
      Label fillProfileLabel4_2 = this.Fill_Profile_Label4;
      size1 = new Size(26, 13);
      Size size46 = size1;
      fillProfileLabel4_2.Size = size46;
      this.Fill_Profile_Label4.TabIndex = 3;
      this.Fill_Profile_Label4.Text = "4";
      this.Fill_Profile_Label4.TextAlign = ContentAlignment.MiddleCenter;
      this.Fill_Profile_Label3.AutoSize = true;
      this.Fill_Profile_Label3.Dock = DockStyle.Fill;
      Label fillProfileLabel3_1 = this.Fill_Profile_Label3;
      point1 = new Point(6, 51);
      Point point47 = point1;
      fillProfileLabel3_1.Location = point47;
      this.Fill_Profile_Label3.Name = "Fill_Profile_Label3";
      Label fillProfileLabel3_2 = this.Fill_Profile_Label3;
      size1 = new Size(26, 13);
      Size size47 = size1;
      fillProfileLabel3_2.Size = size47;
      this.Fill_Profile_Label3.TabIndex = 2;
      this.Fill_Profile_Label3.Text = "3";
      this.Fill_Profile_Label3.TextAlign = ContentAlignment.MiddleCenter;
      this.Fill_Profile_Label2.AutoSize = true;
      this.Fill_Profile_Label2.Dock = DockStyle.Fill;
      Label fillProfileLabel2_1 = this.Fill_Profile_Label2;
      point1 = new Point(6, 35);
      Point point48 = point1;
      fillProfileLabel2_1.Location = point48;
      this.Fill_Profile_Label2.Name = "Fill_Profile_Label2";
      Label fillProfileLabel2_2 = this.Fill_Profile_Label2;
      size1 = new Size(26, 13);
      Size size48 = size1;
      fillProfileLabel2_2.Size = size48;
      this.Fill_Profile_Label2.TabIndex = 1;
      this.Fill_Profile_Label2.Text = "2";
      this.Fill_Profile_Label2.TextAlign = ContentAlignment.MiddleCenter;
      this.Fill_Profile_Label1.AutoSize = true;
      this.Fill_Profile_Label1.Dock = DockStyle.Fill;
      Label fillProfileLabel1_1 = this.Fill_Profile_Label1;
      point1 = new Point(6, 19);
      Point point49 = point1;
      fillProfileLabel1_1.Location = point49;
      this.Fill_Profile_Label1.Name = "Fill_Profile_Label1";
      Label fillProfileLabel1_2 = this.Fill_Profile_Label1;
      size1 = new Size(26, 13);
      Size size49 = size1;
      fillProfileLabel1_2.Size = size49;
      this.Fill_Profile_Label1.TabIndex = 0;
      this.Fill_Profile_Label1.Text = "1";
      this.Fill_Profile_Label1.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Fill_Profile_1_1_Label.AutoSize = true;
      this.PSSV_Fill_Profile_1_1_Label.Dock = DockStyle.Fill;
      Label fillProfile11Label1 = this.PSSV_Fill_Profile_1_1_Label;
      point1 = new Point(41, 19);
      Point point50 = point1;
      fillProfile11Label1.Location = point50;
      this.PSSV_Fill_Profile_1_1_Label.Name = "PSSV_Fill_Profile_1_1_Label";
      Label fillProfile11Label2 = this.PSSV_Fill_Profile_1_1_Label;
      size1 = new Size(59, 13);
      Size size50 = size1;
      fillProfile11Label2.Size = size50;
      this.PSSV_Fill_Profile_1_1_Label.TabIndex = 12;
      this.PSSV_Fill_Profile_1_1_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Fill_Profile_2_1_Label.AutoSize = true;
      this.PSSV_Fill_Profile_2_1_Label.Dock = DockStyle.Fill;
      Label fillProfile21Label1 = this.PSSV_Fill_Profile_2_1_Label;
      point1 = new Point(41, 35);
      Point point51 = point1;
      fillProfile21Label1.Location = point51;
      this.PSSV_Fill_Profile_2_1_Label.Name = "PSSV_Fill_Profile_2_1_Label";
      Label fillProfile21Label2 = this.PSSV_Fill_Profile_2_1_Label;
      size1 = new Size(59, 13);
      Size size51 = size1;
      fillProfile21Label2.Size = size51;
      this.PSSV_Fill_Profile_2_1_Label.TabIndex = 13;
      this.PSSV_Fill_Profile_2_1_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Fill_Profile_3_1_Label.AutoSize = true;
      this.PSSV_Fill_Profile_3_1_Label.Dock = DockStyle.Fill;
      Label fillProfile31Label1 = this.PSSV_Fill_Profile_3_1_Label;
      point1 = new Point(41, 51);
      Point point52 = point1;
      fillProfile31Label1.Location = point52;
      this.PSSV_Fill_Profile_3_1_Label.Name = "PSSV_Fill_Profile_3_1_Label";
      Label fillProfile31Label2 = this.PSSV_Fill_Profile_3_1_Label;
      size1 = new Size(59, 13);
      Size size52 = size1;
      fillProfile31Label2.Size = size52;
      this.PSSV_Fill_Profile_3_1_Label.TabIndex = 14;
      this.PSSV_Fill_Profile_3_1_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Fill_Profile_4_1_Label.AutoSize = true;
      this.PSSV_Fill_Profile_4_1_Label.Dock = DockStyle.Fill;
      Label fillProfile41Label1 = this.PSSV_Fill_Profile_4_1_Label;
      point1 = new Point(41, 67);
      Point point53 = point1;
      fillProfile41Label1.Location = point53;
      this.PSSV_Fill_Profile_4_1_Label.Name = "PSSV_Fill_Profile_4_1_Label";
      Label fillProfile41Label2 = this.PSSV_Fill_Profile_4_1_Label;
      size1 = new Size(59, 13);
      Size size53 = size1;
      fillProfile41Label2.Size = size53;
      this.PSSV_Fill_Profile_4_1_Label.TabIndex = 15;
      this.PSSV_Fill_Profile_4_1_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Fill_Profile_5_1_Label.AutoSize = true;
      this.PSSV_Fill_Profile_5_1_Label.Dock = DockStyle.Fill;
      Label fillProfile51Label1 = this.PSSV_Fill_Profile_5_1_Label;
      point1 = new Point(41, 83);
      Point point54 = point1;
      fillProfile51Label1.Location = point54;
      this.PSSV_Fill_Profile_5_1_Label.Name = "PSSV_Fill_Profile_5_1_Label";
      Label fillProfile51Label2 = this.PSSV_Fill_Profile_5_1_Label;
      size1 = new Size(59, 13);
      Size size54 = size1;
      fillProfile51Label2.Size = size54;
      this.PSSV_Fill_Profile_5_1_Label.TabIndex = 16;
      this.PSSV_Fill_Profile_5_1_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Fill_Profile_6_1_Label.AutoSize = true;
      this.PSSV_Fill_Profile_6_1_Label.Dock = DockStyle.Fill;
      Label fillProfile61Label1 = this.PSSV_Fill_Profile_6_1_Label;
      point1 = new Point(41, 99);
      Point point55 = point1;
      fillProfile61Label1.Location = point55;
      this.PSSV_Fill_Profile_6_1_Label.Name = "PSSV_Fill_Profile_6_1_Label";
      Label fillProfile61Label2 = this.PSSV_Fill_Profile_6_1_Label;
      size1 = new Size(59, 13);
      Size size55 = size1;
      fillProfile61Label2.Size = size55;
      this.PSSV_Fill_Profile_6_1_Label.TabIndex = 17;
      this.PSSV_Fill_Profile_6_1_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Fill_Profile_7_1_Label.AutoSize = true;
      this.PSSV_Fill_Profile_7_1_Label.Dock = DockStyle.Fill;
      Label fillProfile71Label1 = this.PSSV_Fill_Profile_7_1_Label;
      point1 = new Point(41, 115);
      Point point56 = point1;
      fillProfile71Label1.Location = point56;
      this.PSSV_Fill_Profile_7_1_Label.Name = "PSSV_Fill_Profile_7_1_Label";
      Label fillProfile71Label2 = this.PSSV_Fill_Profile_7_1_Label;
      size1 = new Size(59, 13);
      Size size56 = size1;
      fillProfile71Label2.Size = size56;
      this.PSSV_Fill_Profile_7_1_Label.TabIndex = 18;
      this.PSSV_Fill_Profile_7_1_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Fill_Profile_8_1_Label.AutoSize = true;
      this.PSSV_Fill_Profile_8_1_Label.Dock = DockStyle.Fill;
      Label fillProfile81Label1 = this.PSSV_Fill_Profile_8_1_Label;
      point1 = new Point(41, 131);
      Point point57 = point1;
      fillProfile81Label1.Location = point57;
      this.PSSV_Fill_Profile_8_1_Label.Name = "PSSV_Fill_Profile_8_1_Label";
      Label fillProfile81Label2 = this.PSSV_Fill_Profile_8_1_Label;
      size1 = new Size(59, 13);
      Size size57 = size1;
      fillProfile81Label2.Size = size57;
      this.PSSV_Fill_Profile_8_1_Label.TabIndex = 19;
      this.PSSV_Fill_Profile_8_1_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Fill_Profile_9_1_Label.AutoSize = true;
      this.PSSV_Fill_Profile_9_1_Label.Dock = DockStyle.Fill;
      Label fillProfile91Label1 = this.PSSV_Fill_Profile_9_1_Label;
      point1 = new Point(41, 147);
      Point point58 = point1;
      fillProfile91Label1.Location = point58;
      this.PSSV_Fill_Profile_9_1_Label.Name = "PSSV_Fill_Profile_9_1_Label";
      Label fillProfile91Label2 = this.PSSV_Fill_Profile_9_1_Label;
      size1 = new Size(59, 13);
      Size size58 = size1;
      fillProfile91Label2.Size = size58;
      this.PSSV_Fill_Profile_9_1_Label.TabIndex = 20;
      this.PSSV_Fill_Profile_9_1_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Fill_Profile_10_1_Label.AutoSize = true;
      this.PSSV_Fill_Profile_10_1_Label.Dock = DockStyle.Fill;
      Label fillProfile101Label1 = this.PSSV_Fill_Profile_10_1_Label;
      point1 = new Point(41, 163);
      Point point59 = point1;
      fillProfile101Label1.Location = point59;
      this.PSSV_Fill_Profile_10_1_Label.Name = "PSSV_Fill_Profile_10_1_Label";
      Label fillProfile101Label2 = this.PSSV_Fill_Profile_10_1_Label;
      size1 = new Size(59, 13);
      Size size59 = size1;
      fillProfile101Label2.Size = size59;
      this.PSSV_Fill_Profile_10_1_Label.TabIndex = 21;
      this.PSSV_Fill_Profile_10_1_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Fill_Profile_1_2_Label.AutoSize = true;
      this.PSSV_Fill_Profile_1_2_Label.Dock = DockStyle.Fill;
      Label fillProfile12Label1 = this.PSSV_Fill_Profile_1_2_Label;
      point1 = new Point(109, 19);
      Point point60 = point1;
      fillProfile12Label1.Location = point60;
      this.PSSV_Fill_Profile_1_2_Label.Name = "PSSV_Fill_Profile_1_2_Label";
      Label fillProfile12Label2 = this.PSSV_Fill_Profile_1_2_Label;
      size1 = new Size(61, 13);
      Size size60 = size1;
      fillProfile12Label2.Size = size60;
      this.PSSV_Fill_Profile_1_2_Label.TabIndex = 22;
      this.PSSV_Fill_Profile_1_2_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Fill_Profile_2_2_Label.AutoSize = true;
      this.PSSV_Fill_Profile_2_2_Label.Dock = DockStyle.Fill;
      Label fillProfile22Label1 = this.PSSV_Fill_Profile_2_2_Label;
      point1 = new Point(109, 35);
      Point point61 = point1;
      fillProfile22Label1.Location = point61;
      this.PSSV_Fill_Profile_2_2_Label.Name = "PSSV_Fill_Profile_2_2_Label";
      Label fillProfile22Label2 = this.PSSV_Fill_Profile_2_2_Label;
      size1 = new Size(61, 13);
      Size size61 = size1;
      fillProfile22Label2.Size = size61;
      this.PSSV_Fill_Profile_2_2_Label.TabIndex = 23;
      this.PSSV_Fill_Profile_2_2_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Fill_Profile_3_2_Label.AutoSize = true;
      this.PSSV_Fill_Profile_3_2_Label.Dock = DockStyle.Fill;
      Label fillProfile32Label1 = this.PSSV_Fill_Profile_3_2_Label;
      point1 = new Point(109, 51);
      Point point62 = point1;
      fillProfile32Label1.Location = point62;
      this.PSSV_Fill_Profile_3_2_Label.Name = "PSSV_Fill_Profile_3_2_Label";
      Label fillProfile32Label2 = this.PSSV_Fill_Profile_3_2_Label;
      size1 = new Size(61, 13);
      Size size62 = size1;
      fillProfile32Label2.Size = size62;
      this.PSSV_Fill_Profile_3_2_Label.TabIndex = 24;
      this.PSSV_Fill_Profile_3_2_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Fill_Profile_4_2_Label.AutoSize = true;
      this.PSSV_Fill_Profile_4_2_Label.Dock = DockStyle.Fill;
      Label fillProfile42Label1 = this.PSSV_Fill_Profile_4_2_Label;
      point1 = new Point(109, 67);
      Point point63 = point1;
      fillProfile42Label1.Location = point63;
      this.PSSV_Fill_Profile_4_2_Label.Name = "PSSV_Fill_Profile_4_2_Label";
      Label fillProfile42Label2 = this.PSSV_Fill_Profile_4_2_Label;
      size1 = new Size(61, 13);
      Size size63 = size1;
      fillProfile42Label2.Size = size63;
      this.PSSV_Fill_Profile_4_2_Label.TabIndex = 25;
      this.PSSV_Fill_Profile_4_2_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Fill_Profile_5_2_Label.AutoSize = true;
      this.PSSV_Fill_Profile_5_2_Label.Dock = DockStyle.Fill;
      Label fillProfile52Label1 = this.PSSV_Fill_Profile_5_2_Label;
      point1 = new Point(109, 83);
      Point point64 = point1;
      fillProfile52Label1.Location = point64;
      this.PSSV_Fill_Profile_5_2_Label.Name = "PSSV_Fill_Profile_5_2_Label";
      Label fillProfile52Label2 = this.PSSV_Fill_Profile_5_2_Label;
      size1 = new Size(61, 13);
      Size size64 = size1;
      fillProfile52Label2.Size = size64;
      this.PSSV_Fill_Profile_5_2_Label.TabIndex = 26;
      this.PSSV_Fill_Profile_5_2_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Fill_Profile_6_2_Label.AutoSize = true;
      this.PSSV_Fill_Profile_6_2_Label.Dock = DockStyle.Fill;
      Label fillProfile62Label1 = this.PSSV_Fill_Profile_6_2_Label;
      point1 = new Point(109, 99);
      Point point65 = point1;
      fillProfile62Label1.Location = point65;
      this.PSSV_Fill_Profile_6_2_Label.Name = "PSSV_Fill_Profile_6_2_Label";
      Label fillProfile62Label2 = this.PSSV_Fill_Profile_6_2_Label;
      size1 = new Size(61, 13);
      Size size65 = size1;
      fillProfile62Label2.Size = size65;
      this.PSSV_Fill_Profile_6_2_Label.TabIndex = 27;
      this.PSSV_Fill_Profile_6_2_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Fill_Profile_7_2_Label.AutoSize = true;
      this.PSSV_Fill_Profile_7_2_Label.Dock = DockStyle.Fill;
      Label fillProfile72Label1 = this.PSSV_Fill_Profile_7_2_Label;
      point1 = new Point(109, 115);
      Point point66 = point1;
      fillProfile72Label1.Location = point66;
      this.PSSV_Fill_Profile_7_2_Label.Name = "PSSV_Fill_Profile_7_2_Label";
      Label fillProfile72Label2 = this.PSSV_Fill_Profile_7_2_Label;
      size1 = new Size(61, 13);
      Size size66 = size1;
      fillProfile72Label2.Size = size66;
      this.PSSV_Fill_Profile_7_2_Label.TabIndex = 28;
      this.PSSV_Fill_Profile_7_2_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Fill_Profile_8_2_Label.AutoSize = true;
      this.PSSV_Fill_Profile_8_2_Label.Dock = DockStyle.Fill;
      Label fillProfile82Label1 = this.PSSV_Fill_Profile_8_2_Label;
      point1 = new Point(109, 131);
      Point point67 = point1;
      fillProfile82Label1.Location = point67;
      this.PSSV_Fill_Profile_8_2_Label.Name = "PSSV_Fill_Profile_8_2_Label";
      Label fillProfile82Label2 = this.PSSV_Fill_Profile_8_2_Label;
      size1 = new Size(61, 13);
      Size size67 = size1;
      fillProfile82Label2.Size = size67;
      this.PSSV_Fill_Profile_8_2_Label.TabIndex = 29;
      this.PSSV_Fill_Profile_8_2_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Fill_Profile_9_2_Label.AutoSize = true;
      this.PSSV_Fill_Profile_9_2_Label.Dock = DockStyle.Fill;
      Label fillProfile92Label1 = this.PSSV_Fill_Profile_9_2_Label;
      point1 = new Point(109, 147);
      Point point68 = point1;
      fillProfile92Label1.Location = point68;
      this.PSSV_Fill_Profile_9_2_Label.Name = "PSSV_Fill_Profile_9_2_Label";
      Label fillProfile92Label2 = this.PSSV_Fill_Profile_9_2_Label;
      size1 = new Size(61, 13);
      Size size68 = size1;
      fillProfile92Label2.Size = size68;
      this.PSSV_Fill_Profile_9_2_Label.TabIndex = 30;
      this.PSSV_Fill_Profile_9_2_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Fill_Profile_10_2_Label.AutoSize = true;
      this.PSSV_Fill_Profile_10_2_Label.Dock = DockStyle.Fill;
      Label fillProfile102Label1 = this.PSSV_Fill_Profile_10_2_Label;
      point1 = new Point(109, 163);
      Point point69 = point1;
      fillProfile102Label1.Location = point69;
      this.PSSV_Fill_Profile_10_2_Label.Name = "PSSV_Fill_Profile_10_2_Label";
      Label fillProfile102Label2 = this.PSSV_Fill_Profile_10_2_Label;
      size1 = new Size(61, 13);
      Size size69 = size1;
      fillProfile102Label2.Size = size69;
      this.PSSV_Fill_Profile_10_2_Label.TabIndex = 31;
      this.PSSV_Fill_Profile_10_2_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.Fill_Profile_Label13.AutoSize = true;
      this.Fill_Profile_Label13.Dock = DockStyle.Fill;
      this.Fill_Profile_Label13.Font = new Font("굴림", 7.5f);
      Label fillProfileLabel13_1 = this.Fill_Profile_Label13;
      point1 = new Point(6, 179);
      Point point70 = point1;
      fillProfileLabel13_1.Location = point70;
      this.Fill_Profile_Label13.Name = "Fill_Profile_Label13";
      Label fillProfileLabel13_2 = this.Fill_Profile_Label13;
      size1 = new Size(26, 19);
      Size size70 = size1;
      fillProfileLabel13_2.Size = size70;
      this.Fill_Profile_Label13.TabIndex = 32;
      this.Fill_Profile_Label13.Text = "기준";
      this.Fill_Profile_Label13.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Fill_Profile_11_1_Label.AutoSize = true;
      this.PSSV_Fill_Profile_11_1_Label.Dock = DockStyle.Fill;
      this.PSSV_Fill_Profile_11_1_Label.Font = new Font("굴림", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 129);
      Label fillProfile111Label1 = this.PSSV_Fill_Profile_11_1_Label;
      point1 = new Point(41, 179);
      Point point71 = point1;
      fillProfile111Label1.Location = point71;
      this.PSSV_Fill_Profile_11_1_Label.Name = "PSSV_Fill_Profile_11_1_Label";
      Label fillProfile111Label2 = this.PSSV_Fill_Profile_11_1_Label;
      size1 = new Size(59, 19);
      Size size71 = size1;
      fillProfile111Label2.Size = size71;
      this.PSSV_Fill_Profile_11_1_Label.TabIndex = 33;
      this.PSSV_Fill_Profile_11_1_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Fill_Profile_11_2_Label.AutoSize = true;
      this.PSSV_Fill_Profile_11_2_Label.Dock = DockStyle.Fill;
      this.PSSV_Fill_Profile_11_2_Label.Font = new Font("굴림", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 129);
      Label fillProfile112Label1 = this.PSSV_Fill_Profile_11_2_Label;
      point1 = new Point(109, 179);
      Point point72 = point1;
      fillProfile112Label1.Location = point72;
      this.PSSV_Fill_Profile_11_2_Label.Name = "PSSV_Fill_Profile_11_2_Label";
      Label fillProfile112Label2 = this.PSSV_Fill_Profile_11_2_Label;
      size1 = new Size(61, 19);
      Size size72 = size1;
      fillProfile112Label2.Size = size72;
      this.PSSV_Fill_Profile_11_2_Label.TabIndex = 34;
      this.PSSV_Fill_Profile_11_2_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.Pack_Profile.Controls.Add((Control) this.TableLayoutPanel1);
      GroupBox packProfile1 = this.Pack_Profile;
      point1 = new Point(405, 234);
      Point point73 = point1;
      packProfile1.Location = point73;
      this.Pack_Profile.Name = "Pack_Profile";
      GroupBox packProfile2 = this.Pack_Profile;
      size1 = new Size(182, 221);
      Size size73 = size1;
      packProfile2.Size = size73;
      this.Pack_Profile.TabIndex = 3;
      this.Pack_Profile.TabStop = false;
      this.Pack_Profile.Text = "Pack_Profile";
      this.TableLayoutPanel1.CellBorderStyle = TableLayoutPanelCellBorderStyle.InsetDouble;
      this.TableLayoutPanel1.ColumnCount = 3;
      this.TableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20f));
      this.TableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 40f));
      this.TableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 40f));
      this.TableLayoutPanel1.Controls.Add((Control) this.Pack_Profile_Label12, 2, 0);
      this.TableLayoutPanel1.Controls.Add((Control) this.Pack_Profile_Label11, 1, 0);
      this.TableLayoutPanel1.Controls.Add((Control) this.Pack_Profile_Label10, 0, 10);
      this.TableLayoutPanel1.Controls.Add((Control) this.Pack_Profile_Label9, 0, 9);
      this.TableLayoutPanel1.Controls.Add((Control) this.Pack_Profile_Label8, 0, 8);
      this.TableLayoutPanel1.Controls.Add((Control) this.Pack_Profile_Label7, 0, 7);
      this.TableLayoutPanel1.Controls.Add((Control) this.Pack_Profile_Label6, 0, 6);
      this.TableLayoutPanel1.Controls.Add((Control) this.Pack_Profile_Label5, 0, 5);
      this.TableLayoutPanel1.Controls.Add((Control) this.Pack_Profile_Label4, 0, 4);
      this.TableLayoutPanel1.Controls.Add((Control) this.Pack_Profile_Label3, 0, 3);
      this.TableLayoutPanel1.Controls.Add((Control) this.Pack_Profile_Label2, 0, 2);
      this.TableLayoutPanel1.Controls.Add((Control) this.Pack_Profile_Label1, 0, 1);
      this.TableLayoutPanel1.Controls.Add((Control) this.PSSV_Packing_Profile_1_1_Label, 1, 1);
      this.TableLayoutPanel1.Controls.Add((Control) this.PSSV_Packing_Profile_2_1_Label, 1, 2);
      this.TableLayoutPanel1.Controls.Add((Control) this.PSSV_Packing_Profile_3_1_Label, 1, 3);
      this.TableLayoutPanel1.Controls.Add((Control) this.PSSV_Packing_Profile_4_1_Label, 1, 4);
      this.TableLayoutPanel1.Controls.Add((Control) this.PSSV_Packing_Profile_5_1_Label, 1, 5);
      this.TableLayoutPanel1.Controls.Add((Control) this.PSSV_Packing_Profile_6_1_Label, 1, 6);
      this.TableLayoutPanel1.Controls.Add((Control) this.PSSV_Packing_Profile_7_1_Label, 1, 7);
      this.TableLayoutPanel1.Controls.Add((Control) this.PSSV_Packing_Profile_8_1_Label, 1, 8);
      this.TableLayoutPanel1.Controls.Add((Control) this.PSSV_Packing_Profile_9_1_Label, 1, 9);
      this.TableLayoutPanel1.Controls.Add((Control) this.PSSV_Packing_Profile_10_1_Label, 1, 10);
      this.TableLayoutPanel1.Controls.Add((Control) this.PSSV_Packing_Profile_1_2_Label, 2, 1);
      this.TableLayoutPanel1.Controls.Add((Control) this.PSSV_Packing_Profile_2_2_Label, 2, 2);
      this.TableLayoutPanel1.Controls.Add((Control) this.PSSV_Packing_Profile_3_2_Label, 2, 3);
      this.TableLayoutPanel1.Controls.Add((Control) this.PSSV_Packing_Profile_4_2_Label, 2, 4);
      this.TableLayoutPanel1.Controls.Add((Control) this.PSSV_Packing_Profile_5_2_Label, 2, 5);
      this.TableLayoutPanel1.Controls.Add((Control) this.PSSV_Packing_Profile_6_2_Label, 2, 6);
      this.TableLayoutPanel1.Controls.Add((Control) this.PSSV_Packing_Profile_7_2_Label, 2, 7);
      this.TableLayoutPanel1.Controls.Add((Control) this.PSSV_Packing_Profile_8_2_Label, 2, 8);
      this.TableLayoutPanel1.Controls.Add((Control) this.PSSV_Packing_Profile_9_2_Label, 2, 9);
      this.TableLayoutPanel1.Controls.Add((Control) this.PSSV_Packing_Profile_10_2_Label, 2, 10);
      this.TableLayoutPanel1.Dock = DockStyle.Fill;
      TableLayoutPanel tableLayoutPanel1_1 = this.TableLayoutPanel1;
      point1 = new Point(3, 17);
      Point point74 = point1;
      tableLayoutPanel1_1.Location = point74;
      this.TableLayoutPanel1.Name = "TableLayoutPanel1";
      this.TableLayoutPanel1.RowCount = 11;
      this.TableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 9.090908f));
      this.TableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 9.090908f));
      this.TableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 9.090908f));
      this.TableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 9.090908f));
      this.TableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 9.090908f));
      this.TableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 9.090908f));
      this.TableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 9.090908f));
      this.TableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 9.090908f));
      this.TableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 9.090908f));
      this.TableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 9.090908f));
      this.TableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 9.090908f));
      TableLayoutPanel tableLayoutPanel1_2 = this.TableLayoutPanel1;
      size1 = new Size(176, 201);
      Size size74 = size1;
      tableLayoutPanel1_2.Size = size74;
      this.TableLayoutPanel1.TabIndex = 0;
      this.Pack_Profile_Label12.AutoSize = true;
      this.Pack_Profile_Label12.Dock = DockStyle.Fill;
      Label packProfileLabel12_1 = this.Pack_Profile_Label12;
      point1 = new Point(109, 3);
      Point point75 = point1;
      packProfileLabel12_1.Location = point75;
      this.Pack_Profile_Label12.Name = "Pack_Profile_Label12";
      Label packProfileLabel12_2 = this.Pack_Profile_Label12;
      size1 = new Size(61, 14);
      Size size75 = size1;
      packProfileLabel12_2.Size = size75;
      this.Pack_Profile_Label12.TabIndex = 11;
      this.Pack_Profile_Label12.Text = "압력";
      this.Pack_Profile_Label12.TextAlign = ContentAlignment.MiddleCenter;
      this.Pack_Profile_Label11.AutoSize = true;
      this.Pack_Profile_Label11.Dock = DockStyle.Fill;
      Label packProfileLabel11_1 = this.Pack_Profile_Label11;
      point1 = new Point(41, 3);
      Point point76 = point1;
      packProfileLabel11_1.Location = point76;
      this.Pack_Profile_Label11.Name = "Pack_Profile_Label11";
      Label packProfileLabel11_2 = this.Pack_Profile_Label11;
      size1 = new Size(59, 14);
      Size size76 = size1;
      packProfileLabel11_2.Size = size76;
      this.Pack_Profile_Label11.TabIndex = 10;
      this.Pack_Profile_Label11.Text = "시간";
      this.Pack_Profile_Label11.TextAlign = ContentAlignment.MiddleCenter;
      this.Pack_Profile_Label10.AutoSize = true;
      this.Pack_Profile_Label10.Dock = DockStyle.Fill;
      this.Pack_Profile_Label10.Font = new Font("굴림", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 129);
      Label packProfileLabel10_1 = this.Pack_Profile_Label10;
      point1 = new Point(6, 173);
      Point point77 = point1;
      packProfileLabel10_1.Location = point77;
      this.Pack_Profile_Label10.Name = "Pack_Profile_Label10";
      Label packProfileLabel10_2 = this.Pack_Profile_Label10;
      size1 = new Size(26, 25);
      Size size77 = size1;
      packProfileLabel10_2.Size = size77;
      this.Pack_Profile_Label10.TabIndex = 9;
      this.Pack_Profile_Label10.Text = "10";
      this.Pack_Profile_Label10.TextAlign = ContentAlignment.MiddleCenter;
      this.Pack_Profile_Label9.AutoSize = true;
      this.Pack_Profile_Label9.Dock = DockStyle.Fill;
      Label packProfileLabel9_1 = this.Pack_Profile_Label9;
      point1 = new Point(6, 156);
      Point point78 = point1;
      packProfileLabel9_1.Location = point78;
      this.Pack_Profile_Label9.Name = "Pack_Profile_Label9";
      Label packProfileLabel9_2 = this.Pack_Profile_Label9;
      size1 = new Size(26, 14);
      Size size78 = size1;
      packProfileLabel9_2.Size = size78;
      this.Pack_Profile_Label9.TabIndex = 8;
      this.Pack_Profile_Label9.Text = "9";
      this.Pack_Profile_Label9.TextAlign = ContentAlignment.MiddleCenter;
      this.Pack_Profile_Label8.AutoSize = true;
      this.Pack_Profile_Label8.Dock = DockStyle.Fill;
      Label packProfileLabel8_1 = this.Pack_Profile_Label8;
      point1 = new Point(6, 139);
      Point point79 = point1;
      packProfileLabel8_1.Location = point79;
      this.Pack_Profile_Label8.Name = "Pack_Profile_Label8";
      Label packProfileLabel8_2 = this.Pack_Profile_Label8;
      size1 = new Size(26, 14);
      Size size79 = size1;
      packProfileLabel8_2.Size = size79;
      this.Pack_Profile_Label8.TabIndex = 7;
      this.Pack_Profile_Label8.Text = "8";
      this.Pack_Profile_Label8.TextAlign = ContentAlignment.MiddleCenter;
      this.Pack_Profile_Label7.AutoSize = true;
      this.Pack_Profile_Label7.Dock = DockStyle.Fill;
      Label packProfileLabel7_1 = this.Pack_Profile_Label7;
      point1 = new Point(6, 122);
      Point point80 = point1;
      packProfileLabel7_1.Location = point80;
      this.Pack_Profile_Label7.Name = "Pack_Profile_Label7";
      Label packProfileLabel7_2 = this.Pack_Profile_Label7;
      size1 = new Size(26, 14);
      Size size80 = size1;
      packProfileLabel7_2.Size = size80;
      this.Pack_Profile_Label7.TabIndex = 6;
      this.Pack_Profile_Label7.Text = "7";
      this.Pack_Profile_Label7.TextAlign = ContentAlignment.MiddleCenter;
      this.Pack_Profile_Label6.AutoSize = true;
      this.Pack_Profile_Label6.Dock = DockStyle.Fill;
      Label packProfileLabel6_1 = this.Pack_Profile_Label6;
      point1 = new Point(6, 105);
      Point point81 = point1;
      packProfileLabel6_1.Location = point81;
      this.Pack_Profile_Label6.Name = "Pack_Profile_Label6";
      Label packProfileLabel6_2 = this.Pack_Profile_Label6;
      size1 = new Size(26, 14);
      Size size81 = size1;
      packProfileLabel6_2.Size = size81;
      this.Pack_Profile_Label6.TabIndex = 5;
      this.Pack_Profile_Label6.Text = "6";
      this.Pack_Profile_Label6.TextAlign = ContentAlignment.MiddleCenter;
      this.Pack_Profile_Label5.AutoSize = true;
      this.Pack_Profile_Label5.Dock = DockStyle.Fill;
      Label packProfileLabel5_1 = this.Pack_Profile_Label5;
      point1 = new Point(6, 88);
      Point point82 = point1;
      packProfileLabel5_1.Location = point82;
      this.Pack_Profile_Label5.Name = "Pack_Profile_Label5";
      Label packProfileLabel5_2 = this.Pack_Profile_Label5;
      size1 = new Size(26, 14);
      Size size82 = size1;
      packProfileLabel5_2.Size = size82;
      this.Pack_Profile_Label5.TabIndex = 4;
      this.Pack_Profile_Label5.Text = "5";
      this.Pack_Profile_Label5.TextAlign = ContentAlignment.MiddleCenter;
      this.Pack_Profile_Label4.AutoSize = true;
      this.Pack_Profile_Label4.Dock = DockStyle.Fill;
      Label packProfileLabel4_1 = this.Pack_Profile_Label4;
      point1 = new Point(6, 71);
      Point point83 = point1;
      packProfileLabel4_1.Location = point83;
      this.Pack_Profile_Label4.Name = "Pack_Profile_Label4";
      Label packProfileLabel4_2 = this.Pack_Profile_Label4;
      size1 = new Size(26, 14);
      Size size83 = size1;
      packProfileLabel4_2.Size = size83;
      this.Pack_Profile_Label4.TabIndex = 3;
      this.Pack_Profile_Label4.Text = "4";
      this.Pack_Profile_Label4.TextAlign = ContentAlignment.MiddleCenter;
      this.Pack_Profile_Label3.AutoSize = true;
      this.Pack_Profile_Label3.Dock = DockStyle.Fill;
      Label packProfileLabel3_1 = this.Pack_Profile_Label3;
      point1 = new Point(6, 54);
      Point point84 = point1;
      packProfileLabel3_1.Location = point84;
      this.Pack_Profile_Label3.Name = "Pack_Profile_Label3";
      Label packProfileLabel3_2 = this.Pack_Profile_Label3;
      size1 = new Size(26, 14);
      Size size84 = size1;
      packProfileLabel3_2.Size = size84;
      this.Pack_Profile_Label3.TabIndex = 2;
      this.Pack_Profile_Label3.Text = "3";
      this.Pack_Profile_Label3.TextAlign = ContentAlignment.MiddleCenter;
      this.Pack_Profile_Label2.AutoSize = true;
      this.Pack_Profile_Label2.Dock = DockStyle.Fill;
      Label packProfileLabel2_1 = this.Pack_Profile_Label2;
      point1 = new Point(6, 37);
      Point point85 = point1;
      packProfileLabel2_1.Location = point85;
      this.Pack_Profile_Label2.Name = "Pack_Profile_Label2";
      Label packProfileLabel2_2 = this.Pack_Profile_Label2;
      size1 = new Size(26, 14);
      Size size85 = size1;
      packProfileLabel2_2.Size = size85;
      this.Pack_Profile_Label2.TabIndex = 1;
      this.Pack_Profile_Label2.Text = "2";
      this.Pack_Profile_Label2.TextAlign = ContentAlignment.MiddleCenter;
      this.Pack_Profile_Label1.AutoSize = true;
      this.Pack_Profile_Label1.Dock = DockStyle.Fill;
      Label packProfileLabel1_1 = this.Pack_Profile_Label1;
      point1 = new Point(6, 20);
      Point point86 = point1;
      packProfileLabel1_1.Location = point86;
      this.Pack_Profile_Label1.Name = "Pack_Profile_Label1";
      Label packProfileLabel1_2 = this.Pack_Profile_Label1;
      size1 = new Size(26, 14);
      Size size86 = size1;
      packProfileLabel1_2.Size = size86;
      this.Pack_Profile_Label1.TabIndex = 0;
      this.Pack_Profile_Label1.Text = "1";
      this.Pack_Profile_Label1.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Packing_Profile_1_1_Label.AutoSize = true;
      this.PSSV_Packing_Profile_1_1_Label.Dock = DockStyle.Fill;
      Label packingProfile11Label1 = this.PSSV_Packing_Profile_1_1_Label;
      point1 = new Point(41, 20);
      Point point87 = point1;
      packingProfile11Label1.Location = point87;
      this.PSSV_Packing_Profile_1_1_Label.Name = "PSSV_Packing_Profile_1_1_Label";
      Label packingProfile11Label2 = this.PSSV_Packing_Profile_1_1_Label;
      size1 = new Size(59, 14);
      Size size87 = size1;
      packingProfile11Label2.Size = size87;
      this.PSSV_Packing_Profile_1_1_Label.TabIndex = 12;
      this.PSSV_Packing_Profile_1_1_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Packing_Profile_2_1_Label.AutoSize = true;
      this.PSSV_Packing_Profile_2_1_Label.Dock = DockStyle.Fill;
      Label packingProfile21Label1 = this.PSSV_Packing_Profile_2_1_Label;
      point1 = new Point(41, 37);
      Point point88 = point1;
      packingProfile21Label1.Location = point88;
      this.PSSV_Packing_Profile_2_1_Label.Name = "PSSV_Packing_Profile_2_1_Label";
      Label packingProfile21Label2 = this.PSSV_Packing_Profile_2_1_Label;
      size1 = new Size(59, 14);
      Size size88 = size1;
      packingProfile21Label2.Size = size88;
      this.PSSV_Packing_Profile_2_1_Label.TabIndex = 13;
      this.PSSV_Packing_Profile_2_1_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Packing_Profile_3_1_Label.AutoSize = true;
      this.PSSV_Packing_Profile_3_1_Label.Dock = DockStyle.Fill;
      Label packingProfile31Label1 = this.PSSV_Packing_Profile_3_1_Label;
      point1 = new Point(41, 54);
      Point point89 = point1;
      packingProfile31Label1.Location = point89;
      this.PSSV_Packing_Profile_3_1_Label.Name = "PSSV_Packing_Profile_3_1_Label";
      Label packingProfile31Label2 = this.PSSV_Packing_Profile_3_1_Label;
      size1 = new Size(59, 14);
      Size size89 = size1;
      packingProfile31Label2.Size = size89;
      this.PSSV_Packing_Profile_3_1_Label.TabIndex = 14;
      this.PSSV_Packing_Profile_3_1_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Packing_Profile_4_1_Label.AutoSize = true;
      this.PSSV_Packing_Profile_4_1_Label.Dock = DockStyle.Fill;
      Label packingProfile41Label1 = this.PSSV_Packing_Profile_4_1_Label;
      point1 = new Point(41, 71);
      Point point90 = point1;
      packingProfile41Label1.Location = point90;
      this.PSSV_Packing_Profile_4_1_Label.Name = "PSSV_Packing_Profile_4_1_Label";
      Label packingProfile41Label2 = this.PSSV_Packing_Profile_4_1_Label;
      size1 = new Size(59, 14);
      Size size90 = size1;
      packingProfile41Label2.Size = size90;
      this.PSSV_Packing_Profile_4_1_Label.TabIndex = 15;
      this.PSSV_Packing_Profile_4_1_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Packing_Profile_5_1_Label.AutoSize = true;
      this.PSSV_Packing_Profile_5_1_Label.Dock = DockStyle.Fill;
      Label packingProfile51Label1 = this.PSSV_Packing_Profile_5_1_Label;
      point1 = new Point(41, 88);
      Point point91 = point1;
      packingProfile51Label1.Location = point91;
      this.PSSV_Packing_Profile_5_1_Label.Name = "PSSV_Packing_Profile_5_1_Label";
      Label packingProfile51Label2 = this.PSSV_Packing_Profile_5_1_Label;
      size1 = new Size(59, 14);
      Size size91 = size1;
      packingProfile51Label2.Size = size91;
      this.PSSV_Packing_Profile_5_1_Label.TabIndex = 16;
      this.PSSV_Packing_Profile_5_1_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Packing_Profile_6_1_Label.AutoSize = true;
      this.PSSV_Packing_Profile_6_1_Label.Dock = DockStyle.Fill;
      Label packingProfile61Label1 = this.PSSV_Packing_Profile_6_1_Label;
      point1 = new Point(41, 105);
      Point point92 = point1;
      packingProfile61Label1.Location = point92;
      this.PSSV_Packing_Profile_6_1_Label.Name = "PSSV_Packing_Profile_6_1_Label";
      Label packingProfile61Label2 = this.PSSV_Packing_Profile_6_1_Label;
      size1 = new Size(59, 14);
      Size size92 = size1;
      packingProfile61Label2.Size = size92;
      this.PSSV_Packing_Profile_6_1_Label.TabIndex = 17;
      this.PSSV_Packing_Profile_6_1_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Packing_Profile_7_1_Label.AutoSize = true;
      this.PSSV_Packing_Profile_7_1_Label.Dock = DockStyle.Fill;
      Label packingProfile71Label1 = this.PSSV_Packing_Profile_7_1_Label;
      point1 = new Point(41, 122);
      Point point93 = point1;
      packingProfile71Label1.Location = point93;
      this.PSSV_Packing_Profile_7_1_Label.Name = "PSSV_Packing_Profile_7_1_Label";
      Label packingProfile71Label2 = this.PSSV_Packing_Profile_7_1_Label;
      size1 = new Size(59, 14);
      Size size93 = size1;
      packingProfile71Label2.Size = size93;
      this.PSSV_Packing_Profile_7_1_Label.TabIndex = 18;
      this.PSSV_Packing_Profile_7_1_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Packing_Profile_8_1_Label.AutoSize = true;
      this.PSSV_Packing_Profile_8_1_Label.Dock = DockStyle.Fill;
      Label packingProfile81Label1 = this.PSSV_Packing_Profile_8_1_Label;
      point1 = new Point(41, 139);
      Point point94 = point1;
      packingProfile81Label1.Location = point94;
      this.PSSV_Packing_Profile_8_1_Label.Name = "PSSV_Packing_Profile_8_1_Label";
      Label packingProfile81Label2 = this.PSSV_Packing_Profile_8_1_Label;
      size1 = new Size(59, 14);
      Size size94 = size1;
      packingProfile81Label2.Size = size94;
      this.PSSV_Packing_Profile_8_1_Label.TabIndex = 19;
      this.PSSV_Packing_Profile_8_1_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Packing_Profile_9_1_Label.AutoSize = true;
      this.PSSV_Packing_Profile_9_1_Label.Dock = DockStyle.Fill;
      Label packingProfile91Label1 = this.PSSV_Packing_Profile_9_1_Label;
      point1 = new Point(41, 156);
      Point point95 = point1;
      packingProfile91Label1.Location = point95;
      this.PSSV_Packing_Profile_9_1_Label.Name = "PSSV_Packing_Profile_9_1_Label";
      Label packingProfile91Label2 = this.PSSV_Packing_Profile_9_1_Label;
      size1 = new Size(59, 14);
      Size size95 = size1;
      packingProfile91Label2.Size = size95;
      this.PSSV_Packing_Profile_9_1_Label.TabIndex = 20;
      this.PSSV_Packing_Profile_9_1_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Packing_Profile_10_1_Label.AutoSize = true;
      this.PSSV_Packing_Profile_10_1_Label.Dock = DockStyle.Fill;
      Label packingProfile101Label1 = this.PSSV_Packing_Profile_10_1_Label;
      point1 = new Point(41, 173);
      Point point96 = point1;
      packingProfile101Label1.Location = point96;
      this.PSSV_Packing_Profile_10_1_Label.Name = "PSSV_Packing_Profile_10_1_Label";
      Label packingProfile101Label2 = this.PSSV_Packing_Profile_10_1_Label;
      size1 = new Size(59, 25);
      Size size96 = size1;
      packingProfile101Label2.Size = size96;
      this.PSSV_Packing_Profile_10_1_Label.TabIndex = 21;
      this.PSSV_Packing_Profile_10_1_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Packing_Profile_1_2_Label.AutoSize = true;
      this.PSSV_Packing_Profile_1_2_Label.Dock = DockStyle.Fill;
      Label packingProfile12Label1 = this.PSSV_Packing_Profile_1_2_Label;
      point1 = new Point(109, 20);
      Point point97 = point1;
      packingProfile12Label1.Location = point97;
      this.PSSV_Packing_Profile_1_2_Label.Name = "PSSV_Packing_Profile_1_2_Label";
      Label packingProfile12Label2 = this.PSSV_Packing_Profile_1_2_Label;
      size1 = new Size(61, 14);
      Size size97 = size1;
      packingProfile12Label2.Size = size97;
      this.PSSV_Packing_Profile_1_2_Label.TabIndex = 22;
      this.PSSV_Packing_Profile_1_2_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Packing_Profile_2_2_Label.AutoSize = true;
      this.PSSV_Packing_Profile_2_2_Label.Dock = DockStyle.Fill;
      Label packingProfile22Label1 = this.PSSV_Packing_Profile_2_2_Label;
      point1 = new Point(109, 37);
      Point point98 = point1;
      packingProfile22Label1.Location = point98;
      this.PSSV_Packing_Profile_2_2_Label.Name = "PSSV_Packing_Profile_2_2_Label";
      Label packingProfile22Label2 = this.PSSV_Packing_Profile_2_2_Label;
      size1 = new Size(61, 14);
      Size size98 = size1;
      packingProfile22Label2.Size = size98;
      this.PSSV_Packing_Profile_2_2_Label.TabIndex = 23;
      this.PSSV_Packing_Profile_2_2_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Packing_Profile_3_2_Label.AutoSize = true;
      this.PSSV_Packing_Profile_3_2_Label.Dock = DockStyle.Fill;
      Label packingProfile32Label1 = this.PSSV_Packing_Profile_3_2_Label;
      point1 = new Point(109, 54);
      Point point99 = point1;
      packingProfile32Label1.Location = point99;
      this.PSSV_Packing_Profile_3_2_Label.Name = "PSSV_Packing_Profile_3_2_Label";
      Label packingProfile32Label2 = this.PSSV_Packing_Profile_3_2_Label;
      size1 = new Size(61, 14);
      Size size99 = size1;
      packingProfile32Label2.Size = size99;
      this.PSSV_Packing_Profile_3_2_Label.TabIndex = 24;
      this.PSSV_Packing_Profile_3_2_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Packing_Profile_4_2_Label.AutoSize = true;
      this.PSSV_Packing_Profile_4_2_Label.Dock = DockStyle.Fill;
      Label packingProfile42Label1 = this.PSSV_Packing_Profile_4_2_Label;
      point1 = new Point(109, 71);
      Point point100 = point1;
      packingProfile42Label1.Location = point100;
      this.PSSV_Packing_Profile_4_2_Label.Name = "PSSV_Packing_Profile_4_2_Label";
      Label packingProfile42Label2 = this.PSSV_Packing_Profile_4_2_Label;
      size1 = new Size(61, 14);
      Size size100 = size1;
      packingProfile42Label2.Size = size100;
      this.PSSV_Packing_Profile_4_2_Label.TabIndex = 25;
      this.PSSV_Packing_Profile_4_2_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Packing_Profile_5_2_Label.AutoSize = true;
      this.PSSV_Packing_Profile_5_2_Label.Dock = DockStyle.Fill;
      Label packingProfile52Label1 = this.PSSV_Packing_Profile_5_2_Label;
      point1 = new Point(109, 88);
      Point point101 = point1;
      packingProfile52Label1.Location = point101;
      this.PSSV_Packing_Profile_5_2_Label.Name = "PSSV_Packing_Profile_5_2_Label";
      Label packingProfile52Label2 = this.PSSV_Packing_Profile_5_2_Label;
      size1 = new Size(61, 14);
      Size size101 = size1;
      packingProfile52Label2.Size = size101;
      this.PSSV_Packing_Profile_5_2_Label.TabIndex = 26;
      this.PSSV_Packing_Profile_5_2_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Packing_Profile_6_2_Label.AutoSize = true;
      this.PSSV_Packing_Profile_6_2_Label.Dock = DockStyle.Fill;
      Label packingProfile62Label1 = this.PSSV_Packing_Profile_6_2_Label;
      point1 = new Point(109, 105);
      Point point102 = point1;
      packingProfile62Label1.Location = point102;
      this.PSSV_Packing_Profile_6_2_Label.Name = "PSSV_Packing_Profile_6_2_Label";
      Label packingProfile62Label2 = this.PSSV_Packing_Profile_6_2_Label;
      size1 = new Size(61, 14);
      Size size102 = size1;
      packingProfile62Label2.Size = size102;
      this.PSSV_Packing_Profile_6_2_Label.TabIndex = 27;
      this.PSSV_Packing_Profile_6_2_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Packing_Profile_7_2_Label.AutoSize = true;
      this.PSSV_Packing_Profile_7_2_Label.Dock = DockStyle.Fill;
      Label packingProfile72Label1 = this.PSSV_Packing_Profile_7_2_Label;
      point1 = new Point(109, 122);
      Point point103 = point1;
      packingProfile72Label1.Location = point103;
      this.PSSV_Packing_Profile_7_2_Label.Name = "PSSV_Packing_Profile_7_2_Label";
      Label packingProfile72Label2 = this.PSSV_Packing_Profile_7_2_Label;
      size1 = new Size(61, 14);
      Size size103 = size1;
      packingProfile72Label2.Size = size103;
      this.PSSV_Packing_Profile_7_2_Label.TabIndex = 28;
      this.PSSV_Packing_Profile_7_2_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Packing_Profile_8_2_Label.AutoSize = true;
      this.PSSV_Packing_Profile_8_2_Label.Dock = DockStyle.Fill;
      Label packingProfile82Label1 = this.PSSV_Packing_Profile_8_2_Label;
      point1 = new Point(109, 139);
      Point point104 = point1;
      packingProfile82Label1.Location = point104;
      this.PSSV_Packing_Profile_8_2_Label.Name = "PSSV_Packing_Profile_8_2_Label";
      Label packingProfile82Label2 = this.PSSV_Packing_Profile_8_2_Label;
      size1 = new Size(61, 14);
      Size size104 = size1;
      packingProfile82Label2.Size = size104;
      this.PSSV_Packing_Profile_8_2_Label.TabIndex = 29;
      this.PSSV_Packing_Profile_8_2_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Packing_Profile_9_2_Label.AutoSize = true;
      this.PSSV_Packing_Profile_9_2_Label.Dock = DockStyle.Fill;
      Label packingProfile92Label1 = this.PSSV_Packing_Profile_9_2_Label;
      point1 = new Point(109, 156);
      Point point105 = point1;
      packingProfile92Label1.Location = point105;
      this.PSSV_Packing_Profile_9_2_Label.Name = "PSSV_Packing_Profile_9_2_Label";
      Label packingProfile92Label2 = this.PSSV_Packing_Profile_9_2_Label;
      size1 = new Size(61, 14);
      Size size105 = size1;
      packingProfile92Label2.Size = size105;
      this.PSSV_Packing_Profile_9_2_Label.TabIndex = 30;
      this.PSSV_Packing_Profile_9_2_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.PSSV_Packing_Profile_10_2_Label.AutoSize = true;
      this.PSSV_Packing_Profile_10_2_Label.Dock = DockStyle.Fill;
      Label packingProfile102Label1 = this.PSSV_Packing_Profile_10_2_Label;
      point1 = new Point(109, 173);
      Point point106 = point1;
      packingProfile102Label1.Location = point106;
      this.PSSV_Packing_Profile_10_2_Label.Name = "PSSV_Packing_Profile_10_2_Label";
      Label packingProfile102Label2 = this.PSSV_Packing_Profile_10_2_Label;
      size1 = new Size(61, 25);
      Size size106 = size1;
      packingProfile102Label2.Size = size106;
      this.PSSV_Packing_Profile_10_2_Label.TabIndex = 31;
      this.PSSV_Packing_Profile_10_2_Label.TextAlign = ContentAlignment.MiddleCenter;
      this.AutoScaleDimensions = new SizeF(7f, 12f);
      this.AutoScaleMode = AutoScaleMode.Font;
      size1 = new Size(586, 454);
      this.ClientSize = size1;
      this.Controls.Add((Control) this.Pack_Profile);
      this.Controls.Add((Control) this.Fill_Profile);
      this.Controls.Add((Control) this.Process_Setting_View);
      this.Controls.Add((Control) this.Process_Setting_View_Table);
      this.Icon = (Icon) componentResourceManager.GetObject("$this.Icon");
      this.Name = nameof (Form2);
      this.Text = "현재 스터디 공정조건";
      this.Process_Setting_View_Table.ResumeLayout(false);
      this.Process_Setting_View_Table.PerformLayout();
      this.Fill_Profile.ResumeLayout(false);
      this.Fill_Pofile_Table.ResumeLayout(false);
      this.Fill_Pofile_Table.PerformLayout();
      this.Pack_Profile.ResumeLayout(false);
      this.TableLayoutPanel1.ResumeLayout(false);
      this.TableLayoutPanel1.PerformLayout();
      this.ResumeLayout(false);
    }

    internal virtual TableLayoutPanel Process_Setting_View_Table
    {
      [DebuggerNonUserCode] get => this._Process_Setting_View_Table;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Process_Setting_View_Table = value;
      }
    }

    internal virtual Label Process_Setting_View_Label1
    {
      [DebuggerNonUserCode] get => this._Process_Setting_View_Label1;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Process_Setting_View_Label1 = value;
      }
    }

    internal virtual Button Process_Setting_View
    {
      [DebuggerNonUserCode] get => this._Process_Setting_View;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        EventHandler eventHandler = new EventHandler(this.Process_Setting_View_Click);
        if (this._Process_Setting_View != null)
          this._Process_Setting_View.Click -= eventHandler;
        this._Process_Setting_View = value;
        if (this._Process_Setting_View == null)
          return;
        this._Process_Setting_View.Click += eventHandler;
      }
    }

    internal virtual Label Process_Setting_View_Label2
    {
      [DebuggerNonUserCode] get => this._Process_Setting_View_Label2;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Process_Setting_View_Label2 = value;
      }
    }

    internal virtual Label Process_Setting_View_Label3
    {
      [DebuggerNonUserCode] get => this._Process_Setting_View_Label3;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Process_Setting_View_Label3 = value;
      }
    }

    internal virtual Label Process_Setting_View_Label6
    {
      [DebuggerNonUserCode] get => this._Process_Setting_View_Label6;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Process_Setting_View_Label6 = value;
      }
    }

    internal virtual Label Process_Setting_View_Label7
    {
      [DebuggerNonUserCode] get => this._Process_Setting_View_Label7;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Process_Setting_View_Label7 = value;
      }
    }

    internal virtual Label Process_Setting_View_Label8
    {
      [DebuggerNonUserCode] get => this._Process_Setting_View_Label8;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Process_Setting_View_Label8 = value;
      }
    }

    internal virtual Label Process_Setting_View_Label9
    {
      [DebuggerNonUserCode] get => this._Process_Setting_View_Label9;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Process_Setting_View_Label9 = value;
      }
    }

    internal virtual Label Process_Setting_View_Label10
    {
      [DebuggerNonUserCode] get => this._Process_Setting_View_Label10;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Process_Setting_View_Label10 = value;
      }
    }

    internal virtual Label Process_Setting_View_Label11
    {
      [DebuggerNonUserCode] get => this._Process_Setting_View_Label11;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Process_Setting_View_Label11 = value;
      }
    }

    internal virtual Label Process_Setting_View_Label12
    {
      [DebuggerNonUserCode] get => this._Process_Setting_View_Label12;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Process_Setting_View_Label12 = value;
      }
    }

    internal virtual Label Process_Setting_View_Label13
    {
      [DebuggerNonUserCode] get => this._Process_Setting_View_Label13;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Process_Setting_View_Label13 = value;
      }
    }

    internal virtual Label Process_Setting_View_Label14
    {
      [DebuggerNonUserCode] get => this._Process_Setting_View_Label14;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Process_Setting_View_Label14 = value;
      }
    }

    internal virtual Label Process_Setting_View_Label15
    {
      [DebuggerNonUserCode] get => this._Process_Setting_View_Label15;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Process_Setting_View_Label15 = value;
      }
    }

    internal virtual Label Process_Setting_View_Label16
    {
      [DebuggerNonUserCode] get => this._Process_Setting_View_Label16;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Process_Setting_View_Label16 = value;
      }
    }

    internal virtual GroupBox Fill_Profile
    {
      [DebuggerNonUserCode] get => this._Fill_Profile;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Fill_Profile = value;
      }
    }

    internal virtual TableLayoutPanel Fill_Pofile_Table
    {
      [DebuggerNonUserCode] get => this._Fill_Pofile_Table;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Fill_Pofile_Table = value;
      }
    }

    internal virtual Label Fill_Profile_Label12
    {
      [DebuggerNonUserCode] get => this._Fill_Profile_Label12;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Fill_Profile_Label12 = value;
      }
    }

    internal virtual Label Fill_Profile_Label11
    {
      [DebuggerNonUserCode] get => this._Fill_Profile_Label11;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Fill_Profile_Label11 = value;
      }
    }

    internal virtual Label Fill_Profile_Label10
    {
      [DebuggerNonUserCode] get => this._Fill_Profile_Label10;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Fill_Profile_Label10 = value;
      }
    }

    internal virtual Label Fill_Profile_Label9
    {
      [DebuggerNonUserCode] get => this._Fill_Profile_Label9;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Fill_Profile_Label9 = value;
      }
    }

    internal virtual Label Fill_Profile_Label8
    {
      [DebuggerNonUserCode] get => this._Fill_Profile_Label8;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Fill_Profile_Label8 = value;
      }
    }

    internal virtual Label Fill_Profile_Label7
    {
      [DebuggerNonUserCode] get => this._Fill_Profile_Label7;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Fill_Profile_Label7 = value;
      }
    }

    internal virtual Label Fill_Profile_Label6
    {
      [DebuggerNonUserCode] get => this._Fill_Profile_Label6;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Fill_Profile_Label6 = value;
      }
    }

    internal virtual Label Fill_Profile_Label5
    {
      [DebuggerNonUserCode] get => this._Fill_Profile_Label5;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Fill_Profile_Label5 = value;
      }
    }

    internal virtual Label Fill_Profile_Label4
    {
      [DebuggerNonUserCode] get => this._Fill_Profile_Label4;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Fill_Profile_Label4 = value;
      }
    }

    internal virtual Label Fill_Profile_Label3
    {
      [DebuggerNonUserCode] get => this._Fill_Profile_Label3;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Fill_Profile_Label3 = value;
      }
    }

    internal virtual Label Fill_Profile_Label2
    {
      [DebuggerNonUserCode] get => this._Fill_Profile_Label2;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Fill_Profile_Label2 = value;
      }
    }

    internal virtual Label Fill_Profile_Label1
    {
      [DebuggerNonUserCode] get => this._Fill_Profile_Label1;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Fill_Profile_Label1 = value;
      }
    }

    internal virtual GroupBox Pack_Profile
    {
      [DebuggerNonUserCode] get => this._Pack_Profile;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Pack_Profile = value;
      }
    }

    internal virtual TableLayoutPanel TableLayoutPanel1
    {
      [DebuggerNonUserCode] get => this._TableLayoutPanel1;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._TableLayoutPanel1 = value;
      }
    }

    internal virtual Label Pack_Profile_Label12
    {
      [DebuggerNonUserCode] get => this._Pack_Profile_Label12;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Pack_Profile_Label12 = value;
      }
    }

    internal virtual Label Pack_Profile_Label11
    {
      [DebuggerNonUserCode] get => this._Pack_Profile_Label11;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Pack_Profile_Label11 = value;
      }
    }

    internal virtual Label Pack_Profile_Label10
    {
      [DebuggerNonUserCode] get => this._Pack_Profile_Label10;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Pack_Profile_Label10 = value;
      }
    }

    internal virtual Label Pack_Profile_Label9
    {
      [DebuggerNonUserCode] get => this._Pack_Profile_Label9;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Pack_Profile_Label9 = value;
      }
    }

    internal virtual Label Pack_Profile_Label8
    {
      [DebuggerNonUserCode] get => this._Pack_Profile_Label8;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Pack_Profile_Label8 = value;
      }
    }

    internal virtual Label Pack_Profile_Label7
    {
      [DebuggerNonUserCode] get => this._Pack_Profile_Label7;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Pack_Profile_Label7 = value;
      }
    }

    internal virtual Label Pack_Profile_Label6
    {
      [DebuggerNonUserCode] get => this._Pack_Profile_Label6;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Pack_Profile_Label6 = value;
      }
    }

    internal virtual Label Pack_Profile_Label5
    {
      [DebuggerNonUserCode] get => this._Pack_Profile_Label5;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Pack_Profile_Label5 = value;
      }
    }

    internal virtual Label Pack_Profile_Label4
    {
      [DebuggerNonUserCode] get => this._Pack_Profile_Label4;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Pack_Profile_Label4 = value;
      }
    }

    internal virtual Label Pack_Profile_Label3
    {
      [DebuggerNonUserCode] get => this._Pack_Profile_Label3;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Pack_Profile_Label3 = value;
      }
    }

    internal virtual Label Pack_Profile_Label2
    {
      [DebuggerNonUserCode] get => this._Pack_Profile_Label2;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Pack_Profile_Label2 = value;
      }
    }

    internal virtual Label Pack_Profile_Label1
    {
      [DebuggerNonUserCode] get => this._Pack_Profile_Label1;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Pack_Profile_Label1 = value;
      }
    }

    internal virtual Label PSSV_Study_Name_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Study_Name_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Study_Name_Label = value;
      }
    }

    internal virtual Label PSSV_Mesh_Type_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Mesh_Type_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Mesh_Type_Label = value;
      }
    }

    internal virtual Label PSSV_Analysis_Sequence_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Analysis_Sequence_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Analysis_Sequence_Label = value;
      }
    }

    internal virtual Label PSSV_Fill_Control_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Fill_Control_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Fill_Control_Label = value;
      }
    }

    internal virtual Label PSSV_Fill_TIme_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Fill_TIme_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Fill_TIme_Label = value;
      }
    }

    internal virtual Label PSSV_Flow_Rate_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Flow_Rate_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Flow_Rate_Label = value;
      }
    }

    internal virtual Label PSSV_Packing_VP_Control_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Packing_VP_Control_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Packing_VP_Control_Label = value;
      }
    }

    internal virtual Label PSSV_Packing_VP_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Packing_VP_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Packing_VP_Label = value;
      }
    }

    internal virtual Label PSSV_Packing_Control_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Packing_Control_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Packing_Control_Label = value;
      }
    }

    internal virtual Label PSSV_Cooling_Control_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Cooling_Control_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Cooling_Control_Label = value;
      }
    }

    internal virtual Label PSSV_Cooling_Time_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Cooling_Time_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Cooling_Time_Label = value;
      }
    }

    internal virtual Label PSSV_Material_Manufacturer_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Material_Manufacturer_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Material_Manufacturer_Label = value;
      }
    }

    internal virtual Label PSSV_Material_Name_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Material_Name_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Material_Name_Label = value;
      }
    }

    internal virtual Label PSSV_Material_Family_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Material_Family_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Material_Family_Label = value;
      }
    }

    internal virtual Label PSSV_Fill_Profile_1_1_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Fill_Profile_1_1_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Fill_Profile_1_1_Label = value;
      }
    }

    internal virtual Label PSSV_Fill_Profile_2_1_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Fill_Profile_2_1_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Fill_Profile_2_1_Label = value;
      }
    }

    internal virtual Label PSSV_Fill_Profile_3_1_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Fill_Profile_3_1_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Fill_Profile_3_1_Label = value;
      }
    }

    internal virtual Label PSSV_Fill_Profile_4_1_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Fill_Profile_4_1_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Fill_Profile_4_1_Label = value;
      }
    }

    internal virtual Label PSSV_Fill_Profile_5_1_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Fill_Profile_5_1_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Fill_Profile_5_1_Label = value;
      }
    }

    internal virtual Label PSSV_Fill_Profile_6_1_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Fill_Profile_6_1_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Fill_Profile_6_1_Label = value;
      }
    }

    internal virtual Label PSSV_Fill_Profile_7_1_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Fill_Profile_7_1_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Fill_Profile_7_1_Label = value;
      }
    }

    internal virtual Label PSSV_Fill_Profile_8_1_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Fill_Profile_8_1_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Fill_Profile_8_1_Label = value;
      }
    }

    internal virtual Label PSSV_Fill_Profile_9_1_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Fill_Profile_9_1_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Fill_Profile_9_1_Label = value;
      }
    }

    internal virtual Label PSSV_Fill_Profile_10_1_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Fill_Profile_10_1_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Fill_Profile_10_1_Label = value;
      }
    }

    internal virtual Label PSSV_Fill_Profile_1_2_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Fill_Profile_1_2_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Fill_Profile_1_2_Label = value;
      }
    }

    internal virtual Label PSSV_Fill_Profile_2_2_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Fill_Profile_2_2_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Fill_Profile_2_2_Label = value;
      }
    }

    internal virtual Label PSSV_Fill_Profile_3_2_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Fill_Profile_3_2_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Fill_Profile_3_2_Label = value;
      }
    }

    internal virtual Label PSSV_Fill_Profile_4_2_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Fill_Profile_4_2_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Fill_Profile_4_2_Label = value;
      }
    }

    internal virtual Label PSSV_Fill_Profile_5_2_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Fill_Profile_5_2_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Fill_Profile_5_2_Label = value;
      }
    }

    internal virtual Label PSSV_Fill_Profile_6_2_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Fill_Profile_6_2_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Fill_Profile_6_2_Label = value;
      }
    }

    internal virtual Label PSSV_Fill_Profile_7_2_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Fill_Profile_7_2_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Fill_Profile_7_2_Label = value;
      }
    }

    internal virtual Label PSSV_Fill_Profile_8_2_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Fill_Profile_8_2_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Fill_Profile_8_2_Label = value;
      }
    }

    internal virtual Label PSSV_Fill_Profile_9_2_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Fill_Profile_9_2_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Fill_Profile_9_2_Label = value;
      }
    }

    internal virtual Label PSSV_Fill_Profile_10_2_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Fill_Profile_10_2_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Fill_Profile_10_2_Label = value;
      }
    }

    internal virtual Label PSSV_Packing_Profile_1_1_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Packing_Profile_1_1_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Packing_Profile_1_1_Label = value;
      }
    }

    internal virtual Label PSSV_Packing_Profile_2_1_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Packing_Profile_2_1_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Packing_Profile_2_1_Label = value;
      }
    }

    internal virtual Label PSSV_Packing_Profile_3_1_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Packing_Profile_3_1_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Packing_Profile_3_1_Label = value;
      }
    }

    internal virtual Label PSSV_Packing_Profile_4_1_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Packing_Profile_4_1_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Packing_Profile_4_1_Label = value;
      }
    }

    internal virtual Label PSSV_Packing_Profile_5_1_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Packing_Profile_5_1_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Packing_Profile_5_1_Label = value;
      }
    }

    internal virtual Label PSSV_Packing_Profile_6_1_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Packing_Profile_6_1_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Packing_Profile_6_1_Label = value;
      }
    }

    internal virtual Label PSSV_Packing_Profile_7_1_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Packing_Profile_7_1_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Packing_Profile_7_1_Label = value;
      }
    }

    internal virtual Label PSSV_Packing_Profile_8_1_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Packing_Profile_8_1_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Packing_Profile_8_1_Label = value;
      }
    }

    internal virtual Label PSSV_Packing_Profile_9_1_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Packing_Profile_9_1_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Packing_Profile_9_1_Label = value;
      }
    }

    internal virtual Label PSSV_Packing_Profile_10_1_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Packing_Profile_10_1_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Packing_Profile_10_1_Label = value;
      }
    }

    internal virtual Label PSSV_Packing_Profile_1_2_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Packing_Profile_1_2_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Packing_Profile_1_2_Label = value;
      }
    }

    internal virtual Label PSSV_Packing_Profile_2_2_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Packing_Profile_2_2_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Packing_Profile_2_2_Label = value;
      }
    }

    internal virtual Label PSSV_Packing_Profile_3_2_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Packing_Profile_3_2_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Packing_Profile_3_2_Label = value;
      }
    }

    internal virtual Label PSSV_Packing_Profile_4_2_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Packing_Profile_4_2_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Packing_Profile_4_2_Label = value;
      }
    }

    internal virtual Label PSSV_Packing_Profile_5_2_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Packing_Profile_5_2_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Packing_Profile_5_2_Label = value;
      }
    }

    internal virtual Label PSSV_Packing_Profile_6_2_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Packing_Profile_6_2_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Packing_Profile_6_2_Label = value;
      }
    }

    internal virtual Label PSSV_Packing_Profile_7_2_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Packing_Profile_7_2_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Packing_Profile_7_2_Label = value;
      }
    }

    internal virtual Label PSSV_Packing_Profile_8_2_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Packing_Profile_8_2_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Packing_Profile_8_2_Label = value;
      }
    }

    internal virtual Label PSSV_Packing_Profile_9_2_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Packing_Profile_9_2_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Packing_Profile_9_2_Label = value;
      }
    }

    internal virtual Label PSSV_Packing_Profile_10_2_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Packing_Profile_10_2_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Packing_Profile_10_2_Label = value;
      }
    }

    internal virtual Label Process_Setting_View_Label4
    {
      [DebuggerNonUserCode] get => this._Process_Setting_View_Label4;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Process_Setting_View_Label4 = value;
      }
    }

    internal virtual Label Process_Setting_View_Label5
    {
      [DebuggerNonUserCode] get => this._Process_Setting_View_Label5;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Process_Setting_View_Label5 = value;
      }
    }

    internal virtual Label PSSV_Mold_Surface_Temperature_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Mold_Surface_Temperature_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Mold_Surface_Temperature_Label = value;
      }
    }

    internal virtual Label PSSV_Melt_Temperature_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Melt_Temperature_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Melt_Temperature_Label = value;
      }
    }

    internal virtual Label Fill_Profile_Label13
    {
      [DebuggerNonUserCode] get => this._Fill_Profile_Label13;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._Fill_Profile_Label13 = value;
      }
    }

    internal virtual Label PSSV_Fill_Profile_11_1_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Fill_Profile_11_1_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Fill_Profile_11_1_Label = value;
      }
    }

    internal virtual Label PSSV_Fill_Profile_11_2_Label
    {
      [DebuggerNonUserCode] get => this._PSSV_Fill_Profile_11_2_Label;
      [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
      {
        this._PSSV_Fill_Profile_11_2_Label = value;
      }
    }

    private void Process_Setting_View_Click(object sender, EventArgs e)
    {
label_1:
      int num1;
      int num2;
      try
      {
        ProjectData.ClearProjectError();
        num1 = -2;
label_2:
        int num3 = 2;
        object objectValue1 = RuntimeHelpers.GetObjectValue(Interaction.GetObject(Conversions.ToString(NewLateBinding.LateGet(Interaction.CreateObject("WScript.Shell"), (System.Type) null, "ExpandEnvironmentStrings", new object[1]
        {
          (object) "%SAInstance%"
        }, (string[]) null, (System.Type[]) null, (bool[]) null))));
label_3:
        ProjectData.ClearProjectError();
        num1 = 0;
label_4:
        num3 = 4;
        if (objectValue1 == null)
          goto label_6;
label_5:
        num3 = 5;
        object objectValue2 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue1, (System.Type) null, "GetSASynergy", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
        goto label_8;
label_6:
        num3 = 7;
label_7:
        num3 = 8;
        objectValue2 = RuntimeHelpers.GetObjectValue(Interaction.CreateObject("synergy.Synergy"));
label_8:
label_9:
        num3 = 10;
        NewLateBinding.LateCall(objectValue2, (System.Type) null, "SetUnits", new object[1]
        {
          (object) "Metric"
        }, (string[]) null, (System.Type[]) null, (bool[]) null, true);
label_10:
        ProjectData.ClearProjectError();
        num1 = 2;
label_11:
        num3 = 12;
        this.PSSV_Fill_Profile_1_1_Label.Text = "";
label_12:
        num3 = 13;
        this.PSSV_Fill_Profile_1_2_Label.Text = "";
label_13:
        num3 = 14;
        this.PSSV_Fill_Profile_2_1_Label.Text = "";
label_14:
        num3 = 15;
        this.PSSV_Fill_Profile_2_2_Label.Text = "";
label_15:
        num3 = 16;
        this.PSSV_Fill_Profile_3_1_Label.Text = "";
label_16:
        num3 = 17;
        this.PSSV_Fill_Profile_3_2_Label.Text = "";
label_17:
        num3 = 18;
        this.PSSV_Fill_Profile_4_1_Label.Text = "";
label_18:
        num3 = 19;
        this.PSSV_Fill_Profile_4_2_Label.Text = "";
label_19:
        num3 = 20;
        this.PSSV_Fill_Profile_5_1_Label.Text = "";
label_20:
        num3 = 21;
        this.PSSV_Fill_Profile_5_2_Label.Text = "";
label_21:
        num3 = 22;
        this.PSSV_Fill_Profile_6_1_Label.Text = "";
label_22:
        num3 = 23;
        this.PSSV_Fill_Profile_6_2_Label.Text = "";
label_23:
        num3 = 24;
        this.PSSV_Fill_Profile_7_1_Label.Text = "";
label_24:
        num3 = 25;
        this.PSSV_Fill_Profile_7_2_Label.Text = "";
label_25:
        num3 = 26;
        this.PSSV_Fill_Profile_8_1_Label.Text = "";
label_26:
        num3 = 27;
        this.PSSV_Fill_Profile_8_2_Label.Text = "";
label_27:
        num3 = 28;
        this.PSSV_Fill_Profile_9_1_Label.Text = "";
label_28:
        num3 = 29;
        this.PSSV_Fill_Profile_9_2_Label.Text = "";
label_29:
        num3 = 30;
        this.PSSV_Fill_Profile_10_1_Label.Text = "";
label_30:
        num3 = 31;
        this.PSSV_Fill_Profile_10_2_Label.Text = "";
label_31:
        num3 = 32;
        this.PSSV_Packing_Profile_1_1_Label.Text = "";
label_32:
        num3 = 33;
        this.PSSV_Packing_Profile_1_2_Label.Text = "";
label_33:
        num3 = 34;
        this.PSSV_Packing_Profile_2_1_Label.Text = "";
label_34:
        num3 = 35;
        this.PSSV_Packing_Profile_2_2_Label.Text = "";
label_35:
        num3 = 36;
        this.PSSV_Packing_Profile_3_1_Label.Text = "";
label_36:
        num3 = 37;
        this.PSSV_Packing_Profile_3_2_Label.Text = "";
label_37:
        num3 = 38;
        this.PSSV_Packing_Profile_4_1_Label.Text = "";
label_38:
        num3 = 39;
        this.PSSV_Packing_Profile_4_2_Label.Text = "";
label_39:
        num3 = 40;
        this.PSSV_Packing_Profile_5_1_Label.Text = "";
label_40:
        num3 = 41;
        this.PSSV_Packing_Profile_5_2_Label.Text = "";
label_41:
        num3 = 42;
        this.PSSV_Packing_Profile_6_1_Label.Text = "";
label_42:
        num3 = 43;
        this.PSSV_Packing_Profile_6_2_Label.Text = "";
label_43:
        num3 = 44;
        this.PSSV_Packing_Profile_7_1_Label.Text = "";
label_44:
        num3 = 45;
        this.PSSV_Packing_Profile_7_2_Label.Text = "";
label_45:
        num3 = 46;
        this.PSSV_Packing_Profile_8_1_Label.Text = "";
label_46:
        num3 = 47;
        this.PSSV_Packing_Profile_8_2_Label.Text = "";
label_47:
        num3 = 48;
        this.PSSV_Packing_Profile_9_1_Label.Text = "";
label_48:
        num3 = 49;
        this.PSSV_Packing_Profile_9_2_Label.Text = "";
label_49:
        num3 = 50;
        this.PSSV_Packing_Profile_10_1_Label.Text = "";
label_50:
        num3 = 51;
        this.PSSV_Packing_Profile_10_2_Label.Text = "";
label_51:
        num3 = 52;
        this.PSSV_Fill_Profile_11_1_Label.Text = "";
label_52:
        num3 = 53;
        this.PSSV_Fill_Profile_11_2_Label.Text = "";
label_53:
        num3 = 54;
        RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue2, (System.Type) null, "Project", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_54:
        num3 = 55;
        object objectValue3 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue2, (System.Type) null, "StudyDoc", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_55:
        num3 = 56;
        object objectValue4 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue3, (System.Type) null, "StudyName", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_56:
        num3 = 57;
        this.PSSV_Study_Name_Label.Text = Conversions.ToString(objectValue4);
label_57:
        num3 = 58;
        object Left1 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue3, (System.Type) null, "MeshType", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_58:
        num3 = 59;
        if (!Operators.ConditionalCompareObjectEqual(Left1, (object) "Fusion", false))
          goto label_61;
label_59:
        num3 = 60;
        Left1 = (object) "Dual-Domain";
label_60:
        num3 = 61;
        this.PSSV_Mesh_Type_Label.Text = Conversions.ToString(Left1);
        goto label_63;
label_61:
        num3 = 63;
label_62:
        num3 = 64;
        this.PSSV_Mesh_Type_Label.Text = Conversions.ToString(Left1);
label_63:
label_64:
        num3 = 66;
        object objectValue5 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue3, (System.Type) null, "AnalysisSequence", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_65:
        num3 = 67;
        if (!Operators.ConditionalCompareObjectEqual(objectValue5, (object) "Fill", false))
          goto label_68;
label_66:
        num3 = 68;
        object obj1 = (object) "Fill";
label_67:
        num3 = 69;
        this.PSSV_Analysis_Sequence_Label.Text = Conversions.ToString(obj1);
        goto label_77;
label_68:
        num3 = 71;
        if (!Operators.ConditionalCompareObjectEqual(objectValue5, (object) "Flow", false))
          goto label_71;
label_69:
        num3 = 72;
        obj1 = (object) "Fill + Pack";
label_70:
        num3 = 73;
        this.PSSV_Analysis_Sequence_Label.Text = Conversions.ToString(obj1);
        goto label_77;
label_71:
        num3 = 75;
        if (!Operators.ConditionalCompareObjectEqual(objectValue5, (object) "Flow|Warp", false))
          goto label_74;
label_72:
        num3 = 76;
        obj1 = (object) "Fill + Pack + Warp";
label_73:
        num3 = 77;
        this.PSSV_Analysis_Sequence_Label.Text = Conversions.ToString(obj1);
        goto label_77;
label_74:
        num3 = 79;
        if (!Operators.ConditionalCompareObjectEqual(objectValue5, (object) "Cool|Flow|Warp", false))
          goto label_77;
label_75:
        num3 = 80;
        obj1 = (object) "Cool + Fill + Pack + Warp";
label_76:
        num3 = 81;
        this.PSSV_Analysis_Sequence_Label.Text = Conversions.ToString(obj1);
label_77:
label_78:
        num3 = 83;
        object objectValue6 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue2, (System.Type) null, "PropertyEditor", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_79:
        num3 = 84;
        object objectValue7 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue6, (System.Type) null, "GetFirstProperty", new object[1]
        {
          (object) 30011
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_80:
        num3 = 85;
        object objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 11108
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_81:
        num3 = 86;
        object objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_82:
        num3 = 87;
        this.PSSV_Mold_Surface_Temperature_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null), (object) " ℃"));
label_83:
        num3 = 88;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 11002
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_84:
        num3 = 89;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_85:
        num3 = 90;
        this.PSSV_Melt_Temperature_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null), (object) " ℃"));
label_86:
        num3 = 91;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10109
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_87:
        num3 = 92;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_88:
        num3 = 93;
        object obj2 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null));
label_89:
        num3 = 94;
        object Right1 = (object) "";
label_90:
        num3 = 95;
        object Right2 = (object) "";
label_91:
        num3 = 96;
        object Left2 = obj2;
label_92:
        num3 = 99;
        if (!Operators.ConditionalCompareObjectEqual(Left2, (object) "1", false))
          goto label_96;
label_93:
        num3 = 100;
        obj2 = (object) "자동";
label_94:
        num3 = 101;
        this.PSSV_Fill_TIme_Label.Text = "-";
label_95:
        num3 = 102;
        this.PSSV_Flow_Rate_Label.Text = "-";
        goto label_374;
label_96:
        num3 = 104;
        if (!Operators.ConditionalCompareObjectEqual(Left2, (object) "2", false))
          goto label_102;
label_97:
        num3 = 105;
        obj2 = (object) "사출시간";
label_98:
        num3 = 106;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10100
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_99:
        num3 = 107;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_100:
        num3 = 108;
        this.PSSV_Fill_TIme_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null), (object) " s"));
label_101:
        num3 = 109;
        this.PSSV_Flow_Rate_Label.Text = "-";
        goto label_374;
label_102:
        num3 = 111;
        if (!Operators.ConditionalCompareObjectEqual(Left2, (object) "3", false))
          goto label_108;
label_103:
        num3 = 112;
        obj2 = (object) "사출유량";
label_104:
        num3 = 113;
        this.PSSV_Fill_TIme_Label.Text = "-";
label_105:
        num3 = 114;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10107
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_106:
        num3 = 115;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_107:
        num3 = 116;
        this.PSSV_Flow_Rate_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null), (object) " cm^3/s"));
        goto label_374;
label_108:
        num3 = 118;
        if (!Operators.ConditionalCompareObjectEqual(Left2, (object) "4", false))
          goto label_112;
label_109:
        num3 = 119;
        this.PSSV_Fill_TIme_Label.Text = "-";
label_110:
        num3 = 120;
        this.PSSV_Flow_Rate_Label.Text = "-";
label_111:
        num3 = 121;
        obj2 = (object) "예전 속도 / 프로파일지원안함";
        goto label_374;
label_112:
        num3 = 123;
        if (!Operators.ConditionalCompareObjectEqual(Left2, (object) "5", false))
          goto label_198;
label_113:
        num3 = 124;
        this.PSSV_Fill_TIme_Label.Text = "-";
label_114:
        num3 = 125;
        this.PSSV_Flow_Rate_Label.Text = "-";
label_115:
        num3 = 126;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10602
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_116:
        num3 = (int) sbyte.MaxValue;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_117:
        num3 = 128;
        object objectValue10 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null));
label_118:
        num3 = 129;
        Right1 = (object) "%";
label_119:
        num3 = 130;
        Right2 = (object) "%";
label_120:
        num3 = 131;
        object Left3 = objectValue10;
label_121:
        num3 = 134;
        if (!Operators.ConditionalCompareObjectEqual(Left3, (object) "1", false))
          goto label_159;
label_122:
        num3 = 135;
        obj2 = (object) "상대 속도 / %유량 vs. %부피";
label_123:
        num3 = 136;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10618
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_124:
        num3 = 137;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_125:
        num3 = 138;
        object objectValue11 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10110
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_126:
        num3 = 139;
        object objectValue12 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue11, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_127:
        num3 = 140;
        object Left4 = NewLateBinding.LateIndexGet(objectValue12, new object[1]
        {
          (object) 0
        }, (string[]) null);
label_128:
        num3 = 143;
        if (!Operators.ConditionalCompareObjectEqual(Left4, (object) "1", false))
          goto label_133;
label_129:
        num3 = 144;
        this.PSSV_Fill_Profile_11_1_Label.Text = "사출시간";
label_130:
        num3 = 145;
        object objectValue13 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10100
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_131:
        num3 = 146;
        object objectValue14 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue13, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_132:
        num3 = 147;
        this.PSSV_Fill_Profile_11_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue14, new object[1]
        {
          (object) 0
        }, (string[]) null), (object) "s"));
        goto label_138;
label_133:
        num3 = 149;
        if (!Operators.ConditionalCompareObjectEqual(Left4, (object) "2", false))
          goto label_138;
label_134:
        num3 = 150;
        this.PSSV_Fill_Profile_11_1_Label.Text = "사출유량";
label_135:
        num3 = 151;
        objectValue13 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10107
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_136:
        num3 = 152;
        objectValue14 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue13, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_137:
        num3 = 153;
        this.PSSV_Fill_Profile_11_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue14, new object[1]
        {
          (object) 0
        }, (string[]) null), (object) "cm^3/s"));
label_138:
label_139:
        num3 = 155;
        this.PSSV_Fill_Profile_1_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null), Right1));
label_140:
        num3 = 156;
        this.PSSV_Fill_Profile_1_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 1
        }, (string[]) null), Right2));
label_141:
        num3 = 157;
        this.PSSV_Fill_Profile_2_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 2
        }, (string[]) null), Right1));
label_142:
        num3 = 158;
        this.PSSV_Fill_Profile_2_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 3
        }, (string[]) null), Right2));
label_143:
        num3 = 159;
        this.PSSV_Fill_Profile_3_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 4
        }, (string[]) null), Right1));
label_144:
        num3 = 160;
        this.PSSV_Fill_Profile_3_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 5
        }, (string[]) null), Right2));
label_145:
        num3 = 161;
        this.PSSV_Fill_Profile_4_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 6
        }, (string[]) null), Right1));
label_146:
        num3 = 162;
        this.PSSV_Fill_Profile_4_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 7
        }, (string[]) null), Right2));
label_147:
        num3 = 163;
        this.PSSV_Fill_Profile_5_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 8
        }, (string[]) null), Right1));
label_148:
        num3 = 164;
        this.PSSV_Fill_Profile_5_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 9
        }, (string[]) null), Right2));
label_149:
        num3 = 165;
        this.PSSV_Fill_Profile_6_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 10
        }, (string[]) null), Right1));
label_150:
        num3 = 166;
        this.PSSV_Fill_Profile_6_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 11
        }, (string[]) null), Right2));
label_151:
        num3 = 167;
        this.PSSV_Fill_Profile_7_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 12
        }, (string[]) null), Right1));
label_152:
        num3 = 168;
        this.PSSV_Fill_Profile_7_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 13
        }, (string[]) null), Right2));
label_153:
        num3 = 169;
        this.PSSV_Fill_Profile_8_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 14
        }, (string[]) null), Right1));
label_154:
        num3 = 170;
        this.PSSV_Fill_Profile_8_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 15
        }, (string[]) null), Right2));
label_155:
        num3 = 171;
        this.PSSV_Fill_Profile_9_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 16
        }, (string[]) null), Right1));
label_156:
        num3 = 172;
        this.PSSV_Fill_Profile_9_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 17
        }, (string[]) null), Right2));
label_157:
        num3 = 173;
        this.PSSV_Fill_Profile_10_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 18
        }, (string[]) null), Right1));
label_158:
        num3 = 174;
        this.PSSV_Fill_Profile_10_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 19
        }, (string[]) null), Right2));
        goto label_197;
label_159:
        num3 = 176;
        if (!Operators.ConditionalCompareObjectEqual(Left3, (object) "2", false))
          goto label_197;
label_160:
        num3 = 177;
        obj2 = (object) "상대 속도 / %램속도 vs. %스트로크";
label_161:
        num3 = 178;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10605
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_162:
        num3 = 179;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_163:
        num3 = 180;
        objectValue11 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10110
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_164:
        num3 = 181;
        objectValue12 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue11, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_165:
        num3 = 182;
        object Left5 = NewLateBinding.LateIndexGet(objectValue12, new object[1]
        {
          (object) 0
        }, (string[]) null);
label_166:
        num3 = 185;
        if (!Operators.ConditionalCompareObjectEqual(Left5, (object) "1", false))
          goto label_171;
label_167:
        num3 = 186;
        this.PSSV_Fill_Profile_11_1_Label.Text = "사출시간";
label_168:
        num3 = 187;
        objectValue13 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10100
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_169:
        num3 = 188;
        objectValue14 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue13, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_170:
        num3 = 189;
        this.PSSV_Fill_Profile_11_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue14, new object[1]
        {
          (object) 0
        }, (string[]) null), (object) "s"));
        goto label_176;
label_171:
        num3 = 191;
        if (!Operators.ConditionalCompareObjectEqual(Left5, (object) "2", false))
          goto label_176;
label_172:
        num3 = 192;
        this.PSSV_Fill_Profile_11_1_Label.Text = "사출유량";
label_173:
        num3 = 193;
        objectValue13 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10107
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_174:
        num3 = 194;
        objectValue14 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue13, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_175:
        num3 = 195;
        this.PSSV_Fill_Profile_11_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue14, new object[1]
        {
          (object) 0
        }, (string[]) null), (object) "cm^3/s"));
label_176:
label_177:
        num3 = 197;
        this.PSSV_Fill_Profile_1_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null), Right1));
label_178:
        num3 = 198;
        this.PSSV_Fill_Profile_1_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 1
        }, (string[]) null), Right2));
label_179:
        num3 = 199;
        this.PSSV_Fill_Profile_2_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 2
        }, (string[]) null), Right1));
label_180:
        num3 = 200;
        this.PSSV_Fill_Profile_2_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 3
        }, (string[]) null), Right2));
label_181:
        num3 = 201;
        this.PSSV_Fill_Profile_3_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 4
        }, (string[]) null), Right1));
label_182:
        num3 = 202;
        this.PSSV_Fill_Profile_3_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 5
        }, (string[]) null), Right2));
label_183:
        num3 = 203;
        this.PSSV_Fill_Profile_4_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 6
        }, (string[]) null), Right1));
label_184:
        num3 = 204;
        this.PSSV_Fill_Profile_4_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 7
        }, (string[]) null), Right2));
label_185:
        num3 = 205;
        this.PSSV_Fill_Profile_5_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 8
        }, (string[]) null), Right1));
label_186:
        num3 = 206;
        this.PSSV_Fill_Profile_5_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 9
        }, (string[]) null), Right2));
label_187:
        num3 = 207;
        this.PSSV_Fill_Profile_6_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 10
        }, (string[]) null), Right1));
label_188:
        num3 = 208;
        this.PSSV_Fill_Profile_6_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 11
        }, (string[]) null), Right2));
label_189:
        num3 = 209;
        this.PSSV_Fill_Profile_7_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 12
        }, (string[]) null), Right1));
label_190:
        num3 = 210;
        this.PSSV_Fill_Profile_7_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 13
        }, (string[]) null), Right2));
label_191:
        num3 = 211;
        this.PSSV_Fill_Profile_8_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 14
        }, (string[]) null), Right1));
label_192:
        num3 = 212;
        this.PSSV_Fill_Profile_8_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 15
        }, (string[]) null), Right2));
label_193:
        num3 = 213;
        this.PSSV_Fill_Profile_9_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 16
        }, (string[]) null), Right1));
label_194:
        num3 = 214;
        this.PSSV_Fill_Profile_9_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 17
        }, (string[]) null), Right2));
label_195:
        num3 = 215;
        this.PSSV_Fill_Profile_10_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 18
        }, (string[]) null), Right1));
label_196:
        num3 = 216;
        this.PSSV_Fill_Profile_10_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 19
        }, (string[]) null), Right2));
label_197:
        goto label_374;
label_198:
        num3 = 219;
        if (!Operators.ConditionalCompareObjectEqual(Left2, (object) "6", false))
          goto label_374;
label_199:
        num3 = 220;
        this.PSSV_Fill_TIme_Label.Text = "-";
label_200:
        num3 = 221;
        this.PSSV_Flow_Rate_Label.Text = "-";
label_201:
        num3 = 222;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10603
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_202:
        num3 = 223;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_203:
        num3 = 224;
        objectValue10 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null));
label_204:
        num3 = 225;
        object Left6 = objectValue10;
label_205:
        num3 = 228;
        if (!Operators.ConditionalCompareObjectEqual(Left6, (object) "3", false))
          goto label_233;
label_206:
        num3 = 229;
        obj2 = (object) "절대 속도 / 램속도 vs. 램위치";
label_207:
        num3 = 230;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10604
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_208:
        num3 = 231;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_209:
        num3 = 232;
        objectValue11 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10306
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_210:
        num3 = 233;
        objectValue12 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue11, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_211:
        num3 = 234;
        this.PSSV_Fill_Profile_11_1_Label.Text = "계량위치";
label_212:
        num3 = 235;
        this.PSSV_Fill_Profile_11_2_Label.Text = Conversions.ToString(NewLateBinding.LateIndexGet(objectValue12, new object[1]
        {
          (object) 1
        }, (string[]) null));
label_213:
        num3 = 236;
        this.PSSV_Fill_Profile_1_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null), Right1));
label_214:
        num3 = 237;
        this.PSSV_Fill_Profile_1_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 1
        }, (string[]) null), Right2));
label_215:
        num3 = 238;
        this.PSSV_Fill_Profile_2_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 2
        }, (string[]) null), Right1));
label_216:
        num3 = 239;
        this.PSSV_Fill_Profile_2_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 3
        }, (string[]) null), Right2));
label_217:
        num3 = 240;
        this.PSSV_Fill_Profile_3_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 4
        }, (string[]) null), Right1));
label_218:
        num3 = 241;
        this.PSSV_Fill_Profile_3_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 5
        }, (string[]) null), Right2));
label_219:
        num3 = 242;
        this.PSSV_Fill_Profile_4_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 6
        }, (string[]) null), Right1));
label_220:
        num3 = 243;
        this.PSSV_Fill_Profile_4_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 7
        }, (string[]) null), Right2));
label_221:
        num3 = 244;
        this.PSSV_Fill_Profile_5_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 8
        }, (string[]) null), Right1));
label_222:
        num3 = 245;
        this.PSSV_Fill_Profile_5_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 9
        }, (string[]) null), Right2));
label_223:
        num3 = 246;
        this.PSSV_Fill_Profile_6_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 10
        }, (string[]) null), Right1));
label_224:
        num3 = 247;
        this.PSSV_Fill_Profile_6_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 11
        }, (string[]) null), Right2));
label_225:
        num3 = 248;
        this.PSSV_Fill_Profile_7_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 12
        }, (string[]) null), Right1));
label_226:
        num3 = 249;
        this.PSSV_Fill_Profile_7_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 13
        }, (string[]) null), Right2));
label_227:
        num3 = 250;
        this.PSSV_Fill_Profile_8_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 14
        }, (string[]) null), Right1));
label_228:
        num3 = 251;
        this.PSSV_Fill_Profile_8_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 15
        }, (string[]) null), Right2));
label_229:
        num3 = 252;
        this.PSSV_Fill_Profile_9_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 16
        }, (string[]) null), Right1));
label_230:
        num3 = 253;
        this.PSSV_Fill_Profile_9_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 17
        }, (string[]) null), Right2));
label_231:
        num3 = 254;
        this.PSSV_Fill_Profile_10_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 18
        }, (string[]) null), Right1));
label_232:
        num3 = (int) byte.MaxValue;
        this.PSSV_Fill_Profile_10_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 19
        }, (string[]) null), Right2));
        goto label_373;
label_233:
        num3 = 257;
        if (!Operators.ConditionalCompareObjectEqual(Left6, (object) "4", false))
          goto label_261;
label_234:
        num3 = 258;
        obj2 = (object) "절대 속도 / %램속도 vs. 램위치";
label_235:
        num3 = 259;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10606
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_236:
        num3 = 260;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_237:
        num3 = 261;
        objectValue11 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10306
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_238:
        num3 = 262;
        objectValue12 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue11, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_239:
        num3 = 263;
        this.PSSV_Fill_Profile_11_1_Label.Text = "계량위치";
label_240:
        num3 = 264;
        this.PSSV_Fill_Profile_11_2_Label.Text = Conversions.ToString(NewLateBinding.LateIndexGet(objectValue12, new object[1]
        {
          (object) 1
        }, (string[]) null));
label_241:
        num3 = 265;
        this.PSSV_Fill_Profile_1_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null), Right1));
label_242:
        num3 = 266;
        this.PSSV_Fill_Profile_1_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 1
        }, (string[]) null), Right2));
label_243:
        num3 = 267;
        this.PSSV_Fill_Profile_2_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 2
        }, (string[]) null), Right1));
label_244:
        num3 = 268;
        this.PSSV_Fill_Profile_2_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 3
        }, (string[]) null), Right2));
label_245:
        num3 = 269;
        this.PSSV_Fill_Profile_3_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 4
        }, (string[]) null), Right1));
label_246:
        num3 = 270;
        this.PSSV_Fill_Profile_3_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 5
        }, (string[]) null), Right2));
label_247:
        num3 = 271;
        this.PSSV_Fill_Profile_4_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 6
        }, (string[]) null), Right1));
label_248:
        num3 = 272;
        this.PSSV_Fill_Profile_4_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 7
        }, (string[]) null), Right2));
label_249:
        num3 = 273;
        this.PSSV_Fill_Profile_5_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 8
        }, (string[]) null), Right1));
label_250:
        num3 = 274;
        this.PSSV_Fill_Profile_5_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 9
        }, (string[]) null), Right2));
label_251:
        num3 = 275;
        this.PSSV_Fill_Profile_6_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 10
        }, (string[]) null), Right1));
label_252:
        num3 = 276;
        this.PSSV_Fill_Profile_6_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 11
        }, (string[]) null), Right2));
label_253:
        num3 = 277;
        this.PSSV_Fill_Profile_7_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 12
        }, (string[]) null), Right1));
label_254:
        num3 = 278;
        this.PSSV_Fill_Profile_7_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 13
        }, (string[]) null), Right2));
label_255:
        num3 = 279;
        this.PSSV_Fill_Profile_8_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 14
        }, (string[]) null), Right1));
label_256:
        num3 = 280;
        this.PSSV_Fill_Profile_8_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 15
        }, (string[]) null), Right2));
label_257:
        num3 = 281;
        this.PSSV_Fill_Profile_9_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 16
        }, (string[]) null), Right1));
label_258:
        num3 = 282;
        this.PSSV_Fill_Profile_9_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 17
        }, (string[]) null), Right2));
label_259:
        num3 = 283;
        this.PSSV_Fill_Profile_10_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 18
        }, (string[]) null), Right1));
label_260:
        num3 = 284;
        this.PSSV_Fill_Profile_10_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 19
        }, (string[]) null), Right2));
        goto label_373;
label_261:
        num3 = 286;
        if (!Operators.ConditionalCompareObjectEqual(Left6, (object) "5", false))
          goto label_289;
label_262:
        num3 = 287;
        obj2 = (object) "절대 속도 / 유량 vs. 램위치";
label_263:
        num3 = 288;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10628
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_264:
        num3 = 289;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_265:
        num3 = 290;
        objectValue11 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10306
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_266:
        num3 = 291;
        objectValue12 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue11, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_267:
        num3 = 292;
        this.PSSV_Fill_Profile_11_1_Label.Text = "계량위치";
label_268:
        num3 = 293;
        this.PSSV_Fill_Profile_11_2_Label.Text = Conversions.ToString(NewLateBinding.LateIndexGet(objectValue12, new object[1]
        {
          (object) 1
        }, (string[]) null));
label_269:
        num3 = 294;
        this.PSSV_Fill_Profile_1_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null), Right1));
label_270:
        num3 = 295;
        this.PSSV_Fill_Profile_1_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 1
        }, (string[]) null), Right2));
label_271:
        num3 = 296;
        this.PSSV_Fill_Profile_2_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 2
        }, (string[]) null), Right1));
label_272:
        num3 = 297;
        this.PSSV_Fill_Profile_2_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 3
        }, (string[]) null), Right2));
label_273:
        num3 = 298;
        this.PSSV_Fill_Profile_3_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 4
        }, (string[]) null), Right1));
label_274:
        num3 = 299;
        this.PSSV_Fill_Profile_3_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 5
        }, (string[]) null), Right2));
label_275:
        num3 = 300;
        this.PSSV_Fill_Profile_4_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 6
        }, (string[]) null), Right1));
label_276:
        num3 = 301;
        this.PSSV_Fill_Profile_4_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 7
        }, (string[]) null), Right2));
label_277:
        num3 = 302;
        this.PSSV_Fill_Profile_5_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 8
        }, (string[]) null), Right1));
label_278:
        num3 = 303;
        this.PSSV_Fill_Profile_5_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 9
        }, (string[]) null), Right2));
label_279:
        num3 = 304;
        this.PSSV_Fill_Profile_6_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 10
        }, (string[]) null), Right1));
label_280:
        num3 = 305;
        this.PSSV_Fill_Profile_6_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 11
        }, (string[]) null), Right2));
label_281:
        num3 = 306;
        this.PSSV_Fill_Profile_7_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 12
        }, (string[]) null), Right1));
label_282:
        num3 = 307;
        this.PSSV_Fill_Profile_7_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 13
        }, (string[]) null), Right2));
label_283:
        num3 = 308;
        this.PSSV_Fill_Profile_8_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 14
        }, (string[]) null), Right1));
label_284:
        num3 = 309;
        this.PSSV_Fill_Profile_8_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 15
        }, (string[]) null), Right2));
label_285:
        num3 = 310;
        this.PSSV_Fill_Profile_9_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 16
        }, (string[]) null), Right1));
label_286:
        num3 = 311;
        this.PSSV_Fill_Profile_9_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 17
        }, (string[]) null), Right2));
label_287:
        num3 = 312;
        this.PSSV_Fill_Profile_10_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 18
        }, (string[]) null), Right1));
label_288:
        num3 = 313;
        this.PSSV_Fill_Profile_10_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 19
        }, (string[]) null), Right2));
        goto label_373;
label_289:
        num3 = 315;
        if (!Operators.ConditionalCompareObjectEqual(Left6, (object) "7", false))
          goto label_317;
label_290:
        num3 = 316;
        obj2 = (object) "절대 속도 / 램속도 vs. 시간";
label_291:
        num3 = 317;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10614
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_292:
        num3 = 318;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_293:
        num3 = 319;
        objectValue11 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10306
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_294:
        num3 = 320;
        objectValue12 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue11, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_295:
        num3 = 321;
        this.PSSV_Fill_Profile_11_1_Label.Text = "계량위치";
label_296:
        num3 = 322;
        this.PSSV_Fill_Profile_11_2_Label.Text = Conversions.ToString(NewLateBinding.LateIndexGet(objectValue12, new object[1]
        {
          (object) 1
        }, (string[]) null));
label_297:
        num3 = 323;
        this.PSSV_Fill_Profile_1_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null), Right1));
label_298:
        num3 = 324;
        this.PSSV_Fill_Profile_1_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 1
        }, (string[]) null), Right2));
label_299:
        num3 = 325;
        this.PSSV_Fill_Profile_2_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 2
        }, (string[]) null), Right1));
label_300:
        num3 = 326;
        this.PSSV_Fill_Profile_2_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 3
        }, (string[]) null), Right2));
label_301:
        num3 = 327;
        this.PSSV_Fill_Profile_3_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 4
        }, (string[]) null), Right1));
label_302:
        num3 = 328;
        this.PSSV_Fill_Profile_3_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 5
        }, (string[]) null), Right2));
label_303:
        num3 = 329;
        this.PSSV_Fill_Profile_4_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 6
        }, (string[]) null), Right1));
label_304:
        num3 = 330;
        this.PSSV_Fill_Profile_4_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 7
        }, (string[]) null), Right2));
label_305:
        num3 = 331;
        this.PSSV_Fill_Profile_5_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 8
        }, (string[]) null), Right1));
label_306:
        num3 = 332;
        this.PSSV_Fill_Profile_5_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 9
        }, (string[]) null), Right2));
label_307:
        num3 = 333;
        this.PSSV_Fill_Profile_6_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 10
        }, (string[]) null), Right1));
label_308:
        num3 = 334;
        this.PSSV_Fill_Profile_6_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 11
        }, (string[]) null), Right2));
label_309:
        num3 = 335;
        this.PSSV_Fill_Profile_7_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 12
        }, (string[]) null), Right1));
label_310:
        num3 = 336;
        this.PSSV_Fill_Profile_7_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 13
        }, (string[]) null), Right2));
label_311:
        num3 = 337;
        this.PSSV_Fill_Profile_8_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 14
        }, (string[]) null), Right1));
label_312:
        num3 = 338;
        this.PSSV_Fill_Profile_8_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 15
        }, (string[]) null), Right2));
label_313:
        num3 = 339;
        this.PSSV_Fill_Profile_9_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 16
        }, (string[]) null), Right1));
label_314:
        num3 = 340;
        this.PSSV_Fill_Profile_9_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 17
        }, (string[]) null), Right2));
label_315:
        num3 = 341;
        this.PSSV_Fill_Profile_10_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 18
        }, (string[]) null), Right1));
label_316:
        num3 = 342;
        this.PSSV_Fill_Profile_10_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 19
        }, (string[]) null), Right2));
        goto label_373;
label_317:
        num3 = 344;
        if (!Operators.ConditionalCompareObjectEqual(Left6, (object) "8", false))
          goto label_345;
label_318:
        num3 = 345;
        obj2 = (object) "절대 속도 / %램속도 vs. 시간";
label_319:
        num3 = 346;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10616
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_320:
        num3 = 347;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_321:
        num3 = 348;
        objectValue11 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10306
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_322:
        num3 = 349;
        objectValue12 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue11, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_323:
        num3 = 350;
        this.PSSV_Fill_Profile_11_1_Label.Text = "계량위치";
label_324:
        num3 = 351;
        this.PSSV_Fill_Profile_11_2_Label.Text = Conversions.ToString(NewLateBinding.LateIndexGet(objectValue12, new object[1]
        {
          (object) 1
        }, (string[]) null));
label_325:
        num3 = 352;
        this.PSSV_Fill_Profile_1_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null), Right1));
label_326:
        num3 = 353;
        this.PSSV_Fill_Profile_1_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 1
        }, (string[]) null), Right2));
label_327:
        num3 = 354;
        this.PSSV_Fill_Profile_2_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 2
        }, (string[]) null), Right1));
label_328:
        num3 = 355;
        this.PSSV_Fill_Profile_2_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 3
        }, (string[]) null), Right2));
label_329:
        num3 = 356;
        this.PSSV_Fill_Profile_3_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 4
        }, (string[]) null), Right1));
label_330:
        num3 = 357;
        this.PSSV_Fill_Profile_3_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 5
        }, (string[]) null), Right2));
label_331:
        num3 = 358;
        this.PSSV_Fill_Profile_4_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 6
        }, (string[]) null), Right1));
label_332:
        num3 = 359;
        this.PSSV_Fill_Profile_4_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 7
        }, (string[]) null), Right2));
label_333:
        num3 = 360;
        this.PSSV_Fill_Profile_5_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 8
        }, (string[]) null), Right1));
label_334:
        num3 = 361;
        this.PSSV_Fill_Profile_5_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 9
        }, (string[]) null), Right2));
label_335:
        num3 = 362;
        this.PSSV_Fill_Profile_6_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 10
        }, (string[]) null), Right1));
label_336:
        num3 = 363;
        this.PSSV_Fill_Profile_6_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 11
        }, (string[]) null), Right2));
label_337:
        num3 = 364;
        this.PSSV_Fill_Profile_7_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 12
        }, (string[]) null), Right1));
label_338:
        num3 = 365;
        this.PSSV_Fill_Profile_7_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 13
        }, (string[]) null), Right2));
label_339:
        num3 = 366;
        this.PSSV_Fill_Profile_8_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 14
        }, (string[]) null), Right1));
label_340:
        num3 = 367;
        this.PSSV_Fill_Profile_8_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 15
        }, (string[]) null), Right2));
label_341:
        num3 = 368;
        this.PSSV_Fill_Profile_9_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 16
        }, (string[]) null), Right1));
label_342:
        num3 = 369;
        this.PSSV_Fill_Profile_9_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 17
        }, (string[]) null), Right2));
label_343:
        num3 = 370;
        this.PSSV_Fill_Profile_10_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 18
        }, (string[]) null), Right1));
label_344:
        num3 = 371;
        this.PSSV_Fill_Profile_10_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 19
        }, (string[]) null), Right2));
        goto label_373;
label_345:
        num3 = 373;
        if (!Operators.ConditionalCompareObjectEqual(Left6, (object) "9", false))
          goto label_373;
label_346:
        num3 = 374;
        obj2 = (object) "절대 속도 / 유량 vs. 시간";
label_347:
        num3 = 375;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10620
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_348:
        num3 = 376;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_349:
        num3 = 377;
        objectValue11 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10306
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_350:
        num3 = 378;
        objectValue12 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue11, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_351:
        num3 = 379;
        this.PSSV_Fill_Profile_11_1_Label.Text = "계량위치";
label_352:
        num3 = 380;
        this.PSSV_Fill_Profile_11_2_Label.Text = Conversions.ToString(NewLateBinding.LateIndexGet(objectValue12, new object[1]
        {
          (object) 1
        }, (string[]) null));
label_353:
        num3 = 381;
        this.PSSV_Fill_Profile_1_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null), Right1));
label_354:
        num3 = 382;
        this.PSSV_Fill_Profile_1_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 1
        }, (string[]) null), Right2));
label_355:
        num3 = 383;
        this.PSSV_Fill_Profile_2_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 2
        }, (string[]) null), Right1));
label_356:
        num3 = 384;
        this.PSSV_Fill_Profile_2_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 3
        }, (string[]) null), Right2));
label_357:
        num3 = 385;
        this.PSSV_Fill_Profile_3_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 4
        }, (string[]) null), Right1));
label_358:
        num3 = 386;
        this.PSSV_Fill_Profile_3_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 5
        }, (string[]) null), Right2));
label_359:
        num3 = 387;
        this.PSSV_Fill_Profile_4_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 6
        }, (string[]) null), Right1));
label_360:
        num3 = 388;
        this.PSSV_Fill_Profile_4_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 7
        }, (string[]) null), Right2));
label_361:
        num3 = 389;
        this.PSSV_Fill_Profile_5_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 8
        }, (string[]) null), Right1));
label_362:
        num3 = 390;
        this.PSSV_Fill_Profile_5_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 9
        }, (string[]) null), Right2));
label_363:
        num3 = 391;
        this.PSSV_Fill_Profile_6_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 10
        }, (string[]) null), Right1));
label_364:
        num3 = 392;
        this.PSSV_Fill_Profile_6_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 11
        }, (string[]) null), Right2));
label_365:
        num3 = 393;
        this.PSSV_Fill_Profile_7_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 12
        }, (string[]) null), Right1));
label_366:
        num3 = 394;
        this.PSSV_Fill_Profile_7_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 13
        }, (string[]) null), Right2));
label_367:
        num3 = 395;
        this.PSSV_Fill_Profile_8_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 14
        }, (string[]) null), Right1));
label_368:
        num3 = 396;
        this.PSSV_Fill_Profile_8_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 15
        }, (string[]) null), Right2));
label_369:
        num3 = 397;
        this.PSSV_Fill_Profile_9_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 16
        }, (string[]) null), Right1));
label_370:
        num3 = 398;
        this.PSSV_Fill_Profile_9_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 17
        }, (string[]) null), Right2));
label_371:
        num3 = 399;
        this.PSSV_Fill_Profile_10_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 18
        }, (string[]) null), Right1));
label_372:
        num3 = 400;
        this.PSSV_Fill_Profile_10_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 19
        }, (string[]) null), Right2));
label_373:
label_374:
label_375:
        num3 = 403;
        this.PSSV_Fill_Control_Label.Text = Conversions.ToString(obj2);
label_376:
        num3 = 404;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10310
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_377:
        num3 = 405;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_378:
        num3 = 406;
        object obj3 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null));
label_379:
        num3 = 407;
        object Left7 = obj3;
label_380:
        num3 = 410;
        if (!Operators.ConditionalCompareObjectEqual(Left7, (object) "0", false))
          goto label_383;
label_381:
        num3 = 411;
        obj3 = (object) "자동";
label_382:
        num3 = 412;
        this.PSSV_Packing_VP_Label.Text = "-";
        goto label_429;
label_383:
        num3 = 414;
        if (!Operators.ConditionalCompareObjectEqual(Left7, (object) "1", false))
          goto label_389;
label_384:
        num3 = 415;
        obj3 = (object) "%충전부피";
label_385:
        num3 = 416;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10308
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_386:
        num3 = 417;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_387:
        num3 = 418;
        object objectValue15 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null));
label_388:
        num3 = 419;
        this.PSSV_Packing_VP_Label.Text = Conversions.ToString(Operators.ConcatenateObject(objectValue15, (object) " %"));
        goto label_429;
label_389:
        num3 = 421;
        if (!Operators.ConditionalCompareObjectEqual(Left7, (object) "2", false))
          goto label_395;
label_390:
        num3 = 422;
        obj3 = (object) "사출압력";
label_391:
        num3 = 423;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10307
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_392:
        num3 = 424;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_393:
        num3 = 425;
        objectValue15 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null));
label_394:
        num3 = 426;
        this.PSSV_Packing_VP_Label.Text = Conversions.ToString(Operators.ConcatenateObject(objectValue15, (object) " MPa"));
        goto label_429;
label_395:
        num3 = 428;
        if (!Operators.ConditionalCompareObjectEqual(Left7, (object) "3", false))
          goto label_401;
label_396:
        num3 = 429;
        obj3 = (object) "유압압력";
label_397:
        num3 = 430;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10303
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_398:
        num3 = 431;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_399:
        num3 = 432;
        objectValue15 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null));
label_400:
        num3 = 433;
        this.PSSV_Packing_VP_Label.Text = Conversions.ToString(Operators.ConcatenateObject(objectValue15, (object) " MPa"));
        goto label_429;
label_401:
        num3 = 435;
        if (!Operators.ConditionalCompareObjectEqual(Left7, (object) "4", false))
          goto label_407;
label_402:
        num3 = 436;
        obj3 = (object) "형체력";
label_403:
        num3 = 437;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10305
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_404:
        num3 = 438;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_405:
        num3 = 439;
        objectValue15 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null));
label_406:
        num3 = 440;
        this.PSSV_Packing_VP_Label.Text = Conversions.ToString(Operators.ConcatenateObject(objectValue15, (object) " ton"));
        goto label_429;
label_407:
        num3 = 442;
        if (!Operators.ConditionalCompareObjectEqual(Left7, (object) "5", false))
          goto label_414;
label_408:
        num3 = 443;
        obj3 = (object) "지정부위압력";
label_409:
        num3 = 444;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10304
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_410:
        num3 = 445;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_411:
        num3 = 446;
        object objectValue16 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null));
label_412:
        num3 = 447;
        objectValue15 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 1
        }, (string[]) null));
label_413:
        num3 = 448;
        this.PSSV_Packing_VP_Label.Text = Conversions.ToString(Operators.ConcatenateObject(Operators.ConcatenateObject(Operators.ConcatenateObject(objectValue16, (object) " / "), objectValue15), (object) " MPa"));
        goto label_429;
label_414:
        num3 = 450;
        if (!Operators.ConditionalCompareObjectEqual(Left7, (object) "6", false))
          goto label_420;
label_415:
        num3 = 451;
        obj3 = (object) "사출시간";
label_416:
        num3 = 452;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10309
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_417:
        num3 = 453;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_418:
        num3 = 454;
        objectValue15 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null));
label_419:
        num3 = 455;
        this.PSSV_Packing_VP_Label.Text = Conversions.ToString(Operators.ConcatenateObject(objectValue15, (object) " s"));
        goto label_429;
label_420:
        num3 = 457;
        if (!Operators.ConditionalCompareObjectEqual(Left7, (object) "7", false))
          goto label_423;
label_421:
        num3 = 458;
        obj3 = (object) "도달조건";
label_422:
        num3 = 459;
        this.PSSV_Packing_VP_Label.Text = "지원안됨";
        goto label_429;
label_423:
        num3 = 461;
        if (!Operators.ConditionalCompareObjectEqual(Left7, (object) "8", false))
          goto label_429;
label_424:
        num3 = 462;
        obj3 = (object) "램 위치";
label_425:
        num3 = 463;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10313
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_426:
        num3 = 464;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_427:
        num3 = 465;
        objectValue15 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null));
label_428:
        num3 = 466;
        this.PSSV_Packing_VP_Label.Text = Conversions.ToString(Operators.ConcatenateObject(objectValue15, (object) " mm"));
label_429:
label_430:
        num3 = 468;
        this.PSSV_Packing_VP_Control_Label.Text = Conversions.ToString(obj3);
label_431:
        num3 = 469;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10704
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_432:
        num3 = 470;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_433:
        num3 = 471;
        object objectValue17 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null));
label_434:
        num3 = 472;
        object Right3 = (object) "";
label_435:
        num3 = 473;
        object obj4 = (object) "";
label_436:
        num3 = 474;
        object Left8 = objectValue17;
label_437:
        num3 = 477;
        if (!Operators.ConditionalCompareObjectEqual(Left8, (object) "4", false))
          goto label_443;
label_438:
        num3 = 478;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10702
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_439:
        num3 = 479;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_440:
        num3 = 480;
        obj4 = (object) "%충전부피 vs. 시간";
label_441:
        num3 = 481;
        object objectValue18 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "Size", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_442:
        num3 = 482;
        Right3 = (object) "%";
        goto label_461;
label_443:
        num3 = 484;
        if (!Operators.ConditionalCompareObjectEqual(Left8, (object) "2", false))
          goto label_449;
label_444:
        num3 = 485;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10707
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_445:
        num3 = 486;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_446:
        num3 = 487;
        obj4 = (object) "절대압력 vs. 시간";
label_447:
        num3 = 488;
        objectValue18 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "Size", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_448:
        num3 = 489;
        Right3 = (object) "MPa";
        goto label_461;
label_449:
        num3 = 491;
        if (!Operators.ConditionalCompareObjectEqual(Left8, (object) "1", false))
          goto label_455;
label_450:
        num3 = 492;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10706
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_451:
        num3 = 493;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_452:
        num3 = 494;
        obj4 = (object) "유압압력 vs. 시간";
label_453:
        num3 = 495;
        objectValue18 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "Size", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_454:
        num3 = 496;
        Right3 = (object) "MPa";
        goto label_461;
label_455:
        num3 = 498;
        if (!Operators.ConditionalCompareObjectEqual(Left8, (object) "3", false))
          goto label_461;
label_456:
        num3 = 499;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10705
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_457:
        num3 = 500;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_458:
        num3 = 501;
        obj4 = (object) "%최대사출기압력 vs 시간";
label_459:
        num3 = 502;
        objectValue18 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "Size", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_460:
        num3 = 503;
        Right3 = (object) "MPa";
label_461:
label_462:
        num3 = 505;
        this.PSSV_Packing_Control_Label.Text = Conversions.ToString(obj4);
label_463:
        num3 = 506;
        this.PSSV_Packing_Profile_1_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null), (object) "s"));
label_464:
        num3 = 507;
        this.PSSV_Packing_Profile_1_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 1
        }, (string[]) null), Right3));
label_465:
        num3 = 508;
        this.PSSV_Packing_Profile_2_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 2
        }, (string[]) null), (object) "s"));
label_466:
        num3 = 509;
        this.PSSV_Packing_Profile_2_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 3
        }, (string[]) null), Right3));
label_467:
        num3 = 510;
        this.PSSV_Packing_Profile_3_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 4
        }, (string[]) null), (object) "s"));
label_468:
        num3 = 511;
        this.PSSV_Packing_Profile_3_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 5
        }, (string[]) null), Right3));
label_469:
        num3 = 512;
        this.PSSV_Packing_Profile_4_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 6
        }, (string[]) null), (object) "s"));
label_470:
        num3 = 513;
        this.PSSV_Packing_Profile_4_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 7
        }, (string[]) null), Right3));
label_471:
        num3 = 514;
        this.PSSV_Packing_Profile_5_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 8
        }, (string[]) null), (object) "s"));
label_472:
        num3 = 515;
        this.PSSV_Packing_Profile_5_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 9
        }, (string[]) null), Right3));
label_473:
        num3 = 516;
        this.PSSV_Packing_Profile_6_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 10
        }, (string[]) null), (object) "s"));
label_474:
        num3 = 517;
        this.PSSV_Packing_Profile_6_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 11
        }, (string[]) null), Right3));
label_475:
        num3 = 518;
        this.PSSV_Packing_Profile_7_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 12
        }, (string[]) null), (object) "s"));
label_476:
        num3 = 519;
        this.PSSV_Packing_Profile_7_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 13
        }, (string[]) null), Right3));
label_477:
        num3 = 520;
        this.PSSV_Packing_Profile_8_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 14
        }, (string[]) null), (object) "s"));
label_478:
        num3 = 521;
        this.PSSV_Packing_Profile_8_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 15
        }, (string[]) null), Right3));
label_479:
        num3 = 522;
        this.PSSV_Packing_Profile_9_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 16
        }, (string[]) null), (object) "s"));
label_480:
        num3 = 523;
        this.PSSV_Packing_Profile_9_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 17
        }, (string[]) null), Right3));
label_481:
        num3 = 524;
        this.PSSV_Packing_Profile_10_1_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 18
        }, (string[]) null), (object) "s"));
label_482:
        num3 = 525;
        this.PSSV_Packing_Profile_10_2_Label.Text = Conversions.ToString(Operators.ConcatenateObject(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 19
        }, (string[]) null), Right3));
label_483:
        num3 = 526;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 11109
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_484:
        num3 = 527;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_485:
        num3 = 528;
        object objectValue19 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 0
        }, (string[]) null));
label_486:
        num3 = 529;
        object Left9 = objectValue19;
label_487:
        num3 = 532;
        if (!Operators.ConditionalCompareObjectEqual(Left9, (object) "1", false))
          goto label_492;
label_488:
        num3 = 533;
        this.PSSV_Cooling_Control_Label.Text = "지정시간";
label_489:
        num3 = 534;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 10102
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_490:
        num3 = 535;
        object objectValue20 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "Val", new object[1]
        {
          (object) 0
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_491:
        num3 = 536;
        this.PSSV_Cooling_Time_Label.Text = Conversions.ToString(objectValue20);
        goto label_495;
label_492:
        num3 = 538;
        if (!Operators.ConditionalCompareObjectEqual(Left9, (object) "2", false))
          goto label_495;
label_493:
        num3 = 539;
        this.PSSV_Cooling_Control_Label.Text = "자동";
label_494:
        num3 = 540;
        this.PSSV_Cooling_Time_Label.Text = "-";
label_495:
label_496:
        num3 = 542;
        objectValue6 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue2, (System.Type) null, "PropertyEditor", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_497:
        num3 = 543;
        objectValue7 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue6, (System.Type) null, "GetFirstProperty", new object[1]
        {
          (object) 40000
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_498:
        num3 = 544;
        objectValue8 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue7, (System.Type) null, "FieldValues", new object[1]
        {
          (object) 20020
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_499:
        num3 = 545;
        objectValue9 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue8, (System.Type) null, "ToVBSArray", new object[0], (string[]) null, (System.Type[]) null, (bool[]) null));
label_500:
        num3 = 546;
        object objectValue21 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateIndexGet(objectValue9, new object[1]
        {
          (object) 1
        }, (string[]) null));
label_501:
        num3 = 547;
        object Instance = objectValue6;
        object[] objArray = new object[2]
        {
          (object) 21000,
          RuntimeHelpers.GetObjectValue(objectValue21)
        };
        object[] Arguments = objArray;
        bool[] flagArray = new bool[2]{ false, true };
        bool[] CopyBack = flagArray;
        object obj5 = NewLateBinding.LateGet(Instance, (System.Type) null, "FindProperty", Arguments, (string[]) null, (System.Type[]) null, CopyBack);
        if (flagArray[1])
          objectValue21 = RuntimeHelpers.GetObjectValue(objArray[1]);
        object objectValue22 = RuntimeHelpers.GetObjectValue(obj5);
label_504:
        num3 = 548;
        object objectValue23 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue22, (System.Type) null, "FieldDescription", new object[1]
        {
          (object) 1997
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_505:
        num3 = 549;
        this.PSSV_Material_Manufacturer_Label.Text = Conversions.ToString(objectValue23);
label_506:
        num3 = 550;
        object objectValue24 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue22, (System.Type) null, "FieldDescription", new object[1]
        {
          (object) 1998
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_507:
        num3 = 551;
        this.PSSV_Material_Name_Label.Text = Conversions.ToString(objectValue24);
label_508:
        num3 = 552;
        object objectValue25 = RuntimeHelpers.GetObjectValue(NewLateBinding.LateGet(objectValue22, (System.Type) null, "FieldDescription", new object[1]
        {
          (object) 1999
        }, (string[]) null, (System.Type[]) null, (bool[]) null));
label_509:
        num3 = 553;
        this.PSSV_Material_Family_Label.Text = Conversions.ToString(objectValue25);
label_510:
label_511:
        num3 = 554;
        if (0 == Information.Err().Number)
          goto label_514;
label_512:
        num3 = 555;
        ProjectData.ClearProjectError();
        if (num2 == 0)
          throw ProjectData.CreateProjectError(-2146828268);
        goto label_515;
label_514:
        goto label_521;
label_515:
        int num4 = num2 + 1;
        num2 = 0;
        switch (num4)
        {
          case 1:
            goto label_1;
          case 2:
            goto label_2;
          case 3:
            goto label_3;
          case 4:
            goto label_4;
          case 5:
            goto label_5;
          case 6:
          case 9:
            goto label_8;
          case 7:
            goto label_6;
          case 8:
            goto label_7;
          case 10:
            goto label_9;
          case 11:
            goto label_10;
          case 12:
            goto label_11;
          case 13:
            goto label_12;
          case 14:
            goto label_13;
          case 15:
            goto label_14;
          case 16:
            goto label_15;
          case 17:
            goto label_16;
          case 18:
            goto label_17;
          case 19:
            goto label_18;
          case 20:
            goto label_19;
          case 21:
            goto label_20;
          case 22:
            goto label_21;
          case 23:
            goto label_22;
          case 24:
            goto label_23;
          case 25:
            goto label_24;
          case 26:
            goto label_25;
          case 27:
            goto label_26;
          case 28:
            goto label_27;
          case 29:
            goto label_28;
          case 30:
            goto label_29;
          case 31:
            goto label_30;
          case 32:
            goto label_31;
          case 33:
            goto label_32;
          case 34:
            goto label_33;
          case 35:
            goto label_34;
          case 36:
            goto label_35;
          case 37:
            goto label_36;
          case 38:
            goto label_37;
          case 39:
            goto label_38;
          case 40:
            goto label_39;
          case 41:
            goto label_40;
          case 42:
            goto label_41;
          case 43:
            goto label_42;
          case 44:
            goto label_43;
          case 45:
            goto label_44;
          case 46:
            goto label_45;
          case 47:
            goto label_46;
          case 48:
            goto label_47;
          case 49:
            goto label_48;
          case 50:
            goto label_49;
          case 51:
            goto label_50;
          case 52:
            goto label_51;
          case 53:
            goto label_52;
          case 54:
            goto label_53;
          case 55:
            goto label_54;
          case 56:
            goto label_55;
          case 57:
            goto label_56;
          case 58:
            goto label_57;
          case 59:
            goto label_58;
          case 60:
            goto label_59;
          case 61:
            goto label_60;
          case 62:
          case 65:
            goto label_63;
          case 63:
            goto label_61;
          case 64:
            goto label_62;
          case 66:
            goto label_64;
          case 67:
            goto label_65;
          case 68:
            goto label_66;
          case 69:
            goto label_67;
          case 70:
          case 74:
          case 78:
          case 82:
            goto label_77;
          case 71:
            goto label_68;
          case 72:
            goto label_69;
          case 73:
            goto label_70;
          case 75:
            goto label_71;
          case 76:
            goto label_72;
          case 77:
            goto label_73;
          case 79:
            goto label_74;
          case 80:
            goto label_75;
          case 81:
            goto label_76;
          case 83:
            goto label_78;
          case 84:
            goto label_79;
          case 85:
            goto label_80;
          case 86:
            goto label_81;
          case 87:
            goto label_82;
          case 88:
            goto label_83;
          case 89:
            goto label_84;
          case 90:
            goto label_85;
          case 91:
            goto label_86;
          case 92:
            goto label_87;
          case 93:
            goto label_88;
          case 94:
            goto label_89;
          case 95:
            goto label_90;
          case 96:
            goto label_91;
          case 97:
          case 103:
          case 110:
          case 117:
          case 122:
          case 218:
          case 402:
            goto label_374;
          case 98:
          case 99:
            goto label_92;
          case 100:
            goto label_93;
          case 101:
            goto label_94;
          case 102:
            goto label_95;
          case 104:
            goto label_96;
          case 105:
            goto label_97;
          case 106:
            goto label_98;
          case 107:
            goto label_99;
          case 108:
            goto label_100;
          case 109:
            goto label_101;
          case 111:
            goto label_102;
          case 112:
            goto label_103;
          case 113:
            goto label_104;
          case 114:
            goto label_105;
          case 115:
            goto label_106;
          case 116:
            goto label_107;
          case 118:
            goto label_108;
          case 119:
            goto label_109;
          case 120:
            goto label_110;
          case 121:
            goto label_111;
          case 123:
            goto label_112;
          case 124:
            goto label_113;
          case 125:
            goto label_114;
          case 126:
            goto label_115;
          case (int) sbyte.MaxValue:
            goto label_116;
          case 128:
            goto label_117;
          case 129:
            goto label_118;
          case 130:
            goto label_119;
          case 131:
            goto label_120;
          case 132:
          case 175:
          case 217:
            goto label_197;
          case 133:
          case 134:
            goto label_121;
          case 135:
            goto label_122;
          case 136:
            goto label_123;
          case 137:
            goto label_124;
          case 138:
            goto label_125;
          case 139:
            goto label_126;
          case 140:
            goto label_127;
          case 141:
          case 148:
          case 154:
            goto label_138;
          case 142:
          case 143:
            goto label_128;
          case 144:
            goto label_129;
          case 145:
            goto label_130;
          case 146:
            goto label_131;
          case 147:
            goto label_132;
          case 149:
            goto label_133;
          case 150:
            goto label_134;
          case 151:
            goto label_135;
          case 152:
            goto label_136;
          case 153:
            goto label_137;
          case 155:
            goto label_139;
          case 156:
            goto label_140;
          case 157:
            goto label_141;
          case 158:
            goto label_142;
          case 159:
            goto label_143;
          case 160:
            goto label_144;
          case 161:
            goto label_145;
          case 162:
            goto label_146;
          case 163:
            goto label_147;
          case 164:
            goto label_148;
          case 165:
            goto label_149;
          case 166:
            goto label_150;
          case 167:
            goto label_151;
          case 168:
            goto label_152;
          case 169:
            goto label_153;
          case 170:
            goto label_154;
          case 171:
            goto label_155;
          case 172:
            goto label_156;
          case 173:
            goto label_157;
          case 174:
            goto label_158;
          case 176:
            goto label_159;
          case 177:
            goto label_160;
          case 178:
            goto label_161;
          case 179:
            goto label_162;
          case 180:
            goto label_163;
          case 181:
            goto label_164;
          case 182:
            goto label_165;
          case 183:
          case 190:
          case 196:
            goto label_176;
          case 184:
          case 185:
            goto label_166;
          case 186:
            goto label_167;
          case 187:
            goto label_168;
          case 188:
            goto label_169;
          case 189:
            goto label_170;
          case 191:
            goto label_171;
          case 192:
            goto label_172;
          case 193:
            goto label_173;
          case 194:
            goto label_174;
          case 195:
            goto label_175;
          case 197:
            goto label_177;
          case 198:
            goto label_178;
          case 199:
            goto label_179;
          case 200:
            goto label_180;
          case 201:
            goto label_181;
          case 202:
            goto label_182;
          case 203:
            goto label_183;
          case 204:
            goto label_184;
          case 205:
            goto label_185;
          case 206:
            goto label_186;
          case 207:
            goto label_187;
          case 208:
            goto label_188;
          case 209:
            goto label_189;
          case 210:
            goto label_190;
          case 211:
            goto label_191;
          case 212:
            goto label_192;
          case 213:
            goto label_193;
          case 214:
            goto label_194;
          case 215:
            goto label_195;
          case 216:
            goto label_196;
          case 219:
            goto label_198;
          case 220:
            goto label_199;
          case 221:
            goto label_200;
          case 222:
            goto label_201;
          case 223:
            goto label_202;
          case 224:
            goto label_203;
          case 225:
            goto label_204;
          case 226:
          case 256:
          case 285:
          case 314:
          case 343:
          case 372:
          case 401:
            goto label_373;
          case 227:
          case 228:
            goto label_205;
          case 229:
            goto label_206;
          case 230:
            goto label_207;
          case 231:
            goto label_208;
          case 232:
            goto label_209;
          case 233:
            goto label_210;
          case 234:
            goto label_211;
          case 235:
            goto label_212;
          case 236:
            goto label_213;
          case 237:
            goto label_214;
          case 238:
            goto label_215;
          case 239:
            goto label_216;
          case 240:
            goto label_217;
          case 241:
            goto label_218;
          case 242:
            goto label_219;
          case 243:
            goto label_220;
          case 244:
            goto label_221;
          case 245:
            goto label_222;
          case 246:
            goto label_223;
          case 247:
            goto label_224;
          case 248:
            goto label_225;
          case 249:
            goto label_226;
          case 250:
            goto label_227;
          case 251:
            goto label_228;
          case 252:
            goto label_229;
          case 253:
            goto label_230;
          case 254:
            goto label_231;
          case (int) byte.MaxValue:
            goto label_232;
          case 257:
            goto label_233;
          case 258:
            goto label_234;
          case 259:
            goto label_235;
          case 260:
            goto label_236;
          case 261:
            goto label_237;
          case 262:
            goto label_238;
          case 263:
            goto label_239;
          case 264:
            goto label_240;
          case 265:
            goto label_241;
          case 266:
            goto label_242;
          case 267:
            goto label_243;
          case 268:
            goto label_244;
          case 269:
            goto label_245;
          case 270:
            goto label_246;
          case 271:
            goto label_247;
          case 272:
            goto label_248;
          case 273:
            goto label_249;
          case 274:
            goto label_250;
          case 275:
            goto label_251;
          case 276:
            goto label_252;
          case 277:
            goto label_253;
          case 278:
            goto label_254;
          case 279:
            goto label_255;
          case 280:
            goto label_256;
          case 281:
            goto label_257;
          case 282:
            goto label_258;
          case 283:
            goto label_259;
          case 284:
            goto label_260;
          case 286:
            goto label_261;
          case 287:
            goto label_262;
          case 288:
            goto label_263;
          case 289:
            goto label_264;
          case 290:
            goto label_265;
          case 291:
            goto label_266;
          case 292:
            goto label_267;
          case 293:
            goto label_268;
          case 294:
            goto label_269;
          case 295:
            goto label_270;
          case 296:
            goto label_271;
          case 297:
            goto label_272;
          case 298:
            goto label_273;
          case 299:
            goto label_274;
          case 300:
            goto label_275;
          case 301:
            goto label_276;
          case 302:
            goto label_277;
          case 303:
            goto label_278;
          case 304:
            goto label_279;
          case 305:
            goto label_280;
          case 306:
            goto label_281;
          case 307:
            goto label_282;
          case 308:
            goto label_283;
          case 309:
            goto label_284;
          case 310:
            goto label_285;
          case 311:
            goto label_286;
          case 312:
            goto label_287;
          case 313:
            goto label_288;
          case 315:
            goto label_289;
          case 316:
            goto label_290;
          case 317:
            goto label_291;
          case 318:
            goto label_292;
          case 319:
            goto label_293;
          case 320:
            goto label_294;
          case 321:
            goto label_295;
          case 322:
            goto label_296;
          case 323:
            goto label_297;
          case 324:
            goto label_298;
          case 325:
            goto label_299;
          case 326:
            goto label_300;
          case 327:
            goto label_301;
          case 328:
            goto label_302;
          case 329:
            goto label_303;
          case 330:
            goto label_304;
          case 331:
            goto label_305;
          case 332:
            goto label_306;
          case 333:
            goto label_307;
          case 334:
            goto label_308;
          case 335:
            goto label_309;
          case 336:
            goto label_310;
          case 337:
            goto label_311;
          case 338:
            goto label_312;
          case 339:
            goto label_313;
          case 340:
            goto label_314;
          case 341:
            goto label_315;
          case 342:
            goto label_316;
          case 344:
            goto label_317;
          case 345:
            goto label_318;
          case 346:
            goto label_319;
          case 347:
            goto label_320;
          case 348:
            goto label_321;
          case 349:
            goto label_322;
          case 350:
            goto label_323;
          case 351:
            goto label_324;
          case 352:
            goto label_325;
          case 353:
            goto label_326;
          case 354:
            goto label_327;
          case 355:
            goto label_328;
          case 356:
            goto label_329;
          case 357:
            goto label_330;
          case 358:
            goto label_331;
          case 359:
            goto label_332;
          case 360:
            goto label_333;
          case 361:
            goto label_334;
          case 362:
            goto label_335;
          case 363:
            goto label_336;
          case 364:
            goto label_337;
          case 365:
            goto label_338;
          case 366:
            goto label_339;
          case 367:
            goto label_340;
          case 368:
            goto label_341;
          case 369:
            goto label_342;
          case 370:
            goto label_343;
          case 371:
            goto label_344;
          case 373:
            goto label_345;
          case 374:
            goto label_346;
          case 375:
            goto label_347;
          case 376:
            goto label_348;
          case 377:
            goto label_349;
          case 378:
            goto label_350;
          case 379:
            goto label_351;
          case 380:
            goto label_352;
          case 381:
            goto label_353;
          case 382:
            goto label_354;
          case 383:
            goto label_355;
          case 384:
            goto label_356;
          case 385:
            goto label_357;
          case 386:
            goto label_358;
          case 387:
            goto label_359;
          case 388:
            goto label_360;
          case 389:
            goto label_361;
          case 390:
            goto label_362;
          case 391:
            goto label_363;
          case 392:
            goto label_364;
          case 393:
            goto label_365;
          case 394:
            goto label_366;
          case 395:
            goto label_367;
          case 396:
            goto label_368;
          case 397:
            goto label_369;
          case 398:
            goto label_370;
          case 399:
            goto label_371;
          case 400:
            goto label_372;
          case 403:
            goto label_375;
          case 404:
            goto label_376;
          case 405:
            goto label_377;
          case 406:
            goto label_378;
          case 407:
            goto label_379;
          case 408:
          case 413:
          case 420:
          case 427:
          case 434:
          case 441:
          case 449:
          case 456:
          case 460:
          case 467:
            goto label_429;
          case 409:
          case 410:
            goto label_380;
          case 411:
            goto label_381;
          case 412:
            goto label_382;
          case 414:
            goto label_383;
          case 415:
            goto label_384;
          case 416:
            goto label_385;
          case 417:
            goto label_386;
          case 418:
            goto label_387;
          case 419:
            goto label_388;
          case 421:
            goto label_389;
          case 422:
            goto label_390;
          case 423:
            goto label_391;
          case 424:
            goto label_392;
          case 425:
            goto label_393;
          case 426:
            goto label_394;
          case 428:
            goto label_395;
          case 429:
            goto label_396;
          case 430:
            goto label_397;
          case 431:
            goto label_398;
          case 432:
            goto label_399;
          case 433:
            goto label_400;
          case 435:
            goto label_401;
          case 436:
            goto label_402;
          case 437:
            goto label_403;
          case 438:
            goto label_404;
          case 439:
            goto label_405;
          case 440:
            goto label_406;
          case 442:
            goto label_407;
          case 443:
            goto label_408;
          case 444:
            goto label_409;
          case 445:
            goto label_410;
          case 446:
            goto label_411;
          case 447:
            goto label_412;
          case 448:
            goto label_413;
          case 450:
            goto label_414;
          case 451:
            goto label_415;
          case 452:
            goto label_416;
          case 453:
            goto label_417;
          case 454:
            goto label_418;
          case 455:
            goto label_419;
          case 457:
            goto label_420;
          case 458:
            goto label_421;
          case 459:
            goto label_422;
          case 461:
            goto label_423;
          case 462:
            goto label_424;
          case 463:
            goto label_425;
          case 464:
            goto label_426;
          case 465:
            goto label_427;
          case 466:
            goto label_428;
          case 468:
            goto label_430;
          case 469:
            goto label_431;
          case 470:
            goto label_432;
          case 471:
            goto label_433;
          case 472:
            goto label_434;
          case 473:
            goto label_435;
          case 474:
            goto label_436;
          case 475:
          case 483:
          case 490:
          case 497:
          case 504:
            goto label_461;
          case 476:
          case 477:
            goto label_437;
          case 478:
            goto label_438;
          case 479:
            goto label_439;
          case 480:
            goto label_440;
          case 481:
            goto label_441;
          case 482:
            goto label_442;
          case 484:
            goto label_443;
          case 485:
            goto label_444;
          case 486:
            goto label_445;
          case 487:
            goto label_446;
          case 488:
            goto label_447;
          case 489:
            goto label_448;
          case 491:
            goto label_449;
          case 492:
            goto label_450;
          case 493:
            goto label_451;
          case 494:
            goto label_452;
          case 495:
            goto label_453;
          case 496:
            goto label_454;
          case 498:
            goto label_455;
          case 499:
            goto label_456;
          case 500:
            goto label_457;
          case 501:
            goto label_458;
          case 502:
            goto label_459;
          case 503:
            goto label_460;
          case 505:
            goto label_462;
          case 506:
            goto label_463;
          case 507:
            goto label_464;
          case 508:
            goto label_465;
          case 509:
            goto label_466;
          case 510:
            goto label_467;
          case 511:
            goto label_468;
          case 512:
            goto label_469;
          case 513:
            goto label_470;
          case 514:
            goto label_471;
          case 515:
            goto label_472;
          case 516:
            goto label_473;
          case 517:
            goto label_474;
          case 518:
            goto label_475;
          case 519:
            goto label_476;
          case 520:
            goto label_477;
          case 521:
            goto label_478;
          case 522:
            goto label_479;
          case 523:
            goto label_480;
          case 524:
            goto label_481;
          case 525:
            goto label_482;
          case 526:
            goto label_483;
          case 527:
            goto label_484;
          case 528:
            goto label_485;
          case 529:
            goto label_486;
          case 530:
          case 537:
          case 541:
            goto label_495;
          case 531:
          case 532:
            goto label_487;
          case 533:
            goto label_488;
          case 534:
            goto label_489;
          case 535:
            goto label_490;
          case 536:
            goto label_491;
          case 538:
            goto label_492;
          case 539:
            goto label_493;
          case 540:
            goto label_494;
          case 542:
            goto label_496;
          case 543:
            goto label_497;
          case 544:
            goto label_498;
          case 545:
            goto label_499;
          case 546:
            goto label_500;
          case 547:
            goto label_501;
          case 548:
            goto label_504;
          case 549:
            goto label_505;
          case 550:
            goto label_506;
          case 551:
            goto label_507;
          case 552:
            goto label_508;
          case 553:
            goto label_509;
          case 554:
            goto label_511;
          case 555:
            goto label_512;
          case 556:
          case 557:
            goto label_514;
          case 558:
            goto label_521;
          default:
            goto label_520;
        }
label_516:
        num2 = num3;
        switch (num1 > -2 ? num1 : 1)
        {
          case 1:
            goto label_515;
          case 2:
            goto label_510;
        }
      }
      catch (Exception ex) when (ex is Exception & num1 != 0 & num2 == 0)
      {
        ProjectData.SetProjectError(ex);
        goto label_516;
      }
label_520:
      throw ProjectData.CreateProjectError(-2146828237);
label_521:
      if (num2 == 0)
        return;
      ProjectData.ClearProjectError();
    }
  }
}
