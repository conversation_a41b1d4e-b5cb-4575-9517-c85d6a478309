﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.clsLicense
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDLog4Net;
using LSK;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.NetworkInformation;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public class clsLicense
  {
    private static Form m_ParentForm = (Form) null;
    private static Thread m_NetworkLicenseThread = (Thread) null;

    public static bool CheckLicenseType()
    {
      bool flag = false;
      if (!clsDefine.g_fiNetworkLicenseCfg.Exists)
        return flag;
      try
      {
        flag = clsUtill.ConvertToBoolean(clsUtill.ReadINI("Network", "Use", clsDefine.g_fiNetworkLicenseCfg.FullName));
        if (flag)
        {
          clsDefine.g_strWebURL = clsUtill.ReadINI("Network", "URL", clsDefine.g_fiNetworkLicenseCfg.FullName);
          string p_strValue = clsUtill.ReadINI("Network", "Time", clsDefine.g_fiNetworkLicenseCfg.FullName);
          if (!string.IsNullOrEmpty(p_strValue))
          {
            int num = clsUtill.ConvertToInt(p_strValue);
            if (num != 0)
              clsDefine.g_iCheckTime = num;
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsLicense]CheckLicenseType):" + ex.Message));
      }
      return flag;
    }

    public static void CheckLicense()
    {
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      bool flag1 = false;
      List<string> stringList = new List<string>();
      try
      {
        DirectoryInfo directoryInfo = new DirectoryInfo("C:\\dcam\\config\\HDPass");
        if (!directoryInfo.Exists)
          directoryInfo.Create();
        bool flag2 = LicenseCls.CheckDemo("HDMoldFlow");
        clsDefine.g_dicEnabledVersion.Clear();
        if (!flag2)
        {
          FileInfo[] array = ((IEnumerable<FileInfo>) directoryInfo.GetFiles()).Where<FileInfo>((Func<FileInfo, bool>) (Temp => Temp.Name.Contains("HDMoldFlow"))).ToArray<FileInfo>();
          string pattern = "HDMoldFlow(?<Version>\\d*)(\\[(?<Level>\\D*)\\])?";
          for (int index = 0; index < array.Length; ++index)
          {
            string str1 = "HDMoldFlow";
            Match match = new Regex(pattern).Match(array[index].Name);
            string key = match.Groups["Version"].ToString();
            string str2 = match.Groups["Level"].ToString();
            string ProdName;
            if (string.IsNullOrEmpty(key))
            {
              key = "2021";
              ProdName = str1 + "[" + str2 + "]";
            }
            else
              ProdName = str1 + key + "[" + str2 + "]";
            if (!(key == "2021") || System.Enum.IsDefined(typeof (clsDefine.LicLevel), (object) str2))
            {
              ResultCls resultCls = LicenseCls.LicenseRun(ProdName, bMessageBoxShow: false);
              switch (resultCls.Checkint)
              {
                case 0:
                  flag2 = true;
                  break;
                case 1:
                  flag1 = true;
                  break;
                default:
                  int num = (int) MessageBox.Show(key + "Version: " + LocaleControl.getInstance().GetString("IDS_License_" + (object) resultCls.Checkint), "HDMoldFlow", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                  break;
              }
              if (flag1 && index < array.Length - 1)
                flag1 = false;
              else if (flag2)
              {
                clsDefine.enumLicLevel = !(key == "2021") ? clsDefine.LicLevel.Ultimate : (clsDefine.LicLevel) System.Enum.Parse(typeof (clsDefine.LicLevel), str2);
                if (!clsDefine.g_dicEnabledVersion.ContainsKey(key))
                  clsDefine.g_dicEnabledVersion.Add(key, clsDefine.enumLicLevel);
                else if (clsDefine.enumLicLevel > clsDefine.g_dicEnabledVersion[key])
                  clsDefine.g_dicEnabledVersion[key] = clsDefine.enumLicLevel;
                flag2 = false;
              }
            }
          }
        }
        else
        {
          clsDefine.enumLicLevel = clsDefine.LicLevel.Ultimate;
          clsDefine.g_dicEnabledVersion.Add("Demo", clsDefine.enumLicLevel);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsLicense]CheckLicense):" + ex.Message));
      }
    }

    public static void CheckNetworkLience(Form p_parentForm)
    {
      clsLicense.m_ParentForm = p_parentForm;
      string networkLicenseUrl = clsLicense.GetNetworkLicenseURL();
      clsDefine.enumLicLevel = clsDefine.LicLevel.Ultimate;
      clsLicense.m_NetworkLicenseThread = new Thread(new ParameterizedThreadStart(clsLicense.NetworkLicenseThread_Dowork));
      clsLicense.m_NetworkLicenseThread.Start((object) networkLicenseUrl);
    }

    public static void ExitNetworkLicense()
    {
      if (clsLicense.m_NetworkLicenseThread == null)
        return;
      clsLicense.m_NetworkLicenseThread.Abort();
      clsLicense.SendToWeb(clsLicense.GetNetworkLicenseURL(), true);
    }

    private static void NetworkLicenseThread_Dowork(object p_objURL)
    {
      string str = string.Empty;
      while (clsLicense.m_NetworkLicenseThread.IsAlive)
      {
        string web = clsLicense.SendToWeb(p_objURL.ToString());
        try
        {
          if (clsLicense.m_ParentForm != null && !clsLicense.m_ParentForm.IsDisposed)
          {
            if (!web.ToLower().Equals("success"))
            {
              frmNetworkLicense FormNetworkLicense = new frmNetworkLicense();
              FormNetworkLicense.m_strErrorLog = web;
              int num;
              clsLicense.m_ParentForm.InvokeIFNeeded((Action) (() => num = (int) FormNetworkLicense.ShowDialog((IWin32Window) clsLicense.m_ParentForm)));
              if (!FormNetworkLicense.m_isRetry)
                break;
              continue;
            }
          }
          else
            break;
        }
        catch (Exception ex)
        {
          str = "Exception: " + ex.Message;
          break;
        }
        Thread.Sleep(clsDefine.g_iCheckTime);
      }
      clsLicense.SendToWeb(p_objURL.ToString(), true);
      Process.GetCurrentProcess().Kill();
    }

    private static string GetNetworkLicenseURL()
    {
      string empty = string.Empty;
      StringBuilder stringBuilder = new StringBuilder();
      string str = ((IEnumerable<NetworkInterface>) NetworkInterface.GetAllNetworkInterfaces()).Where<NetworkInterface>((Func<NetworkInterface, bool>) (Temp => Temp.OperationalStatus == OperationalStatus.Up)).Select<NetworkInterface, string>((Func<NetworkInterface, string>) (Temp => Temp.GetPhysicalAddress().ToString())).FirstOrDefault<string>();
      stringBuilder.Append("http://");
      stringBuilder.Append(clsDefine.g_strWebURL);
      stringBuilder.Append("/api/v1/licenseCheck?");
      stringBuilder.Append("co_cd=");
      stringBuilder.Append(clsDefine.g_enumCompany.ToString());
      stringBuilder.Append("&");
      stringBuilder.Append("co_mac=");
      stringBuilder.Append(str);
      return stringBuilder.ToString();
    }

    private static string SendToWeb(string p_strWebURL, bool p_isExit = false)
    {
      string web = string.Empty;
      try
      {
        if (p_isExit)
          p_strWebURL += "&status=false";
        WebRequest webRequest = WebRequest.Create(p_strWebURL);
        webRequest.Method = "Get";
        webRequest.Timeout = 5000;
        using (WebResponse response = webRequest.GetResponse())
        {
          if (((HttpWebResponse) response).StatusCode != HttpStatusCode.OK)
          {
            web = "Rest-Response:X";
          }
          else
          {
            using (Stream responseStream = response.GetResponseStream())
            {
              using (StreamReader streamReader = new StreamReader(responseStream))
              {
                JValue jvalue = (JValue) JObject.Parse(streamReader.ReadToEnd())["message"];
                web = jvalue != null ? jvalue.ToString() : "Rest-Jsondata:X";
              }
            }
          }
        }
      }
      catch (Exception ex)
      {
        web = "Exception Error:" + ex.Message;
      }
      return web;
    }

    public static bool CheckAILicense()
    {
      bool flag = false;
      try
      {
        DirectoryInfo directoryInfo = new DirectoryInfo("C:\\dcam\\config\\HDPass");
        if (!directoryInfo.Exists)
          directoryInfo.Create();
        flag = LicenseCls.LicenseRun("HDMoldFlow[AI]", bMessageBoxShow: false).bLicenseResults;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsLicense]CheckAILicense):" + ex.Message));
      }
      return flag;
    }
  }
}
