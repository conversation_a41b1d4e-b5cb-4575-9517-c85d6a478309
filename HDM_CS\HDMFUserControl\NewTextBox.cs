﻿// Decompiled with JetBrains decompiler
// Type: HDMFUserControl.NewTextBox
// Assembly: HDMFUserControl, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 4651D75A-87CE-415F-80A5-EBC4E2EC2106
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFUserControl.dll

using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace HDMFUserControl
{
  public class NewTextBox : UserControl
  {
    private bool m_Digit;
    private IContainer components;
    private TextBox textBox_Main;

    public NewTextBox()
    {
      this.InitializeComponent();
      this.textBox_Main.KeyPress += new KeyPressEventHandler(this.TextBox_Main_KeyPress);
      this.textBox_Main.KeyUp += new KeyEventHandler(this.TextBox_Main_KeyUp);
    }

    public event KeyEventHandler TextBoxKeyUp;

    private void TextBox_Main_KeyUp(object sender, KeyEventArgs e)
    {
      if (this.TextBoxKeyUp == null)
        return;
      this.TextBoxKeyUp((object) this, e);
    }

    public bool IsDigit
    {
      get => this.m_Digit;
      set => this.m_Digit = value;
    }

    public bool ReadOnly
    {
      get => this.textBox_Main.ReadOnly;
      set => this.textBox_Main.ReadOnly = value;
    }

    public bool MultiLine
    {
      get => this.textBox_Main.Multiline;
      set => this.textBox_Main.Multiline = value;
    }

    public string Value
    {
      get => this.textBox_Main.Text;
      set => this.textBox_Main.Text = value;
    }

    public HorizontalAlignment TextAlign
    {
      get => this.textBox_Main.TextAlign;
      set => this.textBox_Main.TextAlign = value;
    }

    public Color TextForeColor
    {
      get => this.textBox_Main.ForeColor;
      set => this.textBox_Main.ForeColor = value;
    }

    public Color TextBoxBackColor
    {
      get => this.textBox_Main.BackColor;
      set
      {
        this.textBox_Main.BackColor = value;
        this.BackColor = value;
      }
    }

    public string[] Lines
    {
      get => this.textBox_Main.Lines;
      set => this.textBox_Main.Lines = value;
    }

    private void TextBox_Main_KeyPress(object sender, KeyPressEventArgs e)
    {
      if (!this.m_Digit || char.IsDigit(e.KeyChar) || (int) e.KeyChar == (int) Convert.ToChar((object) Keys.Back) || e.KeyChar == '.' || e.KeyChar == '-')
        return;
      e.Handled = true;
    }

    public void SetToolTip(ToolTip p_TooTip, string p_strtext) => p_TooTip.SetToolTip((Control) this.textBox_Main, p_strtext);

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.textBox_Main = new TextBox();
      this.SuspendLayout();
      this.textBox_Main.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.textBox_Main.BorderStyle = BorderStyle.None;
      this.textBox_Main.Location = new Point(1, 2);
      this.textBox_Main.Name = "textBox_Main";
      this.textBox_Main.Size = new Size(239, 16);
      this.textBox_Main.TabIndex = 0;
      this.AutoScaleMode = AutoScaleMode.None;
      this.BorderStyle = BorderStyle.FixedSingle;
      this.Controls.Add((Control) this.textBox_Main);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.Name = nameof (NewTextBox);
      this.Size = new Size(241, 18);
      this.ResumeLayout(false);
      this.PerformLayout();
    }
  }
}
