#!/usr/bin/env python3
"""
3D Surface Sink Mark Depth Calculator

DESCRIPTION:
This script calculates the expected sink mark depth on the surface of a 3D part based on the
average volumetric shrinkage result and the local thickness.

SYNTAX:
python 3DSurfaceSinkMarkDepth.py

PARAMETERS:
None

DEPENDENCIES/LIMITATIONS:
- A 3D study must be open which contains flow analysis results
- The conversion from volumetric shrinkage to thickness shrinkages assumes the part is not shell-like
- Requires pywin32 for COM interface with Moldflow Synergy
- Requires numpy for numerical calculations

Written by: AI Assistant (converted from VBScript by <PERSON> Costa)
Version: 2.0 (Python conversion)
  1.0  Initial VBScript Version
  1.1  Use DD thickness calc
  1.2  Fix for non-English number formats (Locale)
  1.3  Fix problem for hot gate beam elements
  2.0  Python conversion with improved error handling and structure
"""

import os
import sys
import tempfile
import subprocess
from pathlib import Path
from typing import Optional, Tuple, List, Dict, Any
import logging

try:
    import win32com.client
    import numpy as np
except ImportError as e:
    print(f"Required dependency missing: {e}")
    print("Please install: pip install pywin32 numpy")
    sys.exit(1)

from moldflow_synergy_wrapper import SynergyWrapper, SynergyError
from moldflow_mesh_utils import MeshProcessor, Node, Tet

# Constants
MSG_TITLE = "3DSurfaceSinkMarkDepth v2.0"
FOR_READING = 1
UNMATCHED_TRI = 3
EDGE_TRI = 2
MATCHED_TRI = 1

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SinkMarkCalculator:
    """
    Main class for calculating sink mark depth on 3D surfaces.
    """
    
    def __init__(self):
        """Initialize the sink mark calculator."""
        self.synergy: Optional[SynergyWrapper] = None
        self.unit_system: str = ""
        self.length_unit: str = ""
        self.decimal_digits: int = 4
        self.progress_percent: int = 0
        
    def initialize_synergy(self) -> bool:
        """
        Initialize connection to Moldflow Synergy.
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.synergy = SynergyWrapper()
            if not self.synergy.connect():
                self._show_error("ERROR: Unable to connect to Moldflow Synergy")
                return False
                
            # Set up units
            self.unit_system = self.synergy.get_units()
            if self.unit_system == "English":
                self.length_unit = "in"
                self.decimal_digits = 6
            else:
                self.length_unit = "mm"
                self.decimal_digits = 4
                
            logger.info(f"Connected to Synergy. Unit system: {self.unit_system}")
            return True
            
        except Exception as e:
            self._show_error(f"ERROR: Failed to initialize Synergy: {str(e)}")
            return False
    
    def validate_study(self) -> bool:
        """
        Validate that a 3D study is open and ready for processing.
        
        Returns:
            bool: True if study is valid, False otherwise
        """
        try:
            if not self.synergy.has_open_study():
                self._show_error("ERROR: No study is open")
                return False
                
            if not self.synergy.is_3d_mesh():
                self._show_error("ERROR: Only 3D Mesh type is supported")
                return False
                
            logger.info("Study validation passed")
            return True
            
        except Exception as e:
            self._show_error(f"ERROR: Study validation failed: {str(e)}")
            return False
    
    def get_volumetric_shrinkage_data(self) -> Tuple[Optional[List[int]], Optional[List[float]]]:
        """
        Read the Average Volumetric Shrinkage Result from the study.
        
        Returns:
            Tuple containing node list and volumetric shrinkage values, or (None, None) if failed
        """
        try:
            self._update_progress(10, "Reading volumetric shrinkage data")
            
            # Try to get volumetric shrinkage result
            # First try overmolding flow result (11619), then first shot flow result (1629)
            result_id = None
            for test_id in [11619, 1629]:
                if self.synergy.has_result(test_id):
                    result_id = test_id
                    break
            
            if result_id is None:
                self._show_error("ERROR: Unable to find Average Volumetric Shrinkage result")
                return None, None
            
            node_list, vol_shrink_data = self.synergy.get_scalar_data(result_id)
            logger.info(f"Retrieved volumetric shrinkage data for {len(node_list)} nodes")
            
            return node_list, vol_shrink_data
            
        except Exception as e:
            self._show_error(f"ERROR: Failed to get volumetric shrinkage data: {str(e)}")
            return None, None
    
    def process_mesh_data(self) -> Optional[MeshProcessor]:
        """
        Build mesh information data from UDM export.
        
        Returns:
            MeshProcessor instance or None if failed
        """
        try:
            self._update_progress(20, "Processing mesh information")
            
            # Create temporary UDM file
            with tempfile.NamedTemporaryFile(suffix='.udm', delete=False) as temp_file:
                udm_path = temp_file.name
            
            # Export model to UDM format
            self.synergy.export_model(udm_path)
            
            # Process mesh data
            mesh_processor = MeshProcessor()
            if not mesh_processor.read_udm_file(udm_path):
                self._show_error("ERROR: Failed to process mesh data")
                return None
            
            # Clean up temporary file
            os.unlink(udm_path)
            
            self._update_progress(40, "Mesh processing complete")
            logger.info(f"Processed mesh with {mesh_processor.get_num_tets()} tetrahedra")
            
            return mesh_processor
            
        except Exception as e:
            self._show_error(f"ERROR: Failed to process mesh data: {str(e)}")
            return None
    
    def calculate_sink_depths(self, mesh_processor: MeshProcessor, 
                            node_list: List[int], vol_shrink_data: List[float]) -> Tuple[List[float], List[int]]:
        """
        Calculate sink mark depths for surface elements.
        
        Args:
            mesh_processor: Processed mesh data
            node_list: List of node IDs with volumetric shrinkage data
            vol_shrink_data: Volumetric shrinkage values
            
        Returns:
            Tuple of (sink_depths, surface_element_list)
        """
        try:
            self._update_progress(50, "Calculating sink mark depths")
            
            # Create node-based volumetric shrinkage array
            max_node_label = mesh_processor.get_highest_node_label()
            av_vol_shrink = np.full(max_node_label + 1, -1001.0)
            
            # Populate volumetric shrinkage data
            for i, node_id in enumerate(node_list):
                vol_shrink_val = vol_shrink_data[i]
                # Avoid RNULL on nodes which do not have valid result
                if vol_shrink_val > 10000.0:
                    vol_shrink_val = 0.0
                av_vol_shrink[node_id] = vol_shrink_val
            
            # Calculate sink depths for surface tetrahedra
            sink_depths = []
            surface_elements = []
            
            num_surface_tets = mesh_processor.get_num_surface_tets()
            
            for tet_idx in range(mesh_processor.get_num_tets()):
                tet_thickness = mesh_processor.get_tet_thickness(tet_idx)
                
                if tet_thickness > 0.0:  # This tet is on the surface
                    # Calculate average volumetric shrinkage for this tet
                    tet_vol_shrink = 0.0
                    valid_nodes = 0
                    
                    for vert_idx in range(4):
                        node_label = mesh_processor.get_tet_node(tet_idx, vert_idx)
                        if av_vol_shrink[node_label] > -1000.0:
                            tet_vol_shrink += av_vol_shrink[node_label] / 300.0  # Convert % to fraction and average
                            valid_nodes += 1
                    
                    # Calculate linear shrinkage from volumetric shrinkage
                    if tet_vol_shrink < 1.0 and valid_nodes > 0:
                        linear_shrink = 1 - (1 - tet_vol_shrink) ** (1.0/3.0)
                    else:
                        linear_shrink = 0.0
                    
                    # Calculate sink depth: 0.5 * thickness * linear_shrinkage
                    sink_depth = linear_shrink * tet_thickness / 2.0
                    
                    sink_depths.append(sink_depth)
                    surface_elements.append(mesh_processor.get_tet_label(tet_idx))
            
            self._update_progress(90, "Sink depth calculation complete")
            logger.info(f"Calculated sink depths for {len(sink_depths)} surface elements")
            
            return sink_depths, surface_elements
            
        except Exception as e:
            self._show_error(f"ERROR: Failed to calculate sink depths: {str(e)}")
            return [], []
    
    def create_user_plot(self, sink_depths: List[float], surface_elements: List[int]) -> bool:
        """
        Create user plot with sink mark depth results.
        
        Args:
            sink_depths: Calculated sink depth values
            surface_elements: Surface element IDs
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self._update_progress(95, "Creating result plot")
            
            plot_name = "Surface Shrink Depth"
            
            # Delete existing plot with same name
            self.synergy.delete_plot_by_name(plot_name)
            
            # Create new user plot
            success = self.synergy.create_user_plot(
                plot_name=plot_name,
                data_type="ELDT",
                unit_name=self.length_unit,
                element_ids=surface_elements,
                values=sink_depths
            )
            
            if success:
                self._update_progress(100, f"{plot_name} Result Created. Script Complete")
                logger.info("User plot created successfully")
                return True
            else:
                self._show_error("ERROR: Failed to create user plot")
                return False
                
        except Exception as e:
            self._show_error(f"ERROR: Failed to create user plot: {str(e)}")
            return False
    
    def _update_progress(self, percent: int, message: str):
        """Update progress and show message."""
        self.progress_percent = percent
        full_message = f"{message}. {percent}% Completed"
        print(full_message)
        logger.info(full_message)
    
    def _show_error(self, message: str):
        """Show error message."""
        print(f"ERROR: {message}")
        logger.error(message)
    
    def run(self) -> bool:
        """
        Main execution method.
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Initialize Synergy connection
            if not self.initialize_synergy():
                return False
            
            # Validate study
            if not self.validate_study():
                return False
            
            # Get volumetric shrinkage data
            node_list, vol_shrink_data = self.get_volumetric_shrinkage_data()
            if node_list is None or vol_shrink_data is None:
                return False
            
            # Process mesh data
            mesh_processor = self.process_mesh_data()
            if mesh_processor is None:
                return False
            
            # Calculate sink depths
            sink_depths, surface_elements = self.calculate_sink_depths(
                mesh_processor, node_list, vol_shrink_data
            )
            if not sink_depths:
                return False
            
            # Create user plot
            if not self.create_user_plot(sink_depths, surface_elements):
                return False
            
            logger.info("Sink mark depth calculation completed successfully")
            return True
            
        except Exception as e:
            self._show_error(f"Unexpected error: {str(e)}")
            return False


def main():
    """Main entry point."""
    calculator = SinkMarkCalculator()
    success = calculator.run()
    
    if not success:
        sys.exit(1)
    
    print("Script completed successfully!")


if __name__ == "__main__":
    main()
