﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmGate
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDLog4Net;
using HDMFUserControl;
using HDMoldFlow.Properties;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public class frmGate : Form
  {
    private const int WS_EX_COMPOSITED = 33554432;
    private IContainer components = (IContainer) null;
    private Label label_Gate;
    private ToolTip toolTip_Hor_Angle;
    private PictureBox pictureBox_Pin;
    private NewTextBox newTextBox_PinLength4;
    private NewTextBox newTextBox_PinLength3;
    private NewTextBox newTextBox_PinLength2;
    private NewTextBox newTextBox_PinLength1;
    private NewTextBox newTextBox_PinDiameter;
    private NewButton newButton_Apply;
    private PictureBox pictureBox_Side;
    private NewTextBox newTextBox_SideLength;
    private NewTextBox newTextBox_SideDiameter1;
    private NewTextBox newTextBox_SideDiameter2;
    private Label label_Side;

    protected override CreateParams CreateParams
    {
      get
      {
        CreateParams createParams = base.CreateParams;
        createParams.ExStyle |= 33554432;
        return createParams;
      }
    }

    public frmGate()
    {
      this.InitializeComponent();
      this.newButton_Apply.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Apply.Image);
      this.Text = LocaleControl.getInstance().GetString("IDS_SET_GATE");
      this.label_Gate.Text = LocaleControl.getInstance().GetString("IDS_GATE");
      this.label_Side.Text = LocaleControl.getInstance().GetString("IDS_SIDE");
      this.newButton_Apply.ButtonText = LocaleControl.getInstance().GetString("IDS_APPLY");
    }

    private void frmGate_Load(object sender, EventArgs e)
    {
      this.ActiveControl = (Control) this.label_Gate;
      foreach (KeyValuePair<string, string> keyValuePair in clsDefine.g_dicGate)
      {
        KeyValuePair<string, string> kvTmp = keyValuePair;
        ((NewTextBox) this.Controls.Cast<Control>().Where<Control>((Func<Control, bool>) (Temp => Temp.Name.Contains(kvTmp.Key))).FirstOrDefault<Control>()).Value = kvTmp.Value;
      }
    }

    private void newButton_Apply_NewClick(object sender, EventArgs e)
    {
      if (this.newTextBox_PinLength1.Value == "" || this.newTextBox_PinLength2.Value == "" || this.newTextBox_PinLength3.Value == "" || this.newTextBox_PinLength4.Value == "" || this.newTextBox_PinDiameter.Value == "" || this.newTextBox_SideDiameter2.Value == "" || this.newTextBox_SideDiameter1.Value == "" || this.newTextBox_SideLength.Value == "")
        return;
      try
      {
        clsDefine.g_dicGate["PinLength1"] = this.newTextBox_PinLength1.Value;
        clsDefine.g_dicGate["PinLength2"] = this.newTextBox_PinLength2.Value;
        clsDefine.g_dicGate["PinLength3"] = this.newTextBox_PinLength3.Value;
        clsDefine.g_dicGate["PinLength4"] = this.newTextBox_PinLength4.Value;
        clsDefine.g_dicGate["PinDiameter"] = this.newTextBox_PinDiameter.Value;
        clsDefine.g_dicGate["SideDiameter1"] = this.newTextBox_SideDiameter1.Value;
        clsDefine.g_dicGate["SideDiameter2"] = this.newTextBox_SideDiameter2.Value;
        clsDefine.g_dicGate["SideLength"] = this.newTextBox_SideLength.Value;
        foreach (KeyValuePair<string, string> keyValuePair in clsDefine.g_dicGate)
        {
          if (keyValuePair.Key.Contains("Pin"))
            clsUtill.WriteINI("Pin", keyValuePair.Key.Replace("Pin", ""), keyValuePair.Value, clsDefine.g_fiGateCfg.FullName);
          else
            clsUtill.WriteINI("Side", keyValuePair.Key.Replace("Side", ""), keyValuePair.Value, clsDefine.g_fiGateCfg.FullName);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMoldFlow][frmGate]newButton_Apply_NewClick):" + ex.Message));
      }
      this.Close();
    }

    private void frmGate_KeyDown(object sender, KeyEventArgs e)
    {
      if (e.KeyCode != Keys.Escape)
        return;
      this.Close();
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.components = (IContainer) new System.ComponentModel.Container();
      ComponentResourceManager componentResourceManager = new ComponentResourceManager(typeof (frmGate));
      this.label_Gate = new Label();
      this.toolTip_Hor_Angle = new ToolTip(this.components);
      this.newTextBox_PinDiameter = new NewTextBox();
      this.newTextBox_PinLength4 = new NewTextBox();
      this.newTextBox_PinLength3 = new NewTextBox();
      this.newTextBox_PinLength1 = new NewTextBox();
      this.newTextBox_PinLength2 = new NewTextBox();
      this.pictureBox_Pin = new PictureBox();
      this.newButton_Apply = new NewButton();
      this.pictureBox_Side = new PictureBox();
      this.newTextBox_SideLength = new NewTextBox();
      this.newTextBox_SideDiameter1 = new NewTextBox();
      this.newTextBox_SideDiameter2 = new NewTextBox();
      this.label_Side = new Label();
      ((ISupportInitialize) this.pictureBox_Pin).BeginInit();
      ((ISupportInitialize) this.pictureBox_Side).BeginInit();
      this.SuspendLayout();
      this.label_Gate.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Gate.BorderStyle = BorderStyle.FixedSingle;
      this.label_Gate.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Gate.ForeColor = Color.MidnightBlue;
      this.label_Gate.Location = new Point(7, 9);
      this.label_Gate.Name = "label_Gate";
      this.label_Gate.Size = new Size(285, 20);
      this.label_Gate.TabIndex = 21;
      this.label_Gate.Text = "[게이트]";
      this.label_Gate.TextAlign = ContentAlignment.MiddleCenter;
      this.toolTip_Hor_Angle.ShowAlways = true;
      this.newTextBox_PinDiameter.BackColor = SystemColors.Window;
      this.newTextBox_PinDiameter.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_PinDiameter.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_PinDiameter.IsDigit = true;
      this.newTextBox_PinDiameter.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_PinDiameter.Location = new Point(72, 130);
      this.newTextBox_PinDiameter.MultiLine = false;
      this.newTextBox_PinDiameter.Name = "newTextBox_PinDiameter";
      this.newTextBox_PinDiameter.ReadOnly = false;
      this.newTextBox_PinDiameter.Size = new Size(62, 23);
      this.newTextBox_PinDiameter.TabIndex = 6;
      this.newTextBox_PinDiameter.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_PinDiameter.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_PinDiameter.TextForeColor = SystemColors.WindowText;
      this.newTextBox_PinDiameter.Value = "0";
      this.newTextBox_PinLength4.BackColor = SystemColors.Window;
      this.newTextBox_PinLength4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_PinLength4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_PinLength4.IsDigit = true;
      this.newTextBox_PinLength4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_PinLength4.Location = new Point(211, 55);
      this.newTextBox_PinLength4.MultiLine = false;
      this.newTextBox_PinLength4.Name = "newTextBox_PinLength4";
      this.newTextBox_PinLength4.ReadOnly = false;
      this.newTextBox_PinLength4.Size = new Size(62, 23);
      this.newTextBox_PinLength4.TabIndex = 2;
      this.newTextBox_PinLength4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_PinLength4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_PinLength4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_PinLength4.Value = "0";
      this.newTextBox_PinLength3.BackColor = SystemColors.Window;
      this.newTextBox_PinLength3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_PinLength3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_PinLength3.IsDigit = true;
      this.newTextBox_PinLength3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_PinLength3.Location = new Point(211, 104);
      this.newTextBox_PinLength3.MultiLine = false;
      this.newTextBox_PinLength3.Name = "newTextBox_PinLength3";
      this.newTextBox_PinLength3.ReadOnly = false;
      this.newTextBox_PinLength3.Size = new Size(62, 23);
      this.newTextBox_PinLength3.TabIndex = 3;
      this.newTextBox_PinLength3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_PinLength3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_PinLength3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_PinLength3.Value = "0";
      this.newTextBox_PinLength1.BackColor = SystemColors.Window;
      this.newTextBox_PinLength1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_PinLength1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_PinLength1.IsDigit = true;
      this.newTextBox_PinLength1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_PinLength1.Location = new Point(211, 180);
      this.newTextBox_PinLength1.MultiLine = false;
      this.newTextBox_PinLength1.Name = "newTextBox_PinLength1";
      this.newTextBox_PinLength1.ReadOnly = false;
      this.newTextBox_PinLength1.Size = new Size(62, 23);
      this.newTextBox_PinLength1.TabIndex = 5;
      this.newTextBox_PinLength1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_PinLength1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_PinLength1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_PinLength1.Value = "0";
      this.newTextBox_PinLength2.BackColor = SystemColors.Window;
      this.newTextBox_PinLength2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_PinLength2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_PinLength2.IsDigit = true;
      this.newTextBox_PinLength2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_PinLength2.Location = new Point(211, 143);
      this.newTextBox_PinLength2.MultiLine = false;
      this.newTextBox_PinLength2.Name = "newTextBox_PinLength2";
      this.newTextBox_PinLength2.ReadOnly = false;
      this.newTextBox_PinLength2.Size = new Size(62, 23);
      this.newTextBox_PinLength2.TabIndex = 4;
      this.newTextBox_PinLength2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_PinLength2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_PinLength2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_PinLength2.Value = "0";
      this.pictureBox_Pin.BackgroundImage = (Image) componentResourceManager.GetObject("pictureBox_Pin.BackgroundImage");
      this.pictureBox_Pin.BackgroundImageLayout = ImageLayout.Stretch;
      this.pictureBox_Pin.BorderStyle = BorderStyle.FixedSingle;
      this.pictureBox_Pin.Location = new Point(7, 17);
      this.pictureBox_Pin.Name = "pictureBox_Pin";
      this.pictureBox_Pin.Size = new Size(285, 224);
      this.pictureBox_Pin.TabIndex = 100;
      this.pictureBox_Pin.TabStop = false;
      this.newButton_Apply.Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.newButton_Apply.ButtonBackColor = Color.White;
      this.newButton_Apply.ButtonText = "적용";
      this.newButton_Apply.FlatBorderSize = 1;
      this.newButton_Apply.FlatStyle = FlatStyle.Flat;
      this.newButton_Apply.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Apply.Image = (Image) Resources.OK;
      this.newButton_Apply.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Apply.Location = new Point(7, 460);
      this.newButton_Apply.Name = "newButton_Apply";
      this.newButton_Apply.Size = new Size(284, 23);
      this.newButton_Apply.TabIndex = 100;
      this.newButton_Apply.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Apply.NewClick += new EventHandler(this.newButton_Apply_NewClick);
      this.pictureBox_Side.BackgroundImage = (Image) componentResourceManager.GetObject("pictureBox_Side.BackgroundImage");
      this.pictureBox_Side.BackgroundImageLayout = ImageLayout.Stretch;
      this.pictureBox_Side.BorderStyle = BorderStyle.FixedSingle;
      this.pictureBox_Side.Location = new Point(7, 233);
      this.pictureBox_Side.Name = "pictureBox_Side";
      this.pictureBox_Side.Size = new Size(285, 224);
      this.pictureBox_Side.TabIndex = 101;
      this.pictureBox_Side.TabStop = false;
      this.newTextBox_SideLength.BackColor = SystemColors.Window;
      this.newTextBox_SideLength.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_SideLength.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_SideLength.IsDigit = true;
      this.newTextBox_SideLength.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_SideLength.Location = new Point(211, 331);
      this.newTextBox_SideLength.MultiLine = false;
      this.newTextBox_SideLength.Name = "newTextBox_SideLength";
      this.newTextBox_SideLength.ReadOnly = false;
      this.newTextBox_SideLength.Size = new Size(62, 23);
      this.newTextBox_SideLength.TabIndex = 9;
      this.newTextBox_SideLength.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_SideLength.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_SideLength.TextForeColor = SystemColors.WindowText;
      this.newTextBox_SideLength.Value = "0";
      this.newTextBox_SideDiameter1.BackColor = SystemColors.Window;
      this.newTextBox_SideDiameter1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_SideDiameter1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_SideDiameter1.IsDigit = true;
      this.newTextBox_SideDiameter1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_SideDiameter1.Location = new Point(72, 429);
      this.newTextBox_SideDiameter1.MultiLine = false;
      this.newTextBox_SideDiameter1.Name = "newTextBox_SideDiameter1";
      this.newTextBox_SideDiameter1.ReadOnly = false;
      this.newTextBox_SideDiameter1.Size = new Size(62, 23);
      this.newTextBox_SideDiameter1.TabIndex = 8;
      this.newTextBox_SideDiameter1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_SideDiameter1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_SideDiameter1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_SideDiameter1.Value = "0";
      this.newTextBox_SideDiameter2.BackColor = SystemColors.Window;
      this.newTextBox_SideDiameter2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_SideDiameter2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_SideDiameter2.IsDigit = true;
      this.newTextBox_SideDiameter2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_SideDiameter2.Location = new Point(72, 275);
      this.newTextBox_SideDiameter2.MultiLine = false;
      this.newTextBox_SideDiameter2.Name = "newTextBox_SideDiameter2";
      this.newTextBox_SideDiameter2.ReadOnly = false;
      this.newTextBox_SideDiameter2.Size = new Size(62, 23);
      this.newTextBox_SideDiameter2.TabIndex = 7;
      this.newTextBox_SideDiameter2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_SideDiameter2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_SideDiameter2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_SideDiameter2.Value = "0";
      this.label_Side.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Side.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Side.ForeColor = Color.MidnightBlue;
      this.label_Side.Location = new Point(7, 230);
      this.label_Side.Name = "label_Side";
      this.label_Side.Size = new Size(285, 20);
      this.label_Side.TabIndex = 102;
      this.label_Side.Text = "[사이드]";
      this.label_Side.TextAlign = ContentAlignment.MiddleCenter;
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(299, 486);
      this.Controls.Add((Control) this.label_Side);
      this.Controls.Add((Control) this.newTextBox_SideDiameter2);
      this.Controls.Add((Control) this.newTextBox_PinDiameter);
      this.Controls.Add((Control) this.newTextBox_SideDiameter1);
      this.Controls.Add((Control) this.newTextBox_PinLength4);
      this.Controls.Add((Control) this.newTextBox_SideLength);
      this.Controls.Add((Control) this.newTextBox_PinLength3);
      this.Controls.Add((Control) this.newTextBox_PinLength1);
      this.Controls.Add((Control) this.newButton_Apply);
      this.Controls.Add((Control) this.newTextBox_PinLength2);
      this.Controls.Add((Control) this.label_Gate);
      this.Controls.Add((Control) this.pictureBox_Pin);
      this.Controls.Add((Control) this.pictureBox_Side);
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmGate);
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = nameof (frmGate);
      this.Load += new EventHandler(this.frmGate_Load);
      this.KeyDown += new KeyEventHandler(this.frmGate_KeyDown);
      ((ISupportInitialize) this.pictureBox_Pin).EndInit();
      ((ISupportInitialize) this.pictureBox_Side).EndInit();
      this.ResumeLayout(false);
    }
  }
}
