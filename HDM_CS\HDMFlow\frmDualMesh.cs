﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmDualMesh
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDLog4Net;
using HDMFUserControl;
using HDMoldFlow.Properties;
using HDMoldFlowLibrary;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public class frmDualMesh : Form
  {
    public DataRow m_drStudy;
    private string m_strResult1_Success = "The elements is right";
    private string m_strResult1_Failed = "You need fix the error elements";
    private string m_strResult2_Success = "it's ok to run for analysis by Dual-Domain";
    private string m_strResult2_Failed = "The Matching percentage is low, it should be made by 3D Mesh type";
    private IContainer components = (IContainer) null;
    private Label label_Mesh_Diagnosis;
    private Label label_Focus;
    private NewTextBox newTextBox_TriangleNumber;
    private Label label2;
    private Label label3;
    private Label label4;
    private Label label5;
    private NewTextBox newTextBox_AspectRatioMax;
    private NewTextBox newTextBox_AspectRatioAve;
    private NewTextBox newTextBox_AspectRatioMin;
    private Label label6;
    private Label label7;
    private Label label8;
    private Label label9;
    private NewTextBox newTextBox_EdgeFree;
    private NewTextBox newTextBox_EdgeManifold;
    private NewTextBox newTextBox_EdgeNon;
    private Label label10;
    private NewTextBox newTextBox_Intersect;
    private NewTextBox newTextBox_Result1;
    private Label label11;
    private Label label12;
    private NewTextBox newTextBox_RatioReciprocal;
    private NewTextBox newTextBox_RatioMatch;
    private NewTextBox newTextBox_Result2;
    private NewButton newButton_Apply;

    public frmDualMesh()
    {
      this.InitializeComponent();
      this.newButton_Apply.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Apply.Image);
      this.Text = LocaleControl.getInstance().GetString("IDS_UPDATE_MESH");
      this.label_Mesh_Diagnosis.Text = LocaleControl.getInstance().GetString("IDS_MESH_DIAGNOSIS");
      this.newButton_Apply.ButtonText = LocaleControl.getInstance().GetString("IDS_UPDATE");
    }

    private void frmDualMesh_Load(object sender, EventArgs e) => this.SetDefault();

    private void SetDefault()
    {
      try
      {
        clsUtill.StartProgress("open Study...", (Form) this);
        clsHDMFLib.OpenStudy(this.m_drStudy["Name"].ToString());
        clsUtill.EndProgress();
        List<string> dualDomainMeshData = clsHDMFLib.GetDualDomainMeshData();
        double p_dblAspectRatio = clsUtill.ConvertToDouble(clsDefine.g_dicMeshStat["Value"]);
        clsHDMFLib.VerificationDualMesh(dualDomainMeshData, p_dblAspectRatio);
        double num1 = Math.Round(double.Parse(dualDomainMeshData[0]), 6);
        double num2 = Math.Round(double.Parse(dualDomainMeshData[1]), 6);
        double num3 = Math.Round(double.Parse(dualDomainMeshData[2]), 6);
        double num4 = Math.Round(double.Parse(dualDomainMeshData[3]), 6);
        double num5 = Math.Round(double.Parse(dualDomainMeshData[4]), 6);
        double num6 = Math.Round(double.Parse(dualDomainMeshData[5]), 6);
        double num7 = Math.Round(double.Parse(dualDomainMeshData[6]), 6);
        double num8 = Math.Round(double.Parse(dualDomainMeshData[7]), 6);
        double num9 = Math.Round(double.Parse(dualDomainMeshData[8]), 6);
        double num10 = Math.Round(double.Parse(dualDomainMeshData[9]), 6);
        this.newTextBox_TriangleNumber.Value = num1.ToString();
        this.newTextBox_AspectRatioMax.Value = num2.ToString();
        this.newTextBox_AspectRatioAve.Value = num3.ToString();
        this.newTextBox_AspectRatioMin.Value = num4.ToString();
        this.newTextBox_EdgeFree.Value = num5.ToString();
        this.newTextBox_EdgeManifold.Value = num6.ToString();
        this.newTextBox_EdgeNon.Value = num7.ToString();
        this.newTextBox_Intersect.Value = num8.ToString();
        this.newTextBox_RatioMatch.Value = num9.ToString();
        this.newTextBox_RatioReciprocal.Value = num10.ToString();
        string empty1 = string.Empty;
        string str1 = num2 <= 0.0 || num5 + num7 + num8 <= 0.0 ? this.m_strResult1_Success : this.m_strResult1_Failed;
        string empty2 = string.Empty;
        string str2 = num9 >= 0.85 && num10 >= 0.85 ? this.m_strResult2_Success : this.m_strResult2_Failed;
        this.newTextBox_Result1.Value = str1;
        this.newTextBox_Result2.Value = str2;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmDualMesh]SetDefault):" + ex.Message));
      }
    }

    private void newButton_Apply_NewClick(object sender, EventArgs e)
    {
      this.DialogResult = DialogResult.OK;
      this.Close();
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.label_Mesh_Diagnosis = new Label();
      this.label_Focus = new Label();
      this.label2 = new Label();
      this.label3 = new Label();
      this.label4 = new Label();
      this.label5 = new Label();
      this.label6 = new Label();
      this.label7 = new Label();
      this.label8 = new Label();
      this.label9 = new Label();
      this.label10 = new Label();
      this.label11 = new Label();
      this.label12 = new Label();
      this.newButton_Apply = new NewButton();
      this.newTextBox_RatioMatch = new NewTextBox();
      this.newTextBox_EdgeNon = new NewTextBox();
      this.newTextBox_AspectRatioMin = new NewTextBox();
      this.newTextBox_EdgeManifold = new NewTextBox();
      this.newTextBox_AspectRatioAve = new NewTextBox();
      this.newTextBox_EdgeFree = new NewTextBox();
      this.newTextBox_AspectRatioMax = new NewTextBox();
      this.newTextBox_RatioReciprocal = new NewTextBox();
      this.newTextBox_Result2 = new NewTextBox();
      this.newTextBox_Result1 = new NewTextBox();
      this.newTextBox_Intersect = new NewTextBox();
      this.newTextBox_TriangleNumber = new NewTextBox();
      this.SuspendLayout();
      this.label_Mesh_Diagnosis.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Mesh_Diagnosis.BorderStyle = BorderStyle.FixedSingle;
      this.label_Mesh_Diagnosis.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Mesh_Diagnosis.ForeColor = Color.MidnightBlue;
      this.label_Mesh_Diagnosis.Location = new Point(5, 5);
      this.label_Mesh_Diagnosis.Name = "label_Mesh_Diagnosis";
      this.label_Mesh_Diagnosis.Size = new Size(253, 20);
      this.label_Mesh_Diagnosis.TabIndex = 8;
      this.label_Mesh_Diagnosis.Text = "메쉬 진단";
      this.label_Mesh_Diagnosis.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Focus.BackColor = Color.Lavender;
      this.label_Focus.BorderStyle = BorderStyle.FixedSingle;
      this.label_Focus.Location = new Point(5, 24);
      this.label_Focus.Name = "label_Focus";
      this.label_Focus.Size = new Size(169, 20);
      this.label_Focus.TabIndex = 9;
      this.label_Focus.Text = "Triangle Number";
      this.label_Focus.TextAlign = ContentAlignment.MiddleCenter;
      this.label2.BackColor = Color.Lavender;
      this.label2.BorderStyle = BorderStyle.FixedSingle;
      this.label2.Location = new Point(5, 43);
      this.label2.Name = "label2";
      this.label2.Size = new Size(253, 20);
      this.label2.TabIndex = 9;
      this.label2.Text = "Aspect Ratio";
      this.label2.TextAlign = ContentAlignment.MiddleCenter;
      this.label3.BackColor = Color.Lavender;
      this.label3.BorderStyle = BorderStyle.FixedSingle;
      this.label3.Location = new Point(5, 62);
      this.label3.Name = "label3";
      this.label3.Size = new Size(85, 20);
      this.label3.TabIndex = 9;
      this.label3.Text = "Maximum";
      this.label3.TextAlign = ContentAlignment.MiddleCenter;
      this.label4.BackColor = Color.Lavender;
      this.label4.BorderStyle = BorderStyle.FixedSingle;
      this.label4.Location = new Point(89, 62);
      this.label4.Name = "label4";
      this.label4.Size = new Size(85, 20);
      this.label4.TabIndex = 9;
      this.label4.Text = "Average";
      this.label4.TextAlign = ContentAlignment.MiddleCenter;
      this.label5.BackColor = Color.Lavender;
      this.label5.BorderStyle = BorderStyle.FixedSingle;
      this.label5.Location = new Point(173, 62);
      this.label5.Name = "label5";
      this.label5.Size = new Size(85, 20);
      this.label5.TabIndex = 9;
      this.label5.Text = "Minimum";
      this.label5.TextAlign = ContentAlignment.MiddleCenter;
      this.label6.BackColor = Color.Lavender;
      this.label6.BorderStyle = BorderStyle.FixedSingle;
      this.label6.Location = new Point(5, 100);
      this.label6.Name = "label6";
      this.label6.Size = new Size(253, 20);
      this.label6.TabIndex = 9;
      this.label6.Text = "Edge detail";
      this.label6.TextAlign = ContentAlignment.MiddleCenter;
      this.label7.BackColor = Color.Lavender;
      this.label7.BorderStyle = BorderStyle.FixedSingle;
      this.label7.Location = new Point(5, 119);
      this.label7.Name = "label7";
      this.label7.Size = new Size(85, 20);
      this.label7.TabIndex = 9;
      this.label7.Text = "Free";
      this.label7.TextAlign = ContentAlignment.MiddleCenter;
      this.label8.BackColor = Color.Lavender;
      this.label8.BorderStyle = BorderStyle.FixedSingle;
      this.label8.Location = new Point(89, 119);
      this.label8.Name = "label8";
      this.label8.Size = new Size(85, 20);
      this.label8.TabIndex = 9;
      this.label8.Text = "Manifold";
      this.label8.TextAlign = ContentAlignment.MiddleCenter;
      this.label9.BackColor = Color.Lavender;
      this.label9.BorderStyle = BorderStyle.FixedSingle;
      this.label9.Location = new Point(173, 119);
      this.label9.Name = "label9";
      this.label9.Size = new Size(85, 20);
      this.label9.TabIndex = 9;
      this.label9.Text = "Non";
      this.label9.TextAlign = ContentAlignment.MiddleCenter;
      this.label10.BackColor = Color.Lavender;
      this.label10.BorderStyle = BorderStyle.FixedSingle;
      this.label10.Location = new Point(5, 157);
      this.label10.Name = "label10";
      this.label10.Size = new Size(169, 20);
      this.label10.TabIndex = 9;
      this.label10.Text = "Intersection detail";
      this.label10.TextAlign = ContentAlignment.MiddleCenter;
      this.label11.BackColor = Color.Lavender;
      this.label11.BorderStyle = BorderStyle.FixedSingle;
      this.label11.Location = new Point(5, 195);
      this.label11.Name = "label11";
      this.label11.Size = new Size(169, 20);
      this.label11.TabIndex = 9;
      this.label11.Text = "Match Ratio";
      this.label11.TextAlign = ContentAlignment.MiddleCenter;
      this.label12.BackColor = Color.Lavender;
      this.label12.BorderStyle = BorderStyle.FixedSingle;
      this.label12.Location = new Point(5, 214);
      this.label12.Name = "label12";
      this.label12.Size = new Size(169, 20);
      this.label12.TabIndex = 9;
      this.label12.Text = "Reciprocal Ratio";
      this.label12.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.ButtonBackColor = Color.White;
      this.newButton_Apply.ButtonText = "업데이트";
      this.newButton_Apply.FlatBorderSize = 1;
      this.newButton_Apply.FlatStyle = FlatStyle.Flat;
      this.newButton_Apply.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Apply.Image = (Image) Resources.Update;
      this.newButton_Apply.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Apply.Location = new Point(5, 257);
      this.newButton_Apply.Name = "newButton_Apply";
      this.newButton_Apply.Size = new Size(253, 23);
      this.newButton_Apply.TabIndex = 19;
      this.newButton_Apply.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Apply.NewClick += new EventHandler(this.newButton_Apply_NewClick);
      this.newTextBox_RatioMatch.BackColor = Color.LavenderBlush;
      this.newTextBox_RatioMatch.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_RatioMatch.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_RatioMatch.IsDigit = false;
      this.newTextBox_RatioMatch.Lines = new string[0];
      this.newTextBox_RatioMatch.Location = new Point(173, 195);
      this.newTextBox_RatioMatch.MultiLine = false;
      this.newTextBox_RatioMatch.Name = "newTextBox_RatioMatch";
      this.newTextBox_RatioMatch.ReadOnly = true;
      this.newTextBox_RatioMatch.Size = new Size(85, 20);
      this.newTextBox_RatioMatch.TabIndex = 10;
      this.newTextBox_RatioMatch.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_RatioMatch.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_RatioMatch.TextForeColor = SystemColors.WindowText;
      this.newTextBox_RatioMatch.Value = "";
      this.newTextBox_EdgeNon.BackColor = Color.LavenderBlush;
      this.newTextBox_EdgeNon.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_EdgeNon.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_EdgeNon.IsDigit = false;
      this.newTextBox_EdgeNon.Lines = new string[0];
      this.newTextBox_EdgeNon.Location = new Point(173, 138);
      this.newTextBox_EdgeNon.MultiLine = false;
      this.newTextBox_EdgeNon.Name = "newTextBox_EdgeNon";
      this.newTextBox_EdgeNon.ReadOnly = true;
      this.newTextBox_EdgeNon.Size = new Size(85, 20);
      this.newTextBox_EdgeNon.TabIndex = 10;
      this.newTextBox_EdgeNon.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_EdgeNon.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_EdgeNon.TextForeColor = SystemColors.WindowText;
      this.newTextBox_EdgeNon.Value = "";
      this.newTextBox_AspectRatioMin.BackColor = Color.LavenderBlush;
      this.newTextBox_AspectRatioMin.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_AspectRatioMin.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_AspectRatioMin.IsDigit = false;
      this.newTextBox_AspectRatioMin.Lines = new string[0];
      this.newTextBox_AspectRatioMin.Location = new Point(173, 81);
      this.newTextBox_AspectRatioMin.MultiLine = false;
      this.newTextBox_AspectRatioMin.Name = "newTextBox_AspectRatioMin";
      this.newTextBox_AspectRatioMin.ReadOnly = true;
      this.newTextBox_AspectRatioMin.Size = new Size(85, 20);
      this.newTextBox_AspectRatioMin.TabIndex = 10;
      this.newTextBox_AspectRatioMin.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_AspectRatioMin.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_AspectRatioMin.TextForeColor = SystemColors.WindowText;
      this.newTextBox_AspectRatioMin.Value = "";
      this.newTextBox_EdgeManifold.BackColor = Color.LavenderBlush;
      this.newTextBox_EdgeManifold.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_EdgeManifold.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_EdgeManifold.IsDigit = false;
      this.newTextBox_EdgeManifold.Lines = new string[0];
      this.newTextBox_EdgeManifold.Location = new Point(89, 138);
      this.newTextBox_EdgeManifold.MultiLine = false;
      this.newTextBox_EdgeManifold.Name = "newTextBox_EdgeManifold";
      this.newTextBox_EdgeManifold.ReadOnly = true;
      this.newTextBox_EdgeManifold.Size = new Size(85, 20);
      this.newTextBox_EdgeManifold.TabIndex = 10;
      this.newTextBox_EdgeManifold.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_EdgeManifold.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_EdgeManifold.TextForeColor = SystemColors.WindowText;
      this.newTextBox_EdgeManifold.Value = "";
      this.newTextBox_AspectRatioAve.BackColor = Color.LavenderBlush;
      this.newTextBox_AspectRatioAve.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_AspectRatioAve.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_AspectRatioAve.IsDigit = false;
      this.newTextBox_AspectRatioAve.Lines = new string[0];
      this.newTextBox_AspectRatioAve.Location = new Point(89, 81);
      this.newTextBox_AspectRatioAve.MultiLine = false;
      this.newTextBox_AspectRatioAve.Name = "newTextBox_AspectRatioAve";
      this.newTextBox_AspectRatioAve.ReadOnly = true;
      this.newTextBox_AspectRatioAve.Size = new Size(85, 20);
      this.newTextBox_AspectRatioAve.TabIndex = 10;
      this.newTextBox_AspectRatioAve.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_AspectRatioAve.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_AspectRatioAve.TextForeColor = SystemColors.WindowText;
      this.newTextBox_AspectRatioAve.Value = "";
      this.newTextBox_EdgeFree.BackColor = Color.LavenderBlush;
      this.newTextBox_EdgeFree.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_EdgeFree.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_EdgeFree.IsDigit = false;
      this.newTextBox_EdgeFree.Lines = new string[0];
      this.newTextBox_EdgeFree.Location = new Point(5, 138);
      this.newTextBox_EdgeFree.MultiLine = false;
      this.newTextBox_EdgeFree.Name = "newTextBox_EdgeFree";
      this.newTextBox_EdgeFree.ReadOnly = true;
      this.newTextBox_EdgeFree.Size = new Size(85, 20);
      this.newTextBox_EdgeFree.TabIndex = 10;
      this.newTextBox_EdgeFree.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_EdgeFree.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_EdgeFree.TextForeColor = SystemColors.WindowText;
      this.newTextBox_EdgeFree.Value = "";
      this.newTextBox_AspectRatioMax.BackColor = Color.LavenderBlush;
      this.newTextBox_AspectRatioMax.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_AspectRatioMax.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_AspectRatioMax.IsDigit = false;
      this.newTextBox_AspectRatioMax.Lines = new string[0];
      this.newTextBox_AspectRatioMax.Location = new Point(5, 81);
      this.newTextBox_AspectRatioMax.MultiLine = false;
      this.newTextBox_AspectRatioMax.Name = "newTextBox_AspectRatioMax";
      this.newTextBox_AspectRatioMax.ReadOnly = true;
      this.newTextBox_AspectRatioMax.Size = new Size(85, 20);
      this.newTextBox_AspectRatioMax.TabIndex = 10;
      this.newTextBox_AspectRatioMax.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_AspectRatioMax.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_AspectRatioMax.TextForeColor = SystemColors.WindowText;
      this.newTextBox_AspectRatioMax.Value = "";
      this.newTextBox_RatioReciprocal.BackColor = Color.LavenderBlush;
      this.newTextBox_RatioReciprocal.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_RatioReciprocal.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_RatioReciprocal.IsDigit = false;
      this.newTextBox_RatioReciprocal.Lines = new string[0];
      this.newTextBox_RatioReciprocal.Location = new Point(173, 214);
      this.newTextBox_RatioReciprocal.MultiLine = false;
      this.newTextBox_RatioReciprocal.Name = "newTextBox_RatioReciprocal";
      this.newTextBox_RatioReciprocal.ReadOnly = true;
      this.newTextBox_RatioReciprocal.Size = new Size(85, 20);
      this.newTextBox_RatioReciprocal.TabIndex = 10;
      this.newTextBox_RatioReciprocal.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_RatioReciprocal.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_RatioReciprocal.TextForeColor = SystemColors.WindowText;
      this.newTextBox_RatioReciprocal.Value = "";
      this.newTextBox_Result2.BackColor = SystemColors.Window;
      this.newTextBox_Result2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Result2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Result2.IsDigit = false;
      this.newTextBox_Result2.Lines = new string[1]
      {
        "it's ok to run for analysis by Dual-Domain"
      };
      this.newTextBox_Result2.Location = new Point(5, 233);
      this.newTextBox_Result2.MultiLine = false;
      this.newTextBox_Result2.Name = "newTextBox_Result2";
      this.newTextBox_Result2.ReadOnly = false;
      this.newTextBox_Result2.Size = new Size(253, 20);
      this.newTextBox_Result2.TabIndex = 10;
      this.newTextBox_Result2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Result2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Result2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Result2.Value = "it's ok to run for analysis by Dual-Domain";
      this.newTextBox_Result1.BackColor = SystemColors.Window;
      this.newTextBox_Result1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Result1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Result1.IsDigit = false;
      this.newTextBox_Result1.Lines = new string[1]
      {
        "The elements is right"
      };
      this.newTextBox_Result1.Location = new Point(5, 176);
      this.newTextBox_Result1.MultiLine = false;
      this.newTextBox_Result1.Name = "newTextBox_Result1";
      this.newTextBox_Result1.ReadOnly = false;
      this.newTextBox_Result1.Size = new Size(253, 20);
      this.newTextBox_Result1.TabIndex = 10;
      this.newTextBox_Result1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Result1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Result1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Result1.Value = "The elements is right";
      this.newTextBox_Intersect.BackColor = SystemColors.Window;
      this.newTextBox_Intersect.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Intersect.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Intersect.IsDigit = false;
      this.newTextBox_Intersect.Lines = new string[0];
      this.newTextBox_Intersect.Location = new Point(173, 157);
      this.newTextBox_Intersect.MultiLine = false;
      this.newTextBox_Intersect.Name = "newTextBox_Intersect";
      this.newTextBox_Intersect.ReadOnly = false;
      this.newTextBox_Intersect.Size = new Size(85, 20);
      this.newTextBox_Intersect.TabIndex = 10;
      this.newTextBox_Intersect.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Intersect.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Intersect.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Intersect.Value = "";
      this.newTextBox_TriangleNumber.BackColor = Color.LavenderBlush;
      this.newTextBox_TriangleNumber.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_TriangleNumber.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_TriangleNumber.IsDigit = false;
      this.newTextBox_TriangleNumber.Lines = new string[0];
      this.newTextBox_TriangleNumber.Location = new Point(173, 24);
      this.newTextBox_TriangleNumber.MultiLine = false;
      this.newTextBox_TriangleNumber.Name = "newTextBox_TriangleNumber";
      this.newTextBox_TriangleNumber.ReadOnly = true;
      this.newTextBox_TriangleNumber.Size = new Size(85, 20);
      this.newTextBox_TriangleNumber.TabIndex = 10;
      this.newTextBox_TriangleNumber.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_TriangleNumber.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_TriangleNumber.TextForeColor = SystemColors.WindowText;
      this.newTextBox_TriangleNumber.Value = "";
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(263, 284);
      this.Controls.Add((Control) this.newButton_Apply);
      this.Controls.Add((Control) this.newTextBox_RatioMatch);
      this.Controls.Add((Control) this.newTextBox_EdgeNon);
      this.Controls.Add((Control) this.newTextBox_AspectRatioMin);
      this.Controls.Add((Control) this.newTextBox_EdgeManifold);
      this.Controls.Add((Control) this.newTextBox_AspectRatioAve);
      this.Controls.Add((Control) this.newTextBox_EdgeFree);
      this.Controls.Add((Control) this.newTextBox_AspectRatioMax);
      this.Controls.Add((Control) this.newTextBox_RatioReciprocal);
      this.Controls.Add((Control) this.newTextBox_Result2);
      this.Controls.Add((Control) this.newTextBox_Result1);
      this.Controls.Add((Control) this.newTextBox_Intersect);
      this.Controls.Add((Control) this.newTextBox_TriangleNumber);
      this.Controls.Add((Control) this.label9);
      this.Controls.Add((Control) this.label5);
      this.Controls.Add((Control) this.label8);
      this.Controls.Add((Control) this.label4);
      this.Controls.Add((Control) this.label7);
      this.Controls.Add((Control) this.label3);
      this.Controls.Add((Control) this.label6);
      this.Controls.Add((Control) this.label2);
      this.Controls.Add((Control) this.label12);
      this.Controls.Add((Control) this.label11);
      this.Controls.Add((Control) this.label10);
      this.Controls.Add((Control) this.label_Focus);
      this.Controls.Add((Control) this.label_Mesh_Diagnosis);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmDualMesh);
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "메쉬 업데이트";
      this.Load += new EventHandler(this.frmDualMesh_Load);
      this.ResumeLayout(false);
    }
  }
}
