﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmInput
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDLog4Net;
using HDMFAI;
using HDMFUserControl;
using HDMoldFlow.Properties;
using HDMoldFlowLibrary;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public class frmInput : Form
  {
    private const int WS_EX_COMPOSITED = 33554432;
    private const string m_strProhibitLayer = "Prohibit Nodes";
    private const string m_strNodesLayer = "Nodes";
    private const string m_strFolder = "Mesh Nodes";
    private const string m_strProhibitKey = "Prohibit";
    private IContainer components = (IContainer) null;
    private ListBox listBox_DB;
    private Label label_DB_List;
    private Label label_Data;
    private Label label_Company;
    private Label label_TradeName;
    private Label label_Density;
    private Label label_Material;
    private NewTextBox newTextBox_Density;
    private Label label_Result;
    private Label label_InjPressure;
    private Label label_CycleTime;
    private Label label_Deflection;
    private Label label_Weight;
    private Label label_TempAtFlowFront;
    private Label label_Sinkmark;
    private Label label_VolShrinkage;
    private NewTextBox newTextBox_InjPressure_Priority;
    private NewTextBox newTextBox_Sinkmark_Priority;
    private NewTextBox newTextBox_CycleTime_Priority;
    private NewTextBox newTextBox_VolShrinkage_Priority;
    private NewTextBox newTextBox_Deflection_Priority;
    private NewTextBox newTextBox_TempAtFlowFront_Priority;
    private NewTextBox newTextBox_Weight_Priority;
    private NewButton newButton_Apply;
    private NewTextBox newTextBox_MeltTemp_Max;
    private NewTextBox newTextBox_MeltTemp_Min;
    private NewTextBox newTextBox_CoolingInletTemp_Min;
    private NewTextBox newTextBox_PackingNum_Min;
    private NewTextBox newTextBox_CoolingTime_Min;
    private NewTextBox newTextBox_VPSwitchOver_Min;
    private NewTextBox newTextBox_FillTime_Min;
    private NewTextBox newTextBox_CoolingInletTemp_Max;
    private Label label_CoolingInletTemp;
    private Label label_CoolingTime;
    private Label label_FillTime;
    private Label label_MeltTemp;
    private NewTextBox newTextBox_PackingNum_Max;
    private NewTextBox newTextBox_CoolingTime_Max;
    private NewTextBox newTextBox_VPSwitchOver_Max;
    private NewTextBox newTextBox_FillTime_Max;
    private Label label_PackingNum;
    private Label label_VPSwitchOver;
    private NewTextBox newTextBox_MoldTemp_Min;
    private NewTextBox newTextBox_MoldTemp_Max;
    private Label label_MoldTemp;
    private NewTextBox newTextBox_GateNum_Min;
    private NewTextBox newTextBox_GateNum_Max;
    private Label label_GateNum;
    private NewTextBox newTextBox_Type;
    private Label label_Type;
    private NewTextBox newTextBox_ClampF_Priority;
    private Label label_ClampF;
    private Label label_AnalysisType;
    private NewComboBox newComboBox_AnalysisType;
    private Label label_Suji_SearchValue;
    private Label label_SearchCond;
    private Label label_Search;
    private NewComboBox newComboBox_Company;
    private NewTextBox newTextBox_Search;
    private NewComboBox newComboBox_TradeName;
    private Panel panel1;
    private NewButton newButton_LoadMaterial;
    private NewButton newButton_System;
    private Label label_Option;
    private Label label_ValveOption;
    private Label label_ClampForceLimit;
    private Label label_DeflectionLimit;
    private Label label_LoopNum;
    private NewComboBox newComboBox_ValveOption;
    private NewComboBox newComboBox_LoopNum;
    private Label label_InjectionPressureLimit;
    private NewTextBox newTextBox_DeflectionLimit;
    private NewTextBox newTextBox_ClampForceLimit;
    private NewTextBox newTextBox_InjectionPressureLimit;
    private NewButton newButton_ClearNodes;
    private NewButton newButton_SelectNodes;
    private Label label_Prohibit;

    protected override CreateParams CreateParams
    {
      get
      {
        CreateParams createParams = base.CreateParams;
        createParams.ExStyle |= 33554432;
        return createParams;
      }
    }

    public DataRow m_drStudy { get; set; }

    public frmInput()
    {
      this.InitializeComponent();
      this.newButton_Apply.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Apply.Image);
      this.label_DB_List.Text = "DB " + LocaleControl.getInstance().GetString("IDS_LIST");
      this.label_Result.Text = LocaleControl.getInstance().GetString("IDS_RESULT_PRIORITY");
      this.newButton_Apply.ButtonText = "Input Data " + LocaleControl.getInstance().GetString("IDS_APPLY");
      this.label_Option.Text = LocaleControl.getInstance().GetString("IDS_OPTION");
      this.label_LoopNum.Text = LocaleControl.getInstance().GetString("IDS_LOOPNUM");
      this.label_DeflectionLimit.Text = LocaleControl.getInstance().GetString("IDS_DEFLECTION_LIMIT");
      this.label_ClampForceLimit.Text = LocaleControl.getInstance().GetString("IDS_CLAMP_FORCE_LIMIT");
      this.label_InjectionPressureLimit.Text = LocaleControl.getInstance().GetString("IDS_INJECTION_PRESSURE_LIMIT");
      this.newButton_SelectNodes.ButtonText = LocaleControl.getInstance().GetString("IDS_SELECT_NODE");
      this.newButton_ClearNodes.ButtonText = LocaleControl.getInstance().GetString("IDS_CLEAR_NODES");
    }

    private void frmInput_Load(object sender, EventArgs e)
    {
      this.ActiveControl = (Control) this.label_DB_List;
      this.newComboBox_AnalysisType.Items.Add((object) "Flow");
      this.newComboBox_AnalysisType.Items.Add((object) "Flow_Warp");
      this.newComboBox_AnalysisType.Items.Add((object) "Cool_Flow_Warp");
      this.newComboBox_AnalysisType.SelectedIndex = 0;
      this.newComboBox_ValveOption.Items.AddRange((object[]) new string[2]
      {
        "Yes",
        "No"
      });
      this.newComboBox_ValveOption.SelectedIndex = 0;
      this.newComboBox_LoopNum.Items.AddRange((object[]) Enumerable.Range(1, 30).Select<int, string>((System.Func<int, string>) (i => i.ToString())).ToArray<string>());
      this.newComboBox_LoopNum.SelectedIndex = 0;
      this.RefreshDBList();
    }

    private void RefreshMaterial()
    {
      this.newComboBox_Company.Items.Clear();
      this.newComboBox_Company.Enabled = false;
      this.newComboBox_TradeName.Items.Clear();
      this.newComboBox_TradeName.Enabled = false;
      this.newTextBox_Search.Enabled = true;
      this.newTextBox_Type.Value = "";
      string strQuery = this.newTextBox_Search.Value;
      try
      {
        DataTable table = clsDefine.g_dsMaterial.Tables["System"];
        if (table == null)
          return;
        DataRow[] dataRowArray = strQuery != null && !(strQuery == "") ? table.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["TradeName"].ToString().ToUpper().Contains(strQuery.ToUpper()))).ToArray<DataRow>() : table.AsEnumerable().ToArray<DataRow>();
        if (dataRowArray.Length == 0)
          return;
        List<string> stringList = new List<string>();
        foreach (DataRow dataRow in dataRowArray)
        {
          if (!stringList.Contains(dataRow["Manufacturer"].ToString()))
            stringList.Add(dataRow["Manufacturer"].ToString());
        }
        stringList.Sort();
        this.newComboBox_Company.Items.AddRange((object[]) stringList.ToArray());
        this.newComboBox_Company.Enabled = true;
        this.newComboBox_Company.SelectedIndex = 0;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInput]RefreshMaterial):" + ex.Message));
      }
    }

    private void GetMaterialFromMoldflow()
    {
      this.newTextBox_Search.Enabled = false;
      this.newComboBox_Company.Items.Clear();
      this.newComboBox_TradeName.Items.Clear();
      Dictionary<string, string> materialData = clsHDMFLib.GetMaterialData();
      this.newComboBox_Company.Items.Add((object) materialData["Manufacturer"]);
      this.newComboBox_Company.SelectedIndex = 0;
      this.newComboBox_TradeName.Items.Add((object) (materialData["Tradename"] + " [ID:" + materialData["MaterialID"] + "]"));
      this.newComboBox_TradeName.SelectedIndex = 0;
      this.newTextBox_Type.Value = materialData["FamilyAbbreviation"];
      this.newTextBox_Density.Value = materialData["SolidDensity"];
    }

    private void RefreshDBList(string p_strCompany = null, string p_strItem = null)
    {
      this.listBox_DB.Items.Clear();
      try
      {
        DataRow[] array = clsDefine.g_dtInputDB.AsEnumerable().ToArray<DataRow>();
        if (array.Length == 0)
          return;
        List<string> stringList = new List<string>();
        foreach (DataRow dataRow in array)
          stringList.Add(dataRow["Name"].ToString());
        stringList.Sort();
        this.listBox_DB.Items.AddRange((object[]) stringList.ToArray());
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInput]RefreshDBList):" + ex.Message));
      }
    }

    private void listBox_DB_SelectedIndexChanged(object sender, EventArgs e)
    {
      string empty = string.Empty;
      try
      {
        DataRow dataRow = clsDefine.g_dtInputDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == this.listBox_DB.Text)).FirstOrDefault<DataRow>();
        if (dataRow == null)
          return;
        string[] strArray1 = dataRow["Data_FillTime"].ToString().Split(',');
        this.newTextBox_FillTime_Min.Value = strArray1[0];
        this.newTextBox_FillTime_Max.Value = strArray1[1];
        string[] strArray2 = dataRow["Data_VPSwitchOver"].ToString().Split(',');
        this.newTextBox_VPSwitchOver_Min.Value = strArray2[0];
        this.newTextBox_VPSwitchOver_Max.Value = strArray2[1];
        string[] strArray3 = dataRow["Data_PackingNumber"].ToString().Split(',');
        this.newTextBox_PackingNum_Min.Value = strArray3[0];
        this.newTextBox_PackingNum_Max.Value = strArray3[1];
        string[] strArray4 = dataRow["Data_CoolingTime"].ToString().Split(',');
        this.newTextBox_CoolingTime_Min.Value = strArray4[0];
        this.newTextBox_CoolingTime_Max.Value = strArray4[1];
        string[] strArray5 = dataRow["Data_CoolingInletTemperature"].ToString().Split(',');
        this.newTextBox_CoolingInletTemp_Min.Value = strArray5[0];
        this.newTextBox_CoolingInletTemp_Max.Value = strArray5[1];
        string[] strArray6 = dataRow["Data_MeltTemperature"].ToString().Split(',');
        this.newTextBox_MeltTemp_Min.Value = strArray6[0];
        this.newTextBox_MeltTemp_Max.Value = strArray6[1];
        string[] strArray7 = dataRow["Data_MoldTemperature"].ToString().Split(',');
        this.newTextBox_MoldTemp_Min.Value = strArray7[0];
        this.newTextBox_MoldTemp_Max.Value = strArray7[1];
        string[] strArray8 = dataRow["Data_GateNumber"].ToString().Split(',');
        this.newTextBox_GateNum_Min.Value = strArray8[0];
        this.newTextBox_GateNum_Max.Value = strArray8[1];
        this.RefreshMaterial();
        this.newComboBox_Company.SelectedIndex = this.newComboBox_Company.Items.IndexOf((object) dataRow["Mat_Company"].ToString());
        this.newComboBox_TradeName.SelectedIndex = this.newComboBox_TradeName.Items.IndexOf((object) (dataRow["Mat_TradeName"].ToString() + " [ID:" + dataRow["Mat_MaterialID"].ToString() + "]"));
        this.newTextBox_Type.Value = dataRow["Mat_Type"].ToString();
        this.newTextBox_Density.Value = dataRow["Mat_Density"].ToString();
        this.newTextBox_Deflection_Priority.Value = dataRow["Result_DeflectionAll"].ToString();
        this.newTextBox_VolShrinkage_Priority.Value = dataRow["Result_VolumetricShrinkage"].ToString();
        this.newTextBox_Sinkmark_Priority.Value = dataRow["Result_Sinkmark"].ToString();
        this.newTextBox_TempAtFlowFront_Priority.Value = dataRow["Result_TemperatureAtFlowFront"].ToString();
        this.newTextBox_CycleTime_Priority.Value = dataRow["Result_CycleTime"].ToString();
        this.newTextBox_InjPressure_Priority.Value = dataRow["Result_InjectionPressure"].ToString();
        this.newTextBox_Weight_Priority.Value = dataRow["Result_Weight"].ToString();
        this.newTextBox_ClampF_Priority.Value = dataRow["Result_ClampForce"].ToString();
        this.newComboBox_ValveOption.SelectedIndex = this.newComboBox_ValveOption.Items.IndexOf((object) dataRow["Option_ValveOption"].ToString());
        this.newComboBox_LoopNum.SelectedIndex = this.newComboBox_LoopNum.Items.IndexOf((object) dataRow["Option_LoopNum"].ToString());
        this.newTextBox_DeflectionLimit.Value = dataRow["Option_DeflectionLimit"].ToString();
        this.newTextBox_ClampForceLimit.Value = dataRow["Option_ClampForceLimit"].ToString();
        this.newTextBox_InjectionPressureLimit.Value = dataRow["Option_InjectionPressureLimit"].ToString();
        this.newComboBox_AnalysisType.SelectedIndex = this.newComboBox_AnalysisType.Items.IndexOf((object) dataRow["Analysis_Type"].ToString());
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInput]listBox_DB_SelectedIndexChanged):" + ex.Message));
      }
      try
      {
        if (clsDefine.g_dtInputDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == this.listBox_DB.Text)).FirstOrDefault<DataRow>() != null)
          ;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcessDB]listBox_DB_SelectedIndexChanged):" + ex.Message));
      }
    }

    private void frmInput_KeyDown(object sender, KeyEventArgs e)
    {
      if (e.KeyCode != Keys.Escape)
        return;
      this.Close();
    }

    private void newButton_Apply_NewClick(object sender, EventArgs e) => this.Apply();

    private void Apply()
    {
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      Dictionary<string, string> p_dicInput = new Dictionary<string, string>();
      string str1 = this.newTextBox_FillTime_Min.Value + "," + this.newTextBox_FillTime_Max.Value;
      p_dicInput.Add("Data_Fill_Time", str1);
      string str2 = this.newTextBox_VPSwitchOver_Min.Value + "," + this.newTextBox_VPSwitchOver_Max.Value;
      p_dicInput.Add("Data_VP_Switch_Over", str2);
      string str3 = this.newTextBox_PackingNum_Min.Value + "," + this.newTextBox_PackingNum_Max.Value;
      p_dicInput.Add("Data_Packing_Number", str3);
      string str4 = this.newTextBox_CoolingTime_Min.Value + "," + this.newTextBox_CoolingTime_Max.Value;
      p_dicInput.Add("Data_Cooling_Time", str4);
      string str5 = this.newTextBox_CoolingInletTemp_Min.Value + "," + this.newTextBox_CoolingInletTemp_Max.Value;
      p_dicInput.Add("Data_Cooling_Inlet_Temperature", str5);
      string str6 = this.newTextBox_MeltTemp_Min.Value + "," + this.newTextBox_MeltTemp_Max.Value;
      p_dicInput.Add("Data_Melt_Temperature", str6);
      string str7 = this.newTextBox_MoldTemp_Min.Value + "," + this.newTextBox_MoldTemp_Max.Value;
      p_dicInput.Add("Data_Mold_Temperature", str7);
      string str8 = this.newTextBox_GateNum_Min.Value + "," + this.newTextBox_GateNum_Max.Value;
      p_dicInput.Add("Data_Gate_Number", str8);
      string[] strArray = this.newComboBox_TradeName.Value.Split('[');
      string str9 = strArray[0].TrimEnd();
      string str10 = strArray[1].Replace("]", "").Replace("ID:", "");
      p_dicInput.Add("Mat_Company", this.newComboBox_Company.Value);
      p_dicInput.Add("Mat_Trade_Name", str9);
      p_dicInput.Add("Mat_MaterialID", str10);
      p_dicInput.Add("Mat_Type", this.newTextBox_Type.Value);
      p_dicInput.Add("Mat_Density", this.newTextBox_Density.Value);
      string str11 = this.newTextBox_Deflection_Priority.Value;
      p_dicInput.Add("Result_Deflection_All", str11);
      string str12 = this.newTextBox_VolShrinkage_Priority.Value;
      p_dicInput.Add("Result_Volumetric_Shrinkage_delta", str12);
      string str13 = this.newTextBox_Sinkmark_Priority.Value;
      p_dicInput.Add("Result_Sink_Mark", str13);
      string str14 = this.newTextBox_TempAtFlowFront_Priority.Value;
      p_dicInput.Add("Result_Temp_at_flow_front_delta", str14);
      string str15 = this.newTextBox_CycleTime_Priority.Value;
      p_dicInput.Add("Result_Cycle_Time", str15);
      string str16 = this.newTextBox_InjPressure_Priority.Value;
      p_dicInput.Add("Result_Injection_Pressure", str16);
      string str17 = this.newTextBox_Weight_Priority.Value;
      p_dicInput.Add("Result_Weight", str17);
      string str18 = this.newTextBox_ClampF_Priority.Value;
      p_dicInput.Add("Result_Clamp_Force", str18);
      string str19 = this.newComboBox_ValveOption.Value;
      p_dicInput.Add("Option_Valve_Option", str19);
      string str20 = this.newComboBox_LoopNum.Value;
      p_dicInput.Add("Option_Loop_Num", str20);
      string str21 = this.newTextBox_DeflectionLimit.Value;
      p_dicInput.Add("Option_Deflection_Limit", str21);
      string str22 = this.newTextBox_ClampForceLimit.Value;
      p_dicInput.Add("Option_Clamp_Force_Limit", str22);
      string str23 = this.newTextBox_InjectionPressureLimit.Value;
      p_dicInput.Add("Option_Injection_Pressure_Limit", str23);
      string str24 = this.newComboBox_AnalysisType.Value;
      p_dicInput.Add("Analysis_Type", str24);
      if (!clsInputData.CreateInputData(this.m_drStudy, p_dicInput))
        return;
      this.DialogResult = DialogResult.OK;
      this.Close();
    }

    private void newButton_Material_NewClick(object sender, EventArgs e)
    {
      NewButton newButton = sender as NewButton;
      if (newButton == this.newButton_System)
      {
        this.RefreshMaterial();
      }
      else
      {
        if (newButton != this.newButton_LoadMaterial)
          return;
        this.GetMaterialFromMoldflow();
      }
    }

    private void newComboBox_Company_SelectedIndexChanged(object sender, EventArgs e)
    {
      if (!this.newTextBox_Search.Enabled)
        return;
      this.newComboBox_TradeName.Items.Clear();
      this.newComboBox_TradeName.Enabled = false;
      this.newTextBox_Type.Value = "";
      string strQuery = this.newTextBox_Search.Value;
      string strManufacturer = this.newComboBox_Company.Value;
      try
      {
        DataTable table = clsDefine.g_dsMaterial.Tables["System"];
        DataRow[] dataRowArray = strQuery != null && !(strQuery == "") ? table.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Manufacturer"].ToString() == strManufacturer && Temp["TradeName"].ToString().ToUpper().Contains(strQuery.ToUpper()))).ToArray<DataRow>() : table.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Manufacturer"].ToString() == strManufacturer)).ToArray<DataRow>();
        if (dataRowArray.Length == 0)
          return;
        List<string> stringList = new List<string>();
        foreach (DataRow dataRow in dataRowArray)
        {
          string str = dataRow["TradeName"].ToString() + " [ID:" + dataRow["ID"].ToString() + "]";
          if (!stringList.Contains(str))
            stringList.Add(str);
        }
        stringList.Sort();
        this.newComboBox_TradeName.Items.AddRange((object[]) stringList.ToArray());
        this.newComboBox_TradeName.Enabled = true;
        this.newComboBox_TradeName.SelectedIndex = 0;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInput]newComboBox_Mat_Manufacturer_SelectedIndexChanged):" + ex.Message));
      }
    }

    private void newComboBox_TradeName_SelectedIndexChanged(object sender, EventArgs e)
    {
      if (!this.newTextBox_Search.Enabled)
        return;
      this.newTextBox_Type.Value = string.Empty;
      try
      {
        string strManufacturer = this.newComboBox_Company.Value;
        string strTradeName = "";
        string strMaterialID = "";
        string[] strArray = this.newComboBox_TradeName.Value.Split('[');
        strTradeName = strArray[0].TrimEnd();
        strMaterialID = strArray[1].Replace("]", "").Replace("ID:", "");
        DataTable table = clsDefine.g_dsMaterial.Tables["System"];
        DataRow dataRow = table.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Manufacturer"].ToString() == strManufacturer && Temp["TradeName"].ToString() == strTradeName && Temp["ID"].ToString() == strMaterialID)).FirstOrDefault<DataRow>();
        if (dataRow == null)
          return;
        this.newTextBox_Type.Value = dataRow["Familyabbreviation"].ToString();
        if (!table.Columns.Contains("MoldDensity"))
          return;
        this.newTextBox_Density.Value = dataRow["MoldDensity"].ToString();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInput]newComboBox_Mat_TradeName_SelectedIndexChanged):" + ex.Message));
      }
    }

    private void newTextBox_Search_TextBoxKeyUp(object sender, KeyEventArgs e)
    {
      if (e.KeyCode != Keys.Return)
        return;
      this.RefreshMaterial();
    }

    private void newButton_Prohibit_NewClick(object sender, EventArgs e)
    {
      NewButton newButton = sender as NewButton;
      if (newButton == this.newButton_SelectNodes)
      {
        this.SelectProhibitNodes();
      }
      else
      {
        if (newButton != this.newButton_ClearNodes)
          return;
        this.ClearProhibitNodes();
      }
    }

    private void SelectProhibitNodes()
    {
      string empty = string.Empty;
      List<string> stringList = new List<string>();
      clsHDMFLib.AssignLayer(clsHDMFLib.GetSelectedNodeID(), "Prohibit Nodes", "Mesh Nodes");
      clsHDMFLib.ChangeColorOfLayer("Prohibit Nodes", 1);
    }

    private void ClearProhibitNodes() => clsHDMFLib.AssignLayer(clsHDMFLib.GetSelectedNodeID(), "Nodes", "Mesh Nodes");

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.listBox_DB = new ListBox();
      this.label_DB_List = new Label();
      this.label_Data = new Label();
      this.label_Company = new Label();
      this.label_TradeName = new Label();
      this.label_Density = new Label();
      this.label_Material = new Label();
      this.newTextBox_Density = new NewTextBox();
      this.label_Result = new Label();
      this.label_InjPressure = new Label();
      this.label_CycleTime = new Label();
      this.label_Deflection = new Label();
      this.label_Weight = new Label();
      this.label_TempAtFlowFront = new Label();
      this.label_Sinkmark = new Label();
      this.label_VolShrinkage = new Label();
      this.newTextBox_InjPressure_Priority = new NewTextBox();
      this.newTextBox_Sinkmark_Priority = new NewTextBox();
      this.newTextBox_CycleTime_Priority = new NewTextBox();
      this.newTextBox_VolShrinkage_Priority = new NewTextBox();
      this.newTextBox_Deflection_Priority = new NewTextBox();
      this.newTextBox_TempAtFlowFront_Priority = new NewTextBox();
      this.newTextBox_Weight_Priority = new NewTextBox();
      this.newButton_Apply = new NewButton();
      this.newTextBox_MeltTemp_Min = new NewTextBox();
      this.newTextBox_CoolingInletTemp_Min = new NewTextBox();
      this.newTextBox_PackingNum_Min = new NewTextBox();
      this.newTextBox_CoolingTime_Min = new NewTextBox();
      this.newTextBox_VPSwitchOver_Min = new NewTextBox();
      this.newTextBox_FillTime_Min = new NewTextBox();
      this.newTextBox_CoolingInletTemp_Max = new NewTextBox();
      this.label_CoolingInletTemp = new Label();
      this.label_CoolingTime = new Label();
      this.label_FillTime = new Label();
      this.label_MeltTemp = new Label();
      this.newTextBox_PackingNum_Max = new NewTextBox();
      this.newTextBox_CoolingTime_Max = new NewTextBox();
      this.newTextBox_VPSwitchOver_Max = new NewTextBox();
      this.newTextBox_FillTime_Max = new NewTextBox();
      this.label_PackingNum = new Label();
      this.label_VPSwitchOver = new Label();
      this.newTextBox_MeltTemp_Max = new NewTextBox();
      this.newTextBox_MoldTemp_Min = new NewTextBox();
      this.newTextBox_MoldTemp_Max = new NewTextBox();
      this.label_MoldTemp = new Label();
      this.newTextBox_GateNum_Min = new NewTextBox();
      this.newTextBox_GateNum_Max = new NewTextBox();
      this.label_GateNum = new Label();
      this.newTextBox_Type = new NewTextBox();
      this.label_Type = new Label();
      this.newTextBox_ClampF_Priority = new NewTextBox();
      this.label_ClampF = new Label();
      this.label_AnalysisType = new Label();
      this.newComboBox_AnalysisType = new NewComboBox();
      this.label_Suji_SearchValue = new Label();
      this.label_SearchCond = new Label();
      this.label_Search = new Label();
      this.newComboBox_Company = new NewComboBox();
      this.newTextBox_Search = new NewTextBox();
      this.newComboBox_TradeName = new NewComboBox();
      this.panel1 = new Panel();
      this.newButton_LoadMaterial = new NewButton();
      this.newButton_System = new NewButton();
      this.label_Option = new Label();
      this.label_ValveOption = new Label();
      this.label_ClampForceLimit = new Label();
      this.label_DeflectionLimit = new Label();
      this.label_LoopNum = new Label();
      this.newComboBox_ValveOption = new NewComboBox();
      this.newComboBox_LoopNum = new NewComboBox();
      this.label_InjectionPressureLimit = new Label();
      this.newTextBox_DeflectionLimit = new NewTextBox();
      this.newTextBox_ClampForceLimit = new NewTextBox();
      this.newTextBox_InjectionPressureLimit = new NewTextBox();
      this.newButton_ClearNodes = new NewButton();
      this.newButton_SelectNodes = new NewButton();
      this.label_Prohibit = new Label();
      this.panel1.SuspendLayout();
      this.SuspendLayout();
      this.listBox_DB.FormattingEnabled = true;
      this.listBox_DB.ItemHeight = 15;
      this.listBox_DB.Location = new Point(5, 23);
      this.listBox_DB.Name = "listBox_DB";
      this.listBox_DB.SelectionMode = SelectionMode.MultiExtended;
      this.listBox_DB.Size = new Size(301, 829);
      this.listBox_DB.TabIndex = 3;
      this.listBox_DB.TabStop = false;
      this.listBox_DB.SelectedIndexChanged += new EventHandler(this.listBox_DB_SelectedIndexChanged);
      this.label_DB_List.BackColor = Color.FromArgb(229, 238, 248);
      this.label_DB_List.BorderStyle = BorderStyle.FixedSingle;
      this.label_DB_List.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_DB_List.ForeColor = Color.MidnightBlue;
      this.label_DB_List.Location = new Point(5, 5);
      this.label_DB_List.Name = "label_DB_List";
      this.label_DB_List.Size = new Size(301, 20);
      this.label_DB_List.TabIndex = 16;
      this.label_DB_List.Text = "DB 리스트";
      this.label_DB_List.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Data.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Data.BorderStyle = BorderStyle.FixedSingle;
      this.label_Data.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Data.ForeColor = Color.MidnightBlue;
      this.label_Data.Location = new Point(310, 313);
      this.label_Data.Name = "label_Data";
      this.label_Data.Size = new Size(495, 20);
      this.label_Data.TabIndex = 97;
      this.label_Data.Text = "Data";
      this.label_Data.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Company.BackColor = Color.Lavender;
      this.label_Company.BorderStyle = BorderStyle.FixedSingle;
      this.label_Company.Location = new Point(310, 218);
      this.label_Company.Name = "label_Company";
      this.label_Company.Size = new Size(248, 23);
      this.label_Company.TabIndex = 129;
      this.label_Company.Text = "Company";
      this.label_Company.TextAlign = ContentAlignment.MiddleCenter;
      this.label_TradeName.BackColor = Color.Lavender;
      this.label_TradeName.BorderStyle = BorderStyle.FixedSingle;
      this.label_TradeName.Location = new Point(557, 218);
      this.label_TradeName.Name = "label_TradeName";
      this.label_TradeName.Size = new Size(248, 23);
      this.label_TradeName.TabIndex = 132;
      this.label_TradeName.Text = "Trade Name";
      this.label_TradeName.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Density.BackColor = Color.Lavender;
      this.label_Density.BorderStyle = BorderStyle.FixedSingle;
      this.label_Density.Location = new Point(557, 262);
      this.label_Density.Name = "label_Density";
      this.label_Density.Size = new Size(248, 23);
      this.label_Density.TabIndex = 133;
      this.label_Density.Text = "Density";
      this.label_Density.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Material.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Material.BorderStyle = BorderStyle.FixedSingle;
      this.label_Material.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Material.ForeColor = Color.MidnightBlue;
      this.label_Material.Location = new Point(310, 110);
      this.label_Material.Name = "label_Material";
      this.label_Material.Size = new Size(495, 20);
      this.label_Material.TabIndex = 134;
      this.label_Material.Text = "Material";
      this.label_Material.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Density.BackColor = Color.White;
      this.newTextBox_Density.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Density.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Density.IsDigit = false;
      this.newTextBox_Density.Lines = new string[0];
      this.newTextBox_Density.Location = new Point(557, 283);
      this.newTextBox_Density.MultiLine = false;
      this.newTextBox_Density.Name = "newTextBox_Density";
      this.newTextBox_Density.ReadOnly = false;
      this.newTextBox_Density.Size = new Size(248, 23);
      this.newTextBox_Density.TabIndex = 135;
      this.newTextBox_Density.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Density.TextBoxBackColor = Color.White;
      this.newTextBox_Density.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Density.Value = "";
      this.label_Result.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Result.BorderStyle = BorderStyle.FixedSingle;
      this.label_Result.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Result.ForeColor = Color.MidnightBlue;
      this.label_Result.Location = new Point(310, 517);
      this.label_Result.Name = "label_Result";
      this.label_Result.Size = new Size(495, 20);
      this.label_Result.TabIndex = 138;
      this.label_Result.Text = "결과 우선 순위";
      this.label_Result.TextAlign = ContentAlignment.MiddleCenter;
      this.label_InjPressure.BackColor = Color.Lavender;
      this.label_InjPressure.BorderStyle = BorderStyle.FixedSingle;
      this.label_InjPressure.Location = new Point(310, 646);
      this.label_InjPressure.Name = "label_InjPressure";
      this.label_InjPressure.Size = new Size(247, 23);
      this.label_InjPressure.TabIndex = 154;
      this.label_InjPressure.Text = "Injection Pressure";
      this.label_InjPressure.TextAlign = ContentAlignment.MiddleCenter;
      this.label_CycleTime.BackColor = Color.Lavender;
      this.label_CycleTime.BorderStyle = BorderStyle.FixedSingle;
      this.label_CycleTime.Location = new Point(310, 624);
      this.label_CycleTime.Name = "label_CycleTime";
      this.label_CycleTime.Size = new Size(247, 23);
      this.label_CycleTime.TabIndex = 153;
      this.label_CycleTime.Text = "Cycle Time";
      this.label_CycleTime.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Deflection.BackColor = Color.Lavender;
      this.label_Deflection.BorderStyle = BorderStyle.FixedSingle;
      this.label_Deflection.Location = new Point(310, 536);
      this.label_Deflection.Name = "label_Deflection";
      this.label_Deflection.Size = new Size(247, 23);
      this.label_Deflection.TabIndex = 148;
      this.label_Deflection.Text = "Deflection All";
      this.label_Deflection.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Weight.BackColor = Color.Lavender;
      this.label_Weight.BorderStyle = BorderStyle.FixedSingle;
      this.label_Weight.Location = new Point(310, 668);
      this.label_Weight.Name = "label_Weight";
      this.label_Weight.Size = new Size(247, 23);
      this.label_Weight.TabIndex = 149;
      this.label_Weight.Text = "Weight";
      this.label_Weight.TextAlign = ContentAlignment.MiddleCenter;
      this.label_TempAtFlowFront.BackColor = Color.Lavender;
      this.label_TempAtFlowFront.BorderStyle = BorderStyle.FixedSingle;
      this.label_TempAtFlowFront.Location = new Point(310, 602);
      this.label_TempAtFlowFront.Name = "label_TempAtFlowFront";
      this.label_TempAtFlowFront.Size = new Size(247, 23);
      this.label_TempAtFlowFront.TabIndex = 151;
      this.label_TempAtFlowFront.Text = "Temperature at Flow Front";
      this.label_TempAtFlowFront.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Sinkmark.BackColor = Color.Lavender;
      this.label_Sinkmark.BorderStyle = BorderStyle.FixedSingle;
      this.label_Sinkmark.Location = new Point(310, 580);
      this.label_Sinkmark.Name = "label_Sinkmark";
      this.label_Sinkmark.Size = new Size(247, 23);
      this.label_Sinkmark.TabIndex = 155;
      this.label_Sinkmark.Text = "Sink mark";
      this.label_Sinkmark.TextAlign = ContentAlignment.MiddleCenter;
      this.label_VolShrinkage.BackColor = Color.Lavender;
      this.label_VolShrinkage.BorderStyle = BorderStyle.FixedSingle;
      this.label_VolShrinkage.Location = new Point(310, 558);
      this.label_VolShrinkage.Name = "label_VolShrinkage";
      this.label_VolShrinkage.Size = new Size(247, 23);
      this.label_VolShrinkage.TabIndex = 158;
      this.label_VolShrinkage.Text = "Volumetric Shrinkage";
      this.label_VolShrinkage.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_InjPressure_Priority.BackColor = SystemColors.Window;
      this.newTextBox_InjPressure_Priority.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_InjPressure_Priority.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_InjPressure_Priority.IsDigit = false;
      this.newTextBox_InjPressure_Priority.Lines = new string[0];
      this.newTextBox_InjPressure_Priority.Location = new Point(556, 646);
      this.newTextBox_InjPressure_Priority.MultiLine = false;
      this.newTextBox_InjPressure_Priority.Name = "newTextBox_InjPressure_Priority";
      this.newTextBox_InjPressure_Priority.ReadOnly = false;
      this.newTextBox_InjPressure_Priority.Size = new Size(249, 23);
      this.newTextBox_InjPressure_Priority.TabIndex = 168;
      this.newTextBox_InjPressure_Priority.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_InjPressure_Priority.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_InjPressure_Priority.TextForeColor = SystemColors.WindowText;
      this.newTextBox_InjPressure_Priority.Value = "";
      this.newTextBox_Sinkmark_Priority.BackColor = SystemColors.Window;
      this.newTextBox_Sinkmark_Priority.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Sinkmark_Priority.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Sinkmark_Priority.IsDigit = false;
      this.newTextBox_Sinkmark_Priority.Lines = new string[0];
      this.newTextBox_Sinkmark_Priority.Location = new Point(556, 580);
      this.newTextBox_Sinkmark_Priority.MultiLine = false;
      this.newTextBox_Sinkmark_Priority.Name = "newTextBox_Sinkmark_Priority";
      this.newTextBox_Sinkmark_Priority.ReadOnly = false;
      this.newTextBox_Sinkmark_Priority.Size = new Size(249, 23);
      this.newTextBox_Sinkmark_Priority.TabIndex = 164;
      this.newTextBox_Sinkmark_Priority.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Sinkmark_Priority.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Sinkmark_Priority.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Sinkmark_Priority.Value = "";
      this.newTextBox_CycleTime_Priority.BackColor = SystemColors.Window;
      this.newTextBox_CycleTime_Priority.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_CycleTime_Priority.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_CycleTime_Priority.IsDigit = false;
      this.newTextBox_CycleTime_Priority.Lines = new string[0];
      this.newTextBox_CycleTime_Priority.Location = new Point(556, 624);
      this.newTextBox_CycleTime_Priority.MultiLine = false;
      this.newTextBox_CycleTime_Priority.Name = "newTextBox_CycleTime_Priority";
      this.newTextBox_CycleTime_Priority.ReadOnly = false;
      this.newTextBox_CycleTime_Priority.Size = new Size(249, 23);
      this.newTextBox_CycleTime_Priority.TabIndex = 167;
      this.newTextBox_CycleTime_Priority.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_CycleTime_Priority.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_CycleTime_Priority.TextForeColor = SystemColors.WindowText;
      this.newTextBox_CycleTime_Priority.Value = "";
      this.newTextBox_VolShrinkage_Priority.BackColor = SystemColors.Window;
      this.newTextBox_VolShrinkage_Priority.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_VolShrinkage_Priority.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_VolShrinkage_Priority.IsDigit = false;
      this.newTextBox_VolShrinkage_Priority.Lines = new string[0];
      this.newTextBox_VolShrinkage_Priority.Location = new Point(556, 558);
      this.newTextBox_VolShrinkage_Priority.MultiLine = false;
      this.newTextBox_VolShrinkage_Priority.Name = "newTextBox_VolShrinkage_Priority";
      this.newTextBox_VolShrinkage_Priority.ReadOnly = false;
      this.newTextBox_VolShrinkage_Priority.Size = new Size(249, 23);
      this.newTextBox_VolShrinkage_Priority.TabIndex = 163;
      this.newTextBox_VolShrinkage_Priority.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_VolShrinkage_Priority.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_VolShrinkage_Priority.TextForeColor = SystemColors.WindowText;
      this.newTextBox_VolShrinkage_Priority.Value = "";
      this.newTextBox_Deflection_Priority.BackColor = SystemColors.Window;
      this.newTextBox_Deflection_Priority.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Deflection_Priority.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Deflection_Priority.IsDigit = false;
      this.newTextBox_Deflection_Priority.Lines = new string[0];
      this.newTextBox_Deflection_Priority.Location = new Point(556, 536);
      this.newTextBox_Deflection_Priority.MultiLine = false;
      this.newTextBox_Deflection_Priority.Name = "newTextBox_Deflection_Priority";
      this.newTextBox_Deflection_Priority.ReadOnly = false;
      this.newTextBox_Deflection_Priority.Size = new Size(249, 23);
      this.newTextBox_Deflection_Priority.TabIndex = 162;
      this.newTextBox_Deflection_Priority.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Deflection_Priority.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Deflection_Priority.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Deflection_Priority.Value = "";
      this.newTextBox_TempAtFlowFront_Priority.BackColor = SystemColors.Window;
      this.newTextBox_TempAtFlowFront_Priority.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_TempAtFlowFront_Priority.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_TempAtFlowFront_Priority.IsDigit = false;
      this.newTextBox_TempAtFlowFront_Priority.Lines = new string[0];
      this.newTextBox_TempAtFlowFront_Priority.Location = new Point(556, 602);
      this.newTextBox_TempAtFlowFront_Priority.MultiLine = false;
      this.newTextBox_TempAtFlowFront_Priority.Name = "newTextBox_TempAtFlowFront_Priority";
      this.newTextBox_TempAtFlowFront_Priority.ReadOnly = false;
      this.newTextBox_TempAtFlowFront_Priority.Size = new Size(249, 23);
      this.newTextBox_TempAtFlowFront_Priority.TabIndex = 170;
      this.newTextBox_TempAtFlowFront_Priority.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_TempAtFlowFront_Priority.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_TempAtFlowFront_Priority.TextForeColor = SystemColors.WindowText;
      this.newTextBox_TempAtFlowFront_Priority.Value = "";
      this.newTextBox_Weight_Priority.BackColor = SystemColors.Window;
      this.newTextBox_Weight_Priority.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Weight_Priority.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Weight_Priority.IsDigit = false;
      this.newTextBox_Weight_Priority.Lines = new string[0];
      this.newTextBox_Weight_Priority.Location = new Point(556, 668);
      this.newTextBox_Weight_Priority.MultiLine = false;
      this.newTextBox_Weight_Priority.Name = "newTextBox_Weight_Priority";
      this.newTextBox_Weight_Priority.ReadOnly = false;
      this.newTextBox_Weight_Priority.Size = new Size(249, 23);
      this.newTextBox_Weight_Priority.TabIndex = 171;
      this.newTextBox_Weight_Priority.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Weight_Priority.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Weight_Priority.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Weight_Priority.Value = "";
      this.newButton_Apply.ButtonBackColor = Color.White;
      this.newButton_Apply.ButtonText = "Input Data 적용";
      this.newButton_Apply.FlatBorderSize = 1;
      this.newButton_Apply.FlatStyle = FlatStyle.Flat;
      this.newButton_Apply.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Apply.ForeColor = Color.Navy;
      this.newButton_Apply.Image = (Image) Resources.OK;
      this.newButton_Apply.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Apply.Location = new Point(5, 857);
      this.newButton_Apply.Name = "newButton_Apply";
      this.newButton_Apply.Size = new Size(800, 28);
      this.newButton_Apply.TabIndex = 173;
      this.newButton_Apply.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Apply.NewClick += new EventHandler(this.newButton_Apply_NewClick);
      this.newTextBox_MeltTemp_Min.BackColor = SystemColors.Window;
      this.newTextBox_MeltTemp_Min.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_MeltTemp_Min.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_MeltTemp_Min.IsDigit = false;
      this.newTextBox_MeltTemp_Min.Lines = new string[0];
      this.newTextBox_MeltTemp_Min.Location = new Point(555, 442);
      this.newTextBox_MeltTemp_Min.MultiLine = false;
      this.newTextBox_MeltTemp_Min.Name = "newTextBox_MeltTemp_Min";
      this.newTextBox_MeltTemp_Min.ReadOnly = false;
      this.newTextBox_MeltTemp_Min.Size = new Size(126, 23);
      this.newTextBox_MeltTemp_Min.TabIndex = 192;
      this.newTextBox_MeltTemp_Min.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_MeltTemp_Min.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_MeltTemp_Min.TextForeColor = SystemColors.WindowText;
      this.newTextBox_MeltTemp_Min.Value = "";
      this.newTextBox_CoolingInletTemp_Min.BackColor = SystemColors.Window;
      this.newTextBox_CoolingInletTemp_Min.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_CoolingInletTemp_Min.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_CoolingInletTemp_Min.IsDigit = false;
      this.newTextBox_CoolingInletTemp_Min.Lines = new string[0];
      this.newTextBox_CoolingInletTemp_Min.Location = new Point(555, 420);
      this.newTextBox_CoolingInletTemp_Min.MultiLine = false;
      this.newTextBox_CoolingInletTemp_Min.Name = "newTextBox_CoolingInletTemp_Min";
      this.newTextBox_CoolingInletTemp_Min.ReadOnly = false;
      this.newTextBox_CoolingInletTemp_Min.Size = new Size(126, 23);
      this.newTextBox_CoolingInletTemp_Min.TabIndex = 190;
      this.newTextBox_CoolingInletTemp_Min.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_CoolingInletTemp_Min.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_CoolingInletTemp_Min.TextForeColor = SystemColors.WindowText;
      this.newTextBox_CoolingInletTemp_Min.Value = "";
      this.newTextBox_PackingNum_Min.BackColor = SystemColors.Window;
      this.newTextBox_PackingNum_Min.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_PackingNum_Min.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_PackingNum_Min.IsDigit = false;
      this.newTextBox_PackingNum_Min.Lines = new string[0];
      this.newTextBox_PackingNum_Min.Location = new Point(555, 376);
      this.newTextBox_PackingNum_Min.MultiLine = false;
      this.newTextBox_PackingNum_Min.Name = "newTextBox_PackingNum_Min";
      this.newTextBox_PackingNum_Min.ReadOnly = false;
      this.newTextBox_PackingNum_Min.Size = new Size(126, 23);
      this.newTextBox_PackingNum_Min.TabIndex = 188;
      this.newTextBox_PackingNum_Min.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_PackingNum_Min.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_PackingNum_Min.TextForeColor = SystemColors.WindowText;
      this.newTextBox_PackingNum_Min.Value = "";
      this.newTextBox_CoolingTime_Min.BackColor = SystemColors.Window;
      this.newTextBox_CoolingTime_Min.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_CoolingTime_Min.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_CoolingTime_Min.IsDigit = false;
      this.newTextBox_CoolingTime_Min.Lines = new string[0];
      this.newTextBox_CoolingTime_Min.Location = new Point(555, 398);
      this.newTextBox_CoolingTime_Min.MultiLine = false;
      this.newTextBox_CoolingTime_Min.Name = "newTextBox_CoolingTime_Min";
      this.newTextBox_CoolingTime_Min.ReadOnly = false;
      this.newTextBox_CoolingTime_Min.Size = new Size(126, 23);
      this.newTextBox_CoolingTime_Min.TabIndex = 189;
      this.newTextBox_CoolingTime_Min.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_CoolingTime_Min.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_CoolingTime_Min.TextForeColor = SystemColors.WindowText;
      this.newTextBox_CoolingTime_Min.Value = "";
      this.newTextBox_VPSwitchOver_Min.BackColor = SystemColors.Window;
      this.newTextBox_VPSwitchOver_Min.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_VPSwitchOver_Min.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_VPSwitchOver_Min.IsDigit = false;
      this.newTextBox_VPSwitchOver_Min.Lines = new string[0];
      this.newTextBox_VPSwitchOver_Min.Location = new Point(555, 354);
      this.newTextBox_VPSwitchOver_Min.MultiLine = false;
      this.newTextBox_VPSwitchOver_Min.Name = "newTextBox_VPSwitchOver_Min";
      this.newTextBox_VPSwitchOver_Min.ReadOnly = false;
      this.newTextBox_VPSwitchOver_Min.Size = new Size(126, 23);
      this.newTextBox_VPSwitchOver_Min.TabIndex = 187;
      this.newTextBox_VPSwitchOver_Min.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_VPSwitchOver_Min.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_VPSwitchOver_Min.TextForeColor = SystemColors.WindowText;
      this.newTextBox_VPSwitchOver_Min.Value = "";
      this.newTextBox_FillTime_Min.BackColor = SystemColors.Window;
      this.newTextBox_FillTime_Min.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_FillTime_Min.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_FillTime_Min.IsDigit = false;
      this.newTextBox_FillTime_Min.Lines = new string[0];
      this.newTextBox_FillTime_Min.Location = new Point(555, 332);
      this.newTextBox_FillTime_Min.MultiLine = false;
      this.newTextBox_FillTime_Min.Name = "newTextBox_FillTime_Min";
      this.newTextBox_FillTime_Min.ReadOnly = false;
      this.newTextBox_FillTime_Min.Size = new Size(126, 23);
      this.newTextBox_FillTime_Min.TabIndex = 186;
      this.newTextBox_FillTime_Min.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_FillTime_Min.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_FillTime_Min.TextForeColor = SystemColors.WindowText;
      this.newTextBox_FillTime_Min.Value = "";
      this.newTextBox_CoolingInletTemp_Max.BackColor = SystemColors.Window;
      this.newTextBox_CoolingInletTemp_Max.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_CoolingInletTemp_Max.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_CoolingInletTemp_Max.IsDigit = false;
      this.newTextBox_CoolingInletTemp_Max.Lines = new string[0];
      this.newTextBox_CoolingInletTemp_Max.Location = new Point(679, 420);
      this.newTextBox_CoolingInletTemp_Max.MultiLine = false;
      this.newTextBox_CoolingInletTemp_Max.Name = "newTextBox_CoolingInletTemp_Max";
      this.newTextBox_CoolingInletTemp_Max.ReadOnly = false;
      this.newTextBox_CoolingInletTemp_Max.Size = new Size(126, 23);
      this.newTextBox_CoolingInletTemp_Max.TabIndex = 185;
      this.newTextBox_CoolingInletTemp_Max.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_CoolingInletTemp_Max.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_CoolingInletTemp_Max.TextForeColor = SystemColors.WindowText;
      this.newTextBox_CoolingInletTemp_Max.Value = "";
      this.label_CoolingInletTemp.BackColor = Color.Lavender;
      this.label_CoolingInletTemp.BorderStyle = BorderStyle.FixedSingle;
      this.label_CoolingInletTemp.Location = new Point(310, 420);
      this.label_CoolingInletTemp.Name = "label_CoolingInletTemp";
      this.label_CoolingInletTemp.Size = new Size(247, 23);
      this.label_CoolingInletTemp.TabIndex = 180;
      this.label_CoolingInletTemp.Text = "Cooling Inlet Temperature";
      this.label_CoolingInletTemp.TextAlign = ContentAlignment.MiddleCenter;
      this.label_CoolingTime.BackColor = Color.Lavender;
      this.label_CoolingTime.BorderStyle = BorderStyle.FixedSingle;
      this.label_CoolingTime.Location = new Point(310, 398);
      this.label_CoolingTime.Name = "label_CoolingTime";
      this.label_CoolingTime.Size = new Size(247, 23);
      this.label_CoolingTime.TabIndex = 179;
      this.label_CoolingTime.Text = "Cooling Time";
      this.label_CoolingTime.TextAlign = ContentAlignment.MiddleCenter;
      this.label_FillTime.BackColor = Color.Lavender;
      this.label_FillTime.BorderStyle = BorderStyle.FixedSingle;
      this.label_FillTime.Location = new Point(310, 332);
      this.label_FillTime.Name = "label_FillTime";
      this.label_FillTime.Size = new Size(247, 23);
      this.label_FillTime.TabIndex = 176;
      this.label_FillTime.Text = "Fill Time";
      this.label_FillTime.TextAlign = ContentAlignment.MiddleCenter;
      this.label_MeltTemp.BackColor = Color.Lavender;
      this.label_MeltTemp.BorderStyle = BorderStyle.FixedSingle;
      this.label_MeltTemp.Location = new Point(310, 442);
      this.label_MeltTemp.Name = "label_MeltTemp";
      this.label_MeltTemp.Size = new Size(247, 23);
      this.label_MeltTemp.TabIndex = 177;
      this.label_MeltTemp.Text = "Melt Temperature";
      this.label_MeltTemp.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_PackingNum_Max.BackColor = SystemColors.Window;
      this.newTextBox_PackingNum_Max.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_PackingNum_Max.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_PackingNum_Max.IsDigit = false;
      this.newTextBox_PackingNum_Max.Lines = new string[0];
      this.newTextBox_PackingNum_Max.Location = new Point(679, 376);
      this.newTextBox_PackingNum_Max.MultiLine = false;
      this.newTextBox_PackingNum_Max.Name = "newTextBox_PackingNum_Max";
      this.newTextBox_PackingNum_Max.ReadOnly = false;
      this.newTextBox_PackingNum_Max.Size = new Size(126, 23);
      this.newTextBox_PackingNum_Max.TabIndex = 174;
      this.newTextBox_PackingNum_Max.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_PackingNum_Max.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_PackingNum_Max.TextForeColor = SystemColors.WindowText;
      this.newTextBox_PackingNum_Max.Value = "";
      this.newTextBox_CoolingTime_Max.BackColor = SystemColors.Window;
      this.newTextBox_CoolingTime_Max.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_CoolingTime_Max.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_CoolingTime_Max.IsDigit = false;
      this.newTextBox_CoolingTime_Max.Lines = new string[0];
      this.newTextBox_CoolingTime_Max.Location = new Point(679, 398);
      this.newTextBox_CoolingTime_Max.MultiLine = false;
      this.newTextBox_CoolingTime_Max.Name = "newTextBox_CoolingTime_Max";
      this.newTextBox_CoolingTime_Max.ReadOnly = false;
      this.newTextBox_CoolingTime_Max.Size = new Size(126, 23);
      this.newTextBox_CoolingTime_Max.TabIndex = 184;
      this.newTextBox_CoolingTime_Max.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_CoolingTime_Max.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_CoolingTime_Max.TextForeColor = SystemColors.WindowText;
      this.newTextBox_CoolingTime_Max.Value = "";
      this.newTextBox_VPSwitchOver_Max.BackColor = SystemColors.Window;
      this.newTextBox_VPSwitchOver_Max.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_VPSwitchOver_Max.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_VPSwitchOver_Max.IsDigit = false;
      this.newTextBox_VPSwitchOver_Max.Lines = new string[0];
      this.newTextBox_VPSwitchOver_Max.Location = new Point(679, 354);
      this.newTextBox_VPSwitchOver_Max.MultiLine = false;
      this.newTextBox_VPSwitchOver_Max.Name = "newTextBox_VPSwitchOver_Max";
      this.newTextBox_VPSwitchOver_Max.ReadOnly = false;
      this.newTextBox_VPSwitchOver_Max.Size = new Size(126, 23);
      this.newTextBox_VPSwitchOver_Max.TabIndex = 173;
      this.newTextBox_VPSwitchOver_Max.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_VPSwitchOver_Max.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_VPSwitchOver_Max.TextForeColor = SystemColors.WindowText;
      this.newTextBox_VPSwitchOver_Max.Value = "";
      this.newTextBox_FillTime_Max.BackColor = SystemColors.Window;
      this.newTextBox_FillTime_Max.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_FillTime_Max.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_FillTime_Max.IsDigit = false;
      this.newTextBox_FillTime_Max.Lines = new string[0];
      this.newTextBox_FillTime_Max.Location = new Point(679, 332);
      this.newTextBox_FillTime_Max.MultiLine = false;
      this.newTextBox_FillTime_Max.Name = "newTextBox_FillTime_Max";
      this.newTextBox_FillTime_Max.ReadOnly = false;
      this.newTextBox_FillTime_Max.Size = new Size(126, 23);
      this.newTextBox_FillTime_Max.TabIndex = 172;
      this.newTextBox_FillTime_Max.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_FillTime_Max.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_FillTime_Max.TextForeColor = SystemColors.WindowText;
      this.newTextBox_FillTime_Max.Value = "";
      this.label_PackingNum.BackColor = Color.Lavender;
      this.label_PackingNum.BorderStyle = BorderStyle.FixedSingle;
      this.label_PackingNum.Location = new Point(310, 376);
      this.label_PackingNum.Name = "label_PackingNum";
      this.label_PackingNum.Size = new Size(247, 23);
      this.label_PackingNum.TabIndex = 181;
      this.label_PackingNum.Text = "Packing Number";
      this.label_PackingNum.TextAlign = ContentAlignment.MiddleCenter;
      this.label_VPSwitchOver.BackColor = Color.Lavender;
      this.label_VPSwitchOver.BorderStyle = BorderStyle.FixedSingle;
      this.label_VPSwitchOver.Location = new Point(310, 354);
      this.label_VPSwitchOver.Name = "label_VPSwitchOver";
      this.label_VPSwitchOver.Size = new Size(247, 23);
      this.label_VPSwitchOver.TabIndex = 183;
      this.label_VPSwitchOver.Text = "V/P Switch Over";
      this.label_VPSwitchOver.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_MeltTemp_Max.BackColor = SystemColors.Window;
      this.newTextBox_MeltTemp_Max.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_MeltTemp_Max.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_MeltTemp_Max.IsDigit = false;
      this.newTextBox_MeltTemp_Max.Lines = new string[0];
      this.newTextBox_MeltTemp_Max.Location = new Point(679, 442);
      this.newTextBox_MeltTemp_Max.MultiLine = false;
      this.newTextBox_MeltTemp_Max.Name = "newTextBox_MeltTemp_Max";
      this.newTextBox_MeltTemp_Max.ReadOnly = false;
      this.newTextBox_MeltTemp_Max.Size = new Size(126, 23);
      this.newTextBox_MeltTemp_Max.TabIndex = 194;
      this.newTextBox_MeltTemp_Max.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_MeltTemp_Max.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_MeltTemp_Max.TextForeColor = SystemColors.WindowText;
      this.newTextBox_MeltTemp_Max.Value = "";
      this.newTextBox_MoldTemp_Min.BackColor = SystemColors.Window;
      this.newTextBox_MoldTemp_Min.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_MoldTemp_Min.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_MoldTemp_Min.IsDigit = false;
      this.newTextBox_MoldTemp_Min.Lines = new string[0];
      this.newTextBox_MoldTemp_Min.Location = new Point(555, 464);
      this.newTextBox_MoldTemp_Min.MultiLine = false;
      this.newTextBox_MoldTemp_Min.Name = "newTextBox_MoldTemp_Min";
      this.newTextBox_MoldTemp_Min.ReadOnly = false;
      this.newTextBox_MoldTemp_Min.Size = new Size(126, 23);
      this.newTextBox_MoldTemp_Min.TabIndex = 196;
      this.newTextBox_MoldTemp_Min.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_MoldTemp_Min.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_MoldTemp_Min.TextForeColor = SystemColors.WindowText;
      this.newTextBox_MoldTemp_Min.Value = "";
      this.newTextBox_MoldTemp_Max.BackColor = SystemColors.Window;
      this.newTextBox_MoldTemp_Max.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_MoldTemp_Max.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_MoldTemp_Max.IsDigit = false;
      this.newTextBox_MoldTemp_Max.Lines = new string[0];
      this.newTextBox_MoldTemp_Max.Location = new Point(679, 464);
      this.newTextBox_MoldTemp_Max.MultiLine = false;
      this.newTextBox_MoldTemp_Max.Name = "newTextBox_MoldTemp_Max";
      this.newTextBox_MoldTemp_Max.ReadOnly = false;
      this.newTextBox_MoldTemp_Max.Size = new Size(126, 23);
      this.newTextBox_MoldTemp_Max.TabIndex = 197;
      this.newTextBox_MoldTemp_Max.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_MoldTemp_Max.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_MoldTemp_Max.TextForeColor = SystemColors.WindowText;
      this.newTextBox_MoldTemp_Max.Value = "";
      this.label_MoldTemp.BackColor = Color.Lavender;
      this.label_MoldTemp.BorderStyle = BorderStyle.FixedSingle;
      this.label_MoldTemp.Location = new Point(310, 464);
      this.label_MoldTemp.Name = "label_MoldTemp";
      this.label_MoldTemp.Size = new Size(247, 23);
      this.label_MoldTemp.TabIndex = 195;
      this.label_MoldTemp.Text = "Mold Temperature";
      this.label_MoldTemp.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_GateNum_Min.BackColor = SystemColors.Window;
      this.newTextBox_GateNum_Min.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_GateNum_Min.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_GateNum_Min.IsDigit = false;
      this.newTextBox_GateNum_Min.Lines = new string[0];
      this.newTextBox_GateNum_Min.Location = new Point(555, 486);
      this.newTextBox_GateNum_Min.MultiLine = false;
      this.newTextBox_GateNum_Min.Name = "newTextBox_GateNum_Min";
      this.newTextBox_GateNum_Min.ReadOnly = false;
      this.newTextBox_GateNum_Min.Size = new Size(126, 23);
      this.newTextBox_GateNum_Min.TabIndex = 199;
      this.newTextBox_GateNum_Min.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_GateNum_Min.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_GateNum_Min.TextForeColor = SystemColors.WindowText;
      this.newTextBox_GateNum_Min.Value = "";
      this.newTextBox_GateNum_Max.BackColor = SystemColors.Window;
      this.newTextBox_GateNum_Max.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_GateNum_Max.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_GateNum_Max.IsDigit = false;
      this.newTextBox_GateNum_Max.Lines = new string[0];
      this.newTextBox_GateNum_Max.Location = new Point(679, 486);
      this.newTextBox_GateNum_Max.MultiLine = false;
      this.newTextBox_GateNum_Max.Name = "newTextBox_GateNum_Max";
      this.newTextBox_GateNum_Max.ReadOnly = false;
      this.newTextBox_GateNum_Max.Size = new Size(126, 23);
      this.newTextBox_GateNum_Max.TabIndex = 200;
      this.newTextBox_GateNum_Max.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_GateNum_Max.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_GateNum_Max.TextForeColor = SystemColors.WindowText;
      this.newTextBox_GateNum_Max.Value = "";
      this.label_GateNum.BackColor = Color.Lavender;
      this.label_GateNum.BorderStyle = BorderStyle.FixedSingle;
      this.label_GateNum.Location = new Point(310, 486);
      this.label_GateNum.Name = "label_GateNum";
      this.label_GateNum.Size = new Size(247, 23);
      this.label_GateNum.TabIndex = 198;
      this.label_GateNum.Text = "Gate Number";
      this.label_GateNum.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Type.BackColor = Color.White;
      this.newTextBox_Type.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Type.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Type.IsDigit = false;
      this.newTextBox_Type.Lines = new string[0];
      this.newTextBox_Type.Location = new Point(310, 283);
      this.newTextBox_Type.MultiLine = false;
      this.newTextBox_Type.Name = "newTextBox_Type";
      this.newTextBox_Type.ReadOnly = false;
      this.newTextBox_Type.Size = new Size(248, 23);
      this.newTextBox_Type.TabIndex = 202;
      this.newTextBox_Type.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Type.TextBoxBackColor = Color.White;
      this.newTextBox_Type.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Type.Value = "";
      this.label_Type.BackColor = Color.Lavender;
      this.label_Type.BorderStyle = BorderStyle.FixedSingle;
      this.label_Type.Location = new Point(310, 262);
      this.label_Type.Name = "label_Type";
      this.label_Type.Size = new Size(248, 23);
      this.label_Type.TabIndex = 201;
      this.label_Type.Text = "Type";
      this.label_Type.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_ClampF_Priority.BackColor = SystemColors.Window;
      this.newTextBox_ClampF_Priority.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_ClampF_Priority.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_ClampF_Priority.IsDigit = false;
      this.newTextBox_ClampF_Priority.Lines = new string[0];
      this.newTextBox_ClampF_Priority.Location = new Point(556, 690);
      this.newTextBox_ClampF_Priority.MultiLine = false;
      this.newTextBox_ClampF_Priority.Name = "newTextBox_ClampF_Priority";
      this.newTextBox_ClampF_Priority.ReadOnly = false;
      this.newTextBox_ClampF_Priority.Size = new Size(249, 23);
      this.newTextBox_ClampF_Priority.TabIndex = 205;
      this.newTextBox_ClampF_Priority.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_ClampF_Priority.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_ClampF_Priority.TextForeColor = SystemColors.WindowText;
      this.newTextBox_ClampF_Priority.Value = "";
      this.label_ClampF.BackColor = Color.Lavender;
      this.label_ClampF.BorderStyle = BorderStyle.FixedSingle;
      this.label_ClampF.Location = new Point(310, 690);
      this.label_ClampF.Name = "label_ClampF";
      this.label_ClampF.Size = new Size(247, 23);
      this.label_ClampF.TabIndex = 203;
      this.label_ClampF.Text = "Clamp force";
      this.label_ClampF.TextAlign = ContentAlignment.MiddleCenter;
      this.label_AnalysisType.BackColor = Color.FromArgb(229, 238, 248);
      this.label_AnalysisType.BorderStyle = BorderStyle.FixedSingle;
      this.label_AnalysisType.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_AnalysisType.ForeColor = Color.MidnightBlue;
      this.label_AnalysisType.Location = new Point(310, 5);
      this.label_AnalysisType.Name = "label_AnalysisType";
      this.label_AnalysisType.Size = new Size(495, 20);
      this.label_AnalysisType.TabIndex = 207;
      this.label_AnalysisType.Text = "Analysis Type";
      this.label_AnalysisType.TextAlign = ContentAlignment.MiddleCenter;
      this.newComboBox_AnalysisType.BackColor = Color.White;
      this.newComboBox_AnalysisType.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_AnalysisType.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_AnalysisType.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_AnalysisType.isSameSelect = false;
      this.newComboBox_AnalysisType.Location = new Point(310, 24);
      this.newComboBox_AnalysisType.Name = "newComboBox_AnalysisType";
      this.newComboBox_AnalysisType.SelectedIndex = -1;
      this.newComboBox_AnalysisType.Size = new Size(495, 23);
      this.newComboBox_AnalysisType.TabIndex = 206;
      this.newComboBox_AnalysisType.TabStop = false;
      this.newComboBox_AnalysisType.TextAlign = HorizontalAlignment.Center;
      this.newComboBox_AnalysisType.Value = "";
      this.label_Suji_SearchValue.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Suji_SearchValue.BorderStyle = BorderStyle.FixedSingle;
      this.label_Suji_SearchValue.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Suji_SearchValue.ForeColor = Color.MidnightBlue;
      this.label_Suji_SearchValue.Location = new Point(310, 199);
      this.label_Suji_SearchValue.Name = "label_Suji_SearchValue";
      this.label_Suji_SearchValue.Size = new Size(495, 20);
      this.label_Suji_SearchValue.TabIndex = 208;
      this.label_Suji_SearchValue.Text = "검색 값";
      this.label_Suji_SearchValue.TextAlign = ContentAlignment.MiddleLeft;
      this.label_SearchCond.BackColor = Color.FromArgb(229, 238, 248);
      this.label_SearchCond.BorderStyle = BorderStyle.FixedSingle;
      this.label_SearchCond.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_SearchCond.ForeColor = Color.MidnightBlue;
      this.label_SearchCond.Location = new Point(310, 158);
      this.label_SearchCond.Name = "label_SearchCond";
      this.label_SearchCond.Size = new Size(495, 20);
      this.label_SearchCond.TabIndex = 209;
      this.label_SearchCond.Text = "검색 조건";
      this.label_SearchCond.TextAlign = ContentAlignment.MiddleLeft;
      this.label_Search.BackColor = Color.Lavender;
      this.label_Search.BorderStyle = BorderStyle.FixedSingle;
      this.label_Search.Location = new Point(310, 177);
      this.label_Search.Name = "label_Search";
      this.label_Search.Size = new Size(100, 23);
      this.label_Search.TabIndex = 213;
      this.label_Search.Text = "Trade Name";
      this.label_Search.TextAlign = ContentAlignment.MiddleCenter;
      this.newComboBox_Company.BackColor = Color.White;
      this.newComboBox_Company.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Company.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_Company.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Company.isSameSelect = false;
      this.newComboBox_Company.Location = new Point(310, 240);
      this.newComboBox_Company.Name = "newComboBox_Company";
      this.newComboBox_Company.SelectedIndex = -1;
      this.newComboBox_Company.Size = new Size(248, 23);
      this.newComboBox_Company.TabIndex = 217;
      this.newComboBox_Company.TabStop = false;
      this.newComboBox_Company.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Company.Value = "";
      this.newComboBox_Company.SelectedIndexChanged += new EventHandler(this.newComboBox_Company_SelectedIndexChanged);
      this.newTextBox_Search.BackColor = SystemColors.Window;
      this.newTextBox_Search.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Search.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Search.IsDigit = false;
      this.newTextBox_Search.Lines = new string[0];
      this.newTextBox_Search.Location = new Point(409, 177);
      this.newTextBox_Search.MultiLine = false;
      this.newTextBox_Search.Name = "newTextBox_Search";
      this.newTextBox_Search.ReadOnly = false;
      this.newTextBox_Search.Size = new Size(396, 23);
      this.newTextBox_Search.TabIndex = 216;
      this.newTextBox_Search.TextAlign = HorizontalAlignment.Left;
      this.newTextBox_Search.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Search.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Search.Value = "";
      this.newTextBox_Search.TextBoxKeyUp += new KeyEventHandler(this.newTextBox_Search_TextBoxKeyUp);
      this.newComboBox_TradeName.BackColor = Color.White;
      this.newComboBox_TradeName.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_TradeName.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_TradeName.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_TradeName.isSameSelect = false;
      this.newComboBox_TradeName.Location = new Point(557, 240);
      this.newComboBox_TradeName.Name = "newComboBox_TradeName";
      this.newComboBox_TradeName.SelectedIndex = -1;
      this.newComboBox_TradeName.Size = new Size(248, 23);
      this.newComboBox_TradeName.TabIndex = 218;
      this.newComboBox_TradeName.TabStop = false;
      this.newComboBox_TradeName.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_TradeName.Value = "";
      this.newComboBox_TradeName.SelectedIndexChanged += new EventHandler(this.newComboBox_TradeName_SelectedIndexChanged);
      this.panel1.BorderStyle = BorderStyle.FixedSingle;
      this.panel1.Controls.Add((Control) this.newButton_LoadMaterial);
      this.panel1.Controls.Add((Control) this.newButton_System);
      this.panel1.Location = new Point(310, 129);
      this.panel1.Name = "panel1";
      this.panel1.Size = new Size(495, 30);
      this.panel1.TabIndex = 220;
      this.newButton_LoadMaterial.ButtonBackColor = Color.White;
      this.newButton_LoadMaterial.ButtonText = "설정값 가져오기";
      this.newButton_LoadMaterial.FlatBorderSize = 1;
      this.newButton_LoadMaterial.FlatStyle = FlatStyle.Flat;
      this.newButton_LoadMaterial.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_LoadMaterial.ForeColor = Color.Navy;
      this.newButton_LoadMaterial.Image = (Image) null;
      this.newButton_LoadMaterial.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_LoadMaterial.Location = new Point(246, -1);
      this.newButton_LoadMaterial.Name = "newButton_LoadMaterial";
      this.newButton_LoadMaterial.Size = new Size(248, 30);
      this.newButton_LoadMaterial.TabIndex = 74;
      this.newButton_LoadMaterial.TabStop = false;
      this.newButton_LoadMaterial.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_LoadMaterial.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_LoadMaterial.NewClick += new EventHandler(this.newButton_Material_NewClick);
      this.newButton_System.ButtonBackColor = Color.White;
      this.newButton_System.ButtonText = "시스템";
      this.newButton_System.FlatBorderSize = 1;
      this.newButton_System.FlatStyle = FlatStyle.Flat;
      this.newButton_System.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_System.ForeColor = Color.Navy;
      this.newButton_System.Image = (Image) null;
      this.newButton_System.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_System.Location = new Point(-1, -1);
      this.newButton_System.Name = "newButton_System";
      this.newButton_System.Size = new Size(248, 30);
      this.newButton_System.TabIndex = 73;
      this.newButton_System.TabStop = false;
      this.newButton_System.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_System.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_System.NewClick += new EventHandler(this.newButton_Material_NewClick);
      this.label_Option.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Option.BorderStyle = BorderStyle.FixedSingle;
      this.label_Option.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Option.ForeColor = Color.MidnightBlue;
      this.label_Option.Location = new Point(310, 722);
      this.label_Option.Name = "label_Option";
      this.label_Option.Size = new Size(495, 20);
      this.label_Option.TabIndex = 221;
      this.label_Option.Text = "옵션";
      this.label_Option.TextAlign = ContentAlignment.MiddleCenter;
      this.label_ValveOption.BackColor = Color.Lavender;
      this.label_ValveOption.BorderStyle = BorderStyle.FixedSingle;
      this.label_ValveOption.Location = new Point(310, 741);
      this.label_ValveOption.Name = "label_ValveOption";
      this.label_ValveOption.Size = new Size(247, 23);
      this.label_ValveOption.TabIndex = 222;
      this.label_ValveOption.Text = "Valve Option";
      this.label_ValveOption.TextAlign = ContentAlignment.MiddleCenter;
      this.label_ClampForceLimit.BackColor = Color.Lavender;
      this.label_ClampForceLimit.BorderStyle = BorderStyle.FixedSingle;
      this.label_ClampForceLimit.Location = new Point(310, 807);
      this.label_ClampForceLimit.Name = "label_ClampForceLimit";
      this.label_ClampForceLimit.Size = new Size(247, 23);
      this.label_ClampForceLimit.TabIndex = 223;
      this.label_ClampForceLimit.Text = "형체력 제한 값";
      this.label_ClampForceLimit.TextAlign = ContentAlignment.MiddleCenter;
      this.label_DeflectionLimit.BackColor = Color.Lavender;
      this.label_DeflectionLimit.BorderStyle = BorderStyle.FixedSingle;
      this.label_DeflectionLimit.Location = new Point(310, 785);
      this.label_DeflectionLimit.Name = "label_DeflectionLimit";
      this.label_DeflectionLimit.Size = new Size(247, 23);
      this.label_DeflectionLimit.TabIndex = 224;
      this.label_DeflectionLimit.Text = "변형 제한 값";
      this.label_DeflectionLimit.TextAlign = ContentAlignment.MiddleCenter;
      this.label_LoopNum.BackColor = Color.Lavender;
      this.label_LoopNum.BorderStyle = BorderStyle.FixedSingle;
      this.label_LoopNum.Location = new Point(310, 763);
      this.label_LoopNum.Name = "label_LoopNum";
      this.label_LoopNum.Size = new Size(247, 23);
      this.label_LoopNum.TabIndex = 225;
      this.label_LoopNum.Text = "최적화 Cycle";
      this.label_LoopNum.TextAlign = ContentAlignment.MiddleCenter;
      this.newComboBox_ValveOption.BackColor = Color.White;
      this.newComboBox_ValveOption.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_ValveOption.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_ValveOption.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_ValveOption.isSameSelect = false;
      this.newComboBox_ValveOption.Location = new Point(556, 741);
      this.newComboBox_ValveOption.Name = "newComboBox_ValveOption";
      this.newComboBox_ValveOption.SelectedIndex = -1;
      this.newComboBox_ValveOption.Size = new Size(249, 23);
      this.newComboBox_ValveOption.TabIndex = 226;
      this.newComboBox_ValveOption.TabStop = false;
      this.newComboBox_ValveOption.TextAlign = HorizontalAlignment.Center;
      this.newComboBox_ValveOption.Value = "";
      this.newComboBox_LoopNum.BackColor = Color.White;
      this.newComboBox_LoopNum.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_LoopNum.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_LoopNum.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_LoopNum.isSameSelect = false;
      this.newComboBox_LoopNum.Location = new Point(556, 763);
      this.newComboBox_LoopNum.Name = "newComboBox_LoopNum";
      this.newComboBox_LoopNum.SelectedIndex = -1;
      this.newComboBox_LoopNum.Size = new Size(249, 23);
      this.newComboBox_LoopNum.TabIndex = 227;
      this.newComboBox_LoopNum.TabStop = false;
      this.newComboBox_LoopNum.TextAlign = HorizontalAlignment.Center;
      this.newComboBox_LoopNum.Value = "";
      this.label_InjectionPressureLimit.BackColor = Color.Lavender;
      this.label_InjectionPressureLimit.BorderStyle = BorderStyle.FixedSingle;
      this.label_InjectionPressureLimit.Location = new Point(310, 829);
      this.label_InjectionPressureLimit.Name = "label_InjectionPressureLimit";
      this.label_InjectionPressureLimit.Size = new Size(247, 23);
      this.label_InjectionPressureLimit.TabIndex = 228;
      this.label_InjectionPressureLimit.Text = "사출압력 제한 값";
      this.label_InjectionPressureLimit.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_DeflectionLimit.BackColor = SystemColors.Window;
      this.newTextBox_DeflectionLimit.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_DeflectionLimit.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_DeflectionLimit.IsDigit = false;
      this.newTextBox_DeflectionLimit.Lines = new string[0];
      this.newTextBox_DeflectionLimit.Location = new Point(556, 785);
      this.newTextBox_DeflectionLimit.MultiLine = false;
      this.newTextBox_DeflectionLimit.Name = "newTextBox_DeflectionLimit";
      this.newTextBox_DeflectionLimit.ReadOnly = false;
      this.newTextBox_DeflectionLimit.Size = new Size(249, 23);
      this.newTextBox_DeflectionLimit.TabIndex = 229;
      this.newTextBox_DeflectionLimit.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_DeflectionLimit.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_DeflectionLimit.TextForeColor = SystemColors.WindowText;
      this.newTextBox_DeflectionLimit.Value = "";
      this.newTextBox_ClampForceLimit.BackColor = SystemColors.Window;
      this.newTextBox_ClampForceLimit.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_ClampForceLimit.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_ClampForceLimit.IsDigit = false;
      this.newTextBox_ClampForceLimit.Lines = new string[0];
      this.newTextBox_ClampForceLimit.Location = new Point(556, 807);
      this.newTextBox_ClampForceLimit.MultiLine = false;
      this.newTextBox_ClampForceLimit.Name = "newTextBox_ClampForceLimit";
      this.newTextBox_ClampForceLimit.ReadOnly = false;
      this.newTextBox_ClampForceLimit.Size = new Size(249, 23);
      this.newTextBox_ClampForceLimit.TabIndex = 230;
      this.newTextBox_ClampForceLimit.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_ClampForceLimit.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_ClampForceLimit.TextForeColor = SystemColors.WindowText;
      this.newTextBox_ClampForceLimit.Value = "";
      this.newTextBox_InjectionPressureLimit.BackColor = SystemColors.Window;
      this.newTextBox_InjectionPressureLimit.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_InjectionPressureLimit.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_InjectionPressureLimit.IsDigit = false;
      this.newTextBox_InjectionPressureLimit.Lines = new string[0];
      this.newTextBox_InjectionPressureLimit.Location = new Point(556, 829);
      this.newTextBox_InjectionPressureLimit.MultiLine = false;
      this.newTextBox_InjectionPressureLimit.Name = "newTextBox_InjectionPressureLimit";
      this.newTextBox_InjectionPressureLimit.ReadOnly = false;
      this.newTextBox_InjectionPressureLimit.Size = new Size(249, 23);
      this.newTextBox_InjectionPressureLimit.TabIndex = 231;
      this.newTextBox_InjectionPressureLimit.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_InjectionPressureLimit.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_InjectionPressureLimit.TextForeColor = SystemColors.WindowText;
      this.newTextBox_InjectionPressureLimit.Value = "";
      this.newButton_ClearNodes.ButtonBackColor = Color.White;
      this.newButton_ClearNodes.ButtonText = "선택 해지";
      this.newButton_ClearNodes.FlatBorderSize = 1;
      this.newButton_ClearNodes.FlatStyle = FlatStyle.Flat;
      this.newButton_ClearNodes.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_ClearNodes.ForeColor = Color.Navy;
      this.newButton_ClearNodes.Image = (Image) null;
      this.newButton_ClearNodes.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_ClearNodes.Location = new Point(557, 72);
      this.newButton_ClearNodes.Name = "newButton_ClearNodes";
      this.newButton_ClearNodes.Size = new Size(248, 30);
      this.newButton_ClearNodes.TabIndex = 233;
      this.newButton_ClearNodes.TabStop = false;
      this.newButton_ClearNodes.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_ClearNodes.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_ClearNodes.NewClick += new EventHandler(this.newButton_Prohibit_NewClick);
      this.newButton_SelectNodes.ButtonBackColor = Color.White;
      this.newButton_SelectNodes.ButtonText = "노드 선택";
      this.newButton_SelectNodes.FlatBorderSize = 1;
      this.newButton_SelectNodes.FlatStyle = FlatStyle.Flat;
      this.newButton_SelectNodes.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_SelectNodes.ForeColor = Color.Navy;
      this.newButton_SelectNodes.Image = (Image) null;
      this.newButton_SelectNodes.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_SelectNodes.Location = new Point(310, 72);
      this.newButton_SelectNodes.Name = "newButton_SelectNodes";
      this.newButton_SelectNodes.Size = new Size(248, 30);
      this.newButton_SelectNodes.TabIndex = 232;
      this.newButton_SelectNodes.TabStop = false;
      this.newButton_SelectNodes.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_SelectNodes.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_SelectNodes.NewClick += new EventHandler(this.newButton_Prohibit_NewClick);
      this.label_Prohibit.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Prohibit.BorderStyle = BorderStyle.FixedSingle;
      this.label_Prohibit.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Prohibit.ForeColor = Color.MidnightBlue;
      this.label_Prohibit.Location = new Point(310, 53);
      this.label_Prohibit.Name = "label_Prohibit";
      this.label_Prohibit.Size = new Size(495, 20);
      this.label_Prohibit.TabIndex = 234;
      this.label_Prohibit.Text = "Prohibit Nodes";
      this.label_Prohibit.TextAlign = ContentAlignment.MiddleCenter;
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(810, 888);
      this.Controls.Add((Control) this.newButton_ClearNodes);
      this.Controls.Add((Control) this.newButton_SelectNodes);
      this.Controls.Add((Control) this.label_Prohibit);
      this.Controls.Add((Control) this.newTextBox_InjectionPressureLimit);
      this.Controls.Add((Control) this.newTextBox_ClampForceLimit);
      this.Controls.Add((Control) this.newTextBox_DeflectionLimit);
      this.Controls.Add((Control) this.label_InjectionPressureLimit);
      this.Controls.Add((Control) this.newComboBox_LoopNum);
      this.Controls.Add((Control) this.newComboBox_ValveOption);
      this.Controls.Add((Control) this.label_Option);
      this.Controls.Add((Control) this.label_ValveOption);
      this.Controls.Add((Control) this.label_ClampForceLimit);
      this.Controls.Add((Control) this.label_DeflectionLimit);
      this.Controls.Add((Control) this.label_LoopNum);
      this.Controls.Add((Control) this.panel1);
      this.Controls.Add((Control) this.label_Suji_SearchValue);
      this.Controls.Add((Control) this.label_SearchCond);
      this.Controls.Add((Control) this.label_Search);
      this.Controls.Add((Control) this.newComboBox_Company);
      this.Controls.Add((Control) this.newTextBox_Search);
      this.Controls.Add((Control) this.newComboBox_TradeName);
      this.Controls.Add((Control) this.label_AnalysisType);
      this.Controls.Add((Control) this.newComboBox_AnalysisType);
      this.Controls.Add((Control) this.newTextBox_ClampF_Priority);
      this.Controls.Add((Control) this.label_ClampF);
      this.Controls.Add((Control) this.newTextBox_Type);
      this.Controls.Add((Control) this.label_Type);
      this.Controls.Add((Control) this.newTextBox_GateNum_Min);
      this.Controls.Add((Control) this.newButton_Apply);
      this.Controls.Add((Control) this.newTextBox_GateNum_Max);
      this.Controls.Add((Control) this.label_Result);
      this.Controls.Add((Control) this.label_GateNum);
      this.Controls.Add((Control) this.newTextBox_MoldTemp_Min);
      this.Controls.Add((Control) this.newTextBox_Weight_Priority);
      this.Controls.Add((Control) this.newTextBox_MoldTemp_Max);
      this.Controls.Add((Control) this.newTextBox_TempAtFlowFront_Priority);
      this.Controls.Add((Control) this.label_MoldTemp);
      this.Controls.Add((Control) this.newTextBox_InjPressure_Priority);
      this.Controls.Add((Control) this.newTextBox_MeltTemp_Min);
      this.Controls.Add((Control) this.newTextBox_Sinkmark_Priority);
      this.Controls.Add((Control) this.newTextBox_CycleTime_Priority);
      this.Controls.Add((Control) this.newTextBox_MeltTemp_Max);
      this.Controls.Add((Control) this.newTextBox_VolShrinkage_Priority);
      this.Controls.Add((Control) this.newTextBox_Deflection_Priority);
      this.Controls.Add((Control) this.newTextBox_CoolingInletTemp_Min);
      this.Controls.Add((Control) this.newTextBox_PackingNum_Min);
      this.Controls.Add((Control) this.label_InjPressure);
      this.Controls.Add((Control) this.newTextBox_CoolingTime_Min);
      this.Controls.Add((Control) this.label_CycleTime);
      this.Controls.Add((Control) this.newTextBox_VPSwitchOver_Min);
      this.Controls.Add((Control) this.label_Deflection);
      this.Controls.Add((Control) this.newTextBox_FillTime_Min);
      this.Controls.Add((Control) this.label_Weight);
      this.Controls.Add((Control) this.newTextBox_CoolingInletTemp_Max);
      this.Controls.Add((Control) this.label_CoolingInletTemp);
      this.Controls.Add((Control) this.label_CoolingTime);
      this.Controls.Add((Control) this.label_TempAtFlowFront);
      this.Controls.Add((Control) this.label_FillTime);
      this.Controls.Add((Control) this.label_MeltTemp);
      this.Controls.Add((Control) this.newTextBox_PackingNum_Max);
      this.Controls.Add((Control) this.newTextBox_CoolingTime_Max);
      this.Controls.Add((Control) this.label_Sinkmark);
      this.Controls.Add((Control) this.newTextBox_VPSwitchOver_Max);
      this.Controls.Add((Control) this.newTextBox_FillTime_Max);
      this.Controls.Add((Control) this.label_VolShrinkage);
      this.Controls.Add((Control) this.label_PackingNum);
      this.Controls.Add((Control) this.label_VPSwitchOver);
      this.Controls.Add((Control) this.newTextBox_Density);
      this.Controls.Add((Control) this.label_Material);
      this.Controls.Add((Control) this.label_Density);
      this.Controls.Add((Control) this.label_TradeName);
      this.Controls.Add((Control) this.label_Company);
      this.Controls.Add((Control) this.label_Data);
      this.Controls.Add((Control) this.listBox_DB);
      this.Controls.Add((Control) this.label_DB_List);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmInput);
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "Input Data DB";
      this.Load += new EventHandler(this.frmInput_Load);
      this.KeyDown += new KeyEventHandler(this.frmInput_KeyDown);
      this.panel1.ResumeLayout(false);
      this.ResumeLayout(false);
    }
  }
}
