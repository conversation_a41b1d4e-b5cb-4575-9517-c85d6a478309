﻿// Decompiled with JetBrains decompiler
// Type: Key_Maker.My.MySettingsProperty
// Assembly: Key_Maker, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 43BE3483-FE2B-4F9D-8AD0-2D447D49E2D6
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\Key_Maker.exe

using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System.ComponentModel.Design;
using System.Diagnostics;
using System.Runtime.CompilerServices;

namespace Key_Maker.My
{
  [StandardModule]
  [HideModuleName]
  [DebuggerNonUserCode]
  [CompilerGenerated]
  internal sealed class MySettingsProperty
  {
    [HelpKeyword("My.Settings")]
    internal static MySettings Settings
    {
      get
      {
        MySettings settings = MySettings.Default;
        return settings;
      }
    }
  }
}
