﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.clsUtill
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using arUtil;
using HDLog4Net;
using HDMoldFlowLibrary;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public class clsUtill
  {
    private static frmProgress m_frmProgress;
    public static BackgroundWorker m_bwProgress;
    private static string m_strText;
    private static Form m_frmParent;

    [DllImport("user32.dll", CharSet = CharSet.Auto)]
    public static extern IntPtr SendMessage(IntPtr hWnd, uint Msg, IntPtr w, IntPtr l);

    public static Image ChangeSyncBackgroundImage(Image p_imgTarget)
    {
      Bitmap bitmap = (Bitmap) p_imgTarget;
      bitmap.MakeTransparent(bitmap.GetPixel(0, 0));
      return (Image) bitmap;
    }

    public static void StartProgress(string p_strText, Form p_frmParent = null)
    {
      clsUtill.m_strText = p_strText;
      clsUtill.m_frmParent = p_frmParent;
      clsUtill.m_bwProgress = new BackgroundWorker();
      clsUtill.m_bwProgress.WorkerReportsProgress = true;
      clsUtill.m_bwProgress.WorkerSupportsCancellation = true;
      clsUtill.m_bwProgress.DoWork += new DoWorkEventHandler(clsUtill.M_bcw_DoWork);
      clsUtill.m_bwProgress.RunWorkerAsync();
      bool isDisposed = false;
      while (true)
      {
        if (clsUtill.m_frmProgress != null)
        {
          clsUtill.m_frmProgress.InvokeIFNeeded((Action) (() => isDisposed = clsUtill.m_frmProgress.IsDisposed));
          if (!isDisposed)
            break;
        }
        Thread.Sleep(1);
      }
    }

    private static void M_bcw_DoWork(object sender, DoWorkEventArgs e)
    {
      clsUtill.m_frmProgress = new frmProgress();
      clsUtill.m_frmProgress.m_frmParent = clsUtill.m_frmParent;
      clsUtill.m_frmProgress.m_strText = clsUtill.m_strText;
      Application.Run((Form) clsUtill.m_frmProgress);
    }

    public static void UpdateProgress(string p_strText)
    {
      if (clsUtill.m_frmProgress == null)
        return;
      clsUtill.m_frmProgress.InvokeIFNeeded((Action) (() => clsUtill.m_frmProgress.UpdateProgress(p_strText)));
    }

    public static void HideProgress()
    {
      if (clsUtill.m_frmProgress == null)
        return;
      clsUtill.m_frmProgress.InvokeIFNeeded((Action) (() => clsUtill.m_frmProgress.Hide()));
    }

    public static void ShowProgress()
    {
      if (clsUtill.m_frmProgress == null)
        return;
      clsUtill.m_frmProgress.InvokeIFNeeded((Action) (() => clsUtill.m_frmProgress.Show()));
    }

    public static void EndProgress()
    {
      Thread.Sleep(1000);
      if (clsUtill.m_bwProgress != null)
      {
        clsUtill.m_bwProgress.CancelAsync();
        clsUtill.m_frmProgress.InvokeIFNeeded((Action) (() => clsUtill.m_frmProgress.Close()));
        clsUtill.m_frmProgress.Dispose();
      }
      clsUtill.m_bwProgress = (BackgroundWorker) null;
    }

    [DllImport("KERNEL32.DLL", EntryPoint = "GetPrivateProfileStringW", CharSet = CharSet.Unicode, CallingConvention = CallingConvention.StdCall, SetLastError = true)]
    private static extern int GetPrivateProfileString(
      string lpAppName,
      string lpKeyName,
      string lpDefault,
      string lpReturnString,
      int nSize,
      string lpFilename);

    [DllImport("kernel32")]
    private static extern int GetPrivateProfileString(
      string section,
      string key,
      string def,
      StringBuilder retVal,
      int size,
      string filePath);

    [DllImport("kernel32.dll")]
    private static extern int GetPrivateProfileSection(
      string section,
      byte[] lpszReturnBuffer,
      int size,
      string filePath);

    [DllImport("kernel32")]
    private static extern long WritePrivateProfileString(
      string section,
      string key,
      string val,
      string filePath);

    public static string ReadINI(string p_strSection, string p_strKey, string p_strIniPath)
    {
      try
      {
        StringBuilder retVal = new StringBuilder(1024);
        if (clsUtill.GetPrivateProfileString(p_strSection, p_strKey, "", retVal, retVal.Capacity, p_strIniPath) > 0)
          return retVal.ToString();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsUtill]ReadINI):" + ex.Message));
      }
      return "";
    }

    public static void WriteINI(
      string p_strSection,
      string p_strKey,
      string p_strValue,
      string p_strIniPath)
    {
      FileInfo fileInfo = new FileInfo(p_strIniPath);
      if (!fileInfo.Directory.Exists)
        fileInfo.Directory.Create();
      clsUtill.WritePrivateProfileString(p_strSection, p_strKey, p_strValue, p_strIniPath);
    }

    public static Dictionary<string, string> GetINIDataFromSection(
      string p_strIniFPath,
      string p_strSection)
    {
      Dictionary<string, string> iniDataFromSection = new Dictionary<string, string>();
      try
      {
        iniDataFromSection = new INIHelper(p_strIniFPath).GetItemList(p_strSection);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsUtill]GetINIDataFromSection):" + ex.Message));
      }
      return iniDataFromSection;
    }

    public static DataTable GetINIData(string p_strIniFPath)
    {
      DataTable iniData = (DataTable) null;
      try
      {
        List<string> sectionList = new INIHelper(p_strIniFPath).GetSectionList();
        if (sectionList.Count > 0)
        {
          iniData = new DataTable();
          foreach (string p_strSection in sectionList)
          {
            Dictionary<string, string> keys = clsUtill.GetKeys(p_strIniFPath, p_strSection);
            if (!iniData.Columns.Contains("Section"))
              iniData.Columns.Add("Section");
            foreach (KeyValuePair<string, string> keyValuePair in keys)
            {
              if (!iniData.Columns.Contains(keyValuePair.Key))
                iniData.Columns.Add(keyValuePair.Key);
            }
            DataRow dataRow = iniData.Rows.Add();
            dataRow["Section"] = (object) p_strSection;
            foreach (KeyValuePair<string, string> keyValuePair in keys)
              dataRow[keyValuePair.Key] = (object) keyValuePair.Value;
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception[HDMFReport][clsUtill]GetINIData):" + ex.Message));
      }
      return iniData;
    }

    private static Dictionary<string, string> GetKeys(string p_strInifile, string p_strSection)
    {
      Dictionary<string, string> keys = new Dictionary<string, string>();
      byte[] numArray = new byte[2048];
      try
      {
        clsUtill.GetPrivateProfileSection(p_strSection, numArray, 2048, p_strInifile);
        foreach (string str in Encoding.UTF8.GetString(numArray).Trim(new char[1]).Split(new char[1]))
        {
          char[] chArray = new char[1]{ '=' };
          string[] strArray = str.Split(chArray);
          keys.Add(strArray[0], strArray[1]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception[HDMFReport][clsUtill]GetKeys):" + ex.Message));
      }
      return keys;
    }

    public static DialogResult ShowMessageBox(
      Form p_Form,
      string p_strMessage,
      string p_strTitle = null,
      MessageBoxButtons p_msgButton = MessageBoxButtons.OK,
      MessageBoxIcon p_msgIcon = MessageBoxIcon.None)
    {
      if (p_Form.Parent != null)
      {
        Control parent = p_Form.Parent;
        while (true)
        {
          if (!parent.GetType().Name.Contains("frm"))
            parent = parent.Parent;
          else
            break;
        }
        p_Form = (Form) parent;
      }
      if (p_strTitle == null)
        p_strTitle = "";
      DialogResult dialogResult = DialogResult.None;
      using (new CenterWinDialog(p_Form))
        dialogResult = MessageBox.Show((IWin32Window) new Form()
        {
          TopMost = true
        }, p_strMessage, p_strTitle, p_msgButton, p_msgIcon);
      return dialogResult;
    }

    public static double ConvertToDouble(string p_strValue)
    {
      double result = 0.0;
      double.TryParse(p_strValue, out result);
      return result;
    }

    public static int ConvertToInt(string p_strValue)
    {
      int result = 0;
      int.TryParse(p_strValue, out result);
      return result;
    }

    public static bool ConvertToBoolean(string p_strValue)
    {
      bool result = false;
      bool.TryParse(p_strValue, out result);
      return result;
    }

    public static clsHDMFLibDefine.Company ConvertToEnumCompany(string p_strValue)
    {
      clsHDMFLibDefine.Company result = clsHDMFLibDefine.Company.HDSolutions;
      Enum.TryParse<clsHDMFLibDefine.Company>(p_strValue, true, out result);
      return result;
    }

    public static void CopyFolder(string sourceFolder, string destFolder)
    {
      if (!Directory.Exists(destFolder))
        Directory.CreateDirectory(destFolder);
      string[] files = Directory.GetFiles(sourceFolder);
      string[] directories = Directory.GetDirectories(sourceFolder);
      foreach (string str in files)
      {
        string fileName = Path.GetFileName(str);
        string destFileName = Path.Combine(destFolder, fileName);
        File.Copy(str, destFileName);
      }
      foreach (string str in directories)
      {
        string fileName = Path.GetFileName(str);
        string destFolder1 = Path.Combine(destFolder, fileName);
        clsUtill.CopyFolder(str, destFolder1);
      }
    }

    [DllImport("user32.dll")]
    private static extern bool SetForegroundWindow(IntPtr hWnd);

    public static void ShowForm(Form frmTarget)
    {
      frmTarget.Show();
      clsUtill.SetForegroundWindow(frmTarget.Handle);
    }

    [DllImport("user32.dll", SetLastError = true)]
    public static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint lpdwProcessId);

    public static void ReleaseComObject(object p_obj)
    {
      if (p_obj == null)
        return;
      Marshal.ReleaseComObject(p_obj);
    }

    public enum ProgressBarColor
    {
      None,
      Green,
      Red,
      Yellow,
    }
  }
}
