﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlowLibrary.clsPlotOption
// Assembly: HDMoldFlowLibrary, Version=2.24.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 16E5D508-B892-4B5D-A02C-2C1930D27867
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMoldFlowLibrary.dll

#nullable disable
namespace HDMoldFlowLibrary
{
  public class clsPlotOption
  {
    public int iSelection;
    public Scaling enumScaling;
    public double dblScalingMin;
    public double dblScalingMax;
    public double dblOpacity;
    public PlotColorType enumColorType;
    public PlotColor enumColor;
    public bool isExpendedColor;

    public clsPlotOption()
    {
      this.iSelection = -1;
      this.dblScalingMin = -1.0;
      this.dblScalingMax = -1.0;
      this.dblOpacity = -1.0;
      this.isExpendedColor = false;
    }
  }
}
