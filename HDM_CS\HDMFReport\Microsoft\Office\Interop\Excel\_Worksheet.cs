﻿// Decompiled with JetBrains decompiler
// Type: Microsoft.Office.Interop.Excel._Worksheet
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

namespace Microsoft.Office.Interop.Excel
{
  [CompilerGenerated]
  [Guid("000208D8-0000-0000-C000-000000000046")]
  [TypeIdentifier]
  [ComImport]
  public interface _Worksheet
  {
    [SpecialName]
    [MethodImpl(MethodCodeType = MethodCodeType.Runtime)]
    sealed extern void _VtblGap1_34();

    [DispId(1377)]
    Shapes Shapes { [DispId(1377), MethodImpl(MethodImplOptions.InternalCall, MethodCodeType = MethodCodeType.Runtime)] [return: MarshalAs(UnmanagedType.Interface)] get; }

    [SpecialName]
    [MethodImpl(MethodCodeType = MethodCodeType.Runtime)]
    sealed extern void _VtblGap2_10();

    [DispId(238)]
    Range Cells { [DispId(238), MethodImpl(MethodImplOptions.InternalCall, MethodCodeType = MethodCodeType.Runtime)] [return: MarshalAs(UnmanagedType.Interface)] get; }

    [SpecialName]
    [MethodImpl(MethodCodeType = MethodCodeType.Runtime)]
    sealed extern void _VtblGap3_47();

    [DispId(197)]
    [MethodImpl(MethodImplOptions.InternalCall, MethodCodeType = MethodCodeType.Runtime)]
    [return: MarshalAs(UnmanagedType.Interface)]
    Range get_Range([MarshalAs(UnmanagedType.Struct), In] object Cell1, [MarshalAs(UnmanagedType.Struct), In, Optional] object Cell2);

    [SpecialName]
    [MethodImpl(MethodCodeType = MethodCodeType.Runtime)]
    sealed extern void _VtblGap4_49();

    [DispId(2493)]
    [MethodImpl(MethodImplOptions.InternalCall, MethodCodeType = MethodCodeType.Runtime)]
    void ExportAsFixedFormat(
      [In] XlFixedFormatType Type,
      [MarshalAs(UnmanagedType.Struct), In, Optional] object Filename,
      [MarshalAs(UnmanagedType.Struct), In, Optional] object Quality,
      [MarshalAs(UnmanagedType.Struct), In, Optional] object IncludeDocProperties,
      [MarshalAs(UnmanagedType.Struct), In, Optional] object IgnorePrintAreas,
      [MarshalAs(UnmanagedType.Struct), In, Optional] object From,
      [MarshalAs(UnmanagedType.Struct), In, Optional] object To,
      [MarshalAs(UnmanagedType.Struct), In, Optional] object OpenAfterPublish,
      [MarshalAs(UnmanagedType.Struct), In, Optional] object FixedFormatExtClassPtr);
  }
}
