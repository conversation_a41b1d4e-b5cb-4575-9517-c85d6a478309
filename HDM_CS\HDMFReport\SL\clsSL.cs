﻿// Decompiled with JetBrains decompiler
// Type: HDMFReport.clsSL
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using HDLog4Net;
using HDMoldFlowLibrary;
using Microsoft.Office.Core;
using Microsoft.Office.Interop.PowerPoint;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace HDMFReport
{
  internal class clsSL : clsBase
  {
    private string m_strStudyName = string.Empty;

    public override void ExportReport(
      DataRow p_drStudy,
      string p_strLangType,
      clsHDMFLibDefine.Company p_enumCompany)
    {
      Dictionary<string, string> p_dicValue = new Dictionary<string, string>();
      Dictionary<string, string> p_dicView = new Dictionary<string, string>();
      Dictionary<string, string> p_dicUse = new Dictionary<string, string>();
      List<string> stringList = new List<string>();
      try
      {
        p_dicValue.Clear();
        p_dicView.Clear();
        clsSLData.GetReportUser(p_drStudy, out p_dicValue, out p_dicView, out p_dicUse);
        this.StartExport(p_drStudy, p_dicValue, p_dicView, p_dicUse, p_enumCompany, p_strLangType);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]ExportReport):" + ex.Message));
      }
    }

    protected override void StartExport(
      DataRow p_drStudy,
      Dictionary<string, string> p_dicValue,
      Dictionary<string, string> p_dicView,
      Dictionary<string, string> p_dicUse,
      clsHDMFLibDefine.Company p_enumCompany,
      string p_strLangType = "KOR")
    {
      Dictionary<string, string> analysisData = clsSLData.GetAnalysisData(p_drStudy, p_dicValue, p_dicView);
      bool flag1 = false;
      if (p_drStudy["Sequence"].ToString().Contains("Cool"))
        flag1 = true;
      bool flag2 = false;
      if (p_drStudy["Sequence"].ToString().Contains("Warp"))
        flag2 = true;
      int num = 1;
      FileInfo fileInfo;
      while (true)
      {
        fileInfo = new FileInfo(clsReportDefine.g_diProject.Parent.FullName + "\\" + clsReportDefine.g_diProject.Name + "_Export\\" + p_drStudy["Name"] + "_" + DateTime.Now.ToString("yyMMdd") + "_Report_Rev" + (object) num + "." + clsReportDefine.g_dicExtension["PowerPoint"]);
        if (fileInfo.Exists)
          ++num;
        else
          break;
      }
      if (!fileInfo.Directory.Exists)
        fileInfo.Directory.Create();
      try
      {
        // ISSUE: variable of a compiler-generated type
        Presentations presentations = ((_Application) Activator.CreateInstance(Marshal.GetTypeFromCLSID(new Guid("91493441-5A91-11CF-8700-00AA0060263B")))).Presentations;
        FileInfo template = clsReportData.GetTemplate(p_enumCompany, p_strLangType);
        template.Refresh();
        if (template.Exists)
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          Presentation presentation = presentations.Open(template.FullName, MsoTriState.msoTrue, WithWindow: MsoTriState.msoFalse);
          List<Slide> allSlide = clsSL.GetAllSlide(presentation);
          if (p_dicUse.Count > 0)
          {
            List<Slide> slideList = new List<Slide>();
            foreach (KeyValuePair<string, string> keyValuePair in p_dicUse)
            {
              KeyValuePair<string, string> kvpTmp = keyValuePair;
              if (!clsReportUtill.ConvertToBoolean(kvpTmp.Value))
              {
                // ISSUE: variable of a compiler-generated type
                Slide slide = allSlide.Where<Slide>((System.Func<Slide, bool>) (Temp => Temp.Name.Contains(kvpTmp.Key))).FirstOrDefault<Slide>();
                if (slide != null)
                  slideList.Add(slide);
              }
            }
            for (int index = 0; index < slideList.Count; ++index)
            {
              // ISSUE: reference to a compiler-generated method
              slideList[index].Delete();
              allSlide.Remove(slideList[index]);
            }
          }
          // ISSUE: variable of a compiler-generated type
          Slide slide1 = allSlide.Where<Slide>((System.Func<Slide, bool>) (Temp => Temp.Name.Contains("Reflector Reflex"))).FirstOrDefault<Slide>();
          if (slide1 != null)
          {
            // ISSUE: reference to a compiler-generated method
            slide1.Delete();
            allSlide.Remove(slide1);
          }
          if (!flag1)
          {
            List<Slide> slideList = new List<Slide>();
            foreach (Slide slide2 in allSlide)
            {
              if (slide2.Name == "Conclusions3")
                slideList.Add(slide2);
              else if (slide2.Name == "Processing Conditions3")
                slideList.Add(slide2);
              else if (slide2.Name == "Mold temperature1" || slide2.Name == "Mold temperature2")
                slideList.Add(slide2);
              else if (slide2.Name == "Circuit Reynolds number")
                slideList.Add(slide2);
              else if (slide2.Name == "Circuit Flow rate")
                slideList.Add(slide2);
              else if (slide2.Name == "Circuit coolant temperature")
                slideList.Add(slide2);
            }
            for (int index = 0; index < slideList.Count; ++index)
            {
              // ISSUE: reference to a compiler-generated method
              slideList[index].Delete();
              allSlide.Remove(slideList[index]);
            }
          }
          if (!flag2)
          {
            List<Slide> slideList = new List<Slide>();
            foreach (Slide slide3 in allSlide)
            {
              if (slide3.Name.Contains("Deflection"))
                slideList.Add(slide3);
            }
            for (int index = 0; index < slideList.Count; ++index)
            {
              // ISSUE: reference to a compiler-generated method
              slideList[index].Delete();
              allSlide.Remove(slideList[index]);
            }
          }
          else
          {
            List<Slide> slideList = new List<Slide>();
            if (p_dicValue["Report_DeflectionX"].Split('/')[0] == "Best fit")
            {
              // ISSUE: variable of a compiler-generated type
              Slide slide4 = allSlide.Where<Slide>((System.Func<Slide, bool>) (Temp => Temp.Name == "Deflection X(AnchorPlan)")).FirstOrDefault<Slide>();
              if (slide4 != null)
                slideList.Add(slide4);
            }
            if (p_dicValue["Report_DeflectionY"].Split('/')[0] == "Best fit")
            {
              // ISSUE: variable of a compiler-generated type
              Slide slide5 = allSlide.Where<Slide>((System.Func<Slide, bool>) (Temp => Temp.Name == "Deflection Y(AnchorPlan)")).FirstOrDefault<Slide>();
              if (slide5 != null)
                slideList.Add(slide5);
            }
            if (p_dicValue["Report_DeflectionZ"].Split('/')[0] == "Best fit")
            {
              // ISSUE: variable of a compiler-generated type
              Slide slide6 = allSlide.Where<Slide>((System.Func<Slide, bool>) (Temp => Temp.Name == "Deflection Z(AnchorPlan)")).FirstOrDefault<Slide>();
              if (slide6 != null)
                slideList.Add(slide6);
            }
            for (int index = 0; index < slideList.Count; ++index)
            {
              // ISSUE: reference to a compiler-generated method
              slideList[index].Delete();
              allSlide.Remove(slideList[index]);
            }
          }
          Dictionary<string, string> dicData = new Dictionary<string, string>();
          foreach (Slide p_slData in allSlide)
          {
            dicData.Clear();
            switch (p_slData.Name)
            {
              case "Air trap":
                dicData = clsSL.GetSlide23(analysisData);
                clsSL.SetSlide23(p_slData, dicData);
                continue;
              case "Circuit Flow rate":
                dicData = clsSL.GetSlide29(analysisData);
                clsSL.SetSlide29(p_slData, dicData);
                continue;
              case "Circuit Reynolds number":
                dicData = clsSL.GetSlide28(analysisData);
                clsSL.SetSlide28(p_slData, dicData);
                continue;
              case "Circuit coolant temperature":
                dicData = clsSL.GetSlide30(analysisData);
                clsSL.SetSlide30(p_slData, dicData);
                continue;
              case "Clamp force":
                dicData = clsSL.GetSlide17(analysisData);
                clsSL.SetSlide17(p_slData, dicData);
                continue;
              case "Conclusions1":
                dicData = clsSL.GetSlide2(analysisData);
                clsSL.SetSlide2(p_slData, dicData);
                continue;
              case "Conclusions2":
                dicData = clsSL.GetSlide3(analysisData);
                clsSL.SetSlide3(p_slData, dicData);
                continue;
              case "Conclusions3":
                dicData = clsSL.GetSlide4(analysisData);
                clsSL.SetSlide4(p_slData, dicData);
                continue;
              case "Deflection All":
                dicData = clsSL.GetSlide32(analysisData);
                clsSL.SetSlide32(p_slData, dicData);
                continue;
              case "Deflection All(Isolate)":
                dicData = clsSL.GetSlide33(analysisData);
                clsSL.SetSlide33(p_slData, dicData);
                continue;
              case "Deflection X(AnchorPlan)":
                dicData = clsSL.GetSlide35(analysisData);
                clsSL.SetSlide35(p_slData, dicData);
                continue;
              case "Deflection X(BestFit)":
                dicData = clsSL.GetSlide34(analysisData);
                clsSL.SetSlide34(p_slData, dicData);
                continue;
              case "Deflection Y(AnchorPlan)":
                dicData = clsSL.GetSlide37(analysisData);
                clsSL.SetSlide37(p_slData, dicData);
                continue;
              case "Deflection Y(BestFit)":
                dicData = clsSL.GetSlide36(analysisData);
                clsSL.SetSlide36(p_slData, dicData);
                continue;
              case "Deflection Z(AnchorPlan)":
                dicData = clsSL.GetSlide39(analysisData);
                clsSL.SetSlide39(p_slData, dicData);
                continue;
              case "Deflection Z(BestFit)":
                dicData = clsSL.GetSlide38(analysisData);
                clsSL.SetSlide38(p_slData, dicData);
                continue;
              case "Filling":
                dicData = clsSL.GetSlide10(analysisData);
                clsSL.SetSlide10(p_slData, dicData);
                continue;
              case "Flow pattern":
                dicData = clsSL.GetSlide9(analysisData);
                clsSL.SetSlide9(p_slData, dicData);
                continue;
              case "Frozen Layer fraction at gate":
                if (analysisData["Mesh[MF]"].ToString() != "Dual")
                {
                  dicData = clsSL.GetSlide18(analysisData);
                  clsSL.SetSlide18(p_slData, dicData);
                  continue;
                }
                continue;
              case "Grow from":
                dicData = clsSL.GetSlide12(analysisData);
                clsSL.SetSlide12(p_slData, dicData);
                continue;
              case "Injection pressure":
                dicData = clsSL.GetSlide15(analysisData);
                clsSL.SetSlide15(p_slData, dicData);
                continue;
              case "Main":
                dicData = clsSL.GetSlide1(analysisData);
                clsSL.SetSlide1(p_slData, dicData);
                continue;
              case "Mold temperature1":
                dicData = clsSL.GetSlide26(analysisData);
                clsSL.SetSlide26(p_slData, dicData);
                continue;
              case "Mold temperature2":
                dicData = clsSL.GetSlide27(analysisData);
                clsSL.SetSlide27(p_slData, dicData);
                continue;
              case "Part model & Mesh":
                dicData = clsSL.GetSlide5(analysisData);
                clsSL.SetSlide5(p_slData, dicData);
                continue;
              case "Pressure at V/P switchover":
                dicData = clsSL.GetSlide13(analysisData);
                clsSL.SetSlide13(p_slData, dicData);
                continue;
              case "Pressure at all gate at Transfer":
                dicData = clsSL.GetSlide16(analysisData);
                clsSL.SetSlide16(p_slData, dicData);
                continue;
              case "Pressure at end of fill":
                dicData = clsSL.GetSlide14(analysisData);
                clsSL.SetSlide14(p_slData, dicData);
                continue;
              case "Processing Conditions1":
                dicData = clsSL.GetSlide6(analysisData);
                clsSL.SetSlide6(p_slData, dicData);
                continue;
              case "Processing Conditions2":
                dicData = clsSL.GetSlide7(analysisData);
                clsSL.SetSlide7(p_slData, dicData);
                continue;
              case "Processing Conditions3":
                dicData = clsSL.GetSlide8(analysisData);
                clsSL.SetSlide8(p_slData, dicData);
                continue;
              case "Shear rate at 95%, Maximum":
                dicData = clsSL.GetSlide20(analysisData);
                clsSL.SetSlide20(p_slData, dicData);
                continue;
              case "Shear stress at wall at 95%":
                dicData = clsSL.GetSlide21(analysisData);
                clsSL.SetSlide21(p_slData, dicData);
                continue;
              case "Temperature at flow front":
                dicData = clsSL.GetSlide19(analysisData);
                clsSL.SetSlide19(p_slData, dicData);
                continue;
              case "Time to reach ejection temperature":
                dicData = clsSL.GetSlide25(analysisData);
                clsSL.SetSlide25(p_slData, dicData);
                continue;
              case "Velocity":
                dicData = clsSL.GetSlide11(analysisData);
                clsSL.SetSlide11(p_slData, dicData);
                continue;
              case "Volumetric shrinkage":
                dicData = clsSL.GetSlide24(analysisData);
                clsSL.SetSlide24(p_slData, dicData);
                continue;
              case "Weld line":
                dicData = clsSL.GetSlide22(analysisData);
                clsSL.SetSlide22(p_slData, dicData);
                continue;
              default:
                continue;
            }
          }
          if (!fileInfo.Extension.Contains(".ppt"))
          {
            // ISSUE: reference to a compiler-generated method
            presentation.SaveAs(fileInfo.FullName.Replace(fileInfo.Extension, ".pptx"));
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            presentation.SaveAs(fileInfo.FullName);
          }
          // ISSUE: reference to a compiler-generated method
          presentation.Close();
          clsReportUtill.ReleaseComObject((object) presentation);
        }
        if (fileInfo.Extension.Contains(".ppt"))
          return;
        if (fileInfo.Exists)
          fileInfo.Delete();
        File.Move(fileInfo.FullName.Replace(fileInfo.Extension, ".pptx"), fileInfo.FullName);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]StartExport):" + ex.Message));
      }
    }

    private static List<Slide> GetAllSlide(Presentation p_pptPre)
    {
      List<Slide> allSlide = new List<Slide>((IEnumerable<Slide>) p_pptPre.Slides.Cast<Slide>().ToArray<Slide>());
      try
      {
        for (int index = 0; index < allSlide.Count; ++index)
        {
          switch (index)
          {
            case 0:
              allSlide[index].Name = "Main";
              break;
            case 1:
              allSlide[index].Name = "Conclusions1";
              break;
            case 2:
              allSlide[index].Name = "Conclusions2";
              break;
            case 3:
              allSlide[index].Name = "Conclusions3";
              break;
            case 4:
              allSlide[index].Name = "Part model & Mesh";
              break;
            case 5:
              allSlide[index].Name = "Processing Conditions1";
              break;
            case 6:
              allSlide[index].Name = "Processing Conditions2";
              break;
            case 7:
              allSlide[index].Name = "Processing Conditions3";
              break;
            case 8:
              allSlide[index].Name = "Flow pattern";
              break;
            case 9:
              allSlide[index].Name = "Filling";
              break;
            case 10:
              allSlide[index].Name = "Velocity";
              break;
            case 11:
              allSlide[index].Name = "Grow from";
              break;
            case 12:
              allSlide[index].Name = "Pressure at V/P switchover";
              break;
            case 13:
              allSlide[index].Name = "Pressure at end of fill";
              break;
            case 14:
              allSlide[index].Name = "Injection pressure";
              break;
            case 15:
              allSlide[index].Name = "Pressure at all gate at Transfer";
              break;
            case 16:
              allSlide[index].Name = "Clamp force";
              break;
            case 17:
              allSlide[index].Name = "Frozen Layer fraction at gate";
              break;
            case 18:
              allSlide[index].Name = "Temperature at flow front";
              break;
            case 19:
              allSlide[index].Name = "Shear rate at 95%, Maximum";
              break;
            case 20:
              allSlide[index].Name = "Shear stress at wall at 95%";
              break;
            case 21:
              allSlide[index].Name = "Weld line";
              break;
            case 22:
              allSlide[index].Name = "Air trap";
              break;
            case 23:
              allSlide[index].Name = "Volumetric shrinkage";
              break;
            case 24:
              allSlide[index].Name = "Time to reach ejection temperature";
              break;
            case 25:
              allSlide[index].Name = "Mold temperature1";
              break;
            case 26:
              allSlide[index].Name = "Mold temperature2";
              break;
            case 27:
              allSlide[index].Name = "Circuit Reynolds number";
              break;
            case 28:
              allSlide[index].Name = "Circuit Flow rate";
              break;
            case 29:
              allSlide[index].Name = "Circuit coolant temperature";
              break;
            case 30:
              allSlide[index].Name = "Reflector Reflex";
              break;
            case 31:
              allSlide[index].Name = "Deflection All";
              break;
            case 32:
              allSlide[index].Name = "Deflection All(Isolate)";
              break;
            case 33:
              allSlide[index].Name = "Deflection X(BestFit)";
              break;
            case 34:
              allSlide[index].Name = "Deflection X(AnchorPlan)";
              break;
            case 35:
              allSlide[index].Name = "Deflection Y(BestFit)";
              break;
            case 36:
              allSlide[index].Name = "Deflection Y(AnchorPlan)";
              break;
            case 37:
              allSlide[index].Name = "Deflection Z(BestFit)";
              break;
            case 38:
              allSlide[index].Name = "Deflection Z(AnchorPlan)";
              break;
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetAllSlide):" + ex.Message));
      }
      return allSlide;
    }

    private static Dictionary<string, string> GetSlide1(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide1 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Item[User]")))
          slide1.Add("Item", dicData["Item[User]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Step[User]")))
          slide1.Add("Step", dicData["Step[User]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Engineer[User]")))
          slide1.Add("Engineer", dicData["Engineer[User]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Manager[User]")))
          slide1.Add("Manager", dicData["Manager[User]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Sequence[MF]")))
          slide1.Add("Sequence", dicData["Sequence[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Model[IMG]")))
          slide1.Add("Image", dicData["Model[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide1):" + ex.Message));
      }
      return slide1;
    }

    private static Dictionary<string, string> GetSlide2(Dictionary<string, string> dicData)
    {
      string empty = string.Empty;
      StringBuilder stringBuilder1 = new StringBuilder();
      StringBuilder stringBuilder2 = new StringBuilder();
      StringBuilder stringBuilder3 = new StringBuilder();
      Dictionary<string, string> slide2 = new Dictionary<string, string>();
      try
      {
        slide2.Add("Fill Pattern", dicData["FillPattern[Log]"]);
        slide2.Add("Injection Pressure", dicData["InjPressure[Log]"]);
        slide2.Add("InCavity Pressure", dicData["PressureXY[Plot]"]);
        slide2.Add("During Filling", dicData["ClampForce_Fill[Log]"]);
        slide2.Add("During Packing", dicData["ClampForce_Pack[Log]"]);
        slide2.Add("More Than", dicData["ClampForce_More[Log]"]);
        if (dicData["WeldLine[User]"].ToUpper().Contains("DISCUSSION"))
          stringBuilder1.Append("weld line");
        slide2.Add("Weld Line", dicData["WeldLine[User]"]);
        if (dicData["AirTrap[User]"].ToUpper().Contains("DISCUSSION"))
        {
          if (stringBuilder1.Length != 0)
            stringBuilder1.Append(", ");
          stringBuilder1.Append("air trap");
        }
        else if (dicData["AirTrap[User]"].Contains("NG"))
          stringBuilder2.Append("air trap");
        slide2.Add("Air Trap", dicData["AirTrap[User]"]);
        slide2.Add("Shear Rate", dicData["ShearRate[Plot]"] + "|" + dicData["ShearRate[MF]"]);
        slide2.Add("Shear Stress", dicData["ShearStress[Plot]"] + "|" + dicData["ShearStress[MF]"]);
        if (dicData["Shrinkage/Sink[User]"].ToUpper().Contains("DISCUSSION"))
        {
          if (stringBuilder1.Length != 0)
            stringBuilder1.Append(", ");
          stringBuilder1.Append("sink mark");
        }
        slide2.Add("Shrinkage/Sink", dicData["Shrinkage/Sink[User]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cooling[User]")))
          slide2.Add("Cooling", dicData["Cooling[User]"].Split('|')[1]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperature[Plot]")))
          slide2.Add("Cycle Time", dicData["TimeToReachEjectionTemperature[Plot]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionX[Plot]")))
        {
          string[] strArray = dicData["DeflectionX[Plot]"].Split('/');
          double num1 = clsReportUtill.ConvertToDouble(strArray[0]);
          double num2 = clsReportUtill.ConvertToDouble(strArray[1]);
          slide2.Add("Deflection X", Math.Abs(num2 - num1).ToString() + "|" + (object) num1 + "|" + (object) num2);
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionY[Plot]")))
        {
          string[] strArray = dicData["DeflectionY[Plot]"].Split('/');
          double num3 = clsReportUtill.ConvertToDouble(strArray[0]);
          double num4 = clsReportUtill.ConvertToDouble(strArray[1]);
          slide2.Add("Deflection Y", Math.Abs(num4 - num3).ToString() + "|" + (object) num3 + "|" + (object) num4);
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionZ[Plot]")))
        {
          string[] strArray = dicData["DeflectionZ[Plot]"].Split('/');
          double num5 = clsReportUtill.ConvertToDouble(strArray[0]);
          double num6 = clsReportUtill.ConvertToDouble(strArray[1]);
          slide2.Add("Deflection Z", Math.Abs(num6 - num5).ToString() + "|" + (object) num5 + "|" + (object) num6);
        }
        if (stringBuilder1.Length == 0 && stringBuilder2.Length == 0)
        {
          stringBuilder3.Append("There is no problem in Injection molding." + Environment.NewLine);
        }
        else
        {
          if (stringBuilder1.Length != 0)
            stringBuilder3.Append("Need discussion about " + stringBuilder1.ToString() + Environment.NewLine);
          if (stringBuilder2.Length != 0)
            stringBuilder3.Append("There is " + stringBuilder2.ToString() + " problem." + Environment.NewLine);
        }
        stringBuilder3.Append(dicData["Countermeasure[User]"]);
        slide2.Add("Countermeasure", stringBuilder3.ToString());
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide2):" + ex.Message));
      }
      return slide2;
    }

    private static Dictionary<string, string> GetSlide3(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide3 = new Dictionary<string, string>();
      try
      {
        slide3.Add("Item", dicData["Item[User]"]);
        slide3.Add("Pressure", dicData["InjPressure[Log]"]);
        slide3.Add("Temp At Flow Front", dicData["TemperatureAtFlowFront[Plot]"]);
        slide3.Add("Air Trap", dicData["AirTrap[User]"].Split('|')[0]);
        slide3.Add("Volumetric Shrinkage", dicData["VolumetricShrinkage[Log]"]);
        slide3.Add("Sink Marks Estimate", dicData["Shrinkage/Sink[User]"].Split('|')[0]);
        slide3.Add("Weld Line", dicData["WeldLine[User]"].Split('|')[0]);
        slide3.Add("Shear Stress", dicData["ShearStress[Plot]"]);
        slide3.Add("Shear Rate", dicData["ShearRate[Plot]"]);
        slide3.Add("Projection Area", dicData["ProjectionArea[Log]"]);
        slide3.Add("Clamp Force", dicData["ClampForce_Pack[Log]"]);
        slide3.Add("Part Weight", dicData["PartWeight[Plot]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionAll[Plot]")))
        {
          if (dicData["DeflectionAll[User]"].Split('/')[0] == "Anchor")
            slide3.Add("DeflectionAll_A", dicData["DeflectionAll_AnchorPlan[Plot]"]);
          slide3.Add("DeflectionAll_B", dicData["DeflectionAll_BestFit[Plot]"]);
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionX[Plot]")))
        {
          if (dicData["DeflectionX[User]"].Split('/')[0] == "Anchor")
            slide3.Add("DeflectionX_A", dicData["DeflectionX_AnchorPlan[Plot]"]);
          slide3.Add("DeflectionX_B", dicData["DeflectionX_BestFit[Plot]"]);
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionY[Plot]")))
        {
          if (dicData["DeflectionY[User]"].Split('/')[0] == "Anchor")
            slide3.Add("DeflectionY_A", dicData["DeflectionY_AnchorPlan[Plot]"]);
          slide3.Add("DeflectionY_B", dicData["DeflectionY_BestFit[Plot]"]);
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionZ[Plot]")))
        {
          if (dicData["DeflectionZ[User]"].Split('/')[0] == "Anchor")
            slide3.Add("DeflectionZ_A", dicData["DeflectionZ_AnchorPlan[Plot]"]);
          slide3.Add("DeflectionZ_B", dicData["DeflectionZ_BestFit[Plot]"]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide3):" + ex.Message));
      }
      return slide3;
    }

    private static Dictionary<string, string> GetSlide4(Dictionary<string, string> dicData)
    {
      StringBuilder stringBuilder = new StringBuilder();
      Dictionary<string, string> slide4 = new Dictionary<string, string>();
      try
      {
        stringBuilder.Clear();
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitReynoldsNumber[Plot]")))
        {
          stringBuilder.Append(dicData["CircuitReynoldsNumber[Plot]"].Replace("|", "/"));
          double num = clsReportUtill.ConvertToDouble(dicData["CircuitReynoldsNumber[Plot]"].Split('|')[0]);
          string p_strValue = clsReportUtill.ReadINI("Value", "CircuitReynoldsNumber", clsReportDefine.g_diCfg.FullName + "\\SLReport\\ResultConfig.ini");
          if (p_strValue != string.Empty)
          {
            stringBuilder.Append("|");
            if (num >= clsReportUtill.ConvertToDouble(p_strValue))
              stringBuilder.Append("OK");
            else
              stringBuilder.Append("NG");
          }
          slide4.Add("Circuit Reynolds Number", stringBuilder.ToString());
        }
        stringBuilder.Clear();
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperature(Core)[Log]")))
        {
          stringBuilder.Append(dicData["CircuitCoolantTemperature(Core)[Log]"]);
          string[] strArray = dicData["CircuitCoolantTemperature(Core)[Log]"].Split('|');
          double num = clsReportUtill.ConvertToDouble(strArray[1]) - clsReportUtill.ConvertToDouble(strArray[0]);
          string p_strValue = clsReportUtill.ReadINI("Value", "CircuitCoolantTempCore", clsReportDefine.g_diCfg.FullName + "\\SLReport\\ResultConfig.ini");
          if (p_strValue != string.Empty)
          {
            stringBuilder.Append("/");
            if (num <= clsReportUtill.ConvertToDouble(p_strValue))
              stringBuilder.Append("OK");
            else
              stringBuilder.Append("NG");
          }
          slide4.Add("Circuit Coolant Temperature(Core)", stringBuilder.ToString());
        }
        stringBuilder.Clear();
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperature(Cavity)[Log]")))
        {
          stringBuilder.Append(dicData["CircuitCoolantTemperature(Cavity)[Log]"]);
          string[] strArray = dicData["CircuitCoolantTemperature(Cavity)[Log]"].Split('|');
          double num = clsReportUtill.ConvertToDouble(strArray[1]) - clsReportUtill.ConvertToDouble(strArray[0]);
          string p_strValue = clsReportUtill.ReadINI("Value", "CircuitCoolantTempCavity", clsReportDefine.g_diCfg.FullName + "\\SLReport\\ResultConfig.ini");
          if (p_strValue != string.Empty)
          {
            stringBuilder.Append("/");
            if (num <= clsReportUtill.ConvertToDouble(p_strValue))
              stringBuilder.Append("OK");
            else
              stringBuilder.Append("NG");
          }
          slide4.Add("Circuit Coolant Temperature(Cavity)", stringBuilder.ToString());
        }
        stringBuilder.Clear();
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitFlowRate[Log]")))
        {
          stringBuilder.Append(dicData["CircuitFlowRate[Log]"]);
          double num = clsReportUtill.ConvertToDouble(dicData["CircuitFlowRate[Log]"]);
          string p_strValue = clsReportUtill.ReadINI("Value", "CircuitFlowRate", clsReportDefine.g_diCfg.FullName + "\\SLReport\\ResultConfig.ini");
          if (p_strValue != string.Empty)
          {
            stringBuilder.Append("|");
            if (num >= clsReportUtill.ConvertToDouble(p_strValue))
              stringBuilder.Append("OK");
            else
              stringBuilder.Append("NG");
          }
          slide4.Add("Circuit Flow Rate", stringBuilder.ToString());
        }
        slide4.Add("Circuit Pressure", dicData["CircuitPressure[Plot]"]);
        slide4.Add("Cooling Efficiency At The Surface", dicData["CoolingEfficiencyAtTheSurface[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionAll[Plot]")))
          slide4.Add("Global Deflection", dicData["DeflectionAll[Plot]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionCooling[Plot]")))
          slide4.Add("Cooling Deflection", dicData["DeflectionCooling[Plot]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionShrinkage[Plot]")))
          slide4.Add("Shrinkage Deflection", dicData["DeflectionShrinkage[Plot]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionOrientation[Plot]")))
          slide4.Add("Orientation Deflection", dicData["DeflectionOrientation[Plot]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide4):" + ex.Message));
      }
      return slide4;
    }

    private static Dictionary<string, string> GetSlide5(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide5 = new Dictionary<string, string>();
      try
      {
        slide5.Add("Phase", dicData["Item[User]"]);
        slide5.Add("Step", dicData["Step[User]"]);
        slide5.Add("Sequence", dicData["Sequence[MF]"]);
        slide5.Add("Mesh Type", dicData["Mesh[MF]"]);
        slide5.Add("Element No", dicData["ElementNo[Log]"]);
        slide5.Add("Number Of Layer Through Thickness", dicData["NumberOfLayerThroughThickness[Log]"]);
        slide5.Add("Projected Area", dicData["ProjectionArea[Log]"]);
        slide5.Add("Image", dicData["PartModelAndMesh[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide5):" + ex.Message));
      }
      return slide5;
    }

    private static Dictionary<string, string> GetSlide6(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide6 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VolumeByComponents[Log]")))
          slide6.Add("Volume By Components", dicData["VolumeByComponents[Log]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "HRSToPartVolumeRatio[Log]")))
          slide6.Add("HRS To Part Volume Ratio", dicData["HRSToPartVolumeRatio[Log]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ManifoldSize[User]")))
          slide6.Add("Manifold Size", dicData["ManifoldSize[User]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "NozzleSize[User]")))
          slide6.Add("Nozzle Size", dicData["NozzleSize[User]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ValvePinSize[User]")))
          slide6.Add("Valve Pin Size", dicData["ValvePinSize[User]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "NozzleGateSize[User]")))
          slide6.Add("Nozzle Gate Size", dicData["NozzleGateSize[User]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ManifoldVolume[User]")))
          slide6.Add("Manifold Volume", dicData["ManifoldVolume[User]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ColdRunnerCrossSectionIs[MF]")))
          slide6.Add("Cold Runner Cross-Section Is", dicData["ColdRunnerCrossSectionIs[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ColdRunnerSize[MF]")))
          slide6.Add("Cold Runner Size", dicData["ColdRunnerSize[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ColdRunnerVolume[Log]")))
          slide6.Add("Cold Runner Volume", dicData["ColdRunnerVolume[Log]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ColdGateType[MF]")))
          slide6.Add("Cold Gate Type", dicData["ColdGateType[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ColdGateSize[MF]")))
          slide6.Add("Cold Gate Size", dicData["ColdGateSize[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CoolingLineDiameter[MF]")))
          slide6.Add("Cooling Line Diameter", dicData["CoolingLineDiameter[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "BaffleDiameter[MF]")))
          slide6.Add("Baffle Diameter", dicData["BaffleDiameter[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "HeatDiameter[User]")))
          slide6.Add("Heat Diameter", dicData["HeatDiameter[User]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "HeatPipeEffectiveness[User]")))
          slide6.Add("Heat Pipe Effectiveness", dicData["HeatPipeEffectiveness[User]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "RunnerDetailAndGatePosition[IMG]")))
        {
          slide6.Add("Image1", dicData["RunnerDetailAndGatePosition[IMG]"]);
          if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CoolingChannelLayout[IMG]")))
            slide6.Add("Image2", dicData["CoolingChannelLayout[IMG]"]);
        }
        else if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CoolingChannelLayout[IMG]")))
          slide6.Add("Image1", dicData["CoolingChannelLayout[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide6):" + ex.Message));
      }
      return slide6;
    }

    private static Dictionary<string, string> GetSlide7(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide7 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Viscosity[MF]")))
          slide7.Add("Image1", dicData["Viscosity[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SpecifiedVolume[MF]")))
          slide7.Add("Image2", dicData["SpecifiedVolume[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Manufacturer[MF]")))
          slide7.Add("Manufacturer", dicData["Manufacturer[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TradeName[MF]")))
          slide7.Add("Trade Name", dicData["TradeName[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FamilyAbbreviation[MF]")))
          slide7.Add("Family Abbreviation", dicData["FamilyAbbreviation[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FibersFillers[MF]")))
          slide7.Add("Fibers Fillers", dicData["FibersFillers[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MaterialIndicator[MF]")))
          slide7.Add("Material Indicator", dicData["MaterialIndicator[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DateTested[MF]")))
          slide7.Add("Date Tested", dicData["DateTested[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MeltTempRange[MF]")))
          slide7.Add("Melt Temp Range", dicData["MeltTempRange[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MoldTempRange[MF]")))
          slide7.Add("Mold Temp Range", dicData["MoldTempRange[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "EjectionTemp[MF]")))
          slide7.Add("Ejection Temp", dicData["EjectionTemp[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TransitionTemp[MF]")))
          slide7.Add("Transition Temp", dicData["TransitionTemp[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CoolingTime[Log]")))
          slide7.Add("Cooling Time", dicData["CoolingTime[Log]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CoolantInletTemp[Log]")))
          slide7.Add("Coolant Inlet Temperature", dicData["CoolantInletTemp[Log]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitFlowRate[Log]")))
          slide7.Add("Coolant Control Flow Rate", dicData["CircuitFlowRate[Log]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MeltTemp[MF]")))
          slide7.Add("Melt Temp", dicData["MeltTemp[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MoldTemp[MF]")))
          slide7.Add("Mold Temp", dicData["MoldTemp[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime[MF]")))
          slide7.Add("Fill Time", dicData["FillTime[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTimeType[MF]")))
          slide7.Add("Fill Time Type", dicData["FillTimeType[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VPSwitchover[MF]")))
          slide7.Add("V/P Switchover", dicData["VPSwitchover[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PackingProfile[MF]")))
          slide7.Add("Packing Profile", dicData["PackingProfile[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CoolantControl[MF]")))
          slide7.Add("Coolant Control", dicData["CoolantControl[MF]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide7):" + ex.Message));
      }
      return slide7;
    }

    private static Dictionary<string, string> GetSlide8(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide8 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CoolantInletTemp[Log]")))
          slide8.Add("Cool Inlet Temperature", dicData["CoolantInletTemp[Log]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureMold[IMG]")))
          slide8.Add("Image", dicData["TemperatureMold[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide8):" + ex.Message));
      }
      return slide8;
    }

    private static Dictionary<string, string> GetSlide9(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide9 = new Dictionary<string, string>();
      try
      {
        KeyValuePair<string, string>[] array = dicData.Where<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key.Contains("FlowPattern"))).ToArray<KeyValuePair<string, string>>();
        if (array.Length != 0)
        {
          slide9.Add("Image1", array[0].Value);
          slide9.Add("Image2", array[1].Value);
          slide9.Add("Image3", array[2].Value);
          slide9.Add("Image4", array[3].Value);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide9):" + ex.Message));
      }
      return slide9;
    }

    private static Dictionary<string, string> GetSlide10(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide10 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Filling[IMG]")))
          slide10.Add("Animation", dicData["Filling[IMG]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillingSmall[IMG]")))
          slide10.Add("Image", dicData["FillingSmall[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide10):" + ex.Message));
      }
      return slide10;
    }

    private static Dictionary<string, string> GetSlide11(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide11 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Velocity[IMG]")))
          slide11.Add("Image", dicData["Velocity[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide11):" + ex.Message));
      }
      return slide11;
    }

    private static Dictionary<string, string> GetSlide12(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide12 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "GrowFrom[IMG]")))
          slide12.Add("Image", dicData["GrowFrom[IMG]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "GrowFrom[Node]")))
          slide12.Add("Gates", dicData["GrowFrom[Node]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VolumeByComponents[Log]")))
          slide12.Add("Part Volume", dicData["VolumeByComponents[Log]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide12):" + ex.Message));
      }
      return slide12;
    }

    private static Dictionary<string, string> GetSlide13(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide13 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "InjPressure[Log]")))
          slide13.Add("Injection Pressure", dicData["InjPressure[Log]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PressureXY[Plot]")))
          slide13.Add("InCavity Pressure", dicData["PressureXY[Plot]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PressureAtVPSwitchover[IMG]")))
          slide13.Add("Image1", dicData["PressureAtVPSwitchover[IMG]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PressureTable[IMG]")))
          slide13.Add("Image2", dicData["PressureTable[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide13):" + ex.Message));
      }
      return slide13;
    }

    private static Dictionary<string, string> GetSlide14(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide14 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PressureAtEndOfFill(InjectionPressure)[Plot]")))
          slide14.Add("Injection Pressure", dicData["PressureAtEndOfFill(InjectionPressure)[Plot]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PressureAtEndOfFill(InCavityPressure)[Plot]")))
          slide14.Add("InCavity Pressure", dicData["PressureAtEndOfFill(InCavityPressure)[Plot]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PressureAtEndOfFill[IMG]")))
          slide14.Add("Image", dicData["PressureAtEndOfFill[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide14):" + ex.Message));
      }
      return slide14;
    }

    private static Dictionary<string, string> GetSlide15(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide15 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PressureAtInjectionLocation[Plot]")) && dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PressureAtInjectionLocation[Log]")))
          slide15.Add("Injection Pressure", dicData["PressureAtInjectionLocation[Plot]"] + "/" + dicData["PressureAtInjectionLocation[Log]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PressureAtInjectionLocation[IMG]")))
          slide15.Add("Image", dicData["PressureAtInjectionLocation[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide15):" + ex.Message));
      }
      return slide15;
    }

    private static Dictionary<string, string> GetSlide16(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide16 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PressureXY[Plot]")))
          slide16.Add("Pressure At All Gate At Transfer", dicData["PressureXY[Plot]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PressureXY[IMG]")))
          slide16.Add("Image", dicData["PressureXY[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide16):" + ex.Message));
      }
      return slide16;
    }

    private static Dictionary<string, string> GetSlide17(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide17 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ClampForce_Fill[Log]")))
          slide17.Add("During Filling", dicData["ClampForce_Fill[Log]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ClampForce_Pack[Log]")))
          slide17.Add("During Packing", dicData["ClampForce_Pack[Log]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ClampForce_More[Log]")))
          slide17.Add("More Than", dicData["ClampForce_More[Log]"]);
        slide17.Add("Image", dicData["ClampForce[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide17):" + ex.Message));
      }
      return slide17;
    }

    private static Dictionary<string, string> GetSlide18(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide18 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFractionAtGate[Plot]")))
          slide18.Add("Frozen Layer Fraction At Gate", dicData["FrozenLayerFractionAtGate[Plot]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFractionAtGate[IMG]")))
          slide18.Add("Image", dicData["FrozenLayerFractionAtGate[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide18):" + ex.Message));
      }
      return slide18;
    }

    private static Dictionary<string, string> GetSlide19(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide19 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureAtFlowFront[Plot]")))
          slide19.Add("Temperature At Flow Front", dicData["TemperatureAtFlowFront[Plot]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TransitionTemp[MF]")))
          slide19.Add("Transition Temperature", dicData["TransitionTemp[MF]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureAtFlowFront[IMG]")))
          slide19.Add("Image", dicData["TemperatureAtFlowFront[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide19):" + ex.Message));
      }
      return slide19;
    }

    private static Dictionary<string, string> GetSlide20(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide20 = new Dictionary<string, string>();
      try
      {
        slide20.Add("Shear Rate(P)", dicData["ShearRate[Plot]"]);
        slide20.Add("Shear Rate(M)", dicData["ShearRate[MF]"]);
        slide20.Add("Image1", dicData["ShearRate[IMG]"]);
        slide20.Add("Image2", dicData["ShearRateXY[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide20):" + ex.Message));
      }
      return slide20;
    }

    private static Dictionary<string, string> GetSlide21(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide21 = new Dictionary<string, string>();
      try
      {
        slide21.Add("Shear Stress(P)", dicData["ShearStress[Plot]"]);
        slide21.Add("Shear Stress(M)", dicData["ShearStress[MF]"]);
        slide21.Add("Image1", dicData["ShearStress[IMG]"]);
        slide21.Add("Image2", dicData["ShearStressXY[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide21):" + ex.Message));
      }
      return slide21;
    }

    private static Dictionary<string, string> GetSlide22(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide22 = new Dictionary<string, string>();
      try
      {
        slide22.Add("Image", dicData["WeldLine[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide22):" + ex.Message));
      }
      return slide22;
    }

    private static Dictionary<string, string> GetSlide23(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide23 = new Dictionary<string, string>();
      try
      {
        slide23.Add("Image", dicData["AirTrap[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide23):" + ex.Message));
      }
      return slide23;
    }

    private static Dictionary<string, string> GetSlide24(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide24 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "AverageVolumetricShrinkage[IMG]")))
          slide24.Add("Image1", dicData["AverageVolumetricShrinkage[IMG]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Temperature[IMG]")))
          slide24.Add("Image2", dicData["Temperature[IMG]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VolumetricScale[IMG]")))
          slide24.Add("Image3", dicData["VolumetricScale[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide24):" + ex.Message));
      }
      return slide24;
    }

    private static Dictionary<string, string> GetSlide25(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide25 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperature[Plot]")))
          slide25.Add("Time To Reach Ejection Temperature", dicData["TimeToReachEjectionTemperature[Plot]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperature1[IMG]")))
          slide25.Add("Image1", dicData["TimeToReachEjectionTemperature1[IMG]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperature2[IMG]")))
          slide25.Add("Image2", dicData["TimeToReachEjectionTemperature2[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide25):" + ex.Message));
      }
      return slide25;
    }

    private static Dictionary<string, string> GetSlide26(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide26 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureMold_Front[IMG]")))
          slide26.Add("Image", dicData["TemperatureMold_Front[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide26):" + ex.Message));
      }
      return slide26;
    }

    private static Dictionary<string, string> GetSlide27(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide27 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureMold_Back[IMG]")))
          slide27.Add("Image", dicData["TemperatureMold_Back[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide27):" + ex.Message));
      }
      return slide27;
    }

    private static Dictionary<string, string> GetSlide28(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide28 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitReynoldsNumber[Log]")))
          slide28.Add("Circuit Reynold Number", dicData["CircuitReynoldsNumber[Log]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitReynoldsNumber[IMG]")))
          slide28.Add("Image", dicData["CircuitReynoldsNumber[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide28):" + ex.Message));
      }
      return slide28;
    }

    private static Dictionary<string, string> GetSlide29(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide29 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitFlowRate[Log]")))
          slide29.Add("Circuit Flow Rate", dicData["CircuitFlowRate[Log]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitFlowRate[IMG]")))
          slide29.Add("Image", dicData["CircuitFlowRate[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide29):" + ex.Message));
      }
      return slide29;
    }

    private static Dictionary<string, string> GetSlide30(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide30 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperature[Plot]")))
          slide30.Add("Circuit Coolant Temperature", dicData["CircuitCoolantTemperature[Plot]"].Split('|')[2]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperature_Front[IMG]")))
          slide30.Add("Image1", dicData["CircuitCoolantTemperature_Front[IMG]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperature_Side[IMG]")))
          slide30.Add("Image2", dicData["CircuitCoolantTemperature_Side[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide30):" + ex.Message));
      }
      return slide30;
    }

    private static Dictionary<string, string> GetSlide32(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide32 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ShrinkageValue[User]")))
          slide32.Add("Shrinkage", dicData["ShrinkageValue[User]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionAll[Plot]")))
          slide32.Add("Deflection All", dicData["DeflectionAll[Plot]"].Split('/')[1]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionAll[IMG]")))
          slide32.Add("Image", dicData["DeflectionAll[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide32):" + ex.Message));
      }
      return slide32;
    }

    private static Dictionary<string, string> GetSlide33(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide33 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ShrinkageValue[User]")))
          slide33.Add("Shrinkage", dicData["ShrinkageValue[User]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionAll_Iso[IMG]")))
          slide33.Add("Image1", dicData["DeflectionAll_Iso[IMG]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionShrinkage[IMG]")))
          slide33.Add("Image2", dicData["DeflectionShrinkage[IMG]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionCooling[IMG]")))
          slide33.Add("Image3", dicData["DeflectionCooling[IMG]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionOrientation[IMG]")))
          slide33.Add("Image4", dicData["DeflectionOrientation[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide33):" + ex.Message));
      }
      return slide33;
    }

    private static Dictionary<string, string> GetSlide34(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide34 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ShrinkageValue[User]")))
          slide34.Add("Shrinkage", dicData["ShrinkageValue[User]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionX_BestFit[Plot]")))
          slide34.Add("Deflection X", dicData["DeflectionX_BestFit[Plot]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionX_BestFit[IMG]")))
          slide34.Add("Image", dicData["DeflectionX_BestFit[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide34):" + ex.Message));
      }
      return slide34;
    }

    private static Dictionary<string, string> GetSlide35(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide35 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionX_AnchorPlan[Plot]")))
          slide35.Add("Deflection X", dicData["DeflectionX_AnchorPlan[Plot]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionX_AnchorPlan[IMG]")))
          slide35.Add("Image", dicData["DeflectionX_AnchorPlan[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide35):" + ex.Message));
      }
      return slide35;
    }

    private static Dictionary<string, string> GetSlide36(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide36 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionY_BestFit[Plot]")))
          slide36.Add("Deflection Y", dicData["DeflectionY_BestFit[Plot]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionY_BestFit[IMG]")))
          slide36.Add("Image", dicData["DeflectionY_BestFit[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide36):" + ex.Message));
      }
      return slide36;
    }

    private static Dictionary<string, string> GetSlide37(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide37 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionY_AnchorPlan[Plot]")))
          slide37.Add("Deflection Y", dicData["DeflectionY_AnchorPlan[Plot]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionY_AnchorPlan[IMG]")))
          slide37.Add("Image", dicData["DeflectionY_AnchorPlan[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide37):" + ex.Message));
      }
      return slide37;
    }

    private static Dictionary<string, string> GetSlide38(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide38 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionZ_BestFit[Plot]")))
          slide38.Add("Deflection Z", dicData["DeflectionZ_BestFit[Plot]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionZ_BestFit[IMG]")))
          slide38.Add("Image", dicData["DeflectionZ_BestFit[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide38):" + ex.Message));
      }
      return slide38;
    }

    private static Dictionary<string, string> GetSlide39(Dictionary<string, string> dicData)
    {
      Dictionary<string, string> slide39 = new Dictionary<string, string>();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionZ_AnchorPlan[Plot]")))
          slide39.Add("Deflection Z", dicData["DeflectionZ_AnchorPlan[Plot]"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionZ_AnchorPlan[IMG]")))
          slide39.Add("Image", dicData["DeflectionZ_AnchorPlan[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetSlide39):" + ex.Message));
      }
      return slide39;
    }

    public static void SetSlide1(Slide p_slData, Dictionary<string, string> dicData)
    {
      string str1 = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Title == "S1_Title")).FirstOrDefault<Shape>();
        if (shape1 != null && dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Item")) && dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Step")))
        {
          if (dicData["Item"] != "")
            str1 = dicData["Item"];
          if (dicData["Step"] != "")
            str1 = str1 + " (" + dicData["Step"] + ")";
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter("Moldflow Simulation Results of " + Environment.NewLine + str1);
          textRange.Font.Bold = MsoTriState.msoTrue;
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Title == "S1_WrittenBy")).FirstOrDefault<Shape>();
        if (shape2 != null && dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Engineer")) && dicData["Engineer"] != "")
        {
          // ISSUE: reference to a compiler-generated method
          shape2.TextFrame.TextRange.InsertAfter(dicData["Engineer"]);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape3 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Title == "S1_ApprovedBy")).FirstOrDefault<Shape>();
        if (shape3 != null && dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Manager")) && dicData["Manager"] != "")
        {
          // ISSUE: reference to a compiler-generated method
          shape3.TextFrame.TextRange.InsertAfter(dicData["Manager"]);
        }
        DateTime now = DateTime.Now;
        string str2 = new CultureInfo("en-US").DateTimeFormat.MonthNames[now.Month - 1] + "/" + (object) now.Day + "/" + (object) now.Year;
        // ISSUE: variable of a compiler-generated type
        Shape shape4 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Title == "S1_Sequence")).FirstOrDefault<Shape>();
        if (shape4 != null && dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Sequence")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape4.TextFrame.TextRange;
          textRange.Text = "";
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter("Engineer:" + Environment.NewLine + "Manager:" + Environment.NewLine + dicData["Sequence"].Replace("|", " + ") + Environment.NewLine + str2);
        }
        if (!dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 258f, (float) byte.MaxValue, 497f, 294f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide1):" + ex.Message));
      }
    }

    public static void SetSlide2(Slide p_slData, Dictionary<string, string> dicData)
    {
      string empty = string.Empty;
      StringBuilder stringBuilder1 = new StringBuilder();
      StringBuilder stringBuilder2 = new StringBuilder();
      StringBuilder stringBuilder3 = new StringBuilder();
      bool flag1 = false;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S2_Content1")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange1 = shape1.TextFrame.TextRange.InsertAfter("Fill pattern");
          textRange1.Font.Bold = MsoTriState.msoTrue;
          textRange1.Font.Size = 20f;
          textRange1.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange2 = textRange1.InsertAfter("\v" + dicData["Fill Pattern"]);
          textRange2.Font.Bold = MsoTriState.msoFalse;
          textRange2.Font.Size = 14f;
          if (dicData["Fill Pattern"] == "Short shot")
          {
            textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            flag1 = true;
          }
          else
            textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange3 = textRange2.InsertAfter(Environment.NewLine + "Pressure");
          textRange3.Font.Bold = MsoTriState.msoTrue;
          textRange3.Font.Size = 20f;
          textRange3.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange4 = textRange3.InsertAfter("\vInjection pressure : ");
          textRange4.Font.Bold = MsoTriState.msoFalse;
          textRange4.Font.Size = 14f;
          textRange4.Font.Underline = MsoTriState.msoFalse;
          textRange4.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          double num1 = clsReportUtill.ConvertToDouble(dicData["Injection Pressure"]);
          double num2 = clsReportUtill.ConvertToDouble(clsReportUtill.ReadINI("Value", "InjPressure2", clsReportDefine.g_diCfg.FullName + "\\SLReport\\ResultConfig.ini"));
          double num3 = clsReportUtill.ConvertToDouble(clsReportUtill.ReadINI("Value", "InjPressure1", clsReportDefine.g_diCfg.FullName + "\\SLReport\\ResultConfig.ini"));
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange5 = textRange4.InsertAfter(string.Format("{0:0.0}", (object) Math.Round(num1, 1)) + " MPa  ");
          textRange5.Font.Bold = MsoTriState.msoTrue;
          textRange5.Font.Size = 14f;
          textRange5.Font.Underline = MsoTriState.msoFalse;
          if (num1 < num2)
            textRange5.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          else if (num1 >= num2 && num1 < num3)
          {
            stringBuilder1.Append("Injection");
            textRange5.Font.Color.RGB = ColorTranslator.ToOle(Color.Orange);
          }
          else
          {
            stringBuilder2.Append("Injection");
            textRange5.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange6 = textRange5.InsertAfter("< " + num3.ToString() + " MPa");
          textRange6.Font.Bold = MsoTriState.msoFalse;
          textRange6.Font.Size = 14f;
          textRange6.Font.Underline = MsoTriState.msoTrue;
          textRange6.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange7 = textRange6.InsertAfter("\vIn-Cavity pressure : ");
          textRange7.Font.Bold = MsoTriState.msoFalse;
          textRange7.Font.Size = 14f;
          textRange7.Font.Underline = MsoTriState.msoFalse;
          textRange7.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          double num4 = clsReportUtill.ConvertToDouble(dicData["InCavity Pressure"]);
          double num5 = clsReportUtill.ConvertToDouble(clsReportUtill.ReadINI("Value", "InCavityPressure2", clsReportDefine.g_diCfg.FullName + "\\SLReport\\ResultConfig.ini"));
          double num6 = clsReportUtill.ConvertToDouble(clsReportUtill.ReadINI("Value", "InCavityPressure1", clsReportDefine.g_diCfg.FullName + "\\SLReport\\ResultConfig.ini"));
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange8 = textRange7.InsertAfter(string.Format("{0:0.0}", (object) Math.Round(num4, 1)) + " MPa  ");
          textRange8.Font.Bold = MsoTriState.msoTrue;
          textRange8.Font.Size = 14f;
          textRange8.Font.Underline = MsoTriState.msoFalse;
          if (num4 < num5)
            textRange8.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          else if (num4 >= num5 && num4 < num6)
          {
            if (stringBuilder1.Length != 0)
              stringBuilder1.Append("/");
            stringBuilder1.Append("In-Cavity");
            textRange8.Font.Color.RGB = ColorTranslator.ToOle(Color.Orange);
          }
          else
          {
            if (stringBuilder2.Length != 0)
              stringBuilder2.Append("/");
            stringBuilder2.Append("In-Cavity");
            textRange8.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange9 = textRange8.InsertAfter("< " + num6.ToString() + " MPa (Inc D3 : 144MPa)");
          textRange9.Font.Bold = MsoTriState.msoFalse;
          textRange9.Font.Size = 14f;
          textRange9.Font.Underline = MsoTriState.msoTrue;
          textRange9.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange10 = textRange9.InsertAfter(Environment.NewLine + "Clamp force");
          textRange10.Font.Bold = MsoTriState.msoTrue;
          textRange10.Font.Size = 20f;
          textRange10.Font.Underline = MsoTriState.msoFalse;
          textRange10.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange11 = textRange10.InsertAfter("\vDuring Filling : ");
          textRange11.Font.Bold = MsoTriState.msoFalse;
          textRange11.Font.Size = 14f;
          textRange11.Font.Underline = MsoTriState.msoFalse;
          textRange11.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange12 = textRange11.InsertAfter(Math.Round(clsReportUtill.ConvertToDouble(dicData["During Filling"]), 0).ToString() + " ton");
          textRange12.Font.Bold = MsoTriState.msoTrue;
          textRange12.Font.Size = 14f;
          textRange12.Font.Underline = MsoTriState.msoFalse;
          textRange12.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange13 = textRange12.InsertAfter(" & During Packing : ");
          textRange13.Font.Bold = MsoTriState.msoFalse;
          textRange13.Font.Size = 14f;
          textRange13.Font.Underline = MsoTriState.msoFalse;
          textRange13.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange14 = textRange13.InsertAfter(Math.Round(clsReportUtill.ConvertToDouble(dicData["During Packing"]), 0).ToString() + " ton");
          textRange14.Font.Bold = MsoTriState.msoTrue;
          textRange14.Font.Size = 14f;
          textRange14.Font.Underline = MsoTriState.msoFalse;
          textRange14.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange15 = textRange14.InsertAfter("\vMore than ");
          textRange15.Font.Bold = MsoTriState.msoFalse;
          textRange15.Font.Size = 14f;
          textRange15.Font.Underline = MsoTriState.msoFalse;
          textRange15.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange16 = textRange15.InsertAfter(Math.Round(clsReportUtill.ConvertToDouble(dicData["More Than"]), 0).ToString() + " ton");
          textRange16.Font.Bold = MsoTriState.msoTrue;
          textRange16.Font.Size = 14f;
          textRange16.Font.Underline = MsoTriState.msoFalse;
          textRange16.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange17 = textRange16.InsertAfter(" machine");
          textRange17.Font.Bold = MsoTriState.msoFalse;
          textRange17.Font.Size = 14f;
          textRange17.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange18 = textRange17.InsertAfter(Environment.NewLine + "Weld line");
          textRange18.Font.Bold = MsoTriState.msoTrue;
          textRange18.Font.Size = 20f;
          textRange18.Font.Underline = MsoTriState.msoFalse;
          string[] strArray1 = dicData["Weld Line"].Split('|');
          bool flag2 = strArray1[0].Contains("-R");
          string str1 = !(strArray1[1] != string.Empty) ? strArray1[0].Replace("-R", "") : strArray1[1];
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange19 = textRange18.InsertAfter("\v" + str1);
          textRange19.Font.Bold = MsoTriState.msoFalse;
          textRange19.Font.Size = 14f;
          textRange19.Font.Underline = MsoTriState.msoFalse;
          textRange19.Font.Color.RGB = !flag2 ? ColorTranslator.ToOle(Color.Black) : ColorTranslator.ToOle(Color.Red);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange20 = textRange19.InsertAfter(Environment.NewLine + "Air trap");
          textRange20.Font.Bold = MsoTriState.msoTrue;
          textRange20.Font.Size = 20f;
          textRange20.Font.Underline = MsoTriState.msoFalse;
          textRange20.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          string[] strArray2 = dicData["Air Trap"].Split('|');
          bool flag3 = strArray2[0].Contains("NG");
          string str2 = !(strArray2[1] != string.Empty) ? strArray2[0] : strArray2[1];
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange21 = textRange20.InsertAfter("\v" + str2);
          textRange21.Font.Bold = MsoTriState.msoFalse;
          textRange21.Font.Size = 14f;
          textRange21.Font.Underline = MsoTriState.msoFalse;
          textRange21.Font.Color.RGB = !flag3 ? ColorTranslator.ToOle(Color.Black) : ColorTranslator.ToOle(Color.Red);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange22 = textRange21.InsertAfter(Environment.NewLine + "Shear");
          textRange22.Font.Bold = MsoTriState.msoTrue;
          textRange22.Font.Size = 20f;
          textRange22.Font.Underline = MsoTriState.msoFalse;
          textRange22.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange23 = textRange22.InsertAfter("\vMaximum shear rate ");
          textRange23.Font.Bold = MsoTriState.msoFalse;
          textRange23.Font.Size = 14f;
          textRange23.Font.Underline = MsoTriState.msoFalse;
          textRange23.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          string[] strArray3 = dicData["Shear Rate"].Split('|');
          double num7 = clsReportUtill.ConvertToDouble(strArray3[0]);
          double num8 = clsReportUtill.ConvertToDouble(strArray3[1]);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange24 = textRange23.InsertAfter(num7.ToString() + " 1/s  ");
          textRange24.Font.Bold = MsoTriState.msoTrue;
          textRange24.Font.Size = 14f;
          textRange24.Font.Underline = MsoTriState.msoFalse;
          textRange24.Font.Color.RGB = num7 <= num8 ? ColorTranslator.ToOle(Color.Black) : ColorTranslator.ToOle(Color.Red);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange25 = textRange24.InsertAfter("< " + (object) num8 + " 1/s");
          textRange25.Font.Bold = MsoTriState.msoFalse;
          textRange25.Font.Size = 14f;
          textRange25.Font.Underline = MsoTriState.msoTrue;
          textRange25.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange26 = textRange25.InsertAfter("\vMaximum shear stress ");
          textRange26.Font.Bold = MsoTriState.msoFalse;
          textRange26.Font.Underline = MsoTriState.msoFalse;
          textRange26.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          string[] strArray4 = dicData["Shear Stress"].Split('|');
          double num9 = clsReportUtill.ConvertToDouble(strArray4[0]);
          double num10 = clsReportUtill.ConvertToDouble(strArray4[1]);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange27 = textRange26.InsertAfter(num9.ToString() + " MPa  ");
          textRange27.Font.Bold = MsoTriState.msoTrue;
          textRange27.Font.Size = 14f;
          textRange27.Font.Underline = MsoTriState.msoFalse;
          textRange27.Font.Color.RGB = num9 <= num10 ? ColorTranslator.ToOle(Color.Black) : ColorTranslator.ToOle(Color.Red);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange28 = textRange27.InsertAfter("< " + (object) num10 + " MPa");
          textRange28.Font.Bold = MsoTriState.msoFalse;
          textRange28.Font.Size = 14f;
          textRange28.Font.Underline = MsoTriState.msoTrue;
          textRange28.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange29 = textRange28.InsertAfter(Environment.NewLine + "Shrinkage/Sink");
          textRange29.Font.Bold = MsoTriState.msoTrue;
          textRange29.Font.Size = 20f;
          textRange29.Font.Underline = MsoTriState.msoFalse;
          textRange29.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          string[] strArray5 = dicData["Shrinkage/Sink"].Split('|');
          bool flag4 = strArray5[0].Contains("-R");
          string str3 = !(strArray5[1] != string.Empty) ? strArray5[0].Replace("-R", "") : strArray5[1];
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange30 = textRange29.InsertAfter("\v" + str3);
          textRange30.Font.Bold = MsoTriState.msoFalse;
          textRange30.Font.Size = 14f;
          textRange30.Font.Underline = MsoTriState.msoFalse;
          textRange30.Font.Color.RGB = !flag4 ? ColorTranslator.ToOle(Color.Black) : ColorTranslator.ToOle(Color.Red);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S2_Content2")).FirstOrDefault<Shape>();
        if (shape2 == null)
          return;
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange31 = shape2.TextFrame.TextRange.InsertAfter("Cooling");
        textRange31.Font.Bold = MsoTriState.msoTrue;
        textRange31.Font.Size = 20f;
        textRange31.Font.Underline = MsoTriState.msoFalse;
        textRange31.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        // ISSUE: reference to a compiler-generated method
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange32 = dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cooling")) ? textRange31.InsertAfter("\v" + dicData["Cooling"]) : textRange31.InsertAfter("\vNo Cool Sequence");
        textRange32.Font.Bold = MsoTriState.msoFalse;
        textRange32.Font.Size = 14f;
        textRange32.Font.Underline = MsoTriState.msoFalse;
        textRange32.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange33 = textRange32.InsertAfter(Environment.NewLine + "Cycle time");
        textRange33.Font.Bold = MsoTriState.msoTrue;
        textRange33.Font.Size = 20f;
        textRange33.Font.Underline = MsoTriState.msoFalse;
        textRange33.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange34 = textRange33.InsertAfter("\vThe expected cycle time is \" ");
        textRange34.Font.Bold = MsoTriState.msoFalse;
        textRange34.Font.Size = 14f;
        textRange34.Font.Underline = MsoTriState.msoFalse;
        textRange34.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        double num = 0.0;
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cycle Time")))
          num = Math.Round(clsReportUtill.ConvertToDouble(dicData["Cycle Time"]), 0);
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange35 = textRange34.InsertAfter(num.ToString() + " sec");
        textRange35.Font.Bold = MsoTriState.msoTrue;
        textRange35.Font.Size = 14f;
        textRange35.Font.Underline = MsoTriState.msoFalse;
        textRange35.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange36 = textRange35.InsertAfter(" + Mold open time \"");
        textRange36.Font.Bold = MsoTriState.msoFalse;
        textRange36.Font.Size = 14f;
        textRange36.Font.Underline = MsoTriState.msoFalse;
        textRange36.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange37 = textRange36.InsertAfter(Environment.NewLine + "Warpage (");
        textRange37.Font.Bold = MsoTriState.msoTrue;
        textRange37.Font.Size = 20f;
        textRange37.Font.Underline = MsoTriState.msoFalse;
        textRange37.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange38 = textRange37.InsertAfter("Anchor plan, ");
        textRange38.Font.Bold = MsoTriState.msoFalse;
        textRange38.Font.Size = 20f;
        textRange38.Font.Underline = MsoTriState.msoFalse;
        textRange38.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange39 = textRange38.InsertAfter("Best fit)");
        textRange39.Font.Bold = MsoTriState.msoFalse;
        textRange39.Font.Size = 20f;
        textRange39.Font.Underline = MsoTriState.msoFalse;
        textRange39.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange40 = textRange39.InsertAfter("\vZ axis. ");
        textRange40.Font.Bold = MsoTriState.msoFalse;
        textRange40.Font.Size = 14f;
        textRange40.Font.Underline = MsoTriState.msoFalse;
        textRange40.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Deflection Z")))
        {
          string[] strArray = dicData["Deflection Z"].Split('|');
          // ISSUE: reference to a compiler-generated method
          textRange40 = textRange40.InsertAfter(strArray[0] + "mm(" + strArray[1] + "mm~" + strArray[2] + "mm)");
          textRange40.Font.Bold = MsoTriState.msoFalse;
          textRange40.Font.Size = 14f;
          textRange40.Font.Underline = MsoTriState.msoFalse;
          textRange40.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange41 = textRange40.InsertAfter("\vX axis. ");
        textRange41.Font.Bold = MsoTriState.msoFalse;
        textRange41.Font.Size = 14f;
        textRange41.Font.Underline = MsoTriState.msoFalse;
        textRange41.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Deflection X")))
        {
          string[] strArray = dicData["Deflection X"].Split('|');
          // ISSUE: reference to a compiler-generated method
          textRange41 = textRange41.InsertAfter(strArray[0] + "mm(" + strArray[1] + "mm~" + strArray[2] + "mm)");
          textRange41.Font.Bold = MsoTriState.msoFalse;
          textRange41.Font.Size = 14f;
          textRange41.Font.Underline = MsoTriState.msoFalse;
          textRange41.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange42 = textRange41.InsertAfter("\vY axis. ");
        textRange42.Font.Bold = MsoTriState.msoFalse;
        textRange42.Font.Size = 14f;
        textRange42.Font.Underline = MsoTriState.msoFalse;
        textRange42.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Deflection Y")))
        {
          string[] strArray = dicData["Deflection Y"].Split('|');
          // ISSUE: reference to a compiler-generated method
          textRange42 = textRange42.InsertAfter(strArray[0] + "mm(" + strArray[1] + "mm~" + strArray[2] + "mm)");
          textRange42.Font.Bold = MsoTriState.msoFalse;
          textRange42.Font.Size = 14f;
          textRange42.Font.Underline = MsoTriState.msoFalse;
          textRange42.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange43 = textRange42.InsertAfter(Environment.NewLine);
        textRange43.Font.Bold = MsoTriState.msoFalse;
        textRange43.Font.Size = 14f;
        textRange43.Font.Underline = MsoTriState.msoFalse;
        textRange43.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange44 = textRange43.InsertAfter(Environment.NewLine);
        textRange44.Font.Bold = MsoTriState.msoFalse;
        textRange44.Font.Size = 14f;
        textRange44.Font.Underline = MsoTriState.msoFalse;
        textRange44.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange45 = textRange44.InsertAfter(Environment.NewLine + "Countermeasure - recommendation");
        textRange45.Font.Bold = MsoTriState.msoTrue;
        textRange45.Font.Size = 20f;
        textRange45.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        if (stringBuilder1.Length != 0)
        {
          // ISSUE: reference to a compiler-generated method
          textRange45 = textRange45.InsertAfter("\v" + (object) stringBuilder1 + " pressure is rather high. ");
          textRange45.Font.Bold = MsoTriState.msoFalse;
          textRange45.Font.Size = 14f;
          textRange45.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (stringBuilder2.Length != 0)
        {
          // ISSUE: reference to a compiler-generated method
          textRange45 = textRange45.InsertAfter("\v" + (object) stringBuilder2 + " pressure is unsatisfied. ");
          textRange45.Font.Bold = MsoTriState.msoFalse;
          textRange45.Font.Size = 14f;
          textRange45.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (flag1)
        {
          // ISSUE: reference to a compiler-generated method
          textRange45 = textRange45.InsertAfter("\v Short shot occurs.");
          textRange45.Font.Bold = MsoTriState.msoFalse;
          textRange45.Font.Size = 14f;
          textRange45.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        string str4 = dicData["Countermeasure"];
        char[] chArray = new char[1]{ '/' };
        foreach (string str5 in str4.Split(chArray))
        {
          // ISSUE: reference to a compiler-generated method
          textRange45 = textRange45.InsertAfter("\v" + str5);
          textRange45.Font.Bold = MsoTriState.msoFalse;
          textRange45.Font.Size = 14f;
          textRange45.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide2):" + ex.Message));
      }
    }

    public static void SetSlide3(Slide p_slData, Dictionary<string, string> dicData)
    {
      string empty = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Table table1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S3_Table1")).FirstOrDefault<Shape>().Table;
        if (table1 != null)
        {
          table1.Rows[1].Cells[2].Shape.TextFrame.TextRange.Text = dicData["Item"];
          // ISSUE: reference to a compiler-generated method
          table1.Rows[1].Cells[2].Shape.TextFrame.TextRange.Words().Font.Bold = MsoTriState.msoTrue;
          double num1 = clsReportUtill.ConvertToDouble(dicData["Pressure"]);
          double num2 = clsReportUtill.ConvertToDouble(clsReportUtill.ReadINI("Value", "InjPressure2", clsReportDefine.g_diCfg.FullName + "\\SLReport\\ResultConfig.ini"));
          double num3 = clsReportUtill.ConvertToDouble(clsReportUtill.ReadINI("Value", "InjPressure1", clsReportDefine.g_diCfg.FullName + "\\SLReport\\ResultConfig.ini"));
          // ISSUE: variable of a compiler-generated type
          TextRange textRange1 = table1.Rows[2].Cells[2].Shape.TextFrame.TextRange;
          textRange1.Text = string.Format("{0:0.00}", (object) num1) + " MPa";
          textRange1.Font.Color.RGB = num1 >= num2 ? (num1 < num2 || num1 >= num3 ? ColorTranslator.ToOle(Color.Red) : ColorTranslator.ToOle(Color.Orange)) : ColorTranslator.ToOle(Color.Black);
          string[] strArray1 = dicData["Temp At Flow Front"].Split('|');
          table1.Rows[3].Cells[2].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray1[0])) + " ℃ ~ " + string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray1[1])) + " ℃";
          string[] strArray2 = dicData["Weld Line"].ToString().Split('|');
          bool flag1 = strArray2[0].Contains("-R");
          // ISSUE: variable of a compiler-generated type
          TextRange textRange2 = table1.Rows[4].Cells[2].Shape.TextFrame.TextRange;
          textRange2.Text = strArray2[0].Replace("-R", "");
          textRange2.Font.Color.RGB = !flag1 ? ColorTranslator.ToOle(Color.Black) : ColorTranslator.ToOle(Color.Red);
          string[] strArray3 = dicData["Air Trap"].ToString().Split('|');
          bool flag2 = strArray3[0].Contains("NG");
          // ISSUE: variable of a compiler-generated type
          TextRange textRange3 = table1.Rows[5].Cells[2].Shape.TextFrame.TextRange;
          textRange3.Text = strArray3[0];
          textRange3.Font.Color.RGB = !flag2 ? ColorTranslator.ToOle(Color.Black) : ColorTranslator.ToOle(Color.Red);
          string[] strArray4 = dicData["Volumetric Shrinkage"].Split('/');
          table1.Rows[6].Cells[2].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray4[0])) + " ~ " + string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray4[1])) + "% (range " + strArray4[2] + "%)";
          string[] strArray5 = dicData["Sink Marks Estimate"].ToString().Split('|');
          bool flag3 = strArray5[0].Contains("-R");
          // ISSUE: variable of a compiler-generated type
          TextRange textRange4 = table1.Rows[7].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange4.Delete();
          textRange4.Text = strArray5[0].Replace("-R", "");
          textRange4.Font.Color.RGB = !flag3 ? ColorTranslator.ToOle(Color.Black) : ColorTranslator.ToOle(Color.Red);
          table1.Rows[8].Cells[2].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(dicData["Shear Stress"])) + " MPa";
          table1.Rows[9].Cells[2].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(dicData["Shear Rate"])) + " [1/s]";
          table1.Rows[10].Cells[2].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(dicData["Projection Area"])) + " ㎠";
          table1.Rows[11].Cells[2].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(dicData["Clamp Force"])) + " ton";
          table1.Rows[12].Cells[2].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(dicData["Part Weight"])) + " g";
        }
        // ISSUE: variable of a compiler-generated type
        Table table2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S3_Table2")).FirstOrDefault<Shape>().Table;
        if (table2 == null)
          return;
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionAll_B")))
        {
          if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionAll_B")))
          {
            string[] strArray = dicData["DeflectionAll_B"].Split('/');
            table2.Rows[2].Cells[2].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray[1])) + " mm";
          }
          if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionAll_A")))
          {
            string[] strArray = dicData["DeflectionAll_A"].Split('/');
            table2.Rows[3].Cells[2].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray[1])) + " mm";
          }
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionX_B")))
        {
          if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionX_B")))
          {
            string[] strArray = dicData["DeflectionX_B"].Split('/');
            table2.Rows[2].Cells[3].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray[0])) + " ~ " + string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray[1])) + " mm";
          }
          if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionX_A")))
          {
            string[] strArray = dicData["DeflectionX_A"].Split('/');
            table2.Rows[3].Cells[3].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray[0])) + " ~ " + string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray[1])) + " mm";
          }
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionY_B")))
        {
          if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionY_B")))
          {
            string[] strArray = dicData["DeflectionY_B"].Split('/');
            table2.Rows[2].Cells[4].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray[0])) + " ~ " + string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray[1])) + " mm";
          }
          if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionY_A")))
          {
            string[] strArray = dicData["DeflectionY_A"].Split('/');
            table2.Rows[3].Cells[4].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray[0])) + " ~ " + string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray[1])) + " mm";
          }
        }
        if (!dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionZ_B")))
          return;
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionZ_B")))
        {
          string[] strArray = dicData["DeflectionZ_B"].Split('/');
          table2.Rows[2].Cells[5].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray[0])) + " ~ " + string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray[1])) + " mm";
        }
        if (!dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DeflectionZ_A")))
          return;
        string[] strArray6 = dicData["DeflectionZ_A"].Split('/');
        table2.Rows[3].Cells[5].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray6[0])) + " ~ " + string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray6[1])) + " mm";
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception(SetSlide3):" + ex.Message));
      }
    }

    public static void SetSlide4(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S4_Table")).FirstOrDefault<Shape>().Table;
        if (table == null)
          return;
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Reynolds Number")))
        {
          string[] strArray1 = dicData["Circuit Reynolds Number"].Split('|');
          string[] strArray2 = strArray1[0].Split('/');
          table.Rows[2].Cells[2].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray2[0])) + " ~ " + string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray2[1]));
          table.Rows[2].Cells[3].Shape.TextFrame.TextRange.Text = strArray1.Length != 1 ? (!(strArray1[1] == "") ? strArray1[1] : "-") : "-";
        }
        else
        {
          table.Rows[2].Cells[2].Shape.TextFrame.TextRange.Text = "-";
          table.Rows[2].Cells[3].Shape.TextFrame.TextRange.Text = "-";
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Coolant Temperature(Core)")))
        {
          string[] strArray3 = dicData["Circuit Coolant Temperature(Core)"].Split('/');
          string[] strArray4 = strArray3[0].Split('|');
          table.Rows[3].Cells[2].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray4[0])) + "℃ ~ " + string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray4[1])) + "℃ (Δ" + strArray4[2] + "℃)";
          table.Rows[3].Cells[3].Shape.TextFrame.TextRange.Text = strArray3.Length != 1 ? (!(strArray3[1] == "") ? strArray3[1] : "-") : "-";
        }
        else
        {
          table.Rows[3].Cells[2].Shape.TextFrame.TextRange.Text = "℃ ~ ℃ (Δ ℃)";
          table.Rows[3].Cells[3].Shape.TextFrame.TextRange.Text = "-";
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Coolant Temperature(Cavity)")))
        {
          string[] strArray5 = dicData["Circuit Coolant Temperature(Cavity)"].Split('/');
          string[] strArray6 = strArray5[0].Split('|');
          table.Rows[4].Cells[2].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray6[0])) + "℃ ~ " + string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray6[1])) + "℃ (Δ" + strArray6[2] + "℃)";
          table.Rows[4].Cells[3].Shape.TextFrame.TextRange.Text = strArray5.Length != 1 ? (!(strArray5[1] == "") ? strArray5[1] : "-") : "-";
        }
        else
        {
          table.Rows[4].Cells[2].Shape.TextFrame.TextRange.Text = "℃ ~ ℃ (Δ ℃)";
          table.Rows[4].Cells[3].Shape.TextFrame.TextRange.Text = "-";
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Flow Rate")))
        {
          string[] strArray7 = dicData["Circuit Flow Rate"].Split('|');
          string[] strArray8 = strArray7[0].Split('/');
          table.Rows[5].Cells[2].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray8[0])) + " l/min";
          table.Rows[5].Cells[3].Shape.TextFrame.TextRange.Text = strArray7.Length != 1 ? (!(strArray7[1] == "") ? strArray7[1] : "-") : "-";
        }
        else
        {
          table.Rows[5].Cells[2].Shape.TextFrame.TextRange.Text = "-";
          table.Rows[5].Cells[3].Shape.TextFrame.TextRange.Text = "-";
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Temperature, Part(Core)")))
        {
          string[] strArray9 = dicData["Temperature, Part(Core)"].Split('|');
          string[] strArray10 = strArray9[0].Split('/');
          table.Rows[6].Cells[2].Shape.TextFrame.TextRange.Text = strArray10[0] + "℃ ~ " + strArray10[1] + "℃ (Δ" + strArray10[2] + "℃)";
          table.Rows[6].Cells[3].Shape.TextFrame.TextRange.Text = strArray9.Length != 1 ? (!(strArray9[1] == "") ? strArray9[1] : "-") : "-";
        }
        else
        {
          table.Rows[6].Cells[2].Shape.TextFrame.TextRange.Text = "℃ ~ ℃ (Δ ℃)";
          table.Rows[6].Cells[3].Shape.TextFrame.TextRange.Text = "-";
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Temperature, Part(Cavity)")))
        {
          string[] strArray11 = dicData["Temperature, Part(Cavity)"].Split('|');
          string[] strArray12 = strArray11[0].Split('/');
          table.Rows[7].Cells[2].Shape.TextFrame.TextRange.Text = strArray12[0] + "℃ ~ " + strArray12[1] + "℃ (Δ" + strArray12[2] + "℃)";
          table.Rows[7].Cells[3].Shape.TextFrame.TextRange.Text = strArray11.Length != 1 ? (!(strArray11[1] == "") ? strArray11[1] : "-") : "-";
        }
        else
        {
          table.Rows[7].Cells[2].Shape.TextFrame.TextRange.Text = "℃ ~ ℃ (Δ ℃)";
          table.Rows[7].Cells[3].Shape.TextFrame.TextRange.Text = "-";
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Temperature Difference Between Core And Cavity")))
        {
          string[] strArray13 = dicData["Temperature Difference Between Core And Cavity"].Split('|');
          string[] strArray14 = strArray13[0].Split('/');
          table.Rows[8].Cells[2].Shape.TextFrame.TextRange.Text = strArray14[0] + "℃ ~ " + strArray14[1] + "℃ (Δ" + strArray14[2] + "℃)";
          table.Rows[8].Cells[3].Shape.TextFrame.TextRange.Text = strArray13.Length != 1 ? (!(strArray13[1] == "") ? strArray13[1] : "-") : "-";
        }
        else
        {
          table.Rows[8].Cells[2].Shape.TextFrame.TextRange.Text = "℃ ~ ℃ (Δ ℃)";
          table.Rows[8].Cells[3].Shape.TextFrame.TextRange.Text = "-";
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Pressure")))
        {
          string[] strArray15 = dicData["Circuit Pressure"].Split('|');
          string[] strArray16 = strArray15[0].Split('/');
          table.Rows[9].Cells[2].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray16[0])) + " kPa";
          table.Rows[9].Cells[3].Shape.TextFrame.TextRange.Text = strArray15.Length != 1 ? (!(strArray15[1] == "") ? strArray15[1] : "-") : "-";
        }
        else
        {
          table.Rows[9].Cells[2].Shape.TextFrame.TextRange.Text = "-";
          table.Rows[9].Cells[3].Shape.TextFrame.TextRange.Text = "-";
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cooling Efficiency At The Surface")))
        {
          string[] strArray = dicData["Cooling Efficiency At The Surface"].Split('|');
          table.Rows[10].Cells[2].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray[0])) + "%";
          table.Rows[10].Cells[3].Shape.TextFrame.TextRange.Text = strArray.Length != 1 ? (!(strArray[1] == "") ? strArray[1] : "-") : "-";
        }
        else
        {
          table.Rows[10].Cells[2].Shape.TextFrame.TextRange.Text = "-";
          table.Rows[10].Cells[3].Shape.TextFrame.TextRange.Text = "-";
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Global Deflection")))
        {
          string[] strArray17 = dicData["Global Deflection"].Split('|');
          string[] strArray18 = strArray17[0].Split('/');
          table.Rows[11].Cells[2].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray18[0])) + "/" + string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray18[1])) + "mm";
          table.Rows[11].Cells[3].Shape.TextFrame.TextRange.Text = strArray17.Length != 1 ? (!(strArray17[1] == "") ? strArray17[1] : "-") : "-";
        }
        else
        {
          table.Rows[11].Cells[2].Shape.TextFrame.TextRange.Text = "-";
          table.Rows[11].Cells[3].Shape.TextFrame.TextRange.Text = "-";
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cooling Deflection")))
        {
          string[] strArray19 = dicData["Cooling Deflection"].Split('|');
          string[] strArray20 = strArray19[0].Split('/');
          table.Rows[12].Cells[2].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray20[0])) + " ~ " + string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray20[1])) + "mm";
          if (strArray19.Length == 1)
            table.Rows[11].Cells[3].Shape.TextFrame.TextRange.Text = "-";
          else
            table.Rows[12].Cells[3].Shape.TextFrame.TextRange.Text = !(strArray19[1] == "") ? strArray19[1] : "-";
        }
        else
        {
          table.Rows[12].Cells[2].Shape.TextFrame.TextRange.Text = "-";
          table.Rows[12].Cells[3].Shape.TextFrame.TextRange.Text = "-";
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Shrinkage Deflection")))
        {
          string[] strArray21 = dicData["Shrinkage Deflection"].Split('|');
          string[] strArray22 = strArray21[0].Split('/');
          table.Rows[13].Cells[2].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray22[0])) + " ~ " + string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray22[1])) + "mm";
          table.Rows[13].Cells[3].Shape.TextFrame.TextRange.Text = strArray21.Length != 1 ? (!(strArray21[1] == "") ? strArray21[1] : "-") : "-";
        }
        else
        {
          table.Rows[13].Cells[2].Shape.TextFrame.TextRange.Text = "-";
          table.Rows[13].Cells[3].Shape.TextFrame.TextRange.Text = "-";
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Orientation Deflection")))
        {
          string[] strArray23 = dicData["Orientation Deflection"].Split('|');
          string[] strArray24 = strArray23[0].Split('/');
          table.Rows[14].Cells[2].Shape.TextFrame.TextRange.Text = string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray24[0])) + " ~ " + string.Format("{0:0.00}", (object) clsHDMFLibUtil.ConvertToDouble(strArray24[1])) + "mm";
          if (strArray23.Length == 1)
            table.Rows[14].Cells[3].Shape.TextFrame.TextRange.Text = "-";
          else if (strArray23[1] == "")
            table.Rows[14].Cells[3].Shape.TextFrame.TextRange.Text = "-";
          else
            table.Rows[14].Cells[3].Shape.TextFrame.TextRange.Text = strArray23[1];
        }
        else
        {
          table.Rows[14].Cells[2].Shape.TextFrame.TextRange.Text = "-";
          table.Rows[14].Cells[3].Shape.TextFrame.TextRange.Text = "-";
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide4):" + ex.Message));
      }
    }

    public static void SetSlide5(Slide p_slData, Dictionary<string, string> dicData)
    {
      string str = string.Empty;
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 216f, 150.9022f, 611.625061f, 355.945679f);
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S5_Table")).FirstOrDefault<Shape>().Table;
        if (table == null)
          return;
        p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Title == "S1_Title")).FirstOrDefault<Shape>();
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Phase")))
          str = dicData["Phase"];
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Step")))
          str = str + " (" + dicData["Step"] + ")";
        table.Rows[1].Cells[2].Shape.TextFrame.TextRange.Text = str;
        table.Rows[2].Cells[2].Shape.TextFrame.TextRange.Text = dicData["Sequence"];
        table.Rows[3].Cells[2].Shape.TextFrame.TextRange.Text = dicData["Mesh Type"];
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Element No")))
        {
          string[] strArray = dicData["Element No"].Split('/');
          table.Rows[4].Cells[2].Shape.TextFrame.TextRange.Text = strArray[0];
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Mesh Size")))
          table.Rows[5].Cells[2].Shape.TextFrame.TextRange.Text = dicData["Mesh Size"];
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Number Of Layer Through Thickness")))
          table.Rows[6].Cells[2].Shape.TextFrame.TextRange.Text = dicData["Number Of Layer Through Thickness"];
        if (!dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Projected Area")))
          return;
        table.Rows[7].Cells[2].Shape.TextFrame.TextRange.Text = dicData["Projected Area"];
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide5):" + ex.Message));
      }
    }

    public static void SetSlide6(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 86f, 130f, 436f, 409f);
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 532.063f, 183.1181f, 435.9685f, 302.740173f);
        }
        // ISSUE: variable of a compiler-generated type
        Table table1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S6_Table1")).FirstOrDefault<Shape>().Table;
        if (table1 != null)
        {
          Dictionary<string, string> source1 = dicData;
          table1.Rows[1].Cells[2].Shape.TextFrame.TextRange.Text = !source1.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Volume By Components")) || !(dicData["Volume By Components"] != string.Empty) ? "-" : Math.Round(clsReportUtill.ConvertToDouble(dicData["Volume By Components"]), 1).ToString();
          Dictionary<string, string> source2 = dicData;
          table1.Rows[2].Cells[2].Shape.TextFrame.TextRange.Text = !source2.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "HRS To Part Volume Ratio")) || !(dicData["HRS To Part Volume Ratio"] != string.Empty) ? "-" : dicData["HRS To Part Volume Ratio"] + "%";
          Dictionary<string, string> source3 = dicData;
          table1.Rows[3].Cells[2].Shape.TextFrame.TextRange.Text = !source3.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Manifold Size")) || !(dicData["Manifold Size"] != string.Empty) ? "-" : dicData["Manifold Size"];
          if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Nozzle Size")))
          {
            string empty = string.Empty;
            string str;
            if (dicData["Nozzle Size"] != string.Empty)
            {
              string[] strArray = dicData["Nozzle Size"].Replace(",", "/").Split('/');
              str = strArray[0];
              if (strArray.Length > 1)
                str = str + ", " + strArray[1];
            }
            else
              str = "-";
            table1.Rows[4].Cells[2].Shape.TextFrame.TextRange.Text = str;
          }
          if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Valve Pin Size")))
          {
            string empty = string.Empty;
            string str = !(dicData["Valve Pin Size"] != string.Empty) ? "-" : empty + dicData["Valve Pin Size"];
            table1.Rows[5].Cells[2].Shape.TextFrame.TextRange.Text = str;
          }
          Dictionary<string, string> source4 = dicData;
          table1.Rows[6].Cells[2].Shape.TextFrame.TextRange.Text = !source4.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Nozzle Gate Size")) || !(dicData["Nozzle Gate Size"] != string.Empty) ? "-" : dicData["Nozzle Gate Size"];
          Dictionary<string, string> source5 = dicData;
          table1.Rows[7].Cells[2].Shape.TextFrame.TextRange.Text = !source5.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Manifold Volume")) || !(dicData["Manifold Volume"] != string.Empty) ? "-" : Math.Round(clsReportUtill.ConvertToDouble(dicData["Manifold Volume"]), 1).ToString();
        }
        // ISSUE: variable of a compiler-generated type
        Table table2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S6_Table2")).FirstOrDefault<Shape>().Table;
        if (table2 != null)
        {
          bool flag = false;
          if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cold Runner Volume")) && dicData["Cold Runner Volume"] != string.Empty && clsReportUtill.ConvertToDouble(dicData["Cold Runner Volume"]) > 0.5)
            flag = true;
          int num1 = !dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cold Runner Cross-Section Is")) ? 0 : (dicData["Cold Runner Cross-Section Is"] != string.Empty ? 1 : 0);
          int num2 = flag ? 1 : 0;
          table2.Rows[1].Cells[2].Shape.TextFrame.TextRange.Text = (num1 & num2) == 0 ? "-" : dicData["Cold Runner Cross-Section Is"];
          int num3 = !dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cold Runner Size")) ? 0 : (dicData["Cold Runner Size"] != string.Empty ? 1 : 0);
          int num4 = flag ? 1 : 0;
          table2.Rows[2].Cells[2].Shape.TextFrame.TextRange.Text = (num3 & num4) == 0 ? "-" : dicData["Cold Runner Size"];
          int num5 = !dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cold Runner Volume")) ? 0 : (dicData["Cold Runner Volume"] != string.Empty ? 1 : 0);
          int num6 = flag ? 1 : 0;
          table2.Rows[5].Cells[2].Shape.TextFrame.TextRange.Text = (num5 & num6) == 0 ? "-" : dicData["Cold Runner Volume"];
          int num7 = !dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cold Gate Type")) ? 0 : (dicData["Cold Gate Type"] != string.Empty ? 1 : 0);
          int num8 = flag ? 1 : 0;
          table2.Rows[3].Cells[2].Shape.TextFrame.TextRange.Text = (num7 & num8) == 0 ? "-" : dicData["Cold Gate Type"];
          int num9 = !dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cold Gate Size")) ? 0 : (dicData["Cold Gate Size"] != string.Empty ? 1 : 0);
          int num10 = flag ? 1 : 0;
          table2.Rows[4].Cells[2].Shape.TextFrame.TextRange.Text = (num9 & num10) == 0 ? "-" : dicData["Cold Gate Size"];
          Dictionary<string, string> source6 = dicData;
          table2.Rows[6].Cells[2].Shape.TextFrame.TextRange.Text = !source6.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cooling Line Diameter")) || !(dicData["Cooling Line Diameter"] != string.Empty) ? "-" : dicData["Cooling Line Diameter"];
          Dictionary<string, string> source7 = dicData;
          table2.Rows[7].Cells[2].Shape.TextFrame.TextRange.Text = !source7.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Baffle Diameter")) || !(dicData["Baffle Diameter"] != string.Empty) ? "-" : dicData["Baffle Diameter"];
        }
        // ISSUE: variable of a compiler-generated type
        Table table3 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S6_Table3")).FirstOrDefault<Shape>().Table;
        if (table3 == null)
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S6_Table3")).FirstOrDefault<Shape>().ZOrder(MsoZOrderCmd.msoBringToFront);
        Dictionary<string, string> source = dicData;
        table3.Rows[1].Cells[2].Shape.TextFrame.TextRange.Text = !source.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Heat Diameter")) || !(dicData["Heat Diameter"] != string.Empty) ? "-" : dicData["Heat Diameter"];
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Heat Pipe Effectiveness")) && dicData["Heat Pipe Effectiveness"] != string.Empty)
          table3.Rows[2].Cells[2].Shape.TextFrame.TextRange.Text = dicData["Heat Pipe Effectiveness"];
        else
          table3.Rows[2].Cells[2].Shape.TextFrame.TextRange.Text = "-";
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide6):" + ex.Message));
      }
    }

    public static void SetSlide7(Slide p_slData, Dictionary<string, string> dicData)
    {
      StringBuilder stringBuilder = new StringBuilder();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 64.1652f, 149.9839f, 288.3813f, 242.4002f);
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 364.5464f, 149.9839f, 288.3813f, 242.4002f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S7_TextBox")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          shape1.TextFrame.TextRange.ParagraphFormat.SpaceBefore = 9.5f;
          // ISSUE: variable of a compiler-generated type
          TextRange textRange1 = shape1.TextFrame.TextRange;
          textRange1.Font.Name = "Arial";
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange2 = textRange1.InsertAfter("Manufacturer : " + dicData["Manufacturer"]);
          textRange2.Font.Bold = MsoTriState.msoTrue;
          textRange2.Font.Size = 16f;
          string[] strArray1 = dicData["Trade Name"].Split('/');
          string[] strArray2 = strArray1[0].Split('-');
          stringBuilder.Clear();
          stringBuilder.Append(Environment.NewLine + "Trade name : " + strArray2[0]);
          if (strArray2.Length > 1)
            stringBuilder.Append(Environment.NewLine + strArray2[1]);
          if (strArray1.Length > 1)
            stringBuilder.Append(" (" + strArray1[1] + ")");
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange3 = textRange2.InsertAfter(stringBuilder.ToString());
          textRange3.Font.Bold = MsoTriState.msoTrue;
          textRange3.Font.Size = 16f;
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange4 = textRange3.InsertAfter(Environment.NewLine + "Family Abbreviation : " + dicData["Family Abbreviation"]);
          textRange4.Font.Bold = MsoTriState.msoTrue;
          textRange4.Font.Size = 16f;
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange5 = textRange4.InsertAfter(Environment.NewLine + "Fibers/Fillers : " + dicData["Fibers Fillers"]);
          textRange5.Font.Bold = MsoTriState.msoTrue;
          textRange5.Font.Size = 16f;
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange6 = textRange5.InsertAfter(Environment.NewLine + "Material indicator : " + dicData["Material Indicator"]);
          textRange6.Font.Bold = MsoTriState.msoTrue;
          textRange6.Font.Size = 16f;
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange7 = textRange6.InsertAfter(Environment.NewLine + "Date tested : " + dicData["Date Tested"]);
          textRange7.Font.Bold = MsoTriState.msoTrue;
          textRange7.Font.Size = 16f;
          string[] strArray3 = dicData["Melt Temp Range"].Split('/');
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange8 = textRange7.InsertAfter(Environment.NewLine + "   Melt Temp. range: " + strArray3[0] + " ~ " + strArray3[1] + " ℃");
          textRange8.Font.Bold = MsoTriState.msoFalse;
          textRange8.Font.Size = 16f;
          string[] strArray4 = dicData["Mold Temp Range"].Split('/');
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange9 = textRange8.InsertAfter(Environment.NewLine + "   Mold Temp. range: " + strArray4[0] + " ~ " + strArray4[1] + " ℃");
          textRange9.Font.Bold = MsoTriState.msoFalse;
          textRange9.Font.Size = 16f;
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange10 = textRange9.InsertAfter(Environment.NewLine + "   Ejection Temperature: " + dicData["Ejection Temp"] + " ℃");
          textRange10.Font.Bold = MsoTriState.msoFalse;
          textRange10.Font.Size = 16f;
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange11 = textRange10.InsertAfter(Environment.NewLine + "   Transition Temperature: " + dicData["Transition Temp"] + " ℃");
          textRange11.Font.Bold = MsoTriState.msoFalse;
          textRange11.Font.Size = 16f;
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.AddTextbox(MsoTextOrientation.msoTextOrientationHorizontal, 98.10165f, 544.5883f, 370.9444f, 90.39425f);
        if (shape2 != null)
        {
          shape2.TextFrame.MarginLeft = 7.2f;
          shape2.TextFrame.MarginRight = 7.2f;
          shape2.TextFrame.MarginTop = 3.6f;
          shape2.TextFrame.MarginBottom = 3.6f;
          shape2.TextFrame.TextRange.ParagraphFormat.SpaceBefore = 9.5f;
          // ISSUE: variable of a compiler-generated type
          TextRange textRange12 = shape2.TextFrame.TextRange;
          textRange12.Font.Name = "Arial";
          if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cooling Time")))
          {
            // ISSUE: reference to a compiler-generated method
            textRange12 = textRange12.InsertAfter("Cooling Time: " + dicData["Cooling Time"] + " sec");
            textRange12.Font.Bold = MsoTriState.msoFalse;
            textRange12.Font.Size = 14f;
          }
          if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Coolant Inlet Temperature")))
          {
            // ISSUE: reference to a compiler-generated method
            textRange12 = textRange12.InsertAfter(Environment.NewLine + "Coolant inlet Temp.: " + dicData["Coolant Inlet Temperature"] + " ℃");
            textRange12.Font.Bold = MsoTriState.msoFalse;
            textRange12.Font.Size = 14f;
          }
          if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Coolant Control")))
          {
            string[] strArray = dicData["Coolant Control"].Split('/');
            if (strArray.Length > 1)
            {
              // ISSUE: reference to a compiler-generated method
              // ISSUE: variable of a compiler-generated type
              TextRange textRange13 = textRange12.InsertAfter(Environment.NewLine + "Coolant Control " + strArray[0] + " : " + strArray[1]);
              textRange13.Font.Bold = MsoTriState.msoFalse;
              textRange13.Font.Size = 14f;
            }
          }
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Shape shape3 = p_slData.Shapes.AddTextbox(MsoTextOrientation.msoTextOrientationHorizontal, 437.102f, 439.4112f, 370.9444f, 195.5714f);
        if (shape3 == null)
          return;
        shape3.TextFrame.MarginLeft = 7.2f;
        shape3.TextFrame.MarginRight = 7.2f;
        shape3.TextFrame.MarginTop = 3.6f;
        shape3.TextFrame.MarginBottom = 3.6f;
        shape3.TextFrame.TextRange.ParagraphFormat.SpaceBefore = 9.5f;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange14 = shape3.TextFrame.TextRange;
        textRange14.Font.Name = "Arial";
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Melt Temp")))
        {
          // ISSUE: reference to a compiler-generated method
          textRange14 = textRange14.InsertAfter("Melt Temp.: " + dicData["Melt Temp"] + " ℃");
          textRange14.Font.Bold = MsoTriState.msoFalse;
          textRange14.Font.Size = 14f;
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Mold Temp")))
        {
          // ISSUE: reference to a compiler-generated method
          textRange14 = textRange14.InsertAfter(Environment.NewLine + "Mold Temp.: " + dicData["Mold Temp"] + " ℃");
          textRange14.Font.Bold = MsoTriState.msoFalse;
          textRange14.Font.Size = 14f;
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Fill Time")))
        {
          string[] strArray = dicData["Fill Time"].Split('|');
          if (strArray.Length > 1)
          {
            string str1;
            string str2;
            if (strArray[0] == "0")
            {
              str1 = "Injection Time";
              str2 = "sec";
            }
            else
            {
              str1 = "Flow Rate";
              str2 = "cm^3/s";
            }
            string str3 = strArray[1];
            string str4 = "";
            if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Fill Time Type")))
              str4 = dicData["Fill Time Type"];
            if (str4 != "" && (str4 == "Injection Time" || str4 == "Flow Rate"))
              str4 = "";
            string NewText;
            if (str4 == "")
              NewText = Environment.NewLine + str1 + " : " + str3 + " " + str2 + " ";
            else
              NewText = Environment.NewLine + str1 + " : " + str3 + " " + str2 + "  (" + str4 + ")";
            // ISSUE: reference to a compiler-generated method
            textRange14 = textRange14.InsertAfter(NewText);
            textRange14.Font.Bold = MsoTriState.msoFalse;
            textRange14.Font.Size = 14f;
          }
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "V/P Switchover")))
        {
          // ISSUE: reference to a compiler-generated method
          textRange14 = textRange14.InsertAfter(Environment.NewLine + "V/P Switch Over: " + dicData["V/P Switchover"] + " %");
          textRange14.Font.Bold = MsoTriState.msoFalse;
          textRange14.Font.Size = 14f;
        }
        stringBuilder.Clear();
        if (!dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Packing Profile")))
          return;
        string[] strArray5 = dicData["Packing Profile"].Split('/');
        stringBuilder.Append(Environment.NewLine + "Packing Profile: " + strArray5[0]);
        for (int index = 1; index < strArray5.Length; ++index)
        {
          string[] strArray6 = strArray5[index].Split('|');
          stringBuilder.Append(Environment.NewLine + "        " + strArray6[0] + "                   " + strArray6[1]);
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange15 = textRange14.InsertAfter(stringBuilder.ToString());
        textRange15.Font.Bold = MsoTriState.msoFalse;
        textRange15.Font.Size = 14f;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide7):" + ex.Message));
      }
    }

    public static void SetSlide8(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 134.9291f, 200.126f, 474.519684f, 344.976379f);
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S8_Content")).FirstOrDefault<Shape>();
        if (shape == null)
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = textRange1.InsertAfter("Determine of Coolant inlet temp, ");
        textRange2.Font.Bold = MsoTriState.msoFalse;
        if (!dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cool Inlet Temperature")))
          return;
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange3 = textRange2.InsertAfter(dicData["Cool Inlet Temperature"] + "℃");
        textRange3.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide8):" + ex.Message));
      }
    }

    public static void SetSlide9(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(dicData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 147.685f, 178.583f, 341.5748f, 248.315f);
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(dicData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 558.7087f, 178.583f, 341.5748f, 248.315f);
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(dicData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 147.685f, 454.110229f, 341.5748f, 248.315f);
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(dicData["Image4"], MsoTriState.msoFalse, MsoTriState.msoTrue, 558.7087f, 454.110229f, 341.5748f, 248.315f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide9):" + ex.Message));
      }
    }

    public static void SetSlide10(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Animation")))
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Animation"], MsoTriState.msoFalse, MsoTriState.msoTrue, 36f, 198.425f, 604.063f, 439.0866f).ZOrder(MsoZOrderCmd.msoSendToBack);
        }
        if (!dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 651.969f, 127.559f, 350.929138f, 255.118f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide10):" + ex.Message));
      }
    }

    public static void SetSlide11(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        if (!dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 36f, 198.425f, 604.063f, 439.0866f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide10):" + ex.Message));
      }
    }

    public static void SetSlide12(Slide p_slData, Dictionary<string, string> dicData)
    {
      double num = 0.0;
      string empty = string.Empty;
      StringBuilder stringBuilder = new StringBuilder();
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 36f, 198.425f, 604.063f, 439.0866f);
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Part Volume")))
          num = clsReportUtill.ConvertToDouble(dicData["Part Volume"]);
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Gates")))
          empty = dicData["Gates"];
        string[] strArray1 = empty.Split('|');
        for (int index = 0; index < strArray1.Length; ++index)
        {
          string[] strArray2 = strArray1[index].Split('/');
          stringBuilder.Append("Gate " + (object) (index + 1) + " : " + strArray2[0] + " / " + strArray2[1] + "% / ");
          stringBuilder.Append(Math.Round(clsReportUtill.ConvertToDouble(strArray2[1]) * num / 100.0, 2).ToString() + "㎤" + Environment.NewLine);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "TextBox 3")).FirstOrDefault<Shape>();
        if (shape == null)
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange = shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange.InsertAfter(stringBuilder.ToString());
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide12):" + ex.Message));
      }
    }

    public static void SetSlide13(Slide p_slData, Dictionary<string, string> dicData)
    {
      string empty = string.Empty;
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 34.0157f, 198.425f, 737.008f, 504.8504f);
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 700.1575f, 170.079f, 289.1339f, 232.441f);
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.AddTextbox(MsoTextOrientation.msoTextOrientationHorizontal, 102.0902f, 148.6641f, 334.7638f, 55.08252f);
        if (shape == null)
          return;
        shape.TextFrame.MarginLeft = 0.0f;
        shape.TextFrame.MarginRight = 0.0f;
        shape.TextFrame.MarginTop = 0.0f;
        shape.TextFrame.MarginBottom = 0.0f;
        shape.TextFrame.TextRange.ParagraphFormat.SpaceWithin = 1.4f;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape.TextFrame.TextRange;
        textRange1.Font.Name = "Arial";
        double num1 = clsReportUtill.ConvertToDouble(dicData["Injection Pressure"]);
        double num2 = clsReportUtill.ConvertToDouble(clsReportUtill.ReadINI("Value", "InjPressure2", clsReportDefine.g_diCfg.FullName + "\\SLReport\\ResultConfig.ini"));
        double num3 = clsReportUtill.ConvertToDouble(clsReportUtill.ReadINI("Value", "InjPressure1", clsReportDefine.g_diCfg.FullName + "\\SLReport\\ResultConfig.ini"));
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = textRange1.InsertAfter("Injection pressure : ");
        textRange2.Font.Bold = MsoTriState.msoTrue;
        textRange2.Font.Underline = MsoTriState.msoFalse;
        textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange3 = textRange2.InsertAfter(num1.ToString() + " MPa ");
        textRange3.Font.Bold = MsoTriState.msoTrue;
        textRange3.Font.Underline = MsoTriState.msoFalse;
        textRange3.Font.Color.RGB = num1 >= num2 ? (num1 < num2 || num1 >= num3 ? ColorTranslator.ToOle(Color.Red) : ColorTranslator.ToOle(Color.Orange)) : ColorTranslator.ToOle(Color.Black);
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange4 = textRange3.InsertAfter("< " + num3.ToString() + " MPa");
        textRange4.Font.Bold = MsoTriState.msoTrue;
        textRange4.Font.Underline = MsoTriState.msoTrue;
        textRange4.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        double num4 = clsReportUtill.ConvertToDouble(dicData["InCavity Pressure"]);
        double num5 = clsReportUtill.ConvertToDouble(clsReportUtill.ReadINI("Value", "InCavityPressure2", clsReportDefine.g_diCfg.FullName + "\\SLReport\\ResultConfig.ini"));
        double num6 = clsReportUtill.ConvertToDouble(clsReportUtill.ReadINI("Value", "InCavityPressure1", clsReportDefine.g_diCfg.FullName + "\\SLReport\\ResultConfig.ini"));
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange5 = textRange4.InsertAfter(Environment.NewLine + "In-Cavity pressure : ");
        textRange5.Font.Bold = MsoTriState.msoTrue;
        textRange5.Font.Underline = MsoTriState.msoFalse;
        textRange5.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange6 = textRange5.InsertAfter(num4.ToString() + " MPa ");
        textRange6.Font.Bold = MsoTriState.msoTrue;
        textRange6.Font.Underline = MsoTriState.msoFalse;
        textRange6.Font.Color.RGB = num4 >= num5 ? (num4 < num5 || num4 >= num6 ? ColorTranslator.ToOle(Color.Red) : ColorTranslator.ToOle(Color.Orange)) : ColorTranslator.ToOle(Color.Black);
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange7 = textRange6.InsertAfter("< " + num6.ToString() + " MPa");
        textRange7.Font.Bold = MsoTriState.msoTrue;
        textRange7.Font.Underline = MsoTriState.msoTrue;
        textRange7.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide13):" + ex.Message));
      }
    }

    public static void SetSlide14(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 129.5433f, 195.591f, 765.354f, 527.2441f);
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.AddTextbox(MsoTextOrientation.msoTextOrientationHorizontal, 102.0902f, 148.6641f, 334.7638f, 55.08252f);
        if (shape == null)
          return;
        shape.TextFrame.MarginLeft = 0.0f;
        shape.TextFrame.MarginRight = 0.0f;
        shape.TextFrame.MarginTop = 0.0f;
        shape.TextFrame.MarginBottom = 0.0f;
        shape.TextFrame.TextRange.ParagraphFormat.SpaceWithin = 1.4f;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape.TextFrame.TextRange;
        textRange1.Font.Name = "Arial";
        string str1 = "";
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Injection Pressure")))
          str1 = dicData["Injection Pressure"];
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = textRange1.InsertAfter("Injection pressure : " + str1 + " MPa");
        textRange2.Font.Bold = MsoTriState.msoTrue;
        string str2 = "";
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "InCavity Pressure")))
          str2 = dicData["InCavity Pressure"];
        // ISSUE: reference to a compiler-generated method
        textRange2.InsertAfter(Environment.NewLine + "In-Cavity pressure : " + str2 + " MPa").Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide14):" + ex.Message));
      }
    }

    public static void SetSlide15(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 129.5433f, 195.591f, 765.354f, 527.2441f);
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.AddTextbox(MsoTextOrientation.msoTextOrientationHorizontal, 102.0902f, 148.6641f, 334.7638f, 55.08252f);
        if (shape == null)
          return;
        shape.TextFrame.MarginLeft = 0.0f;
        shape.TextFrame.MarginRight = 0.0f;
        shape.TextFrame.MarginTop = 0.0f;
        shape.TextFrame.MarginBottom = 0.0f;
        shape.TextFrame.TextRange.ParagraphFormat.SpaceWithin = 1.4f;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape.TextFrame.TextRange;
        textRange1.Font.Name = "Arial";
        string[] strArray1 = dicData["Injection Pressure"].Split('/');
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = textRange1.InsertAfter("Injection pressure : " + strArray1[0] + " MPa");
        textRange2.Font.Bold = MsoTriState.msoTrue;
        if (strArray1.Length <= 1)
          return;
        int num = 1;
        for (int index = 1; index < strArray1.Length; ++index)
        {
          string[] strArray2 = strArray1[index].Split('|');
          if (strArray2.Length <= 1 || !(strArray2[1] == "0"))
          {
            string NewText;
            switch (index)
            {
              case 1:
                NewText = "st";
                break;
              case 2:
                NewText = "nd";
                break;
              case 3:
                NewText = "rd";
                break;
              default:
                NewText = "th";
                break;
            }
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange3 = textRange2.InsertAfter(Environment.NewLine + (object) num);
            textRange3.Font.Superscript = MsoTriState.msoFalse;
            textRange3.Font.Bold = MsoTriState.msoTrue;
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange4 = textRange3.InsertAfter(NewText);
            textRange4.Font.Superscript = MsoTriState.msoTrue;
            textRange4.Font.Bold = MsoTriState.msoTrue;
            // ISSUE: reference to a compiler-generated method
            textRange2 = textRange4.InsertAfter(" Holding pressure : " + strArray2[0] + " MPa, " + strArray2[1] + " sec");
            textRange2.Font.Superscript = MsoTriState.msoFalse;
            textRange2.Font.Bold = MsoTriState.msoTrue;
            ++num;
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide15):" + ex.Message));
      }
    }

    public static void SetSlide16(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 129.5433f, 195.591f, 765.354f, 527.2441f);
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.AddTextbox(MsoTextOrientation.msoTextOrientationHorizontal, 102f, 149f, 335f, 55f);
        if (shape == null)
          return;
        shape.TextFrame.MarginLeft = 0.0f;
        shape.TextFrame.MarginRight = 0.0f;
        shape.TextFrame.MarginTop = 0.0f;
        shape.TextFrame.MarginBottom = 0.0f;
        shape.TextFrame.TextRange.ParagraphFormat.SpaceWithin = 1.4f;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape.TextFrame.TextRange;
        textRange1.Font.Name = "Arial";
        // ISSUE: reference to a compiler-generated method
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = !dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Pressure At All Gate At Transfer")) ? textRange1.InsertAfter("During pressure at the gate : -") : textRange1.InsertAfter("During pressure at the gate : " + dicData["Pressure At All Gate At Transfer"] + " MPa");
        textRange2.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide16):" + ex.Message));
      }
    }

    public static void SetSlide17(Slide p_slData, Dictionary<string, string> dicData)
    {
      string str = "";
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 129.5433f, 195.591f, 765.354f, 527.2441f);
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.AddTextbox(MsoTextOrientation.msoTextOrientationHorizontal, 102.0902f, 149f, 335f, 55f);
        if (shape == null)
          return;
        shape.TextFrame.MarginLeft = 0.0f;
        shape.TextFrame.MarginRight = 0.0f;
        shape.TextFrame.MarginTop = 0.0f;
        shape.TextFrame.MarginBottom = 0.0f;
        shape.TextFrame.TextRange.ParagraphFormat.SpaceWithin = 1.4f;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape.TextFrame.TextRange;
        textRange1.Font.Name = "Arial";
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "During Filling")))
          str = dicData["During Filling"];
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = textRange1.InsertAfter("End of fill : " + str + " ton");
        textRange2.Font.Bold = MsoTriState.msoTrue;
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "During Packing")))
          str = dicData["During Packing"];
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange3 = textRange2.InsertAfter(Environment.NewLine + "At the packing : " + str + " ton * 1.2");
        textRange3.Font.Bold = MsoTriState.msoTrue;
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "More Than")))
          str = dicData["More Than"];
        // ISSUE: reference to a compiler-generated method
        textRange3.InsertAfter(Environment.NewLine + "More than " + str + " ton machine").Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide17):" + ex.Message));
      }
    }

    public static void SetSlide18(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 129.5433f, 195.591f, 765.354f, 527.2441f);
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.AddTextbox(MsoTextOrientation.msoTextOrientationHorizontal, 102f, 148f, 335f, 55f);
        if (shape == null)
          return;
        shape.TextFrame.MarginLeft = 0.0f;
        shape.TextFrame.MarginRight = 0.0f;
        shape.TextFrame.MarginTop = 0.0f;
        shape.TextFrame.MarginBottom = 0.0f;
        shape.TextFrame.TextRange.ParagraphFormat.SpaceWithin = 1.4f;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange = shape.TextFrame.TextRange;
        textRange.Font.Name = "Arial";
        // ISSUE: reference to a compiler-generated method
        textRange.InsertAfter("Fill + Pack time or Gate frozen time : " + dicData["Frozen Layer Fraction At Gate"] + " sec").Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide18):" + ex.Message));
      }
    }

    public static void SetSlide19(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 129.5433f, 195.591f, 765.354f, 527.2441f);
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.AddTextbox(MsoTextOrientation.msoTextOrientationHorizontal, 102f, 149f, 335f, 55f);
        if (shape == null)
          return;
        shape.TextFrame.MarginLeft = 0.0f;
        shape.TextFrame.MarginRight = 0.0f;
        shape.TextFrame.MarginTop = 0.0f;
        shape.TextFrame.MarginBottom = 0.0f;
        shape.TextFrame.TextRange.ParagraphFormat.SpaceWithin = 1.4f;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape.TextFrame.TextRange;
        textRange1.Font.Name = "Arial";
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Temperature At Flow Front")))
        {
          string[] strArray = dicData["Temperature At Flow Front"].Split('|');
          // ISSUE: reference to a compiler-generated method
          textRange1 = textRange1.InsertAfter(strArray[0] + " ~ " + strArray[1] + "(" + strArray[2] + " ℃)  ");
          textRange1.Font.Bold = MsoTriState.msoTrue;
          textRange1.Font.Underline = MsoTriState.msoFalse;
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = textRange1.InsertAfter("> Transition Temperature (" + dicData["Transition Temperature"] + " ℃)");
        textRange2.Font.Bold = MsoTriState.msoTrue;
        textRange2.Font.Underline = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide19):" + ex.Message));
      }
    }

    public static void SetSlide20(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 34.0157f, 198.425f, 737.008f, 504.8504f);
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 700.1575f, 170.079f, 289.1339f, 232.441f);
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.AddTextbox(MsoTextOrientation.msoTextOrientationHorizontal, 102f, 149f, 335f, 55f);
        if (shape == null)
          return;
        shape.TextFrame.MarginLeft = 0.0f;
        shape.TextFrame.MarginRight = 0.0f;
        shape.TextFrame.MarginTop = 0.0f;
        shape.TextFrame.MarginBottom = 0.0f;
        shape.TextFrame.TextRange.ParagraphFormat.SpaceWithin = 1.4f;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape.TextFrame.TextRange;
        textRange1.Font.Name = "Arial";
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = textRange1.InsertAfter(dicData["Shear Rate(P)"] + " 1/s ");
        textRange2.Font.Bold = MsoTriState.msoTrue;
        textRange2.Font.Underline = MsoTriState.msoFalse;
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange3 = textRange2.InsertAfter("< Material limit (" + dicData["Shear Rate(M)"] + " 1/s)");
        textRange3.Font.Bold = MsoTriState.msoTrue;
        textRange3.Font.Underline = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide20):" + ex.Message));
      }
    }

    public static void SetSlide21(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 34.0157f, 198.425f, 737.008f, 504.8504f);
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 700.1575f, 170.079f, 289.1339f, 232.441f);
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.AddTextbox(MsoTextOrientation.msoTextOrientationHorizontal, 102f, 149f, 335f, 55f);
        if (shape == null)
          return;
        shape.TextFrame.MarginLeft = 0.0f;
        shape.TextFrame.MarginRight = 0.0f;
        shape.TextFrame.MarginTop = 0.0f;
        shape.TextFrame.MarginBottom = 0.0f;
        shape.TextFrame.TextRange.ParagraphFormat.SpaceWithin = 1.4f;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape.TextFrame.TextRange;
        textRange1.Font.Name = "Arial";
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = textRange1.InsertAfter(dicData["Shear Stress(P)"] + " MPa ");
        textRange2.Font.Bold = MsoTriState.msoTrue;
        textRange2.Font.Underline = MsoTriState.msoFalse;
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange3 = textRange2.InsertAfter("< Material limit (" + dicData["Shear Stress(M)"] + " MPa)");
        textRange3.Font.Bold = MsoTriState.msoTrue;
        textRange3.Font.Underline = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide21):" + ex.Message));
      }
    }

    public static void SetSlide22(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        if (!dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 129.5433f, 195.591f, 765.354f, 527.2441f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide22):" + ex.Message));
      }
    }

    public static void SetSlide23(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        if (!dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 129.5433f, 195.591f, 765.354f, 527.2441f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide23):" + ex.Message));
      }
    }

    public static void SetSlide24(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 34.0157f, 198.425f, 737.008f, 504.8504f);
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 700.1575f, 170.079f, 289.1339f, 232.441f);
        }
        if (!dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(dicData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 700.1575f, 430.8661f, 289.1339f, 232.441f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide24):" + ex.Message));
      }
    }

    public static void SetSlide25(Slide p_slData, Dictionary<string, string> dicData)
    {
      // ISSUE: variable of a compiler-generated type
      TextRange textRange1 = (TextRange) null;
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 34.0157f, 198.425f, 737.008f, 504.8504f).ZOrder(MsoZOrderCmd.msoSendToBack);
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 700.1575f, 170.079f, 289.1339f, 232.441f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S25_Rectangle")).FirstOrDefault<Shape>();
        if (shape == null)
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange2.Delete();
        textRange2.Font.Name = "Arial";
        if (!dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Time To Reach Ejection Temperature")))
          return;
        // ISSUE: reference to a compiler-generated method
        textRange1 = textRange2.InsertAfter(string.Format("Most parts may be ejected with parts 80% frozen. \vAlmost areas reach the ejection temperature within {0} seconds.", (object) dicData["Time To Reach Ejection Temperature"]));
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide25):" + ex.Message));
      }
    }

    public static void SetSlide26(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        if (!dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 129.5433f, 195.591f, 765.354f, 527.2441f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide26):" + ex.Message));
      }
    }

    public static void SetSlide27(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        if (!dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 129.5433f, 195.591f, 765.354f, 527.2441f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide27):" + ex.Message));
      }
    }

    public static void SetSlide28(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 129.5433f, 195.591f, 765.354f, 527.2441f);
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.AddTextbox(MsoTextOrientation.msoTextOrientationHorizontal, 102f, 149f, 479f, 32f);
        if (shape == null)
          return;
        shape.TextFrame.MarginLeft = 0.0f;
        shape.TextFrame.MarginRight = 0.0f;
        shape.TextFrame.MarginTop = 0.0f;
        shape.TextFrame.MarginBottom = 0.0f;
        shape.TextFrame.TextRange.ParagraphFormat.SpaceWithin = 1.4f;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape.TextFrame.TextRange;
        textRange1.Font.Name = "Arial";
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Reynold Number")))
        {
          // ISSUE: reference to a compiler-generated method
          textRange1 = textRange1.InsertAfter(dicData["Circuit Reynold Number"] + " bar ");
          textRange1.Font.Bold = MsoTriState.msoTrue;
          textRange1.Font.Underline = MsoTriState.msoFalse;
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = textRange1.InsertAfter("< Inlet max-pressure (15 bar)");
        textRange2.Font.Bold = MsoTriState.msoTrue;
        textRange2.Font.Underline = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide28):" + ex.Message));
      }
    }

    public static void SetSlide29(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 129.5433f, 195.591f, 765.354f, 527.2441f);
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.AddTextbox(MsoTextOrientation.msoTextOrientationHorizontal, 102f, 149f, 479f, 32f);
        if (shape == null)
          return;
        shape.TextFrame.MarginLeft = 0.0f;
        shape.TextFrame.MarginRight = 0.0f;
        shape.TextFrame.MarginTop = 0.0f;
        shape.TextFrame.MarginBottom = 0.0f;
        shape.TextFrame.TextRange.ParagraphFormat.SpaceWithin = 1.4f;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape.TextFrame.TextRange;
        textRange1.Font.Name = "Arial";
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Flow Rate")))
        {
          // ISSUE: reference to a compiler-generated method
          textRange1 = textRange1.InsertAfter(dicData["Circuit Flow Rate"] + " L/min ");
          textRange1.Font.Bold = MsoTriState.msoTrue;
          textRange1.Font.Underline = MsoTriState.msoFalse;
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = textRange1.InsertAfter("> 5 L/min (SL spec)");
        textRange2.Font.Bold = MsoTriState.msoTrue;
        textRange2.Font.Underline = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide29):" + ex.Message));
      }
    }

    public static void SetSlide30(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 77.10236f, 303.023621f, 430.015747f, 276.0945f);
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 517.039368f, 303.023621f, 430.015747f, 276.0945f);
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.AddTextbox(MsoTextOrientation.msoTextOrientationHorizontal, 102f, 149f, 479f, 32f);
        if (shape == null)
          return;
        shape.TextFrame.MarginLeft = 0.0f;
        shape.TextFrame.MarginRight = 0.0f;
        shape.TextFrame.MarginTop = 0.0f;
        shape.TextFrame.MarginBottom = 0.0f;
        shape.TextFrame.TextRange.ParagraphFormat.SpaceWithin = 1.4f;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape.TextFrame.TextRange;
        textRange1.Font.Name = "Arial";
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Coolant Temperature")))
        {
          // ISSUE: reference to a compiler-generated method
          textRange1 = textRange1.InsertAfter(dicData["Circuit Coolant Temperature"] + " ℃ ");
          textRange1.Font.Bold = MsoTriState.msoTrue;
          textRange1.Font.Underline = MsoTriState.msoFalse;
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = textRange1.InsertAfter("< Allowable limit (3 ℃)");
        textRange2.Font.Bold = MsoTriState.msoTrue;
        textRange2.Font.Underline = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide30):" + ex.Message));
      }
    }

    public static void SetSlide32(Slide p_slData, Dictionary<string, string> dicData)
    {
      string str = "";
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 129.5433f, 195.591f, 765.354f, 527.2441f);
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Shrinkage")))
          str = dicData["Shrinkage"];
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.AddPlaceholder(PpPlaceholderType.ppPlaceholderObject, 51f, 99f, 922f, 23f);
        if (shape1 != null)
        {
          shape1.TextFrame.TextRange.Font.Name = "Arial";
          shape1.TextFrame.TextRange.Font.Size = 14f;
          // ISSUE: variable of a compiler-generated type
          TextRange textRange1 = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange2 = textRange1.InsertAfter("Use Shrinkage compensation, Estimated shrinkage % : " + str);
          textRange2.Font.Bold = MsoTriState.msoTrue;
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.AddTextbox(MsoTextOrientation.msoTextOrientationHorizontal, 102.0901f, 148.664f, 479.0991f, 32.29929f);
        if (shape2 == null)
          return;
        shape2.TextFrame.MarginLeft = 0.0f;
        shape2.TextFrame.MarginRight = 0.0f;
        shape2.TextFrame.MarginTop = 0.0f;
        shape2.TextFrame.MarginBottom = 0.0f;
        shape2.TextFrame.TextRange.ParagraphFormat.SpaceWithin = 1.4f;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange3 = shape2.TextFrame.TextRange;
        textRange3.Font.Name = "Arial";
        if (!dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Deflection All")))
          return;
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange4 = textRange3.InsertAfter("Deflection All : " + dicData["Deflection All"] + " mm (Scale factor 1)");
        textRange4.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide32):" + ex.Message));
      }
    }

    public static void SetSlide33(Slide p_slData, Dictionary<string, string> dicData)
    {
      string str = "";
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          Shape shape = p_slData.Shapes.AddPicture(dicData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 132f, 197f, 374f, 254f);
          // ISSUE: reference to a compiler-generated method
          shape.ZOrder(MsoZOrderCmd.msoSendToBack);
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          Shape shape = p_slData.Shapes.AddPicture(dicData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 554f, 197f, 374f, 254f);
          // ISSUE: reference to a compiler-generated method
          shape.ZOrder(MsoZOrderCmd.msoSendToBack);
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          Shape shape = p_slData.Shapes.AddPicture(dicData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 132f, 472f, 374f, 254f);
          // ISSUE: reference to a compiler-generated method
          shape.ZOrder(MsoZOrderCmd.msoSendToBack);
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image4")))
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          Shape shape = p_slData.Shapes.AddPicture(dicData["Image4"], MsoTriState.msoFalse, MsoTriState.msoTrue, 554f, 472f, 374f, 254f);
          // ISSUE: reference to a compiler-generated method
          shape.ZOrder(MsoZOrderCmd.msoSendToBack);
        }
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Shrinkage")))
          str = dicData["Shrinkage"];
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.AddPlaceholder(PpPlaceholderType.ppPlaceholderObject, 51f, 99f, 922f, 23f);
        if (shape1 != null)
        {
          shape1.TextFrame.TextRange.Font.Name = "Arial";
          shape1.TextFrame.TextRange.Font.Size = 14f;
          // ISSUE: reference to a compiler-generated method
          shape1.TextFrame.TextRange.InsertAfter("Use Shrinkage compensation, Estimated shrinkage % : " + str).Font.Bold = MsoTriState.msoTrue;
        }
        if (!dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Content2")))
          return;
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.AddTextbox(MsoTextOrientation.msoTextOrientationHorizontal, 102f, 149f, 479f, 32f);
        if (shape2 == null)
          return;
        shape2.TextFrame.MarginLeft = 0.0f;
        shape2.TextFrame.MarginRight = 0.0f;
        shape2.TextFrame.MarginTop = 0.0f;
        shape2.TextFrame.MarginBottom = 0.0f;
        shape2.TextFrame.TextRange.ParagraphFormat.SpaceWithin = 1.4f;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange = shape2.TextFrame.TextRange;
        textRange.Font.Name = "Arial";
        // ISSUE: reference to a compiler-generated method
        textRange.InsertAfter("Deflection All : " + dicData["Content2"] + " mm (Scale factor 1)").Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide33):" + ex.Message));
      }
    }

    public static void SetSlide34(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 129.5433f, 195.591f, 765.354f, 527.2441f);
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.AddTextbox(MsoTextOrientation.msoTextOrientationHorizontal, 102f, 149f, 297f, 32f);
        if (shape == null)
          return;
        shape.TextFrame.MarginLeft = 0.0f;
        shape.TextFrame.MarginRight = 0.0f;
        shape.TextFrame.MarginTop = 0.0f;
        shape.TextFrame.MarginBottom = 0.0f;
        shape.TextFrame.TextRange.ParagraphFormat.SpaceWithin = 1.4f;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape.TextFrame.TextRange;
        textRange1.Font.Name = "Arial";
        if (!dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Deflection X")))
          return;
        string[] strArray = dicData["Deflection X"].Split('/');
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = textRange1.InsertAfter("X : " + strArray[1] + " mm, -X : " + (object) Math.Abs(Convert.ToDouble(strArray[0])) + " mm");
        textRange2.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide34):" + ex.Message));
      }
    }

    public static void SetSlide35(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 129.5433f, 195.591f, 765.354f, 527.2441f);
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.AddTextbox(MsoTextOrientation.msoTextOrientationHorizontal, 102f, 149f, 297f, 32f);
        if (shape == null)
          return;
        shape.TextFrame.MarginLeft = 0.0f;
        shape.TextFrame.MarginRight = 0.0f;
        shape.TextFrame.MarginTop = 0.0f;
        shape.TextFrame.MarginBottom = 0.0f;
        shape.TextFrame.TextRange.ParagraphFormat.SpaceWithin = 1.4f;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape.TextFrame.TextRange;
        textRange1.Font.Name = "Arial";
        if (!dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Deflection X")))
          return;
        string[] strArray = dicData["Deflection X"].Split('/');
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = textRange1.InsertAfter("X : " + strArray[1] + " mm, -X : " + (object) Math.Abs(Convert.ToDouble(strArray[0])) + " mm");
        textRange2.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide35):" + ex.Message));
      }
    }

    public static void SetSlide36(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 129.5433f, 195.591f, 765.354f, 527.2441f);
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.AddTextbox(MsoTextOrientation.msoTextOrientationHorizontal, 102f, 149f, 297f, 32f);
        if (shape == null)
          return;
        shape.TextFrame.MarginLeft = 0.0f;
        shape.TextFrame.MarginRight = 0.0f;
        shape.TextFrame.MarginTop = 0.0f;
        shape.TextFrame.MarginBottom = 0.0f;
        shape.TextFrame.TextRange.ParagraphFormat.SpaceWithin = 1.4f;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape.TextFrame.TextRange;
        textRange1.Font.Name = "Arial";
        if (!dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Deflection Y")))
          return;
        string[] strArray = dicData["Deflection Y"].Split('/');
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = textRange1.InsertAfter("Y : " + strArray[1] + " mm, -Y : " + (object) Math.Abs(Convert.ToDouble(strArray[0])) + " mm");
        textRange2.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide36):" + ex.Message));
      }
    }

    public static void SetSlide37(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 129.5433f, 195.591f, 765.354f, 527.2441f);
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.AddTextbox(MsoTextOrientation.msoTextOrientationHorizontal, 102f, 149f, 297f, 32f);
        if (shape == null)
          return;
        shape.TextFrame.MarginLeft = 0.0f;
        shape.TextFrame.MarginRight = 0.0f;
        shape.TextFrame.MarginTop = 0.0f;
        shape.TextFrame.MarginBottom = 0.0f;
        shape.TextFrame.TextRange.ParagraphFormat.SpaceWithin = 1.4f;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape.TextFrame.TextRange;
        textRange1.Font.Name = "Arial";
        if (!dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Deflection Y")))
          return;
        string[] strArray = dicData["Deflection Y"].Split('/');
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = textRange1.InsertAfter("Y : " + strArray[1] + " mm, -Y : " + (object) Math.Abs(Convert.ToDouble(strArray[0])) + " mm");
        textRange2.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide37):" + ex.Message));
      }
    }

    public static void SetSlide38(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 129.5433f, 195.591f, 765.354f, 527.2441f);
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.AddTextbox(MsoTextOrientation.msoTextOrientationHorizontal, 102f, 149f, 297f, 32f);
        if (shape == null)
          return;
        shape.TextFrame.MarginLeft = 0.0f;
        shape.TextFrame.MarginRight = 0.0f;
        shape.TextFrame.MarginTop = 0.0f;
        shape.TextFrame.MarginBottom = 0.0f;
        shape.TextFrame.TextRange.ParagraphFormat.SpaceWithin = 1.4f;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape.TextFrame.TextRange;
        textRange1.Font.Name = "Arial";
        if (!dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Deflection Z")))
          return;
        string[] strArray = dicData["Deflection Z"].Split('/');
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = textRange1.InsertAfter("Z : " + strArray[1] + " mm, -Z : " + (object) Math.Abs(Convert.ToDouble(strArray[0])) + " mm");
        textRange2.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide38):" + ex.Message));
      }
    }

    public static void SetSlide39(Slide p_slData, Dictionary<string, string> dicData)
    {
      try
      {
        if (dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(dicData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 129.5433f, 195.591f, 765.354f, 527.2441f);
        }
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.AddTextbox(MsoTextOrientation.msoTextOrientationHorizontal, 102f, 149f, 297f, 32f);
        if (shape == null)
          return;
        shape.TextFrame.MarginLeft = 0.0f;
        shape.TextFrame.MarginRight = 0.0f;
        shape.TextFrame.MarginTop = 0.0f;
        shape.TextFrame.MarginBottom = 0.0f;
        shape.TextFrame.TextRange.ParagraphFormat.SpaceWithin = 1.4f;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape.TextFrame.TextRange;
        textRange1.Font.Name = "Arial";
        if (!dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Deflection Z")))
          return;
        string[] strArray = dicData["Deflection Z"].Split('/');
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = textRange1.InsertAfter("Z : " + strArray[1] + " mm, -Z : " + (object) Math.Abs(Convert.ToDouble(strArray[0])) + " mm");
        textRange2.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]SetSlide39):" + ex.Message));
      }
    }
  }
}
