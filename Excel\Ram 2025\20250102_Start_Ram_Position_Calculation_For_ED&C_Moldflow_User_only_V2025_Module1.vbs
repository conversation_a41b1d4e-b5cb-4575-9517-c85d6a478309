Attribute VB_Name = "Module1"

Sub Import_Information()
    TDAY = Format(Date, "yyyy-mm-dd")
    EDAY = "2025-12-31"
    
    If TDAY > EDAY Then
        MsgBox ("사용날짜가 지났습니다. ED&C에 문의하세요." & vbCr & "02-2069-0099 / <EMAIL>")
        Exit Sub
    End If
    
    'EventsEnabled = False
    Application.EnableEvents = False
    
    ' Conexión con Synergy usando el mismo patrón que TAPEbyAudi.vbs
    Dim SynergyGetter As Object, Synergy As Object
    On Error Resume Next
    
    ' Primera forma - usando WScript.Shell
    Set SynergyGetter = GetObject(CreateObject("WScript.Shell").ExpandEnvironmentStrings("%SAInstance%"))
    On Error GoTo 0
    
    If (Not IsEmpty(SynergyGetter)) And (Not SynergyGetter Is Nothing) Then
        Set Synergy = SynergyGetter.GetSASynergy
    Else
        ' Segunda forma - directa
        On Error Resume Next
        Set Synergy = GetObject("synergy.Synergy")
        If Err.Number <> 0 Then
            MsgBox "No se pudo conectar con la instancia activa de Synergy." & vbCrLf & _
                   "Por favor asegúrese que:" & vbCrLf & _
                   "1. Synergy está abierto" & vbCrLf & _
                   "2. Tiene un estudio abierto" & vbCrLf & _
                   "3. No hay diálogos o ventanas modales abiertas", vbCritical
            Exit Sub
        End If
        On Error GoTo 0
    End If
    
    If Synergy Is Nothing Then
        MsgBox "No se pudo establecer conexión con Synergy", vbCritical
        Exit Sub
    End If
    
    On Error Resume Next
    Synergy.SetUnits "Metric"
    If Err.Number <> 0 Then
        MsgBox "Error al configurar las unidades: " & Err.Description, vbCritical
        Exit Sub
    End If
    On Error GoTo 0



Dim StudyDoc

Set StudyDoc = Synergy.StudyDoc()



'Mesh Type 가져오기

Dim MeshType

MeshType = StudyDoc.MeshType

If MeshType = "Fusion" Then

    MeshType = "Dual-Domain"

    Else

End If



'Out 파일 가져오기

Dim AnalysisType

Set StudyDoc = Synergy.StudyDoc()

AnalysisType = StudyDoc.AnalysisSequence



If AnalysisType = "Fill" Then

ElseIf AnalysisType = "Flow|Warp" Or AnalysisType = "Flow" Or AnalysisType = "Cool|Flow|Warp" Then

    AnalysisType = "Flow"

End If



Dim lName_A, lOutName, StringLength

lName_A = StudyDoc.GetResultPrefix(AnalysisType)

Set Project = Synergy.Project()

lOutName = Project.Path

StringLength = Len(lOutName)

lOutNameA = lOutName & "\" & lName_A & ".out"



Dim lMessages As ScreenOutput

Set lMessages = New ScreenOutput

On Error Resume Next

lMessages.LoadOutputFile (lOutNameA)

On Error GoTo 0 ' 오류 처리 원래대로 복원



'평균 수축률 가져오기

Set MM = lMessages.GetMessage(40704, 1)

Ft = MM.GetFloat(0)

VAV = Round(Ft, 4)

PVatResult = 100 - VAV



'Mesh 정보 가져오기

'2D인 경우

If MeshType = "Dual-Domain" Or MeshType = "Midplane" Then



    '제품 충전 부피

    Dim PV

    Set MM = lMessages.GetMessage(39322, 1)

    PV = CStr(MM.GetFloat(0)) * 1000000

    Partvolume = Format(PV, "##,##0.0000")



    'Cold Runner

    Set MM = lMessages.GetMessage(39324, 1)

    PV = CStr(MM.GetFloat(0)) * 1000000

    Cold_R = Format(PV, "##,##0.0000")

    

    'Hot Runner

    Set MM = lMessages.GetMessage(39310, 1)

    PV = CStr(MM.GetFloat(0)) * 1000000

    Hot_R = Format(PV, "##,##0.0000")

    

    '투영면적

    Set MM = lMessages.GetMessage(39410, 1)

    Pa = CStr(MM.GetFloat(0)) * 10000

    Projectarea = Format(Pa, "##,##0.0000 cm^2")



'3D인 경우

ElseIf MeshType = "3D" Then

    

    '제품 충전 부피

    Set MM = lMessages.GetMessage(300320, 1)

    PV = CStr(MM.GetFloat(0)) * 1000000

    Partvolume = Format(PV, "##,##0.0000")

    

    'Cold Runner

    Set MM = lMessages.GetMessage(300330, 1)

    PV = CStr(MM.GetFloat(0)) * 1000000

    Cold_R = Format(PV, "##,##0.0000")



    'Hot Runner

    Set MM = lMessages.GetMessage(300300, 1)

    PV = CStr(MM.GetFloat(0)) * 1000000

    Hot_R = Format(PV, "##,##0.0000")



    '투영면적

    Set MM = lMessages.GetMessage(300350, 1)

    Pa = CStr(MM.GetFloat(0)) * 10000

    Projectarea = Format(Pa, "##,##0.0000 cm^2")

    

End If



Dim MatID



Set PropEd = Synergy.PropertyEditor()

Set Prop = PropEd.GetFirstProperty(40000)

Set DblArr = Prop.FieldValues(20020)

VbArr = DblArr.ToVBSArray()

MatID = VbArr(1)



'MATERIAL

Set Prop = PropEd.FindProperty(21000, MatID)



Set Melt_density = Prop.FieldValues(1000) 'Melt density

M_density = Melt_density.Val(0)

Set Solid_density = Prop.FieldValues(1001) 'Solid density

S_density = Solid_density.Val(0)



'INJECTION MACHINE

N = GetLastTsetID(30007)

Set Prop = PropEd.FindProperty(30007, N)



Set Machin_Screw_diameter = Prop.FieldValues(10008) 'Machin_Screw_diameter

MSD = Machin_Screw_diameter.Val(0)



Set Maximum_Machine_Injection_Rate = Prop.FieldValues(10005) 'Maximum_Machine_Injection_Rate

MMIR = Maximum_Machine_Injection_Rate.Val(0)



'Ram speed, recommended:XY Plot 결과 출력

FilePath = ThisWorkbook.FullName

DirectoryPath = Left(FilePath, InStrRev(FilePath, "\"))



Set PlotManager = Synergy.PlotManager()

Set Plot = PlotManager.FindPlotByName2("Ram speed, recommended:XY Plot", "Recommended ram speed")

Set Viewer = Synergy.Viewer()

Viewer.ShowPlot Plot

Plot.SaveXYPlotCurveData DirectoryPath & "\" & "recommended_XY_Plot_Recommended_ram_speed_aaa.txt"

Viewer.HidePlot Plot



Dim FileNumber As Integer

Dim LineData As String

Dim LineItems() As String

Dim RowIndex As Integer

Dim ColIndex As Integer



' 파일 경로 설정 (사용자가 직접 메모장 파일의 경로를 입력해야 합니다)

txtFilePath = DirectoryPath & "\" & "recommended_XY_Plot_Recommended_ram_speed_aaa.txt"



' 시작 위치 설정 (엑셀 시트에서 "C17"은 행 17, 열 3 입니다)

startRow = 17

startCol = 3



' 파일 열기

FileNumber = FreeFile

Open txtFilePath For Input As #FileNumber



' 초기 행 인덱스 설정 및 라인 카운트 초기화

RowIndex = startRow

LineCount = 0

rowCount = 1

' 파일에서 한 줄씩 읽기



Do While Not EOF(FileNumber)

    Line Input #FileNumber, LineData

    LineCount = LineCount + 1



    ' 상단 2줄을 건너뛰기

    If LineCount <= 2 Then

        GoTo SkipLine

    End If

    

    If LineData = PreviousLineData Then

        GoTo SkipLine

    End If

    

    ' 탭을 기준으로 데이터 분리

    LineItems = Split(LineData, vbTab)



    ' 각 항목을 엑셀 셀에 삽입

    For ColIndex = 0 To UBound(LineItems)

        Cells(RowIndex, startCol + ColIndex).Value = LineItems(ColIndex)

    Next ColIndex

    Cells(RowIndex, "B") = "Step " & rowCount

    PreviousLineData = LineData

    ' 다음 행으로 이동

    RowIndex = RowIndex + 1

    rowCount = rowCount + 1



SkipLine:

Loop

rowCount = rowCount - 1

' 파일 닫기

Close #FileNumber

    

If MSD = "0" Then

    MsgBox ("Moldflow에 설정된 스크류 직경이 없거나" & vbCr & "" & "0" & "" & "이기 때문에 기본으로 45mm가 적용됩니다." & vbCr & "스크류 직경 입력란에 알맞은 값을 입력하세요")

    Cells(5, "F").Value = 45

Else

    Cells(5, "F") = MSD

End If



Cells(5, "C") = M_density

Cells(6, "C") = S_density

Cells(7, "C").Value = Cells(6, "C").Value - Cells(5, "C").Value

Cells(10, "C") = Partvolume

Cells(11, "C") = Cold_R

Cells(12, "C") = Hot_R

Cells(13, "C").Value = Cells(10, "C").Value + Cells(11, "C").Value + Cells(12, "C").Value

Cells(7, "F") = MMIR

Cells(10, "H") = PVatResult

Cells(17 + L, "I") = 0

Set Delete_Table = Range("H17:I37")

Delete_Table.ClearContents



If Cells(13, "G").Value = "선택" And Cells(14, "G").Value = "해제" Then

    Cells(17, "E").Value = Cells(13, "F").Value

    Cells(17, "H").Value = Cells(13, "F").Value

ElseIf Cells(13, "G").Value = "해제" And Cells(14, "G").Value = "선택" Then

    Cells(17, "E").Value = Cells(14, "F").Value

    Cells(17, "H").Value = Cells(14, "F").Value

    End If



Cells(6, "G").Value = Cells(5, "F").Value * Cells(6, "F").Value

Cells(5, "G").Value = (Cells(5, "F").Value * Cells(5, "F").Value) * 3.14 / 4 / 100

Cells(8, "F").Value = (Cells(7, "F").Value / Cells(5, "G").Value) * 10

Cells(11, "F").Value = (Cells(10, "C").Value + Cells(11, "C").Value) * Cells(6, "C").Value

Cells(11, "G").Value = (Cells(10, "C").Value + Cells(11, "C").Value) * Cells(5, "C").Value

Cells(11, "H").Value = (Cells(10, "C").Value + Cells(11, "C").Value) * Cells(6, "C").Value * (Cells(10, "H").Value / 100)

Cells(13, "F").Value = 10 * (Cells(11, "F").Value / (Cells(5, "G").Value * Cells(5, "C").Value * Cells(12, "F").Value)) + Cells(5, "F").Value * Cells(6, "F").Value

Cells(14, "F").Value = 10 * ((Cells(13, "C") - Cells(12, "C")) / Cells(5, "G").Value) + Cells(6, "G")



If Cells(13, "G").Value = "선택" And Cells(14, "G").Value = "해제" Then

    Cells(17, "E").Value = Cells(13, "F").Value

    Cells(17, "H").Value = Cells(13, "F").Value

ElseIf Cells(13, "G").Value = "해제" And Cells(14, "G").Value = "선택" Then

    Cells(17, "E").Value = Cells(14, "F").Value

    Cells(17, "H").Value = Cells(14, "F").Value

    End If

Cells(17, "F") = Cells(17, "D") * Cells(7, "K").Value / 100

Cells(17, "G").Value = 1

For i = 1 To rowCount - 1

    Cells(17 + i, "E") = Cells(17 + i - 1, "E") - ((Cells(17, "E").Value - Cells(6, "G").Value) * ((Cells(17 + i, "C") - Cells(17 + i - 1, "C")) / 100))

    Cells(17 + i, "F") = Cells(17 + i, "D") * Cells(7, "K").Value / 100

Next



Step_Setting = Cells(15, "I")

For i = 1 To rowCount

    If i <= (rowCount / Step_Setting) Then

        Cells(16 + i, "G").Value = 1

    ElseIf i <= ((rowCount / Step_Setting) * 2) And i > (rowCount / Step_Setting) And Step_Setting >= 2 Then

        Cells(16 + i, "G").Value = 2

    ElseIf i <= ((rowCount / Step_Setting) * 3) And i > ((rowCount / Step_Setting) * 2) And Step_Setting >= 3 Then

        Cells(16 + i, "G").Value = 3

    ElseIf i <= ((rowCount / Step_Setting) * 4) And i > ((rowCount / Step_Setting) * 3) And Step_Setting >= 4 Then

        Cells(16 + i, "G").Value = 4

    ElseIf i <= ((rowCount / Step_Setting) * 5) And i > ((rowCount / Step_Setting) * 4) And Step_Setting >= 5 Then

        Cells(16 + i, "G").Value = 5

    ElseIf i <= ((rowCount / Step_Setting) * 6) And i > ((rowCount / Step_Setting) * 5) And Step_Setting >= 6 Then

        Cells(16 + i, "G").Value = 6

    ElseIf i <= ((rowCount / Step_Setting) * 7) And i > ((rowCount / Step_Setting) * 6) And Step_Setting >= 7 Then

        Cells(16 + i, "G").Value = 7

    ElseIf i <= ((rowCount / Step_Setting) * 8) And i > ((rowCount / Step_Setting) * 7) And Step_Setting >= 8 Then

        Cells(16 + i, "G").Value = 8

    ElseIf i <= ((rowCount / Step_Setting) * 9) And i > ((rowCount / Step_Setting) * 8) And Step_Setting >= 9 Then

        Cells(16 + i, "G").Value = 9

    ElseIf i < rowCount And i > ((rowCount / Step_Setting) * 9) And Step_Setting >= 10 Then

        Cells(16 + i, "G").Value = 10

    ElseIf i = rowCount Then

        Cells(16 + i, "G").Value = Step_Setting

    End If

Next



HH = 1

II = 1



For L = 0 To ((Step_Setting * 2) - 1) Step 2

    If L = 0 Then

        Cells(17 + L, "I") = 0

        ' AVERAGEIF 함수 적용

        Cells(18 + L, "I").Formula = "=AVERAGEIF(G17:G42," & II & ", F17:F42)"

        ' 일반 수식

        Cells(18 + L, "H").Formula = "=H17-IFERROR((0.5*((I18/J15)*J15^2)),0)"



    Else

        ' 배열 수식을 FormulaArray로 적용

        Cells(18 + L, "H").FormulaArray = "=MIN(IF(G17:G42=" & HH - 1 & ", E17:E42))-IFERROR(((" & Cells(17 + L, "I").Value & "*J15)+(0.5*((" & Cells(18 + L, "I").Value & "-" & Cells(17 + L, "I").Value & ")/J15)*J15^2)),0)"

        

    End If



    ' 배열 수식을 FormulaArray로 적용

    Cells(19 + L, "H").FormulaArray = "=MIN(IF(G17:G42=" & HH & ", E17:E42))"

    ' AVERAGEIF 함수 적용

    Cells(18 + L, "I").Formula = "=AVERAGEIF(G17:G42," & II & ", F17:F42)"

    Cells(19 + L, "I").Formula = "=AVERAGEIF(G17:G42," & II & ", F17:F42)"

    HH = HH + 1

    II = II + 1

Next



SR = 20 + ((Step_Setting - 1) * 2)

ER = SR + 1 + ((10 - Step_Setting) * 2)

Set Delete_Table = Range("H" & SR & ":I" & ER)

Delete_Table.ClearContents



If Cells(7, "K").Value > Cells(8, "F").Value * 0.85 Then

    Range("J7").Interior.ColorIndex = 3

    Range("J6").Interior.ColorIndex = 4

    Range("E8").Interior.ColorIndex = 4

    MsgBox ("시간에 의해 계산된 최대 램 속도가" & vbCr & "사출기 최대 속도의 85% 보다 큽니다." & vbCr & "사출시간 또는 사출기 최고 속도를 변경하세요.")

Else

    Range("J7").Interior.ColorIndex = 2

    Range("J6").Interior.ColorIndex = 2

    Range("E8").Interior.ColorIndex = 2

End If



' 시트 보호를 다시 설정

Sheet2.Protect Password:=sheetPassword

Application.EnableEvents = True

'EventsEnabled = True



End Sub



' Function: Get Last Tset ID for the model

Function GetLastTsetID(Tset)

    Set Synergy = CreateObject("synergy.Synergy")

    'Msgbox Tset

    GetLastTsetID = 0

    Set Project = Synergy.Project()

    Set PropEd = Synergy.PropertyEditor()

    Set TestPropertyID = PropEd.GetFirstProperty(Tset)

        

    'GetLastTsetID = TestPropertyID.ID

    

    'MsgBox GetLastTsetID

    While Not TestPropertyID Is Nothing

        If TestPropertyID.ID > GetLastTsetID Then

            GetLastTsetID = TestPropertyID.ID

            'MsgBox GetLastTsetID

        End If

        Set TestPropertyID = PropEd.GetNextPropertyOfType(TestPropertyID)

    Wend

    

    'MsgBox GetLastTsetID

End Function

Function FindLastRow(startRow As Long, columnLetter As String) As Long

    Dim lastRow As Long

    

    ' 지정된 열의 마지막 행을 찾습니다.

    lastRow = Cells(Rows.count, columnLetter).End(xlUp).Row

    

    ' 시작 행부터 마지막 데이터를 찾습니다.

    If lastRow < startRow Then

        FindLastRow = -1 ' 시작 행 이후에 데이터가 없음을 나타내기 위해 -1 반환

    Else

        Do While IsEmpty(Cells(lastRow, columnLetter)) Or lastRow < startRow

            lastRow = lastRow - 1

        Loop

        

        If lastRow < startRow Then

            FindLastRow = -1 ' 시작 행 이후에 데이터가 없음을 나타내기 위해 -1 반환

        Else

            FindLastRow = lastRow

        End If

    End If

End Function

