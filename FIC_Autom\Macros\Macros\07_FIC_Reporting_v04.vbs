'%RunPerInstance
'@
'@@ 
Option Explicit
SetLocale("en-us")
Dim Synergy
Dim SynergyGetter
Dim PlotMgr, Plot, Viewer

Const MaxResultValue = 1.0E20

Dim FS
Set FS = CreateObject("Scripting.FileSystemObject")

Dim WshShell
Set WshShell = WScript.CreateObject("WScript.Shell")

On Error Resume Next
Set SynergyGetter = GetObject(CreateObject("WScript.Shell").ExpandEnvironmentStrings("%SAInstance%"))
On Error GoTo 0
If (Not IsEmpty(SynergyGetter)) Then
	Set Synergy = SynergyGetter.GetSASynergy
Else
	Set Synergy = CreateObject("amiws.Synergy")
End If

Dim debug
Dim StudyDoc
Set StudyDoc = Synergy.StudyDoc()
Set Viewer = Synergy.Viewer()
Set PlotMgr = Synergy.PlotManager()

Set LayerManager = Synergy.LayerManager()
LayerManager.ShowAllLayers

Dim ahora, luego, Tiempo
ahora = Time

Dim objExcelApp, objWB, objSheet
Dim xlsTemplate
Dim pptTemplate, objPPTApp, objPPT

' ########## VARIABLES A CONFIGURAR ##########
' Plantilla PPT
pptTemplate = "M:\CAE\2_TECHNICAL_INFORMATION\7_Rheologic\3_Technical_Information\Moldflow\Macros\CAE_Reports_Templates_Rheologic-StandardProcess_22_Abril_2024.pptx"
' Plantilla Excel tablas para informe
xlsTemplate = "M:\CAE\2_TECHNICAL_INFORMATION\7_Rheologic\3_Technical_Information\Moldflow\Macros\CAE_Reports_Templates_Rheologic-ReportSupportingTables_12feb2024.xlsx"
' Variable para hacer debug (True o False). En True abre una ventana, en False rellena las plantillas.
debug = False
' Otros valores metidos en código:
'	-	Celdas del Excel de plantillas en lineas que contienen objSheet.Range
'	-	Los números de página del informe PPT para los resultados de Fill dentro de la sub GenerateFillSequence()
' ########## FIN VARIABLES A CONFIGURAR ##########


' Get the name of the .out file associated with the flow results.
' NOTE: This assumes a Flow analysis sequence was run.
Dim lName 
lName = StudyDoc.GetResultPrefix("Flow")
Dim lOutName
lOutName = lName & ".out" 

' Create and Populate the ScreenOutput Class for the lOutName
Dim lMessages
Set lMessages = New ScreenOutput
lMessages.LoadOutputFile(lOutName)


Dim MM, lStr
Dim rdPartVolume, rdRunnersVolume, rdProjectedArea, rdCavityFillTime
Dim rdPackingTime1, rdPackingPressure1, rdPackingTime2, rdPackingPressure2, rdPackingTime3, rdPackingPressure3, rdPackingTime4, rdPackingPressure4, rdPackingTime5, rdPackingPressure5 
Dim rdPackingPorcentage1, rdPackingPorcentage2, rdPackingPorcentage3, rdPackingPorcentage4, rdPackingPorcentage5
Dim rdFinalPartWeight, rdFinalRunnersWeight, rdMeltTemperature, rdMoldTemperature
Dim rdMaxClampForce, rdMaxFFTemp, rdMinFFTemp, rdMaxPressure, rdMinPressure
Dim rdFlowFrontTempRange
Dim LayerManager, LayerList, PropEd, Prop, LayerList1, EntList

lStr = "RESULTADOS" & vbCrlf & vbCrlf

' PART VOLUME 
'MSCD 300320 1 0 0 0 0 0 1
'           Part volume to be filled                       = %11.4G
'    m^3,1,1
Set MM = lMessages.GetMessage(300320,1)
lStr = lstr & "Volumen de Pieza a llenar: " & CStr(MM.GetFloat(0)*1000000) & "cm3" & vbCr
rdPartVolume = CStr(MM.GetFloat(0)*1000000)

' RUNNERS VOLUME
'MSCD 300330 1 0 0 0 0 0 5
'           Sprue/runner/gate volume to be filled          = %11.4G
'    m^3,1,1
Set MM = lMessages.GetMessage(300330,1)
lStr = lstr & "Volumen de ramales a llenar: " & CStr(MM.GetFloat(0)*1000000) & "cm3" & vbCr
rdRunnersVolume = CStr(MM.GetFloat(0)*1000000)

'Pressure at gate from V/P Plot
Set Plot = PlotMgr.FindPlotByName("Pressure at V/P switchover")
Viewer.ShowPlot Plot
Set LayerManager = Synergy.LayerManager()
LayerManager.ShowAllLayers


Set LayerList = LayerManager.FindLayerByName("Cooling")
LayerManager.ToggleLayer LayerList
Set LayerList = LayerManager.FindLayerByName("Tetras_1")
LayerManager.ToggleLayer LayerList
Set LayerList = LayerManager.FindLayerByName("Tetras_2")
LayerManager.ToggleLayer LayerList
Set LayerList = LayerManager.FindLayerByName("Beams_2ndshot")
LayerManager.ToggleLayer LayerList
Set LayerList = LayerManager.FindLayerByName("Tetras_2ndshot_1")
LayerManager.ToggleLayer LayerList
Set LayerList = LayerManager.FindLayerByName("Tetras_2ndshot_2")
LayerManager.ToggleLayer LayerList



WScript.Sleep 2000

Plot.Regenerate



rdMaxPressure = CStr(Plot.GetMaxValue())
rdMinPressure = CStr(Plot.GetMinValue())
lStr = lStr & "Max " & rdMaxPressure & "MPa and Min " & rdMinPressure & "MPa " & vbCrlf
LayerManager.ShowAllLayers 

'#####################CODE BY MANUEL HERVAS##################'


'PACKING'
'MSCD 302055 3 1 0 0 2 0 5
'       Pressure profile:
'             duration  %% filling pressure
'             -----------------------------
'           %11.2G       %11.2G
'   s,1,1,2
'   ,1,2,2

Dim aMSCD
aMSCD = 302055

Dim bMSCD, NN
bMSCD = 302056

Set MM = lMessages.GetMessage(aMSCD,1)
Set NN = lMessages.GetMessage(bMSCD,1)


Dim arrPackingRange(), I, count


Redim arrPackingRange(9)

For I = 0 to 9
	arrPackingRange(I) = 0	
Next

count=0

If NN Is Nothing Then

	For I = 0 to MM.getNumFloat-1 step 2
		If ((Cstr((MM.GetMSCD) = Cstr(aMSCD))) AND (MM.GetFloat(I)<>0)) Then
			
			
			arrPackingRange(count) =(MM.GetFloat(I))
			arrPackingRange(count+1) =(MM.GetFloat(I+1))
			
			count = count + 2
		
		End If
	Next
	
	rdPackingTime1 = (arrPackingRange(0))
	rdPackingPorcentage1 = (arrPackingRange(1))/100
	rdPackingPressure1 = rdMaxPressure * rdPackingPorcentage1
	rdPackingTime2 = (arrPackingRange(2))
	rdPackingPorcentage2 = (arrPackingRange(3))/100
	rdPackingPressure2 = rdMaxPressure * rdPackingPorcentage2
	rdPackingTime3 = (arrPackingRange(4))
	rdPackingPorcentage3 = (arrPackingRange(5))/100
	rdPackingPressure3 = rdMaxPressure * rdPackingPorcentage3
	rdPackingTime4 = (arrPackingRange(6))
	rdPackingPorcentage4 =(arrPackingRange(7))/100
	rdPackingPressure4 = rdMaxPressure * rdPackingPorcentage4
	rdPackingTime5 = (arrPackingRange(8))
	rdPackingPorcentage5 =(arrPackingRange(9))/100
	rdPackingPressure4 = rdMaxPressure * rdPackingPorcentage4
	
	
Else
	For I = 0 to NN.getNumFloat-1 step 2
		If ((Cstr((NN.GetMSCD) = Cstr(bMSCD))) AND (NN.GetFloat(I)<>0)) Then
			
			
			arrPackingRange(count) =(NN.GetFloat(I))
			arrPackingRange(count+1) =(NN.GetFloat(I+1))
			
			count = count + 2
		
		End If
	Next
	
	rdPackingTime1 = (arrPackingRange(0))
	rdPackingPorcentage1 = (arrPackingRange(1))/(rdMaxPressure*1000000)
	rdPackingPressure1 = arrPackingRange(1)/1000000
	rdPackingTime2 = (arrPackingRange(2))
	rdPackingPorcentage2 = (arrPackingRange(3))/(rdMaxPressure*1000000)
	rdPackingPressure2 = arrPackingRange(3)/1000000
	rdPackingTime3 = (arrPackingRange(4))
	rdPackingPorcentage3 = (arrPackingRange(5))/(rdMaxPressure*1000000)
	rdPackingPressure3 = arrPackingRange(5)/1000000
	rdPackingTime4 = (arrPackingRange(6))
	rdPackingPorcentage4 =(arrPackingRange(7))/(rdMaxPressure*1000000)
	rdPackingPressure4 = arrPackingRange(7)/1000000
	rdPackingTime5 = (arrPackingRange(8))
	rdPackingPorcentage5 =(arrPackingRange(9))/(rdMaxPressure*1000000)
	rdPackingPressure5 = arrPackingRange(9)/1000000
	
End if




'----
'MSCD 302056 3 1 0 0 2 0 5
'       Pressure profile:
'              duration            pressure
'              ----------------------------
'            %11.2G       %11.4G
'    s,1,1,2
'    Pa,1,2,2


' PROJECTED AREA
' MSCD 300350 1 0 0 0 0 0 5
'       Total projected area                               = %11.4G
'    m^2,1,1
' Note: Please see .../data/dat/shared/cmmesage.dat for format details
Set MM = lMessages.GetMessage(300350,1)
lStr = lstr & "Total Proejcted Area    = " & CStr(MM.GetFloat(0)*10000) & "cm2" & vbLf
rdProjectedArea = CStr(MM.GetFloat(0)*10000)

' CAVITY FILL TIME
' MSCD 300400 1 0 0 0 0 0 5
       ' Current time from start of cycle                   = %11.4G
    ' s,1,1
' ----
Set MM = lMessages.GetMessage(300400,1)
If Not MM is Nothing Then 
	lStr = lstr & "Tiempo Llenado : " & CStr(MM.GetFloat(0)) & "s" & vbCr
End if
rdCavityFillTime = CStr(MM.GetFloat(0))



' PART WEIGHT AFTER PACKING
' Workaround to check for "no runners"
' Return the MSCD 304139
'MSCD 304139 1 0 0 0 0 0 5
'          Part mass                                       = %11.4G
'    kg,1,1
' MSCD 300370 1 0 0 0 0 0 5
       ' Total mass                                         = %11.4G
    ' kg,1,1
Set MM = lMessages.GetMessage(304139,2)
If Not MM is Nothing Then 
	lStr = lstr & "Peso pieza : " & CStr(MM.GetFloat(0)*1000) & "g" & vbCr
	rdFinalPartWeight = CStr(MM.GetFloat(0)*1000)
Else
	Set MM = lMessages.GetMessage(300370,2)
	lStr = lstr & "Peso pieza : " & CStr(MM.GetFloat(0)*1000) & "g" & vbCr
	rdFinalPartWeight = CStr(MM.GetFloat(0)*1000)
End If


' RUNNERS WEIGHT AFTER PACKING
' Return the MSCD 304146
'MSCD 304146 1 0 0 0 0 0 5
'          Sprue/runner/gate mass                          = %11.4G
'    kg,1,1
Set MM = lMessages.GetMessage(304146,1)
If Not MM is Nothing Then 
	lStr = lstr & "Peso ramales : " & CStr(MM.GetFloat(0)*1000) & "g" & vbCr
	rdFinalRunnersWeight = CStr(MM.GetFloat(0)*1000)
End if

' MSCD 102101 1 0 0 0 0 0 5
       ' Melt temperature                                   = %11.4G
    ' K,1,1
' ----
Set MM = lMessages.GetMessage(102101,1)
If Not MM is Nothing Then 
	lStr = lstr & "Melt Temperature : " & CStr(MM.GetFloat(0) - 273.15) & "C" & vbCr
	rdMeltTemperature = CStr(MM.GetFloat(0) - 273.15)
End if

' ----
' MSCD 220124 1 0 0 0 0 0 5
       ' Mold temperature                                   = %11.2f
    ' K,1,1
' ----
Set MM = lMessages.GetMessage(220124,1)
If Not MM is Nothing Then 
	lStr = lstr & "Mold Temperature : " & CStr(MM.GetFloat(0) - 273.15) & "C" & vbCr
	rdMoldTemperature = CStr(MM.GetFloat(0) - 273.15)
End if

if debug = True Then
	Dim ppTitulo, ppMensaje
	ppTitulo = "ATENCION"
	ppMensaje = "La recopilacion de todos los resultados" & vbCrlf & "puede tardar hasta 5 minutos." & vbCrlf & vbCrlf & " ESPERAR A QUE APAREZCA EL MENSAJE DE FIN "
	WshShell.Popup ppMensaje, 5, ppTitulo, 64
End if

'Clamp Force from Result
rdMaxClampForce = getMaxClampForce()
lStr = lStr & "Max Clamp Force : " & rdMaxClampForce & " tonne" & vbCrlf

'@ Desactivado por ineficiente ... para 2M de elementos tarda >6 minutos
'Temperature at flowfront from Result
Set rdFlowFrontTempRange = Synergy.CreateDoubleArray()
Set rdFlowFrontTempRange = getRangeTempFlowFront()
rdMaxFFTemp = CStr(rdFlowFrontTempRange.Val(1))
rdMinFFTemp = CStr(rdFlowFrontTempRange.Val(0))
lStr = lStr & "Flow front Temperature range : " & rdMinFFTemp & "C to " & rdMaxFFTemp & "C" & vbCrlf



'Material related Information
Dim MaterialID, MaterialSubID, Material2ID, Material2SubID
Call GetMaterialData(MaterialID, MaterialSubID, Material2ID, Material2SubID)
Dim arrRecommendedTemps, MaterialName, FamilyAbreviation
If MaterialID > 0 Then
	Set arrRecommendedTemps = Synergy.CreateDoubleArray()
	Call GetTCodeValue(MaterialID, MaterialSubID, 1800, arrRecommendedTemps)
	Call GetTCodeDescription(MaterialID, MaterialSubID, 1998, MaterialName)
	Call GetTCodeDescription(MaterialID, MaterialSubID, 1999, FamilyAbreviation)
	 lStr = lStr & "Material: " & MaterialName & vbCrlf
	 lStr = lStr & "Family: " & FamilyAbreviation & vbCrlf
	 lStr = lStr & "Temp range: " & arrRecommendedTemps.Val(0) & "-" & arrRecommendedTemps.Val(1) & vbCrlf
End if

if debug = True Then
	luego = Time
	Tiempo = DateDiff("s", ahora, luego)
	lStr = lStr & vbCrlf & vbCrlf & "Tiempo de ejecucion: " & Tiempo & "s"
	MsgBox CStr(lStr)
	Wscript.Quit(0)
End If

' ------ Supporting XLS Management ------
Set objExcelApp = CreateObject("Excel.Application")
Set objWB = objExcelApp.Workbooks.Open(xlsTemplate,3, True)
objExcelApp.Visible = True
'objWB.Worksheets(x) refers to sheet X in the document, better than call by name
Set objSheet = objWB.Worksheets(1)
objSheet.Activate
objSheet.Range("E3").Formula = "=round(" & rdProjectedArea & ",0)"
objSheet.Range("E4").Value = "=round(" & rdPartVolume & ",1)"
objSheet.Range("E5").Value = "=round(" & rdRunnersVolume & ",1)"
objSheet.Range("E8").Value = "=round(" & rdFinalPartWeight & ",1)"
objSheet.Range("E11").Value = "=round(" & rdFinalRunnersWeight & ",1)"
objSheet.Range("E15").Value = "=round(" & rdCavityFillTime & ",1)"
objSheet.Range("E25").Value = "=round(" & rdMaxPressure & ",0)"
objSheet.Range("E27").Value = "=round(" & rdMinPressure & ",0)"
objSheet.Range("E13").Value = "=round(" & rdMeltTemperature & ",0)"
objSheet.Range("E14").Value = "=round(" & rdMoldTemperature & ",0)"
objSheet.Range("E29").Value = "=roundup(" & rdMaxClampForce & ",0)"
objSheet.Range("E30").Value = "=round(" & rdMaxFFTemp & ",0)"
objSheet.Range("E31").Value = "=round(" & rdMinFFTemp & ",0)"


Set objSheet = objWB.Worksheets(2)
objSheet.Activate
objSheet.Range("E2").Formula = FamilyAbreviation
objSheet.Range("E3").Formula = MaterialName
objSheet.Range("E7").Formula = FamilyAbreviation
objSheet.Range("E8").Formula = MaterialName
objSheet.Range("E20").Value = "=(" & rdPackingTime1 & ")"
objSheet.Range("F20").Value = "=roundup(" & rdPackingPorcentage1 & ",2)"
objSheet.Range("G20").Value = "=roundup(" & rdPackingPressure1 & ",0)"
objSheet.Range("E21").Value = "=(" & rdPackingTime2 & ")"
objSheet.Range("F21").Value = "=roundup(" & rdPackingPorcentage2 & ",2)"
objSheet.Range("G21").Value = "=roundup(" & rdPackingPressure2 & ",0)"
objSheet.Range("E22").Value = "=(" & rdPackingTime3 & ")"
objSheet.Range("F22").Value = "=roundup(" & rdPackingPorcentage3 & ",2)"
objSheet.Range("G23").Value = "=roundup(" & rdPackingPressure3 & ",0)"
objSheet.Range("E23").Value = "=(" & rdPackingTime4 & ")"
objSheet.Range("F23").Value = "=roundup(" & rdPackingPorcentage4 & ",2)"
objSheet.Range("G23").Value = "=roundup(" & rdPackingPressure4 & ",0)"
objSheet.Range("E24").Value = "=(" & rdPackingTime5 & ")"
objSheet.Range("F24").Value = "=roundup(" & rdPackingPorcentage5 & ",2)"
objSheet.Range("G24").Value = "=roundup(" & rdPackingPressure5 & ",0)"


Set objSheet = objWB.Worksheets(4)
objSheet.Activate
objSheet.Range("G13").Formula = "=" & arrRecommendedTemps.Val(0)
objSheet.Range("H13").Formula = "=" & arrRecommendedTemps.Val(1)

' ------ PPT Report Management ------
Set objPPTApp = CreateObject("PowerPoint.Application")
Set objPPT = objPPTApp.Presentations.Open(pptTemplate,3, True)

GenerateFillSequence()
GeneratePressureatswitchover()
GenerateTemperatureatflowfront()
GeneratePressureatinjectionlocation()
GenerateTotalWeight()
GenerateDeflections()



WshShell.Popup "Informe generado", , "", 64

' ------ Code by JZR ------
Sub GenerateFillSequence()
	Set Plot = PlotMgr.FindPlotByName("Fill time")
	dim LayerManager, EntList, LayerList, LayerList1, LayerList2, LayerList3
	
	Set LayerManager = Synergy.LayerManager()
	LayerManager.ShowAllLayers 
	
	Set LayerList = LayerManager.FindLayerByName("Cooling")
	LayerManager.ToggleLayer LayerList
	Set LayerList = LayerManager.FindLayerByName("Nodes on circuits")
	LayerManager.ToggleLayer LayerList
	Set LayerList = LayerManager.FindLayerByName("Beams_2ndshot")
	LayerManager.ToggleLayer LayerList
	Set LayerList = LayerManager.FindLayerByName("Tetras_2ndshot_1")
	LayerManager.ToggleLayer LayerList
	Set LayerList = LayerManager.FindLayerByName("Tetras_2ndshot_2")
	LayerManager.ToggleLayer LayerList

	WScript.Sleep 2000
	
	Plot.Regenerate

	
	'Set LayerList = LayerManager.FindLayerByName("Runner")
	'Set LayerList1 = LayerManager.FindLayerByName("Beams on feed system")
	'Set LayerList2 = LayerManager.FindLayerByName("Tetras_1")
	'Set LayerList3 = LayerManager.FindLayerByName("Tetras_2")	
	'LayerManager.HideAllOtherLayers LayerList, LayerList1, LayerList2, LayerList3

	'Set EntList = LayerManager.CreateEntityList()
	'LayerManager.HideAllOtherLayers
	'Set LayerList = LayerManager.FindLayerByName("Beams")
	'LayerManager.HideAllOtherLayers LayerList
	'EntList.SelectFromString "Cooling "
	'LayerManager.ShowLayers EntList, False
	'EntList.SelectFromString "Beams "
	'LayerManager.ShowLayers EntList, True

	'Set LayerManager = Synergy.LayerManager()
	'Set LayerList = LayerManager.FindLayerByName("Beams")
	'LayerManager.HideAllOtherLayers LayerList
	
	Plot.SetNumberOfFrames(24)
	Dim iFrames, iPages(9), I
	Set iFrames = Synergy.CreateIntegerArray()
	iFrames.AddInteger(5)
	iFrames.AddInteger(8)
	iFrames.AddInteger(11)
	iFrames.AddInteger(14)
	iFrames.AddInteger(17)
	iFrames.AddInteger(20)
	iFrames.AddInteger(21)
	iFrames.AddInteger(22)
	iFrames.AddInteger(23)
	For I = 0 to 8
		iPages(I) = I + 7
	Next
	Dim ObjSlide, TemplateName, objShape, Plot2
	Dim l, t, h, w
	l = 36
	t = 87
	h = 470
	w = 270

	For I = 0 to iFrames.Size()-1
		Dim tmpImageName
		tmpImageName = GetTempPath & "\Fill_" & iPages(I) & ".jpg"
		Viewer.ShowPlotFrame Plot, iFrames.Val(I)
		Viewer.SaveImage tmpImageName
		objPPTApp.ActiveWindow.View.GotoSlide(iPages(I))
		Set objSlide = objPPT.Slides(iPages(I))
		' Experimentalmente la Shape(2) es el cuadro en cuestion. Si se toca la plantillas
		' hay que asegurarse de que siga siendo el mismo en todas las paginas.
		Set objShape = objSlide.Shapes(2)
		objShape.Delete
		Set objShape = objSlide.Shapes.AddPicture(tmpImageName, 0, -1, l, t, w, h)
	Next
'Generate animation
	objPPTApp.ActiveWindow.View.GotoSlide(17)
	tmpImageName = GetTempPath & "\Fill_animation" & ".gif"
	Viewer.SaveAnimation(tmpImageName)
	Set objSlide = objPPT.Slides(17)
	Set objShape = objSlide.Shapes(2)
	objShape.Delete
	Set objShape = objSlide.Shapes.AddPicture(tmpImageName, 0, -1, l, t, w, h)
	
End Sub

Function getRangeTempFlowFront()
	Dim arrResults, ID, arrIndependentValues, arrEntIDs, arrScalarData
	Set Plot = PlotMgr.FindPlotByName("Temperature at flow front")
	ID = Plot.GetDataID()
	Set arrIndependentValues = Synergy.CreateDoubleArray
	Set arrEntIDs = Synergy.CreateIntegerArray()
	Set arrScalarData = Synergy.CreateDoubleArray()
	PlotMgr.GetScalarData ID, arrIndependentValues, arrEntIDs, arrScalarData
	Dim lMin,lMax
	Call GetinMaxValueFromArray(arrScalarData, lMin, lMax)
	Set arrResults = Synergy.CreateDoubleArray()
	arrResults.AddDouble(lMin)
	arrResults.AddDouble(lMax)
	Set getRangeTempFlowFront = arrResults
End Function

Function getMaxClampForce()
	Dim OK, arrResults, ID, arrIndependentValues, I, arrValues
	getMaxClampForce = 99999
	Set Plot = PlotMgr.FindPlotByName("Clamp force:XY Plot")
	ID = Plot.GetDataID()
	Set arrIndependentValues = Synergy.CreateDoubleArray
	PlotMgr.GetIndpValues ID, arrIndependentValues
	Set arrResults = Synergy.CreateDoubleArray()
	For I = 0 To arrIndependentValues.Size()-1
		Dim arrInd
		Set arrValues = Synergy.CreateDoubleArray()
		Set arrInd = Synergy.CreateDoubleArray()
		arrInd.AddDouble(arrIndependentValues.Val(I))
		PlotMgr.GetNonMeshData ID, arrInd, arrValues
		arrResults.AddDouble(CDbl(arrValues.Val(0)))
	Next
	Dim lMin,lMax
	Call GetinMaxValueFromArray(arrResults, lMin, lMax)
	getMaxClampForce = lMax
End Function

Sub GetinMaxValueFromArray(ScalarValueArray, nMin, nMax)
	' Get Minimun and Maximum Values of an Array
	Dim ValueArray
	ValueArray = ScalarValueArray.ToVBSArray ()
	nMin = ValueArray(0)
	nMax = ValueArray(0)
	Dim nItem 
	For Each nItem in ValueArray 
		if nItem > nMax Then nMax = nItem 
		if nItem < nMin Then nMin = nItem 
	Next 
End Sub

'-----------------CODE BY MANUEL HERVAS-----------------'

Sub GeneratePressureatswitchover()
	
	Set Plot = PlotMgr.FindPlotByName("Pressure at V/P switchover")
	Viewer.ShowPlot Plot
	
	dim LayerManager, EntList, LayerList, LayerList1, LayerList2, LayerList3
	
	Set LayerManager = Synergy.LayerManager()
	LayerManager.ShowAllLayers 
	
	Set LayerList = LayerManager.FindLayerByName("Cooling")
	LayerManager.ToggleLayer LayerList
	Set LayerList = LayerManager.FindLayerByName("Nodes on circuits")
	LayerManager.ToggleLayer LayerList
	Set LayerList = LayerManager.FindLayerByName("Beams_2ndshot")
	LayerManager.ToggleLayer LayerList
	Set LayerList = LayerManager.FindLayerByName("Tetras_2ndshot_1")
	LayerManager.ToggleLayer LayerList
	Set LayerList = LayerManager.FindLayerByName("Tetras_2ndshot_2")
	LayerManager.ToggleLayer LayerList	

	WScript.Sleep 2000
	

	Dim ObjSlide, TemplateName, objShape, tmpImageName2
	Dim l, t, h, w
	l = 36
	t = 87
	h = 470
	w = 270


	tmpImageName2 = GetTempPath & "\PressureInjectionPoint" & ".jpg"
	'Viewer.ShowPlot Plot
	'WScript.Sleep 3000
	Viewer.SaveImage(tmpImageName2)
	objPPTApp.ActiveWindow.View.GotoSlide(19)
	
	Set objSlide = objPPT.Slides(19)
	Set objShape = objSlide.Shapes(3)
	objShape.Delete
	Set objShape = objSlide.Shapes.AddPicture(tmpImageName2, 0, -1, l, t, w, h)
	
	
	
	Set LayerList = LayerManager.FindLayerByName("Runner")
	LayerManager.ToggleLayer LayerList
	Set LayerList = LayerManager.FindLayerByName("Beams")
	LayerManager.ToggleLayer LayerList

	WScript.Sleep 3000
	
	'Plot.Regenerate
	
	tmpImageName2 = GetTempPath & "\PressureInjectionPoint" & ".jpg"
	
	'Viewer.ShowPlot Plot
	Viewer.SaveImage(tmpImageName2)
	objPPTApp.ActiveWindow.View.GotoSlide(19)
	
	Set objSlide = objPPT.Slides(19)
	Set objShape = objSlide.Shapes(3)
	objShape.Delete
	Set objShape = objSlide.Shapes.AddPicture(tmpImageName2, 0, -1, l, t, w, h)
	
End Sub

Sub GenerateTemperatureatflowfront()

	dim LayerManager, EntList, LayerList, LayerList1, LayerList2, LayerList3
	
	Set LayerManager = Synergy.LayerManager()
	LayerManager.ShowAllLayers 
	
	Set LayerList = LayerManager.FindLayerByName("Cooling")
	LayerManager.ToggleLayer LayerList
	Set LayerList = LayerManager.FindLayerByName("Nodes on circuits")
	LayerManager.ToggleLayer LayerList
	Set LayerList = LayerManager.FindLayerByName("Beams_2ndshot")
	LayerManager.ToggleLayer LayerList
	Set LayerList = LayerManager.FindLayerByName("Tetras_2ndshot_1")
	LayerManager.ToggleLayer LayerList
	Set LayerList = LayerManager.FindLayerByName("Tetras_2ndshot_2")
	LayerManager.ToggleLayer LayerList
	WScript.Sleep 2000
	
	' Read Melt Temperature range for First Material
	Dim I, OK, Value, lStr,Vrangomax, Vrangomin, maxV, minV
	Dim Plot, Viewer, tmpImageName2, objSlide, objShape, l,t,w,h
	Set Viewer = Synergy.Viewer()
	Set PlotMgr = Synergy.PlotManager()
	If MaterialID > 0 Then
		lStr = ""
		Set Value = Synergy.CreateDoubleArray()
		OK = GetTCodeValue(MaterialID, MaterialSubID, 1800, Value)
	End If


		Vrangomax = Value.Val(1)
		Vrangomin = Value.Val(0)

		Set Plot = PlotMgr.FindPlotByName("Temperature at flow front")


		Viewer.ShowPlot Plot
		Plot.SetScaleOption 0
		Plot.SetExtendedColor True		
		Plot.Regenerate

		maxV = round(Plot.GetMaxValue,2)
		minV = round(Plot.GetMinValue,2)

			'if maxV <= Vrangomax and minV => Vrangomin then
		
			Plot.SetScaleOption 2
			Plot.SetMaxValue Vrangomax
			Plot.SetMinValue Vrangomin
			Plot.SetExtendedColor True

			Plot.Regenerate
				
				
			tmpImageName2 = GetTempPath & "\Temperatureatflowfront" & ".jpg"
			Viewer.SaveImage(tmpImageName2)
			objPPTApp.ActiveWindow.View.GotoSlide(20)
			Set objSlide = objPPT.Slides(20)
			Set objShape = objSlide.Shapes(2)
			objShape.Delete
			Set objShape = objSlide.Shapes.AddPicture(tmpImageName2, 0, -1, l, t, w, h)	
		
	
	
			if maxV > Vrangomax then
				
				Plot.SetScaleOption 2
				Plot.SetMaxValue maxV
				Plot.SetMinValue Vrangomax
				Plot.SetExtendedColor False
				
				Plot.Regenerate
				
				tmpImageName2 = GetTempPath & "\Temperatureatflowfront" & ".jpg"
				Viewer.SaveImage(tmpImageName2)
				objPPTApp.ActiveWindow.View.GotoSlide(20)
				Set objSlide = objPPT.Slides(20)
				Set objShape = objSlide.Shapes(2)
				objShape.Delete
				Set objShape = objSlide.Shapes.AddPicture(tmpImageName2, 0, -1, l, t, w, h)
			
				MsgBox  "Maximum temperature at flow front " & maxV & " C is OVER melt temperature range recommended " & Vrangomax
				
			end if
			
			if minV < Vrangomin then
			
				Plot.SetScaleOption 2
				Plot.SetMaxValue Vrangomin
				Plot.SetMinValue minV
				Plot.SetExtendedColor False
				
				Plot.Regenerate
				tmpImageName2 = GetTempPath & "\Temperatureatflowfront" & ".jpg"
				Viewer.SaveImage(tmpImageName2)
				objPPTApp.ActiveWindow.View.GotoSlide(20)
				Set objSlide = objPPT.Slides(20)
				Set objShape = objSlide.Shapes(2)
				objShape.Delete
				Set objShape = objSlide.Shapes.AddPicture(tmpImageName2, 0, -1, l, t, w, h)
			
				MsgBox  "Minimum temperature at flow front " & minV & " C is BELOW melt temperature range recommended " & Vrangomin

			end if	
	

	

	
	
	'Set Plot = PlotMgr.FindPlotByName("Temperature at flow front")
	
	
	'Dim ObjSlide, TemplateName, objShape, tmpImageName2
	'Dim l, t, h, w
	'l = 36
	't = 87
	'h = 470
	'w = 270


	'tmpImageName2 = GetTempPath & "\Temperatureatflowfront" & ".jpg"
	'Viewer.ShowPlot Plot
	'Viewer.SaveImage(tmpImageName2)
	'objPPTApp.ActiveWindow.View.GotoSlide(20)
	
	'Set objSlide = objPPT.Slides(20)
	'Set objShape = objSlide.Shapes(2)
	'objShape.Delete
	'Set objShape = objSlide.Shapes.AddPicture(tmpImageName2, 0, -1, l, t, w, h)
	
End Sub

Sub GeneratePressureatinjectionlocation()

	Dim Plot2
	
	Set Plot = PlotMgr.FindPlotByName("Pressure at injection location:XY Plot")

	
	
	Dim ObjSlide, TemplateName, objShape, tmpImageName2, tmpImageName3, ObjSlide2, objShape2
	Dim l, t, h, w
	l = 36
	t = 87
	h = 470
	w = 270


	tmpImageName2 = GetTempPath & "\pressureinjectionlocation" & ".jpg"
	tmpImageName3 = GetTempPath & "\ClampForce" & ".jpg"
	
	Viewer.ShowPlot Plot
	
	
	Plot.SetXYPlotShowPoints False
	Plot.SetXYPlotOverlayWithMesh False
	'WScript.Sleep 2000
	Plot.Regenerate
	Viewer.SaveImage(tmpImageName2)

	'Viewer.SaveXYPlotCurveData(tmpImageName2)
	objPPTApp.ActiveWindow.View.GotoSlide(23)
	
	Set objSlide = objPPT.Slides(23)
	Set objShape = objSlide.Shapes(2)
	objShape.Delete
	Set objShape = objSlide.Shapes.AddPicture(tmpImageName2, 0, -1, l, t, w, h)
	
Set Plot2 = PlotMgr.FindPlotByName("Clamp force:XY Plot")
	
	Viewer.ShowPlot Plot2
	Plot2.SetXYPlotShowPoints False
	Plot2.SetXYPlotOverlayWithMesh False
	Plot2.Regenerate
	
	'WScript.Sleep 5000
	
	
	Viewer.SaveImage(tmpImageName3)
	
	
	Set objShape2 = objSlide.Shapes(2)
	objShape2.Delete
	Set objShape2 = objSlide.Shapes.AddPicture(tmpImageName3, 0, -1, 0, t, w, h)
	
End Sub

Sub GenerateTotalWeight()

	Dim SynergyGetter, Synergy
	On Error Resume Next
	Set SynergyGetter = GetObject(CreateObject("WScript.Shell").ExpandEnvironmentStrings("%SAInstance%"))
	On Error GoTo 0
	If (Not IsEmpty(SynergyGetter)) Then
	Set Synergy = SynergyGetter.GetSASynergy
	Else
	Set Synergy = CreateObject("synergy.Synergy")
	End If
	Set PlotMgr = Synergy.PlotManager()
	Set Viewer = Synergy.Viewer()
	Set Plot = PlotMgr.CreatePlotByName("Total part weight:XY Plot", 1)
	Viewer.ShowPlot Plot

	
	'Set Plot = PlotMgr.FindPlotByName("Total part weight:XY Plot")
	
	
	Dim ObjSlide, TemplateName, objShape, tmpImageName2
	Dim l, t, h, w
	l = 36
	t = 87
	h = 470
	w = 270


	tmpImageName2 = GetTempPath & "\Total_weight" & ".jpg"
	Viewer.ShowPlot Plot
	Plot.SetXYPlotShowPoints False
	Plot.SetXYPlotOverlayWithMesh False
	
	'WScript.Sleep 2000
	Plot.Regenerate	
	
	
	Viewer.SaveImage(tmpImageName2)
	'Viewer.SaveXYPlotCurveData(tmpImageName2)
	objPPTApp.ActiveWindow.View.GotoSlide(30)
	
	Set objSlide = objPPT.Slides(30)
	Set objShape = objSlide.Shapes(2)
	objShape.Delete
	Set objShape = objSlide.Shapes.AddPicture(tmpImageName2, 0, -1, l, t, w, h)
	
End Sub

Sub GenerateDeflections()
	
	' SET scale on "shrkVector"
	

	'MsgBox  "Don't Forget! Fix Anchor!",,WScript.ScriptName
	
	dim usrInput, shrkg, shrkVector, LayerList

	usrInput = InputBox("Shrinkage factor to use: ", "Shrinkage")
	shrkg = CDbl(usrInput)
	Set shrkVector = Synergy.CreateVector()
	shrkVector.SetXYZ shrkg, shrkg, shrkg
	
	Set LayerList = LayerManager.FindLayerByName("Tetras_2")
	LayerManager.ToggleLayer LayerList
	Set LayerList = LayerManager.FindLayerByName("Tetras_2ndshot_1")
	LayerManager.ToggleLayer LayerList

	WScript.Sleep 2000

	' SET range on "myrange"
	'usrInput = InputBox("Range for the scale (just the number, it will be converted and used both for Min and Max): ", "Results range")
	'scaleInput = CDbl(usrInput)

	'scaleRange(0)= scaleInput * -1
	'scaleRange(1)= scaleInput

		'Set Plot = PlotMgr.FindPlotByName("Deflection, all effects:Deflection")

	Dim scaleRange(1)
	Dim Value(1)
	Dim plotList(3)
	Dim PlotManager, Viewer, currplot

	plotList(0)="Deflection, all effects:Deflection"
	plotList(1)="Deflection, all effects:X Component"
	plotList(2)="Deflection, all effects:Y Component"
	plotList(3)="Deflection, all effects:Z Component"

	Set PlotManager = Synergy.PlotManager()
	Set Viewer = Synergy.Viewer()
	
	Set LayerList = LayerManager.FindLayerByName("Runner")
	LayerManager.ToggleLayer LayerList
	Set LayerList = LayerManager.FindLayerByName("Beams")
	LayerManager.ToggleLayer LayerList
	WScript.Sleep 2000

	Dim lStr,I,a, minV, maxV

	a=30

	For Each currplot in plotList
	
		Set Plot = PlotManager.FindPlotByName(currplot)
		
		a=a+1
	
		Viewer.ShowPlot Plot
	

		Plot.SetPlotMethod 1
		Plot.SetDeflectionScaleDirection 3
		Plot.SetShrinkageCompensationOption "Isotropic"
		Plot.SetShrinkageCompensationEstimatedShrinkage shrkVector

		Plot.SetScaleOption 0
		Plot.SetExtendedColor True
		Plot.Regenerate
	
		minV = Plot.GetMinValue
		maxV = Plot.GetMaxValue
		minV = (Abs(minV))
		maxV = (Abs(maxV))
		Value(0) = Round(minV,2)
		Value(1) = Round (maxV,2)

		'lStr = ""
		'For I = 0 To 1
			'lStr = lStr & Value(I) & vbCrLf
		'Next
		'MsgBox  lStr,,WScript.ScriptName
	
		If Value(1) > Value(0) then
			scaleRange(0)= Value(1) * -1
			scaleRange(1)= Value(1)
			Plot.SetScaleOption 2
			Plot.SetMaxValue scaleRange(1)
			Plot.SetMinValue scaleRange(0)
		else
			scaleRange(0)= Value(0) * -1
			scaleRange(1)= Value(0)
			Plot.SetScaleOption 2
			Plot.SetMaxValue scaleRange(1)
			Plot.SetMinValue scaleRange(0)
		
		end if
	
		'Viewer.ShowPlot Plot
		'Plot.SetPlotMethod 1
		'Plot.SetDeflectionScaleDirection 3
		'Plot.SetShrinkageCompensationOption "Isotropic"
		'Plot.SetShrinkageCompensationEstimatedShrinkage shrkVector
		
		Plot.Regenerate
		Dim ObjSlide, TemplateName, objShape, tmpImageName2
		Dim l, t, h, w
		l = 36
		t = 87
		h = 470
		w = 270


		tmpImageName2 = GetTempPath & "\Deflection" & ".jpg"
		Viewer.ShowPlot Plot
		Plot.Regenerate	
		Viewer.SaveImage(tmpImageName2)
		objPPTApp.ActiveWindow.View.GotoSlide(a)
	
		Set objSlide = objPPT.Slides(a)
		Set objShape = objSlide.Shapes(2)
		objShape.Delete
		Set objShape = objSlide.Shapes.AddPicture(tmpImageName2, 0, -1, l, t, w, h)
		
		

	Next

	
End Sub

' ------ Code by AUTODESK ------
Class Message
  Private mMSCD			' MSCD Message ID
  Private mNumString    ' Number of Strings associated with the Message
  Private mNumFloat     ' Number of Floats  Associated with the Message
  Private mStrings()    ' The Strings Associated with the Message
  Private mFloats()		' The Numerical Values Associated with the Message
  
  Public Sub SetMSCD(aMSCD)
    mMSCD = aMSCD
  End Sub
  
  Public Sub SetNumString(aNumString)
    mNumString = aNumString
  End Sub
  
  Public Sub SetNumFloat(aNumFloat)
    mNumFloat = aNumFloat
  End Sub
  
  Public Sub AddFloat(aFloat)
    mNumFloat = mNumFloat + 1
    ReDim Preserve mFloats(mNumFloat)
    mFloats(mNumFloat-1) = aFloat
  End Sub
  
  Public Sub AddString(aString)
    mNumString = mNumString + 1
    ReDim Preserve mStrings(mNumString)
    mStrings(mNumString-1) = aString
  End Sub
  
  Public Function GetMSCD()
  	GetMSCD = mMSCD
  End Function
  Public Function GetString(aIndex)
    GetString = ""
  	If aIndex >= 0 And aIndex < mNumString Then
  	   GetString = mStrings(aIndex)
  	End if
  End Function
  
  Public Function GetFloat(aIndex)
    GetFloat = ""
  	If aIndex >= 0 And aIndex < mNumFloat Then
  	   GetFloat = mFloats(aIndex)
  	End if
  End Function
  
  Public Function GetNumString()
    GetNumString = mNumString
  End Function
  
  Public Function GetNumFloat()
    GetNumFloat = mNumFloat
  End Function
  
  Private Sub Class_Initialize
    mMSCD = -1
    mNumString = 0
    mNumFloat = 0
  End Sub
End Class

Class ScreenOutput

 Private mMessages()		' Array of Messages associate with the screen output File
 Private mNumMessages		' Number of messages in the screen output file

  Public Function LoadOutputFile(aFile)
  	Const ForReading = 1
  	Dim FS
  	Set FS = CreateObject("Scripting.FileSystemObject")
  	Dim File
    Set File = FS.OpenTextFile(aFile, ForReading)
        While Not File.AtEndOfStream
    	Dim ID
    	ID = -1
       	' Read the MSCD
       	Dim Line,lenLine
		Line = File.ReadLine
		lenLine = len(Line)
		If Not File.AtEndOfStream or lenLine >= 1 Then
			ID = Line
			Dim curMessage
			Set curMessage = New Message
			curMessage.SetMSCD(ID)
			' Read the number of strings
			Line = File.ReadLine
			lenLine = len(Line)
			If Not File.AtEndOfStream or lenLine >= 1 Then
				Dim numString
				numString = Line
				' Read Strings
				Dim i
				For i = 1 To numString
					Line = File.ReadLine
					lenLine = len(Line)
					If Not File.AtEndOfStream or lenLine >= 1 Then
						CurMessage.AddString(Line)
					End if
				Next
			End if
			' Read the number of floats
			Line = File.ReadLine
			lenLine = len(Line)
			If Not File.AtEndOfStream or lenLine >= 1 Then
				Dim numFloat
				numFloat = Line
				' Read Floats
				For i = 1 To numFloat
					Line = File.ReadLine
					lenLine = len(Line)
					If Not File.AtEndOfStream or lenLine >= 1 Then
						CurMessage.AddFloat(Line)
					End if
				Next
			End If
			' Add current message to the list
			AddMessage(CurMessage)
		End If
	Wend
    File.Close
  End Function
  
  Public Sub AddMessage(aMessage)
	mNumMessages = mNumMessages + 1
   	ReDim Preserve mMessages(mNumMessages)
    Set mMessages(mNumMessages-1) = aMessage
  End Sub
  
  Public Function GetNumMessages()
    GetNumMessages = mNumMessages
  End Function
  
  Public Function GetMessage(aMSCD,aOccur)
  	Set GetMessage = Nothing
  	Dim j
  	Dim lFindInstance
  	lFindInstance = aOccur
  	If aOccur < 0 Then
  		lFindInstance = 0
  	End if
  	Dim Count
  	Count = 0
  	For j = 0 To mNumMessages-1
  		If CStr(mMessages(j).GetMSCD) = CStr(aMSCD) Then
  		   Count = Count + 1
  		   If Count >= lFindInstance Then
  		   		Set GetMessage = mMessages(j)
				Exit Function
  		   End if
  		End if
  	Next
  End Function
  
  Private Sub Class_Initialize
	mNumMessages = 0
  End Sub
End Class

Function GetTCodeDescription(ID, SubID, TCode, Value)
' Get a Tcode description
	Value = ""
	GetTCodeDescription = false
	Set PropEd = Synergy.PropertyEditor()
	Set Prop = PropEd.FindProperty(ID, SubID)
	If  Not Prop Is Nothing Then
		Dim AA
		AA = Prop.GetFirstField()
		While AA > 0
			If AA = TCode Then
				Value = Prop.FieldDescription(AA)
				GetTCodeDescription = true
			End If
			AA = Prop.GetNextField(AA)
		Wend
	End if
End Function

Function GetTCodeName(ID, TCode, Value)
' Get a Tcode description
	Value = ""
	GetTCodeName = false
	Set PropEd = Synergy.PropertyEditor()
	Value = PropEd.GetDataDEscription(ID, TCode)
	If Value <> "" Then
		GetTCodeName = true
	End if
End Function

Function GetTCodeValue(ID, SubID, TCode, Value)
' Get a Tcode Item Value   Returns itms in visible units.
	GetTCodeValue = False
	Set PropEd = Synergy.PropertyEditor()
	Set Prop = PropEd.FindProperty(ID, SubID)
	If  Not Prop Is Nothing Then
		Dim AA
		AA = Prop.GetFirstField()
		While AA > 0
			If AA = TCode Then
				Dim Values
				Set Values = Prop.FieldValues(AA)
				If Not Values Is Nothing then
					Set Value = Values
					GetTCodeValue = True
				End If
			End If
			AA = Prop.GetNextField(AA)
		Wend
	End if
End Function

Sub GetInjectionData (InjectionID, InjectionSubID, Injection2ID, Injection2SubID)
' Get the relevent Injection TSet data  from a study file
	InjectionID = 0
	InjectionSubID = 0
	Injection2ID = 0
	Injection2SubID = 0
	Dim StudyDoc, Process
	Set StudyDoc = Synergy.StudyDoc()
	Process = StudyDoc.MoldingProcess
	InjectionID = 40000
	If InStr(Process, "REACTIVE") > 0 Then
		InjectionID = 40002
	ElseIf InStr(Process, "REACTIVE") > 0 Then
		InjectionID = 40002
	ElseIf InStr(Process, "THERMOSET") > 0 Then
		InjectionID = 40002
	ElseIf InStr(Process, "MICROCHIP") > 0 Then
		InjectionID = 40002
	ElseIf InStr(Process, "UNDERFILL") > 0 Then
		InjectionID = 40002
	ElseIf InStr(Process, "RTM_OR_SRIM") > 0 Then
		InjectionID = 40002
	End If
	' The SUBID is nmost likely the last SUBID of the type required.
	' There is no programatic way in the API calls to get NDBC so so we cannot
	' be 100% sure.
	Dim PropED, Prop
	Set PropED = Synergy.PropertyEditor()
	Set Prop = PropEd.GetFirstProperty(InjectionID)
	While Not Prop Is nothing
		InjectionSubID = Prop.ID
		Set Prop = PropEd.GetNextPropertyofType(Prop)
	Wend

	' Look for a Second Location
	If (InStr(Process, "OVERMOLDING") > 0)  Or (InStr(Process, "OVER_MOLDING") > 0) Then
		Injection2ID = 40001
	ElseIf InStr(Process, "BI_INJECTION") > 0 Then
		Injection2ID = InjectionID
	ElseIf InStr(Process, "CO_INJECTION") > 0 Then
		Injection2ID = InjectionID
	End if
	If (Injection2ID > 0) Then
		Set Prop = PropEd.GetFirstProperty(Injection2ID)
		While Not Prop Is nothing
			Injection2SubID = Prop.ID
			Set Prop = PropEd.GetNextPropertyofType(Prop)
		Wend
	End If
End Sub

Sub GetMaterialData(MaterialID, MaterialSubID, Material2ID, Material2SubID)
' Get the relevent proccessing TSet data  from a study file
	MaterialID = 0
	MaterialSubID = 0
	Material2ID = 0
	Material2SubID = 0
	' Find Appropriate Injection sets.
	Dim InjectionID, InjectionSubID, Injection2ID, Injection2SubID
	Call GetInjectionData (InjectionID, InjectionSubID, Injection2ID, Injection2SubID)
	' First Material
	Dim PropED, Prop
	Set PropED = Synergy.PropertyEditor()
	Set Prop = PropEd.FindProperty(InjectionID, InjectionSubID)
	If Not Prop Is nothing Then
		Dim Field
		Field = Prop.GetFirstField()
		While Not Field = 0 And (MaterialID < 1)
			' Material reference Tcode
			If Field = 20020 Then
				Dim FieldValues
				Set FieldValues = Prop.FieldValues(Field)
				MaterialID = FieldValues.Val(0)
				MaterialSubID = FieldValues.Val(1)
			End If
			Field = Prop.GetNextField(Field)
		Wend
	End if
	'MsgBox "Material ID = " & MaterialID & " Sub ID = " & MaterialSubID

	' Second Material
	If (Injection2ID > 0) Then
		Set Prop = PropEd.FindProperty(Injection2ID, Injection2SubID)
		If Not Prop Is nothing Then
			Field = Prop.GetFirstField()
			While Not Field = 0 And (Material2ID < 1)
				' Material reference Tcode
				If Field = 20021 Then
					Set FieldValues = Prop.FieldValues(Field)
					Material2ID = FieldValues.Val(0)
					Material2SubID = FieldValues.Val(1)
				End If
				Field = Prop.GetNextField(Field)
			Wend
		End If
		'MsgBox "Second Material ID = " & Material2ID & " Sub ID = " & Material2SubID
	End if
End Sub

Function GetTempPath()
' Gets temporary folder location
  Const TemporaryFolder = 2
  Dim Temp
  Set Temp = FS.GetSpecialFolder(TemporaryFolder)
  GetTempPath = Temp.Path
End Function

'      ---- Code by MHP -----

Function AddMaterialSpecificHeatPlot(ObjSlide, MatIndex, X, Y, XSize, YSize)
'Add  ThermalSpecific  profile charts
	Set AddMaterialSpecificHeatPlot = Nothing
	Dim IsData, OK
	Dim ArrX, ArrY, Title, XLabel, YLabel, ArrXV, ArrYV, strUnit
    IsData = ReadProfileFromScreenOutput(1101, MatIndex, ArrX, ArrY)
    If IsData Then
    	'Title = GetMessageString(50061) ' Specific Heat
   		OK = ConvertSIToVisible(ArrX, "K", ArrXV, strUnit)
   		XLabel = GetMessageString(50062) ' Temperature
   		XLabel = XLabel & " " & strUnit
   		OK = ConvertSIToVisible(ArrY, "J/kg-C", ArrYV, strUnit)
   		YLabel = GetMessageString(50061) ' Specific Heat
   		YLabel = YLabel & " " & strUnit
   		Set AddMaterialSpecificHeatPlot = DrawGraphWithBorderTable(ObjSlide, ArrXV, ArrYV, Title, XLabel, YLabel, X, Y, XSize, YSize)
   	Else
   		Dim lValue
   		IsData = ReadValuesFromScreenOutput(1100, MatIndex, lValue)
   		If IsData Then
   			Dim SystemMessage
			Set SystemMessage = Synergy.SystemMessage
			Dim lStrings, lValues, lStr
			Set lStrings = Synergy.CreateStringArray()
			Set lValues = Synergy.CreateDoubleArray()
			lValues.AddDouble(lValue(0))
			lStr  = SystemMessage.GetDataMessage( 1100, lStrings, lValues, Synergy.GetUnits())
			Dim ObjText
			Set ObjText =  AddTextBox(ObjSlide, X, Y+YSize/2, XSize, YSize/2, lStr, "")
   		End if
   	End If
End Function


Function getRangeTempFlowFront()
	Dim arrResults, ID, arrIndependentValues, arrEntIDs, arrScalarData
	Set Plot = PlotMgr.FindPlotByName("Temperature at flow front")
	ID = Plot.GetDataID()
	Set arrIndependentValues = Synergy.CreateDoubleArray
	Set arrEntIDs = Synergy.CreateIntegerArray()
	Set arrScalarData = Synergy.CreateDoubleArray()
	PlotMgr.GetScalarData ID, arrIndependentValues, arrEntIDs, arrScalarData
	Dim lMin,lMax
	Call GetinMaxValueFromArray(arrScalarData, lMin, lMax)
	Set arrResults = Synergy.CreateDoubleArray()
	arrResults.AddDouble(lMin)
	arrResults.AddDouble(lMax)
	Set getRangeTempFlowFront = arrResults
End Function