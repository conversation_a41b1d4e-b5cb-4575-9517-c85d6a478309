﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlowLibrary.My.MySettings
// Assembly: HDMoldFlowLibrary, Version=2.24.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 16E5D508-B892-4B5D-A02C-2C1930D27867
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMoldFlowLibrary.dll

using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Configuration;
using System.Runtime.CompilerServices;

#nullable disable
namespace HDMoldFlowLibrary.My
{
  [CompilerGenerated]
  [GeneratedCode("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "14.0.0.0")]
  [EditorBrowsable(EditorBrowsableState.Advanced)]
  internal sealed class MySettings : ApplicationSettingsBase
  {
    private static MySettings defaultInstance = (MySettings) SettingsBase.Synchronized((SettingsBase) new MySettings());

    public static MySettings Default => MySettings.defaultInstance;
  }
}
