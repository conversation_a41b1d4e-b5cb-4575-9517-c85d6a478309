'%RunPerInstance

' ###############################################################################################################

' Created:  	2022 by H. Schuette, Code Product Solutions

'				www.code-ps.com // <EMAIL>

'

' THIS SCRIPT IS STRICTLY CONFIDENTIAL AND IS OWNED BY CODE PRODUCT SOLUTIONS B.V.

' ANY ATTEMPT TO REVERSE-ENGINEER, DECRYPT, OR MODIFY THIS SCRIPT IS NOT ALLOWED

' ANY ATTEMPT TO DISTRIBUTE, SELL, RESELL, LEASE, RENT, LOAN, TRANSFER OR SUBLICENSE THIS SCRIPT IS NOT ALLOWED

' ###############################################################################################################



' ###############################################################################################################

' ARGUMENTS:

' 0 - Moldflow version, e.g. "2021"

' ###############################################################################################################



' ###############################################################################################################

' ERROR CODES: 

' 0 - Successful.

' 1 - Could not find mfcom__get_inst_id.vbs.

' 2 - Specified version is not installed.

' 3 - Could not link to a Moldflow Session.

' ###############################################################################################################



SetLocale("en-us")



WRITE_ECHO = False



' update run directory in case script was invoked

Set objFSO = CreateObject("Scripting.FileSystemObject")

scriptDir = objFSO.GetParentFolderName(WScript.ScriptFullName)

GetInstIDScript = objFSO.BuildPath(scriptDir, "mfcom__get_inst_id.vbs")		' doesn't work with vbe !!!

If Not objFSO.FileExists(GetInstIDScript) Then 
	If WRITE_ECHO Then WScript.Echo "Exited with error code 1: Could not find Instance ID."
	WScript.Quit 1
End If


' retreive arguments

Dim boolArgs

Dim userMFVersion

boolArgs = False

If WScript.Arguments.Count > 0 Then boolArgs = True

If WScript.Arguments.Count > 0 Then userMFVersion = WScript.Arguments.Item(0)



' set default (as index in ReleaseList)

defaultMFVersion = 0

index = defaultMFVersion



Dim ReleaseList ' = ("Release Name", "Registry Key")

ReleaseList = Array(Array("Moldflow Insight 2025",	 "Moldflow Synergy\2025"), _
				Array("Moldflow Insight 2024",	 "Moldflow Synergy\2024"), _
				Array("Moldflow Insight 2023",	 "Moldflow Synergy\2023"), _
				Array("Moldflow Insight 2021.2", "Moldflow Synergy\2021R2"), _
				Array("Moldflow Insight 2021.1", "Moldflow Synergy\2021R1"), _
				Array("Moldflow Insight 2021"  , "Moldflow Synergy\2021"), _
				Array("Moldflow Insight 2019"  , "Moldflow Synergy\2019"), _
				Array("Moldflow Insight 2018.2",	 "Moldflow Synergy\2018R2"), _
				Array("Moldflow Insight 2018.1", "Moldflow Synergy\2018R1"), _
				Array("Moldflow Insight 2018"  , "Moldflow Synergy\2018")  , _
				Array("Moldflow Insight 2017.3", "Moldflow Synergy\2017R3"), _
				Array("Moldflow Insight 2017R2", "Moldflow Synergy\2017R2"), _
				Array("Moldflow Insight 2017"  , "Moldflow Synergy\2017"))


' check if version is installed and get corrected string

instVersions = GetInstalledVersions("version")



corrMFVersion = ""

If boolArgs And userMFVersion <> "" Then

	' search for exact match 

	For Each ver in instVersions

		If ver = userMFVersion Or Replace(ver, "R", ".") = userMFVersion Then

			corrMFVersion = userMFVersion

			Exit For

		End If

	Next

	' if no exact match was found (e.g. 2021.1 installed, 2021 given)

	If corrMFVersion = "" Then 

		For Each ver in instVersions

			' search for partial match 

			If InStr(ver, userMFVersion) > 0 Or InStr(Replace(ver, "R", "."), userMFVersion) > 0 Then

				corrMFVersion = userMFVersion

				Exit For

			End If

		Next

	End If

	' not found 

	If corrMFVersion = "" Then

		If WRITE_ECHO Then WScript.Echo "Exited with error code 2: Could not find version " & userMFVersion & " (found " & Join(instVersions, ", ") & ")."

		WScript.Quit 2

	End If
 

Else 

	' no arguments: use newest installed version

	corrMFVersion = instVersions(UBound(instVersions))
End If


' get correct array index

If boolArgs And userMFVersion <> "" Then

	' corrMFVersion = userMFVersion

	index = 0

	For Each dArray in ReleaseList

		If InStr(dArray(0), corrMFVersion) > 0 Or InStr(dArray(1), corrMFVersion) > 0 Then

			Exit For

		End If

		index = index + 1

	Next

	If index > UBound(ReleaseList) Then index = defaultMFVersion	' fall back on default
End If


UseRelease = ReleaseList(index) ' Choose which release to use by specifying the array index



Dim Synergy

Dim WaitingTime

Dim LaunchSynergyError

Set Synergy = LaunchSynergy

If (Synergy Is Nothing) Then

	If WRITE_ECHO Then WScript.Echo "Exited with error code 3: Failed to start Synergy."

	WScript.Quit 3





Synergy.SetUnits "Metric"



If WRITE_ECHO Then WScript.Echo "Finished successfully."

WScript.Quit



'''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''

'   Add your commands here 

'   example: open a project

' Synergy.OpenProject "E:\Projects\My AMI 2021 Projects\Proj\Proj.mpi"

'''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''



Function LaunchSynergy

	Const ReleaseRegKey1 = "HKEY_LOCAL_MACHINE\Software\Autodesk\"

	Const ReleaseRegKey2 = "\InstallLocation"

	Const MaxWaitingTime = 30000

	Const WaitingInv = 1000

	RegKey = ReleaseRegKey1 & UseRelease(1) & ReleaseRegKey2

	InstIDFile = ObjFSO.GetSpecialFolder(2) & "\InstID.tmp"		'TEMP



	SynergyExec = "bin\synergy.exe"



	Set LaunchSynergy = Nothing

	Set ObjFSO = CreateObject("Scripting.FileSystemObject")

	If ObjFSO.FileExists(InstIDFile) Then ObjFSO.DeleteFile(InstIDFile)



	On Error Resume Next

	Set ObjShell = CreateObject("WScript.Shell")

	RegVal = ObjShell.RegRead(RegKey)



	If Err.Number = 0 Then

		LaunchSynergyError = ""

		StrCommandLine = """" & RegVal & SynergyExec & """ /script """ & GetInstIDScript & """ /noshutdown"		'/silent

		Dim cmdExitCode

		cmdExitCode = ObjShell.Run(StrCommandLine)

		If cmdExitCode <> 0 Then 

			LaunchSynergyError = "GUID not printed."

			Exit Function 

		 

		WaitingTime = 0

		IsFileReady = False

		Do

			WScript.Sleep WaitingInv

			WaitingTime = WaitingTime + WaitingInv

			If ObjFSO.FileExists(InstIDFile) Then IsFileReady = True

		Loop While (WaitingTime <= MaxWaitingTime) And (Not IsFileReady)

		If IsFileReady Then

			Set ObjTextFile = ObjFSO.OpenTextFile(InstIDFile, 1)

			InstID = ObjTextFile.ReadLine

			ObjTextFile.Close

			' ObjFSO.DeleteFile(InstIDFile)

			Dim SynergyGetter

			WaitingTime = 0

			Do

				WScript.Sleep WaitingInv

				WaitingTime = WaitingTime + WaitingInv

				Set SynergyGetter = GetObject(InstID)

			Loop While (WaitingTime <= MaxWaitingTime) And (IsEmpty(SynergyGetter))

			On Error GoTo 0

			If (Not IsEmpty(SynergyGetter)) Then

				Set LaunchSynergy = SynergyGetter.GetSASynergy

			

		Else 

			LaunchSynergyError = "GUID not readable."

		

	Else 

		LaunchSynergyError = Err.Number

	

End Function



Function GetInstalledVersions(mode)

	Const HKEY_CLASSES_ROOT   = &*********

	Const HKEY_CURRENT_USER   = &*********

	Const HKEY_LOCAL_MACHINE  = &*********

	Const HKEY_USERS          = &*********



	' Remark: HKEY_CURRENT_USER not working at Mark's installation, March-24 !

	

	strKeyPath = "Software\Autodesk\Moldflow Synergy"

	If mode = "version" Then 

		GetInstalledVersions = EnumerateKeys(HKEY_LOCAL_MACHINE, "HKEY_LOCAL_MACHINE", strKeyPath, "version")

	Else 

		GetInstalledVersions = EnumerateKeys(HKEY_LOCAL_MACHINE, "HKEY_LOCAL_MACHINE", strKeyPath, "path")

	 

End Function



Function EnumerateKeys(hive, hivestr, key, mode)

	Set ObjShell = CreateObject("WScript.Shell")

	Set FS = CreateObject("Scripting.FileSystemObject")

	Set reg = GetObject("winmgmts://./root/default:StdRegProv")

	reg.EnumKey hive, key, arrSubKeys

	Dim installDirectories()

	Dim installedVersions()

	i = 0

	If Not IsNull(arrSubKeys) Then

		For Each subkey In arrSubKeys

			On Error Resume Next

			' update key   

			If hivestr = "HKEY_LOCAL_MACHINE" Then strInstallDir = "\InstallLocation"

			If hivestr = "HKEY_CURRENT_USER" Then strInstallDir = "\InstallDirectory"

			If hivestr = "HKEY_LOCAL_MACHINE" Then strEXE = "bin\synergy.exe"

			If hivestr = "HKEY_CURRENT_USER" Then strEXE = "\synergy.exe"

			RegVal = ObjShell.RegRead(hivestr & "\" & key & "\" & subkey & strInstallDir)

			If Err.Number = 0 Then

				If FS.FileExists(RegVal & strEXE) Then 

					ReDim Preserve installDirectories(i)

					ReDim Preserve installedVersions(i)

					installDirectories(i) = RegVal & strEXE

					installedVersions(i) = subkey

					i = i + 1

				

			

			On Error GoTo 0

		Next

	

	If mode = "version" Then 

		EnumerateKeys = installedVersions

	Else 

		EnumerateKeys = installDirectories

	 

End Function



Function in_array(needle, haystack)

	in_array = False

	needle = trim(needle)

	For Each hay in haystack

		If trim(hay) = needle Then

			in_array = True

			Exit For

		

	Next

End Function
