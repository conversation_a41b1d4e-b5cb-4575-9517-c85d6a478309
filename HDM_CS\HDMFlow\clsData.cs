﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.clsData
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDLog4Net;
using HDMoldFlowLibrary;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Xml;

namespace HDMoldFlow
{
  public class clsData
  {
    public static void ChangeLocale()
    {
      try
      {
        clsDefine.g_strLanguageType = clsUtill.ReadINI("Option", "Lang", clsDefine.g_fiLangCfg.FullName).ToUpper();
        LocaleControl.getInstance().SetlanguageType(clsDefine.g_strLanguageType);
        LocaleControl.getInstance().RefreshLocale(clsDefine.g_strLocaleFPath, clsDefine.g_iLanguageIndex);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsData]ChangeLocale):" + ex.Message));
      }
    }

    public static void LoadProject()
    {
      try
      {
        if (!clsDefine.g_fiProjCfg.Exists && clsDefine.g_fiProjCfgDefault.Exists)
        {
          if (!clsDefine.g_fiProjCfg.Directory.Exists)
            clsDefine.g_fiProjCfg.Directory.Create();
          clsDefine.g_fiProjCfgDefault.CopyTo(clsDefine.g_fiProjCfg.FullName);
        }
        clsDefine.g_fiProjCfg.Refresh();
        if (clsDefine.g_fiProjCfg.Exists)
        {
          string path = clsUtill.ReadINI("Project", "Path", clsDefine.g_fiProjCfg.FullName);
          if (path != "")
            clsDefine.g_diBasicProject = new DirectoryInfo(path);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsData]LoadProject):" + ex.Message));
      }
      if (clsDefine.g_diBasicProject.Exists)
        return;
      clsDefine.g_diBasicProject.Create();
    }

    public static void LoadBigData()
    {
      string empty = string.Empty;
      try
      {
        clsDefine.g_fiBigDataCfg.Refresh();
        if (clsDefine.g_fiBigDataCfg.Exists)
        {
          string path = clsUtill.ReadINI("BigData", "Path", clsDefine.g_fiBigDataCfg.FullName);
          if (path != "")
            clsDefine.g_diBigData = new DirectoryInfo(path);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsData]LoadProject):" + ex.Message));
      }
      if (clsDefine.g_diBasicProject.Exists)
        return;
      clsDefine.g_diBasicProject.Create();
    }

    public static void LoadLang()
    {
      try
      {
        if (!clsDefine.g_fiLangCfg.Exists && clsDefine.g_fiLangCfgDefault.Exists)
        {
          if (clsDefine.g_fiLangCfg.Exists)
            clsDefine.g_fiLangCfg.Delete();
          if (!clsDefine.g_fiLangCfg.Directory.Exists)
            clsDefine.g_fiLangCfg.Directory.Create();
          clsDefine.g_fiLangCfgDefault.CopyTo(clsDefine.g_fiLangCfg.FullName);
        }
        clsDefine.g_fiLangCfg.Refresh();
        if (!clsDefine.g_fiLangCfg.Exists)
          return;
        string str = clsUtill.ReadINI("Option", "Lang", clsDefine.g_fiLangCfg.FullName);
        if (str != "")
          clsDefine.g_strLanguageType = str;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsData]LoadCase):" + ex.Message));
      }
    }

    public static void LoadInjection()
    {
      clsDefine.g_dtInjectionDB = new DataTable();
      clsDefine.g_dtInjectionDB.Columns.Add("FileName");
      clsDefine.g_dtInjectionDB.Columns.Add("Name");
      clsDefine.g_dtInjectionDB.Columns.Add("Company");
      clsDefine.g_dtInjectionDB.Columns.Add("DataSource");
      clsDefine.g_dtInjectionDB.Columns.Add("LastModified");
      clsDefine.g_dtInjectionDB.Columns.Add("MaxStroke");
      clsDefine.g_dtInjectionDB.Columns.Add("MaxRate");
      clsDefine.g_dtInjectionDB.Columns.Add("ScrewDia");
      clsDefine.g_dtInjectionDB.Columns.Add("MaxPressure");
      clsDefine.g_dtInjectionDB.Columns.Add("MaxClamp");
      clsDefine.g_dtInjectionDB.Columns.Add("PreRatio");
      try
      {
        if ((!clsDefine.g_diInjectionDBCfg.Exists || clsDefine.g_diInjectionDBCfg.GetFiles("*.xml").Length == 0) && clsDefine.g_diInjectionDBCfgDefault.Exists && clsDefine.g_diInjectionDBCfgDefault.GetFiles("*.xml").Length != 0)
          clsUtill.CopyFolder(clsDefine.g_diInjectionDBCfgDefault.FullName, clsDefine.g_diInjectionDBCfg.FullName);
        foreach (FileInfo file in clsDefine.g_diInjectionDBCfg.GetFiles("*.xml"))
        {
          DataRow dataRow = clsDefine.g_dtInjectionDB.Rows.Add();
          dataRow["FileName"] = (object) Path.GetFileNameWithoutExtension(file.FullName);
          using (XmlReader reader = XmlReader.Create(file.FullName))
          {
            XmlDocument xmlDocument = new XmlDocument();
            xmlDocument.Load(reader);
            XmlElement documentElement = xmlDocument.DocumentElement;
            if (documentElement != null)
            {
              foreach (DataColumn column in (InternalDataCollectionBase) clsDefine.g_dtInjectionDB.Columns)
              {
                if (!(column.ColumnName == "FileName"))
                {
                  XmlNode xmlNode = documentElement.GetElementsByTagName(column.ColumnName)[0];
                  if (xmlNode != null)
                    dataRow[column.ColumnName] = (object) xmlNode.InnerText;
                }
              }
            }
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsData]LoadInjection):" + ex.Message));
      }
    }

    public static void LoadMaterial()
    {
      clsDefine.g_dsMaterial = new DataSet();
      try
      {
        if (!clsDefine.g_fiMaterialCfg.Exists && clsDefine.g_fiMaterialCfgDefault.Exists)
        {
          if (!clsDefine.g_fiMaterialCfg.Directory.Exists)
            clsDefine.g_fiMaterialCfg.Directory.Create();
          clsDefine.g_fiMaterialCfgDefault.CopyTo(clsDefine.g_fiMaterialCfg.FullName);
        }
        clsDefine.g_fiMaterialCfg.Refresh();
        if (!clsDefine.g_fiMaterialCfg.Exists)
          return;
        clsDefine.g_dsMaterial.Tables.Add(clsHDMFLib.GetSystemMaterial(clsDefine.g_fiMaterialCfg.FullName));
        clsDefine.g_dsMaterial.Tables.Add(clsHDMFLib.GetUserMaterial());
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsData]LoadMaterial):" + ex.Message));
      }
    }

    public static void LoadReportView()
    {
      try
      {
        DirectoryInfo directoryInfo1 = new DirectoryInfo(clsDefine.g_diCfg.FullName + "\\ReportView");
        if (directoryInfo1.Exists)
          return;
        DirectoryInfo directoryInfo2 = new DirectoryInfo(clsDefine.g_diReportViewCfgDefault.FullName);
        if (directoryInfo2.Exists)
        {
          if (!directoryInfo1.Exists)
            directoryInfo1.Create();
          clsUtill.CopyFolder(directoryInfo2.FullName, directoryInfo1.FullName);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsData]LoadReportView):" + ex.Message));
      }
    }

    public static void LoadMesh()
    {
      clsDefine.g_dicMesh = new Dictionary<string, string>();
      try
      {
        if (!clsDefine.g_fiMeshCfg.Exists && clsDefine.g_fiMeshCfgDefault.Exists)
        {
          if (!clsDefine.g_fiMeshCfg.Directory.Exists)
            clsDefine.g_fiMeshCfg.Directory.Create();
          clsDefine.g_fiMeshCfgDefault.CopyTo(clsDefine.g_fiMeshCfg.FullName);
        }
        clsDefine.g_fiMeshCfg.Refresh();
        if (!clsDefine.g_fiMeshCfg.Exists)
          return;
        string str1 = clsUtill.ReadINI("Solid", "Length", clsDefine.g_fiMeshCfg.FullName);
        if (str1 == "")
          clsDefine.g_dicMesh.Add("SolidLength", "3");
        else
          clsDefine.g_dicMesh.Add("SolidLength", str1);
        string str2 = clsUtill.ReadINI("Solid", "Scale", clsDefine.g_fiMeshCfg.FullName);
        if (str2 == "")
          clsDefine.g_dicMesh.Add("SolidScale", "0.8");
        else
          clsDefine.g_dicMesh.Add("SolidScale", str2);
        string str3 = clsUtill.ReadINI("Surface", "Length", clsDefine.g_fiMeshCfg.FullName);
        if (str3 == "")
          clsDefine.g_dicMesh.Add("SurfaceLength", "-2");
        else
          clsDefine.g_dicMesh.Add("SurfaceLength", str3);
        string str4 = clsUtill.ReadINI("3D", "TetraLayer", clsDefine.g_fiMeshCfg.FullName);
        if (str4 == "")
          clsDefine.g_dicMesh.Add("TetraLayer", "10");
        else
          clsDefine.g_dicMesh.Add("TetraLayer", str4);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsData]LoadMesh):" + ex.Message));
      }
    }

    public static void LoadMeshStat()
    {
      clsDefine.g_dicMeshStat = new Dictionary<string, string>();
      try
      {
        if (!clsDefine.g_fiMeshStatCfg.Exists && clsDefine.g_fiMeshStatCfgDefault.Exists)
        {
          if (!clsDefine.g_fiMeshStatCfg.Directory.Exists)
            clsDefine.g_fiMeshStatCfg.Directory.Create();
          clsDefine.g_fiMeshStatCfgDefault.CopyTo(clsDefine.g_fiMeshStatCfg.FullName);
        }
        clsDefine.g_fiMeshStatCfg.Refresh();
        if (!clsDefine.g_fiMeshStatCfg.Exists)
          return;
        string str = clsUtill.ReadINI("AspectRatio", "Value", clsDefine.g_fiMeshStatCfg.FullName);
        if (str == "")
          clsDefine.g_dicMeshStat.Add("Value", "20");
        else
          clsDefine.g_dicMeshStat.Add("Value", str);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsData]LoadMeshStat):" + ex.Message));
      }
    }

    public static void LoadRotate()
    {
      try
      {
        clsDefine.g_fiRotateCfg.Refresh();
        clsDefine.g_dtRotate = new DataTable();
        clsDefine.g_dtRotate.Columns.Add("Section");
        clsDefine.g_dtRotate.Columns.Add("Direction1");
        clsDefine.g_dtRotate.Columns.Add("Value1");
        clsDefine.g_dtRotate.Columns.Add("Direction2");
        clsDefine.g_dtRotate.Columns.Add("Value2");
        clsDefine.g_dtRotate.Columns.Add("Direction3");
        clsDefine.g_dtRotate.Columns.Add("Value3");
        if (!clsDefine.g_fiRotateCfg.Exists)
          return;
        clsDefine.g_dtRotate = clsUtill.GetINIData(clsDefine.g_fiRotateCfg.FullName);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsData]LoadRotate):" + ex.Message));
      }
    }

    public static void LoadGate()
    {
      clsDefine.g_dicGate = new Dictionary<string, string>();
      try
      {
        if (!clsDefine.g_fiGateCfg.Exists && clsDefine.g_fiGateCfgDefault.Exists)
        {
          if (!clsDefine.g_fiGateCfg.Directory.Exists)
            clsDefine.g_fiGateCfg.Directory.Create();
          clsDefine.g_fiGateCfgDefault.CopyTo(clsDefine.g_fiGateCfg.FullName);
        }
        clsDefine.g_fiGateCfg.Refresh();
        if (!clsDefine.g_fiGateCfg.Exists)
          return;
        string str1 = clsUtill.ReadINI("Pin", "Diameter", clsDefine.g_fiGateCfg.FullName);
        if (str1 == "")
          clsDefine.g_dicGate.Add("PinDiameter", "5");
        else
          clsDefine.g_dicGate.Add("PinDiameter", str1);
        string str2 = clsUtill.ReadINI("Pin", "Length1", clsDefine.g_fiGateCfg.FullName);
        if (str2 == "")
          clsDefine.g_dicGate.Add("PinLength1", "3");
        else
          clsDefine.g_dicGate.Add("PinLength1", str2);
        string str3 = clsUtill.ReadINI("Pin", "Length2", clsDefine.g_fiGateCfg.FullName);
        if (str3 == "")
          clsDefine.g_dicGate.Add("PinLength2", "1.5");
        else
          clsDefine.g_dicGate.Add("PinLength2", str3);
        string str4 = clsUtill.ReadINI("Pin", "Length3", clsDefine.g_fiGateCfg.FullName);
        if (str4 == "")
          clsDefine.g_dicGate.Add("PinLength3", "3");
        else
          clsDefine.g_dicGate.Add("PinLength3", str4);
        string str5 = clsUtill.ReadINI("Pin", "Length4", clsDefine.g_fiGateCfg.FullName);
        if (str5 == "")
          clsDefine.g_dicGate.Add("PinLength4", "1.5");
        else
          clsDefine.g_dicGate.Add("PinLength4", str5);
        string str6 = clsUtill.ReadINI("Side", "Length", clsDefine.g_fiGateCfg.FullName);
        if (str6 == "")
          clsDefine.g_dicGate.Add("SideLength", "7");
        else
          clsDefine.g_dicGate.Add("SideLength", str6);
        string str7 = clsUtill.ReadINI("Side", "Diameter1", clsDefine.g_fiGateCfg.FullName);
        if (str7 == "")
          clsDefine.g_dicGate.Add("SideDiameter1", "10");
        else
          clsDefine.g_dicGate.Add("SideDiameter1", str7);
        string str8 = clsUtill.ReadINI("Side", "Diameter2", clsDefine.g_fiGateCfg.FullName);
        if (str8 == "")
          clsDefine.g_dicGate.Add("SideDiameter2", "3");
        else
          clsDefine.g_dicGate.Add("SideDiameter2", str8);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsData]LoadGate):" + ex.Message));
      }
    }

    public static void LoadValve()
    {
      clsDefine.g_dicValve = new Dictionary<string, string>();
      try
      {
        if (!clsDefine.g_fiValveCfg.Exists && clsDefine.g_fiValveCfgDefault.Exists)
        {
          if (!clsDefine.g_fiValveCfg.Directory.Exists)
            clsDefine.g_fiValveCfg.Directory.Create();
          clsDefine.g_fiValveCfgDefault.CopyTo(clsDefine.g_fiValveCfg.FullName);
        }
        clsDefine.g_fiValveCfg.Refresh();
        if (!clsDefine.g_fiValveCfg.Exists)
          return;
        string str = clsUtill.ReadINI("Valve", "VavClose", clsDefine.g_fiValveCfg.FullName);
        if (str == "")
          clsDefine.g_dicValve.Add("VavClose", "15");
        else
          clsDefine.g_dicValve.Add("VavClose", str);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsData]LoadValve):" + ex.Message));
      }
    }

    public static void LoadRunnerDB()
    {
      clsDefine.g_dtRunnerDB = new DataTable();
      clsDefine.g_dtRunnerDB.Columns.Add("Name");
      clsDefine.g_dtRunnerDB.Columns.Add("Company");
      clsDefine.g_dtRunnerDB.Columns.Add("Item");
      clsDefine.g_dtRunnerDB.Columns.Add("Option");
      clsDefine.g_dtRunnerDB.Columns.Add("OccNumber");
      clsDefine.g_dtRunnerDB.Columns.Add("Pin_System");
      for (int index = 0; index < 4; ++index)
      {
        clsDefine.g_dtRunnerDB.Columns.Add("Pin_GateDim1_G" + (object) (index + 1));
        clsDefine.g_dtRunnerDB.Columns.Add("Pin_GateDim2_G" + (object) (index + 1));
        clsDefine.g_dtRunnerDB.Columns.Add("Pin_RunnerLength_G" + (object) (index + 1));
        clsDefine.g_dtRunnerDB.Columns.Add("Pin_RunnerDim1_G" + (object) (index + 1));
        clsDefine.g_dtRunnerDB.Columns.Add("Pin_RunnerDim2_G" + (object) (index + 1));
      }
      for (int index = 0; index < 4; ++index)
      {
        clsDefine.g_dtRunnerDB.Columns.Add("Side_GateType1_G" + (object) (index + 1));
        clsDefine.g_dtRunnerDB.Columns.Add("Side_GateType2_G" + (object) (index + 1));
        clsDefine.g_dtRunnerDB.Columns.Add("Side_GateLength_G" + (object) (index + 1));
        clsDefine.g_dtRunnerDB.Columns.Add("Side_GateDim1_G" + (object) (index + 1));
        clsDefine.g_dtRunnerDB.Columns.Add("Side_GateDim2_G" + (object) (index + 1));
        clsDefine.g_dtRunnerDB.Columns.Add("Side_GateDim3_G" + (object) (index + 1));
        clsDefine.g_dtRunnerDB.Columns.Add("Side_GateDim4_G" + (object) (index + 1));
        clsDefine.g_dtRunnerDB.Columns.Add("Side_RunnerType_G" + (object) (index + 1));
        clsDefine.g_dtRunnerDB.Columns.Add("Side_RunnerLength_G" + (object) (index + 1));
        clsDefine.g_dtRunnerDB.Columns.Add("Side_RunnerDim1_G" + (object) (index + 1));
        clsDefine.g_dtRunnerDB.Columns.Add("Side_RunnerDim2_G" + (object) (index + 1));
        clsDefine.g_dtRunnerDB.Columns.Add("Side_RunnerDim3_G" + (object) (index + 1));
        clsDefine.g_dtRunnerDB.Columns.Add("Side_RunnerDim4_G" + (object) (index + 1));
      }
      clsDefine.g_dtRunnerDB.Columns.Add("Hor_Stage");
      clsDefine.g_dtRunnerDB.Columns.Add("Hor_Two_Type");
      clsDefine.g_dtRunnerDB.Columns.Add("Hor_Two_Dia");
      clsDefine.g_dtRunnerDB.Columns.Add("Hor_Two_Length");
      clsDefine.g_dtRunnerDB.Columns.Add("Hor_Two_Dir");
      clsDefine.g_dtRunnerDB.Columns.Add("Hor_Two_Dim1");
      clsDefine.g_dtRunnerDB.Columns.Add("Hor_Two_Dim2");
      clsDefine.g_dtRunnerDB.Columns.Add("Hor_Two_Angle");
      clsDefine.g_dtRunnerDB.Columns.Add("Hor_Two_CenterTol");
      clsDefine.g_dtRunnerDB.Columns.Add("Hor_Two_Inter");
      clsDefine.g_dtRunnerDB.Columns.Add("Hor_Three_Quadrant");
      clsDefine.g_dtRunnerDB.Columns.Add("Hor_Three_Type");
      clsDefine.g_dtRunnerDB.Columns.Add("Hor_Three_Length");
      clsDefine.g_dtRunnerDB.Columns.Add("Hor_Three_Dia");
      clsDefine.g_dtRunnerDB.Columns.Add("Hor_Three_CenterTol");
      clsDefine.g_dtRunnerDB.Columns.Add("Sprue_Type");
      clsDefine.g_dtRunnerDB.Columns.Add("Sprue_Length");
      clsDefine.g_dtRunnerDB.Columns.Add("Sprue_Dim1");
      clsDefine.g_dtRunnerDB.Columns.Add("Sprue_Dim2");
      try
      {
        if ((!clsDefine.g_diRunnerDBCfg.Exists || clsDefine.g_diRunnerDBCfg.GetFiles("*.xml").Length == 0) && clsDefine.g_diRunnerDBCfgDefault.Exists && clsDefine.g_diRunnerDBCfgDefault.GetFiles("*.xml").Length != 0)
          clsUtill.CopyFolder(clsDefine.g_diRunnerDBCfgDefault.FullName, clsDefine.g_diRunnerDBCfg.FullName);
        clsDefine.g_diRunnerDBCfg.Refresh();
        if (!clsDefine.g_diRunnerDBCfg.Exists)
          return;
        foreach (FileInfo file in clsDefine.g_diRunnerDBCfg.GetFiles("*.xml"))
        {
          DataRow dataRow = clsDefine.g_dtRunnerDB.Rows.Add();
          dataRow["Name"] = (object) Path.GetFileNameWithoutExtension(file.FullName);
          string[] strArray = dataRow["Name"].ToString().Split('_');
          dataRow["Company"] = (object) strArray[0];
          dataRow["Item"] = (object) strArray[1];
          dataRow["Option"] = (object) strArray[2];
          using (XmlReader reader = XmlReader.Create(file.FullName))
          {
            XmlDocument xmlDocument = new XmlDocument();
            xmlDocument.Load(reader);
            XmlElement documentElement = xmlDocument.DocumentElement;
            if (documentElement != null)
            {
              XmlElement xmlElement1 = (XmlElement) documentElement.GetElementsByTagName("Pin")[0];
              XmlNode xmlNode1 = xmlElement1.GetElementsByTagName("SysType")[0];
              dataRow["Pin_System"] = xmlNode1 != null ? (object) xmlNode1.InnerText : (object) "0";
              for (int index = 0; index < 4; ++index)
              {
                XmlElement xmlElement2 = (XmlElement) xmlElement1.GetElementsByTagName("Group" + (object) (index + 1))[0];
                if (xmlElement2 != null)
                {
                  XmlNode xmlNode2 = xmlElement2.GetElementsByTagName("Gate")[0];
                  if (xmlNode2 != null)
                  {
                    XmlNode attribute1 = (XmlNode) xmlNode2.Attributes["Dim1"];
                    if (attribute1 == null)
                      dataRow["Pin_GateDim1_G" + (object) (index + 1)] = (object) "";
                    else
                      dataRow["Pin_GateDim1_G" + (object) (index + 1)] = (object) attribute1.Value;
                    XmlNode attribute2 = (XmlNode) xmlNode2.Attributes["Dim2"];
                    if (attribute2 == null)
                      dataRow["Pin_GateDim2_G" + (object) (index + 1)] = (object) "";
                    else
                      dataRow["Pin_GateDim2_G" + (object) (index + 1)] = (object) attribute2.Value;
                  }
                  XmlNode xmlNode3 = xmlElement2.GetElementsByTagName("Runner")[0];
                  if (xmlNode3 != null)
                  {
                    XmlNode attribute3 = (XmlNode) xmlNode3.Attributes["Length"];
                    if (attribute3 == null)
                      dataRow["Pin_RunnerLength_G" + (object) (index + 1)] = (object) "";
                    else
                      dataRow["Pin_RunnerLength_G" + (object) (index + 1)] = (object) attribute3.Value;
                    XmlNode attribute4 = (XmlNode) xmlNode3.Attributes["Dim1"];
                    if (attribute4 == null)
                      dataRow["Pin_RunnerDim1_G" + (object) (index + 1)] = (object) "";
                    else
                      dataRow["Pin_RunnerDim1_G" + (object) (index + 1)] = (object) attribute4.Value;
                    XmlNode attribute5 = (XmlNode) xmlNode3.Attributes["Dim2"];
                    if (attribute5 == null)
                      dataRow["Pin_RunnerDim2_G" + (object) (index + 1)] = (object) "";
                    else
                      dataRow["Pin_RunnerDim2_G" + (object) (index + 1)] = (object) attribute5.Value;
                  }
                }
              }
              XmlElement xmlElement3 = (XmlElement) documentElement.GetElementsByTagName("Side")[0];
              for (int index = 0; index < 4; ++index)
              {
                XmlElement xmlElement4 = (XmlElement) xmlElement3.GetElementsByTagName("Group" + (object) (index + 1))[0];
                if (xmlElement4 != null)
                {
                  XmlNode xmlNode4 = xmlElement4.GetElementsByTagName("Gate")[0];
                  if (xmlNode4 != null)
                  {
                    XmlNode attribute6 = (XmlNode) xmlNode4.Attributes["Type1"];
                    if (attribute6 == null)
                      dataRow["Side_GateType1_G" + (object) (index + 1)] = (object) "";
                    else
                      dataRow["Side_GateType1_G" + (object) (index + 1)] = (object) attribute6.Value;
                    XmlNode attribute7 = (XmlNode) xmlNode4.Attributes["Type2"];
                    if (attribute7 == null)
                      dataRow["Side_GateType2_G" + (object) (index + 1)] = (object) "";
                    else
                      dataRow["Side_GateType2_G" + (object) (index + 1)] = (object) attribute7.Value;
                    XmlNode attribute8 = (XmlNode) xmlNode4.Attributes["Length"];
                    if (attribute8 == null)
                      dataRow["Side_GateLength_G" + (object) (index + 1)] = (object) "";
                    else
                      dataRow["Side_GateLength_G" + (object) (index + 1)] = (object) attribute8.Value;
                    XmlNode attribute9 = (XmlNode) xmlNode4.Attributes["Dim1"];
                    if (attribute9 == null)
                      dataRow["Side_GateDim1_G" + (object) (index + 1)] = (object) "";
                    else
                      dataRow["Side_GateDim1_G" + (object) (index + 1)] = (object) attribute9.Value;
                    XmlNode attribute10 = (XmlNode) xmlNode4.Attributes["Dim2"];
                    if (attribute10 == null)
                      dataRow["Side_GateDim2_G" + (object) (index + 1)] = (object) "";
                    else
                      dataRow["Side_GateDim2_G" + (object) (index + 1)] = (object) attribute10.Value;
                    XmlNode attribute11 = (XmlNode) xmlNode4.Attributes["Dim3"];
                    if (attribute11 == null)
                      dataRow["Side_GateDim3_G" + (object) (index + 1)] = (object) "";
                    else
                      dataRow["Side_GateDim3_G" + (object) (index + 1)] = (object) attribute11.Value;
                    XmlNode attribute12 = (XmlNode) xmlNode4.Attributes["Dim4"];
                    if (attribute12 == null)
                      dataRow["Sid_GateDim4_G" + (object) (index + 1)] = (object) "";
                    else
                      dataRow["Side_GateDim4_G" + (object) (index + 1)] = (object) attribute12.Value;
                  }
                  XmlNode xmlNode5 = xmlElement4.GetElementsByTagName("Runner")[0];
                  if (xmlNode5 != null)
                  {
                    XmlNode attribute13 = (XmlNode) xmlNode5.Attributes["Type"];
                    if (attribute13 == null)
                      dataRow["Side_RunnerType_G" + (object) (index + 1)] = (object) "";
                    else
                      dataRow["Side_RunnerType_G" + (object) (index + 1)] = (object) attribute13.Value;
                    XmlNode attribute14 = (XmlNode) xmlNode5.Attributes["Length"];
                    if (attribute14 == null)
                      dataRow["Side_RunnerLength_G" + (object) (index + 1)] = (object) "";
                    else
                      dataRow["Side_RunnerLength_G" + (object) (index + 1)] = (object) attribute14.Value;
                    XmlNode attribute15 = (XmlNode) xmlNode5.Attributes["Dim1"];
                    if (attribute15 == null)
                      dataRow["Side_RunnerDim1_G" + (object) (index + 1)] = (object) "";
                    else
                      dataRow["Side_RunnerDim1_G" + (object) (index + 1)] = (object) attribute15.Value;
                    XmlNode attribute16 = (XmlNode) xmlNode5.Attributes["Dim2"];
                    if (attribute16 == null)
                      dataRow["Side_RunnerDim2_G" + (object) (index + 1)] = (object) "";
                    else
                      dataRow["Side_RunnerDim2_G" + (object) (index + 1)] = (object) attribute16.Value;
                    XmlNode attribute17 = (XmlNode) xmlNode5.Attributes["Dim3"];
                    if (attribute17 == null)
                      dataRow["Side_RunnerDim3_G" + (object) (index + 1)] = (object) "";
                    else
                      dataRow["Side_RunnerDim3_G" + (object) (index + 1)] = (object) attribute17.Value;
                    XmlNode attribute18 = (XmlNode) xmlNode5.Attributes["Dim4"];
                    if (attribute18 == null)
                      dataRow["Side_RunnerDim4_G" + (object) (index + 1)] = (object) "";
                    else
                      dataRow["Side_RunnerDim4_G" + (object) (index + 1)] = (object) attribute18.Value;
                  }
                }
              }
              XmlElement xmlElement5 = (XmlElement) documentElement.GetElementsByTagName("Horizon")[0];
              XmlNode attribute19 = (XmlNode) xmlElement5.Attributes["Stage"];
              dataRow["Hor_Stage"] = attribute19 != null ? (object) attribute19.Value : (object) "";
              XmlNode xmlNode6 = xmlElement5.GetElementsByTagName("Two")[0];
              if (xmlNode6 != null)
              {
                XmlNode attribute20 = (XmlNode) xmlNode6.Attributes["Type"];
                dataRow["Hor_Two_Type"] = attribute20 != null ? (object) attribute20.Value : (object) "";
                XmlNode attribute21 = (XmlNode) xmlNode6.Attributes["Dia"];
                dataRow["Hor_Two_Dia"] = attribute21 != null ? (object) attribute21.Value : (object) "";
                XmlNode attribute22 = (XmlNode) xmlNode6.Attributes["Length"];
                dataRow["Hor_Two_Length"] = attribute22 != null ? (object) attribute22.Value : (object) "";
                XmlNode attribute23 = (XmlNode) xmlNode6.Attributes["Dir"];
                dataRow["Hor_Two_Dir"] = attribute23 != null ? (object) attribute23.Value : (object) "0";
                XmlNode attribute24 = (XmlNode) xmlNode6.Attributes["Dim1"];
                dataRow["Hor_Two_Dim1"] = attribute24 != null ? (object) attribute24.Value : (object) "";
                XmlNode attribute25 = (XmlNode) xmlNode6.Attributes["Dim2"];
                dataRow["Hor_Two_Dim2"] = attribute25 != null ? (object) attribute25.Value : (object) "";
                XmlNode attribute26 = (XmlNode) xmlNode6.Attributes["Angle"];
                dataRow["Hor_Two_Angle"] = attribute26 != null ? (object) attribute26.Value : (object) "";
                XmlNode attribute27 = (XmlNode) xmlNode6.Attributes["CenterTol"];
                dataRow["Hor_Two_CenterTol"] = attribute27 != null ? (object) attribute27.Value : (object) "";
                XmlNode attribute28 = (XmlNode) xmlNode6.Attributes["Inter"];
                dataRow["Hor_Two_Inter"] = attribute28 != null ? (object) attribute28.Value : (object) "";
              }
              XmlNode xmlNode7 = xmlElement5.GetElementsByTagName("Three")[0];
              if (xmlNode7 != null)
              {
                XmlNode attribute29 = (XmlNode) xmlNode7.Attributes["Quadrant"];
                dataRow["Hor_Three_Quadrant"] = attribute29 != null ? (object) attribute29.Value : (object) "";
                XmlNode attribute30 = (XmlNode) xmlNode7.Attributes["Type"];
                dataRow["Hor_Three_Type"] = attribute30 != null ? (object) attribute30.Value : (object) "";
                XmlNode attribute31 = (XmlNode) xmlNode7.Attributes["Length"];
                dataRow["Hor_Three_Length"] = attribute31 != null ? (object) attribute31.Value : (object) "";
                XmlNode attribute32 = (XmlNode) xmlNode7.Attributes["Dia"];
                dataRow["Hor_Three_Dia"] = attribute32 != null ? (object) attribute32.Value : (object) "";
                XmlNode attribute33 = (XmlNode) xmlNode7.Attributes["CenterTol"];
                dataRow["Hor_Three_CenterTol"] = attribute33 != null ? (object) attribute33.Value : (object) "";
              }
              XmlElement xmlElement6 = (XmlElement) documentElement.GetElementsByTagName("OccurenceNumber")[0];
              dataRow["OccNumber"] = (object) xmlElement6.InnerText;
              XmlElement xmlElement7 = (XmlElement) documentElement.GetElementsByTagName("Sprue")[0];
              XmlNode attribute34 = (XmlNode) xmlElement7.Attributes["Type"];
              dataRow["Sprue_Type"] = attribute34 != null ? (object) attribute34.Value : (object) "";
              XmlNode attribute35 = (XmlNode) xmlElement7.Attributes["Length"];
              dataRow["Sprue_Length"] = attribute35 != null ? (object) attribute35.Value : (object) "";
              XmlNode attribute36 = (XmlNode) xmlElement7.Attributes["Dim1"];
              dataRow["Sprue_Dim1"] = attribute36 != null ? (object) attribute36.Value : (object) "";
              XmlNode attribute37 = (XmlNode) xmlElement7.Attributes["Dim2"];
              dataRow["Sprue_Dim2"] = attribute37 != null ? (object) attribute37.Value : (object) "";
            }
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsData]LoadRunnerDB):" + ex.Message));
      }
    }

    public static void LoadInputDB()
    {
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      clsDefine.g_dtInputDB = new DataTable();
      clsDefine.g_dtInputDB.Columns.Add("Name");
      clsDefine.g_dtInputDB.Columns.Add("Company");
      clsDefine.g_dtInputDB.Columns.Add("Item");
      clsDefine.g_dtInputDB.Columns.Add("Option");
      clsDefine.g_dtInputDB.Columns.Add("Data_FillTime");
      clsDefine.g_dtInputDB.Columns.Add("Data_VPSwitchOver");
      clsDefine.g_dtInputDB.Columns.Add("Data_PackingNumber");
      clsDefine.g_dtInputDB.Columns.Add("Data_CoolingTime");
      clsDefine.g_dtInputDB.Columns.Add("Data_CoolingInletTemperature");
      clsDefine.g_dtInputDB.Columns.Add("Data_MeltTemperature");
      clsDefine.g_dtInputDB.Columns.Add("Data_MoldTemperature");
      clsDefine.g_dtInputDB.Columns.Add("Data_GateNumber");
      clsDefine.g_dtInputDB.Columns.Add("Mat_MaterialID");
      clsDefine.g_dtInputDB.Columns.Add("Mat_Company");
      clsDefine.g_dtInputDB.Columns.Add("Mat_TradeName");
      clsDefine.g_dtInputDB.Columns.Add("Mat_Type");
      clsDefine.g_dtInputDB.Columns.Add("Mat_Density");
      clsDefine.g_dtInputDB.Columns.Add("Result_DeflectionAll");
      clsDefine.g_dtInputDB.Columns.Add("Result_VolumetricShrinkage");
      clsDefine.g_dtInputDB.Columns.Add("Result_Sinkmark");
      clsDefine.g_dtInputDB.Columns.Add("Result_TemperatureAtFlowFront");
      clsDefine.g_dtInputDB.Columns.Add("Result_CycleTime");
      clsDefine.g_dtInputDB.Columns.Add("Result_InjectionPressure");
      clsDefine.g_dtInputDB.Columns.Add("Result_Weight");
      clsDefine.g_dtInputDB.Columns.Add("Result_ClampForce");
      clsDefine.g_dtInputDB.Columns.Add("Option_ValveOption");
      clsDefine.g_dtInputDB.Columns.Add("Option_LoopNum");
      clsDefine.g_dtInputDB.Columns.Add("Option_DeflectionLimit");
      clsDefine.g_dtInputDB.Columns.Add("Option_ClampForceLimit");
      clsDefine.g_dtInputDB.Columns.Add("Option_InjectionPressureLimit");
      clsDefine.g_dtInputDB.Columns.Add("Analysis_Type");
      try
      {
        if ((!clsDefine.g_diInputDBCfg.Exists || clsDefine.g_diInputDBCfg.GetFiles("*.xml").Length == 0) && clsDefine.g_diInputDBCfgDefault.Exists && clsDefine.g_diInputDBCfgDefault.GetFiles("*.xml").Length != 0)
          clsUtill.CopyFolder(clsDefine.g_diInputDBCfgDefault.FullName, clsDefine.g_diInputDBCfg.FullName);
        clsDefine.g_diInputDBCfg.Refresh();
        if (!clsDefine.g_diInputDBCfg.Exists)
          return;
        foreach (FileInfo file in clsDefine.g_diInputDBCfg.GetFiles("*.xml"))
        {
          DataRow dataRow = clsDefine.g_dtInputDB.Rows.Add();
          dataRow["Name"] = (object) Path.GetFileNameWithoutExtension(file.FullName);
          string[] strArray = dataRow["Name"].ToString().Split('_');
          dataRow["Company"] = (object) strArray[0];
          dataRow["Item"] = (object) strArray[1];
          dataRow["Option"] = (object) strArray[2];
          using (XmlReader reader = XmlReader.Create(file.FullName))
          {
            XmlDocument xmlDocument = new XmlDocument();
            xmlDocument.Load(reader);
            XmlElement documentElement = xmlDocument.DocumentElement;
            if (documentElement != null)
            {
              XmlElement xmlElement1 = (XmlElement) documentElement.GetElementsByTagName("Data")[0];
              XmlNode xmlNode1 = xmlElement1.GetElementsByTagName("FillTime")[0];
              if (xmlNode1 != null)
              {
                string str1 = xmlNode1.Attributes["Min"].Value;
                string str2 = xmlNode1.Attributes["Max"].Value;
                dataRow["Data_FillTime"] = (object) (str1 + "," + str2);
              }
              XmlNode xmlNode2 = xmlElement1.GetElementsByTagName("VPSwitchOver")[0];
              if (xmlNode2 != null)
              {
                string str3 = xmlNode2.Attributes["Min"].Value;
                string str4 = xmlNode2.Attributes["Max"].Value;
                dataRow["Data_VPSwitchOver"] = (object) (str3 + "," + str4);
              }
              XmlNode xmlNode3 = xmlElement1.GetElementsByTagName("PackingNumber")[0];
              if (xmlNode3 != null)
              {
                string str5 = xmlNode3.Attributes["Min"].Value;
                string str6 = xmlNode3.Attributes["Max"].Value;
                dataRow["Data_PackingNumber"] = (object) (str5 + "," + str6);
              }
              XmlNode xmlNode4 = xmlElement1.GetElementsByTagName("CoolingTime")[0];
              if (xmlNode4 != null)
              {
                string str7 = xmlNode4.Attributes["Min"].Value;
                string str8 = xmlNode4.Attributes["Max"].Value;
                dataRow["Data_CoolingTime"] = (object) (str7 + "," + str8);
              }
              XmlNode xmlNode5 = xmlElement1.GetElementsByTagName("CoolingInletTemp")[0];
              if (xmlNode5 != null)
              {
                string str9 = xmlNode5.Attributes["Min"].Value;
                string str10 = xmlNode5.Attributes["Max"].Value;
                dataRow["Data_CoolingInletTemperature"] = (object) (str9 + "," + str10);
              }
              XmlNode xmlNode6 = xmlElement1.GetElementsByTagName("MeltTemperature")[0];
              if (xmlNode6 != null)
              {
                string str11 = xmlNode6.Attributes["Min"].Value;
                string str12 = xmlNode6.Attributes["Max"].Value;
                dataRow["Data_MeltTemperature"] = (object) (str11 + "," + str12);
              }
              XmlNode xmlNode7 = xmlElement1.GetElementsByTagName("MoldTemperature")[0];
              if (xmlNode7 != null)
              {
                string str13 = xmlNode7.Attributes["Min"].Value;
                string str14 = xmlNode7.Attributes["Max"].Value;
                dataRow["Data_MoldTemperature"] = (object) (str13 + "," + str14);
              }
              XmlNode xmlNode8 = xmlElement1.GetElementsByTagName("GateNumber")[0];
              if (xmlNode8 != null)
              {
                string str15 = xmlNode8.Attributes["Min"].Value;
                string str16 = xmlNode8.Attributes["Max"].Value;
                dataRow["Data_GateNumber"] = (object) (str15 + "," + str16);
              }
              XmlElement xmlElement2 = (XmlElement) documentElement.GetElementsByTagName("Material")[0];
              XmlNode xmlNode9 = xmlElement2.GetElementsByTagName("MaterialID")[0];
              if (xmlNode9 != null)
                dataRow["Mat_MaterialID"] = (object) xmlNode9.InnerText;
              XmlNode xmlNode10 = xmlElement2.GetElementsByTagName("Company")[0];
              if (xmlNode10 != null)
                dataRow["Mat_Company"] = (object) xmlNode10.InnerText;
              XmlNode xmlNode11 = xmlElement2.GetElementsByTagName("TradeName")[0];
              if (xmlNode11 != null)
                dataRow["Mat_TradeName"] = (object) xmlNode11.InnerText;
              XmlNode xmlNode12 = xmlElement2.GetElementsByTagName("Type")[0];
              if (xmlNode12 != null)
                dataRow["Mat_Type"] = (object) xmlNode12.InnerText;
              XmlNode xmlNode13 = xmlElement2.GetElementsByTagName("Density")[0];
              if (xmlNode13 != null)
                dataRow["Mat_Density"] = (object) xmlNode13.InnerText;
              XmlElement xmlElement3 = (XmlElement) documentElement.GetElementsByTagName("Result")[0];
              XmlNode xmlNode14 = xmlElement3.GetElementsByTagName("DeflectionAll")[0];
              if (xmlNode14 != null)
              {
                string str = xmlNode14.Attributes["Priority"].Value;
                dataRow["Result_DeflectionAll"] = (object) str;
              }
              XmlNode xmlNode15 = xmlElement3.GetElementsByTagName("VolumetricShrinkage")[0];
              if (xmlNode15 != null)
              {
                string str = xmlNode15.Attributes["Priority"].Value;
                dataRow["Result_VolumetricShrinkage"] = (object) str;
              }
              XmlNode xmlNode16 = xmlElement3.GetElementsByTagName("Sinkmark")[0];
              if (xmlNode16 != null)
              {
                string str = xmlNode16.Attributes["Priority"].Value;
                dataRow["Result_Sinkmark"] = (object) str;
              }
              XmlNode xmlNode17 = xmlElement3.GetElementsByTagName("TemperatureAtFlowFront")[0];
              if (xmlNode17 != null)
              {
                string str = xmlNode17.Attributes["Priority"].Value;
                dataRow["Result_TemperatureAtFlowFront"] = (object) str;
              }
              XmlNode xmlNode18 = xmlElement3.GetElementsByTagName("CycleTime")[0];
              if (xmlNode18 != null)
              {
                string str = xmlNode18.Attributes["Priority"].Value;
                dataRow["Result_CycleTime"] = (object) str;
              }
              XmlNode xmlNode19 = xmlElement3.GetElementsByTagName("InjectionPressure")[0];
              if (xmlNode19 != null)
              {
                string str = xmlNode19.Attributes["Priority"].Value;
                dataRow["Result_InjectionPressure"] = (object) str;
              }
              XmlNode xmlNode20 = xmlElement3.GetElementsByTagName("Weight")[0];
              if (xmlNode20 != null)
              {
                string str = xmlNode20.Attributes["Priority"].Value;
                dataRow["Result_Weight"] = (object) str;
              }
              XmlNode xmlNode21 = xmlElement3.GetElementsByTagName("ClampForce")[0];
              if (xmlNode21 != null)
              {
                string str = xmlNode21.Attributes["Priority"].Value;
                dataRow["Result_ClampForce"] = (object) str;
              }
              XmlElement xmlElement4 = (XmlElement) documentElement.GetElementsByTagName("Option")[0];
              XmlNode xmlNode22 = xmlElement4.GetElementsByTagName("ValveOption")[0];
              if (xmlNode22 != null)
              {
                string str = xmlNode22.Attributes["Value"].Value;
                dataRow["Option_ValveOption"] = (object) str;
              }
              XmlNode xmlNode23 = xmlElement4.GetElementsByTagName("LoopNum")[0];
              if (xmlNode23 != null)
              {
                string str = xmlNode23.Attributes["Value"].Value;
                dataRow["Option_LoopNum"] = (object) str;
              }
              XmlNode xmlNode24 = xmlElement4.GetElementsByTagName("DeflectionLimit")[0];
              if (xmlNode24 != null)
              {
                string str = xmlNode24.Attributes["Value"].Value;
                dataRow["Option_DeflectionLimit"] = (object) str;
              }
              XmlNode xmlNode25 = xmlElement4.GetElementsByTagName("ClampForceLimit")[0];
              if (xmlNode25 != null)
              {
                string str = xmlNode25.Attributes["Value"].Value;
                dataRow["Option_ClampForceLimit"] = (object) str;
              }
              XmlNode xmlNode26 = xmlElement4.GetElementsByTagName("InjectionPressureLimit")[0];
              if (xmlNode26 != null)
              {
                string str = xmlNode26.Attributes["Value"].Value;
                dataRow["Option_InjectionPressureLimit"] = (object) str;
              }
              XmlNode xmlNode27 = ((XmlElement) documentElement.GetElementsByTagName("Default")[0]).GetElementsByTagName("AnalysisType")[0];
              if (xmlNode27 != null)
                dataRow["Analysis_Type"] = (object) xmlNode27.Attributes["Value"].Value;
            }
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsData]LoadInputDB):" + ex.Message));
      }
    }

    public static void LoadMidResult()
    {
      clsDefine.g_dicMidResult = new Dictionary<string, string>();
      try
      {
        if (!clsDefine.g_fiMidResultCfg.Exists && clsDefine.g_fiMidResultCfgDefault.Exists)
        {
          if (!clsDefine.g_fiMidResultCfg.Directory.Exists)
            clsDefine.g_fiMidResultCfg.Directory.Create();
          clsDefine.g_fiMidResultCfgDefault.CopyTo(clsDefine.g_fiMidResultCfg.FullName);
        }
        clsDefine.g_fiMidResultCfg.Refresh();
        if (!clsDefine.g_fiMidResultCfg.Exists)
          return;
        string str1 = clsUtill.ReadINI("Phase", "Filling", clsDefine.g_fiMidResultCfg.FullName);
        if (str1 == "")
          clsDefine.g_dicMidResult.Add("Filling", "50");
        else
          clsDefine.g_dicMidResult.Add("Filling", str1);
        string str2 = clsUtill.ReadINI("Phase", "Packing", clsDefine.g_fiMidResultCfg.FullName);
        if (str2 == "")
          clsDefine.g_dicMidResult.Add("Packing", "50");
        else
          clsDefine.g_dicMidResult.Add("Packing", str2);
        string str3 = clsUtill.ReadINI("Phase", "Cooling", clsDefine.g_fiMidResultCfg.FullName);
        if (str3 == "")
          clsDefine.g_dicMidResult.Add("Cooling", "50");
        else
          clsDefine.g_dicMidResult.Add("Cooling", str3);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsData]LoadMidResult):" + ex.Message));
      }
    }

    public static void LoadCase()
    {
      try
      {
        clsDefine.g_dtCase = new DataTable();
        clsDefine.g_dtCase.Columns.Add("FC");
        clsDefine.g_dtCase.Columns.Add("VP");
        clsDefine.g_dtCase.Columns.Add("Melt");
        clsDefine.g_dtCase.Columns.Add("Mold");
        clsDefine.g_dtCase.Columns.Add("Cooling");
        clsDefine.g_dtCase.Columns.Add("Packing");
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsData]LoadCase):" + ex.Message));
      }
    }

    public static void LoadAI()
    {
      try
      {
        if (!clsDefine.g_fiAICfg.Exists && clsDefine.g_fiAICfgDefault.Exists)
        {
          if (!clsDefine.g_fiAICfg.Directory.Exists)
            clsDefine.g_fiAICfg.Directory.Create();
          clsDefine.g_fiAICfgDefault.CopyTo(clsDefine.g_fiAICfg.FullName);
        }
        clsDefine.g_fiAICfg.Refresh();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMoldFlow][clsData]LoadAI):" + ex.Message));
      }
    }

    public static void LoadSummary()
    {
      bool result = false;
      try
      {
        if (!clsDefine.g_fiSummaryCfg.Exists && clsDefine.g_fiSummaryCfgDefault.Exists)
        {
          if (!clsDefine.g_fiSummaryCfg.Directory.Exists)
            clsDefine.g_fiSummaryCfg.Directory.Create();
          clsDefine.g_fiSummaryCfgDefault.CopyTo(clsDefine.g_fiSummaryCfg.FullName);
        }
        clsDefine.g_fiSummaryCfg.Refresh();
        if (!clsDefine.g_fiSummaryCfg.Exists)
          return;
        string str = clsUtill.ReadINI("Export", "AI", clsDefine.g_fiSummaryCfg.FullName);
        if (str != "")
        {
          bool.TryParse(str, out result);
          if (result)
            clsDefine.enumSummaryType = clsHDMFLibDefine.SummaryType.AI;
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMoldFlow][clsData]LoadSummary):" + ex.Message));
      }
    }

    public static void LoadSummaryView()
    {
      try
      {
        DirectoryInfo directoryInfo1 = new DirectoryInfo(clsDefine.g_diCfg.FullName + "\\SummaryView");
        if (!directoryInfo1.Exists)
        {
          DirectoryInfo directoryInfo2 = new DirectoryInfo(clsDefine.g_diSummaryViewCfgDefault.FullName);
          if (directoryInfo2.Exists)
          {
            if (!directoryInfo1.Exists)
              directoryInfo1.Create();
            clsUtill.CopyFolder(directoryInfo2.FullName, directoryInfo1.FullName);
          }
        }
        if (!clsDefine.g_fiSummaryViewOptionCfg.Exists)
          return;
        clsDefine.g_dtSummaryView = clsUtill.GetINIData(clsDefine.g_fiSummaryViewOptionCfg.FullName);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsData]LoadSummaryView):" + ex.Message));
      }
    }

    public static void LoadExtension()
    {
      clsDefine.g_dicExtension = new Dictionary<string, string>();
      try
      {
        if (!clsDefine.g_fiExtensionCfg.Exists && clsDefine.g_fiExtensionCfgDefault.Exists)
        {
          if (!clsDefine.g_fiExtensionCfg.Directory.Exists)
            clsDefine.g_fiExtensionCfg.Directory.Create();
          clsDefine.g_fiExtensionCfgDefault.CopyTo(clsDefine.g_fiExtensionCfg.FullName);
        }
        clsDefine.g_fiExtensionCfg.Refresh();
        if (!clsDefine.g_fiExtensionCfg.Exists)
          return;
        string str1 = clsUtill.ReadINI("Extension", "Excel", clsDefine.g_fiExtensionCfg.FullName);
        if (str1 != "")
          clsDefine.g_dicExtension.Add("Excel", str1);
        string str2 = clsUtill.ReadINI("Extension", "PowerPoint", clsDefine.g_fiExtensionCfg.FullName);
        if (str2 != "")
          clsDefine.g_dicExtension.Add("PowerPoint", str2);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMoldFlow][clsData]LoadExtension):" + ex.Message));
      }
    }

    public static void LoadProcessDB()
    {
      try
      {
        clsDefine.g_dtProcessDB = new DataTable();
        clsDefine.g_dtProcessDB.Columns.Add("Name");
        clsDefine.g_dtProcessDB.Columns.Add("Company");
        clsDefine.g_dtProcessDB.Columns.Add("Item");
        clsDefine.g_dtProcessDB.Columns.Add("Option");
        clsDefine.g_dtProcessDB.Columns.Add("Mat_UDB");
        clsDefine.g_dtProcessDB.Columns.Add("Mat_Manufacturer");
        clsDefine.g_dtProcessDB.Columns.Add("Mat_TradeName");
        clsDefine.g_dtProcessDB.Columns.Add("Mat_Familyabbreviation");
        clsDefine.g_dtProcessDB.Columns.Add("Mat_MaterialID");
        clsDefine.g_dtProcessDB.Columns.Add("Mat_Sequence");
        clsDefine.g_dtProcessDB.Columns.Add("Mat_CTime");
        clsDefine.g_dtProcessDB.Columns.Add("Mat_MeltTemp");
        clsDefine.g_dtProcessDB.Columns.Add("Mat_MoldTemp");
        clsDefine.g_dtProcessDB.Columns.Add("FC_Type");
        for (int index = 0; index < 14; ++index)
          clsDefine.g_dtProcessDB.Columns.Add("FC1_" + (object) (index + 1));
        for (int index = 0; index < 14; ++index)
          clsDefine.g_dtProcessDB.Columns.Add("FC2_" + (object) (index + 1));
        clsDefine.g_dtProcessDB.Columns.Add("FC3");
        clsDefine.g_dtProcessDB.Columns.Add("FC4");
        clsDefine.g_dtProcessDB.Columns.Add("VP_Type");
        clsDefine.g_dtProcessDB.Columns.Add("VP_Value");
        clsDefine.g_dtProcessDB.Columns.Add("PHC_Type");
        for (int index = 0; index < 3; ++index)
          clsDefine.g_dtProcessDB.Columns.Add("PHC1_" + (object) (index + 1));
        for (int index = 0; index < 3; ++index)
          clsDefine.g_dtProcessDB.Columns.Add("PHC2_" + (object) (index + 1));
        if ((!clsDefine.g_diProcessDBCfg.Exists || clsDefine.g_diProcessDBCfg.GetFiles("*.xml").Length == 0) && clsDefine.g_diProcessDBCfgDefault.Exists && clsDefine.g_diProcessDBCfgDefault.GetFiles("*.xml").Length != 0)
          clsUtill.CopyFolder(clsDefine.g_diProcessDBCfgDefault.FullName, clsDefine.g_diProcessDBCfg.FullName);
        clsDefine.g_diProcessDBCfg.Refresh();
        if (!clsDefine.g_diProcessDBCfg.Exists)
          return;
        foreach (FileInfo file in clsDefine.g_diProcessDBCfg.GetFiles("*.xml"))
        {
          string withoutExtension = Path.GetFileNameWithoutExtension(file.FullName);
          string[] strArray = withoutExtension.ToString().Split('_');
          if (strArray.Length == 3)
          {
            DataRow dataRow = clsDefine.g_dtProcessDB.Rows.Add();
            dataRow["Name"] = (object) withoutExtension;
            dataRow["Company"] = (object) strArray[0];
            dataRow["Item"] = (object) strArray[1];
            dataRow["Option"] = (object) strArray[2];
            using (XmlReader reader = XmlReader.Create(file.FullName))
            {
              XmlDocument xmlDocument = new XmlDocument();
              xmlDocument.Load(reader);
              XmlElement documentElement = xmlDocument.DocumentElement;
              if (documentElement != null)
              {
                XmlElement xmlElement1 = (XmlElement) documentElement.GetElementsByTagName("Material")[0];
                XmlNode xmlNode1 = xmlElement1.GetElementsByTagName("UDB")[0];
                if (xmlNode1 != null)
                  dataRow["Mat_UDB"] = (object) xmlNode1.InnerText;
                XmlNode xmlNode2 = xmlElement1.GetElementsByTagName("Manufacturer")[0];
                if (xmlNode2 != null)
                  dataRow["Mat_Manufacturer"] = (object) xmlNode2.InnerText;
                XmlNode xmlNode3 = xmlElement1.GetElementsByTagName("TradeName")[0];
                if (xmlNode3 != null)
                  dataRow["Mat_TradeName"] = (object) xmlNode3.InnerText;
                XmlNode xmlNode4 = xmlElement1.GetElementsByTagName("FamilyAbbreviation")[0];
                if (xmlNode4 != null)
                  dataRow["Mat_Familyabbreviation"] = (object) xmlNode4.InnerText;
                XmlNode xmlNode5 = xmlElement1.GetElementsByTagName("MaterialID")[0];
                if (xmlNode5 != null)
                  dataRow["Mat_MaterialID"] = (object) xmlNode5.InnerText;
                XmlNode xmlNode6 = xmlElement1.GetElementsByTagName("Sequence")[0];
                if (xmlNode6 != null)
                  dataRow["Mat_Sequence"] = (object) xmlNode6.InnerText;
                XmlNode xmlNode7 = xmlElement1.GetElementsByTagName("CoolingTime")[0];
                if (xmlNode7 != null)
                  dataRow["Mat_CTime"] = (object) xmlNode7.InnerText;
                XmlNode xmlNode8 = xmlElement1.GetElementsByTagName("MeltTemperature")[0];
                if (xmlNode8 != null)
                  dataRow["Mat_MeltTemp"] = (object) xmlNode8.InnerText;
                XmlNode xmlNode9 = xmlElement1.GetElementsByTagName("MoldTemperature")[0];
                if (xmlNode9 != null)
                  dataRow["Mat_MoldTemp"] = (object) xmlNode9.InnerText;
                XmlElement xmlElement2 = (XmlElement) documentElement.GetElementsByTagName("FillingControl")[0];
                XmlNode xmlNode10 = xmlElement2.GetElementsByTagName("Type")[0];
                if (xmlNode10 != null)
                  dataRow["FC_Type"] = (object) xmlNode10.InnerText;
                XmlNode xmlNode11 = xmlElement2.GetElementsByTagName("FC1")[0];
                if (xmlNode11 != null)
                {
                  for (int index = 0; index < 14; ++index)
                    dataRow["FC1_" + (object) (index + 1)] = (object) xmlNode11.Attributes["V" + (object) (index + 1)].Value;
                }
                XmlNode xmlNode12 = xmlElement2.GetElementsByTagName("FC2")[0];
                if (xmlNode12 != null)
                {
                  for (int index = 0; index < 14; ++index)
                    dataRow["FC2_" + (object) (index + 1)] = (object) xmlNode12.Attributes["V" + (object) (index + 1)].Value;
                }
                XmlNode xmlNode13 = xmlElement2.GetElementsByTagName("FC3")[0];
                if (xmlNode13 != null)
                  dataRow["FC3"] = (object) xmlNode13.Attributes["V1"].Value;
                XmlNode xmlNode14 = xmlElement2.GetElementsByTagName("FC4")[0];
                if (xmlNode14 != null)
                  dataRow["FC4"] = (object) xmlNode14.Attributes["V1"].Value;
                XmlElement xmlElement3 = (XmlElement) documentElement.GetElementsByTagName("VPSwitchOver")[0];
                XmlNode xmlNode15 = xmlElement3.GetElementsByTagName("Type")[0];
                if (xmlNode15 != null)
                  dataRow["VP_Type"] = (object) xmlNode15.InnerText;
                XmlNode xmlNode16 = xmlElement3.GetElementsByTagName("Value")[0];
                if (xmlNode16 != null)
                  dataRow["VP_Value"] = (object) xmlNode16.InnerText;
                XmlElement xmlElement4 = (XmlElement) documentElement.GetElementsByTagName("PackHoldingControl")[0];
                XmlNode xmlNode17 = xmlElement4.GetElementsByTagName("Type")[0];
                if (xmlNode17 != null)
                  dataRow["PHC_Type"] = (object) xmlNode17.InnerText;
                XmlNode xmlNode18 = xmlElement4.GetElementsByTagName("PHC1")[0];
                if (xmlNode18 != null)
                {
                  for (int index = 0; index < 3; ++index)
                    dataRow["PHC1_" + (object) (index + 1)] = (object) xmlNode18.Attributes["V" + (object) (index + 1)].Value;
                }
                XmlNode xmlNode19 = xmlElement4.GetElementsByTagName("PHC2")[0];
                if (xmlNode19 != null)
                {
                  for (int index = 0; index < 3; ++index)
                    dataRow["PHC2_" + (object) (index + 1)] = (object) xmlNode19.Attributes["V" + (object) (index + 1)].Value;
                }
              }
            }
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsData]LoadProcessDB):" + ex.Message));
      }
    }

    public static void LoadUDB()
    {
      if (clsDefine.g_diProcessUDBCfg.Exists && clsDefine.g_diProcessUDBCfg.GetFiles("*.21000.udb").Length != 0 || !clsDefine.g_diProcessUDBCfgDefault.Exists || clsDefine.g_diProcessUDBCfgDefault.GetFiles("*.udb").Length == 0)
        return;
      clsUtill.CopyFolder(clsDefine.g_diProcessUDBCfgDefault.FullName, clsDefine.g_diProcessUDBCfg.FullName);
    }

    public static void LoadTemplate()
    {
      try
      {
        if (clsDefine.g_diTemplate.Exists && clsDefine.g_diTemplate.GetFiles().Length != 0 || !clsDefine.g_diTemplateDefault.Exists || clsDefine.g_diTemplateDefault.GetFiles().Length == 0)
          return;
        clsUtill.CopyFolder(clsDefine.g_diTemplateDefault.FullName, clsDefine.g_diTemplate.FullName);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsData]LoadTemplate):" + ex.Message));
      }
    }

    public static DataRow GetStudy(string p_strStudy)
    {
      DataRow study = (DataRow) null;
      try
      {
        foreach (DataTable table in (InternalDataCollectionBase) clsDefine.g_dsProduct.Tables)
        {
          foreach (DataRow row in (InternalDataCollectionBase) table.Rows)
          {
            if (row["Name"].ToString() == p_strStudy)
            {
              study = row;
              break;
            }
          }
          if (study != null)
            break;
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsData]GetStudy):" + ex.Message));
      }
      return study;
    }

    public static DataRow[] GetStudyDataRow(bool p_isComplete)
    {
      List<DataRow> dataRowList = new List<DataRow>();
      try
      {
        foreach (DataTable table in (InternalDataCollectionBase) clsDefine.g_dsProduct.Tables)
        {
          foreach (DataRow dataRow in table.AsEnumerable())
          {
            if (dataRow["Check"] == DBNull.Value && (!p_isComplete || !(dataRow["Status"].ToString() != clsDefine.Status.COMPLETED.ToString())))
              dataRowList.Add(dataRow);
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsData]GetStudyDataRow):" + ex.Message));
      }
      return dataRowList.ToArray();
    }

    public static Dictionary<string, string> GetSummaryViewDataFromIni()
    {
      Dictionary<string, string> summaryViewDataFromIni = new Dictionary<string, string>();
      try
      {
        FileInfo fileInfo;
        if (clsDefine.g_strLanguageType == "KOR")
        {
          fileInfo = new FileInfo(clsDefine.g_diCfg.FullName + "\\SummaryView\\SummaryView.ini");
        }
        else
        {
          fileInfo = new FileInfo(clsDefine.g_diCfg.FullName + "\\SummaryView\\SummaryView_" + clsDefine.g_strLanguageType + ".ini");
          if (!fileInfo.Exists)
            fileInfo = new FileInfo(clsDefine.g_diCfg.FullName + "\\SummaryView\\SummaryView.ini");
        }
        summaryViewDataFromIni = clsUtill.GetINIDataFromSection(fileInfo.FullName, "View");
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsData]GetSummaryViewDataFromIni):" + ex.Message));
      }
      return summaryViewDataFromIni;
    }
  }
}
