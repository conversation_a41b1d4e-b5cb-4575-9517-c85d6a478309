'%RunPerInstance
'@ Script mejorado para automatización de informes Moldflow
'@@ Versión 5.0 - Mayo 2025
Option Explicit
SetLocale("en-us")

' ########## VARIABLES DE CONFIGURACIÓN ##########
' Rutas de plantillas
Const TEMPLATE_PPT = "C:\Code_Studio\Moldflow\FICO_Autom\Macros\Macros\CAE_Reports_Templates_Rheologic-StandardProcess_22_Abril_2024.pptx"
Const TEMPLATE_XLS = "C:\Code_Studio\Moldflow\FICO_Autom\Macros\Macros\CAE_Reports_Templates_Rheologic-ReportSupportingTables_12feb2024.xlsx"
' Modo debug (True = solo mostrar datos, False = generar informe completo)
Const DEBUG_MODE = False
' Tiempo de espera para regeneración de vistas (ms)
Const WAIT_TIME = 2000
' Valor máximo para resultados
Const MAX_RESULT_VALUE = 1.0E20
' ########## FIN VARIABLES DE CONFIGURACIÓN ##########

' Variables globales principales
Dim Synergy, SynergyGetter
Dim PlotMgr, Plot, Viewer
Dim StudyDoc
Dim LayerManager
Dim objExcelApp, objWB, objSheet
Dim objPPTApp, objPPT
Dim FS, WshShell
Dim ahora, luego, Tiempo

' Inicialización de objetos COM
Set FS = CreateObject("Scripting.FileSystemObject")
Set WshShell = WScript.CreateObject("WScript.Shell")

' Inicializar el tiempo de ejecución
ahora = Time

' Inicializar Synergy
InitializeSynergy()

' Inicializar otros objetos principales
Set StudyDoc = Synergy.StudyDoc()
Set Viewer = Synergy.Viewer()
Set PlotMgr = Synergy.PlotManager()
Set LayerManager = Synergy.LayerManager()
LayerManager.ShowAllLayers

' Recopilación de datos y generación de informe
Main()

Sub Main()
    ' Variables para almacenar los resultados extraídos
    Dim resultData
    Set resultData = CreateObject("Scripting.Dictionary")
    
    ' Extraer datos desde el archivo de resultados
    ExtractResultData resultData
    
    ' Si estamos en modo debug, mostrar resultados y salir
    If DEBUG_MODE Then
        ShowDebugInfo resultData
        Exit Sub
    End If
    
    ' Inicializar Excel y PowerPoint
    InitializeExcel TEMPLATE_XLS
    InitializePowerPoint TEMPLATE_PPT
    
    ' Rellenar datos en Excel
    FillExcelData resultData
    
    ' Generar secuencias de gráficos en PowerPoint
    GeneratePPTSequences
    
    ' Mostrar mensaje de finalización
    WshShell.Popup "Informe generado correctamente", , "FICOSA Reporting", 64
End Sub

Sub InitializeSynergy()
    On Error Resume Next
    Set SynergyGetter = GetObject(WshShell.ExpandEnvironmentStrings("%SAInstance%"))
    On Error GoTo 0
    
    If (Not IsEmpty(SynergyGetter)) Then
        Set Synergy = SynergyGetter.GetSASynergy
    Else
        Set Synergy = CreateObject("amiws.Synergy")
    End If
End Sub

Sub ExtractResultData(resultData)
    ' Obtener el prefijo del archivo de resultados
    Dim lName, lOutName
    lName = StudyDoc.GetResultPrefix("Flow")
    lOutName = lName & ".out"
    
    ' Crear y cargar la clase ScreenOutput
    Dim lMessages
    Set lMessages = New ScreenOutput
    lMessages.LoadOutputFile(lOutName)
    
    ' Extraer volumen de pieza
    ExtractPartVolume lMessages, resultData
    
    ' Extraer volumen de ramales
    ExtractRunnersVolume lMessages, resultData
    
    ' Extraer presión en compuerta
    ExtractPressureData resultData
    
    ' Extraer datos de packing (compactación)
    ExtractPackingData lMessages, resultData
    
    ' Extraer área proyectada
    ExtractProjectedArea lMessages, resultData
    
    ' Extraer tiempo de llenado
    ExtractFillTime lMessages, resultData
    
    ' Extraer pesos finales
    ExtractFinalWeights lMessages, resultData
    
    ' Extraer temperaturas
    ExtractTemperatures lMessages, resultData
    
    ' Extraer fuerza de cierre
    resultData.Add "MaxClampForce", getMaxClampForce()
    
    ' Extraer rango de temperatura en frente de flujo
    Dim ffTempRange
    Set ffTempRange = getRangeTempFlowFront()
    resultData.Add "MaxFFTemp", ffTempRange.Val(1)
    resultData.Add "MinFFTemp", ffTempRange.Val(0)
    
    ' Extraer información del material
    ExtractMaterialInfo resultData
End Sub

Sub ExtractPartVolume(lMessages, resultData)
    Dim MM
    Set MM = lMessages.GetMessage(300320, 1)
    If Not MM Is Nothing Then
        resultData.Add "PartVolume", MM.GetFloat(0) * 1000000 ' Convertir a cm³
    End If
End Sub

Sub ExtractRunnersVolume(lMessages, resultData)
    Dim MM
    Set MM = lMessages.GetMessage(300330, 1)
    If Not MM Is Nothing Then
        resultData.Add "RunnersVolume", MM.GetFloat(0) * 1000000 ' Convertir a cm³
    End If
End Sub

Sub ExtractPressureData(resultData)
    Set Plot = PlotMgr.FindPlotByName("Pressure at V/P switchover")
    Viewer.ShowPlot Plot
    
    ' Ocultar capas irrelevantes para mejor visualización
    HideIrrelevantLayers
    
    WScript.Sleep WAIT_TIME
    Plot.Regenerate
    
    resultData.Add "MaxPressure", Plot.GetMaxValue()
    resultData.Add "MinPressure", Plot.GetMinValue()
    
    ' Restaurar todas las capas
    LayerManager.ShowAllLayers
End Sub

Sub ExtractPackingData(lMessages, resultData)
    Dim aMSCD, bMSCD
    aMSCD = 302055
    bMSCD = 302056
    
    Dim MM, NN
    Set MM = lMessages.GetMessage(aMSCD, 1)
    Set NN = lMessages.GetMessage(bMSCD, 1)
    
    Dim arrPackingRange(), I, count
    Redim arrPackingRange(9)
    For I = 0 to 9
        arrPackingRange(I) = 0
    Next
    count = 0
    
    ' Procesar datos de compactación según el formato disponible
    If NN Is Nothing Then
        ProcessFirstPackingFormat MM, aMSCD, arrPackingRange, count, resultData
    Else
        ProcessSecondPackingFormat NN, bMSCD, arrPackingRange, count, resultData
    End If
End Sub

Sub ProcessFirstPackingFormat(MM, aMSCD, arrPackingRange, count, resultData)
    Dim I
    For I = 0 to MM.getNumFloat-1 Step 2
        If ((CStr(MM.GetMSCD) = CStr(aMSCD)) AND (MM.GetFloat(I) <> 0)) Then
            arrPackingRange(count) = MM.GetFloat(I)
            arrPackingRange(count+1) = MM.GetFloat(I+1)
            count = count + 2
        End If
    Next
    
    ' Guardar los datos procesados
    resultData.Add "PackingTime1", arrPackingRange(0)
    resultData.Add "PackingPorcentage1", arrPackingRange(1) / 100
    resultData.Add "PackingPressure1", resultData("MaxPressure") * resultData("PackingPorcentage1")
    
    ' Agregar el resto de datos de compactación de manera similar
    If count > 2 Then
        resultData.Add "PackingTime2", arrPackingRange(2)
        resultData.Add "PackingPorcentage2", arrPackingRange(3) / 100
        resultData.Add "PackingPressure2", resultData("MaxPressure") * resultData("PackingPorcentage2")
    End If
    
    If count > 4 Then
        resultData.Add "PackingTime3", arrPackingRange(4)
        resultData.Add "PackingPorcentage3", arrPackingRange(5) / 100
        resultData.Add "PackingPressure3", resultData("MaxPressure") * resultData("PackingPorcentage3")
    End If
    
    If count > 6 Then
        resultData.Add "PackingTime4", arrPackingRange(6)
        resultData.Add "PackingPorcentage4", arrPackingRange(7) / 100
        resultData.Add "PackingPressure4", resultData("MaxPressure") * resultData("PackingPorcentage4")
    End If
    
    If count > 8 Then
        resultData.Add "PackingTime5", arrPackingRange(8)
        resultData.Add "PackingPorcentage5", arrPackingRange(9) / 100
        resultData.Add "PackingPressure5", resultData("MaxPressure") * resultData("PackingPorcentage5")
    End If
End Sub

Sub ProcessSecondPackingFormat(NN, bMSCD, arrPackingRange, count, resultData)
    Dim I
    For I = 0 to NN.getNumFloat-1 Step 2
        If ((CStr(NN.GetMSCD) = CStr(bMSCD)) AND (NN.GetFloat(I) <> 0)) Then
            arrPackingRange(count) = NN.GetFloat(I)
            arrPackingRange(count+1) = NN.GetFloat(I+1)
            count = count + 2
        End If
    Next
    
    ' Guardar los datos procesados
    resultData.Add "PackingTime1", arrPackingRange(0)
    resultData.Add "PackingPorcentage1", arrPackingRange(1) / (resultData("MaxPressure") * 1000000)
    resultData.Add "PackingPressure1", arrPackingRange(1) / 1000000
    
    ' Agregar el resto de datos de compactación de manera similar
    If count > 2 Then
        resultData.Add "PackingTime2", arrPackingRange(2)
        resultData.Add "PackingPorcentage2", arrPackingRange(3) / (resultData("MaxPressure") * 1000000)
        resultData.Add "PackingPressure2", arrPackingRange(3) / 1000000
    End If
    
    If count > 4 Then
        resultData.Add "PackingTime3", arrPackingRange(4)
        resultData.Add "PackingPorcentage3", arrPackingRange(5) / (resultData("MaxPressure") * 1000000)
        resultData.Add "PackingPressure3", arrPackingRange(5) / 1000000
    End If
    
    If count > 6 Then
        resultData.Add "PackingTime4", arrPackingRange(6)
        resultData.Add "PackingPorcentage4", arrPackingRange(7) / (resultData("MaxPressure") * 1000000)
        resultData.Add "PackingPressure4", arrPackingRange(7) / 1000000
    End If
    
    If count > 8 Then
        resultData.Add "PackingTime5", arrPackingRange(8)
        resultData.Add "PackingPorcentage5", arrPackingRange(9) / (resultData("MaxPressure") * 1000000)
        resultData.Add "PackingPressure5", arrPackingRange(9) / 1000000
    End If
End Sub

Sub ExtractProjectedArea(lMessages, resultData)
    Dim MM
    Set MM = lMessages.GetMessage(300350, 1)
    If Not MM Is Nothing Then
        resultData.Add "ProjectedArea", MM.GetFloat(0) * 10000 ' Convertir a cm²
    End If
End Sub

Sub ExtractFillTime(lMessages, resultData)
    Dim MM
    Set MM = lMessages.GetMessage(300400, 1)
    If Not MM Is Nothing Then
        resultData.Add "CavityFillTime", MM.GetFloat(0)
    End If
End Sub

Sub ExtractFinalWeights(lMessages, resultData)
    ' Extraer peso de la pieza después de compactación
    Dim MM
    Set MM = lMessages.GetMessage(304139, 2)
    If Not MM Is Nothing Then
        resultData.Add "FinalPartWeight", MM.GetFloat(0) * 1000 ' Convertir a gramos
    Else
        Set MM = lMessages.GetMessage(300370, 2)
        If Not MM Is Nothing Then
            resultData.Add "FinalPartWeight", MM.GetFloat(0) * 1000
        End If
    End If
    
    ' Extraer peso de los ramales después de compactación
    Set MM = lMessages.GetMessage(304146, 1)
    If Not MM Is Nothing Then
        resultData.Add "FinalRunnersWeight", MM.GetFloat(0) * 1000
    End If
End Sub

Sub ExtractTemperatures(lMessages, resultData)
    ' Extraer temperatura de fundido
    Dim MM
    Set MM = lMessages.GetMessage(102101, 1)
    If Not MM Is Nothing Then
        resultData.Add "MeltTemperature", MM.GetFloat(0) - 273.15 ' Convertir de K a °C
    End If
    
    ' Extraer temperatura de molde
    Set MM = lMessages.GetMessage(220124, 1)
    If Not MM Is Nothing Then
        resultData.Add "MoldTemperature", MM.GetFloat(0) - 273.15
    End If
End Sub

Sub ExtractMaterialInfo(resultData)
    Dim MaterialID, MaterialSubID, Material2ID, Material2SubID
    Call GetMaterialData(MaterialID, MaterialSubID, Material2ID, Material2SubID)
    
    If MaterialID > 0 Then
        Dim arrRecommendedTemps, MaterialName, FamilyAbreviation
        Set arrRecommendedTemps = Synergy.CreateDoubleArray()
        
        Call GetTCodeValue(MaterialID, MaterialSubID, 1800, arrRecommendedTemps)
        Call GetTCodeDescription(MaterialID, MaterialSubID, 1998, MaterialName)
        Call GetTCodeDescription(MaterialID, MaterialSubID, 1999, FamilyAbreviation)
        
        resultData.Add "MaterialName", MaterialName
        resultData.Add "FamilyAbreviation", FamilyAbreviation
        resultData.Add "MinRecommendedTemp", arrRecommendedTemps.Val(0)
        resultData.Add "MaxRecommendedTemp", arrRecommendedTemps.Val(1)
    End If
End Sub

Sub HideIrrelevantLayers()
    Dim layerNames
    layerNames = Array("Cooling", "Tetras_1", "Tetras_2", "Beams_2ndshot", "Tetras_2ndshot_1", "Tetras_2ndshot_2")
    
    Dim layerName, LayerList
    For Each layerName In layerNames
        Set LayerList = LayerManager.FindLayerByName(layerName)
        If Not LayerList Is Nothing Then
            LayerManager.ToggleLayer LayerList
        End If
    Next
End Sub

Sub ShowDebugInfo(resultData)
    Dim lStr, key
    lStr = "RESULTADOS" & vbCrlf & vbCrlf
    
    For Each key In resultData.Keys
        lStr = lStr & key & ": " & resultData(key) & vbCrlf
    Next
    
    luego = Time
    Tiempo = DateDiff("s", ahora, luego)
    lStr = lStr & vbCrlf & vbCrlf & "Tiempo de ejecución: " & Tiempo & "s"
    
    MsgBox lStr
    WScript.Quit(0)
End Sub

Sub InitializeExcel(templatePath)
    Set objExcelApp = CreateObject("Excel.Application")
    Set objWB = objExcelApp.Workbooks.Open(templatePath, 3, True)
    objExcelApp.Visible = True
End Sub

Sub InitializePowerPoint(templatePath)
    Set objPPTApp = CreateObject("PowerPoint.Application")
    Set objPPT = objPPTApp.Presentations.Open(templatePath, 3, True)
End Sub

Sub FillExcelData(resultData)
    ' Hoja 1 - Datos generales
    Set objSheet = objWB.Worksheets(1)
    objSheet.Activate
    
    If resultData.Exists("ProjectedArea") Then
        objSheet.Range("E3").Formula = "=round(" & resultData("ProjectedArea") & ",0)"
    End If
    
    If resultData.Exists("PartVolume") Then
        objSheet.Range("E4").Formula = "=round(" & resultData("PartVolume") & ",1)"
    End If
    
    If resultData.Exists("RunnersVolume") Then
        objSheet.Range("E5").Formula = "=round(" & resultData("RunnersVolume") & ",1)"
    End If
    
    If resultData.Exists("FinalPartWeight") Then
        objSheet.Range("E8").Formula = "=round(" & resultData("FinalPartWeight") & ",1)"
    End If
    
    If resultData.Exists("FinalRunnersWeight") Then
        objSheet.Range("E11").Formula = "=round(" & resultData("FinalRunnersWeight") & ",1)"
    End If
    
    If resultData.Exists("CavityFillTime") Then
        objSheet.Range("E15").Formula = "=round(" & resultData("CavityFillTime") & ",1)"
    End If
    
    If resultData.Exists("MaxPressure") Then
        objSheet.Range("E25").Formula = "=round(" & resultData("MaxPressure") & ",0)"
    End If
    
    If resultData.Exists("MinPressure") Then
        objSheet.Range("E27").Formula = "=round(" & resultData("MinPressure") & ",0)"
    End If
    
    If resultData.Exists("MeltTemperature") Then
        objSheet.Range("E13").Formula = "=round(" & resultData("MeltTemperature") & ",0)"
    End If
    
    If resultData.Exists("MoldTemperature") Then
        objSheet.Range("E14").Formula = "=round(" & resultData("MoldTemperature") & ",0)"
    End If
    
    If resultData.Exists("MaxClampForce") Then
        objSheet.Range("E29").Formula = "=roundup(" & resultData("MaxClampForce") & ",0)"
    End If
    
    If resultData.Exists("MaxFFTemp") Then
        objSheet.Range("E30").Formula = "=round(" & resultData("MaxFFTemp") & ",0)"
    End If
    
    If resultData.Exists("MinFFTemp") Then
        objSheet.Range("E31").Formula = "=round(" & resultData("MinFFTemp") & ",0)"
    End If
    
    ' Hoja 2 - Datos de material y compactación
    Set objSheet = objWB.Worksheets(2)
    objSheet.Activate
    
    If resultData.Exists("FamilyAbreviation") Then
        objSheet.Range("E2").Formula = resultData("FamilyAbreviation")
        objSheet.Range("E7").Formula = resultData("FamilyAbreviation")
    End If
    
    If resultData.Exists("MaterialName") Then
        objSheet.Range("E3").Formula = resultData("MaterialName")
        objSheet.Range("E8").Formula = resultData("MaterialName")
    End If
    
    ' Datos de compactación
    FillPackingData objSheet, resultData
    
    ' Hoja 4 - Temperaturas recomendadas
    Set objSheet = objWB.Worksheets(4)
    objSheet.Activate
    
    If resultData.Exists("MinRecommendedTemp") Then
        objSheet.Range("G13").Formula = "=" & resultData("MinRecommendedTemp")
    End If
    
    If resultData.Exists("MaxRecommendedTemp") Then
        objSheet.Range("H13").Formula = "=" & resultData("MaxRecommendedTemp")
    End If
End Sub

Sub FillPackingData(objSheet, resultData)
    ' Primer punto de compactación
    If resultData.Exists("PackingTime1") Then
        objSheet.Range("E20").Formula = "=(" & resultData("PackingTime1") & ")"
    End If
    
    If resultData.Exists("PackingPorcentage1") Then
        objSheet.Range("F20").Formula = "=roundup(" & resultData("PackingPorcentage1") & ",2)"
    End If
    
    If resultData.Exists("PackingPressure1") Then
        objSheet.Range("G20").Formula = "=roundup(" & resultData("PackingPressure1") & ",0)"
    End If
    
    ' Segundo punto de compactación
    If resultData.Exists("PackingTime2") Then
        objSheet.Range("E21").Formula = "=(" & resultData("PackingTime2") & ")"
    End If
    
    If resultData.Exists("PackingPorcentage2") Then
        objSheet.Range("F21").Formula = "=roundup(" & resultData("PackingPorcentage2") & ",2)"
    End If
    
    If resultData.Exists("PackingPressure2") Then
        objSheet.Range("G21").Formula = "=roundup(" & resultData("PackingPressure2") & ",0)"
    End If
    
    ' Tercer punto de compactación
    If resultData.Exists("PackingTime3") Then
        objSheet.Range("E22").Formula = "=(" & resultData("PackingTime3") & ")"
    End If
    
    If resultData.Exists("PackingPorcentage3") Then
        objSheet.Range("F22").Formula = "=roundup(" & resultData("PackingPorcentage3") & ",2)"
    End If
    
    If resultData.Exists("PackingPressure3") Then
        objSheet.Range("G22").Formula = "=roundup(" & resultData("PackingPressure3") & ",0)"
    End If
    
    ' Cuarto punto de compactación
    If resultData.Exists("PackingTime4") Then
        objSheet.Range("E23").Formula = "=(" & resultData("PackingTime4") & ")"
    End If
    
    If resultData.Exists("PackingPorcentage4") Then
        objSheet.Range("F23").Formula = "=roundup(" & resultData("PackingPorcentage4") & ",2)"
    End If
    
    If resultData.Exists("PackingPressure4") Then
        objSheet.Range("G23").Formula = "=roundup(" & resultData("PackingPressure4") & ",0)"
    End If
    
    ' Quinto punto de compactación
    If resultData.Exists("PackingTime5") Then
        objSheet.Range("E24").Formula = "=(" & resultData("PackingTime5") & ")"
    End If
    
    If resultData.Exists("PackingPorcentage5") Then
        objSheet.Range("F24").Formula = "=roundup(" & resultData("PackingPorcentage5") & ",2)"
    End If
    
    If resultData.Exists("PackingPressure5") Then
        objSheet.Range("G24").Formula = "=roundup(" & resultData("PackingPressure5") & ",0)"
    End If
End Sub

Sub GeneratePPTSequences()
    ' Generar secuencias de gráficos para el informe PPT
    GenerateFillSequence()
    GeneratePressureatswitchover()
    GenerateTemperatureatflowfront()
    GeneratePressureatinjectionlocation()
    GenerateTotalWeight()
    GenerateDeflections()
End Sub

' [Mantener aquí las funciones existentes que generan secuencias PPT]
' [Asegurar que todas las funciones auxiliares existentes se mantienen]

' ... [Resto de funciones como GenerateFillSequence(), GetTCodeValue(), etc.]

' Incluir aquí todas las clases y funciones auxiliares que tenías antes