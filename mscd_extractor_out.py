import re
import csv
import os
import sys

def load_mscd_codes(csv_file):
    """Load MSCD codes from generated CSV file."""
    mscd_dict = {}
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            mscd_dict[row['mscd_code']] = row
    return mscd_dict

def extract_mscd_values(out_file, mscd_dict, output_file):
    """Extrae valores de los códigos MSCD encontrados en el archivo .out, respetando el número de parámetros y su orden."""
    results = []
    mscd_pattern = re.compile(r'^([A-Za-z ]*:?\s*)?([A-Za-z ]*:?\s*)?(\w+)?(\s*=)?\s*([\d\.,\-]+\s*[A-Za-z%]*)?$')
    with open(out_file, 'r', encoding='utf-8', errors='ignore') as f:
        lines = f.readlines()
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            # Buscar coincidencias de línea con formato clave = valor
            for mscd_code, mscd_info in mscd_dict.items():
                message_template = mscd_info.get('message_template') or mscd_info.get('message')
                if message_template and message_template.split('=')[0].strip() in line:
                    # Extraer valor después del signo igual
                    if '=' in line:
                        valor = line.split('=',1)[1].strip()
                    else:
                        valor = ''
                    results.append({
                        'mscd_code': mscd_code,
                        'message': message_template,
                        'value': valor,
                        'original_line': line
                    })
            i += 1
    # Guardar resultados en CSV
    fieldnames = ['mscd_code', 'message', 'value', 'original_line']
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        for result in results:
            writer.writerow(result)
    return len(results)

def main():
    if len(sys.argv) < 2:
        print("Usage: python mscd_extractor_out.py <out_file>")
        print("Example: python mscd_extractor_out.py c:\\Moldflow\\results.out")
        return
    
    out_file = sys.argv[1]
    if not os.path.exists(out_file):
        print(f"Error: File {out_file} does not exist.")
        return
    
    # File paths
    base_dir = os.path.dirname(os.path.abspath(__file__))
    mscd_csv = os.path.join(base_dir, 'mscd_codes.csv')
    
    if not os.path.exists(mscd_csv):
        print(f"Error: MSCD codes file not found at {mscd_csv}")
        print("Run mscd_extractor.py first to generate the codes file.")
        return
    
    # Output filename based on input .out file
    out_basename = os.path.basename(out_file)
    output_file = os.path.join(base_dir, f"extracted_{out_basename}.csv")
    
    print(f"Loading MSCD codes from {mscd_csv}...")
    mscd_dict = load_mscd_codes(mscd_csv)
    print(f"Loaded {len(mscd_dict)} MSCD codes.")
    
    print(f"Analyzing .out file: {out_file}...")
    count = extract_mscd_values(out_file, mscd_dict, output_file)
    print(f"Found {count} MSCD code values in the file.")
    print(f"Results saved to: {output_file}")

if __name__ == "__main__":
    main()