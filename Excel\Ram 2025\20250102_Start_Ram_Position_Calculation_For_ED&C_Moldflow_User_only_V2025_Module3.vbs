Attribute VB_Name = "Module3"

Sub imciimci()



Dim sht As Worksheet

Set sht = Sheets("SRP 계산하기")



On Error Resume Next

' 예외가 발생할 가능성이 있는 코드

' 화면 갱신 및 자동 계산 비활성화

'Application.ScreenUpdating = False

'Application.Calculation = xlCalculationManual



' 상시 업데이트를 제어하는 셀 (A1)

Dim controlCell As Range

Set controlCell = sht.Range("J2")

If controlCell.Value <> "Manual" Then Exit Sub



If Not Intersect(Target, Me.Range("F13")) Is Nothing Then

    Application.EnableEvents = False ' 이벤트 핸들러 재귀 호출 방지

    

    ' 영향을 받는 변수들의 범위를 정의합니다.

    Set affectedCells = sht.Range("F5")

    

    ' 영향을 받는 변수들의 값을 업데이트합니다.

    For Each updatedCell In affectedCells

        Select Case updatedCell.Address

            Case "$F$5"

                A = Cells(13, "F").Value

                B = Cells(11, "F").Value

                D = Cells(5, "C").Value

                E = Cells(12, "F").Value

                G = Cells(6, "F").Value

                Pi = 3.14159265358979

                k = 0.0078539816339

                

        End Select

    Next updatedCell

    

    Cells(6, "G").Value = Cells(5, "F").Value * Cells(6, "F").Value

    Cells(7, "F").Value = (Cells(5, "F").Value * Cells(5, "F").Value) * 3.14 / 4 / 100

    

    Application.EnableEvents = True ' 이벤트 핸들러 활성화

Else

    Application.EnableEvents = False

    

    Cells(7, "C").Value = Cells(6, "C").Value - Cells(5, "C").Value

    Cells(13, "C").Value = Cells(10, "C").Value + Cells(11, "C").Value + Cells(12, "C").Value

    Cells(6, "G").Value = Cells(5, "F").Value * Cells(6, "F").Value

    Cells(7, "F").Value = (Cells(5, "F").Value * Cells(5, "F").Value) * 3.14 / 4 / 100

    Cells(11, "F").Value = (Cells(10, "C").Value + Cells(11, "C").Value) * Cells(6, "C").Value

    Cells(11, "G").Value = (Cells(10, "C").Value + Cells(11, "C").Value) * Cells(5, "C").Value

    Cells(11, "H").Value = (Cells(10, "C").Value + Cells(11, "C").Value) * Cells(6, "C").Value * 0.95

    Cells(13, "F").Value = 10 * (Cells(11, "F").Value / (Cells(7, "F").Value * Cells(5, "C").Value * Cells(12, "F").Value)) + Cells(5, "F").Value * Cells(6, "F").Value

    Sheet2.Cells(17, "E") = Cells(13, "F").Value

    Sheet2.Cells(17, "F") = Sheet2.Cells(17, "D") * Cells(8, "F").Value / 100

    

    ' 예: B17부터 시작하는 마지막 행을 찾기

    lastRow = FindLastRow(17, "B")

    rowCount = lastRow - 17

    For i = 1 To rowCount

        Sheet2.Cells(17 + i, "E") = Sheet2.Cells(17 + i - 1, "E") - ((Cells(13, "F").Value - Cells(6, "G").Value) * ((Sheet2.Cells(17 + i, "C") - Sheet2.Cells(17 + i - 1, "C")) / 100))

        Sheet2.Cells(17 + i, "F") = Sheet2.Cells(17 + i, "D") * Cells(7, "K").Value / 100

    Next

    Application.EnableEvents = True

End If



End Sub





Function FindLastRow(startRow As Long, columnLetter As String) As Long

    Dim lastRow As Long

    

    ' 지정된 열의 마지막 행을 찾습니다.

    lastRow = Cells(Rows.count, columnLetter).End(xlUp).Row

    

    ' 시작 행부터 마지막 데이터를 찾습니다.

    If lastRow < startRow Then

        FindLastRow = -1 ' 시작 행 이후에 데이터가 없음을 나타내기 위해 -1 반환

    Else

        Do While IsEmpty(Cells(lastRow, columnLetter)) Or lastRow < startRow

            lastRow = lastRow - 1

        Loop

        

        If lastRow < startRow Then

            FindLastRow = -1 ' 시작 행 이후에 데이터가 없음을 나타내기 위해 -1 반환

        Else

            FindLastRow = lastRow

        End If

    End If

End Function

