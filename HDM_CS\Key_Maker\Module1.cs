﻿// Decompiled with JetBrains decompiler
// Type: Key_Maker.Module1
// Assembly: Key_Maker, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 43BE3483-FE2B-4F9D-8AD0-2D447D49E2D6
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\Key_Maker.exe

using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using Microsoft.Win32;
using System;
using System.Diagnostics;
using System.IO;
using System.Management;
using System.Runtime.CompilerServices;
using System.Security.Cryptography;
using System.Security.Principal;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace Key_Maker
{
  [StandardModule]
  internal sealed class Module1
  {
    private static TripleDESCryptoServiceProvider DES = new TripleDESCryptoServiceProvider();
    private static MD5CryptoServiceProvider MD5 = new MD5CryptoServiceProvider();

    [STAThread]
    public static void Main()
    {
      string str = Interaction.Command();
      Module1.RunAdmin();
      if (Operators.CompareString(str, "", false) != 0)
      {
        if (Operators.CompareString(Module1.ReadRegistry(str), "False", false) == 0 || !Operators.ConditionalCompareObjectEqual(Module1.MakeKey(), (object) "1", false))
          return;
        Module1.MakeEnabled(str);
        Module1.WriteRegistry(str, true);
        Module1.WriteProductName(str);
      }
      else
      {
        int num = (int) MessageBox.Show("제품명이 입력안됐습니다.");
      }
    }

    private static void RunAdmin()
    {
      if (Module1.IsAdministrator())
        return;
      try
      {
        Process.Start(new ProcessStartInfo()
        {
          UseShellExecute = true,
          FileName = Application.ExecutablePath,
          WorkingDirectory = Environment.CurrentDirectory,
          Verb = "runas"
        });
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Console.WriteLine(ex.Message.ToString());
        ProjectData.ClearProjectError();
      }
    }

    private static bool IsAdministrator() => new WindowsPrincipal(WindowsIdentity.GetCurrent()).IsInRole(WindowsBuiltInRole.Administrator);

    private static void WriteProductName(string product)
    {
      StreamWriter streamWriter = new StreamWriter(Application.StartupPath + "\\\\systems.dat", false, Encoding.UTF8);
      streamWriter.WriteLine(product);
      streamWriter.Close();
    }

    private static void WriteRegistry(string product, bool v) => Registry.LocalMachine.CreateSubKey("SOFTWARE\\\\WOW6432Node\\\\HDLicense", RegistryKeyPermissionCheck.ReadWriteSubTree).SetValue(product, (object) v, RegistryValueKind.String);

    private static void MakeEnabled(string command)
    {
      SaveFileDialog saveFileDialog = new SaveFileDialog();
      try
      {
        string str = Module1.Encrypt(Conversions.ToString(Operators.ConcatenateObject(Operators.ConcatenateObject(Operators.ConcatenateObject(Operators.ConcatenateObject(Operators.ConcatenateObject((object) (command + " "), Module1.getProcessor()), Module1.getBios()), Module1.getMainboard()), (object) " "), (object) "EnabledTRUE")), "delcam한국");
        StreamWriter streamWriter = new StreamWriter(Application.StartupPath + "\\delcamcodes.enabled", false, Encoding.UTF8);
        streamWriter.WriteLine(str);
        streamWriter.Close();
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        ProjectData.ClearProjectError();
      }
    }

    public static object MakeKey()
    {
      SaveFileDialog saveFileDialog = new SaveFileDialog();
      string computerName = SystemInformation.ComputerName;
      object obj;
      try
      {
        string str = Module1.Encrypt(Conversions.ToString(Operators.ConcatenateObject(Operators.ConcatenateObject(Module1.getProcessor(), Module1.getBios()), Module1.getMainboard())), "delcam한국");
        StreamWriter streamWriter = new StreamWriter(Application.StartupPath + "\\delcamcodes_" + Interaction.Command() + "_" + computerName + ".key", false, Encoding.UTF8);
        streamWriter.WriteLine(str);
        streamWriter.Close();
        obj = (object) "1";
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        int num = (int) MessageBox.Show("키 발행이 실패하였습니다.");
        obj = (object) "0";
        ProjectData.ClearProjectError();
      }
      return obj;
    }

    [MethodImpl(MethodImplOptions.NoInlining | MethodImplOptions.NoOptimization)]
    public static object getBios()
    {
      object bios;
      try
      {
        ManagementObjectSearcher managementObjectSearcher = new ManagementObjectSearcher("root\\CIMV2", "SELECT * FROM Win32_BIOS");
        string Left;
        try
        {
          foreach (ManagementBaseObject managementBaseObject in managementObjectSearcher.Get())
            Left = Conversions.ToString(managementBaseObject["SerialNumber"]);
        }
        finally
        {
          ManagementObjectCollection.ManagementObjectEnumerator objectEnumerator;
          objectEnumerator?.Dispose();
        }
        if (Operators.CompareString(Left, "", false) != 0)
        {
          bios = (object) Left;
        }
        else
        {
          int num = (int) MessageBox.Show("시스템 값을 가져 올 수 없습니다.");
          ProjectData.EndApp();
        }
      }
      catch (ManagementException ex)
      {
        ProjectData.SetProjectError((Exception) ex);
        int num = (int) MessageBox.Show("An error occurred while querying for WMI data: " + ex.Message);
        ProjectData.ClearProjectError();
      }
      return bios;
    }

    [MethodImpl(MethodImplOptions.NoInlining | MethodImplOptions.NoOptimization)]
    public static object getProcessor()
    {
      object processor;
      try
      {
        ManagementObjectSearcher managementObjectSearcher = new ManagementObjectSearcher("root\\CIMV2", "SELECT * FROM Win32_Processor");
        string Left;
        try
        {
          foreach (ManagementBaseObject managementBaseObject in managementObjectSearcher.Get())
            Left = Conversions.ToString(managementBaseObject["ProcessorId"]);
        }
        finally
        {
          ManagementObjectCollection.ManagementObjectEnumerator objectEnumerator;
          objectEnumerator?.Dispose();
        }
        if (Operators.CompareString(Left, "", false) != 0)
        {
          processor = (object) Left;
        }
        else
        {
          int num = (int) MessageBox.Show("시스템 값을 가져 올 수 없습니다.");
          ProjectData.EndApp();
        }
      }
      catch (ManagementException ex)
      {
        ProjectData.SetProjectError((Exception) ex);
        int num = (int) MessageBox.Show("An error occurred while querying for WMI data: " + ex.Message);
        ProjectData.ClearProjectError();
      }
      return processor;
    }

    [MethodImpl(MethodImplOptions.NoInlining | MethodImplOptions.NoOptimization)]
    public static object getMainboard()
    {
      object mainboard;
      try
      {
        ManagementObjectSearcher managementObjectSearcher = new ManagementObjectSearcher("root\\CIMV2", "SELECT * FROM Win32_BaseBoard");
        string Left;
        try
        {
          foreach (ManagementBaseObject managementBaseObject in managementObjectSearcher.Get())
            Left = Conversions.ToString(managementBaseObject["SerialNumber"]);
        }
        finally
        {
          ManagementObjectCollection.ManagementObjectEnumerator objectEnumerator;
          objectEnumerator?.Dispose();
        }
        if (Operators.CompareString(Left, "", false) != 0)
        {
          mainboard = (object) Left;
        }
        else
        {
          int num = (int) MessageBox.Show("시스템 값을 가져 올 수 없습니다.");
          ProjectData.EndApp();
        }
      }
      catch (ManagementException ex)
      {
        ProjectData.SetProjectError((Exception) ex);
        int num = (int) MessageBox.Show("An error occurred while querying for WMI data: " + ex.Message);
        ProjectData.ClearProjectError();
      }
      return mainboard;
    }

    public static byte[] MD5Hash(string value) => Module1.MD5.ComputeHash(Encoding.ASCII.GetBytes(value));

    public static string Encrypt(string stringToEncrypt, string key)
    {
      Module1.DES.Key = Module1.MD5Hash(key);
      Module1.DES.Mode = CipherMode.ECB;
      byte[] bytes = Encoding.ASCII.GetBytes(stringToEncrypt);
      return Convert.ToBase64String(Module1.DES.CreateEncryptor().TransformFinalBlock(bytes, 0, bytes.Length));
    }

    public static string Decrypt(string encryptedString, string key)
    {
      string str;
      try
      {
        Module1.DES.Key = Module1.MD5Hash(key);
        Module1.DES.Mode = CipherMode.ECB;
        byte[] inputBuffer = Convert.FromBase64String(encryptedString);
        str = Encoding.ASCII.GetString(Module1.DES.CreateDecryptor().TransformFinalBlock(inputBuffer, 0, inputBuffer.Length));
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        int num = (int) MessageBox.Show("The encryption key specified was not appropriate for decryption.", "Invalid Enryption Key", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
        str = "";
        ProjectData.ClearProjectError();
      }
      return str;
    }

    private static void MonthCalendar1_MouseDown(object sender, MouseEventArgs e) => e.Clicks.ToString();

    private static void MonthCalendar1_DateChanged(object sender, DateRangeEventArgs e)
    {
    }

    public static void WriteRegistry(string para1)
    {
    }

    public static string ReadRegistry(string regVal)
    {
      string str;
      try
      {
        RegistryKey registryKey = Registry.LocalMachine.OpenSubKey("SOFTWARE\\\\WOW6432Node\\\\HDLicense", RegistryKeyPermissionCheck.ReadSubTree);
        Thread.Sleep(1000);
        if (Operators.CompareString(registryKey.ToString(), (string) null, false) == 0)
          str = "";
        if (Operators.ConditionalCompareObjectNotEqual((object) null, registryKey.GetValue(regVal), false))
          str = Convert.ToString(RuntimeHelpers.GetObjectValue(registryKey.GetValue(regVal)));
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        str = (string) null;
        ProjectData.ClearProjectError();
      }
      return str;
    }

    public static void DeleteRegistry() => Registry.LocalMachine.DeleteSubKey("Software\\\\TEST");
  }
}
