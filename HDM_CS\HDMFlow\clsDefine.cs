﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.clsDefine
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDMoldFlowLibrary;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public class clsDefine
  {
    public static clsDefine.LicLevel enumLicLevel = clsDefine.LicLevel.Light;
    public const string g_strLicensePath = "C:\\dcam\\config\\HDPass";
    public static FileInfo g_fiDefaultCfg = new FileInfo(Application.StartupPath + "\\Default.ini");
    public static string g_strPath = "C:\\HDSolutions\\HDMFlow";
    public static clsHDMFLibDefine.Company g_enumCompany = clsHDMFLibDefine.Company.HDSolutions;
    public static Dictionary<string, clsDefine.LicLevel> g_dicEnabledVersion = new Dictionary<string, clsDefine.LicLevel>();
    public static string g_strScmPort = "44100";
    public static string g_strLanguageType = "KOR";
    public static int g_iLanguageIndex = 0;
    public static string g_strLocaleFPath = Application.StartupPath + "\\HDMFlow_Locale.xml";
    public static string g_strMoldflowFPath = Application.StartupPath + "\\Moldflow_Locale.xml";
    public static FileInfo g_fiLangCfgDefault = new FileInfo(Application.StartupPath + "\\Config\\Language.ini");
    public static FileInfo g_fiLangCfg;
    public static Dictionary<string, string> g_dicLangType = new Dictionary<string, string>();
    public static FileInfo g_fiNetworkLicenseCfg = new FileInfo(Application.StartupPath + "\\Network.ini");
    public static string g_strWebURL = string.Empty;
    public static int g_iCheckTime = 1800000;
    public static bool g_isNetworkLicense = false;
    public static bool g_isAILicense = false;
    public static DirectoryInfo g_diProject;
    public static DirectoryInfo g_diBasicProject;
    public static FileInfo g_fiProject;
    public static FileInfo g_fiProjCfgDefault = new FileInfo(Application.StartupPath + "\\Config\\Project.ini");
    public static FileInfo g_fiProjCfg;
    public static DirectoryInfo g_diBigData;
    public static FileInfo g_fiBigDataCfg;
    public static Dictionary<string, string> g_dicMesh;
    public static FileInfo g_fiMeshCfgDefault = new FileInfo(Application.StartupPath + "\\Config\\Mesh.ini");
    public static FileInfo g_fiMeshCfg;
    public static Dictionary<string, string> g_dicMeshStat;
    public static FileInfo g_fiMeshStatCfgDefault = new FileInfo(Application.StartupPath + "\\Config\\MeshStat.ini");
    public static FileInfo g_fiMeshStatCfg;
    public static DataTable g_dtRotate;
    public static FileInfo g_fiRotateCfg;
    public static DataTable g_dtRunnerDB;
    public static DirectoryInfo g_diRunnerDBCfgDefault = new DirectoryInfo(Application.StartupPath + "\\DB\\Runner");
    public static DirectoryInfo g_diRunnerDBCfg;
    public static Dictionary<string, string> g_dicMidResult;
    public static FileInfo g_fiMidResultCfgDefault = new FileInfo(Application.StartupPath + "\\Config\\MidResult.ini");
    public static FileInfo g_fiMidResultCfg;
    public static DataTable g_dtCase;
    public static Dictionary<string, string> g_dicGate;
    public static FileInfo g_fiGateCfgDefault = new FileInfo(Application.StartupPath + "\\Config\\Gate.ini");
    public static FileInfo g_fiGateCfg;
    public static clsHDMFLibDefine.SummaryType enumSummaryType = clsHDMFLibDefine.SummaryType.Basic;
    public static FileInfo g_fiSummaryCfgDefault = new FileInfo(Application.StartupPath + "\\Config\\Summary.ini");
    public static FileInfo g_fiSummaryCfg;
    public static FileInfo g_fiAICfgDefault = new FileInfo(Application.StartupPath + "\\Config\\AI.ini");
    public static FileInfo g_fiAICfg;
    public static DataTable g_dtInputDB;
    public static DirectoryInfo g_diInputDBCfgDefault = new DirectoryInfo(Application.StartupPath + "\\DB\\Input");
    public static DirectoryInfo g_diInputDBCfg;
    public static DirectoryInfo g_diSummaryViewCfgDefault = new DirectoryInfo(Application.StartupPath + "\\Config\\SummaryView");
    public static FileInfo g_fiSummaryViewOptionCfg;
    public static DataTable g_dtSummaryView;
    public static DataTable g_dtProcessDB;
    public static DirectoryInfo g_diProcessDBCfgDefault = new DirectoryInfo(Application.StartupPath + "\\DB\\Process");
    public static DirectoryInfo g_diProcessDBCfg;
    public static DirectoryInfo g_diProcessUDBCfgDefault = new DirectoryInfo(Application.StartupPath + "\\UDB\\Process");
    public static DirectoryInfo g_diProcessUDBCfg;
    public static DataTable g_dtInjectionDB;
    public static DirectoryInfo g_diInjectionDBCfgDefault = new DirectoryInfo(Application.StartupPath + "\\DB\\Injection");
    public static DirectoryInfo g_diInjectionDBCfg;
    public static DirectoryInfo g_diReportViewCfgDefault = new DirectoryInfo(Application.StartupPath + "\\Config\\ReportView");
    public static string g_strMaterial = "MaterialData.xml";
    public static string g_strUdbFolder = "My AMI 2021.1 Projects";
    public static DataSet g_dsMaterial;
    public static FileInfo g_fiMaterialCfgDefault = new FileInfo(Application.StartupPath + "\\DB\\Material\\" + clsDefine.g_strMaterial);
    public static FileInfo g_fiMaterialCfg;
    public static DirectoryInfo g_diUserUDB = new DirectoryInfo(Environment.GetFolderPath(Environment.SpecialFolder.Personal) + "\\" + clsDefine.g_strUdbFolder + "\\udb");
    public static DirectoryInfo g_diTemplateDefault = new DirectoryInfo(Application.StartupPath + "\\Template");
    public static DirectoryInfo g_diTemplate;
    public static Dictionary<string, string> g_dicValve;
    public static FileInfo g_fiValveCfgDefault = new FileInfo(Application.StartupPath + "\\Config\\Valve.ini");
    public static FileInfo g_fiValveCfg;
    public static DirectoryInfo g_diCfg;
    public static DirectoryInfo g_diTmpReport;
    public static DirectoryInfo g_diTmpAI;
    public static Dictionary<string, string> g_dicExtension;
    public static FileInfo g_fiExtensionCfgDefault = new FileInfo(Application.StartupPath + "\\Config\\Extension.ini");
    public static FileInfo g_fiExtensionCfg;
    public static bool g_isMoveZero = true;
    public static bool g_isCreate3D = false;

    public static DataSet g_dsProduct { get; set; }

    public enum LicLevel
    {
      Light,
      Standard,
      Premium,
      Ultimate,
    }

    public enum Status
    {
      NOT_READY,
      READY,
      COMPLETED,
    }

    public enum Analysis
    {
      FAIELD,
      CANCELED,
      COMPLETE,
    }
  }
}
