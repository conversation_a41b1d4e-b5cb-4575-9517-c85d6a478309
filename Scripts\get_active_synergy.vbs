'%RunPerInstance
Option Explicit

' Script para obtener la instancia activa de Synergy
Dim WshShell, objFSO
Set WshShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' Intentar obtener la instancia activa a través de ROT
Dim GetActiveObject
Set GetActiveObject = GetObject("winmgmts:\\.\root\CIMV2")
Dim colProcesses
Set colProcesses = GetActiveObject.ExecQuery("SELECT ProcessId,CommandLine FROM Win32_Process WHERE Name='synergy.exe'")

Dim process, pid, commandLine
Dim found : found = False

For Each process in colProcesses
    pid = process.ProcessId
    commandLine = process.CommandLine
    WScript.Echo "Found Synergy process: PID=" & pid & ", Command=" & commandLine
    found = True
    
    ' Intentar diferentes métodos de conexión
    On Error Resume Next
    
    ' Método 1: Intentar GetObject directo
    Dim Synergy
    Set Synergy = GetObject("synergy.Synergy")
    If Not Synergy Is Nothing Then
        WScript.Echo "Method 1: Connected successfully"
        WScript.Quit(0)
    End If
    
    ' Método 2: Intentar a través de ProgID
    Set Synergy = GetObject("", "synergy.Synergy")
    If Not Synergy Is Nothing Then
        WScript.Echo "Method 2: Connected successfully"
        WScript.Quit(0)
    End If
    
    ' Método 3: Intentar crear nuevo y conectar
    Set Synergy = CreateObject("synergy.Synergy")
    If Not Synergy Is Nothing Then
        WScript.Echo "Method 3: Created new instance"
        ' Guardar el moniker en un archivo temporal
        Dim tempFile
        Set tempFile = objFSO.CreateTextFile(objFSO.GetSpecialFolder(2) & "\synergy_moniker.txt", True)
        tempFile.WriteLine GetActiveObject.GetObjectText(Synergy)
        tempFile.Close
        WScript.Quit(0)
    End If
    
    On Error GoTo 0
Next

If Not found Then
    WScript.Echo "No active Synergy instance found"
    WScript.Quit(1)
End If

WScript.Echo "Failed to connect using any method"
WScript.Quit(2)
