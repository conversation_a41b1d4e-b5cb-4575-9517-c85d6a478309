{"version": 2, "dgSpecHash": "NmsQfjSclWY=", "success": true, "projectFilePath": "C:\\Moldflow\\SevenZipExtractor\\SevenZipExtractor.Tests\\SevenZipExtractor.Tests.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codecoverage\\16.4.0\\microsoft.codecoverage.16.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.test.sdk\\16.4.0\\microsoft.net.test.sdk.16.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mstest.testadapter\\1.3.2\\mstest.testadapter.1.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mstest.testframework\\1.3.2\\mstest.testframework.1.3.2.nupkg.sha512"], "logs": []}