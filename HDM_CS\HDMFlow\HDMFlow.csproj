﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!--Project was exported from assembly: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe-->
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{31DAF7D0-254A-42D6-B5B9-ED0B65E182D1}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AssemblyName>HDMFlow</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <ApplicationVersion>1.0.0.0</ApplicationVersion>
    <FileAlignment>512</FileAlignment>
    <RootNamespace>HDMoldFlow</RootNamespace>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ArSetting.Net4">
      <HintPath>lib\ArSetting.Net4.dll</HintPath>
    </Reference>
    <Reference Include="HDLocale">
      <HintPath>lib\HDLocale.dll</HintPath>
    </Reference>
    <Reference Include="HDLog4Net">
      <HintPath>lib\HDLog4Net.dll</HintPath>
    </Reference>
    <Reference Include="HDMFAI">
      <HintPath>lib\HDMFAI.dll</HintPath>
    </Reference>
    <Reference Include="HDMFReport">
      <HintPath>lib\HDMFReport.dll</HintPath>
    </Reference>
    <Reference Include="HDMFSummary">
      <HintPath>lib\HDMFSummary.dll</HintPath>
    </Reference>
    <Reference Include="HDMFUserControl">
      <HintPath>lib\HDMFUserControl.dll</HintPath>
    </Reference>
    <Reference Include="HDMoldFlowLibrary">
      <HintPath>lib\HDMoldFlowLibrary.dll</HintPath>
    </Reference>
    <Reference Include="HDSplash">
      <HintPath>lib\HDSplash.dll</HintPath>
    </Reference>
    <Reference Include="LSK">
      <HintPath>lib\LSK.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAPICodePack.Shell">
      <HintPath>lib\Microsoft.WindowsAPICodePack.Shell.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>lib\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Windows.Forms.Ribbon">
      <HintPath>lib\System.Windows.Forms.Ribbon.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="clsClasses.cs" />
    <Compile Include="1clsClasses.cs" />
    <Compile Include="2clsClasses.cs" />
    <Compile Include="3clsClasses.cs" />
    <Compile Include="4clsClasses.cs" />
    <Compile Include="5clsClasses.cs" />
    <Compile Include="clsData.cs" />
    <Compile Include="clsDefine.cs" />
    <Compile Include="clsMesh.cs" />
    <Compile Include="clsLicense.cs" />
    <Compile Include="clsSCM.cs" />
    <Compile Include="clsUtill.cs" />
    <Compile Include="frmAISolution.cs" />
    <Compile Include="frmBigData.cs" />
    <Compile Include="frmInput.cs" />
    <Compile Include="frmInputDB.cs" />
    <Compile Include="frmRunner.cs" />
    <Compile Include="frmValve.cs" />
    <Compile Include="frmNetworkLicense.cs" />
    <Compile Include="frmRotate.cs" />
    <Compile Include="frmMeshStat.cs" />
    <Compile Include="frmPinGate.cs" />
    <Compile Include="frmGate.cs" />
    <Compile Include="frmSummaryView.cs" />
    <Compile Include="frmCase.cs" />
    <Compile Include="frmCool.cs" />
    <Compile Include="frmDualMesh.cs" />
    <Compile Include="frmLanguage.cs" />
    <Compile Include="frmInfo.cs" />
    <Compile Include="frmInjection.cs" />
    <Compile Include="frmMain.cs" />
    <Compile Include="frmMesh.cs" />
    <Compile Include="frmMidResult.cs" />
    <Compile Include="frmProcess.cs" />
    <Compile Include="frmProcessDB.cs" />
    <Compile Include="frmProgress.cs" />
    <Compile Include="frmProject.cs" />
    <Compile Include="frmRunnerDB.cs" />
    <Compile Include="frmUpdateMesh.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\Resources.Designer.cs" />
    <Compile Include="Properties\Settings.Designer.cs" />
    <Compile Include="AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="frmAISolution.resx" />
    <EmbeddedResource Include="frmBigData.resx" />
    <EmbeddedResource Include="frmCase.resx" />
    <EmbeddedResource Include="frmCool.resx" />
    <EmbeddedResource Include="frmDualMesh.resx" />
    <EmbeddedResource Include="frmGate.resx" />
    <EmbeddedResource Include="frmInfo.resx" />
    <EmbeddedResource Include="frmInjection.resx" />
    <EmbeddedResource Include="frmInput.resx" />
    <EmbeddedResource Include="frmInputDB.resx" />
    <EmbeddedResource Include="frmLanguage.resx" />
    <EmbeddedResource Include="frmMain.resx" />
    <EmbeddedResource Include="frmMesh.resx" />
    <EmbeddedResource Include="frmMeshStat.resx" />
    <EmbeddedResource Include="frmMidResult.resx" />
    <EmbeddedResource Include="frmNetworkLicense.resx" />
    <EmbeddedResource Include="frmPinGate.resx" />
    <EmbeddedResource Include="frmProcess.resx" />
    <EmbeddedResource Include="frmProcessDB.resx" />
    <EmbeddedResource Include="frmProgress.resx" />
    <EmbeddedResource Include="frmProject.resx" />
    <EmbeddedResource Include="frmRotate.resx" />
    <EmbeddedResource Include="frmRunner.resx" />
    <EmbeddedResource Include="frmRunnerDB.resx" />
    <EmbeddedResource Include="frmSummaryView.resx" />
    <EmbeddedResource Include="frmUpdateMesh.resx" />
    <EmbeddedResource Include="frmValve.resx" />
    <EmbeddedResource Include="Properties\Resources.resx" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>