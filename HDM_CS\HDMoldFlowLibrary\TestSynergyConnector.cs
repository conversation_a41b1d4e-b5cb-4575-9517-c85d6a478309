using System;
using HDMoldFlowLibrary;

namespace HDMoldFlowLibrary
{
    class TestSynergyConnector
    {
        static void Main(string[] args)
        {
            try
            {
                using (var connector = new SynergyConnector())
                {
                    var project = connector.GetProject();
                    var studyDoc = connector.GetStudyDoc();
                    if (project == null)
                    {
                        Console.WriteLine("Error: El objeto Project es nulo.");
                    }
                    else
                    {
                        try
                        {
                            var version = connector.GetType().GetProperty("Version") != null ? connector.GetType().GetProperty("Version").GetValue(connector) : null;
                            Console.WriteLine($"Synergy Version: {version ?? "(no disponible)"}");
                        }
                        catch { }
                        Console.WriteLine("Project obtenido correctamente.");
                    }
                    if (studyDoc == null)
                    {
                        Console.WriteLine("Error: El objeto StudyDoc es nulo.");
                    }
                    else
                    {
                        Console.WriteLine("StudyDoc obtenido correctamente.");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Excepción: {ex.Message}");
            }
            Console.WriteLine("Presione cualquier tecla para salir...");
            Console.ReadKey();
        }
    }
}