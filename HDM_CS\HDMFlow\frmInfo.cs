﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmInfo
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDMFUserControl;
using HDMoldFlow.Properties;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public class frmInfo : Form
  {
    public string m_strVersion;
    private IContainer components = (IContainer) null;
    private PictureBox pictureBox1;
    private Label label_VersionTitle;
    private Label label_Version;
    private NewButton newButton_Apply;

    public frmInfo()
    {
      this.InitializeComponent();
      this.Text = LocaleControl.getInstance().GetString("IDS_INFO");
      this.label_VersionTitle.Text = LocaleControl.getInstance().GetString("IDS_VERSION") + ":";
      this.newButton_Apply.ButtonText = LocaleControl.getInstance().GetString("IDS_OK");
    }

    private void frmInfo_Load(object sender, EventArgs e) => this.label_Version.Text = this.m_strVersion;

    private void frmInfo_KeyDown(object sender, KeyEventArgs e)
    {
      if (e.KeyCode != Keys.Escape)
        return;
      this.Close();
    }

    private void newButton1_NewClick(object sender, EventArgs e) => this.Close();

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.pictureBox1 = new PictureBox();
      this.label_VersionTitle = new Label();
      this.label_Version = new Label();
      this.newButton_Apply = new NewButton();
      ((ISupportInitialize) this.pictureBox1).BeginInit();
      this.SuspendLayout();
      this.pictureBox1.Image = (Image) Resources.HDSolutions;
      this.pictureBox1.Location = new Point(3, 2);
      this.pictureBox1.Name = "pictureBox1";
      this.pictureBox1.Size = new Size(161, 48);
      this.pictureBox1.SizeMode = PictureBoxSizeMode.Zoom;
      this.pictureBox1.TabIndex = 0;
      this.pictureBox1.TabStop = false;
      this.label_VersionTitle.AutoSize = true;
      this.label_VersionTitle.Location = new Point(39, 55);
      this.label_VersionTitle.Name = "label_VersionTitle";
      this.label_VersionTitle.Size = new Size(33, 12);
      this.label_VersionTitle.TabIndex = 2;
      this.label_VersionTitle.Text = "버전:";
      this.label_VersionTitle.TextAlign = ContentAlignment.MiddleLeft;
      this.label_Version.Location = new Point(75, 53);
      this.label_Version.Name = "label_Version";
      this.label_Version.Size = new Size(85, 17);
      this.label_Version.TabIndex = 3;
      this.label_Version.Text = "v0.0.0.0";
      this.label_Version.TextAlign = ContentAlignment.MiddleLeft;
      this.newButton_Apply.ButtonBackColor = Color.White;
      this.newButton_Apply.ButtonText = "확인";
      this.newButton_Apply.FlatBorderSize = 1;
      this.newButton_Apply.FlatStyle = FlatStyle.Flat;
      this.newButton_Apply.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Apply.Image = (Image) null;
      this.newButton_Apply.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.Location = new Point(47, 73);
      this.newButton_Apply.Name = "newButton_Apply";
      this.newButton_Apply.Size = new Size(75, 23);
      this.newButton_Apply.TabIndex = 4;
      this.newButton_Apply.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Apply.NewClick += new EventHandler(this.newButton1_NewClick);
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(167, 99);
      this.ControlBox = false;
      this.Controls.Add((Control) this.newButton_Apply);
      this.Controls.Add((Control) this.label_VersionTitle);
      this.Controls.Add((Control) this.label_Version);
      this.Controls.Add((Control) this.pictureBox1);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.Name = nameof (frmInfo);
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "HDMFlow정보";
      this.Load += new EventHandler(this.frmInfo_Load);
      this.KeyDown += new KeyEventHandler(this.frmInfo_KeyDown);
      ((ISupportInitialize) this.pictureBox1).EndInit();
      this.ResumeLayout(false);
      this.PerformLayout();
    }
  }
}
