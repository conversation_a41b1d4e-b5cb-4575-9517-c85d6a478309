﻿// Decompiled with JetBrains decompiler
// Type: HDMFReport.Properties.Resources
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;

namespace HDMFReport.Properties
{
  [GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
  [DebuggerNonUserCode]
  [CompilerGenerated]
  internal class Resources
  {
    private static ResourceManager resourceMan;
    private static CultureInfo resourceCulture;

    internal Resources()
    {
    }

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    internal static ResourceManager ResourceManager
    {
      get
      {
        if (HDMFReport.Properties.Resources.resourceMan == null)
          HDMFReport.Properties.Resources.resourceMan = new ResourceManager("HDMFReport.Properties.Resources", typeof (HDMFReport.Properties.Resources).Assembly);
        return HDMFReport.Properties.Resources.resourceMan;
      }
    }

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    internal static CultureInfo Culture
    {
      get => HDMFReport.Properties.Resources.resourceCulture;
      set => HDMFReport.Properties.Resources.resourceCulture = value;
    }

    internal static Bitmap OK => (Bitmap) HDMFReport.Properties.Resources.ResourceManager.GetObject(nameof (OK), HDMFReport.Properties.Resources.resourceCulture);
  }
}
