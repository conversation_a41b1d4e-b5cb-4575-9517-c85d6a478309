<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="pictureBox_Pin.BackgroundImage" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>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*************************************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</value>
  </data>
</root>