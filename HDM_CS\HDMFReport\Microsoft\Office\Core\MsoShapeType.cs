﻿// Decompiled with JetBrains decompiler
// Type: Microsoft.Office.Core.MsoShapeType
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

namespace Microsoft.Office.Core
{
  [CompilerGenerated]
  [TypeIdentifier("2df8d04c-5bfa-101b-bde5-00aa0044de52", "Microsoft.Office.Core.MsoShapeType")]
  public enum MsoShapeType
  {
    msoShapeTypeMixed = -2, // 0xFFFFFFFE
    msoAutoShape = 1,
    msoCallout = 2,
    msoChart = 3,
    msoComment = 4,
    msoFreeform = 5,
    msoGroup = 6,
    msoEmbeddedOLEObject = 7,
    msoFormControl = 8,
    msoLine = 9,
    msoLinkedOLEObject = 10, // 0x0000000A
    msoLinkedPicture = 11, // 0x0000000B
    msoOLEControlObject = 12, // 0x0000000C
    msoPicture = 13, // 0x0000000D
    msoPlaceholder = 14, // 0x0000000E
    msoTextEffect = 15, // 0x0000000F
    msoMedia = 16, // 0x00000010
    msoTextBox = 17, // 0x00000011
    msoScriptAnchor = 18, // 0x00000012
    msoTable = 19, // 0x00000013
    msoCanvas = 20, // 0x00000014
    msoDiagram = 21, // 0x00000015
    msoInk = 22, // 0x00000016
    msoInkComment = 23, // 0x00000017
    msoSmartArt = 24, // 0x00000018
    msoSlicer = 25, // 0x00000019
    msoWebVideo = 26, // 0x0000001A
    msoContentApp = 27, // 0x0000001B
  }
}
