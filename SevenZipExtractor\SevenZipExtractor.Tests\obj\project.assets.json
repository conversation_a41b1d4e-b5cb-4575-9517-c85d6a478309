{"version": 3, "targets": {".NETFramework,Version=v4.5": {"Microsoft.CodeCoverage/16.4.0": {"type": "package", "compile": {"lib/net45/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {}}, "runtime": {"lib/net45/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {}}, "build": {"build/netstandard1.0/Microsoft.CodeCoverage.props": {}, "build/netstandard1.0/Microsoft.CodeCoverage.targets": {}}}, "Microsoft.NET.Test.Sdk/16.4.0": {"type": "package", "dependencies": {"Microsoft.CodeCoverage": "16.4.0"}, "build": {"build/net40/Microsoft.NET.Test.Sdk.props": {}, "build/net40/Microsoft.NET.Test.Sdk.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.NET.Test.Sdk.props": {}}}, "MSTest.TestAdapter/1.3.2": {"type": "package", "build": {"build/net45/MSTest.TestAdapter.props": {}, "build/net45/MSTest.TestAdapter.targets": {}}}, "MSTest.TestFramework/1.3.2": {"type": "package", "compile": {"lib/net45/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll": {"related": ".XML"}, "lib/net45/Microsoft.VisualStudio.TestPlatform.TestFramework.dll": {"related": ".Extensions.XML;.XML"}}, "runtime": {"lib/net45/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll": {"related": ".XML"}, "lib/net45/Microsoft.VisualStudio.TestPlatform.TestFramework.dll": {"related": ".Extensions.XML;.XML"}}}, "SevenZipExtractor/1.0.19": {"type": "project", "framework": ".NETFramework,Version=v4.5", "compile": {"bin/placeholder/SevenZipExtractor.dll": {}}, "runtime": {"bin/placeholder/SevenZipExtractor.dll": {}}}}}, "libraries": {"Microsoft.CodeCoverage/16.4.0": {"sha512": "qb7PMVZMAY5iUCvB/kDUEt9xqazWKZoKY/sGpAJO4VtwgN5IcEgipCu3n0i1GPCwGbWVL6k4a8a9F+FqmADJng==", "type": "package", "path": "microsoft.codecoverage/16.4.0", "files": [".nupkg.metadata", ".signature.p7s", "build/netstandard1.0/CodeCoverage/CodeCoverage.config", "build/netstandard1.0/CodeCoverage/CodeCoverage.exe", "build/netstandard1.0/CodeCoverage/amd64/covrun64.dll", "build/netstandard1.0/CodeCoverage/amd64/msdia140.dll", "build/netstandard1.0/CodeCoverage/codecoveragemessages.dll", "build/netstandard1.0/CodeCoverage/covrun32.dll", "build/netstandard1.0/CodeCoverage/msdia140.dll", "build/netstandard1.0/Microsoft.CodeCoverage.props", "build/netstandard1.0/Microsoft.CodeCoverage.targets", "build/netstandard1.0/Microsoft.VisualStudio.TraceDataCollector.dll", "build/netstandard1.0/cs/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard1.0/de/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard1.0/es/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard1.0/fr/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard1.0/it/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard1.0/ja/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard1.0/ko/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard1.0/pl/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard1.0/pt-BR/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard1.0/ru/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard1.0/tr/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard1.0/zh-<PERSON>/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "build/netstandard1.0/zh-Hant/Microsoft.VisualStudio.TraceDataCollector.resources.dll", "lib/net45/Microsoft.VisualStudio.CodeCoverage.Shim.dll", "lib/netcoreapp1.0/Microsoft.VisualStudio.CodeCoverage.Shim.dll", "microsoft.codecoverage.16.4.0.nupkg.sha512", "microsoft.codecoverage.nuspec"]}, "Microsoft.NET.Test.Sdk/16.4.0": {"sha512": "gjjqS3rCzg4DrSQq4YwJwUUvc49HtXpXrZkW9fu5VG+K4X+Ztn4+UzolyML7wPnl/EAIufk4hu628bJB4WtpFg==", "type": "package", "path": "microsoft.net.test.sdk/16.4.0", "files": [".nupkg.metadata", ".signature.p7s", "build/net40/Microsoft.NET.Test.Sdk.props", "build/net40/Microsoft.NET.Test.Sdk.targets", "build/netcoreapp2.1/Microsoft.NET.Test.Sdk.Program.cs", "build/netcoreapp2.1/Microsoft.NET.Test.Sdk.Program.fs", "build/netcoreapp2.1/Microsoft.NET.Test.Sdk.Program.vb", "build/netcoreapp2.1/Microsoft.NET.Test.Sdk.props", "build/netcoreapp2.1/Microsoft.NET.Test.Sdk.targets", "build/uap10.0/Microsoft.NET.Test.Sdk.props", "buildMultiTargeting/Microsoft.NET.Test.Sdk.props", "microsoft.net.test.sdk.16.4.0.nupkg.sha512", "microsoft.net.test.sdk.nuspec"]}, "MSTest.TestAdapter/1.3.2": {"sha512": "ZUof05vDwa2IMhzceN31KBR1wrnrpLf4mKGMCrnCaP7QjuxMdQjagBlIYzRJBS6OoBi8l/qj1S21Bo2qzqVl9w==", "type": "package", "path": "mstest.testadapter/1.3.2", "files": [".nupkg.metadata", ".signature.p7s", "build/_common/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.dll", "build/_common/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.Interface.dll", "build/_common/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/_common/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "build/_common/cs/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/cs/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_common/de/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/de/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_common/es/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/es/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_common/fr/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/fr/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_common/it/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/it/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_common/ja/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/ja/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_common/ko/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/ko/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_common/pl/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/pl/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_common/pt/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/pt/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/pt/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_common/ru/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/ru/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_common/tr/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/tr/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_common/zh-Hans/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/zh-Hans/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/zh-Hans/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/_common/zh-Hant/Microsoft.VisualStudio.TestPlatform.MSTest.TestAdapter.resources.dll", "build/_common/zh-Hant/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.resources.dll", "build/_common/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll", "build/net45/MSTest.TestAdapter.props", "build/net45/MSTest.TestAdapter.targets", "build/netcoreapp1.0/MSTest.TestAdapter.props", "build/netcoreapp1.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "build/uap10.0/MSTest.TestAdapter.props", "build/uap10.0/MSTest.TestAdapter.targets", "build/uap10.0/Microsoft.VisualStudio.TestPlatform.MSTestAdapter.PlatformServices.dll", "mstest.testadapter.1.3.2.nupkg.sha512", "mstest.testadapter.nuspec"]}, "MSTest.TestFramework/1.3.2": {"sha512": "RC0EruieQM+TNh5TfRtENgqorleO4s4XRyYasFZHDapuIFfeuy0BkPu4Qc5W0HKvLKwJdbb6WoSGxFRqmUA3Lg==", "type": "package", "path": "mstest.testframework/1.3.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.XML", "lib/net45/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/net45/Microsoft.VisualStudio.TestPlatform.TestFramework.XML", "lib/net45/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/net45/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net45/de/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/de/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net45/es/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/es/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net45/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net45/it/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/it/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net45/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net45/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net45/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net45/pt/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/pt/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net45/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net45/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net45/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/net45/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/net45/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.XML", "lib/netstandard1.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/netstandard1.0/Microsoft.VisualStudio.TestPlatform.TestFramework.XML", "lib/netstandard1.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/netstandard1.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/pt/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/pt/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/netstandard1.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/netstandard1.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.XML", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.XML", "lib/uap10.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll", "lib/uap10.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/pt/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/pt/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/zh-<PERSON>/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "lib/uap10.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.xml", "lib/uap10.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.xml", "mstest.testframework.1.3.2.nupkg.sha512", "mstest.testframework.nuspec"]}, "SevenZipExtractor/1.0.19": {"type": "project", "path": "../SevenZipExtractor/SevenZipExtractor.csproj", "msbuildProject": "../SevenZipExtractor/SevenZipExtractor.csproj"}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.5": ["MSTest.TestAdapter >= 1.3.2", "MSTest.TestFramework >= 1.3.2", "Microsoft.NET.Test.Sdk >= 16.4.0", "SevenZipExtractor >= 1.0.19"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Moldflow\\SevenZipExtractor\\SevenZipExtractor.Tests\\SevenZipExtractor.Tests.csproj", "projectName": "SevenZipExtractor.Tests", "projectPath": "C:\\Moldflow\\SevenZipExtractor\\SevenZipExtractor.Tests\\SevenZipExtractor.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Moldflow\\SevenZipExtractor\\SevenZipExtractor.Tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net45"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net45": {"targetAlias": "net45", "projectReferences": {"C:\\Moldflow\\SevenZipExtractor\\SevenZipExtractor\\SevenZipExtractor.csproj": {"projectPath": "C:\\Moldflow\\SevenZipExtractor\\SevenZipExtractor\\SevenZipExtractor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net45": {"targetAlias": "net45", "dependencies": {"MSTest.TestAdapter": {"target": "Package", "version": "[1.3.2, )"}, "MSTest.TestFramework": {"target": "Package", "version": "[1.3.2, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[16.4.0, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.204\\RuntimeIdentifierGraph.json"}}}}