﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmNetworkLicense
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDLog4Net;
using HDMFUserControl;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public class frmNetworkLicense : Form
  {
    public bool m_isRetry = false;
    private IContainer components = (IContainer) null;
    private NewButton newButton_Retry;
    private Label label_Msg;
    private NewButton newButton_Cancle;

    public string m_strErrorLog { get; set; }

    public frmNetworkLicense()
    {
      this.InitializeComponent();
      this.newButton_Retry.ButtonText = LocaleControl.getInstance().GetString("IDS_RETRY");
      this.newButton_Cancle.ButtonText = LocaleControl.getInstance().GetString("IDS_CANCLE");
      this.label_Msg.Text = LocaleControl.getInstance().GetString("IDS_LOGIN_FAIL");
    }

    private void frmNetworkLicense_Load(object sender, EventArgs e) => this.label_Msg.Text = this.SetErrorMessage();

    private string SetErrorMessage()
    {
      string str = string.Empty;
      try
      {
        switch (this.m_strErrorLog.ToLower())
        {
          case "cocd not found":
            str = LocaleControl.getInstance().GetString("IDS_COCD_NOT_FOUND");
            break;
          case "comac not found":
            str = LocaleControl.getInstance().GetString("IDS_COMAC_NOT_FOUND");
            break;
          case "cocd not valid":
            str = LocaleControl.getInstance().GetString("IDS_COCD_NOT_VALID");
            break;
          case "license count error":
            str = LocaleControl.getInstance().GetString("IDS_LICENSE_COUNT_ERROR");
            break;
          case "license full":
            str = LocaleControl.getInstance().GetString("IDS_LICENSE_FULL");
            break;
          case "macadress not valid":
            str = LocaleControl.getInstance().GetString("IDS_MACADDRESS_NOT_VALID");
            break;
          default:
            str = "Error Code: " + this.m_strErrorLog;
            break;
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMoldFlow][frmNetworkLicense]SetErrorMessage):" + ex.Message));
      }
      return str;
    }

    private void newButton_License_NewClick(object sender, EventArgs e)
    {
      if (sender as NewButton == this.newButton_Retry)
        this.m_isRetry = true;
      this.Close();
    }

    private void frmNetworkLicense_KeyDown(object sender, KeyEventArgs e)
    {
      if (e.KeyCode != Keys.Escape)
        return;
      this.Close();
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.label_Msg = new Label();
      this.newButton_Cancle = new NewButton();
      this.newButton_Retry = new NewButton();
      this.SuspendLayout();
      this.label_Msg.ForeColor = Color.Red;
      this.label_Msg.Location = new Point(12, 9);
      this.label_Msg.Name = "label_Msg";
      this.label_Msg.Size = new Size(227, 54);
      this.label_Msg.TabIndex = 22;
      this.label_Msg.Text = "인증 실패";
      this.label_Msg.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Cancle.ButtonBackColor = Color.White;
      this.newButton_Cancle.ButtonText = "취소 ";
      this.newButton_Cancle.FlatBorderSize = 1;
      this.newButton_Cancle.FlatStyle = FlatStyle.Flat;
      this.newButton_Cancle.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Cancle.Image = (Image) null;
      this.newButton_Cancle.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Cancle.Location = new Point(129, 75);
      this.newButton_Cancle.Name = "newButton_Cancle";
      this.newButton_Cancle.Size = new Size(111, 23);
      this.newButton_Cancle.TabIndex = 23;
      this.newButton_Cancle.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Cancle.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Cancle.NewClick += new EventHandler(this.newButton_License_NewClick);
      this.newButton_Retry.ButtonBackColor = Color.White;
      this.newButton_Retry.ButtonText = "재시도";
      this.newButton_Retry.FlatBorderSize = 1;
      this.newButton_Retry.FlatStyle = FlatStyle.Flat;
      this.newButton_Retry.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Retry.Image = (Image) null;
      this.newButton_Retry.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Retry.Location = new Point(12, 75);
      this.newButton_Retry.Name = "newButton_Retry";
      this.newButton_Retry.Size = new Size(111, 23);
      this.newButton_Retry.TabIndex = 19;
      this.newButton_Retry.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Retry.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Retry.NewClick += new EventHandler(this.newButton_License_NewClick);
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(252, 110);
      this.Controls.Add((Control) this.newButton_Cancle);
      this.Controls.Add((Control) this.label_Msg);
      this.Controls.Add((Control) this.newButton_Retry);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = "frmNetwork";
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "Network License Error";
      this.TopMost = true;
      this.Load += new EventHandler(this.frmNetworkLicense_Load);
      this.KeyDown += new KeyEventHandler(this.frmNetworkLicense_KeyDown);
      this.ResumeLayout(false);
    }
  }
}
