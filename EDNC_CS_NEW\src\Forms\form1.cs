using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;

namespace EDNC_CS_NEW
{
    public class Form1 : Form
    {
        // Componentes del formulario
        private IContainer components = null;
        private TabControl metk_Tab_Control;
        private TabPage process_Setting_Tab;
        private TabPage analysis_Result_Tab;
        private TabPage home_Tab;
        
        // Componentes del Home Tab
        private PictureBox pictureBox1;
        private Button naver_Cafe_Link;
        private Button team_Viewer_Download_Button;
        private Button youTube_Link;
        private Button remoteCall_Download;
        private Label label1;
        private Label label2;
        private Label label3;
        private Label label4;
        private Label label5;
        
        // Componentes del Process Setting Tab
        private TabControl process_Setting_Case_Tab;
        private TabPage case_Home;
        private TabPage case1;
        private TabPage case2;
        private TabPage case3;
        private TabPage case4;
        private TabPage case5;
        private TabPage case6;
        private TabPage case7;
        private TabPage case8;
        private TabPage case9;
        private TabPage case10;
        
        // Componentes del Case1
        private Panel case1_Panel;
        private PictureBox a_Process_Setting_Tab_Image;
        private Button a_Process_Setting_Tab_Button;
        
        // Componentes del Case2
        private Panel case2_Panel;
        private PictureBox b_Process_Setting_Tab_Image;
        private Button b_Process_Setting_Tab_Button;
        
        // Componentes del Case3
        private Panel case3_Panel;
        
        // Componentes del Analysis Result Tab
        private PictureBox analysis_Result_Tab_Image;
        private Button analysis_Result_Compare;
        private Label anlysis_Result_Tab_Comment1;
        private Label anlysis_Result_Tab_Comment2;
        private Label anlysis_Result_Tab_Comment3;
        
        // Campos para resultados de análisis
        private TextBox[] injectionTime = new TextBox[10];
        private TextBox[] vpPressure = new TextBox[10];
        private TextBox[] tff = new TextBox[10];
        private TextBox[] mip = new TextBox[10];
        private TextBox[] mcf = new TextBox[10];
        private TextBox[] tret = new TextBox[10];
        private TextBox[] avs = new TextBox[10];
        private TextBox[] dt = new TextBox[10];
        private TextBox[] dx = new TextBox[10];
        private TextBox[] dy = new TextBox[10];
        private TextBox[] dz = new TextBox[10];
        
        // Campos para límites
        private TextBox injectionTimeLow;
        private TextBox injectionTimeHigh;
        private TextBox vpPressureLow;
        private TextBox vpPressureHigh;
        private TextBox tffLow;
        private TextBox tffHigh;
        private TextBox mipLow;
        private TextBox mipHigh;
        private TextBox mcfLow;
        private TextBox mcfHigh;
        private TextBox tretLow;
        private TextBox tretHigh;
        private TextBox avsLow;
        private TextBox avsHigh;
        private TextBox dtLow;
        private TextBox dtHigh;
        private TextBox dxLow;
        private TextBox dxHigh;
        private TextBox dyLow;
        private TextBox dyHigh;
        private TextBox dzLow;
        private TextBox dzHigh;
        
        // Etiquetas
        private Label high;
        private Label low;
        
        // Constructor
        public Form1()
        {
            InitializeComponent();
        }
        
        // Método para inicializar los componentes
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.metk_Tab_Control = new System.Windows.Forms.TabControl();
            this.home_Tab = new System.Windows.Forms.TabPage();
            this.process_Setting_Tab = new System.Windows.Forms.TabPage();
            this.analysis_Result_Tab = new System.Windows.Forms.TabPage();
            
            // Inicialización de componentes del Home Tab
            this.pictureBox1 = new System.Windows.Forms.PictureBox();
            this.naver_Cafe_Link = new System.Windows.Forms.Button();
            this.team_Viewer_Download_Button = new System.Windows.Forms.Button();
            this.youTube_Link = new System.Windows.Forms.Button();
            this.remoteCall_Download = new System.Windows.Forms.Button();
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            
            // Inicialización de componentes del Process Setting Tab
            this.process_Setting_Case_Tab = new System.Windows.Forms.TabControl();
            this.case_Home = new System.Windows.Forms.TabPage();
            this.case1 = new System.Windows.Forms.TabPage();
            this.case2 = new System.Windows.Forms.TabPage();
            this.case3 = new System.Windows.Forms.TabPage();
            this.case4 = new System.Windows.Forms.TabPage();
            this.case5 = new System.Windows.Forms.TabPage();
            this.case6 = new System.Windows.Forms.TabPage();
            this.case7 = new System.Windows.Forms.TabPage();
            this.case8 = new System.Windows.Forms.TabPage();
            this.case9 = new System.Windows.Forms.TabPage();
            this.case10 = new System.Windows.Forms.TabPage();
            
            // Inicialización de componentes de los Cases
            this.case1_Panel = new System.Windows.Forms.Panel();
            this.a_Process_Setting_Tab_Image = new System.Windows.Forms.PictureBox();
            this.a_Process_Setting_Tab_Button = new System.Windows.Forms.Button();
            this.case2_Panel = new System.Windows.Forms.Panel();
            this.b_Process_Setting_Tab_Image = new System.Windows.Forms.PictureBox();
            this.b_Process_Setting_Tab_Button = new System.Windows.Forms.Button();
            this.case3_Panel = new System.Windows.Forms.Panel();
            
            // Inicialización de componentes del Analysis Result Tab
            this.analysis_Result_Tab_Image = new System.Windows.Forms.PictureBox();
            this.analysis_Result_Compare = new System.Windows.Forms.Button();
            this.anlysis_Result_Tab_Comment1 = new System.Windows.Forms.Label();
            this.anlysis_Result_Tab_Comment2 = new System.Windows.Forms.Label();
            this.anlysis_Result_Tab_Comment3 = new System.Windows.Forms.Label();
            
            // Inicialización de TextBoxes para resultados
            for (int i = 0; i < 10; i++)
            {
                this.injectionTime[i] = new System.Windows.Forms.TextBox();
                this.vpPressure[i] = new System.Windows.Forms.TextBox();
                this.tff[i] = new System.Windows.Forms.TextBox();
                this.mip[i] = new System.Windows.Forms.TextBox();
                this.mcf[i] = new System.Windows.Forms.TextBox();
                this.tret[i] = new System.Windows.Forms.TextBox();
                this.avs[i] = new System.Windows.Forms.TextBox();
                this.dt[i] = new System.Windows.Forms.TextBox();
                this.dx[i] = new System.Windows.Forms.TextBox();
                this.dy[i] = new System.Windows.Forms.TextBox();
                this.dz[i] = new System.Windows.Forms.TextBox();
            }
            
            // Inicialización de TextBoxes para límites
            this.injectionTimeLow = new System.Windows.Forms.TextBox();
            this.injectionTimeHigh = new System.Windows.Forms.TextBox();
            this.vpPressureLow = new System.Windows.Forms.TextBox();
            this.vpPressureHigh = new System.Windows.Forms.TextBox();
            this.tffLow = new System.Windows.Forms.TextBox();
            this.tffHigh = new System.Windows.Forms.TextBox();
            this.mipLow = new System.Windows.Forms.TextBox();
            this.mipHigh = new System.Windows.Forms.TextBox();
            this.mcfLow = new System.Windows.Forms.TextBox();
            this.mcfHigh = new System.Windows.Forms.TextBox();
            this.tretLow = new System.Windows.Forms.TextBox();
            this.tretHigh = new System.Windows.Forms.TextBox();
            this.avsLow = new System.Windows.Forms.TextBox();
            this.avsHigh = new System.Windows.Forms.TextBox();
            this.dtLow = new System.Windows.Forms.TextBox();
            this.dtHigh = new System.Windows.Forms.TextBox();
            this.dxLow = new System.Windows.Forms.TextBox();
            this.dxHigh = new System.Windows.Forms.TextBox();
            this.dyLow = new System.Windows.Forms.TextBox();
            this.dyHigh = new System.Windows.Forms.TextBox();
            this.dzLow = new System.Windows.Forms.TextBox();
            this.dzHigh = new System.Windows.Forms.TextBox();
            
            // Inicialización de etiquetas
            this.high = new System.Windows.Forms.Label();
            this.low = new System.Windows.Forms.Label();
            
            // Configuración del formulario principal
            this.SuspendLayout();
            this.Text = "EDNC CS NEW";
            this.Size = new System.Drawing.Size(1024, 768);
            this.StartPosition = FormStartPosition.CenterScreen;
            
            // Configuración del TabControl principal
            this.metk_Tab_Control.Dock = DockStyle.Fill;
            this.metk_Tab_Control.Controls.Add(this.home_Tab);
            this.metk_Tab_Control.Controls.Add(this.process_Setting_Tab);
            this.metk_Tab_Control.Controls.Add(this.analysis_Result_Tab);
            this.Controls.Add(this.metk_Tab_Control);
            
            // Configuración de las pestañas principales
            this.home_Tab.Text = "Home";
            this.process_Setting_Tab.Text = "Process Setting";
            this.analysis_Result_Tab.Text = "Analysis Result";
            
            // Configuración del Home Tab
            this.home_Tab.Controls.Add(this.pictureBox1);
            this.home_Tab.Controls.Add(this.naver_Cafe_Link);
            this.home_Tab.Controls.Add(this.team_Viewer_Download_Button);
            this.home_Tab.Controls.Add(this.youTube_Link);
            this.home_Tab.Controls.Add(this.remoteCall_Download);
            this.home_Tab.Controls.Add(this.label1);
            this.home_Tab.Controls.Add(this.label2);
            this.home_Tab.Controls.Add(this.label3);
            this.home_Tab.Controls.Add(this.label4);
            this.home_Tab.Controls.Add(this.label5);
            
            // Configuración del Process Setting Tab
            this.process_Setting_Tab.Controls.Add(this.process_Setting_Case_Tab);
            this.process_Setting_Case_Tab.Dock = DockStyle.Fill;
            
            // Configuración de las pestañas de casos
            this.process_Setting_Case_Tab.Controls.Add(this.case_Home);
            this.process_Setting_Case_Tab.Controls.Add(this.case1);
            this.process_Setting_Case_Tab.Controls.Add(this.case2);
            this.process_Setting_Case_Tab.Controls.Add(this.case3);
            this.process_Setting_Case_Tab.Controls.Add(this.case4);
            this.process_Setting_Case_Tab.Controls.Add(this.case5);
            this.process_Setting_Case_Tab.Controls.Add(this.case6);
            this.process_Setting_Case_Tab.Controls.Add(this.case7);
            this.process_Setting_Case_Tab.Controls.Add(this.case8);
            this.process_Setting_Case_Tab.Controls.Add(this.case9);
            this.process_Setting_Case_Tab.Controls.Add(this.case10);
            
            // Configuración de los paneles de casos
            this.case1.Controls.Add(this.case1_Panel);
            this.case1_Panel.Controls.Add(this.a_Process_Setting_Tab_Image);
            this.case1_Panel.Controls.Add(this.a_Process_Setting_Tab_Button);
            
            this.case2.Controls.Add(this.case2_Panel);
            this.case2_Panel.Controls.Add(this.b_Process_Setting_Tab_Image);
            this.case2_Panel.Controls.Add(this.b_Process_Setting_Tab_Button);
            
            this.case3.Controls.Add(this.case3_Panel);
            
            // Configuración del Analysis Result Tab
            this.analysis_Result_Tab.Controls.Add(this.analysis_Result_Tab_Image);
            this.analysis_Result_Tab.Controls.Add(this.analysis_Result_Compare);
            this.analysis_Result_Tab.Controls.Add(this.anlysis_Result_Tab_Comment1);
            this.analysis_Result_Tab.Controls.Add(this.anlysis_Result_Tab_Comment2);
            this.analysis_Result_Tab.Controls.Add(this.anlysis_Result_Tab_Comment3);
            
            // Configuración de etiquetas de límites
            this.analysis_Result_Tab.Controls.Add(this.high);
            this.analysis_Result_Tab.Controls.Add(this.low);
            
            this.ResumeLayout(false);
        }
        
        // Método para liberar recursos
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }
        
        // Punto de entrada principal
        [STAThread]
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new Form1());
        }
    }
}