﻿// Decompiled with JetBrains decompiler
// Type: HDMFAI.clsInputData
// Assembly: HDMFAI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: A66A47CE-D63B-4ACB-B9A4-A4F118252C4E
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFAI.dll

using HDLog4Net;
using HDMoldFlowLibrary;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;

namespace HDMFAI
{
  public class clsInputData
  {
    public static bool CreateInputData(DataRow p_drStudy, Dictionary<string, string> p_dicInput)
    {
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      string empty3 = string.Empty;
      bool inputData = false;
      DataTable p_dtDet = new DataTable();
      try
      {
        string p_strStudy = p_drStudy["Name"].ToString();
        string str1 = p_strStudy + "/" + DateTime.Now.Ticks.ToString();
        p_dicInput.Add("Study", str1);
        string[] strArray = p_strStudy.Split('_');
        if (strArray.Length > 3)
          str1 = strArray[3];
        p_dicInput.Add("Study_Group", str1);
        string str2 = p_drStudy["Mesh"].ToString();
        if (str2.ToLower() == "dual")
          str2 = "2D";
        p_dicInput.Add("Mesh_Type", str2);
        Dictionary<string, string> allNodes = clsHDMFLib.GetAllNodes(ref p_dtDet);
        string nodeFromStudyNote = clsHDMFLib.GetGateNodeFromStudyNote();
        string[] arr_strGateNode;
        if (nodeFromStudyNote == string.Empty)
          arr_strGateNode = clsHDMFLib.GetGateNodes(allNodes, p_dtDet);
        else
          arr_strGateNode = ((IEnumerable<string>) nodeFromStudyNote.Split(' ')).Where<string>((System.Func<string, bool>) (Temp => !string.IsNullOrEmpty(Temp))).ToArray<string>();
        clsHDMFLib.ChangeActiveLayer(0, clsHDMFLib.GetAllLayers(), false, 0L);
        clsInputData.GetNodesData(allNodes, arr_strGateNode, p_dtDet, ref p_dicInput);
        clsInputData.GetProductInfo(ref p_dicInput);
        clsInputData.SaveInputData(p_strStudy, p_dicInput);
        inputData = true;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsInputData]CreateInputData):" + ex.Message));
      }
      return inputData;
    }

    private static void GetNodesData(
      Dictionary<string, string> p_dicAllNodes,
      string[] arr_strGateNode,
      DataTable p_dtDet,
      ref Dictionary<string, string> p_dicData)
    {
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      string empty3 = string.Empty;
      string empty4 = string.Empty;
      string empty5 = string.Empty;
      StringBuilder stringBuilder = new StringBuilder();
      List<string> stringList = new List<string>();
      Dictionary<string, string> p_dicAllNodes1 = new Dictionary<string, string>((IDictionary<string, string>) p_dicAllNodes);
      try
      {
        string[] array = ((IEnumerable<string>) clsHDMFLib.GetNodesOfLayers("Prohibit Nodes").Split(' ')).Where<string>((System.Func<string, bool>) (Temp => !string.IsNullOrEmpty(Temp))).ToArray<string>();
        foreach (DataRow row in (InternalDataCollectionBase) p_dtDet.Rows)
        {
          string str1 = "N" + row["SNode"].ToString();
          if (!stringList.Contains(str1) && !((IEnumerable<string>) arr_strGateNode).Contains<string>(str1))
            stringList.Add(str1);
          string str2 = "N" + row["ENode"].ToString();
          if (!stringList.Contains(str2) && !((IEnumerable<string>) arr_strGateNode).Contains<string>(str2))
            stringList.Add(str2);
        }
        double[] modelCenterPoint = clsHDMFLib.GetModelCenterPoint(p_dicAllNodes1);
        if (modelCenterPoint.Length > 2)
        {
          p_dicData.Add("Data_Center_X", modelCenterPoint[0].ToString());
          p_dicData.Add("Data_Center_Y", modelCenterPoint[1].ToString());
          p_dicData.Add("Data_Center_Z", modelCenterPoint[2].ToString());
        }
        foreach (KeyValuePair<string, string> keyValuePair in p_dicAllNodes1)
        {
          if (stringBuilder.Length != 0)
            stringBuilder.Append("|");
          int num = !((IEnumerable<string>) array).Contains<string>(keyValuePair.Key) ? 0 : 1;
          stringBuilder.Append(keyValuePair.Key + "/" + keyValuePair.Value + "," + num.ToString());
        }
        p_dicData.Add("Nodes", stringBuilder.ToString());
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsInputdata]GetNodesData):" + ex.Message));
      }
    }

    private static void GetProductInfo(ref Dictionary<string, string> p_dicData)
    {
      string empty = string.Empty;
      try
      {
        string volumeFromDualDomain = clsHDMFLib.GetMeshVolumeFromDualDomain();
        p_dicData.Add("Product_Part_Volume", volumeFromDualDomain);
        string str = clsHDMFLib.GetThicknessDataFromPlotFormat().Split('/')[0];
        if (str != string.Empty)
        {
          string[] strArray = str.Split('|');
          if (strArray.Length > 1)
          {
            p_dicData.Add("Product_Thickness", strArray[0]);
            p_dicData.Add("Product_Thickness_Percentage", strArray[1]);
          }
        }
        double[] modelLengths = clsHDMFLib.GetModelLengths();
        p_dicData.Add("Product_X_Length", modelLengths[0].ToString());
        p_dicData.Add("Product_Y_Length", modelLengths[1].ToString());
        p_dicData.Add("Product_Z_Length", modelLengths[2].ToString());
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsInputdata]GetProductInfo):" + ex.Message));
      }
    }

    private static void SaveInputData(string p_strStudy, Dictionary<string, string> p_dicInputData)
    {
      string str = clsAIDefine.g_diTmpAI.ToString() + "\\" + p_strStudy;
      string path1 = str + "\\Input.ini";
      string path2 = str + "\\Nodes.txt";
      if (File.Exists(path1))
        File.Delete(path1);
      if (File.Exists(path2))
        File.Delete(path2);
      foreach (KeyValuePair<string, string> keyValuePair in p_dicInputData)
      {
        if (!(keyValuePair.Key == "Nodes"))
          clsUtill.WriteINI("Input", keyValuePair.Key, keyValuePair.Value, str + "\\Input.ini");
      }
      using (StreamWriter streamWriter = new StreamWriter(str + "\\Nodes.txt"))
      {
        streamWriter.Write(p_dicInputData["Nodes"]);
        streamWriter.Flush();
        streamWriter.Close();
      }
    }

    public static string SendInputDataToWeb(string p_strStudy)
    {
      string str1 = string.Empty;
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      Dictionary<string, string> dictionary = new Dictionary<string, string>();
      try
      {
        string str2 = clsAIDefine.g_diTmpAI.ToString() + "\\" + p_strStudy;
        Dictionary<string, string> iniDataFromSection = clsUtill.GetINIDataFromSection(str2 + "\\Input.ini", "Input");
        string str3 = string.Empty;
        using (StreamReader streamReader = new StreamReader(str2 + "\\Nodes.txt"))
          str3 = streamReader.ReadToEnd();
        iniDataFromSection.Add("Nodes", str3);
        string jsonData = clsInputData.CreateJsonData(iniDataFromSection);
        if (clsAIDefine.g_fiAICfg != null)
          str1 = clsUtill.ReadINI("Sub", "Input", clsAIDefine.g_fiAICfg.FullName);
        if (string.IsNullOrEmpty(str1))
          str1 = "/api/input";
        string p_strSendURL = str1;
        return clsWeb.SendDataToWeb(jsonData, p_strSendURL);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsInputData]SendInputDataToWeb):" + ex.Message));
        return empty2 + Environment.NewLine + ex.Message;
      }
    }

    private static string CreateJsonData(Dictionary<string, string> p_dicInput)
    {
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      string empty3 = string.Empty;
      JObject jobject1 = new JObject();
      JObject jobject2 = new JObject();
      JArray jarray = new JArray();
      try
      {
        jobject1.Add("Study", (JToken) p_dicInput["Study"]);
        jobject1.Add("Study_Group", (JToken) p_dicInput["Study_Group"]);
        jobject1.Add("Mesh_Type", (JToken) p_dicInput["Mesh_Type"]);
        jobject1.Add("Analysis_Type", (JToken) p_dicInput["Analysis_Type"]);
        foreach (KeyValuePair<string, string> keyValuePair in p_dicInput.Where<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key.Contains("Data"))))
        {
          string propertyName = keyValuePair.Key.Replace("Data_", string.Empty);
          jobject1.Add(propertyName, (JToken) keyValuePair.Value);
        }
        IEnumerable<KeyValuePair<string, string>> keyValuePairs1 = p_dicInput.Where<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key.Contains("Mat")));
        jarray.Clear();
        JObject jobject3 = new JObject();
        foreach (KeyValuePair<string, string> keyValuePair in keyValuePairs1)
        {
          string propertyName = keyValuePair.Key.Replace("Mat_", string.Empty);
          jobject3.Add(propertyName, (JToken) keyValuePair.Value);
        }
        jobject1.Add("Material", jobject3.DeepClone());
        IEnumerable<KeyValuePair<string, string>> keyValuePairs2 = p_dicInput.Where<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key.Contains("Product_")));
        JObject jobject4 = new JObject();
        foreach (KeyValuePair<string, string> keyValuePair in keyValuePairs2)
        {
          string propertyName = keyValuePair.Key.Replace("Product_", string.Empty);
          jobject4.Add(propertyName, (JToken) keyValuePair.Value);
        }
        jobject1.Add("Product_Info", jobject4.DeepClone());
        IEnumerable<KeyValuePair<string, string>> keyValuePairs3 = p_dicInput.Where<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key.Contains("Result")));
        jarray.Clear();
        JObject jobject5 = new JObject();
        foreach (KeyValuePair<string, string> keyValuePair in keyValuePairs3)
        {
          string propertyName = keyValuePair.Key.Replace("Result_", string.Empty);
          jobject5.Add(propertyName, (JToken) keyValuePair.Value);
        }
        jobject1.Add("Results_Priority", jobject5.DeepClone());
        IEnumerable<KeyValuePair<string, string>> keyValuePairs4 = p_dicInput.Where<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key.Contains("Option")));
        jarray.Clear();
        JObject jobject6 = new JObject();
        foreach (KeyValuePair<string, string> keyValuePair in keyValuePairs4)
        {
          string propertyName = keyValuePair.Key.Replace("Option_", string.Empty);
          jobject6.Add(propertyName, (JToken) keyValuePair.Value);
        }
        jobject1.Add("Option", jobject6.DeepClone());
        if (p_dicInput.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Nodes")))
        {
          string[] strArray1 = p_dicInput["Nodes"].Split('|');
          jarray.Clear();
          for (int index = 0; index < strArray1.Length; ++index)
          {
            string[] strArray2 = strArray1[index].Split('/');
            JObject jobject7 = new JObject();
            jobject7.Add("Node", (JToken) strArray2[0]);
            string[] strArray3 = strArray2[1].Split(',');
            jobject7.Add("X", (JToken) strArray3[0]);
            jobject7.Add("Y", (JToken) strArray3[1]);
            jobject7.Add("Z", (JToken) strArray3[2]);
            jobject7.Add("Prohibit", (JToken) strArray3[3]);
            jarray.Add((JToken) jobject7);
          }
          jobject1.Add("Nodes", jarray.DeepClone());
        }
        empty1 = jobject1.ToString();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsInputData]CreateJsonData):" + ex.Message));
      }
      return empty1;
    }
  }
}
