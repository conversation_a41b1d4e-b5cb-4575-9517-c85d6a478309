#!/usr/bin/env python3
"""
Simple test to check Synergy connection.
"""

import os
import sys

print("Starting Synergy connection test...")

try:
    import win32com.client
    print("✅ win32com imported successfully")
except ImportError as e:
    print(f"❌ Failed to import win32com: {e}")
    sys.exit(1)

# Test 1: Try to find Moldflow installation
print("\n1. Looking for Moldflow installation...")
possible_paths = [
    r"C:\Program Files\Autodesk\Moldflow Synergy 2025\bin\synergy.exe",
    r"C:\Program Files\Autodesk\Moldflow Synergy 2024\bin\synergy.exe",
    r"C:\Program Files\Autodesk\Moldflow Insight 2025\bin\Synergy.exe",
    r"C:\Program Files\Autodesk\Moldflow Insight 2024\bin\Synergy.exe",
    r"C:\Program Files\Autodesk\Moldflow Insight 2023\bin\Synergy.exe",
    r"C:\Program Files\Autodesk\Moldflow Insight 2022\bin\Synergy.exe",
]

found_path = None
for path in possible_paths:
    if os.path.exists(path):
        print(f"✅ Found Moldflow at: {path}")
        found_path = path
        break

if not found_path:
    print("❌ Moldflow Synergy not found in standard locations")

# Test 2: Try different COM connection methods
print("\n2. Testing COM connections...")

connection_methods = [
    ("Synergy.Synergy", "Standard Synergy dispatch"),
    ("Scandium.Synergy", "Scandium dispatch"),
]

for prog_id, description in connection_methods:
    try:
        print(f"   Trying {description} ({prog_id})...")
        synergy = win32com.client.Dispatch(prog_id)
        print(f"   ✅ {description} - SUCCESS!")

        # Try to get version
        try:
            version = synergy.Version
            print(f"   Version: {version}")
        except:
            print("   Could not get version (may need study open)")

        # Try to get units
        try:
            units = synergy.GetUnits()
            print(f"   Units: {units}")
        except:
            print("   Could not get units (may need study open)")

        break

    except Exception as e:
        print(f"   ❌ {description} failed: {str(e)}")

print("\n3. Checking environment variables...")
sa_instance = os.environ.get('SAInstance')
if sa_instance:
    print(f"✅ SAInstance found: {sa_instance}")
else:
    print("❌ SAInstance environment variable not set")

mf_buildcode = os.environ.get('MF_BUILDCODE')
if mf_buildcode:
    print(f"✅ MF_BUILDCODE found: {mf_buildcode}")
else:
    print("❌ MF_BUILDCODE environment variable not set")

print("\nTest completed!")
