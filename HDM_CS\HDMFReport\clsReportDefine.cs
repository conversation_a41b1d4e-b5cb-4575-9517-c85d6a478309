﻿// Decompiled with JetBrains decompiler
// Type: HDMFReport.clsReportDefine
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using HDMoldFlowLibrary;
using System.Collections.Generic;
using System.Data;
using System.IO;

namespace HDMFReport
{
  public class clsReportDefine
  {
    public static clsReportDefine.LicLevel enumLicLevel = clsReportDefine.LicLevel.Light;
    public static clsHDMFLibDefine.Company g_enumSelectedCompany = clsHDMFLibDefine.Company.HDSolutions;
    public static DirectoryInfo g_diProject;
    public static DataTable g_dtInjectionDB;
    public static DirectoryInfo g_diTemplate;
    public static DirectoryInfo g_diCfg;
    public static DirectoryInfo g_diTmpReport;
    public static Dictionary<string, string> g_dicExtension;
    public static bool g_isSingleReport = true;

    public enum LicLevel
    {
      Light,
      Standard,
      Premium,
      Ultimate,
    }
  }
}
