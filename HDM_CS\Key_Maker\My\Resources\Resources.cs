﻿// Decompiled with JetBrains decompiler
// Type: Key_Maker.My.Resources.Resources
// Assembly: Key_Maker, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 43BE3483-FE2B-4F9D-8AD0-2D447D49E2D6
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\Key_Maker.exe

using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;

namespace Key_Maker.My.Resources
{
  [StandardModule]
  [GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
  [DebuggerNonUserCode]
  [CompilerGenerated]
  [HideModuleName]
  internal sealed class Resources
  {
    private static ResourceManager resourceMan;
    private static CultureInfo resourceCulture;

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    internal static ResourceManager ResourceManager
    {
      get
      {
        if (object.ReferenceEquals((object) Key_Maker.My.Resources.Resources.resourceMan, (object) null))
          Key_Maker.My.Resources.Resources.resourceMan = new ResourceManager("Key_Maker.Resources", typeof (Key_Maker.My.Resources.Resources).Assembly);
        return Key_Maker.My.Resources.Resources.resourceMan;
      }
    }

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    internal static CultureInfo Culture
    {
      get => Key_Maker.My.Resources.Resources.resourceCulture;
      set => Key_Maker.My.Resources.Resources.resourceCulture = value;
    }
  }
}
