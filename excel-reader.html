<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Lector de Excel MCP</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        #output {
            margin-top: 20px;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f9f9f9;
            min-height: 200px;
        }
        .input-group {
            margin-bottom: 10px;
        }
        input {
            padding: 8px;
            width: 300px;
        }
    </style>
</head>
<body>
    <h1>Lector de Excel MCP</h1>
    
    <div class="input-group">
        <label for="filePath">Ruta del archivo Excel:</label>
        <input type="text" id="filePath" placeholder="C:\ruta\al\archivo.xlsx">
        <button onclick="openExcelFile()">Abrir Archivo</button>
    </div>

    <div>
        <button onclick="readCells()"><PERSON><PERSON></button>
        <button onclick="executeMacro()">Ejecutar Macro "material"</button>
    </div>
    
    <div id="output">
        <p>Los resultados se mostrarán aquí...</p>
    </div>

    <script>
        // URL base del servidor MCP Excel (por defecto es localhost:3000)
        const baseUrl = 'http://localhost:3000';
        let workbookId = null;

        // Función para abrir un archivo Excel
        async function openExcelFile() {
            const filePath = document.getElementById('filePath').value;
            if (!filePath) {
                updateOutput('Por favor, ingresa una ruta de archivo válida');
                return;
            }

            try {
                updateOutput('Intentando abrir el archivo: ' + filePath);
                
                const response = await fetch(`${baseUrl}/workbooks`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ path: filePath })
                });

                const data = await response.json();
                
                if (data.id) {
                    workbookId = data.id;
                    updateOutput(`Archivo Excel abierto correctamente. ID: ${workbookId}`);
                } else {
                    updateOutput('Error al abrir el archivo: ' + JSON.stringify(data));
                }
            } catch (error) {
                updateOutput('Error de conexión: ' + error.message);
            }
        }

        // Función para leer celdas del archivo Excel
        async function readCells() {
            if (!workbookId) {
                updateOutput('Primero debes abrir un archivo Excel');
                return;
            }

            try {
                // Leer la celda D9 (mencionada en la macro)
                const response = await fetch(`${baseUrl}/workbooks/${workbookId}/worksheets/active/range/D9`);
                const data = await response.json();
                
                updateOutput('Contenido de la celda D9: ' + JSON.stringify(data));
                
                // También leer algunas otras celdas para mostrar más información
                const rangeResponse = await fetch(`${baseUrl}/workbooks/${workbookId}/worksheets/active/range/A1:C10`);
                const rangeData = await rangeResponse.json();
                
                updateOutput(document.getElementById('output').innerHTML + 
                    '<br><br>Contenido del rango A1:C10: ' + JSON.stringify(rangeData));
            } catch (error) {
                updateOutput('Error al leer celdas: ' + error.message);
            }
        }

        // Función para ejecutar la macro "material"
        async function executeMacro() {
            if (!workbookId) {
                updateOutput('Primero debes abrir un archivo Excel');
                return;
            }

            try {
                updateOutput('Intentando ejecutar la macro "material"...');
                
                const response = await fetch(`${baseUrl}/workbooks/${workbookId}/macros/material`, {
                    method: 'POST'
                });

                const data = await response.json();
                updateOutput('Resultado de la ejecución de la macro: ' + JSON.stringify(data));
            } catch (error) {
                updateOutput('Error al ejecutar la macro: ' + error.message);
            }
        }

        // Función para actualizar el área de salida
        function updateOutput(message) {
            document.getElementById('output').innerHTML = '<p>' + message + '</p>';
        }
    </script>
</body>
</html>