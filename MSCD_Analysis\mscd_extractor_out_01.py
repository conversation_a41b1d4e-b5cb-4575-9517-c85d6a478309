import re
import csv
import os
import sys
import subprocess
import logging

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('mscd_extractor.log')
    ]
)

def is_text_file(filepath, blocksize=512):
    """Checks if a file is likely a text file."""
    try:
        # Try reading with utf-8, common for text files
        with open(filepath, 'r', encoding='utf-8') as f:
            f.read(blocksize) # Try to read a block
        return True
    except UnicodeDecodeError:
        # If utf-8 fails, try latin-1, another common encoding
        try:
            with open(filepath, 'r', encoding='latin-1') as f:
                f.read(blocksize)
            return True
        except UnicodeDecodeError:
            # If both fail, it's likely not a text file or uses an unsupported encoding
            logging.warning(f"is_text_file: File {filepath} could not be decoded as UTF-8 or Latin-1.")
            return False
    except FileNotFoundError:
        logging.error(f"is_text_file: File not found at {filepath}")
        return False # File doesn't exist
    except Exception as e:
        # Other potential errors (e.g., permissions)
        logging.warning(f"is_text_file: Error checking file {filepath}: {e}")
        return False # Uncertain, assume not text for safety

def load_mscd_codes(csv_file):
    """Carga códigos MSCD desde el archivo CSV generado."""
    mscd_dict = {}
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            mscd_dict[row['mscd_code']] = row
    return mscd_dict

def extract_section_data(lines, start_idx):
    """Extrae datos de secciones especiales como perfiles de presión."""
    section_data = []
    i = start_idx
    
    # Buscar la línea que contiene los encabezados de columnas
    while i < len(lines) and not ('duration' in lines[i].lower() and 'pressure' in lines[i].lower()):
        i += 1
    
    if i >= len(lines):
        return [], start_idx
    
    # Avanzar hasta encontrar la línea con guiones (separador)
    while i < len(lines) and '----' not in lines[i]:
        i += 1
    
    if i >= len(lines):
        return [], start_idx
    
    i += 1  # Avanzar después de la línea de guiones
    
    # Recopilar datos hasta encontrar una línea vacía o el final de la sección
    while i < len(lines) and lines[i].strip() and not lines[i].strip().startswith('---'):
        line = lines[i].strip()
        if line:  # Ignorar líneas vacías
            section_data.append(line)
        i += 1
    
    return section_data, i

def normalize_message(message):
    """Normaliza un mensaje para comparación, eliminando caracteres especiales y formateadores."""
    if not message:
        return ""
    
    # Eliminar formateadores como %11.4G, %s, etc.
    normalized = re.sub(r'%[\d\.]*[a-zA-Z]', '', message)
    
    # Eliminar caracteres especiales y normalizar espacios
    normalized = re.sub(r'[\[\]\(\)\{\}\*\+\?\^\$\\\.\|]', ' ', normalized)
    normalized = re.sub(r'\s+', ' ', normalized).strip()
    
    # Si hay un signo igual, tomar solo la parte izquierda
    if '=' in normalized:
        normalized = normalized.split('=')[0].strip()
    
    return normalized

def extract_mscd_values(file_path, mscd_dict, output_file, verbose=False):
    """Extrae valores de los códigos MSCD encontrados en el archivo de texto, respetando el formato."""
    results = []
    
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        lines = [line.rstrip() for line in f.readlines()]
    
    if verbose:
        print(f"Leyendo {len(lines)} líneas del archivo.")
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # Detectar secciones especiales como perfiles de presión
        if 'profile:' in line.lower():
            if verbose:
                print(f"Detectada sección de perfil en línea {i+1}: {line}")
            
            section_data, new_idx = extract_section_data(lines, i+1)
            
            if section_data:
                # Buscar coincidencias en los mensajes de los códigos MSCD para esta sección
                for mscd_code, mscd_info in mscd_dict.items():
                    message_template = mscd_info.get('message', '')
                    if 'profile' in message_template.lower():
                        results.append({
                            'mscd_code': mscd_code,
                            'message': message_template,
                            'value': '\n'.join(section_data),
                            'original_line': line,
                            'is_profile': True
                        })
                        if verbose:
                            print(f"  - Asociado con código MSCD: {mscd_code}")
                        break
            
            if new_idx > i:
                i = new_idx
                continue
        
        # Buscar líneas con formato "clave = valor"
        if '=' in line and not line.startswith('---'):
            key_part = line.split('=')[0].strip()
            value_part = line.split('=')[1].strip() if len(line.split('=')) > 1 else ''
            
            if verbose:
                print(f"Analizando línea {i+1}: {key_part} = {value_part}")
            
            # Buscar coincidencias en los mensajes de los códigos MSCD
            for mscd_code, mscd_info in mscd_dict.items():
                message_template = mscd_info.get('message', '')
                
                # Normalizar el mensaje para comparación
                normalized_message = normalize_message(message_template)
                normalized_key = normalize_message(key_part)
                
                # Verificar si el mensaje normalizado está contenido en la clave normalizada
                if normalized_message and normalized_message in normalized_key:
                    results.append({
                        'mscd_code': mscd_code,
                        'message': message_template,
                        'value': value_part,
                        'original_line': line,
                        'is_profile': False
                    })
                    if verbose:
                        print(f"  - Coincidencia encontrada con código MSCD: {mscd_code}")
                    break
        i += 1
    
    # Guardar resultados en CSV
    fieldnames = ['mscd_code', 'message', 'value', 'original_line', 'is_profile']
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        for result in results:
            writer.writerow(result)
    
    return len(results)

def main():
    if len(sys.argv) < 2:
        print("Uso: python mscd_extractor_out_01.py <archivo_out/txt> [--verbose]")
        print("Ejemplo: python mscd_extractor_out_01.py c:\\Moldflow\\Test.out")
        return

    input_file = sys.argv[1]
    verbose = "--verbose" in sys.argv

    if not os.path.exists(input_file):
        print(f"Error: El archivo {input_file} no existe.")
        return

    # Si el archivo es .out, convertirlo a .txt usando el ejecutable oficial de Moldflow
    base_dir = os.path.dirname(os.path.abspath(__file__))
    mscd_csv = os.path.join(base_dir, 'mscd_codes.csv')

    file_ext = os.path.splitext(input_file)[1].lower()
    if file_ext == '.out':
        txt_file = os.path.splitext(input_file)[0] + '.txt'
    else:
        txt_file = input_file
    temp_txt_created = False
    if file_ext == '.out':
        # Generar nombre base sin doble extensión
        # txt_file is already defined as: os.path.splitext(input_file)[0] + '.txt'
        moldflow_exe = r'C:\Program Files\Autodesk\Moldflow Synergy 2025\bin\studyrlt.exe'
        if not os.path.exists(moldflow_exe):
            logging.error(f"No se encontró el ejecutable studyrlt.exe en {moldflow_exe}")
            print(f"Error: No se encontró el ejecutable studyrlt.exe en {moldflow_exe}")
            print("Por favor, verifique la instalación de Autodesk Moldflow Synergy 2025.")
            return

        norm_input_file = os.path.normpath(input_file)
        norm_txt_file = os.path.normpath(txt_file) # txt_file is from the outer scope

        command_args = [
            moldflow_exe,
            norm_input_file,
            "-exportoutput",
            "-output",
            norm_txt_file,
            "-unit",
            "SI"
        ]
        logging.info(f"Convirtiendo {norm_input_file} a {norm_txt_file} usando studyrlt.exe...")
        logging.info(f"Ejecutando comando: {' '.join(command_args)}")

        try:
            result = subprocess.run(
                command_args,
                capture_output=True,
                text=True, # Decodes stdout/stderr using default encoding
                check=False, # Will check result.returncode manually
                shell=False
            )

            # Log stdout and stderr from studyrlt.exe regardless of exit code
            if result.stdout:
                logging.info(f"studyrlt.exe stdout:\n{result.stdout}")
            if result.stderr:
                logging.warning(f"studyrlt.exe stderr:\n{result.stderr}") # Use warning for stderr, could be non-fatal info

            conversion_successful_flag = False
            if result.returncode == 0:
                # studyrlt.exe reported success
                logging.info(f"studyrlt.exe completado con código de salida 0.")
                if os.path.exists(norm_txt_file) and os.path.getsize(norm_txt_file) > 0:
                    logging.info(f"Archivo principal {norm_txt_file} creado exitosamente.")
                    temp_txt_created = True
                    conversion_successful_flag = True
                else:
                    logging.warning(f"studyrlt.exe retornó código 0, pero el archivo principal {norm_txt_file} no se encontró o está vacío.")
                    # Check for alternative file even if studyrlt.exe reported success but primary file is missing/empty
                    alternative_txt_file = norm_txt_file + '.out'
                    if os.path.exists(alternative_txt_file):
                        logging.info(f"Se encontró un archivo alternativo {alternative_txt_file}. Intentando renombrar.")
                        try:
                            if os.path.exists(norm_txt_file):
                                logging.info(f"El archivo de destino {norm_txt_file} ya existe. Eliminándolo antes de renombrar.")
                                try:
                                    os.remove(norm_txt_file)
                                    logging.info(f"Archivo {norm_txt_file} eliminado exitosamente.")
                                except OSError as e_remove:
                                    logging.error(f"Error al eliminar el archivo existente {norm_txt_file}: {e_remove}")
                                    print(f"Error fatal: No se pudo eliminar el archivo existente {norm_txt_file} para permitir el renombrado. Detalles: {e_remove}")
                                    return # Critical error
                            os.rename(alternative_txt_file, norm_txt_file)
                            logging.info(f"Archivo alternativo {alternative_txt_file} renombrado exitosamente a {norm_txt_file}")
                            temp_txt_created = True
                            conversion_successful_flag = True
                        except OSError as rename_error:
                            logging.error(f"Error al intentar renombrar el archivo alternativo {alternative_txt_file} a {norm_txt_file}: {rename_error}")
                            print(f"Error fatal: No se pudo renombrar el archivo alternativo {alternative_txt_file}. Detalles: {rename_error}")
                            return # Critical error
                    else:
                        logging.error(f"studyrlt.exe retornó código 0, pero no se encontró ni {norm_txt_file} (o estaba vacío) ni {alternative_txt_file}.")
                        print(f"Error: studyrlt.exe pareció tener éxito pero no generó un archivo de salida utilizable.")
                        return # Critical error
            else:
                # studyrlt.exe failed
                error_msg = f"studyrlt.exe falló con código de salida {result.returncode}."
                logging.error(error_msg)
                # Imprimir la salida estándar y de error de studyrlt.exe si existe
                if result.stdout and result.stdout.strip():
                    logging.info(f"studyrlt.exe stdout (información del error):\n{result.stdout}")
                    print(f"studyrlt.exe stdout (información del error):\n{result.stdout}")
                if result.stderr and result.stderr.strip():
                    logging.error(f"studyrlt.exe stderr (información del error):\n{result.stderr}") # Usar logging.error para stderr
                    print(f"studyrlt.exe stderr (información del error):\n{result.stderr}")

                critical_error_message = (
                    f"Error CRÍTICO: La conversión del archivo '{norm_input_file}' a formato de texto falló "
                    f"porque la herramienta externa 'studyrlt.exe' devolvió el código de error {result.returncode}.\n"
                    "Este paso es esencial para la extracción de datos. El script no puede continuar.\n"
                    "Por favor, investigue el problema con 'studyrlt.exe'. Verifique su instalación, "
                    "la integridad del archivo de entrada y los permisos.\n"
                    "No se intentará procesar ningún archivo alternativo debido a este error crítico."
                )
                logging.critical(critical_error_message)
                print(critical_error_message)
                return # Detener la ejecución del script

            if not conversion_successful_flag:
                # This case should ideally be covered by returns above, but as a safeguard:
                logging.error(f"No se pudo obtener un archivo .txt utilizable desde {norm_input_file}.")
                print(f"Error: Falló la conversión de {norm_input_file} a un archivo de texto utilizable.")
                return

        except Exception as e_global: # Catch any other unexpected error during subprocess handling
            logging.error(f"Error inesperado durante la ejecución de studyrlt.exe o el manejo de archivos: {e_global}", exc_info=True)
            print(f"Error inesperado durante la conversión: {e_global}")
            return

    # Verify if the target txt_file exists and is a text file before proceeding
    # Note: txt_file here refers to the variable from the outer scope, which is norm_txt_file in this context.
    if not os.path.exists(txt_file): # txt_file should be the path to the (potentially converted) text file
        logging.error(f"El archivo de texto {txt_file} no se encontró o no se pudo crear después del proceso de conversión/renombrado.")
        print(f"Error fatal: El archivo de texto requerido '{txt_file}' no existe o no pudo ser generado.")
        return

    # Check if the file is empty
    try:
        if os.path.getsize(txt_file) == 0:
            logging.error(f"El archivo de texto convertido '{txt_file}' está vacío.")
            print(f"Error fatal: El archivo de texto convertido '{txt_file}' está vacío.")
            if temp_txt_created and os.path.exists(txt_file): # Attempt to clean up only if we created it
                try:
                    os.remove(txt_file)
                    logging.info(f"Archivo temporal vacío eliminado: {txt_file}")
                except Exception as e_rem:
                    logging.warning(f"No se pudo eliminar el archivo temporal vacío '{txt_file}'. Error: {e_rem}")
            return
    except OSError as e_size:
        logging.error(f"Error al obtener el tamaño del archivo '{txt_file}': {e_size}")
        print(f"Error fatal: No se pudo verificar el tamaño del archivo '{txt_file}'. Detalles: {e_size}")
        return

    # Stricter check for text file
    if not is_text_file(txt_file):
        logging.error(f"El archivo '{txt_file}' no parece ser un archivo de texto válido según la función is_text_file.")
        print(f"Error fatal: El archivo '{txt_file}' no es un archivo de texto válido o no se pudo leer correctamente.")
        if temp_txt_created and os.path.exists(txt_file): # Attempt to clean up only if we created it
            try:
                os.remove(txt_file)
                logging.info(f"Archivo temporal no válido eliminado: {txt_file}")
            except Exception as e_rem:
                logging.warning(f"No se pudo eliminar el archivo temporal no válido '{txt_file}'. Error: {e_rem}")
        return

    # Nombre del archivo de salida basado en el archivo de entrada (sin doble extensión)
    input_basename = os.path.basename(txt_file)
    output_file = os.path.join(base_dir, f"extracted_{os.path.splitext(input_basename)[0]}.csv")
    print(f"Cargando códigos MSCD desde {mscd_csv}...")
    mscd_dict = load_mscd_codes(mscd_csv)
    print(f"Cargados {len(mscd_dict)} códigos MSCD.")
    print(f"Analizando archivo: {txt_file}...")
    count = extract_mscd_values(txt_file, mscd_dict, output_file, verbose)
    print(f"Se encontraron {count} valores de códigos MSCD en el archivo.")
    print(f"Resultados guardados en: {output_file}")
    # Limpiar archivo intermedio si fue creado
    if temp_txt_created and os.path.exists(txt_file):
        try:
            # os.remove(txt_file) # Comentado para no eliminar el archivo temporal
            logging.info(f"Archivo temporal {txt_file} conservado.") # Modificado para reflejar que no se elimina
        except Exception as e:
            logging.warning(f"No se pudo eliminar el archivo temporal: {txt_file}. Error: {e}")

if __name__ == "__main__":
    main()