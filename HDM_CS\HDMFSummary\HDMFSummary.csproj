﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!--Project was exported from assembly: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFSummary.dll-->
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{0F00BC75-FCB8-4318-AAB2-13E9C5F1553C}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AssemblyName>HDMFSummary</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <ApplicationVersion>2.3.0.0</ApplicationVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="HDLog4Net">
      <HintPath>lib\HDLog4Net.dll</HintPath>
    </Reference>
    <Reference Include="HDMoldFlowLibrary">
      <HintPath>lib\HDMoldFlowLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Newtonsoft.Json">
      <HintPath>lib\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Microsoft\Office\Interop\Excel\AppEvents.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\AppEvents_Event.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\Application.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\DocEvents.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\DocEvents_Event.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\Name.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\Names.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\Range.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\Sheets.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\Workbook.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\WorkbookEvents.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\WorkbookEvents_Event.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\Workbooks.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\Worksheet.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\XlFileFormat.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\XlSaveAsAccessMode.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\_Application.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\_Workbook.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\_Worksheet.cs" />
    <Compile Include="HDMFSummary\clsSummaryData.cs" />
    <Compile Include="HDMFSummary\clsSummary.cs" />
    <Compile Include="HDMFSummary\clsSummaryDefine.cs" />
    <Compile Include="HDMFSummary\clsSummaryUtill.cs" />
    <Compile Include="HDMFSummary\clsSummaryExcel.cs" />
    <Compile Include="AssemblyInfo.cs" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>