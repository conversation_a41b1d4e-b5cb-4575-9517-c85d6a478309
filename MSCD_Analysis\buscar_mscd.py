import csv
import os
import sys
import re

def cargar_codigos_mscd(csv_file):
    """Carga los códigos MSCD desde el archivo CSV generado."""
    mscd_list = []
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            mscd_list.append(row)
    return mscd_list

def buscar_por_codigo(mscd_list, codigo):
    """Busca un código MSCD específico."""
    resultados = []
    for entry in mscd_list:
        if entry['mscd_code'] == codigo:
            resultados.append(entry)
    return resultados

def buscar_por_texto(mscd_list, texto):
    """Busca códigos MSCD que contengan un texto específico en su mensaje."""
    resultados = []
    patron = re.compile(texto, re.IGNORECASE)
    for entry in mscd_list:
        if patron.search(entry['message']):
            resultados.append(entry)
    return resultados

def mostrar_resultados(resultados):
    """Muestra los resultados de la búsqueda en formato legible."""
    if not resultados:
        print("\nNo se encontraron resultados.")
        return
    
    print(f"\nSe encontraron {len(resultados)} resultados:")
    print("-" * 80)
    
    for i, entry in enumerate(resultados, 1):
        print(f"Resultado {i}:")
        print(f"  Código MSCD: {entry['mscd_code']}")
        print(f"  Mensaje: {entry['message']}")
        print(f"  Parámetros de formato: {entry['num_format_params']}")
        print(f"  Parámetro Formato 1: {entry['format_param1']}")
        print(f"  Parámetro Formato 2: {entry['format_param2']}")
        print(f"  Parámetro Formato 3: {entry['format_param3']}")
        print(f"  Parámetro Formato 4: {entry['format_param4']}")
        print(f"  Categoría/Severidad: {entry['category_severity']}")
        if entry['format_info']:
            print(f"  Información de formato: {entry['format_info']}")
        print("-" * 80)

def main():
    # Ruta del archivo CSV
    base_dir = os.path.dirname(os.path.abspath(__file__))
    mscd_csv = os.path.join(base_dir, 'mscd_codes.csv')
    
    if not os.path.exists(mscd_csv):
        print(f"Error: No se encontró el archivo de códigos MSCD en {mscd_csv}")
        print("Ejecute primero mscd_extractor.py para generar el archivo de códigos.")
        return
    
    print("Cargando códigos MSCD...")
    mscd_list = cargar_codigos_mscd(mscd_csv)
    print(f"Se cargaron {len(mscd_list)} códigos MSCD.")
    
    while True:
        print("\nBuscador de Códigos MSCD")
        print("1. Buscar por código MSCD")
        print("2. Buscar por texto en el mensaje")
        print("3. Salir")
        
        opcion = input("Seleccione una opción (1-3): ")
        
        if opcion == '1':
            codigo = input("Ingrese el código MSCD a buscar: ")
            resultados = buscar_por_codigo(mscd_list, codigo)
            mostrar_resultados(resultados)
        
        elif opcion == '2':
            texto = input("Ingrese el texto a buscar en los mensajes: ")
            resultados = buscar_por_texto(mscd_list, texto)
            mostrar_resultados(resultados)
        
        elif opcion == '3':
            print("¡Hasta luego!")
            break
        
        else:
            print("Opción no válida. Intente de nuevo.")

if __name__ == "__main__":
    main()