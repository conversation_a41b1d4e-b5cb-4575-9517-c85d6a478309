﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlowLibrary.clsHDMFLibUtil
// Assembly: HDMoldFlowLibrary, Version=2.24.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 16E5D508-B892-4B5D-A02C-2C1930D27867
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMoldFlowLibrary.dll

using HDLog4Net;
using Microsoft.VisualBasic.CompilerServices;
using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.RegularExpressions;

#nullable disable
namespace HDMoldFlowLibrary
{
  [StandardModule]
  public sealed class clsHDMFLibUtil
  {
    private const int WM_CLOSE = 16;
    private static List<IntPtr> m_lst_procThdHandle = new List<IntPtr>();

    [DllImport("user32")]
    public static extern uint GetWindowThreadProcessId(IntPtr hWnd, ref uint lpdwProcessId);

    [DllImport("user32")]
    private static extern bool EnumThreadWindows(
      int dwThreadIC,
      clsHDMFLibUtil.EnumThreadDelegate lpfn,
      IntPtr lParam);

    [DllImport("user32")]
    public static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);

    [DllImport("user32", CharSet = CharSet.Auto)]
    private static extern IntPtr SendMessage(IntPtr hWnd, int Msg, int wParam, int lParam);

    [DllImport("kernel32")]
    private static extern int GetPrivateProfileString(
      string p_strSection,
      string p_strKey,
      string p_strDef,
      StringBuilder p_stringBuilder,
      int p_isize,
      string p_strFilePath);

    public static void CloseWindow(string p_strText)
    {
      Process[] processesByName = Process.GetProcessesByName("synergy");
      if (processesByName.Length == 0)
        return;
      clsHDMFLibUtil.m_lst_procThdHandle.Clear();
      int num1 = checked (processesByName[0].Threads.Count - 1);
      int index1 = 0;
      while (index1 <= num1)
      {
        int id = processesByName[0].Threads[index1].Id;
        clsHDMFLibUtil.EnumThreadDelegate lpfn;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibUtil._Closure\u0024__.\u0024IR8\u002D1 != null)
        {
          // ISSUE: reference to a compiler-generated field
          lpfn = clsHDMFLibUtil._Closure\u0024__.\u0024IR8\u002D1;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibUtil._Closure\u0024__.\u0024IR8\u002D1 = lpfn = (clsHDMFLibUtil.EnumThreadDelegate) ([SpecialName] (a0, a1) => clsHDMFLibUtil.EnumThreadWndProc((long) a0, (long) a1));
        }
        IntPtr zero = IntPtr.Zero;
        clsHDMFLibUtil.EnumThreadWindows(id, lpfn, zero);
        checked { ++index1; }
      }
      if (clsHDMFLibUtil.m_lst_procThdHandle.Count <= 0)
        return;
      int num2 = checked (clsHDMFLibUtil.m_lst_procThdHandle.Count - 1);
      int index2 = 0;
      while (index2 <= num2)
      {
        StringBuilder lpString = new StringBuilder(1024);
        clsHDMFLibUtil.GetWindowText(clsHDMFLibUtil.m_lst_procThdHandle[index2], lpString, 1024);
        if (lpString.ToString().Contains(p_strText))
          clsHDMFLibUtil.SendMessage(clsHDMFLibUtil.m_lst_procThdHandle[index2], 16, 0, 0);
        checked { ++index2; }
      }
    }

    public static bool EnumThreadWndProc(long hwnd, long lParam)
    {
      clsHDMFLibUtil.m_lst_procThdHandle.Add((IntPtr) hwnd);
      return true;
    }

    public static string GetIniData(string p_strSection, string p_strKey, string p_strFilePath)
    {
      string empty;
      try
      {
        StringBuilder p_stringBuilder = new StringBuilder(1024);
        if (clsHDMFLibUtil.GetPrivateProfileString(p_strSection, p_strKey, "", p_stringBuilder, p_stringBuilder.Capacity, p_strFilePath) > 0)
        {
          empty = p_stringBuilder.ToString();
          goto label_4;
        }
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception(GetIniData):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      empty = string.Empty;
label_4:
      return empty;
    }

    public static string[] GetNewData(List<string> p_lst_strData1, List<string> p_lst_strData2)
    {
      try
      {
        foreach (string str in p_lst_strData1)
          p_lst_strData2.Remove(str);
      }
      finally
      {
        List<string>.Enumerator enumerator;
        enumerator.Dispose();
      }
      return p_lst_strData2.ToArray();
    }

    public static double ConvertToDouble(string p_strValue)
    {
      double result = 0.0;
      if (p_strValue != null & (object) p_strValue != (object) "")
        double.TryParse(p_strValue, out result);
      return result;
    }

    public static int ConvertToInt32(string p_strValue)
    {
      int result = 0;
      if (p_strValue != null & (object) p_strValue != (object) "")
        int.TryParse(p_strValue, out result);
      return result;
    }

    public static bool ConvertToBoolean(string p_strValue)
    {
      bool result = false;
      if (p_strValue != null & (object) p_strValue != (object) "")
        bool.TryParse(p_strValue, out result);
      return result;
    }

    public static string ConvertToLocaleID(string p_strKey)
    {
      string empty = string.Empty;
      return new Regex("[ ,:%()-=.]").Replace(p_strKey.ToUpper(), "");
    }

    public static string ConvertToKelvin(string p_strKey)
    {
      string empty = string.Empty;
      return Math.Round(clsHDMFLibUtil.ConvertToDouble(p_strKey) - 273.15, 4).ToString();
    }

    public static string GetMoldflowLocale()
    {
      string moldflowLocale = "enu";
      try
      {
        clsHDMFLib.GetMoldflowVersion();
        RegistryKey registryKey = Registry.CurrentUser.OpenSubKey("SOFTWARE\\Autodesk\\Moldflow Synergy\\" + clsHDMFLibDefine.m_strVersion.Replace(" ", "") + "\\Environment", false);
        if (registryKey != null & (object) registryKey != (object) "")
          moldflowLocale = Conversions.ToString(registryKey.GetValue("MFSYN_LOCALE"));
        registryKey.Close();
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception(GetMoldflowLocale):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      return moldflowLocale;
    }

    public static void GetLocaleValue(ref string p_strKey)
    {
      string empty = string.Empty;
      string localeId = clsHDMFLibUtil.ConvertToLocaleID(p_strKey);
      if (!clsHDMFLibDefine.m_dicLang.ContainsKey(localeId))
        return;
      p_strKey = clsHDMFLibDefine.m_dicLang[localeId];
    }

    public delegate bool EnumThreadDelegate(IntPtr hWnd, IntPtr lParam);
  }
}
