﻿// Decompiled with JetBrains decompiler
// Type: Microsoft.Office.Interop.PowerPoint.PpPlaceholderType
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

namespace Microsoft.Office.Interop.PowerPoint
{
  [CompilerGenerated]
  [Guid("ADEADB7E-F268-4574-90FE-BC0BF4B28B3C")]
  [TypeIdentifier("91493440-5a91-11cf-8700-00aa0060263b", "Microsoft.Office.Interop.PowerPoint.PpPlaceholderType")]
  public enum PpPlaceholderType
  {
    ppPlaceholderMixed = -2, // 0xFFFFFFFE
    ppPlaceholderTitle = 1,
    ppPlaceholderBody = 2,
    ppPlaceholderCenterTitle = 3,
    ppPlaceholderSubtitle = 4,
    ppPlaceholderVerticalTitle = 5,
    ppPlaceholderVerticalBody = 6,
    ppPlaceholderObject = 7,
    ppPlaceholderChart = 8,
    ppPlaceholderBitmap = 9,
    ppPlaceholderMediaClip = 10, // 0x0000000A
    ppPlaceholderOrgChart = 11, // 0x0000000B
    ppPlaceholderTable = 12, // 0x0000000C
    ppPlaceholderSlideNumber = 13, // 0x0000000D
    ppPlaceholderHeader = 14, // 0x0000000E
    ppPlaceholderFooter = 15, // 0x0000000F
    ppPlaceholderDate = 16, // 0x00000010
    ppPlaceholderVerticalObject = 17, // 0x00000011
    ppPlaceholderPicture = 18, // 0x00000012
  }
}
