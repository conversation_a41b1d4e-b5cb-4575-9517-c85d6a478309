import re
import csv
import os
import subprocess

def parse_mscd_file(file_path):
    """Analiza el archivo cmmesage.dat y extrae los códigos MSCD con sus parámetros."""
    mscd_entries = []
    current_entry = None
    
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
        content = file.read()
        
    # Dividir el archivo por las secciones separadas por '----'
    sections = content.split('----')
    
    for section in sections:
        section = section.strip()
        if not section:
            continue
            
        # Buscar la línea que comienza con MSCD
        mscd_match = re.search(r'^MSCD\s+(\d+)\s+(\d+)\s+(\d+)\s+(\d+)\s+(\d+)\s+(\d+)\s+(\d+)\s+(\d+)', 
                              section, re.MULTILINE)
        
        if mscd_match:
            # Extraer los parámetros del código MSCD
            mscd_code = mscd_match.group(1)
            num_lines = mscd_match.group(2)  # Número de líneas del mensaje
            format_param1 = mscd_match.group(3)  # Parámetro de formato 1
            format_param2 = mscd_match.group(4)  # Parámetro de formato 2
            format_param3 = mscd_match.group(5)  # Parámetro de formato 3
            format_param4 = mscd_match.group(6)  # Parámetro de formato 4
            num_format_params = mscd_match.group(7)  # Número de parámetros de formato
            category_severity = mscd_match.group(8)  # Categoría o severidad
            
            # Extraer el mensaje (todo lo que sigue después de la línea MSCD)
            message_lines = section.split('\n')[1:]
            
            # Separar el mensaje principal de las unidades/formato
            message_text_lines = []
            units_lines = []
            
            for line in message_lines:
                if re.match(r'^\s*[\w\^\d\-]+,\d+,\d+', line):
                    units_lines.append(line.strip())
                else:
                    message_text_lines.append(line)
            
            # Limpiar el mensaje para el CSV (eliminar espacios extra y saltos de línea)
            message_text = ' '.join([line.strip() for line in message_text_lines]).strip()
            message_text = re.sub(r'\s+', ' ', message_text)  # Reemplazar múltiples espacios por uno solo
            
            # Extraer información de formato y unidades
            format_info = []
            for unit_line in units_lines:
                unit_line = unit_line.strip()
                if unit_line:
                    format_info.append(unit_line)
            
            mscd_entries.append({
                'mscd_code': mscd_code,
                'num_lines': num_lines,
                'format_param1': format_param1,
                'format_param2': format_param2,
                'format_param3': format_param3,
                'format_param4': format_param4,
                'num_format_params': num_format_params,
                'category_severity': category_severity,
                'message': message_text,
                'format_info': ';'.join(format_info)
            })
    
    return mscd_entries

def save_to_csv(mscd_entries, output_file):
    """Guarda las entradas MSCD en un archivo CSV."""
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = [
            'mscd_code',           # Código identificador único MSCD
            'num_lines',           # Número de líneas del mensaje
            'format_param1',       # Parámetro de formato 1
            'format_param2',       # Parámetro de formato 2
            'format_param3',       # Parámetro de formato 3
            'format_param4',       # Parámetro de formato 4
            'num_format_params',   # Número de parámetros de formato
            'category_severity',   # Categoría o severidad
            'message',             # Texto del mensaje
            'format_info'          # Información de formato y unidades
        ]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for entry in mscd_entries:
            writer.writerow(entry)

def create_readme(output_dir):
    """Crea un archivo README con información sobre la estructura del CSV."""
    readme_content = """
# Extractor de Códigos MSCD de Moldflow

Este directorio contiene archivos generados para analizar los códigos MSCD utilizados en Moldflow.

## Estructura del archivo CSV

El archivo `mscd_codes.csv` contiene los siguientes campos:

- **mscd_code**: Código identificador único MSCD
- **num_lines**: Número de líneas del mensaje
- **format_param1**: Parámetro de formato 1
- **format_param2**: Parámetro de formato 2
- **format_param3**: Parámetro de formato 3
- **format_param4**: Parámetro de formato 4
- **num_format_params**: Número de parámetros de formato que se esperan
- **category_severity**: Categoría o severidad del mensaje
- **message**: Texto del mensaje
- **format_info**: Información de formato y unidades (separados por punto y coma)

## Uso

Este CSV puede utilizarse para relacionar los códigos MSCD con los valores correspondientes en archivos de resultados .out de Moldflow.
"""
    
    with open(os.path.join(output_dir, 'README.md'), 'w', encoding='utf-8') as f:
        f.write(readme_content)

def extract_with_studyrlt(study_file, output_file, sequence=None, message_id=None, occurrence=None, item=None):
    """
    Ejecuta studyrlt.exe para extraer resultados de un archivo de estudio Moldflow.
    Parámetros opcionales: sequence, message_id, occurrence, item.
    """
    exe_path = r'C:\Program Files\Autodesk\Moldflow Synergy 2025\bin\studyrlt.exe'
    cmd = [exe_path, '-i', study_file, '-o', output_file]
    if sequence:
        cmd += ['-s', str(sequence)]
    if message_id:
        cmd += ['-m', str(message_id)]
    if occurrence:
        cmd += ['-c', str(occurrence)]
    if item:
        cmd += ['-t', str(item)]
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print(f"studyrlt.exe ejecutado correctamente. Salida:\n{result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error al ejecutar studyrlt.exe:\n{e.stderr}")
        return False

def main():
    input_file = r'c:\Moldflow\API_2026\cmmesage.dat'
    output_dir = r'c:\Moldflow\MSCD_Analysis'
    study_file = r'c:\ruta\a\estudio.sdy'  # TODO: Actualizar ruta real
    studyrlt_output = os.path.join(output_dir, 'studyrlt_output.txt')
    # Ejemplo de uso de studyrlt.exe para extraer resultados
    extract_with_studyrlt(study_file, studyrlt_output, sequence=None, message_id=None, occurrence=None, item=None)
    # El resto del flujo sigue igual
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    output_file = os.path.join(output_dir, 'mscd_codes.csv')
    print(f"Analizando archivo: {input_file}")
    mscd_entries = parse_mscd_file(input_file)
    print(f"Se encontraron {len(mscd_entries)} códigos MSCD")
    save_to_csv(mscd_entries, output_file)
    create_readme(output_dir)
    print(f"Resultados guardados en: {output_file}")
    print(f"Se ha creado un archivo README.md con información sobre la estructura del CSV.")

if __name__ == "__main__":
    main()