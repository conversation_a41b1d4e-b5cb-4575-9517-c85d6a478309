﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmValve
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDMFUserControl;
using HDMoldFlow.Properties;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public class frmValve : Form
  {
    private IContainer components = (IContainer) null;
    private Label label_Valve;
    private Label label_VavClose;
    private NewButton newButton_Apply;
    private NewTextBox newTextBox_VavClose;

    public frmValve()
    {
      this.InitializeComponent();
      this.newButton_Apply.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Apply.Image);
      this.SetLocale();
    }

    private void frmValve_Load(object sender, EventArgs e)
    {
      this.ActiveControl = (Control) this.label_VavClose;
      foreach (KeyValuePair<string, string> keyValuePair in clsDefine.g_dicValve)
      {
        KeyValuePair<string, string> kvTmp = keyValuePair;
        ((NewTextBox) this.Controls.Cast<Control>().Where<Control>((Func<Control, bool>) (Temp => Temp.Name.Contains(kvTmp.Key))).FirstOrDefault<Control>()).Value = kvTmp.Value;
      }
    }

    private void SetLocale() => this.label_Valve.Text = LocaleControl.getInstance().GetString("IDS_APPLY");

    private void frmValve_KeyDown(object sender, KeyEventArgs e)
    {
      if (e.KeyCode != Keys.Escape)
        return;
      this.Close();
    }

    private void newButton_Apply_NewClick(object sender, EventArgs e)
    {
      if (this.newTextBox_VavClose.Value == "")
        return;
      clsDefine.g_dicValve["VavClose"] = this.newTextBox_VavClose.Value;
      clsUtill.WriteINI("Valve", "VavClose", clsDefine.g_dicValve["VavClose"], clsDefine.g_fiValveCfg.FullName);
      this.Close();
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.label_Valve = new Label();
      this.label_VavClose = new Label();
      this.newButton_Apply = new NewButton();
      this.newTextBox_VavClose = new NewTextBox();
      this.SuspendLayout();
      this.label_Valve.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Valve.BorderStyle = BorderStyle.FixedSingle;
      this.label_Valve.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Valve.ForeColor = Color.MidnightBlue;
      this.label_Valve.Location = new Point(5, 5);
      this.label_Valve.Name = "label_Valve";
      this.label_Valve.Size = new Size(177, 20);
      this.label_Valve.TabIndex = 5;
      this.label_Valve.Text = "Valve";
      this.label_Valve.TextAlign = ContentAlignment.MiddleCenter;
      this.label_VavClose.BackColor = Color.Lavender;
      this.label_VavClose.BorderStyle = BorderStyle.FixedSingle;
      this.label_VavClose.Location = new Point(5, 24);
      this.label_VavClose.Name = "label_VavClose";
      this.label_VavClose.Size = new Size(97, 23);
      this.label_VavClose.TabIndex = 7;
      this.label_VavClose.Text = "Close at";
      this.label_VavClose.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.ButtonBackColor = Color.White;
      this.newButton_Apply.ButtonText = "적용";
      this.newButton_Apply.FlatBorderSize = 1;
      this.newButton_Apply.FlatStyle = FlatStyle.Flat;
      this.newButton_Apply.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Apply.Image = (Image) Resources.OK;
      this.newButton_Apply.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Apply.Location = new Point(5, 53);
      this.newButton_Apply.Name = "newButton_Apply";
      this.newButton_Apply.Size = new Size(177, 23);
      this.newButton_Apply.TabIndex = 21;
      this.newButton_Apply.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Apply.NewClick += new EventHandler(this.newButton_Apply_NewClick);
      this.newTextBox_VavClose.BackColor = SystemColors.Window;
      this.newTextBox_VavClose.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_VavClose.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_VavClose.IsDigit = true;
      this.newTextBox_VavClose.Lines = new string[1]{ "15" };
      this.newTextBox_VavClose.Location = new Point(101, 24);
      this.newTextBox_VavClose.MultiLine = false;
      this.newTextBox_VavClose.Name = "newTextBox_VavClose";
      this.newTextBox_VavClose.ReadOnly = false;
      this.newTextBox_VavClose.Size = new Size(81, 23);
      this.newTextBox_VavClose.TabIndex = 22;
      this.newTextBox_VavClose.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_VavClose.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_VavClose.TextForeColor = SystemColors.WindowText;
      this.newTextBox_VavClose.Value = "15";
      this.AutoScaleDimensions = new SizeF(7f, 15f);
      this.AutoScaleMode = AutoScaleMode.Font;
      this.BackColor = Color.White;
      this.ClientSize = new Size(188, 83);
      this.Controls.Add((Control) this.newTextBox_VavClose);
      this.Controls.Add((Control) this.newButton_Apply);
      this.Controls.Add((Control) this.label_VavClose);
      this.Controls.Add((Control) this.label_Valve);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.Margin = new Padding(3, 4, 3, 4);
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmValve);
      this.ShowInTaskbar = false;
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "Valve";
      this.Load += new EventHandler(this.frmValve_Load);
      this.KeyDown += new KeyEventHandler(this.frmValve_KeyDown);
      this.ResumeLayout(false);
    }
  }
}
