﻿// Decompiled with JetBrains decompiler
// Type: HDSplash.FrmSplash
// Assembly: HDSplash, Version=1.0.1.0, Culture=neutral, PublicKeyToken=null
// MVID: 57BF8E78-0319-4ECF-A8F5-B637E39756A4
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDSplash.dll

using System;
using System.ComponentModel;
using System.Drawing;
using System.Threading;
using System.Windows.Forms;

namespace HDSplash
{
  public class FrmSplash : Form
  {
    private static Thread m_splashThread;
    private static FrmSplash m_splashForm;
    private static string m_strProductName;
    private static string m_strSplashImagePath;
    private static string m_strProductVersion;
    private IContainer components;
    private Label ProductNameLabel;
    private PictureBox SplashImage;
    private Label LoadingLabel;
    private ProgressBar LoadingBar;
    private Label VersionLabel;
    private Label ProgressStatusLabel;

    public Color cProductNameColor
    {
      get => this.ProductNameLabel.ForeColor;
      set => this.ProductNameLabel.ForeColor = value;
    }

    public Point pProductNameLocation
    {
      get => this.ProductNameLabel.Location;
      set => this.ProductNameLabel.Location = value;
    }

    public Size sProductNameSize
    {
      get => this.ProductNameLabel.Size;
      set => this.ProductNameLabel.Size = value;
    }

    public Font fProductNameFont
    {
      get => this.ProductNameLabel.Font;
      set => this.ProductNameLabel.Font = value;
    }

    public Color cVersionColor
    {
      get => this.VersionLabel.ForeColor;
      set => this.VersionLabel.ForeColor = value;
    }

    public Point pVersionLocation
    {
      get => this.VersionLabel.Location;
      set => this.VersionLabel.Location = value;
    }

    public Size sVersionSize
    {
      get => this.VersionLabel.Size;
      set => this.VersionLabel.Size = value;
    }

    public Font fVersionFont
    {
      get => this.VersionLabel.Font;
      set => this.VersionLabel.Font = value;
    }

    public string strLoadingLabelText
    {
      get => this.LoadingLabel.Text;
      set => this.LoadingLabel.Text = value;
    }

    public Point pLoadingLabelLocation
    {
      get => this.LoadingLabel.Location;
      set => this.LoadingLabel.Location = value;
    }

    public Font fLoadingLabelFont
    {
      get => this.LoadingLabel.Font;
      set => this.LoadingLabel.Font = value;
    }

    public Color cLoadingLabelColor
    {
      get => this.LoadingLabel.ForeColor;
      set => this.LoadingLabel.ForeColor = value;
    }

    public Size sLoadingLabelSize
    {
      get => this.LoadingLabel.Size;
      set => this.LoadingLabel.Size = value;
    }

    public Point pLoadingBarLocation
    {
      get => this.LoadingBar.Location;
      set => this.LoadingBar.Location = value;
    }

    public int iLoadingBarSpeed
    {
      get => this.LoadingBar.MarqueeAnimationSpeed;
      set => this.LoadingBar.MarqueeAnimationSpeed = value;
    }

    public Size sLoadingBarSize
    {
      get => this.LoadingBar.Size;
      set => this.LoadingBar.Size = value;
    }

    public string strProgressStausText
    {
      get => this.ProgressStatusLabel.Text;
      set => this.ProgressStatusLabel.Text = value;
    }

    public Point pProgressStatusLocation
    {
      get => this.ProgressStatusLabel.Location;
      set => this.ProgressStatusLabel.Location = value;
    }

    public Size sProgressStatusSize
    {
      get => this.ProgressStatusLabel.Size;
      set => this.ProgressStatusLabel.Size = value;
    }

    public Color cProgressStatusColor
    {
      get => this.ProgressStatusLabel.ForeColor;
      set => this.ProgressStatusLabel.ForeColor = value;
    }

    public Font fProgressStatusFont
    {
      get => this.ProgressStatusLabel.Font;
      set => this.ProgressStatusLabel.Font = value;
    }

    public FrmSplash() => this.InitializeComponent();

    public void showSplash(
      string strProductName = null,
      string strSplashImagePath = null,
      string strProductVerison = null)
    {
      if (FrmSplash.m_splashThread != null)
        return;
      FrmSplash.m_splashThread = new Thread(new ThreadStart(this.doShowSplash));
      FrmSplash.m_splashThread.IsBackground = true;
      FrmSplash.m_splashThread.Start();
      FrmSplash.m_strProductName = strProductName;
      FrmSplash.m_strSplashImagePath = strSplashImagePath;
      FrmSplash.m_strProductVersion = strProductVerison;
    }

    private void doShowSplash()
    {
      if (FrmSplash.m_splashForm != null)
        return;
      FrmSplash.m_splashForm = this;
      Application.Run((Form) FrmSplash.m_splashForm);
    }

    public void closeSplash()
    {
      if (FrmSplash.m_splashForm.InvokeRequired)
        FrmSplash.m_splashForm.Invoke((Delegate) new MethodInvoker(this.closeSplash));
      else
        Application.ExitThread();
    }

    public void showProgressStatus(string strStatusText)
    {
      if (!this.InvokeRequired)
        return;
      this.Invoke((Delegate) (() => this.ProgressStatusLabel.Text = strStatusText));
    }

    private void setSplash(
      string strProductName,
      string strSplashImagePath,
      string strProductVersion)
    {
      FrmSplash.m_splashForm.Text = strProductName;
      this.VersionLabel.BackColor = Color.Transparent;
      this.VersionLabel.Parent = (Control) this.SplashImage;
      if (strProductVersion != null)
        this.VersionLabel.Text = strProductVersion;
      else
        this.VersionLabel.Visible = false;
      try
      {
        this.SplashImage.SizeMode = PictureBoxSizeMode.StretchImage;
        this.SplashImage.Image = Image.FromFile(strSplashImagePath);
      }
      catch
      {
        this.SplashImage.Image = (Image) null;
        this.SplashImage.BackColor = Color.Gray;
      }
      this.ProductNameLabel.Text = strProductName;
      this.ProductNameLabel.BackColor = Color.Transparent;
      this.ProductNameLabel.Parent = (Control) this.SplashImage;
      this.LoadingLabel.BackColor = Color.Transparent;
      this.LoadingLabel.Parent = (Control) this.SplashImage;
      this.ProgressStatusLabel.BackColor = Color.Transparent;
      this.ProgressStatusLabel.Parent = (Control) this.SplashImage;
    }

    private void FrmSplash_Load(object sender, EventArgs e)
    {
      FrmSplash.m_splashForm.setSplash(FrmSplash.m_strProductName, FrmSplash.m_strSplashImagePath, FrmSplash.m_strProductVersion);
      this.ShowInTaskbar = false;
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.ProductNameLabel = new Label();
      this.SplashImage = new PictureBox();
      this.LoadingLabel = new Label();
      this.LoadingBar = new ProgressBar();
      this.VersionLabel = new Label();
      this.ProgressStatusLabel = new Label();
      ((ISupportInitialize) this.SplashImage).BeginInit();
      this.SuspendLayout();
      this.ProductNameLabel.AutoSize = true;
      this.ProductNameLabel.BackColor = SystemColors.Control;
      this.ProductNameLabel.Font = new Font("Constantia", 27.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.ProductNameLabel.Location = new Point(8, 26);
      this.ProductNameLabel.Name = "ProductNameLabel";
      this.ProductNameLabel.Size = new Size(261, 45);
      this.ProductNameLabel.TabIndex = 1;
      this.ProductNameLabel.Text = "ProductName";
      this.SplashImage.BackColor = SystemColors.Control;
      this.SplashImage.Dock = DockStyle.Fill;
      this.SplashImage.Location = new Point(0, 0);
      this.SplashImage.Name = "SplashImage";
      this.SplashImage.Size = new Size(506, 359);
      this.SplashImage.TabIndex = 0;
      this.SplashImage.TabStop = false;
      this.LoadingLabel.AutoSize = true;
      this.LoadingLabel.BackColor = SystemColors.Control;
      this.LoadingLabel.Font = new Font("Microsoft Sans Serif", 14.25f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.LoadingLabel.Location = new Point(9, 284);
      this.LoadingLabel.Name = "LoadingLabel";
      this.LoadingLabel.Size = new Size(93, 24);
      this.LoadingLabel.TabIndex = 2;
      this.LoadingLabel.Text = "Loading...";
      this.LoadingBar.BackColor = SystemColors.ControlDark;
      this.LoadingBar.Location = new Point(12, 309);
      this.LoadingBar.MarqueeAnimationSpeed = 30;
      this.LoadingBar.Name = "LoadingBar";
      this.LoadingBar.Size = new Size(295, 13);
      this.LoadingBar.Style = ProgressBarStyle.Marquee;
      this.LoadingBar.TabIndex = 3;
      this.VersionLabel.AutoSize = true;
      this.VersionLabel.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.VersionLabel.Location = new Point(253, 293);
      this.VersionLabel.Name = "VersionLabel";
      this.VersionLabel.Size = new Size(48, 15);
      this.VersionLabel.TabIndex = 4;
      this.VersionLabel.Text = "Version";
      this.ProgressStatusLabel.AutoSize = true;
      this.ProgressStatusLabel.BackColor = SystemColors.Control;
      this.ProgressStatusLabel.Font = new Font("HY나무B", 9.75f, FontStyle.Regular, GraphicsUnit.Point, (byte) 129);
      this.ProgressStatusLabel.Location = new Point(13, 325);
      this.ProgressStatusLabel.Name = "ProgressStatusLabel";
      this.ProgressStatusLabel.Size = new Size(0, 13);
      this.ProgressStatusLabel.TabIndex = 5;
      this.AutoScaleDimensions = new SizeF(7f, 12f);
      this.AutoScaleMode = AutoScaleMode.Font;
      this.ClientSize = new Size(506, 359);
      this.Controls.Add((Control) this.ProgressStatusLabel);
      this.Controls.Add((Control) this.VersionLabel);
      this.Controls.Add((Control) this.LoadingBar);
      this.Controls.Add((Control) this.LoadingLabel);
      this.Controls.Add((Control) this.ProductNameLabel);
      this.Controls.Add((Control) this.SplashImage);
      this.FormBorderStyle = FormBorderStyle.None;
      this.Name = nameof (FrmSplash);
      this.ShowIcon = false;
      this.StartPosition = FormStartPosition.CenterScreen;
      this.Text = "Form1";
      this.TopMost = true;
      this.Load += new EventHandler(this.FrmSplash_Load);
      ((ISupportInitialize) this.SplashImage).EndInit();
      this.ResumeLayout(false);
      this.PerformLayout();
    }
  }
}
