﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net45</TargetFramework>
    <ProjectTypeGuids>{3AC096D0-A1C2-E12C-1390-A8335801FDAB};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
    <ReferencePath>$(ProgramFiles)\Common Files\microsoft shared\VSTT\$(VisualStudioVersion)\UITestExtensionPackages</ReferencePath>
    <IsCodedUITest>False</IsCodedUITest>
    <TestProjectType>UnitTest</TestProjectType>
    <AssemblyTitle>SevenZipExtractor.Tests</AssemblyTitle>
    <Product>SevenZipExtractor.Tests</Product>
    <Copyright>Copyright ©  2019</Copyright>
    <OutputPath>bin\$(Configuration)\</OutputPath>
    <Platforms>AnyCPU;x64;x86</Platforms>
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>
  <Import Project="$(VSToolsPath)\TeamTest\Microsoft.TestTools.targets" Condition="Exists('$(VSToolsPath)\TeamTest\Microsoft.TestTools.targets')" />
  <ItemGroup>
    <PackageReference Include="MSTest.TestAdapter" Version="1.3.2" />
    <PackageReference Include="MSTest.TestFramework" Version="1.3.2" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="16.4.0" />
    <PackageReference Include="MSTest.TestAdapter" Version="2.0.0" />
    <PackageReference Include="MSTest.TestFramework" Version="2.0.0" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="TestFiles.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>TestFiles.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\7z.7z" />
    <None Include="Resources\ansimate-arj.arj" />
    <None Include="Resources\lzh.lzh" />
    <None Include="Resources\rar.rar" />
    <None Include="Resources\SevenZip.7z" />
    <None Include="Resources\zip.zip" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\SevenZipExtractor\SevenZipExtractor.csproj" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Update="TestFiles.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>TestFiles.Designer.cs</LastGenOutput>
      <CustomToolNamespace>Resources</CustomToolNamespace>
    </EmbeddedResource>
  </ItemGroup>
</Project>