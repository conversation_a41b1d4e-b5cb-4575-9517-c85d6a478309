'%RunPerInstance
'@
'@ DESCRIPTION
'@ This command will read the Material Tradename from the currently Selected Material
'@ 
'@
'@ SYNTAX
'@ SetShearStress
'@
'@ PARAMETERS
'@ none 
'@
'@ DEPENDENCIES/LIMITATIONS
'@  1. has minimal error chcking
'@@ 

Option Explicit
Dim SynergyGetter, Synergy
 On Error Resume Next
 Set SynergyGetter = GetObject(CreateObject("WScript.Shell").ExpandEnvironmentStrings("%SAInstance%"))
 On Error GoTo 0
 If (Not IsEmpty(SynergyGetter)) Then
   Set Synergy = SynergyGetter.GetSASynergy
 Else
   Set Synergy = CreateObject("synergy.Synergy")
 End If


' Locate the Material Tset and TsetSubID
Dim MaterialID, MaterialSubID, Material2ID, Material2SubID
Call GetMaterialData(MaterialID, MaterialSubID, Material2ID, Material2SubID)

' Read Melt Temperature range for First Material
Dim I, OK, Value, lStr
Dim PlotMgr, Plot, Viewer, maxV, limit
Set Viewer = Synergy.Viewer()
Set PlotMgr = Synergy.PlotManager()

'MSCD 1600202 1 0 0 0 0 0 5
       'Maximum shear stress at wall                     = %8.2f
    ',1,1,0
	
	
	
If MaterialID > 0 Then

	Set Value = Synergy.CreateDoubleArray()
	OK = GetTCodeValue(MaterialID, MaterialSubID, 1804, Value)

End If

		Set Plot = PlotMgr.FindPlotByName("Shear stress at wall")
		
		Viewer.ShowPlot Plot
		Plot.SetScaleOption 0
		Plot.Regenerate
		
		maxV = Plot.GetMaxValue
		limit = Value.Val(0)
		
		If maxV > limit then
		
			'lStr = ""
		'If OK Then
			'For I = 0 To Value.Size()-1
				'lStr = "Hola Valentin"
			'Next
			'MsgBox  lStr,,WScript.ScriptName

		'End if
		
		Plot.SetScaleOption 2
		Plot.SetMinValue Value.Val(0)
		Plot.SetExtendedColor False
			
		MsgBox  "Maximum shear stress limit ( " & limit & "MPa ) has been exceeded"
		
		Plot.Regenerate
		
		else
		
			MsgBox  "Maximum shear stress limit ( " & limit & "MPa ) has NOT been exceeded"
		
		end if
		


If MaterialID > 0 Then
	lStr = ""
	Set Value = Synergy.CreateDoubleArray()
	OK = GetTCodeValue(MaterialID, MaterialSubID, 1806, Value)

End If

		Set Plot = PlotMgr.FindPlotByName("Shear rate")

		Plot.SetScaleOption 0
		Viewer.ShowPlot Plot
		
		maxV = Plot.GetMaxValue
		limit = Value.Val(0)		
		
		If maxV > limit then
		
			'lStr = ""
		'If OK Then
			'For I = 0 To Value.Size()-1
				'lStr = "Hola Valentin"
			'Next
			'MsgBox  lStr,,WScript.ScriptName

		'End if
		
		Plot.SetScaleOption 2
		Plot.SetMinValue Value.Val(0)
		Plot.SetExtendedColor False
		
		MsgBox  "Maximum shear rate limit ( " & limit & " 1/s ) has been exceeded"	
		
		Plot.Regenerate
		
		else
		
			MsgBox  "Maximum shear rate limit ( " & limit & " 1/s ) has NOT been exceeded"
		
		end if

WScript.Quit(0)

Sub GetInjectionData (InjectionID, InjectionSubID, Injection2ID, Injection2SubID)
' Get the relevent Injection TSet data  from a study file
	InjectionID = 0
	InjectionSubID = 0
	Injection2ID = 0
	Injection2SubID = 0
	Dim StudyDoc, Process
	Set StudyDoc = Synergy.StudyDoc()
	Process = StudyDoc.MoldingProcess
	InjectionID = 40000
	If InStr(Process, "REACTIVE") > 0 Then
		InjectionID = 40002
	ElseIf InStr(Process, "REACTIVE") > 0 Then
		InjectionID = 40002
	ElseIf InStr(Process, "THERMOSET") > 0 Then
		InjectionID = 40002
	ElseIf InStr(Process, "MICROCHIP") > 0 Then
		InjectionID = 40002
	ElseIf InStr(Process, "UNDERFILL") > 0 Then
		InjectionID = 40002
	ElseIf InStr(Process, "RTM_OR_SRIM") > 0 Then
		InjectionID = 40002
	End If
	' The SUBID is nmost likely the last SUBID of the type required.
	' There is no programatic way in the API calls to get NDBC so so we cannot
	' be 100% sure.
	Dim PropED, Prop
	Set PropED = Synergy.PropertyEditor()
	Set Prop = PropEd.GetFirstProperty(InjectionID)
	While Not Prop Is nothing
		InjectionSubID = Prop.ID
		Set Prop = PropEd.GetNextPropertyofType(Prop)
	Wend

	' Look for a Second Location
	If (InStr(Process, "OVERMOLDING") > 0)  Or (InStr(Process, "OVER_MOLDING") > 0) Then
		Injection2ID = 40001
	ElseIf InStr(Process, "BI_INJECTION") > 0 Then
		Injection2ID = InjectionID
	ElseIf InStr(Process, "CO_INJECTION") > 0 Then
		Injection2ID = InjectionID
	End if
	If (Injection2ID > 0) Then
		Set Prop = PropEd.GetFirstProperty(Injection2ID)
		While Not Prop Is nothing
			Injection2SubID = Prop.ID
			Set Prop = PropEd.GetNextPropertyofType(Prop)
		Wend
	End If
End Sub

Sub GetMaterialData(MaterialID, MaterialSubID, Material2ID, Material2SubID)
' Get the relevent proccessing TSet data  from a study file
	MaterialID = 0
	MaterialSubID = 0
	Material2ID = 0
	Material2SubID = 0
	' Find Appropriate Injection sets.
	Dim InjectionID, InjectionSubID, Injection2ID, Injection2SubID
	Call GetInjectionData (InjectionID, InjectionSubID, Injection2ID, Injection2SubID)
	' First Material
	Dim PropED, Prop
	Set PropED = Synergy.PropertyEditor()
	Set Prop = PropEd.FindProperty(InjectionID, InjectionSubID)
	If Not Prop Is nothing Then
		Dim Field
		Field = Prop.GetFirstField()
		While Not Field = 0 And (MaterialID < 1)
			' Material reference Tcode
			If Field = 20020 Then
				Dim FieldValues
				Set FieldValues = Prop.FieldValues(Field)
				MaterialID = FieldValues.Val(0)
				MaterialSubID = FieldValues.Val(1)
			End If
			Field = Prop.GetNextField(Field)
		Wend
	End if
	'MsgBox "Material ID = " & MaterialID & " Sub ID = " & MaterialSubID

	' Second Material
	If (Injection2ID > 0) Then
		Set Prop = PropEd.FindProperty(Injection2ID, Injection2SubID)
		If Not Prop Is nothing Then
			Field = Prop.GetFirstField()
			While Not Field = 0 And (Material2ID < 1)
				' Material reference Tcode
				If Field = 20021 Then
					Set FieldValues = Prop.FieldValues(Field)
					Material2ID = FieldValues.Val(0)
					Material2SubID = FieldValues.Val(1)
				End If
				Field = Prop.GetNextField(Field)
			Wend
		End If
		'MsgBox "Second Material ID = " & Material2ID & " Sub ID = " & Material2SubID
	End if
End Sub

Sub GetProcessData(ProcessID, ProcessSubID, Process2ID, Process2SubID)
' Get the relevent proccessing TSet data  from a study file
	ProcessID = 0
	ProcessSubID = 0
	Process2ID = 0
	Process2SubID = 0
	' Find Appropriate Injection sets.
	Dim InjectionID, InjectionSubID, Injection2ID, Injection2SubID
	Call GetInjectionData (InjectionID, InjectionSubID, Injection2ID, Injection2SubID)
	' First Process
	Set Prop = PropEd.FindProperty(InjectionID, InjectionSubID)
	If Not Prop Is nothing Then
		Dim Field
		Field = Prop.GetFirstField()
		While Not Field = 0 And (ProcessID < 1)
			' Process reference Tcode
			If Field = 20040 Then
				Dim FieldValues
				Set FieldValues = Prop.FieldValues(Field)
				ProcessID = FieldValues.Val(0)
				ProcessSubID = FieldValues.Val(1)
			End If
			Field = Prop.GetNextField(Field)
		Wend
	End if

	' Second Process
	If (Injection2ID > 0) Then
		Set Prop = PropEd.FindProperty(Injection2ID, Injection2SubID)
		If Not Prop Is nothing Then
			Field = Prop.GetFirstField()
			While Not Field = 0 And (Process2ID < 1)
				' Process reference Tcode
				If Field = 20043 Then
					Set FieldValues = Prop.FieldValues(Field)
					Process2ID = FieldValues.Val(0)
					Process2SubID = FieldValues.Val(1)
				End If
				Field = Prop.GetNextField(Field)
			Wend
		End If
	End if
End Sub

Function GetTCodeValue(ID, SubID, TCode, Value)
' Get a Tcode Item Value   Returns itms in visible units.
	GetTCodeValue = False
	Dim PropEd, Prop
	Set PropEd = Synergy.PropertyEditor()
	Set Prop = PropEd.FindProperty(ID, SubID)
	If  Not Prop Is Nothing Then
		Dim AA
		AA = Prop.GetFirstField()
		While AA > 0
			If AA = TCode Then
				Dim Values
				Set Values = Prop.FieldValues(AA)
				If Not Values Is Nothing then
					Set Value = Values
					GetTCodeValue = True
				End If
			End If
			AA = Prop.GetNextField(AA)
		Wend
	End if
End Function

Function SetTCodeValue(ID, SubID, TCode, Value)
' Get a Tcode Item Value   Returns itms in visible units.
	SetTCodeValue = False
	Dim PropEd, Prop
	Set PropEd = Synergy.PropertyEditor()
	Set Prop = PropEd.FindProperty(ID, SubID)
	If  Not Prop Is Nothing Then
		Prop.FieldValues TCode, Value
		PropEd.CommitChanges "Edit"
		SetTCodeValue = True
	End if
End Function

Function GetTCodeUnit(ID, SubID, TCode, UnitStr)
' Get a Tcode Item Unit String  Returns itms in visible unit string
	GetTCodeUnit = False
	Dim PropEd, Prop
	Set PropEd = Synergy.PropertyEditor()
	Set Prop = PropEd.FindProperty(ID, SubID)
	If  Not Prop Is Nothing Then
		Dim AA
		AA = Prop.GetFirstField()
		While AA > 0
			If AA = TCode Then
				Dim Values
				Set Values = Prop.FieldUnits(AA)
				If Not Values Is Nothing Then
					Set UnitStr = Values
					GetTCodeUnit = true
				End If
			End If
			AA = Prop.GetNextField(AA)
		Wend
	End if
End Function

Function GetTCodeDescription(ID, SubID, TCode, Value)
' Get a Tcode description
	Value = ""
	GetTCodeDescription = False
	Dim PropEd, Prop
	Set PropEd = Synergy.PropertyEditor()
	Set Prop = PropEd.FindProperty(ID, SubID)
	If  Not Prop Is Nothing Then
		Dim AA
		AA = Prop.GetFirstField()
		While AA > 0
			If AA = TCode Then
				Value = Prop.FieldDescription(AA)
				GetTCodeDescription = true
			End If
			AA = Prop.GetNextField(AA)
		Wend
	End if
End Function

Function GetTCodeName(ID, TCode, Value)
' Get a Tcode description
	Value = ""
	GetTCodeName = False
	Dim PropEd
	Set PropEd = Synergy.PropertyEditor()
	Value = PropEd.GetDataDEscription(ID, TCode)
	If Value <> "" Then
		GetTCodeName = true
	End if
End Function

