﻿// Decompiled with JetBrains decompiler
// Type: HDMFUserControl.UnitTextBox
// Assembly: HDMFUserControl, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 4651D75A-87CE-415F-80A5-EBC4E2EC2106
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFUserControl.dll

using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace HDMFUserControl
{
  public class UnitTextBox : UserControl
  {
    private bool m_Digit;
    private IContainer components;
    private TextBox textBox_Main;
    private Label label_Unit;

    public UnitTextBox()
    {
      this.InitializeComponent();
      this.textBox_Main.KeyPress += new KeyPressEventHandler(this.TextBox_Main_KeyPress);
    }

    public string Value
    {
      get => this.textBox_Main.Text;
      set => this.textBox_Main.Text = value;
    }

    public string Unit
    {
      get => this.label_Unit.Text;
      set => this.label_Unit.Text = value;
    }

    public HorizontalAlignment TextAlign
    {
      get => this.textBox_Main.TextAlign;
      set => this.textBox_Main.TextAlign = value;
    }

    public Color ControlBackColor
    {
      get => this.BackColor;
      set
      {
        this.BackColor = value;
        this.textBox_Main.BackColor = value;
        this.label_Unit.BackColor = value;
      }
    }

    public bool IsDigit
    {
      get => this.m_Digit;
      set => this.m_Digit = value;
    }

    public bool ReadOnly
    {
      get => this.textBox_Main.ReadOnly;
      set => this.textBox_Main.ReadOnly = value;
    }

    private void TextBox_Main_KeyPress(object sender, KeyPressEventArgs e)
    {
      if (!this.m_Digit || char.IsDigit(e.KeyChar) || (int) e.KeyChar == (int) Convert.ToChar((object) Keys.Back) || e.KeyChar == '.' || e.KeyChar == '-')
        return;
      e.Handled = true;
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.textBox_Main = new TextBox();
      this.label_Unit = new Label();
      this.SuspendLayout();
      this.textBox_Main.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.textBox_Main.BorderStyle = BorderStyle.None;
      this.textBox_Main.Location = new Point(25, 2);
      this.textBox_Main.Name = "textBox_Main";
      this.textBox_Main.Size = new Size(76, 16);
      this.textBox_Main.TabIndex = 0;
      this.label_Unit.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Right;
      this.label_Unit.Font = new Font("Segoe UI", 8f);
      this.label_Unit.Location = new Point(101, -1);
      this.label_Unit.Name = "label_Unit";
      this.label_Unit.Size = new Size(35, 20);
      this.label_Unit.TabIndex = 1;
      this.label_Unit.Text = "cm^3";
      this.label_Unit.TextAlign = ContentAlignment.MiddleRight;
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.BorderStyle = BorderStyle.FixedSingle;
      this.Controls.Add((Control) this.label_Unit);
      this.Controls.Add((Control) this.textBox_Main);
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.Name = nameof (UnitTextBox);
      this.Size = new Size(136, 20);
      this.ResumeLayout(false);
      this.PerformLayout();
    }
  }
}
