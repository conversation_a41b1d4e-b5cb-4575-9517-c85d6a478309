﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmProgress
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDMoldFlow.Properties;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public class frmProgress : Form
  {
    private const int CP_NOCLOSE_BUTTON = 512;
    public Form m_frmParent;
    public string m_strText;
    private IContainer components = (IContainer) null;
    private PictureBox pictureBox1;
    private Label label_Text;

    public frmProgress() => this.InitializeComponent();

    protected override CreateParams CreateParams
    {
      get
      {
        CreateParams createParams = base.CreateParams;
        createParams.ClassStyle |= 512;
        return createParams;
      }
    }

    [DllImport("user32.dll")]
    private static extern bool SetForegroundWindow(IntPtr hWnd);

    private void frmProgress_Load(object sender, EventArgs e)
    {
      if (this.m_frmParent != null)
      {
        this.Top = this.m_frmParent.Top + (this.m_frmParent.Height / 2 - this.Height / 2);
        this.Left = this.m_frmParent.Left + (this.m_frmParent.Width / 2 - this.Width / 2);
      }
      else
        this.CenterToScreen();
      this.label_Text.Text = this.m_strText;
      frmProgress.SetForegroundWindow(this.Handle);
      this.TopMost = true;
    }

    public void UpdateProgress(string p_strText)
    {
      frmProgress.SetForegroundWindow(this.Handle);
      this.label_Text.Text = p_strText;
    }

    public void CloseProgress() => this.Close();

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.pictureBox1 = new PictureBox();
      this.label_Text = new Label();
      ((ISupportInitialize) this.pictureBox1).BeginInit();
      this.SuspendLayout();
      this.pictureBox1.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.pictureBox1.Image = (Image) Resources.Loading;
      this.pictureBox1.Location = new Point(57, -1);
      this.pictureBox1.Name = "pictureBox1";
      this.pictureBox1.Size = new Size(190, 43);
      this.pictureBox1.SizeMode = PictureBoxSizeMode.StretchImage;
      this.pictureBox1.TabIndex = 0;
      this.pictureBox1.TabStop = false;
      this.label_Text.Dock = DockStyle.Bottom;
      this.label_Text.Location = new Point(0, 43);
      this.label_Text.Name = "label_Text";
      this.label_Text.Size = new Size(308, 30);
      this.label_Text.TabIndex = 1;
      this.label_Text.Text = "label1";
      this.label_Text.TextAlign = ContentAlignment.TopCenter;
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(308, 73);
      this.Controls.Add((Control) this.pictureBox1);
      this.Controls.Add((Control) this.label_Text);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.MaximizeBox = false;
      this.Name = nameof (frmProgress);
      this.StartPosition = FormStartPosition.CenterParent;
      this.TopMost = true;
      this.TransparencyKey = SystemColors.Control;
      this.Load += new EventHandler(this.frmProgress_Load);
      ((ISupportInitialize) this.pictureBox1).EndInit();
      this.ResumeLayout(false);
    }
  }
}
