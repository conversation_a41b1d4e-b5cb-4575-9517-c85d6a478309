﻿// Decompiled with JetBrains decompiler
// Type: HDMFAI.ControlExtensions
// Assembly: HDMFAI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: A66A47CE-D63B-4ACB-B9A4-A4F118252C4E
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFAI.dll

using System;
using System.Windows.Forms;

namespace HDMFAI
{
  internal static class ControlExtensions
  {
    public static void InvokeIFNeeded(this Control p_Control, Action p_Action)
    {
      if (p_Control.InvokeRequired)
        p_Control.Invoke((Delegate) p_Action);
      else
        p_Action();
    }
  }
}
