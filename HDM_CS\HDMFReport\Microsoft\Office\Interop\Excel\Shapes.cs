﻿// Decompiled with JetBrains decompiler
// Type: Microsoft.Office.Interop.Excel.Shapes
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using Microsoft.Office.Core;
using System.Collections;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

namespace Microsoft.Office.Interop.Excel
{
  [CompilerGenerated]
  [InterfaceType(2)]
  [DefaultMember("_Default")]
  [Guid("0002443A-0000-0000-C000-000000000046")]
  [TypeIdentifier]
  [ComImport]
  public interface Shapes : IEnumerable
  {
    [SpecialName]
    [MethodImpl(MethodCodeType = MethodCodeType.Runtime)]
    sealed extern void _VtblGap1_5();

    [DispId(0)]
    [MethodImpl(MethodImplOptions.PreserveSig | MethodImplOptions.InternalCall, MethodCodeType = MethodCodeType.Runtime)]
    [return: MarshalAs(UnmanagedType.Interface)]
    Shape _Default([MarshalAs(UnmanagedType.Struct), In] object Index);

    [SpecialName]
    [MethodImpl(MethodCodeType = MethodCodeType.Runtime)]
    sealed extern void _VtblGap2_6();

    [DispId(1723)]
    [MethodImpl(MethodImplOptions.PreserveSig | MethodImplOptions.InternalCall, MethodCodeType = MethodCodeType.Runtime)]
    [return: MarshalAs(UnmanagedType.Interface)]
    Shape AddPicture(
      [MarshalAs(UnmanagedType.BStr), In] string Filename,
      [In] MsoTriState LinkToFile,
      [In] MsoTriState SaveWithDocument,
      [In] float Left,
      [In] float Top,
      [In] float Width,
      [In] float Height);
  }
}
