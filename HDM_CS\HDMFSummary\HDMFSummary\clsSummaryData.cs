﻿// Decompiled with JetBrains decompiler
// Type: HDMFSummary.clsSummaryData
// Assembly: HDMFSummary, Version=2.3.0.0, Culture=neutral, PublicKeyToken=null
// MVID: ********-3B9F-45E9-9723-0DEF72C057B2
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFSummary.dll

using HDLog4Net;
using HDMoldFlowLibrary;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Xml;

namespace HDMFSummary
{
  public class clsSummaryData
  {
    internal static Dictionary<string, string> GetSummaryDataFromMoldFlow(
      string p_strStudyName,
      List<string> p_lst_strALog)
    {
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      string empty3 = string.Empty;
      StringBuilder stringBuilder = new StringBuilder();
      string[] strArray1 = (string[]) null;
      Dictionary<string, string> p_dicData = new Dictionary<string, string>();
      List<DataRow> dataRowList1 = new List<DataRow>((IEnumerable<DataRow>) clsHDMFLib.GetFillingPhaseData(p_lst_strALog).Rows.Cast<DataRow>().ToArray<DataRow>());
      List<DataRow> dataRowList2 = new List<DataRow>((IEnumerable<DataRow>) clsHDMFLib.GetPackingPhaseData(p_lst_strALog).Rows.Cast<DataRow>().ToArray<DataRow>());
      List<DataRow> dataRowList3 = new List<DataRow>();
      dataRowList3.AddRange((IEnumerable<DataRow>) dataRowList1.ToArray());
      dataRowList3.AddRange((IEnumerable<DataRow>) dataRowList2.ToArray());
      try
      {
        p_dicData.Add("FillTime", clsHDMFLib.GetFillingFilledTimeFromLog(dataRowList3));
        p_dicData.Add("Pressure", clsHDMFLib.GetSpruePressureFromLog(dataRowList3));
        p_dicData.Add("ClampForce", clsHDMFLib.GetClampForceFromLog2(dataRowList3));
        p_dicData.Add("VPSwitchOverPressure", clsHDMFLib.GetVPSwitchOverVolumeFromLog(dataRowList1));
        string meshType = clsHDMFLib.GetMeshType();
        p_dicData.Add("Study_Name", p_strStudyName);
        p_dicData.Add("Mesh_Type", meshType);
        string studySequence = clsHDMFLib.GetStudySequence();
        p_dicData.Add("Sequence", studySequence.Replace('|', ','));
        p_dicData.Add("Part_Volume", clsHDMFLib.GetPartVolumeFromLog(p_lst_strALog));
        p_dicData.Add("Projected_Area", clsHDMFLib.GetProjectedAreaFromLog(p_lst_strALog));
        p_dicData.Add("Manufacturer", clsHDMFLib.GetManufacturer());
        p_dicData.Add("Trade_Name", clsHDMFLib.GetTradeName());
        p_dicData.Add("Family_Name", clsHDMFLib.GetFamilyAbbreviation());
        p_dicData.Add("Transition_Temp", clsHDMFLib.GetTransitionTemperature());
        p_dicData.Add("Filler_Properties", clsHDMFLib.GetFillerPropertiesFromLog(p_lst_strALog));
        p_dicData.Add("Filler_Weight", clsHDMFLib.GetFillerWeightFromLog(p_lst_strALog));
        p_dicData.Add("Injection_Pressure", clsHDMFLib.GetInjectionPressure());
        p_dicData.Add("Injection_Clamp_Force", clsHDMFLib.GetInjectionClampForce());
        p_dicData.Add("Melt_Temp", clsHDMFLib.GetMeltTemperatureDataFromProcessSet());
        p_dicData.Add("Mold_Temp", clsHDMFLib.GetMoldTemperatureDataFromProcessSet());
        string fillTimeFromLog = clsHDMFLib.GetFillTimeFromLog(p_lst_strALog);
        if (fillTimeFromLog != string.Empty)
        {
          string[] strArray2 = fillTimeFromLog.Split('|');
          p_dicData.Add("Filling_Ctrl_Type", strArray2[0]);
          if (strArray2[0] == "Injection")
            p_dicData.Add("Fill_time", strArray2[1]);
          else
            p_dicData.Add("Nominal_flow_rate", strArray2[1]);
        }
        string switchOverFromLog = clsHDMFLib.GetVelocityPressureSwitchOverFromLog(p_lst_strALog);
        if (switchOverFromLog != string.Empty)
        {
          p_dicData.Add("VP_Ctrl_Type", "By % volume filled");
          p_dicData.Add("VP_Value", switchOverFromLog);
        }
        string controlFromProcess = clsHDMFLib.GetPackHoldingControlFromProcess();
        if (controlFromProcess != "")
        {
          string[] strArray3 = controlFromProcess.Split('/');
          for (int index = 0; index < strArray3.Length; ++index)
          {
            strArray1 = strArray3[index].Split('|');
            if (index == 0)
            {
              empty3 = strArray1[3];
            }
            else
            {
              p_dicData.Add("Pack_Time_" + index.ToString(), strArray1[0].Trim() + "|s");
              p_dicData.Add("Pack_Pressure_" + index.ToString(), strArray1[1].Trim() + "|" + empty3);
            }
          }
        }
        string empty4 = string.Empty;
        if (dataRowList2.Count != 0)
          empty4 = (Math.Round(clsHDMFLibUtil.ConvertToDouble(clsHDMFLib.GetFillLastTimeFromLog(dataRowList2)), 2) - Math.Round(clsHDMFLibUtil.ConvertToDouble(clsHDMFLib.GetPhaseZeroPressureTimeFromLog(dataRowList3)), 2)).ToString();
        p_dicData.Add("Cooling_Time", empty4);
        p_dicData.Add("Filling_Result_Filled_Time", clsHDMFLib.GetFillingFilledTimeFromLog(dataRowList1));
        p_dicData.Add("ShortShot_Check", clsHDMFLib.GetShortShotFromLog(p_lst_strALog));
        string str1 = !(meshType == "3D") ? clsHDMFLib.GetSpruePressureFromLog(dataRowList3) : clsHDMFLib.GetInjectionPressureFromAllPhase(dataRowList1);
        p_dicData.Add("Filling_Result_Pressure_Max", str1 + "|MPa");
        p_dicData.Add("Filling_Result_Clamp_Force_Max", clsHDMFLib.GetClampForceFromLog2(dataRowList3) + "|tone");
        p_dicData.Add("Packing_Result_Pressure_Max", clsHDMFLib.GetInjectionPressureFromAllPhase(dataRowList2) + "|MPa");
        p_dicData.Add("Packing_Result_Clamp_Force_Max", clsHDMFLib.GetClampForceFromAllPhase(dataRowList2) + "|tone");
        p_dicData.Add("Packing_Result_95_Frozen_Time", clsHDMFLib.GetPercentFrozenTimeFromLog(95, dataRowList3) + "|s");
        string partMassFromLog = clsHDMFLib.GetPartMassFromLog(p_lst_strALog);
        if (partMassFromLog != string.Empty)
          p_dicData.Add("Packing_Result_Part_Mass", partMassFromLog + "|g");
        string shrinkageFromLog2 = clsHDMFLib.GetVolumetricShrinkageFromLog2(p_lst_strALog);
        if (shrinkageFromLog2 != string.Empty)
        {
          string[] strArray4 = shrinkageFromLog2.Split('|');
          p_dicData.Add("Packing_Result_Vol_Shk_Min", strArray4[0]);
          p_dicData.Add("Packing_Result_Vol_Shk_Max", strArray4[1]);
          p_dicData.Add("Packing_Result_Vol_Shk_Ave", strArray4[2]);
          p_dicData.Add("VolumetricShrinkage", strArray4[0] + "|" + strArray4[1]);
        }
        string transDataFromLog = clsHDMFLib.GetTransDataFromLog(1, p_lst_strALog);
        if (transDataFromLog != string.Empty)
        {
          string[] strArray5 = transDataFromLog.Split('|');
          for (int index = 0; index < strArray5.Length; ++index)
          {
            string str2;
            switch (index)
            {
              case 0:
                str2 = "X";
                break;
              case 1:
                str2 = "Y";
                break;
              default:
                str2 = "Z";
                break;
            }
            p_dicData.Add("Warp_Result_Total_Deflection_" + str2 + "_Min", strArray1[0] + "|mm");
            p_dicData.Add("Warp_Result_Total_Deflection_" + str2 + "_Max", strArray1[1] + "|mm");
          }
        }
        clsSummaryData.SaveSummaryToXml(p_dicData);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsLogSummary]GetSummaryDataFromMoldFlow):" + ex.Message));
      }
      return p_dicData;
    }

    private static void SaveSummaryToXml(Dictionary<string, string> p_dicData)
    {
      try
      {
        FileInfo fileInfo = new FileInfo(clsSummaryDefine.g_strSaveFileName + "_Summary.xml");
        if (!fileInfo.Directory.Exists)
          fileInfo.Directory.Create();
        if (fileInfo.Exists)
          fileInfo.Delete();
        using (XmlWriter xmlWriter = XmlWriter.Create(fileInfo.FullName, new XmlWriterSettings()
        {
          Indent = true,
          OmitXmlDeclaration = true,
          NewLineOnAttributes = true
        }))
        {
          xmlWriter.WriteStartDocument();
          xmlWriter.WriteStartElement("Data");
          xmlWriter.WriteEndElement();
          xmlWriter.Flush();
        }
        using (XmlReader reader = XmlReader.Create(fileInfo.FullName))
        {
          XmlDocument xmlDocument = new XmlDocument();
          xmlDocument.Load(reader);
          XmlElement documentElement = xmlDocument.DocumentElement;
          documentElement.RemoveAll();
          foreach (KeyValuePair<string, string> keyValuePair in p_dicData)
          {
            XmlNode element = (XmlNode) xmlDocument.CreateElement(keyValuePair.Key);
            element.InnerText = keyValuePair.Value;
            documentElement.AppendChild(element);
          }
          reader.Close();
          xmlDocument.Save(fileInfo.FullName);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsSummaryData]SaveSummaryToXml):" + ex.Message));
      }
    }

    internal static Dictionary<string, string> GetSummaryDataFromXml(string p_strXmlPath)
    {
      Dictionary<string, string> summaryDataFromXml = new Dictionary<string, string>();
      FileInfo fileInfo = new FileInfo(p_strXmlPath);
      try
      {
        using (XmlReader reader = XmlReader.Create(fileInfo.FullName))
        {
          XmlDocument xmlDocument = new XmlDocument();
          xmlDocument.Load(reader);
          XmlElement documentElement = xmlDocument.DocumentElement;
          if (documentElement != null)
          {
            foreach (XmlNode childNode in documentElement.ChildNodes)
              summaryDataFromXml.Add(childNode.Name, childNode.InnerText);
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsSummaryData]GetSummaryDataFromXml):" + ex.Message));
      }
      return summaryDataFromXml;
    }

    internal static Dictionary<string, string> GetStudySummaryDataFromLogData(
      DataRow p_drStudy,
      List<string> p_lst_strOLog)
    {
      string empty = string.Empty;
      Dictionary<string, string> p_dicSummary = new Dictionary<string, string>();
      try
      {
        List<DataRow> dataRowList1 = new List<DataRow>((IEnumerable<DataRow>) clsHDMFLibOutLog.GetFillingPhaseDataFromOutData(p_lst_strOLog).Rows.Cast<DataRow>().ToArray<DataRow>());
        List<DataRow> dataRowList2 = new List<DataRow>((IEnumerable<DataRow>) clsHDMFLibOutLog.GetPackingPhaseDataFromOutData(p_lst_strOLog).Rows.Cast<DataRow>().ToArray<DataRow>());
        List<DataRow> dataRowList3 = new List<DataRow>();
        dataRowList3.AddRange((IEnumerable<DataRow>) dataRowList1.ToArray());
        dataRowList3.AddRange((IEnumerable<DataRow>) dataRowList2.ToArray());
        p_dicSummary.Add("Study_Name", p_drStudy["Name"].ToString());
        clsSummaryData.GetSummaryDataFromOutLog(p_lst_strOLog, ref p_dicSummary);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsLogSummary]GetAnalyzedDataForLog):" + ex.Message));
      }
      return p_dicSummary;
    }

    private static void GetSummaryDataFromOutLog(
      List<string> p_lst_strOLog,
      ref Dictionary<string, string> p_dicSummary)
    {
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      StringBuilder stringBuilder = new StringBuilder();
      List<DataRow> dataRowList1 = new List<DataRow>((IEnumerable<DataRow>) clsHDMFLibOutLog.GetFillingPhaseDataFromOutData(p_lst_strOLog).Rows.Cast<DataRow>().ToArray<DataRow>());
      List<DataRow> dataRowList2 = new List<DataRow>((IEnumerable<DataRow>) clsHDMFLibOutLog.GetPackingPhaseDataFromOutData(p_lst_strOLog).Rows.Cast<DataRow>().ToArray<DataRow>());
      List<DataRow> dataRowList3 = new List<DataRow>();
      dataRowList3.AddRange((IEnumerable<DataRow>) dataRowList1.ToArray());
      dataRowList3.AddRange((IEnumerable<DataRow>) dataRowList2.ToArray());
      p_dicSummary.Add("FillTime", clsHDMFLib.GetFillingFilledTimeFromLog(dataRowList3));
      p_dicSummary.Add("Pressure", clsHDMFLib.GetSpruePressureFromLog(dataRowList3));
      p_dicSummary.Add("ClampForce", clsHDMFLib.GetClampForceFromLog2(dataRowList3));
      p_dicSummary.Add("VPSwitchOverPressure", clsHDMFLib.GetVPSwitchOverVolumeFromLog(dataRowList1));
      string meshTypeFromOutData = clsHDMFLibOutLog.GetMeshTypeFromOutData(p_lst_strOLog);
      p_dicSummary.Add("Mesh_Type", meshTypeFromOutData);
      p_dicSummary.Add("Sequence", clsHDMFLibOutLog.GetSequenceFromOutData(p_lst_strOLog));
      p_dicSummary.Add("Part_Volume", clsHDMFLibOutLog.GetPartVolumeFromOutData(p_lst_strOLog));
      p_dicSummary.Add("Projected_Area", clsHDMFLibOutLog.GetTotalProjectedAreaFromOutData(p_lst_strOLog));
      string tradeFromOutData = clsHDMFLibOutLog.GetManufactureAndTradeFromOutData(p_lst_strOLog);
      if (tradeFromOutData != string.Empty)
      {
        string[] strArray = tradeFromOutData.Split('|');
        if (strArray.Length > 1)
        {
          p_dicSummary.Add("Manufacturer", strArray[0]);
          p_dicSummary.Add("Trade_Name", strArray[1]);
        }
      }
      p_dicSummary.Add("Family_Name", clsHDMFLibOutLog.GetFamilyNameFromOutData(p_lst_strOLog));
      p_dicSummary.Add("Transition_Temp", clsHDMFLibOutLog.GetTransitionTempFromOutData(p_lst_strOLog));
      p_dicSummary.Add("Filler_Properties", clsHDMFLibOutLog.GetFillerPropertiesFromOutData(p_lst_strOLog));
      p_dicSummary.Add("Filler_Weight", clsHDMFLibOutLog.GetFillerWeightFromOutData(p_lst_strOLog, meshTypeFromOutData));
      p_dicSummary.Add("Injection_Pressure", clsHDMFLibOutLog.GetMachineInjectionPFromOutData(p_lst_strOLog));
      p_dicSummary.Add("Injection_Clamp_Force", clsHDMFLibOutLog.GetMachineClampFFromOutData(p_lst_strOLog));
      p_dicSummary.Add("Melt_Temp", clsHDMFLibOutLog.GetProcessMeltTempFromOutData(p_lst_strOLog));
      p_dicSummary.Add("Mold_Temp", clsHDMFLibOutLog.GetProcessMoldTempFromOutData(p_lst_strOLog));
      string fillingTypeFromOutData = clsHDMFLibOutLog.GetFillingTypeFromOutData(p_lst_strOLog);
      if (fillingTypeFromOutData != string.Empty)
      {
        string[] strArray = fillingTypeFromOutData.Split('|');
        p_dicSummary.Add("Filling_Ctrl_Type", strArray[0]);
        if (strArray[0].Contains("Injection time"))
          p_dicSummary.Add("Fill_time", strArray[1]);
        else
          p_dicSummary.Add("Nominal_flow_rate", strArray[1]);
      }
      string vpTypeFromOutData = clsHDMFLibOutLog.GetProcessVPTypeFromOutData(p_lst_strOLog);
      if (vpTypeFromOutData != string.Empty)
      {
        string[] strArray = vpTypeFromOutData.Split('|');
        p_dicSummary.Add("VP_Ctrl_Type", strArray[0]);
        p_dicSummary.Add("VP_Value", strArray[1]);
      }
      string packDataFromOutData = clsHDMFLibOutLog.GetPackDataFromOutData(p_lst_strOLog);
      if (packDataFromOutData != string.Empty)
      {
        string[] strArray1 = packDataFromOutData.Split('|');
        string str = strArray1[0];
        for (int index = 0; index < strArray1.Length; ++index)
        {
          if (index != 0)
          {
            string[] strArray2 = strArray1[index].Split(',');
            p_dicSummary.Add("Pack_Time_" + index.ToString(), strArray2[0] + "|s");
            p_dicSummary.Add("Pack_Pressure_" + index.ToString(), strArray2[1] + "|" + str);
          }
        }
      }
      p_dicSummary.Add("Cooling_Time", clsHDMFLibOutLog.GetCoolingTimeFromOutData(p_lst_strOLog, dataRowList2));
      string filledDataFromOutData = clsHDMFLibOutLog.GetFilledDataFromOutData(p_lst_strOLog, meshTypeFromOutData);
      if (filledDataFromOutData != string.Empty)
      {
        string[] strArray = filledDataFromOutData.Split(',');
        p_dicSummary.Add("Filling_Result_Filled_Time", strArray[0]);
        string str = clsSummaryUtill.ConvertToDouble(strArray[1]) != 100.0 ? "O" : "X";
        p_dicSummary.Add("ShortShot_Check", str);
      }
      string str1 = !(meshTypeFromOutData == "3D") ? clsHDMFLibOutLog.GetPressureMaxFromOutData(p_lst_strOLog) : clsHDMFLib.GetInjectionPressureFromAllPhase(dataRowList1);
      p_dicSummary.Add("Filling_Result_Pressure_Max", str1 + "|MPa");
      p_dicSummary.Add("Filling_Result_Clamp_Force_Max", clsHDMFLibOutLog.GetFillingClampFMaxFromOutData(p_lst_strOLog) + "|tone");
      p_dicSummary.Add("Packing_Result_Pressure_Max", clsHDMFLib.GetInjectionPressureFromAllPhase(dataRowList2) + "|MPa");
      string str2 = !(meshTypeFromOutData == "3D") ? clsHDMFLibOutLog.GetPackingClampFMaxFromOutData(p_lst_strOLog) : clsHDMFLib.GetClampForceFromAllPhase(dataRowList2);
      p_dicSummary.Add("Packing_Result_Clamp_Force_Max", str2 + "|tone");
      p_dicSummary.Add("Packing_Result_95_Frozen_Time", clsHDMFLib.GetPercentFrozenTimeFromLog(95, dataRowList3) + "|s");
      string partMassFromOutData = clsHDMFLibOutLog.GetPackingPartMassFromOutData(p_lst_strOLog);
      if (partMassFromOutData != string.Empty)
        p_dicSummary.Add("Packing_Result_Part_Mass", partMassFromOutData + "|g");
      string shkDataFromOutData = clsHDMFLibOutLog.GetPackingVolShkDataFromOutData(p_lst_strOLog);
      if (shkDataFromOutData != string.Empty)
      {
        string[] strArray = shkDataFromOutData.Split('|');
        p_dicSummary.Add("Packing_Result_Vol_Shk_Max", strArray[0]);
        p_dicSummary.Add("Packing_Result_Vol_Shk_Min", strArray[1]);
        p_dicSummary.Add("Packing_Result_Vol_Shk_Ave", strArray[2]);
        p_dicSummary.Add("VolumetricShrinkage", strArray[1] + "|" + strArray[0]);
      }
      string deflectionDataFromOutData = clsHDMFLibOutLog.GetDeflectionDataFromOutData(p_lst_strOLog);
      if (!(deflectionDataFromOutData != string.Empty))
        return;
      string[] strArray3 = deflectionDataFromOutData.Split('|')[0].Split(',');
      if (strArray3.Length <= 5)
        return;
      p_dicSummary.Add("Warp_Result_Total_Deflection_X_Min", strArray3[0] + "|mm");
      p_dicSummary.Add("Warp_Result_Total_Deflection_X_Max", strArray3[1] + "|mm");
      p_dicSummary.Add("Warp_Result_Total_Deflection_Y_Min", strArray3[2] + "|mm");
      p_dicSummary.Add("Warp_Result_Total_Deflection_Y_Max", strArray3[3] + "|mm");
      p_dicSummary.Add("Warp_Result_Total_Deflection_Z_Min", strArray3[4] + "|mm");
      p_dicSummary.Add("Warp_Result_Total_Deflection_Z_Max", strArray3[5] + "|mm");
    }
  }
}
