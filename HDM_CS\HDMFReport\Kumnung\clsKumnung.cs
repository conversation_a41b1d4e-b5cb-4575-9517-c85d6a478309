﻿// Decompiled with JetBrains decompiler
// Type: HDMFReport.clsKumnung
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using HDLog4Net;
using HDMoldFlowLibrary;
using Microsoft.Office.Core;
using Microsoft.Office.Interop.PowerPoint;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace HDMFReport
{
  internal class clsKumnung : clsBase
  {
    public override void ExportReport(
      DataRow p_drStudy,
      string p_strLangType,
      clsHDMFLibDefine.Company p_enumCompany)
    {
      Dictionary<string, string> p_dicValue = new Dictionary<string, string>();
      Dictionary<string, string> p_dicView = new Dictionary<string, string>();
      Dictionary<string, string> p_dicUse = new Dictionary<string, string>();
      try
      {
        p_dicValue.Clear();
        p_dicView.Clear();
        p_dicUse.Clear();
        clsKumnungData.GetReportUser(p_drStudy, out p_dicValue, out p_dicView, out p_dicUse);
        this.StartExport(p_drStudy, p_dicValue, p_dicView, p_dicUse, p_enumCompany, p_strLangType);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]ExportReport):" + ex.Message));
      }
    }

    protected override void StartExport(
      DataRow p_drStudy,
      Dictionary<string, string> p_dicValue,
      Dictionary<string, string> p_dicView,
      Dictionary<string, string> p_dicUse,
      clsHDMFLibDefine.Company p_enumCompany,
      string p_strLangType = "KOR")
    {
      Dictionary<string, string> analysisData = clsKumnungData.GetAnalysisData(p_drStudy, p_dicValue, p_dicView);
      int num = 1;
      FileInfo fileInfo;
      while (true)
      {
        fileInfo = new FileInfo(clsReportDefine.g_diProject.Parent.FullName + "\\" + clsReportDefine.g_diProject.Name + "_Export\\" + p_drStudy["Name"] + "_" + DateTime.Now.ToString("yyMMdd") + "_Report_Rev" + (object) num + "." + clsReportDefine.g_dicExtension["PowerPoint"]);
        if (fileInfo.Exists)
          ++num;
        else
          break;
      }
      if (!fileInfo.Directory.Exists)
        fileInfo.Directory.Create();
      try
      {
        // ISSUE: variable of a compiler-generated type
        Presentations presentations = ((_Application) Activator.CreateInstance(Marshal.GetTypeFromCLSID(new Guid("91493441-5A91-11CF-8700-00AA0060263B")))).Presentations;
        FileInfo template = clsReportData.GetTemplate(p_enumCompany, p_strLangType);
        template.Refresh();
        if (template.Exists)
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          Presentation presentation = presentations.Open(template.FullName, MsoTriState.msoTrue, WithWindow: MsoTriState.msoFalse);
          List<Slide> allSlide = this.GetAllSlide(presentation);
          if (p_dicUse.Count > 0)
          {
            List<Slide> slideList = new List<Slide>();
            foreach (KeyValuePair<string, string> keyValuePair in p_dicUse)
            {
              KeyValuePair<string, string> kvpTmp = keyValuePair;
              if (!clsReportUtill.ConvertToBoolean(kvpTmp.Value))
              {
                // ISSUE: variable of a compiler-generated type
                Slide slide = allSlide.Where<Slide>((System.Func<Slide, bool>) (Temp => Temp.Name.Contains(kvpTmp.Key))).FirstOrDefault<Slide>();
                if (slide != null)
                  slideList.Add(slide);
              }
            }
            for (int index = 0; index < slideList.Count; ++index)
            {
              // ISSUE: reference to a compiler-generated method
              slideList[index].Delete();
              allSlide.Remove(slideList[index]);
            }
          }
          foreach (Slide p_slData in allSlide)
          {
            switch (p_slData.Name)
            {
              case "AirTrap":
                this.SetSlide9(p_slData, this.GetSlide9(analysisData));
                continue;
              case "DefXY":
                this.SetSlide14(p_slData, this.GetSlide14(analysisData));
                continue;
              case "DefZ":
                this.SetSlide15(p_slData, this.GetSlide15(analysisData));
                continue;
              case "FillTime_1":
                this.SetSlide6(p_slData, this.GetSlide6(analysisData));
                continue;
              case "FillTime_2":
                this.SetSlide7(p_slData, this.GetSlide7(analysisData));
                continue;
              case "Info_1":
                this.SetSlide3(p_slData, this.GetSlide3(analysisData));
                continue;
              case "Info_2":
                this.SetSlide4(p_slData, this.GetSlide4(analysisData));
                continue;
              case "Info_3":
                this.SetSlide5(p_slData, this.GetSlide5(analysisData));
                continue;
              case "PressureClamp":
                this.SetSlide11(p_slData, this.GetSlide11(analysisData));
                continue;
              case "SinkMark":
                this.SetSlide13(p_slData, this.GetSlide13(analysisData));
                continue;
              case "Summary":
                this.SetSlide2(p_slData, this.GetSlide2(analysisData));
                continue;
              case "Title":
                this.SetSlide1(p_slData, this.GetSlide1(analysisData));
                continue;
              case "WeldMeldLine":
                this.SetSlide10(p_slData, this.GetSlide10(analysisData));
                continue;
              default:
                continue;
            }
          }
          if (!fileInfo.Extension.Contains(".ppt"))
          {
            // ISSUE: reference to a compiler-generated method
            presentation.SaveAs(fileInfo.FullName.Replace(fileInfo.Extension, ".pptx"));
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            presentation.SaveAs(fileInfo.FullName);
          }
          // ISSUE: reference to a compiler-generated method
          presentation.Close();
          clsReportUtill.ReleaseComObject((object) presentation);
        }
        if (fileInfo.Extension.Contains(".ppt"))
          return;
        if (fileInfo.Exists)
          fileInfo.Delete();
        File.Move(fileInfo.FullName.Replace(fileInfo.Extension, ".pptx"), fileInfo.FullName);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]StartExport):" + ex.Message));
      }
    }

    private List<Slide> GetAllSlide(Presentation p_pptPre)
    {
      List<Slide> allSlide = new List<Slide>((IEnumerable<Slide>) p_pptPre.Slides.Cast<Slide>().ToArray<Slide>());
      try
      {
        for (int index = 0; index < allSlide.Count; ++index)
        {
          switch (index)
          {
            case 0:
              allSlide[index].Name = "Title";
              break;
            case 1:
              allSlide[index].Name = "Summary";
              break;
            case 2:
              allSlide[index].Name = "Info_1";
              break;
            case 3:
              allSlide[index].Name = "Info_2";
              break;
            case 4:
              allSlide[index].Name = "Info_3";
              break;
            case 5:
              allSlide[index].Name = "FillTime_1";
              break;
            case 6:
              allSlide[index].Name = "FillTime_2";
              break;
            case 7:
              allSlide[index].Name = "Temperature";
              break;
            case 8:
              allSlide[index].Name = "AirTrap";
              break;
            case 9:
              allSlide[index].Name = "WeldMeldLine";
              break;
            case 10:
              allSlide[index].Name = "PressureClamp";
              break;
            case 11:
              allSlide[index].Name = "Shrinkage";
              break;
            case 12:
              allSlide[index].Name = "SinkMark";
              break;
            case 13:
              allSlide[index].Name = "DefXY";
              break;
            case 14:
              allSlide[index].Name = "DefZ";
              break;
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetAllSlide):" + ex.Message));
      }
      return allSlide;
    }

    private Dictionary<string, string> GetSlide1(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide1 = new Dictionary<string, string>();
      try
      {
        slide1.Add("Date", DateTime.Now.ToString("yy. MM. dd"));
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Engineer[User]")))
          slide1.Add("Engineer", p_dicData["Engineer[User]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Manager[User]")))
          slide1.Add("Manager", p_dicData["Manager[User]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide1):" + ex.Message));
      }
      return slide1;
    }

    private Dictionary<string, string> GetSlide2(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide2 = new Dictionary<string, string>();
      try
      {
        slide2.Add("Date", DateTime.Now.ToString("yy. MM. dd"));
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FamilyName[MF]")))
          slide2.Add("FamilyName", p_dicData["FamilyName[MF]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide2):" + ex.Message));
      }
      return slide2;
    }

    private Dictionary<string, string> GetSlide3(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide3 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Thickness[IMG]")))
          slide3.Add("Image1", p_dicData["Thickness[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FamilyName[MF]")))
          slide3.Add("FamilyName", p_dicData["FamilyName[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TradeName[MF]")))
          slide3.Add("TradeName", p_dicData["TradeName[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MeltTemp[MF]")))
          slide3.Add("MeltTemp", p_dicData["MeltTemp[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperatureCavity[Log]")))
        {
          string[] strArray = p_dicData["CircuitCoolantTemperatureCavity[Log]"].Split('|');
          slide3.Add("CavityTemp", strArray[0]);
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperatureCore[Log]")))
        {
          string[] strArray = p_dicData["CircuitCoolantTemperatureCore[Log]"].Split('|');
          slide3.Add("CoreTemp", strArray[0]);
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillingControl[MF]")))
          slide3.Add("FillingControl", p_dicData["FillingControl[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VPSwitchOver[MF]")))
          slide3.Add("VPSwitchOver", p_dicData["VPSwitchOver[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PackHoldingControl[MF]")))
          slide3.Add("PackHoldingControl", p_dicData["PackHoldingControl[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CoolingTime[MF]")))
          slide3.Add("CoolingTime", p_dicData["CoolingTime[MF]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide3):" + ex.Message));
      }
      return slide3;
    }

    private Dictionary<string, string> GetSlide4(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide4 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Gate1[IMG]")))
          slide4.Add("Image1", p_dicData["Gate1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Gate2[IMG]")))
          slide4.Add("Image2", p_dicData["Gate2[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide4):" + ex.Message));
      }
      return slide4;
    }

    private Dictionary<string, string> GetSlide5(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide5 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperature[IMG]")))
          slide5.Add("Image1", p_dicData["CircuitCoolantTemperature[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide5):" + ex.Message));
      }
      return slide5;
    }

    private Dictionary<string, string> GetSlide6(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide6 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime6_1[IMG]")))
          slide6.Add("Image1", p_dicData["FillTime6_1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime6_2[IMG]")))
          slide6.Add("Image2", p_dicData["FillTime6_2[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime6_3[IMG]")))
          slide6.Add("Image3", p_dicData["FillTime6_3[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillAnimation6_1[IMG]")))
          slide6.Add("Animation1", p_dicData["FillAnimation6_1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillAnimation6_2[IMG]")))
          slide6.Add("Animation2", p_dicData["FillAnimation6_2[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillAnimation6_3[IMG]")))
          slide6.Add("Animation3", p_dicData["FillAnimation6_3[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide6):" + ex.Message));
      }
      return slide6;
    }

    private Dictionary<string, string> GetSlide7(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide7 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime7_1[IMG]")))
          slide7.Add("Image1", p_dicData["FillTime7_1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime7_2[IMG]")))
          slide7.Add("Image2", p_dicData["FillTime7_2[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime7_3[IMG]")))
          slide7.Add("Image3", p_dicData["FillTime7_3[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime7_4[IMG]")))
          slide7.Add("Image4", p_dicData["FillTime7_4[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime7_5[IMG]")))
          slide7.Add("Image5", p_dicData["FillTime7_5[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide7):" + ex.Message));
      }
      return slide7;
    }

    private Dictionary<string, string> GetSlide8(Dictionary<string, string> p_dicData)
    {
      StringBuilder stringBuilder = new StringBuilder();
      Dictionary<string, string> slide8 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Manufacturer[MF]")))
          stringBuilder.Append(p_dicData["Manufacturer[MF]"]);
        stringBuilder.Append("_");
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TradeName[MF]")))
          stringBuilder.Append(p_dicData["TradeName[MF]"]);
        stringBuilder.Append("_");
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FamilyAbbreviation[MF]")))
          stringBuilder.Append(p_dicData["FamilyAbbreviation[MF]"]);
        slide8.Add("Material", stringBuilder.ToString());
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide8):" + ex.Message));
      }
      return slide8;
    }

    private Dictionary<string, string> GetSlide9(Dictionary<string, string> p_dicData)
    {
      StringBuilder stringBuilder = new StringBuilder();
      Dictionary<string, string> slide9 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "AirTrap[IMG]")))
          slide9.Add("Image1", p_dicData["AirTrap[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime9_1[IMG]")))
          slide9.Add("Image2", p_dicData["FillTime9_1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillTime9_2[IMG]")))
          slide9.Add("Image3", p_dicData["FillTime9_2[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide9):" + ex.Message));
      }
      return slide9;
    }

    private Dictionary<string, string> GetSlide10(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide10 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MeldLine[IMG]")))
          slide10.Add("Image1", p_dicData["MeldLine[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "WeldLine[IMG]")))
          slide10.Add("Image2", p_dicData["WeldLine[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide11):" + ex.Message));
      }
      return slide10;
    }

    private Dictionary<string, string> GetSlide11(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide11 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PressureAtInjectionLocation[IMG]")))
          slide11.Add("Image1", p_dicData["PressureAtInjectionLocation[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ClampForceXY[IMG]")))
          slide11.Add("Image2", p_dicData["ClampForceXY[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SpruePressure[Log]")))
          slide11.Add("Pressure", p_dicData["SpruePressure[Log]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ClampForce[Log]")))
          slide11.Add("ClampForce", p_dicData["ClampForce[Log]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide12):" + ex.Message));
      }
      return slide11;
    }

    private Dictionary<string, string> GetSlide12(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide12 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PressureAtInjectionLocation[IMG]")))
          slide12.Add("Image1", p_dicData["PressureAtInjectionLocation[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ClampForceXY[IMG]")))
          slide12.Add("Image2", p_dicData["ClampForceXY[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide12):" + ex.Message));
      }
      return slide12;
    }

    private Dictionary<string, string> GetSlide13(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide13 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SinkMark1[IMG]")))
          slide13.Add("Image1", p_dicData["SinkMark1[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide12):" + ex.Message));
      }
      return slide13;
    }

    private Dictionary<string, string> GetSlide14(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide14 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX_BestFit[IMG]")))
          slide14.Add("Image1", p_dicData["DefX_BestFit[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY_BestFit[IMG]")))
          slide14.Add("Image2", p_dicData["DefY_BestFit[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX_BestFit[Plot]")))
          slide14.Add("DefX", p_dicData["DefX_BestFit[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY_BestFit[Plot]")))
          slide14.Add("DefY", p_dicData["DefY_BestFit[Plot]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide12):" + ex.Message));
      }
      return slide14;
    }

    private Dictionary<string, string> GetSlide15(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide15 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ_BestFit[IMG]")))
          slide15.Add("Image1", p_dicData["DefZ_BestFit[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ_BestFit[Plot]")))
          slide15.Add("DefZ", p_dicData["DefZ_BestFit[Plot]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]GetSlide15):" + ex.Message));
      }
      return slide15;
    }

    private void SetSlide1(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S1_Text_Date")).FirstOrDefault<Shape>();
        if (shape1 != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Date")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter("'" + p_dicSData["Date"]);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S1_Text_Name")).FirstOrDefault<Shape>();
        if (shape2 == null || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Engineer")) || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Manager")) || !(p_dicSData["Engineer"] != "") || !(p_dicSData["Manager"] != ""))
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape2.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange1.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange1.InsertAfter(p_dicSData["Engineer"] + " / " + p_dicSData["Manager"]);
        textRange1.Font.Bold = MsoTriState.msoTrue;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]SetSlide1):" + ex.Message));
      }
    }

    private void SetSlide2(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S2_Title_Summary")).FirstOrDefault<Shape>();
        if (shape != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Date")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter("Ⅰ. SUMMARY(DR1, '" + p_dicSData["Date"] + ")");
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S2_Table_Result1")).FirstOrDefault<Shape>().Table;
        if (table == null || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FamilyName")))
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = table.Rows[3].Cells[6].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange1.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange1.InsertAfter(p_dicSData["FamilyName"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]SetSlide2):" + ex.Message));
      }
    }

    private void SetSlide3(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string[] strArray1 = (string[]) null;
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 49.6063f, 168.6614f, 248.5984f, 205.5118f);
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S3_Table_1")).FirstOrDefault<Shape>().Table;
        if (table == null)
          return;
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FamilyName")) && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TradeName")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = table.Rows[2].Cells[5].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(p_dicSData["FamilyName"] + "&" + p_dicSData["FamilyName"]);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MeltTemp")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = table.Rows[4].Cells[5].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(p_dicSData["MeltTemp"]);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CavityTemp")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = table.Rows[5].Cells[5].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter("상측 : " + p_dicSData["CavityTemp"]);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CoreTemp")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = table.Rows[5].Cells[7].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter("하측 : " + p_dicSData["CoreTemp"]);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillingControl")))
        {
          strArray1 = p_dicSData["FillingControl"].Split('|');
          if (strArray1.Length > 1)
          {
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = table.Rows[6].Cells[5].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter(strArray1[1]);
          }
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VPSwitchOver")))
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = table.Rows[7].Cells[5].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(strArray1[1]);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PackHoldingControl")))
        {
          string[] strArray2 = p_dicSData["PackHoldingControl"].Replace("\r\n", "").Split('/');
          if (strArray2.Length > 2)
          {
            for (int index = 1; index < 3; ++index)
            {
              string[] strArray3 = strArray2[index].Replace("\r\n", "").Split('|');
              // ISSUE: variable of a compiler-generated type
              TextRange textRange1 = table.Rows[8].Cells[5 + 2 * (index - 1)].Shape.TextFrame.TextRange;
              // ISSUE: reference to a compiler-generated method
              textRange1.Delete();
              // ISSUE: reference to a compiler-generated method
              textRange1.InsertAfter(strArray3[0]);
              // ISSUE: variable of a compiler-generated type
              TextRange textRange2 = table.Rows[8].Cells[6 + 2 * (index - 1)].Shape.TextFrame.TextRange;
              // ISSUE: reference to a compiler-generated method
              textRange2.Delete();
              // ISSUE: reference to a compiler-generated method
              textRange2.InsertAfter(strArray3[1]);
            }
          }
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CoolingTime")))
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange3 = table.Rows[9].Cells[5].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange3.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange3.InsertAfter(p_dicSData["CoolingTime"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]SetSlide3):" + ex.Message));
      }
    }

    private void SetSlide4(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 225.0709f, 292.535431f, 226.2047f, 220.5354f);
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 472.535431f, 129.2598f, 219.4016f, 190.4882f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]SetSlide4):" + ex.Message));
      }
    }

    private void SetSlide5(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 17.0079f, 157.6063f, 180.2835f, 150.8031f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]SetSlide5):" + ex.Message));
      }
    }

    private void SetSlide6(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 45.07087f, 144.8504f, 163.8425f, 125.2913f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 275.5276f, 144.8504f, 163.8425f, 125.2913f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 505.417328f, 144.8504f, 163.8425f, 125.2913f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Animation1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Animation1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 33.73228f, 322.86615f, 213.7323f, 158.4567f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Animation2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Animation2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 253.1339f, 322.86615f, 213.7323f, 158.4567f);
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Animation3")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Animation3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 472.535431f, 322.86615f, 213.7323f, 158.4567f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]SetSlide6):" + ex.Message));
      }
    }

    private void SetSlide7(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 45.07087f, 144.8504f, 163.8425f, 125.2913f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 275.5276f, 144.8504f, 163.8425f, 125.2913f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 505.417328f, 144.8504f, 163.8425f, 125.2913f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image4")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image4"], MsoTriState.msoFalse, MsoTriState.msoTrue, 146.2677f, 343.2756f, 163.8425f, 123.874f);
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image5")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image5"], MsoTriState.msoFalse, MsoTriState.msoTrue, 360f, 343.2756f, 208.063f, 112.5354f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]SetSlide7):" + ex.Message));
      }
    }

    private void SetSlide9(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 202.3937f, 165.2598f, 308.125977f, 205.7953f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 67.46457f, 369.070862f, 190.2047f, 127.2756f);
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 501.7323f, 365.385834f, 164.409f, 134.3622f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]SetSlide9):" + ex.Message));
      }
    }

    private void SetSlide10(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 61.79528f, 174.3307f, 270.1417f, 186.5197f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 394.86615f, 174.6142f, 268.7244f, 185.6693f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S10_Rectangle_1")).FirstOrDefault<Shape>();
        // ISSUE: reference to a compiler-generated method
        shape1?.ZOrder(MsoZOrderCmd.msoBringToFront);
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S10_Rectangle_2")).FirstOrDefault<Shape>();
        // ISSUE: reference to a compiler-generated method
        shape2?.ZOrder(MsoZOrderCmd.msoBringToFront);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]SetSlide10):" + ex.Message));
      }
    }

    private void SetSlide11(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 45.07087f, 155.622f, 213.7323f, 148.252f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 45.07087f, 348.6614f, 213.7323f, 148.252f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S11_Rectangle_1")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange1 = shape1.TextFrame.TextRange;
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Pressure")))
          {
            // ISSUE: reference to a compiler-generated method
            textRange1.Delete();
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = textRange1.InsertAfter("최대 사출 압력 : " + p_dicSData["Pressure"] + " [Mpa]");
            textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange3 = textRange2.InsertAfter(" -> 사출 압력 양호");
            textRange3.Font.Color.RGB = ColorTranslator.ToOle(Color.Blue);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange4 = textRange3.InsertAfter(Environment.NewLine + Environment.NewLine + "※ ABS 사출 범위 : ~ [Mpa]");
            textRange4.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          }
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S11_Rectangle_2")).FirstOrDefault<Shape>();
        if (shape2 == null)
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange5 = shape2.TextFrame.TextRange;
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ClampForce")))
          return;
        // ISSUE: reference to a compiler-generated method
        textRange5.Delete();
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange6 = textRange5.InsertAfter("형 체 력 : " + p_dicSData["ClampForce"] + " [Ton]");
        textRange6.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange7 = textRange6.InsertAfter(" -> [CAE Tone 사양]");
        textRange7.Font.Color.RGB = ColorTranslator.ToOle(Color.Blue);
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange8 = textRange7.InsertAfter(Environment.NewLine + Environment.NewLine + "※ CAE 형체력 X 1.2(안전계수) X (Cavity수)");
        textRange8.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]SetSlide11):" + ex.Message));
      }
    }

    private void SetSlide13(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 106.8661f, 191.3386f, 236.126f, 155.906f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]SetSlide13):" + ex.Message));
      }
    }

    private void SetSlide14(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 86.74016f, 157.0394f, 203.5276f, 140.315f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 433.13385f, 157.0394f, 203.5276f, 140.315f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S14_Rectangle_1")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange1 = shape1.TextFrame.TextRange;
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX")))
          {
            string[] strArray = p_dicSData["DefX"].Split('|');
            double num = Math.Abs(clsReportUtill.ConvertToDouble(strArray[1])) + Math.Abs(clsReportUtill.ConvertToDouble(strArray[0]));
            // ISSUE: reference to a compiler-generated method
            textRange1.Delete();
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = textRange1.InsertAfter("Max : " + strArray[1] + "           Min : " + strArray[0]);
            textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange3 = textRange2.InsertAfter(Environment.NewLine + Environment.NewLine + "X 방향 변형 : " + num.ToString() + " [mm]");
            textRange3.Font.Color.RGB = ColorTranslator.ToOle(Color.Blue);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange4 = textRange3.InsertAfter(Environment.NewLine + Environment.NewLine + "▶ X방향 비교적 균일하게 수축");
            textRange4.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          }
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S14_Rectangle_2")).FirstOrDefault<Shape>();
        if (shape2 == null)
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange5 = shape2.TextFrame.TextRange;
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY")))
          return;
        string[] strArray1 = p_dicSData["DefY"].Split('|');
        double num1 = Math.Abs(clsReportUtill.ConvertToDouble(strArray1[1])) + Math.Abs(clsReportUtill.ConvertToDouble(strArray1[0]));
        // ISSUE: reference to a compiler-generated method
        textRange5.Delete();
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange6 = textRange5.InsertAfter("Max : " + strArray1[1] + "           Min : " + strArray1[0]);
        textRange6.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange7 = textRange6.InsertAfter(Environment.NewLine + Environment.NewLine + "Y 방향 변형 : " + num1.ToString() + " [mm]");
        textRange7.Font.Color.RGB = ColorTranslator.ToOle(Color.Blue);
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange8 = textRange7.InsertAfter(Environment.NewLine + Environment.NewLine + "▶ Y방향 변형 확인");
        textRange8.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]SetSlide14):" + ex.Message));
      }
    }

    private void SetSlide15(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 246.614f, 151.937f, 204.9449f, 141.4488f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S15_Rectangle_1")).FirstOrDefault<Shape>();
        // ISSUE: reference to a compiler-generated method
        shape1?.ZOrder(MsoZOrderCmd.msoBringToFront);
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S15_Rectangle_2")).FirstOrDefault<Shape>();
        if (shape2 == null)
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape2.TextFrame.TextRange;
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ")))
          return;
        string[] strArray = p_dicSData["DefZ"].Split('|');
        double num = Math.Abs(clsReportUtill.ConvertToDouble(strArray[1])) + Math.Abs(clsReportUtill.ConvertToDouble(strArray[0]));
        // ISSUE: reference to a compiler-generated method
        textRange1.Delete();
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = textRange1.InsertAfter("Max : " + strArray[1] + "           Min : " + strArray[0]);
        textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange3 = textRange2.InsertAfter(Environment.NewLine + Environment.NewLine + "Z 방향 변형 : " + num.ToString() + " [mm]");
        textRange3.Font.Color.RGB = ColorTranslator.ToOle(Color.Blue);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsKumnung]SetSlide15):" + ex.Message));
      }
    }
  }
}
