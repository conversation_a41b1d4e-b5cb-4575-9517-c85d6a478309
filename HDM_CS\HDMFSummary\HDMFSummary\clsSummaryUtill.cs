﻿// Decompiled with JetBrains decompiler
// Type: HDMFSummary.clsSummaryUtill
// Assembly: HDMFSummary, Version=2.3.0.0, Culture=neutral, PublicKeyToken=null
// MVID: ********-3B9F-45E9-9723-0DEF72C057B2
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFSummary.dll

using HDLog4Net;
using Microsoft.CSharp.RuntimeBinder;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Expressions;
using System.Runtime.CompilerServices;

namespace HDMFSummary
{
  public class clsSummaryUtill
  {
    public static string GetExcelCellValue(Microsoft.Office.Interop.Excel.Range p_rgCell)
    {
      string excelCellValue = "";
      if (p_rgCell != null)
      {
        try
        {
          // ISSUE: reference to a compiler-generated field
          if (clsSummaryUtill.\u003C\u003Eo__0.\u003C\u003Ep__1 == null)
          {
            // ISSUE: reference to a compiler-generated field
            clsSummaryUtill.\u003C\u003Eo__0.\u003C\u003Ep__1 = CallSite<Func<CallSite, object, bool>>.Create(Binder.UnaryOperation(CSharpBinderFlags.None, ExpressionType.IsTrue, typeof (clsSummaryUtill), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[1]
            {
              CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
            }));
          }
          // ISSUE: reference to a compiler-generated field
          Func<CallSite, object, bool> target1 = clsSummaryUtill.\u003C\u003Eo__0.\u003C\u003Ep__1.Target;
          // ISSUE: reference to a compiler-generated field
          CallSite<Func<CallSite, object, bool>> p1 = clsSummaryUtill.\u003C\u003Eo__0.\u003C\u003Ep__1;
          // ISSUE: reference to a compiler-generated field
          if (clsSummaryUtill.\u003C\u003Eo__0.\u003C\u003Ep__0 == null)
          {
            // ISSUE: reference to a compiler-generated field
            clsSummaryUtill.\u003C\u003Eo__0.\u003C\u003Ep__0 = CallSite<Func<CallSite, object, object, object>>.Create(Binder.BinaryOperation(CSharpBinderFlags.None, ExpressionType.NotEqual, typeof (clsSummaryUtill), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
            {
              CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
              CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.Constant, (string) null)
            }));
          }
          // ISSUE: reference to a compiler-generated field
          // ISSUE: reference to a compiler-generated field
          object obj1 = clsSummaryUtill.\u003C\u003Eo__0.\u003C\u003Ep__0.Target((CallSite) clsSummaryUtill.\u003C\u003Eo__0.\u003C\u003Ep__0, p_rgCell.Text, (object) null);
          if (target1((CallSite) p1, obj1))
          {
            // ISSUE: reference to a compiler-generated field
            if (clsSummaryUtill.\u003C\u003Eo__0.\u003C\u003Ep__3 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsSummaryUtill.\u003C\u003Eo__0.\u003C\u003Ep__3 = CallSite<Func<CallSite, object, string>>.Create(Binder.Convert(CSharpBinderFlags.None, typeof (string), typeof (clsSummaryUtill)));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, object, string> target2 = clsSummaryUtill.\u003C\u003Eo__0.\u003C\u003Ep__3.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, object, string>> p3 = clsSummaryUtill.\u003C\u003Eo__0.\u003C\u003Ep__3;
            // ISSUE: reference to a compiler-generated field
            if (clsSummaryUtill.\u003C\u003Eo__0.\u003C\u003Ep__2 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsSummaryUtill.\u003C\u003Eo__0.\u003C\u003Ep__2 = CallSite<Func<CallSite, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "ToString", (IEnumerable<Type>) null, typeof (clsSummaryUtill), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[1]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            object obj2 = clsSummaryUtill.\u003C\u003Eo__0.\u003C\u003Ep__2.Target((CallSite) clsSummaryUtill.\u003C\u003Eo__0.\u003C\u003Ep__2, p_rgCell.Text);
            excelCellValue = target2((CallSite) p3, obj2);
          }
        }
        catch (Exception ex)
        {
          HDLog.Instance().Error((object) ("Exception([HDMFSummary][clsSummaryUtill]GetExcelCellValue):" + ex.Message));
        }
      }
      return excelCellValue;
    }

    public static string GetExcelCellName(Microsoft.Office.Interop.Excel.Range p_rgCell, Dictionary<string, string> p_dic_Names)
    {
      string empty = string.Empty;
      if (p_rgCell != null)
      {
        try
        {
          Dictionary<string, string>.KeyCollection keys = p_dic_Names.Keys;
          int column = p_rgCell.Column;
          string str = column.ToString();
          if (keys.Contains<string>(str))
          {
            Dictionary<string, string> dictionary = p_dic_Names;
            column = p_rgCell.Column;
            string key = column.ToString();
            empty = dictionary[key];
          }
        }
        catch (Exception ex)
        {
          HDLog.Instance().Error((object) ("Exception([HDMFSummary][clsSummaryUtill]GetExcelCellNamed):" + ex.Message));
        }
      }
      return empty;
    }

    public static int GetColumnIndex(Worksheet p_wSheet, string p_strNamedRange)
    {
      int columnIndex = -1;
      try
      {
        // ISSUE: reference to a compiler-generated method
        columnIndex = p_wSheet.get_Range((object) p_strNamedRange, Type.Missing).Column;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFSummary][clsSummaryUtill]GetColumnIndex):" + ex.Message));
      }
      return columnIndex;
    }

    public static DataTable GetWorkSheetData(Worksheet p_wSheet)
    {
      int RowIndex = 3;
      DataTable workSheetData = (DataTable) null;
      Dictionary<string, string> p_dic_Names = new Dictionary<string, string>();
      if (p_wSheet != null)
      {
        try
        {
          workSheetData = new DataTable();
          workSheetData.TableName = p_wSheet.Name;
          List<string> stringList = new List<string>();
          foreach (Name name in p_wSheet.Names)
          {
            string key = ((IEnumerable<string>) (name.RefersToR1C1Local as string).Split('C')).Last<string>();
            string str = ((IEnumerable<string>) name.Name.Split('!')).Last<string>();
            p_dic_Names.Add(key, str);
          }
          for (int index = 0; index < p_wSheet.Columns.Count; ++index)
          {
            // ISSUE: reference to a compiler-generated field
            if (clsSummaryUtill.\u003C\u003Eo__3.\u003C\u003Ep__0 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsSummaryUtill.\u003C\u003Eo__3.\u003C\u003Ep__0 = CallSite<Func<CallSite, object, Microsoft.Office.Interop.Excel.Range>>.Create(Binder.Convert(CSharpBinderFlags.ConvertExplicit, typeof (Microsoft.Office.Interop.Excel.Range), typeof (clsSummaryUtill)));
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            // ISSUE: variable of a compiler-generated type
            Microsoft.Office.Interop.Excel.Range p_rgCell = clsSummaryUtill.\u003C\u003Eo__3.\u003C\u003Ep__0.Target((CallSite) clsSummaryUtill.\u003C\u003Eo__3.\u003C\u003Ep__0, p_wSheet.Cells[(object) 2, (object) (index + 1)]);
            if (!(clsSummaryUtill.GetExcelCellValue(p_rgCell) == ""))
            {
              string excelCellName = clsSummaryUtill.GetExcelCellName(p_rgCell, p_dic_Names);
              if (excelCellName != string.Empty)
                workSheetData.Columns.Add(excelCellName);
            }
            else
              break;
          }
          while (true)
          {
            // ISSUE: reference to a compiler-generated field
            if (clsSummaryUtill.\u003C\u003Eo__3.\u003C\u003Ep__1 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsSummaryUtill.\u003C\u003Eo__3.\u003C\u003Ep__1 = CallSite<Func<CallSite, object, Microsoft.Office.Interop.Excel.Range>>.Create(Binder.Convert(CSharpBinderFlags.ConvertExplicit, typeof (Microsoft.Office.Interop.Excel.Range), typeof (clsSummaryUtill)));
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            if (!(clsSummaryUtill.GetExcelCellValue(clsSummaryUtill.\u003C\u003Eo__3.\u003C\u003Ep__1.Target((CallSite) clsSummaryUtill.\u003C\u003Eo__3.\u003C\u003Ep__1, p_wSheet.Cells[(object) RowIndex, (object) 1])) == ""))
            {
              DataRow dataRow1 = workSheetData.Rows.Add();
              for (int index = 0; index < workSheetData.Columns.Count; ++index)
              {
                int columnIndex1 = clsSummaryUtill.GetColumnIndex(p_wSheet, workSheetData.Columns[index].ToString());
                DataRow dataRow2 = dataRow1;
                int columnIndex2 = index;
                // ISSUE: reference to a compiler-generated field
                if (clsSummaryUtill.\u003C\u003Eo__3.\u003C\u003Ep__2 == null)
                {
                  // ISSUE: reference to a compiler-generated field
                  clsSummaryUtill.\u003C\u003Eo__3.\u003C\u003Ep__2 = CallSite<Func<CallSite, object, Microsoft.Office.Interop.Excel.Range>>.Create(Binder.Convert(CSharpBinderFlags.ConvertExplicit, typeof (Microsoft.Office.Interop.Excel.Range), typeof (clsSummaryUtill)));
                }
                // ISSUE: reference to a compiler-generated field
                // ISSUE: reference to a compiler-generated field
                string excelCellValue = clsSummaryUtill.GetExcelCellValue(clsSummaryUtill.\u003C\u003Eo__3.\u003C\u003Ep__2.Target((CallSite) clsSummaryUtill.\u003C\u003Eo__3.\u003C\u003Ep__2, p_wSheet.Cells[(object) RowIndex, (object) columnIndex1]));
                dataRow2[columnIndex2] = (object) excelCellValue;
              }
              ++RowIndex;
            }
            else
              break;
          }
        }
        catch (Exception ex)
        {
          HDLog.Instance().Error((object) ("Exception([HDMFSummary][clsSummaryUtill]GetWorkSheetData):" + ex.Message));
        }
      }
      return workSheetData;
    }

    public static double ConvertToDouble(string p_strValue)
    {
      double result = 0.0;
      double.TryParse(p_strValue, out result);
      return result;
    }

    public static int ConvertToInt(string p_strValue)
    {
      int result = 0;
      int.TryParse(p_strValue, out result);
      return result;
    }
  }
}
