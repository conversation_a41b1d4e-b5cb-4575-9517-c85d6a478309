﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmInputDB
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDLog4Net;
using HDMFUserControl;
using HDMoldFlow.Properties;
using HDMoldFlowLibrary;
using Microsoft.WindowsAPICodePack.Dialogs;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using System.Xml;

namespace HDMoldFlow
{
  public class frmInputDB : Form
  {
    private const int WS_EX_COMPOSITED = 33554432;
    private IContainer components = (IContainer) null;
    private ListBox listBox_DB;
    private Label label_DB_List;
    private NewButton newButton_Add;
    private NewButton newButton_Edit;
    private NewButton newButton_Del;
    private NewButton newButton_Import;
    private NewButton newButton_Export;
    private Label label_Data;
    private Label label_Company;
    private Label label_TradeName;
    private Label label_Density;
    private Label label_Material;
    private NewTextBox newTextBox_Density;
    private Label label_Result;
    private Label label_InjPressure;
    private Label label_CycleTime;
    private Label label_Deflection;
    private Label label_Weight;
    private Label label_TempAtFlowFront;
    private Label label_Sinkmark;
    private Label label_VolShrinkage;
    private NewTextBox newTextBox_InjPressure_Priority;
    private NewTextBox newTextBox_Sinkmark_Priority;
    private NewTextBox newTextBox_CycleTime_Priority;
    private NewTextBox newTextBox_VolShrinkage_Priority;
    private NewTextBox newTextBox_Deflection_Priority;
    private NewTextBox newTextBox_TempAtFlowFront_Priority;
    private NewTextBox newTextBox_Weight_Priority;
    private NewTextBox newTextBox_Option;
    private NewTextBox newTextBox_Item;
    private NewTextBox newTextBox_Company;
    private Label label_DB_Opt;
    private Label label_DB_Item;
    private Label label_DB_Company;
    private NewTextBox newTextBox_GateNum_Min;
    private NewTextBox newTextBox_GateNum_Max;
    private Label label_GateNum;
    private NewTextBox newTextBox_MoldTemp_Min;
    private NewTextBox newTextBox_MoldTemp_Max;
    private Label label_MoldTemp;
    private NewTextBox newTextBox_MeltTemp_Min;
    private NewTextBox newTextBox_MeltTemp_Max;
    private NewTextBox newTextBox_CoolingInletTemp_Min;
    private NewTextBox newTextBox_PackingNum_Min;
    private NewTextBox newTextBox_CoolingTime_Min;
    private NewTextBox newTextBox_VPSwitchOver_Min;
    private NewTextBox newTextBox_FillTime_Min;
    private NewTextBox newTextBox_CoolingInletTemp_Max;
    private Label label_CoolingInletTemp;
    private Label label_CoolingTime;
    private Label label_FillTime;
    private Label label_MeltTemp;
    private NewTextBox newTextBox_PackingNum_Max;
    private NewTextBox newTextBox_CoolingTime_Max;
    private NewTextBox newTextBox_VPSwitchOver_Max;
    private NewTextBox newTextBox_FillTime_Max;
    private Label label_PackingNum;
    private Label label_VPSwitchOver;
    private NewTextBox newTextBox_Type;
    private Label label_Type;
    private NewTextBox newTextBox_ClampF_Priority;
    private Label label_ClampF;
    private Label label_AnalysisType;
    private NewComboBox newComboBox_AnalysisType;
    private NewButton newButton_LoadMaterial;
    private NewButton newButton_System;
    private NewComboBox newComboBox_Company;
    private NewComboBox newComboBox_TradeName;
    private NewTextBox newTextBox_InjectionPressureLimit;
    private NewTextBox newTextBox_ClampForceLimit;
    private NewTextBox newTextBox_DeflectionLimit;
    private Label label_InjectionPressureLimit;
    private NewComboBox newComboBox_LoopNum;
    private NewComboBox newComboBox_ValveOption;
    private Label label_Option;
    private Label label_ValveOption;
    private Label label_ClampForceLimit;
    private Label label_DeflectionLimit;
    private Label label_LoopNum;

    protected override CreateParams CreateParams
    {
      get
      {
        CreateParams createParams = base.CreateParams;
        createParams.ExStyle |= 33554432;
        return createParams;
      }
    }

    public frmInputDB()
    {
      this.InitializeComponent();
      this.newButton_Add.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Add.Image);
      this.newButton_Edit.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Edit.Image);
      this.newButton_Del.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Del.Image);
      this.newButton_Import.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Import.Image);
      this.newButton_Export.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Export.Image);
      this.label_DB_List.Text = "DB " + LocaleControl.getInstance().GetString("IDS_LIST");
      this.label_DB_Company.Text = LocaleControl.getInstance().GetString("IDS_COMPANY_NAME");
      this.label_DB_Item.Text = LocaleControl.getInstance().GetString("IDS_ITEM_NAME");
      this.label_DB_Opt.Text = LocaleControl.getInstance().GetString("IDS_OPTION");
      this.newButton_Add.ButtonText = LocaleControl.getInstance().GetString("IDS_ADD");
      this.newButton_Edit.ButtonText = LocaleControl.getInstance().GetString("IDS_EDIT");
      this.newButton_Del.ButtonText = LocaleControl.getInstance().GetString("IDS_DELETE");
      this.newButton_Import.ButtonText = LocaleControl.getInstance().GetString("IDS_IMPORT");
      this.newButton_Export.ButtonText = LocaleControl.getInstance().GetString("IDS_EXPORT");
      this.label_Result.Text = LocaleControl.getInstance().GetString("IDS_RESULT_PRIORITY");
      this.label_Option.Text = LocaleControl.getInstance().GetString("IDS_OPTION");
      this.label_LoopNum.Text = LocaleControl.getInstance().GetString("IDS_LOOPNUM");
      this.label_DeflectionLimit.Text = LocaleControl.getInstance().GetString("IDS_DEFLECTION_LIMIT");
      this.label_ClampForceLimit.Text = LocaleControl.getInstance().GetString("IDS_CLAMP_FORCE_LIMIT");
      this.label_InjectionPressureLimit.Text = LocaleControl.getInstance().GetString("IDS_INJECTION_PRESSURE_LIMIT");
    }

    private void frmInputDB_Load(object sender, EventArgs e)
    {
      this.ActiveControl = (Control) this.label_DB_List;
      this.newComboBox_AnalysisType.Items.Add((object) "Flow");
      this.newComboBox_AnalysisType.Items.Add((object) "Flow_Warp");
      this.newComboBox_AnalysisType.Items.Add((object) "Cool_Flow_Warp");
      this.newComboBox_ValveOption.Items.AddRange((object[]) new string[2]
      {
        "Yes",
        "No"
      });
      this.newComboBox_LoopNum.Items.AddRange((object[]) Enumerable.Range(1, 30).Select<int, string>((System.Func<int, string>) (i => i.ToString())).ToArray<string>());
      this.RefreshDBList();
    }

    private void RefreshDBList()
    {
      this.listBox_DB.Items.Clear();
      try
      {
        DataRow[] array = clsDefine.g_dtInputDB.AsEnumerable().ToArray<DataRow>();
        if (array.Length == 0)
          return;
        List<string> stringList = new List<string>();
        foreach (DataRow dataRow in array)
          stringList.Add(dataRow["Name"].ToString());
        stringList.Sort();
        this.listBox_DB.Items.AddRange((object[]) stringList.ToArray());
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInputDB]RefreshDBList):" + ex.Message));
      }
    }

    private void newButton_NewClick(object sender, EventArgs e)
    {
      NewButton newButton = sender as NewButton;
      if (newButton == this.newButton_Add)
        this.AddDB();
      else if (newButton == this.newButton_Edit)
        this.EditDB();
      else if (newButton == this.newButton_Del)
        this.DeleteDB();
      else if (newButton == this.newButton_Import)
        this.ImportDB();
      else
        this.ExportDB();
    }

    private void AddDB()
    {
      try
      {
        if (((IEnumerable<DataRow>) clsDefine.g_dtInputDB.AsEnumerable().ToArray<DataRow>()).Any<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Company"].ToString() == this.newComboBox_Company.Value && Temp["Item"].ToString() == this.newTextBox_Item.Value && Temp["Option"].ToString() == this.newTextBox_Option.Value)))
          return;
        DataRow p_drInputDB = clsDefine.g_dtInputDB.Rows.Add();
        p_drInputDB["Name"] = (object) (this.newComboBox_Company.Value + "_" + this.newTextBox_Item.Value + "_" + this.newTextBox_Option.Value);
        p_drInputDB["Company"] = (object) this.newComboBox_Company.Value;
        p_drInputDB["Item"] = (object) this.newTextBox_Item.Value;
        p_drInputDB["Option"] = (object) this.newTextBox_Option.Value;
        this.UpdateDB(p_drInputDB);
        this.CreateDBFile(p_drInputDB);
        this.RefreshDBList();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInputDB]AddDB):" + ex.Message));
      }
    }

    private void EditDB()
    {
      if (this.listBox_DB.SelectedIndex == -1)
        return;
      try
      {
        string strInputDB = this.listBox_DB.Text;
        DataRow p_drInputDB = clsDefine.g_dtInputDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == strInputDB)).FirstOrDefault<DataRow>();
        if (p_drInputDB == null)
          return;
        this.UpdateDB(p_drInputDB);
        FileInfo fileInfo = new FileInfo(clsDefine.g_diInputDBCfg.ToString() + "\\" + p_drInputDB["Name"] + ".xml");
        if (fileInfo.Exists)
          fileInfo.Delete();
        p_drInputDB["Name"] = (object) (this.newTextBox_Company.Value + "_" + this.newTextBox_Item.Value + "_" + this.newTextBox_Option.Value);
        p_drInputDB["Company"] = (object) this.newTextBox_Company.Value;
        p_drInputDB["Item"] = (object) this.newTextBox_Item.Value;
        p_drInputDB["Option"] = (object) this.newTextBox_Option.Value;
        this.CreateDBFile(p_drInputDB);
        this.RefreshDBList();
        this.listBox_DB.SelectedIndex = this.listBox_DB.Items.IndexOf(p_drInputDB["Name"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInputDB]EditDB):" + ex.Message));
      }
    }

    private void DeleteDB()
    {
      bool flag = false;
      if (this.listBox_DB.SelectedItems.Count == 0)
        return;
      try
      {
        if (clsUtill.ShowMessageBox((Form) this, LocaleControl.getInstance().GetString("IDS_WANT_DELETE"), this.Text, MessageBoxButtons.YesNo, MessageBoxIcon.Asterisk) != DialogResult.Yes)
          return;
        foreach (string selectedItem in this.listBox_DB.SelectedItems)
        {
          string strInputDB = selectedItem;
          DataRow row = clsDefine.g_dtInputDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == strInputDB)).FirstOrDefault<DataRow>();
          if (row != null)
          {
            FileInfo fileInfo = new FileInfo(clsDefine.g_diInputDBCfg.ToString() + "\\" + row["Name"] + ".xml");
            if (fileInfo.Exists)
              fileInfo.Delete();
            clsDefine.g_dtProcessDB.Rows.Remove(row);
            flag = true;
          }
        }
        if (flag)
          this.RefreshDBList();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInputDB]DeleteDB):" + ex.Message));
      }
    }

    private void ImportDB()
    {
      string strInputDB = "";
      bool flag = false;
      try
      {
        OpenFileDialog openFileDialog = new OpenFileDialog();
        openFileDialog.Filter = "DB Files(*.xml)|*.xml";
        openFileDialog.Multiselect = true;
        if (openFileDialog.ShowDialog((IWin32Window) this) != DialogResult.OK)
          return;
        foreach (string fileName in openFileDialog.FileNames)
        {
          strInputDB = Path.GetFileNameWithoutExtension(fileName);
          if (!clsDefine.g_dtInputDB.AsEnumerable().Any<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == strInputDB)))
          {
            if (strInputDB.Split('_').Length == 3)
            {
              FileInfo fileInfo = new FileInfo(clsDefine.g_diProcessDBCfg.FullName + "\\" + strInputDB + ".xml");
              File.Copy(fileName, fileInfo.FullName, true);
              flag = true;
            }
          }
        }
        if (flag)
        {
          clsData.LoadInputDB();
          this.RefreshDBList();
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInputDB]ImportDB):" + ex.Message));
      }
    }

    private void ExportDB()
    {
      if (this.listBox_DB.SelectedItems.Count == 0)
        return;
      try
      {
        CommonOpenFileDialog commonOpenFileDialog = new CommonOpenFileDialog()
        {
          IsFolderPicker = true
        };
        if (commonOpenFileDialog.ShowDialog() != CommonFileDialogResult.Ok)
          return;
        foreach (string selectedItem in this.listBox_DB.SelectedItems)
        {
          string strInputDB = selectedItem;
          DataRow dataRow = clsDefine.g_dtProcessDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == strInputDB)).FirstOrDefault<DataRow>();
          if (dataRow != null)
          {
            FileInfo fileInfo = new FileInfo(clsDefine.g_diInputDBCfg.FullName + "\\" + dataRow["Name"].ToString() + ".xml");
            if (fileInfo.Exists)
              fileInfo.CopyTo(commonOpenFileDialog.FileName + "\\" + fileInfo.Name, true);
          }
        }
        Process.Start(commonOpenFileDialog.FileName);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInputDB]ExportDB):" + ex.Message));
      }
    }

    private void UpdateDB(DataRow p_drInputDB)
    {
      string empty = string.Empty;
      try
      {
        p_drInputDB["Data_FillTime"] = (object) (this.newTextBox_FillTime_Min.Value + "," + this.newTextBox_FillTime_Max.Value);
        p_drInputDB["Data_VPSwitchOver"] = (object) (this.newTextBox_VPSwitchOver_Min.Value + "," + this.newTextBox_VPSwitchOver_Max.Value);
        p_drInputDB["Data_PackingNumber"] = (object) (this.newTextBox_PackingNum_Min.Value + "," + this.newTextBox_PackingNum_Max.Value);
        p_drInputDB["Data_CoolingTime"] = (object) (this.newTextBox_CoolingTime_Min.Value + "," + this.newTextBox_CoolingTime_Max.Value);
        p_drInputDB["Data_CoolingInletTemperature"] = (object) (this.newTextBox_CoolingInletTemp_Min.Value + "," + this.newTextBox_CoolingInletTemp_Max.Value);
        p_drInputDB["Data_MeltTemperature"] = (object) (this.newTextBox_MeltTemp_Min.Value + "," + this.newTextBox_MeltTemp_Max.Value);
        p_drInputDB["Data_MoldTemperature"] = (object) (this.newTextBox_MoldTemp_Min.Value + "," + this.newTextBox_MoldTemp_Max.Value);
        p_drInputDB["Data_GateNumber"] = (object) (this.newTextBox_GateNum_Min.Value + "," + this.newTextBox_GateNum_Max.Value);
        string[] strArray = this.newComboBox_TradeName.Value.Split('[');
        string str1 = strArray[0].TrimEnd();
        string str2 = strArray[1].Replace("]", "").Replace("ID:", "");
        p_drInputDB["Mat_Company"] = (object) this.newComboBox_Company.Value;
        p_drInputDB["Mat_TradeName"] = (object) str1;
        p_drInputDB["Mat_MaterialID"] = (object) str2;
        p_drInputDB["Mat_Type"] = (object) this.newTextBox_Type.Value;
        p_drInputDB["Mat_Density"] = (object) this.newTextBox_Density.Value;
        p_drInputDB["Result_DeflectionAll"] = (object) this.newTextBox_Deflection_Priority.Value;
        p_drInputDB["Result_VolumetricShrinkage"] = (object) this.newTextBox_VolShrinkage_Priority.Value;
        p_drInputDB["Result_Sinkmark"] = (object) this.newTextBox_Sinkmark_Priority.Value;
        p_drInputDB["Result_TemperatureAtFlowFront"] = (object) this.newTextBox_TempAtFlowFront_Priority.Value;
        p_drInputDB["Result_CycleTime"] = (object) this.newTextBox_CycleTime_Priority.Value;
        p_drInputDB["Result_InjectionPressure"] = (object) this.newTextBox_InjPressure_Priority.Value;
        p_drInputDB["Result_Weight"] = (object) this.newTextBox_Weight_Priority.Value;
        p_drInputDB["Result_ClampForce"] = (object) this.newTextBox_ClampF_Priority.Value;
        p_drInputDB["Option_ValveOption"] = (object) this.newComboBox_ValveOption.Value;
        p_drInputDB["Option_LoopNum"] = (object) this.newComboBox_LoopNum.Value;
        p_drInputDB["Option_DeflectionLimit"] = (object) this.newTextBox_DeflectionLimit.Value;
        p_drInputDB["Option_ClampForceLimit"] = (object) this.newTextBox_ClampForceLimit.Value;
        p_drInputDB["Option_InjectionPressureLimit"] = (object) this.newTextBox_InjectionPressureLimit.Value;
        p_drInputDB["Analysis_Type"] = (object) this.newComboBox_AnalysisType.Value;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInputDB]UpdateDB):" + ex.Message));
      }
    }

    private void CreateDBFile(DataRow p_drInputDB)
    {
      try
      {
        FileInfo fileInfo = new FileInfo(clsDefine.g_diInputDBCfg.ToString() + "\\" + p_drInputDB["Name"].ToString() + ".xml");
        if (!fileInfo.Directory.Exists)
          fileInfo.Directory.Create();
        using (XmlWriter xmlWriter = XmlWriter.Create(fileInfo.FullName, new XmlWriterSettings()
        {
          Indent = true,
          OmitXmlDeclaration = true,
          NewLineOnAttributes = true
        }))
        {
          xmlWriter.WriteStartDocument();
          xmlWriter.WriteStartElement("DB");
          xmlWriter.WriteEndElement();
          xmlWriter.Flush();
        }
        using (XmlReader reader = XmlReader.Create(fileInfo.FullName))
        {
          XmlDocument xmlDocument = new XmlDocument();
          xmlDocument.Load(reader);
          XmlElement documentElement = xmlDocument.DocumentElement;
          documentElement.RemoveAll();
          XmlNode element1 = (XmlNode) xmlDocument.CreateElement("Data");
          XmlNode element2 = (XmlNode) xmlDocument.CreateElement("FillTime");
          string[] strArray1 = p_drInputDB["Data_FillTime"].ToString().Split(',');
          XmlAttribute attribute1 = xmlDocument.CreateAttribute("Min");
          attribute1.Value = strArray1[0];
          element2.Attributes.Append(attribute1);
          XmlAttribute attribute2 = xmlDocument.CreateAttribute("Max");
          attribute2.Value = strArray1[1];
          element2.Attributes.Append(attribute2);
          element1.AppendChild(element2);
          XmlNode element3 = (XmlNode) xmlDocument.CreateElement("VPSwitchOver");
          string[] strArray2 = p_drInputDB["Data_VPSwitchOver"].ToString().Split(',');
          XmlAttribute attribute3 = xmlDocument.CreateAttribute("Min");
          attribute3.Value = strArray2[0];
          element3.Attributes.Append(attribute3);
          XmlAttribute attribute4 = xmlDocument.CreateAttribute("Max");
          attribute4.Value = strArray2[1];
          element3.Attributes.Append(attribute4);
          element1.AppendChild(element3);
          XmlNode element4 = (XmlNode) xmlDocument.CreateElement("PackingNumber");
          string[] strArray3 = p_drInputDB["Data_PackingNumber"].ToString().Split(',');
          XmlAttribute attribute5 = xmlDocument.CreateAttribute("Min");
          attribute5.Value = strArray3[0];
          element4.Attributes.Append(attribute5);
          XmlAttribute attribute6 = xmlDocument.CreateAttribute("Max");
          attribute6.Value = strArray3[1];
          element4.Attributes.Append(attribute6);
          element1.AppendChild(element4);
          XmlNode element5 = (XmlNode) xmlDocument.CreateElement("CoolingTime");
          string[] strArray4 = p_drInputDB["Data_CoolingTime"].ToString().Split(',');
          XmlAttribute attribute7 = xmlDocument.CreateAttribute("Min");
          attribute7.Value = strArray4[0];
          element5.Attributes.Append(attribute7);
          XmlAttribute attribute8 = xmlDocument.CreateAttribute("Max");
          attribute8.Value = strArray4[1];
          element5.Attributes.Append(attribute8);
          element1.AppendChild(element5);
          XmlNode element6 = (XmlNode) xmlDocument.CreateElement("CoolingInletTemp");
          string[] strArray5 = p_drInputDB["Data_CoolingInletTemperature"].ToString().Split(',');
          XmlAttribute attribute9 = xmlDocument.CreateAttribute("Min");
          attribute9.Value = strArray5[0];
          element6.Attributes.Append(attribute9);
          XmlAttribute attribute10 = xmlDocument.CreateAttribute("Max");
          attribute10.Value = strArray5[1];
          element6.Attributes.Append(attribute10);
          element1.AppendChild(element6);
          XmlNode element7 = (XmlNode) xmlDocument.CreateElement("MeltTemperature");
          string[] strArray6 = p_drInputDB["Data_MeltTemperature"].ToString().Split(',');
          XmlAttribute attribute11 = xmlDocument.CreateAttribute("Min");
          attribute11.Value = strArray6[0];
          element7.Attributes.Append(attribute11);
          XmlAttribute attribute12 = xmlDocument.CreateAttribute("Max");
          attribute12.Value = strArray6[1];
          element7.Attributes.Append(attribute12);
          element1.AppendChild(element7);
          XmlNode element8 = (XmlNode) xmlDocument.CreateElement("MoldTemperature");
          string[] strArray7 = p_drInputDB["Data_MoldTemperature"].ToString().Split(',');
          XmlAttribute attribute13 = xmlDocument.CreateAttribute("Min");
          attribute13.Value = strArray7[0];
          element8.Attributes.Append(attribute13);
          XmlAttribute attribute14 = xmlDocument.CreateAttribute("Max");
          attribute14.Value = strArray7[1];
          element8.Attributes.Append(attribute14);
          element1.AppendChild(element8);
          XmlNode element9 = (XmlNode) xmlDocument.CreateElement("GateNumber");
          string[] strArray8 = p_drInputDB["Data_GateNumber"].ToString().Split(',');
          XmlAttribute attribute15 = xmlDocument.CreateAttribute("Min");
          attribute15.Value = strArray8[0];
          element9.Attributes.Append(attribute15);
          XmlAttribute attribute16 = xmlDocument.CreateAttribute("Max");
          attribute16.Value = strArray8[1];
          element9.Attributes.Append(attribute16);
          element1.AppendChild(element9);
          documentElement.AppendChild(element1);
          XmlNode element10 = (XmlNode) xmlDocument.CreateElement("Material");
          XmlNode element11 = (XmlNode) xmlDocument.CreateElement("Company");
          element11.InnerText = p_drInputDB["Mat_Company"].ToString();
          element10.AppendChild(element11);
          XmlNode element12 = (XmlNode) xmlDocument.CreateElement("TradeName");
          element12.InnerText = p_drInputDB["Mat_TradeName"].ToString();
          element10.AppendChild(element12);
          XmlNode element13 = (XmlNode) xmlDocument.CreateElement("MaterialID");
          element13.InnerText = p_drInputDB["Mat_MaterialID"].ToString();
          element10.AppendChild(element13);
          XmlNode element14 = (XmlNode) xmlDocument.CreateElement("Type");
          element14.InnerText = p_drInputDB["Mat_Type"].ToString();
          element10.AppendChild(element14);
          XmlNode element15 = (XmlNode) xmlDocument.CreateElement("Density");
          element15.InnerText = p_drInputDB["Mat_Density"].ToString();
          element10.AppendChild(element15);
          documentElement.AppendChild(element10);
          XmlNode element16 = (XmlNode) xmlDocument.CreateElement("Result");
          XmlNode element17 = (XmlNode) xmlDocument.CreateElement("DeflectionAll");
          XmlAttribute attribute17 = xmlDocument.CreateAttribute("Priority");
          attribute17.Value = p_drInputDB["Result_DeflectionAll"].ToString();
          element17.Attributes.Append(attribute17);
          element16.AppendChild(element17);
          XmlNode element18 = (XmlNode) xmlDocument.CreateElement("VolumetricShrinkage");
          XmlAttribute attribute18 = xmlDocument.CreateAttribute("Priority");
          attribute18.Value = p_drInputDB["Result_VolumetricShrinkage"].ToString();
          element18.Attributes.Append(attribute18);
          element16.AppendChild(element18);
          XmlNode element19 = (XmlNode) xmlDocument.CreateElement("Sinkmark");
          XmlAttribute attribute19 = xmlDocument.CreateAttribute("Priority");
          attribute19.Value = p_drInputDB["Result_Sinkmark"].ToString();
          element19.Attributes.Append(attribute19);
          element16.AppendChild(element19);
          XmlNode element20 = (XmlNode) xmlDocument.CreateElement("TemperatureAtFlowFront");
          XmlAttribute attribute20 = xmlDocument.CreateAttribute("Priority");
          attribute20.Value = p_drInputDB["Result_TemperatureAtFlowFront"].ToString();
          element20.Attributes.Append(attribute20);
          element16.AppendChild(element20);
          XmlNode element21 = (XmlNode) xmlDocument.CreateElement("CycleTime");
          XmlAttribute attribute21 = xmlDocument.CreateAttribute("Priority");
          attribute21.Value = p_drInputDB["Result_CycleTime"].ToString();
          element21.Attributes.Append(attribute21);
          element16.AppendChild(element21);
          XmlNode element22 = (XmlNode) xmlDocument.CreateElement("InjectionPressure");
          XmlAttribute attribute22 = xmlDocument.CreateAttribute("Priority");
          attribute22.Value = p_drInputDB["Result_InjectionPressure"].ToString();
          element22.Attributes.Append(attribute22);
          element16.AppendChild(element22);
          XmlNode element23 = (XmlNode) xmlDocument.CreateElement("Weight");
          XmlAttribute attribute23 = xmlDocument.CreateAttribute("Priority");
          attribute23.Value = p_drInputDB["Result_Weight"].ToString();
          element23.Attributes.Append(attribute23);
          element16.AppendChild(element23);
          XmlNode element24 = (XmlNode) xmlDocument.CreateElement("ClampForce");
          XmlAttribute attribute24 = xmlDocument.CreateAttribute("Priority");
          attribute24.Value = p_drInputDB["Result_ClampForce"].ToString();
          element24.Attributes.Append(attribute24);
          element16.AppendChild(element24);
          documentElement.AppendChild(element16);
          XmlNode element25 = (XmlNode) xmlDocument.CreateElement("Option");
          XmlNode element26 = (XmlNode) xmlDocument.CreateElement("ValveOption");
          XmlAttribute attribute25 = xmlDocument.CreateAttribute("Value");
          attribute25.Value = p_drInputDB["Option_ValveOption"].ToString();
          element26.Attributes.Append(attribute25);
          element25.AppendChild(element26);
          XmlNode element27 = (XmlNode) xmlDocument.CreateElement("LoopNum");
          XmlAttribute attribute26 = xmlDocument.CreateAttribute("Value");
          attribute26.Value = p_drInputDB["Option_LoopNum"].ToString();
          element27.Attributes.Append(attribute26);
          element25.AppendChild(element27);
          XmlNode element28 = (XmlNode) xmlDocument.CreateElement("DeflectionLimit");
          XmlAttribute attribute27 = xmlDocument.CreateAttribute("Value");
          attribute27.Value = p_drInputDB["Option_DeflectionLimit"].ToString();
          element28.Attributes.Append(attribute27);
          element25.AppendChild(element28);
          XmlNode element29 = (XmlNode) xmlDocument.CreateElement("ClampForceLimit");
          XmlAttribute attribute28 = xmlDocument.CreateAttribute("Value");
          attribute28.Value = p_drInputDB["Option_ClampForceLimit"].ToString();
          element29.Attributes.Append(attribute28);
          element25.AppendChild(element29);
          XmlNode element30 = (XmlNode) xmlDocument.CreateElement("InjectionPressureLimit");
          XmlAttribute attribute29 = xmlDocument.CreateAttribute("Value");
          attribute29.Value = p_drInputDB["Option_InjectionPressureLimit"].ToString();
          element30.Attributes.Append(attribute29);
          element25.AppendChild(element30);
          documentElement.AppendChild(element25);
          XmlNode element31 = (XmlNode) xmlDocument.CreateElement("Default");
          XmlNode element32 = (XmlNode) xmlDocument.CreateElement("AnalysisType");
          XmlAttribute attribute30 = xmlDocument.CreateAttribute("Value");
          attribute30.Value = p_drInputDB["Analysis_Type"].ToString();
          element32.Attributes.Append(attribute30);
          element31.AppendChild(element32);
          documentElement.AppendChild(element31);
          reader.Close();
          xmlDocument.Save(fileInfo.FullName);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInputDB]CreateDBFile):" + ex.Message));
      }
    }

    private void frmInputDB_KeyDown(object sender, KeyEventArgs e)
    {
      if (e.KeyCode != Keys.Escape)
        return;
      this.Close();
    }

    private void listBox_DB_SelectedIndexChanged(object sender, EventArgs e)
    {
      string empty = string.Empty;
      try
      {
        DataRow dataRow = clsDefine.g_dtInputDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == this.listBox_DB.Text)).FirstOrDefault<DataRow>();
        if (dataRow == null)
          return;
        this.newTextBox_Company.Value = dataRow["Company"].ToString();
        this.newTextBox_Item.Value = dataRow["Item"].ToString();
        this.newTextBox_Option.Value = dataRow["Option"].ToString();
        string[] strArray1 = dataRow["Data_FillTime"].ToString().Split(',');
        this.newTextBox_FillTime_Min.Value = strArray1[0];
        this.newTextBox_FillTime_Max.Value = strArray1[1];
        string[] strArray2 = dataRow["Data_VPSwitchOver"].ToString().Split(',');
        this.newTextBox_VPSwitchOver_Min.Value = strArray2[0];
        this.newTextBox_VPSwitchOver_Max.Value = strArray2[1];
        string[] strArray3 = dataRow["Data_PackingNumber"].ToString().Split(',');
        this.newTextBox_PackingNum_Min.Value = strArray3[0];
        this.newTextBox_PackingNum_Max.Value = strArray3[1];
        string[] strArray4 = dataRow["Data_CoolingTime"].ToString().Split(',');
        this.newTextBox_CoolingTime_Min.Value = strArray4[0];
        this.newTextBox_CoolingTime_Max.Value = strArray4[1];
        string[] strArray5 = dataRow["Data_CoolingInletTemperature"].ToString().Split(',');
        this.newTextBox_CoolingInletTemp_Min.Value = strArray5[0];
        this.newTextBox_CoolingInletTemp_Max.Value = strArray5[1];
        string[] strArray6 = dataRow["Data_MeltTemperature"].ToString().Split(',');
        this.newTextBox_MeltTemp_Min.Value = strArray6[0];
        this.newTextBox_MeltTemp_Max.Value = strArray6[1];
        string[] strArray7 = dataRow["Data_MoldTemperature"].ToString().Split(',');
        this.newTextBox_MoldTemp_Min.Value = strArray7[0];
        this.newTextBox_MoldTemp_Max.Value = strArray7[1];
        string[] strArray8 = dataRow["Data_GateNumber"].ToString().Split(',');
        this.newTextBox_GateNum_Min.Value = strArray8[0];
        this.newTextBox_GateNum_Max.Value = strArray8[1];
        this.RefreshMaterial();
        this.newComboBox_Company.SelectedIndex = this.newComboBox_Company.Items.IndexOf((object) dataRow["Mat_Company"].ToString());
        this.newComboBox_TradeName.SelectedIndex = this.newComboBox_TradeName.Items.IndexOf((object) (dataRow["Mat_TradeName"].ToString() + " [ID:" + dataRow["Mat_MaterialID"].ToString() + "]"));
        this.newTextBox_Type.Value = dataRow["Mat_Type"].ToString();
        this.newTextBox_Density.Value = dataRow["Mat_Density"].ToString();
        this.newTextBox_Deflection_Priority.Value = dataRow["Result_DeflectionAll"].ToString();
        this.newTextBox_VolShrinkage_Priority.Value = dataRow["Result_VolumetricShrinkage"].ToString();
        this.newTextBox_Sinkmark_Priority.Value = dataRow["Result_Sinkmark"].ToString();
        this.newTextBox_TempAtFlowFront_Priority.Value = dataRow["Result_TemperatureAtFlowFront"].ToString();
        this.newTextBox_CycleTime_Priority.Value = dataRow["Result_CycleTime"].ToString();
        this.newTextBox_InjPressure_Priority.Value = dataRow["Result_InjectionPressure"].ToString();
        this.newTextBox_Weight_Priority.Value = dataRow["Result_Weight"].ToString();
        this.newTextBox_ClampF_Priority.Value = dataRow["Result_ClampForce"].ToString();
        this.newComboBox_ValveOption.SelectedIndex = this.newComboBox_ValveOption.Items.IndexOf((object) dataRow["Option_ValveOption"].ToString());
        this.newComboBox_LoopNum.SelectedIndex = this.newComboBox_LoopNum.Items.IndexOf((object) dataRow["Option_LoopNum"].ToString());
        this.newTextBox_DeflectionLimit.Value = dataRow["Option_DeflectionLimit"].ToString();
        this.newTextBox_ClampForceLimit.Value = dataRow["Option_ClampForceLimit"].ToString();
        this.newTextBox_InjectionPressureLimit.Value = dataRow["Option_InjectionPressureLimit"].ToString();
        this.newComboBox_AnalysisType.SelectedIndex = this.newComboBox_AnalysisType.Items.IndexOf((object) dataRow["Analysis_Type"].ToString());
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInputDB]listBox_DB_SelectedIndexChanged):" + ex.Message));
      }
    }

    private void newButton_System_NewClick(object sender, EventArgs e)
    {
      NewButton newButton = sender as NewButton;
      if (newButton == this.newButton_System)
      {
        this.RefreshMaterial();
      }
      else
      {
        if (newButton != this.newButton_LoadMaterial)
          return;
        this.GetMaterialFromMoldflow();
      }
    }

    private void RefreshMaterial()
    {
      this.newComboBox_Company.Items.Clear();
      this.newComboBox_Company.Enabled = false;
      this.newComboBox_TradeName.Items.Clear();
      this.newComboBox_TradeName.Enabled = false;
      this.newTextBox_Type.Value = "";
      try
      {
        DataTable table = clsDefine.g_dsMaterial.Tables["System"];
        if (table == null)
          return;
        DataRow[] array = table.AsEnumerable().ToArray<DataRow>();
        if (array.Length == 0)
          return;
        List<string> stringList = new List<string>();
        foreach (DataRow dataRow in array)
        {
          if (!stringList.Contains(dataRow["Manufacturer"].ToString()))
            stringList.Add(dataRow["Manufacturer"].ToString());
        }
        stringList.Sort();
        this.newComboBox_Company.Items.AddRange((object[]) stringList.ToArray());
        this.newComboBox_Company.Enabled = true;
        this.newComboBox_Company.SelectedIndex = 0;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInputDB]RefreshMaterial):" + ex.Message));
      }
    }

    private void GetMaterialFromMoldflow()
    {
      this.newComboBox_Company.Items.Clear();
      this.newComboBox_TradeName.Items.Clear();
      Dictionary<string, string> materialData = clsHDMFLib.GetMaterialData();
      this.newComboBox_Company.Items.Add((object) materialData["Manufacturer"]);
      this.newComboBox_Company.SelectedIndex = 0;
      this.newComboBox_TradeName.Items.Add((object) (materialData["Tradename"] + " [ID:" + materialData["MaterialID"] + "]"));
      this.newComboBox_TradeName.SelectedIndex = 0;
      this.newTextBox_Type.Value = materialData["FamilyAbbreviation"];
      this.newTextBox_Density.Value = materialData["SolidDensity"];
    }

    private void newComboBox_Company_SelectedIndexChanged(object sender, EventArgs e)
    {
      this.newComboBox_TradeName.Items.Clear();
      this.newComboBox_TradeName.Enabled = false;
      this.newTextBox_Type.Value = "";
      string strManufacturer = this.newComboBox_Company.Value;
      try
      {
        DataRow[] array = clsDefine.g_dsMaterial.Tables["System"].AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Manufacturer"].ToString() == strManufacturer)).ToArray<DataRow>();
        if (array.Length == 0)
          return;
        List<string> stringList = new List<string>();
        foreach (DataRow dataRow in array)
        {
          string str = dataRow["TradeName"].ToString() + " [ID:" + dataRow["ID"].ToString() + "]";
          if (!stringList.Contains(str))
            stringList.Add(str);
        }
        stringList.Sort();
        this.newComboBox_TradeName.Items.AddRange((object[]) stringList.ToArray());
        this.newComboBox_TradeName.Enabled = true;
        this.newComboBox_TradeName.SelectedIndex = 0;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInputDB]newComboBox_Company_SelectedIndexChanged):" + ex.Message));
      }
    }

    private void newComboBox_TradeName_SelectedIndexChanged(object sender, EventArgs e)
    {
      this.newTextBox_Type.Value = string.Empty;
      try
      {
        string strManufacturer = this.newComboBox_Company.Value;
        string strTradeName = "";
        string strMaterialID = "";
        string[] strArray = this.newComboBox_TradeName.Value.Split('[');
        strTradeName = strArray[0].TrimEnd();
        strMaterialID = strArray[1].Replace("]", "").Replace("ID:", "");
        DataTable table = clsDefine.g_dsMaterial.Tables["System"];
        DataRow dataRow = table.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Manufacturer"].ToString() == strManufacturer && Temp["TradeName"].ToString() == strTradeName && Temp["ID"].ToString() == strMaterialID)).FirstOrDefault<DataRow>();
        if (dataRow == null)
          return;
        this.newTextBox_Type.Value = dataRow["Familyabbreviation"].ToString();
        if (!table.Columns.Contains("MoldDensity"))
          return;
        this.newTextBox_Density.Value = dataRow["MoldDensity"].ToString();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInputDB]newComboBox_TradeName_SelectedIndexChanged):" + ex.Message));
      }
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.listBox_DB = new ListBox();
      this.label_DB_List = new Label();
      this.newButton_Export = new NewButton();
      this.label_Data = new Label();
      this.label_Company = new Label();
      this.label_TradeName = new Label();
      this.label_Density = new Label();
      this.label_Material = new Label();
      this.newTextBox_Density = new NewTextBox();
      this.label_Result = new Label();
      this.label_InjPressure = new Label();
      this.label_CycleTime = new Label();
      this.label_Deflection = new Label();
      this.label_Weight = new Label();
      this.label_TempAtFlowFront = new Label();
      this.label_Sinkmark = new Label();
      this.label_VolShrinkage = new Label();
      this.newTextBox_InjPressure_Priority = new NewTextBox();
      this.newTextBox_Sinkmark_Priority = new NewTextBox();
      this.newTextBox_CycleTime_Priority = new NewTextBox();
      this.newTextBox_VolShrinkage_Priority = new NewTextBox();
      this.newTextBox_Deflection_Priority = new NewTextBox();
      this.newTextBox_TempAtFlowFront_Priority = new NewTextBox();
      this.newTextBox_Weight_Priority = new NewTextBox();
      this.newButton_Import = new NewButton();
      this.newButton_Del = new NewButton();
      this.newButton_Edit = new NewButton();
      this.newButton_Add = new NewButton();
      this.newTextBox_Option = new NewTextBox();
      this.newTextBox_Item = new NewTextBox();
      this.newTextBox_Company = new NewTextBox();
      this.label_DB_Opt = new Label();
      this.label_DB_Item = new Label();
      this.label_DB_Company = new Label();
      this.newTextBox_GateNum_Min = new NewTextBox();
      this.newTextBox_GateNum_Max = new NewTextBox();
      this.label_GateNum = new Label();
      this.newTextBox_MoldTemp_Min = new NewTextBox();
      this.newTextBox_MoldTemp_Max = new NewTextBox();
      this.label_MoldTemp = new Label();
      this.newTextBox_MeltTemp_Min = new NewTextBox();
      this.newTextBox_MeltTemp_Max = new NewTextBox();
      this.newTextBox_CoolingInletTemp_Min = new NewTextBox();
      this.newTextBox_PackingNum_Min = new NewTextBox();
      this.newTextBox_CoolingTime_Min = new NewTextBox();
      this.newTextBox_VPSwitchOver_Min = new NewTextBox();
      this.newTextBox_FillTime_Min = new NewTextBox();
      this.newTextBox_CoolingInletTemp_Max = new NewTextBox();
      this.label_CoolingInletTemp = new Label();
      this.label_CoolingTime = new Label();
      this.label_FillTime = new Label();
      this.label_MeltTemp = new Label();
      this.newTextBox_PackingNum_Max = new NewTextBox();
      this.newTextBox_CoolingTime_Max = new NewTextBox();
      this.newTextBox_VPSwitchOver_Max = new NewTextBox();
      this.newTextBox_FillTime_Max = new NewTextBox();
      this.label_PackingNum = new Label();
      this.label_VPSwitchOver = new Label();
      this.newTextBox_Type = new NewTextBox();
      this.label_Type = new Label();
      this.newTextBox_ClampF_Priority = new NewTextBox();
      this.label_ClampF = new Label();
      this.label_AnalysisType = new Label();
      this.newComboBox_AnalysisType = new NewComboBox();
      this.newButton_LoadMaterial = new NewButton();
      this.newButton_System = new NewButton();
      this.newComboBox_Company = new NewComboBox();
      this.newComboBox_TradeName = new NewComboBox();
      this.newTextBox_InjectionPressureLimit = new NewTextBox();
      this.newTextBox_ClampForceLimit = new NewTextBox();
      this.newTextBox_DeflectionLimit = new NewTextBox();
      this.label_InjectionPressureLimit = new Label();
      this.newComboBox_LoopNum = new NewComboBox();
      this.newComboBox_ValveOption = new NewComboBox();
      this.label_Option = new Label();
      this.label_ValveOption = new Label();
      this.label_ClampForceLimit = new Label();
      this.label_DeflectionLimit = new Label();
      this.label_LoopNum = new Label();
      this.SuspendLayout();
      this.listBox_DB.FormattingEnabled = true;
      this.listBox_DB.ItemHeight = 15;
      this.listBox_DB.Location = new Point(5, 23);
      this.listBox_DB.Name = "listBox_DB";
      this.listBox_DB.SelectionMode = SelectionMode.MultiExtended;
      this.listBox_DB.Size = new Size(301, 619);
      this.listBox_DB.TabIndex = 3;
      this.listBox_DB.TabStop = false;
      this.listBox_DB.SelectedIndexChanged += new EventHandler(this.listBox_DB_SelectedIndexChanged);
      this.label_DB_List.BackColor = Color.FromArgb(229, 238, 248);
      this.label_DB_List.BorderStyle = BorderStyle.FixedSingle;
      this.label_DB_List.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_DB_List.ForeColor = Color.MidnightBlue;
      this.label_DB_List.Location = new Point(5, 5);
      this.label_DB_List.Name = "label_DB_List";
      this.label_DB_List.Size = new Size(301, 20);
      this.label_DB_List.TabIndex = 16;
      this.label_DB_List.Text = "DB 리스트";
      this.label_DB_List.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Export.ButtonBackColor = Color.White;
      this.newButton_Export.ButtonText = "출력";
      this.newButton_Export.FlatBorderSize = 1;
      this.newButton_Export.FlatStyle = FlatStyle.Flat;
      this.newButton_Export.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Export.ForeColor = Color.Navy;
      this.newButton_Export.Image = (Image) Resources.Export;
      this.newButton_Export.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Export.Location = new Point(246, 687);
      this.newButton_Export.Name = "newButton_Export";
      this.newButton_Export.Size = new Size(60, 32);
      this.newButton_Export.TabIndex = 11;
      this.newButton_Export.TabStop = false;
      this.newButton_Export.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Export.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Export.NewClick += new EventHandler(this.newButton_NewClick);
      this.label_Data.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Data.BorderStyle = BorderStyle.FixedSingle;
      this.label_Data.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Data.ForeColor = Color.MidnightBlue;
      this.label_Data.Location = new Point(310, 52);
      this.label_Data.Name = "label_Data";
      this.label_Data.Size = new Size(495, 20);
      this.label_Data.TabIndex = 97;
      this.label_Data.Text = "Data";
      this.label_Data.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Company.BackColor = Color.Lavender;
      this.label_Company.BorderStyle = BorderStyle.FixedSingle;
      this.label_Company.Location = new Point(310, 299);
      this.label_Company.Name = "label_Company";
      this.label_Company.Size = new Size(248, 23);
      this.label_Company.TabIndex = 129;
      this.label_Company.Text = "Company";
      this.label_Company.TextAlign = ContentAlignment.MiddleCenter;
      this.label_TradeName.BackColor = Color.Lavender;
      this.label_TradeName.BorderStyle = BorderStyle.FixedSingle;
      this.label_TradeName.Location = new Point(557, 299);
      this.label_TradeName.Name = "label_TradeName";
      this.label_TradeName.Size = new Size(248, 23);
      this.label_TradeName.TabIndex = 132;
      this.label_TradeName.Text = "Trade Name";
      this.label_TradeName.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Density.BackColor = Color.Lavender;
      this.label_Density.BorderStyle = BorderStyle.FixedSingle;
      this.label_Density.Location = new Point(557, 343);
      this.label_Density.Name = "label_Density";
      this.label_Density.Size = new Size(248, 23);
      this.label_Density.TabIndex = 133;
      this.label_Density.Text = "Density";
      this.label_Density.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Material.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Material.BorderStyle = BorderStyle.FixedSingle;
      this.label_Material.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Material.ForeColor = Color.MidnightBlue;
      this.label_Material.Location = new Point(310, 251);
      this.label_Material.Name = "label_Material";
      this.label_Material.Size = new Size(495, 20);
      this.label_Material.TabIndex = 134;
      this.label_Material.Text = "Material";
      this.label_Material.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Density.BackColor = Color.White;
      this.newTextBox_Density.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Density.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Density.IsDigit = false;
      this.newTextBox_Density.Lines = new string[0];
      this.newTextBox_Density.Location = new Point(557, 365);
      this.newTextBox_Density.MultiLine = false;
      this.newTextBox_Density.Name = "newTextBox_Density";
      this.newTextBox_Density.ReadOnly = false;
      this.newTextBox_Density.Size = new Size(248, 23);
      this.newTextBox_Density.TabIndex = 135;
      this.newTextBox_Density.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Density.TextBoxBackColor = Color.White;
      this.newTextBox_Density.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Density.Value = "";
      this.label_Result.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Result.BorderStyle = BorderStyle.FixedSingle;
      this.label_Result.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Result.ForeColor = Color.MidnightBlue;
      this.label_Result.Location = new Point(310, 390);
      this.label_Result.Name = "label_Result";
      this.label_Result.Size = new Size(495, 20);
      this.label_Result.TabIndex = 138;
      this.label_Result.Text = "결과 우선 순위";
      this.label_Result.TextAlign = ContentAlignment.MiddleCenter;
      this.label_InjPressure.BackColor = Color.Lavender;
      this.label_InjPressure.BorderStyle = BorderStyle.FixedSingle;
      this.label_InjPressure.Location = new Point(310, 519);
      this.label_InjPressure.Name = "label_InjPressure";
      this.label_InjPressure.Size = new Size(247, 23);
      this.label_InjPressure.TabIndex = 154;
      this.label_InjPressure.Text = "Injection Pressure";
      this.label_InjPressure.TextAlign = ContentAlignment.MiddleCenter;
      this.label_CycleTime.BackColor = Color.Lavender;
      this.label_CycleTime.BorderStyle = BorderStyle.FixedSingle;
      this.label_CycleTime.Location = new Point(310, 497);
      this.label_CycleTime.Name = "label_CycleTime";
      this.label_CycleTime.Size = new Size(247, 23);
      this.label_CycleTime.TabIndex = 153;
      this.label_CycleTime.Text = "Cycle Time";
      this.label_CycleTime.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Deflection.BackColor = Color.Lavender;
      this.label_Deflection.BorderStyle = BorderStyle.FixedSingle;
      this.label_Deflection.Location = new Point(310, 409);
      this.label_Deflection.Name = "label_Deflection";
      this.label_Deflection.Size = new Size(247, 23);
      this.label_Deflection.TabIndex = 148;
      this.label_Deflection.Text = "Deflection All";
      this.label_Deflection.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Weight.BackColor = Color.Lavender;
      this.label_Weight.BorderStyle = BorderStyle.FixedSingle;
      this.label_Weight.Location = new Point(310, 541);
      this.label_Weight.Name = "label_Weight";
      this.label_Weight.Size = new Size(247, 23);
      this.label_Weight.TabIndex = 149;
      this.label_Weight.Text = "Weight";
      this.label_Weight.TextAlign = ContentAlignment.MiddleCenter;
      this.label_TempAtFlowFront.BackColor = Color.Lavender;
      this.label_TempAtFlowFront.BorderStyle = BorderStyle.FixedSingle;
      this.label_TempAtFlowFront.Location = new Point(310, 475);
      this.label_TempAtFlowFront.Name = "label_TempAtFlowFront";
      this.label_TempAtFlowFront.Size = new Size(247, 23);
      this.label_TempAtFlowFront.TabIndex = 151;
      this.label_TempAtFlowFront.Text = "Temperature at Flow Front";
      this.label_TempAtFlowFront.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Sinkmark.BackColor = Color.Lavender;
      this.label_Sinkmark.BorderStyle = BorderStyle.FixedSingle;
      this.label_Sinkmark.Location = new Point(310, 453);
      this.label_Sinkmark.Name = "label_Sinkmark";
      this.label_Sinkmark.Size = new Size(247, 23);
      this.label_Sinkmark.TabIndex = 155;
      this.label_Sinkmark.Text = "Sink mark";
      this.label_Sinkmark.TextAlign = ContentAlignment.MiddleCenter;
      this.label_VolShrinkage.BackColor = Color.Lavender;
      this.label_VolShrinkage.BorderStyle = BorderStyle.FixedSingle;
      this.label_VolShrinkage.Location = new Point(310, 431);
      this.label_VolShrinkage.Name = "label_VolShrinkage";
      this.label_VolShrinkage.Size = new Size(247, 23);
      this.label_VolShrinkage.TabIndex = 158;
      this.label_VolShrinkage.Text = "Volumetric Shrinkage";
      this.label_VolShrinkage.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_InjPressure_Priority.BackColor = SystemColors.Window;
      this.newTextBox_InjPressure_Priority.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_InjPressure_Priority.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_InjPressure_Priority.IsDigit = false;
      this.newTextBox_InjPressure_Priority.Lines = new string[0];
      this.newTextBox_InjPressure_Priority.Location = new Point(555, 519);
      this.newTextBox_InjPressure_Priority.MultiLine = false;
      this.newTextBox_InjPressure_Priority.Name = "newTextBox_InjPressure_Priority";
      this.newTextBox_InjPressure_Priority.ReadOnly = false;
      this.newTextBox_InjPressure_Priority.Size = new Size(250, 23);
      this.newTextBox_InjPressure_Priority.TabIndex = 168;
      this.newTextBox_InjPressure_Priority.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_InjPressure_Priority.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_InjPressure_Priority.TextForeColor = SystemColors.WindowText;
      this.newTextBox_InjPressure_Priority.Value = "";
      this.newTextBox_Sinkmark_Priority.BackColor = SystemColors.Window;
      this.newTextBox_Sinkmark_Priority.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Sinkmark_Priority.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Sinkmark_Priority.IsDigit = false;
      this.newTextBox_Sinkmark_Priority.Lines = new string[0];
      this.newTextBox_Sinkmark_Priority.Location = new Point(555, 453);
      this.newTextBox_Sinkmark_Priority.MultiLine = false;
      this.newTextBox_Sinkmark_Priority.Name = "newTextBox_Sinkmark_Priority";
      this.newTextBox_Sinkmark_Priority.ReadOnly = false;
      this.newTextBox_Sinkmark_Priority.Size = new Size(250, 23);
      this.newTextBox_Sinkmark_Priority.TabIndex = 164;
      this.newTextBox_Sinkmark_Priority.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Sinkmark_Priority.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Sinkmark_Priority.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Sinkmark_Priority.Value = "";
      this.newTextBox_CycleTime_Priority.BackColor = SystemColors.Window;
      this.newTextBox_CycleTime_Priority.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_CycleTime_Priority.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_CycleTime_Priority.IsDigit = false;
      this.newTextBox_CycleTime_Priority.Lines = new string[0];
      this.newTextBox_CycleTime_Priority.Location = new Point(555, 497);
      this.newTextBox_CycleTime_Priority.MultiLine = false;
      this.newTextBox_CycleTime_Priority.Name = "newTextBox_CycleTime_Priority";
      this.newTextBox_CycleTime_Priority.ReadOnly = false;
      this.newTextBox_CycleTime_Priority.Size = new Size(250, 23);
      this.newTextBox_CycleTime_Priority.TabIndex = 167;
      this.newTextBox_CycleTime_Priority.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_CycleTime_Priority.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_CycleTime_Priority.TextForeColor = SystemColors.WindowText;
      this.newTextBox_CycleTime_Priority.Value = "";
      this.newTextBox_VolShrinkage_Priority.BackColor = SystemColors.Window;
      this.newTextBox_VolShrinkage_Priority.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_VolShrinkage_Priority.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_VolShrinkage_Priority.IsDigit = false;
      this.newTextBox_VolShrinkage_Priority.Lines = new string[0];
      this.newTextBox_VolShrinkage_Priority.Location = new Point(555, 431);
      this.newTextBox_VolShrinkage_Priority.MultiLine = false;
      this.newTextBox_VolShrinkage_Priority.Name = "newTextBox_VolShrinkage_Priority";
      this.newTextBox_VolShrinkage_Priority.ReadOnly = false;
      this.newTextBox_VolShrinkage_Priority.Size = new Size(250, 23);
      this.newTextBox_VolShrinkage_Priority.TabIndex = 163;
      this.newTextBox_VolShrinkage_Priority.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_VolShrinkage_Priority.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_VolShrinkage_Priority.TextForeColor = SystemColors.WindowText;
      this.newTextBox_VolShrinkage_Priority.Value = "";
      this.newTextBox_Deflection_Priority.BackColor = SystemColors.Window;
      this.newTextBox_Deflection_Priority.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Deflection_Priority.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Deflection_Priority.IsDigit = false;
      this.newTextBox_Deflection_Priority.Lines = new string[0];
      this.newTextBox_Deflection_Priority.Location = new Point(555, 409);
      this.newTextBox_Deflection_Priority.MultiLine = false;
      this.newTextBox_Deflection_Priority.Name = "newTextBox_Deflection_Priority";
      this.newTextBox_Deflection_Priority.ReadOnly = false;
      this.newTextBox_Deflection_Priority.Size = new Size(250, 23);
      this.newTextBox_Deflection_Priority.TabIndex = 162;
      this.newTextBox_Deflection_Priority.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Deflection_Priority.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Deflection_Priority.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Deflection_Priority.Value = "";
      this.newTextBox_TempAtFlowFront_Priority.BackColor = SystemColors.Window;
      this.newTextBox_TempAtFlowFront_Priority.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_TempAtFlowFront_Priority.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_TempAtFlowFront_Priority.IsDigit = false;
      this.newTextBox_TempAtFlowFront_Priority.Lines = new string[0];
      this.newTextBox_TempAtFlowFront_Priority.Location = new Point(555, 475);
      this.newTextBox_TempAtFlowFront_Priority.MultiLine = false;
      this.newTextBox_TempAtFlowFront_Priority.Name = "newTextBox_TempAtFlowFront_Priority";
      this.newTextBox_TempAtFlowFront_Priority.ReadOnly = false;
      this.newTextBox_TempAtFlowFront_Priority.Size = new Size(250, 23);
      this.newTextBox_TempAtFlowFront_Priority.TabIndex = 170;
      this.newTextBox_TempAtFlowFront_Priority.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_TempAtFlowFront_Priority.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_TempAtFlowFront_Priority.TextForeColor = SystemColors.WindowText;
      this.newTextBox_TempAtFlowFront_Priority.Value = "";
      this.newTextBox_Weight_Priority.BackColor = SystemColors.Window;
      this.newTextBox_Weight_Priority.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Weight_Priority.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Weight_Priority.IsDigit = false;
      this.newTextBox_Weight_Priority.Lines = new string[0];
      this.newTextBox_Weight_Priority.Location = new Point(555, 541);
      this.newTextBox_Weight_Priority.MultiLine = false;
      this.newTextBox_Weight_Priority.Name = "newTextBox_Weight_Priority";
      this.newTextBox_Weight_Priority.ReadOnly = false;
      this.newTextBox_Weight_Priority.Size = new Size(250, 23);
      this.newTextBox_Weight_Priority.TabIndex = 171;
      this.newTextBox_Weight_Priority.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Weight_Priority.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Weight_Priority.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Weight_Priority.Value = "";
      this.newButton_Import.ButtonBackColor = Color.White;
      this.newButton_Import.ButtonText = "삽입";
      this.newButton_Import.FlatBorderSize = 1;
      this.newButton_Import.FlatStyle = FlatStyle.Flat;
      this.newButton_Import.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Import.ForeColor = Color.Navy;
      this.newButton_Import.Image = (Image) Resources.Import;
      this.newButton_Import.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Import.Location = new Point(187, 687);
      this.newButton_Import.Name = "newButton_Import";
      this.newButton_Import.Size = new Size(60, 32);
      this.newButton_Import.TabIndex = 10;
      this.newButton_Import.TabStop = false;
      this.newButton_Import.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Import.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Import.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_Del.ButtonBackColor = Color.White;
      this.newButton_Del.ButtonText = "삭제";
      this.newButton_Del.FlatBorderSize = 1;
      this.newButton_Del.FlatStyle = FlatStyle.Flat;
      this.newButton_Del.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Del.ForeColor = Color.Navy;
      this.newButton_Del.Image = (Image) Resources.Del;
      this.newButton_Del.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Del.Location = new Point(123, 687);
      this.newButton_Del.Name = "newButton_Del";
      this.newButton_Del.Size = new Size(60, 32);
      this.newButton_Del.TabIndex = 9;
      this.newButton_Del.TabStop = false;
      this.newButton_Del.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Del.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Del.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_Edit.ButtonBackColor = Color.White;
      this.newButton_Edit.ButtonText = "수정";
      this.newButton_Edit.FlatBorderSize = 1;
      this.newButton_Edit.FlatStyle = FlatStyle.Flat;
      this.newButton_Edit.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Edit.ForeColor = Color.Navy;
      this.newButton_Edit.Image = (Image) Resources.Edit;
      this.newButton_Edit.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Edit.Location = new Point(64, 687);
      this.newButton_Edit.Name = "newButton_Edit";
      this.newButton_Edit.Size = new Size(60, 32);
      this.newButton_Edit.TabIndex = 8;
      this.newButton_Edit.TabStop = false;
      this.newButton_Edit.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Edit.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Edit.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_Add.ButtonBackColor = Color.White;
      this.newButton_Add.ButtonText = "추가";
      this.newButton_Add.FlatBorderSize = 1;
      this.newButton_Add.FlatStyle = FlatStyle.Flat;
      this.newButton_Add.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Add.ForeColor = Color.Navy;
      this.newButton_Add.Image = (Image) Resources.Add;
      this.newButton_Add.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Add.Location = new Point(5, 687);
      this.newButton_Add.Name = "newButton_Add";
      this.newButton_Add.Size = new Size(60, 32);
      this.newButton_Add.TabIndex = 7;
      this.newButton_Add.TabStop = false;
      this.newButton_Add.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Add.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Add.NewClick += new EventHandler(this.newButton_NewClick);
      this.newTextBox_Option.BackColor = Color.White;
      this.newTextBox_Option.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Option.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Option.IsDigit = false;
      this.newTextBox_Option.Lines = new string[0];
      this.newTextBox_Option.Location = new Point(205, 661);
      this.newTextBox_Option.MultiLine = false;
      this.newTextBox_Option.Name = "newTextBox_Option";
      this.newTextBox_Option.ReadOnly = false;
      this.newTextBox_Option.Size = new Size(101, 23);
      this.newTextBox_Option.TabIndex = 175;
      this.newTextBox_Option.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Option.TextBoxBackColor = Color.White;
      this.newTextBox_Option.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Option.Value = "";
      this.newTextBox_Item.BackColor = Color.White;
      this.newTextBox_Item.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Item.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Item.IsDigit = false;
      this.newTextBox_Item.Lines = new string[0];
      this.newTextBox_Item.Location = new Point(105, 661);
      this.newTextBox_Item.MultiLine = false;
      this.newTextBox_Item.Name = "newTextBox_Item";
      this.newTextBox_Item.ReadOnly = false;
      this.newTextBox_Item.Size = new Size(101, 23);
      this.newTextBox_Item.TabIndex = 174;
      this.newTextBox_Item.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Item.TextBoxBackColor = Color.White;
      this.newTextBox_Item.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Item.Value = "";
      this.newTextBox_Company.BackColor = Color.White;
      this.newTextBox_Company.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Company.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Company.IsDigit = false;
      this.newTextBox_Company.Lines = new string[0];
      this.newTextBox_Company.Location = new Point(5, 661);
      this.newTextBox_Company.MultiLine = false;
      this.newTextBox_Company.Name = "newTextBox_Company";
      this.newTextBox_Company.ReadOnly = false;
      this.newTextBox_Company.Size = new Size(101, 23);
      this.newTextBox_Company.TabIndex = 173;
      this.newTextBox_Company.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Company.TextBoxBackColor = Color.White;
      this.newTextBox_Company.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Company.Value = "";
      this.label_DB_Opt.BackColor = Color.Lavender;
      this.label_DB_Opt.BorderStyle = BorderStyle.FixedSingle;
      this.label_DB_Opt.Location = new Point(205, 639);
      this.label_DB_Opt.Name = "label_DB_Opt";
      this.label_DB_Opt.Size = new Size(101, 23);
      this.label_DB_Opt.TabIndex = 178;
      this.label_DB_Opt.Text = "옵션";
      this.label_DB_Opt.TextAlign = ContentAlignment.MiddleCenter;
      this.label_DB_Item.BackColor = Color.Lavender;
      this.label_DB_Item.BorderStyle = BorderStyle.FixedSingle;
      this.label_DB_Item.Location = new Point(105, 639);
      this.label_DB_Item.Name = "label_DB_Item";
      this.label_DB_Item.Size = new Size(101, 23);
      this.label_DB_Item.TabIndex = 177;
      this.label_DB_Item.Text = "아이템명";
      this.label_DB_Item.TextAlign = ContentAlignment.MiddleCenter;
      this.label_DB_Company.BackColor = Color.Lavender;
      this.label_DB_Company.BorderStyle = BorderStyle.FixedSingle;
      this.label_DB_Company.Location = new Point(5, 639);
      this.label_DB_Company.Name = "label_DB_Company";
      this.label_DB_Company.Size = new Size(101, 23);
      this.label_DB_Company.TabIndex = 176;
      this.label_DB_Company.Text = "회사명";
      this.label_DB_Company.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_GateNum_Min.BackColor = SystemColors.Window;
      this.newTextBox_GateNum_Min.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_GateNum_Min.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_GateNum_Min.IsDigit = false;
      this.newTextBox_GateNum_Min.Lines = new string[0];
      this.newTextBox_GateNum_Min.Location = new Point(555, 225);
      this.newTextBox_GateNum_Min.MultiLine = false;
      this.newTextBox_GateNum_Min.Name = "newTextBox_GateNum_Min";
      this.newTextBox_GateNum_Min.ReadOnly = false;
      this.newTextBox_GateNum_Min.Size = new Size(126, 23);
      this.newTextBox_GateNum_Min.TabIndex = 226;
      this.newTextBox_GateNum_Min.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_GateNum_Min.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_GateNum_Min.TextForeColor = SystemColors.WindowText;
      this.newTextBox_GateNum_Min.Value = "";
      this.newTextBox_GateNum_Max.BackColor = SystemColors.Window;
      this.newTextBox_GateNum_Max.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_GateNum_Max.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_GateNum_Max.IsDigit = false;
      this.newTextBox_GateNum_Max.Lines = new string[0];
      this.newTextBox_GateNum_Max.Location = new Point(679, 225);
      this.newTextBox_GateNum_Max.MultiLine = false;
      this.newTextBox_GateNum_Max.Name = "newTextBox_GateNum_Max";
      this.newTextBox_GateNum_Max.ReadOnly = false;
      this.newTextBox_GateNum_Max.Size = new Size(126, 23);
      this.newTextBox_GateNum_Max.TabIndex = 227;
      this.newTextBox_GateNum_Max.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_GateNum_Max.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_GateNum_Max.TextForeColor = SystemColors.WindowText;
      this.newTextBox_GateNum_Max.Value = "";
      this.label_GateNum.BackColor = Color.Lavender;
      this.label_GateNum.BorderStyle = BorderStyle.FixedSingle;
      this.label_GateNum.Location = new Point(310, 225);
      this.label_GateNum.Name = "label_GateNum";
      this.label_GateNum.Size = new Size(247, 23);
      this.label_GateNum.TabIndex = 225;
      this.label_GateNum.Text = "Gate Number";
      this.label_GateNum.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_MoldTemp_Min.BackColor = SystemColors.Window;
      this.newTextBox_MoldTemp_Min.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_MoldTemp_Min.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_MoldTemp_Min.IsDigit = false;
      this.newTextBox_MoldTemp_Min.Lines = new string[0];
      this.newTextBox_MoldTemp_Min.Location = new Point(555, 203);
      this.newTextBox_MoldTemp_Min.MultiLine = false;
      this.newTextBox_MoldTemp_Min.Name = "newTextBox_MoldTemp_Min";
      this.newTextBox_MoldTemp_Min.ReadOnly = false;
      this.newTextBox_MoldTemp_Min.Size = new Size(126, 23);
      this.newTextBox_MoldTemp_Min.TabIndex = 223;
      this.newTextBox_MoldTemp_Min.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_MoldTemp_Min.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_MoldTemp_Min.TextForeColor = SystemColors.WindowText;
      this.newTextBox_MoldTemp_Min.Value = "";
      this.newTextBox_MoldTemp_Max.BackColor = SystemColors.Window;
      this.newTextBox_MoldTemp_Max.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_MoldTemp_Max.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_MoldTemp_Max.IsDigit = false;
      this.newTextBox_MoldTemp_Max.Lines = new string[0];
      this.newTextBox_MoldTemp_Max.Location = new Point(679, 203);
      this.newTextBox_MoldTemp_Max.MultiLine = false;
      this.newTextBox_MoldTemp_Max.Name = "newTextBox_MoldTemp_Max";
      this.newTextBox_MoldTemp_Max.ReadOnly = false;
      this.newTextBox_MoldTemp_Max.Size = new Size(126, 23);
      this.newTextBox_MoldTemp_Max.TabIndex = 224;
      this.newTextBox_MoldTemp_Max.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_MoldTemp_Max.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_MoldTemp_Max.TextForeColor = SystemColors.WindowText;
      this.newTextBox_MoldTemp_Max.Value = "";
      this.label_MoldTemp.BackColor = Color.Lavender;
      this.label_MoldTemp.BorderStyle = BorderStyle.FixedSingle;
      this.label_MoldTemp.Location = new Point(310, 203);
      this.label_MoldTemp.Name = "label_MoldTemp";
      this.label_MoldTemp.Size = new Size(247, 23);
      this.label_MoldTemp.TabIndex = 222;
      this.label_MoldTemp.Text = "Mold Temperature";
      this.label_MoldTemp.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_MeltTemp_Min.BackColor = SystemColors.Window;
      this.newTextBox_MeltTemp_Min.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_MeltTemp_Min.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_MeltTemp_Min.IsDigit = false;
      this.newTextBox_MeltTemp_Min.Lines = new string[0];
      this.newTextBox_MeltTemp_Min.Location = new Point(555, 181);
      this.newTextBox_MeltTemp_Min.MultiLine = false;
      this.newTextBox_MeltTemp_Min.Name = "newTextBox_MeltTemp_Min";
      this.newTextBox_MeltTemp_Min.ReadOnly = false;
      this.newTextBox_MeltTemp_Min.Size = new Size(126, 23);
      this.newTextBox_MeltTemp_Min.TabIndex = 219;
      this.newTextBox_MeltTemp_Min.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_MeltTemp_Min.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_MeltTemp_Min.TextForeColor = SystemColors.WindowText;
      this.newTextBox_MeltTemp_Min.Value = "";
      this.newTextBox_MeltTemp_Max.BackColor = SystemColors.Window;
      this.newTextBox_MeltTemp_Max.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_MeltTemp_Max.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_MeltTemp_Max.IsDigit = false;
      this.newTextBox_MeltTemp_Max.Lines = new string[0];
      this.newTextBox_MeltTemp_Max.Location = new Point(679, 181);
      this.newTextBox_MeltTemp_Max.MultiLine = false;
      this.newTextBox_MeltTemp_Max.Name = "newTextBox_MeltTemp_Max";
      this.newTextBox_MeltTemp_Max.ReadOnly = false;
      this.newTextBox_MeltTemp_Max.Size = new Size(126, 23);
      this.newTextBox_MeltTemp_Max.TabIndex = 221;
      this.newTextBox_MeltTemp_Max.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_MeltTemp_Max.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_MeltTemp_Max.TextForeColor = SystemColors.WindowText;
      this.newTextBox_MeltTemp_Max.Value = "";
      this.newTextBox_CoolingInletTemp_Min.BackColor = SystemColors.Window;
      this.newTextBox_CoolingInletTemp_Min.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_CoolingInletTemp_Min.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_CoolingInletTemp_Min.IsDigit = false;
      this.newTextBox_CoolingInletTemp_Min.Lines = new string[0];
      this.newTextBox_CoolingInletTemp_Min.Location = new Point(555, 159);
      this.newTextBox_CoolingInletTemp_Min.MultiLine = false;
      this.newTextBox_CoolingInletTemp_Min.Name = "newTextBox_CoolingInletTemp_Min";
      this.newTextBox_CoolingInletTemp_Min.ReadOnly = false;
      this.newTextBox_CoolingInletTemp_Min.Size = new Size(126, 23);
      this.newTextBox_CoolingInletTemp_Min.TabIndex = 217;
      this.newTextBox_CoolingInletTemp_Min.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_CoolingInletTemp_Min.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_CoolingInletTemp_Min.TextForeColor = SystemColors.WindowText;
      this.newTextBox_CoolingInletTemp_Min.Value = "";
      this.newTextBox_PackingNum_Min.BackColor = SystemColors.Window;
      this.newTextBox_PackingNum_Min.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_PackingNum_Min.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_PackingNum_Min.IsDigit = false;
      this.newTextBox_PackingNum_Min.Lines = new string[0];
      this.newTextBox_PackingNum_Min.Location = new Point(555, 115);
      this.newTextBox_PackingNum_Min.MultiLine = false;
      this.newTextBox_PackingNum_Min.Name = "newTextBox_PackingNum_Min";
      this.newTextBox_PackingNum_Min.ReadOnly = false;
      this.newTextBox_PackingNum_Min.Size = new Size(126, 23);
      this.newTextBox_PackingNum_Min.TabIndex = 215;
      this.newTextBox_PackingNum_Min.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_PackingNum_Min.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_PackingNum_Min.TextForeColor = SystemColors.WindowText;
      this.newTextBox_PackingNum_Min.Value = "";
      this.newTextBox_CoolingTime_Min.BackColor = SystemColors.Window;
      this.newTextBox_CoolingTime_Min.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_CoolingTime_Min.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_CoolingTime_Min.IsDigit = false;
      this.newTextBox_CoolingTime_Min.Lines = new string[0];
      this.newTextBox_CoolingTime_Min.Location = new Point(555, 137);
      this.newTextBox_CoolingTime_Min.MultiLine = false;
      this.newTextBox_CoolingTime_Min.Name = "newTextBox_CoolingTime_Min";
      this.newTextBox_CoolingTime_Min.ReadOnly = false;
      this.newTextBox_CoolingTime_Min.Size = new Size(126, 23);
      this.newTextBox_CoolingTime_Min.TabIndex = 216;
      this.newTextBox_CoolingTime_Min.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_CoolingTime_Min.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_CoolingTime_Min.TextForeColor = SystemColors.WindowText;
      this.newTextBox_CoolingTime_Min.Value = "";
      this.newTextBox_VPSwitchOver_Min.BackColor = SystemColors.Window;
      this.newTextBox_VPSwitchOver_Min.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_VPSwitchOver_Min.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_VPSwitchOver_Min.IsDigit = false;
      this.newTextBox_VPSwitchOver_Min.Lines = new string[0];
      this.newTextBox_VPSwitchOver_Min.Location = new Point(555, 93);
      this.newTextBox_VPSwitchOver_Min.MultiLine = false;
      this.newTextBox_VPSwitchOver_Min.Name = "newTextBox_VPSwitchOver_Min";
      this.newTextBox_VPSwitchOver_Min.ReadOnly = false;
      this.newTextBox_VPSwitchOver_Min.Size = new Size(126, 23);
      this.newTextBox_VPSwitchOver_Min.TabIndex = 214;
      this.newTextBox_VPSwitchOver_Min.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_VPSwitchOver_Min.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_VPSwitchOver_Min.TextForeColor = SystemColors.WindowText;
      this.newTextBox_VPSwitchOver_Min.Value = "";
      this.newTextBox_FillTime_Min.BackColor = SystemColors.Window;
      this.newTextBox_FillTime_Min.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_FillTime_Min.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_FillTime_Min.IsDigit = false;
      this.newTextBox_FillTime_Min.Lines = new string[0];
      this.newTextBox_FillTime_Min.Location = new Point(555, 71);
      this.newTextBox_FillTime_Min.MultiLine = false;
      this.newTextBox_FillTime_Min.Name = "newTextBox_FillTime_Min";
      this.newTextBox_FillTime_Min.ReadOnly = false;
      this.newTextBox_FillTime_Min.Size = new Size(126, 23);
      this.newTextBox_FillTime_Min.TabIndex = 213;
      this.newTextBox_FillTime_Min.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_FillTime_Min.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_FillTime_Min.TextForeColor = SystemColors.WindowText;
      this.newTextBox_FillTime_Min.Value = "";
      this.newTextBox_CoolingInletTemp_Max.BackColor = SystemColors.Window;
      this.newTextBox_CoolingInletTemp_Max.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_CoolingInletTemp_Max.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_CoolingInletTemp_Max.IsDigit = false;
      this.newTextBox_CoolingInletTemp_Max.Lines = new string[0];
      this.newTextBox_CoolingInletTemp_Max.Location = new Point(679, 159);
      this.newTextBox_CoolingInletTemp_Max.MultiLine = false;
      this.newTextBox_CoolingInletTemp_Max.Name = "newTextBox_CoolingInletTemp_Max";
      this.newTextBox_CoolingInletTemp_Max.ReadOnly = false;
      this.newTextBox_CoolingInletTemp_Max.Size = new Size(126, 23);
      this.newTextBox_CoolingInletTemp_Max.TabIndex = 212;
      this.newTextBox_CoolingInletTemp_Max.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_CoolingInletTemp_Max.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_CoolingInletTemp_Max.TextForeColor = SystemColors.WindowText;
      this.newTextBox_CoolingInletTemp_Max.Value = "";
      this.label_CoolingInletTemp.BackColor = Color.Lavender;
      this.label_CoolingInletTemp.BorderStyle = BorderStyle.FixedSingle;
      this.label_CoolingInletTemp.Location = new Point(310, 159);
      this.label_CoolingInletTemp.Name = "label_CoolingInletTemp";
      this.label_CoolingInletTemp.Size = new Size(247, 23);
      this.label_CoolingInletTemp.TabIndex = 208;
      this.label_CoolingInletTemp.Text = "Cooling Inlet Temperature";
      this.label_CoolingInletTemp.TextAlign = ContentAlignment.MiddleCenter;
      this.label_CoolingTime.BackColor = Color.Lavender;
      this.label_CoolingTime.BorderStyle = BorderStyle.FixedSingle;
      this.label_CoolingTime.Location = new Point(310, 137);
      this.label_CoolingTime.Name = "label_CoolingTime";
      this.label_CoolingTime.Size = new Size(247, 23);
      this.label_CoolingTime.TabIndex = 207;
      this.label_CoolingTime.Text = "Cooling Time";
      this.label_CoolingTime.TextAlign = ContentAlignment.MiddleCenter;
      this.label_FillTime.BackColor = Color.Lavender;
      this.label_FillTime.BorderStyle = BorderStyle.FixedSingle;
      this.label_FillTime.Location = new Point(310, 71);
      this.label_FillTime.Name = "label_FillTime";
      this.label_FillTime.Size = new Size(247, 23);
      this.label_FillTime.TabIndex = 204;
      this.label_FillTime.Text = "Fill Time";
      this.label_FillTime.TextAlign = ContentAlignment.MiddleCenter;
      this.label_MeltTemp.BackColor = Color.Lavender;
      this.label_MeltTemp.BorderStyle = BorderStyle.FixedSingle;
      this.label_MeltTemp.Location = new Point(310, 181);
      this.label_MeltTemp.Name = "label_MeltTemp";
      this.label_MeltTemp.Size = new Size(247, 23);
      this.label_MeltTemp.TabIndex = 205;
      this.label_MeltTemp.Text = "Melt Temperature";
      this.label_MeltTemp.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_PackingNum_Max.BackColor = SystemColors.Window;
      this.newTextBox_PackingNum_Max.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_PackingNum_Max.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_PackingNum_Max.IsDigit = false;
      this.newTextBox_PackingNum_Max.Lines = new string[0];
      this.newTextBox_PackingNum_Max.Location = new Point(679, 115);
      this.newTextBox_PackingNum_Max.MultiLine = false;
      this.newTextBox_PackingNum_Max.Name = "newTextBox_PackingNum_Max";
      this.newTextBox_PackingNum_Max.ReadOnly = false;
      this.newTextBox_PackingNum_Max.Size = new Size(126, 23);
      this.newTextBox_PackingNum_Max.TabIndex = 203;
      this.newTextBox_PackingNum_Max.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_PackingNum_Max.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_PackingNum_Max.TextForeColor = SystemColors.WindowText;
      this.newTextBox_PackingNum_Max.Value = "";
      this.newTextBox_CoolingTime_Max.BackColor = SystemColors.Window;
      this.newTextBox_CoolingTime_Max.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_CoolingTime_Max.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_CoolingTime_Max.IsDigit = false;
      this.newTextBox_CoolingTime_Max.Lines = new string[0];
      this.newTextBox_CoolingTime_Max.Location = new Point(679, 137);
      this.newTextBox_CoolingTime_Max.MultiLine = false;
      this.newTextBox_CoolingTime_Max.Name = "newTextBox_CoolingTime_Max";
      this.newTextBox_CoolingTime_Max.ReadOnly = false;
      this.newTextBox_CoolingTime_Max.Size = new Size(126, 23);
      this.newTextBox_CoolingTime_Max.TabIndex = 211;
      this.newTextBox_CoolingTime_Max.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_CoolingTime_Max.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_CoolingTime_Max.TextForeColor = SystemColors.WindowText;
      this.newTextBox_CoolingTime_Max.Value = "";
      this.newTextBox_VPSwitchOver_Max.BackColor = SystemColors.Window;
      this.newTextBox_VPSwitchOver_Max.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_VPSwitchOver_Max.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_VPSwitchOver_Max.IsDigit = false;
      this.newTextBox_VPSwitchOver_Max.Lines = new string[0];
      this.newTextBox_VPSwitchOver_Max.Location = new Point(679, 93);
      this.newTextBox_VPSwitchOver_Max.MultiLine = false;
      this.newTextBox_VPSwitchOver_Max.Name = "newTextBox_VPSwitchOver_Max";
      this.newTextBox_VPSwitchOver_Max.ReadOnly = false;
      this.newTextBox_VPSwitchOver_Max.Size = new Size(126, 23);
      this.newTextBox_VPSwitchOver_Max.TabIndex = 202;
      this.newTextBox_VPSwitchOver_Max.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_VPSwitchOver_Max.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_VPSwitchOver_Max.TextForeColor = SystemColors.WindowText;
      this.newTextBox_VPSwitchOver_Max.Value = "";
      this.newTextBox_FillTime_Max.BackColor = SystemColors.Window;
      this.newTextBox_FillTime_Max.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_FillTime_Max.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_FillTime_Max.IsDigit = false;
      this.newTextBox_FillTime_Max.Lines = new string[0];
      this.newTextBox_FillTime_Max.Location = new Point(679, 71);
      this.newTextBox_FillTime_Max.MultiLine = false;
      this.newTextBox_FillTime_Max.Name = "newTextBox_FillTime_Max";
      this.newTextBox_FillTime_Max.ReadOnly = false;
      this.newTextBox_FillTime_Max.Size = new Size(126, 23);
      this.newTextBox_FillTime_Max.TabIndex = 201;
      this.newTextBox_FillTime_Max.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_FillTime_Max.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_FillTime_Max.TextForeColor = SystemColors.WindowText;
      this.newTextBox_FillTime_Max.Value = "";
      this.label_PackingNum.BackColor = Color.Lavender;
      this.label_PackingNum.BorderStyle = BorderStyle.FixedSingle;
      this.label_PackingNum.Location = new Point(310, 115);
      this.label_PackingNum.Name = "label_PackingNum";
      this.label_PackingNum.Size = new Size(247, 23);
      this.label_PackingNum.TabIndex = 209;
      this.label_PackingNum.Text = "Packing Number";
      this.label_PackingNum.TextAlign = ContentAlignment.MiddleCenter;
      this.label_VPSwitchOver.BackColor = Color.Lavender;
      this.label_VPSwitchOver.BorderStyle = BorderStyle.FixedSingle;
      this.label_VPSwitchOver.Location = new Point(310, 93);
      this.label_VPSwitchOver.Name = "label_VPSwitchOver";
      this.label_VPSwitchOver.Size = new Size(247, 23);
      this.label_VPSwitchOver.TabIndex = 210;
      this.label_VPSwitchOver.Text = "V/P Switch Over";
      this.label_VPSwitchOver.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Type.BackColor = Color.White;
      this.newTextBox_Type.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Type.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Type.IsDigit = false;
      this.newTextBox_Type.Lines = new string[0];
      this.newTextBox_Type.Location = new Point(310, 365);
      this.newTextBox_Type.MultiLine = false;
      this.newTextBox_Type.Name = "newTextBox_Type";
      this.newTextBox_Type.ReadOnly = false;
      this.newTextBox_Type.Size = new Size(248, 23);
      this.newTextBox_Type.TabIndex = 229;
      this.newTextBox_Type.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Type.TextBoxBackColor = Color.White;
      this.newTextBox_Type.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Type.Value = "";
      this.label_Type.BackColor = Color.Lavender;
      this.label_Type.BorderStyle = BorderStyle.FixedSingle;
      this.label_Type.Location = new Point(310, 343);
      this.label_Type.Name = "label_Type";
      this.label_Type.Size = new Size(248, 23);
      this.label_Type.TabIndex = 228;
      this.label_Type.Text = "Type";
      this.label_Type.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_ClampF_Priority.BackColor = SystemColors.Window;
      this.newTextBox_ClampF_Priority.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_ClampF_Priority.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_ClampF_Priority.IsDigit = false;
      this.newTextBox_ClampF_Priority.Lines = new string[0];
      this.newTextBox_ClampF_Priority.Location = new Point(555, 563);
      this.newTextBox_ClampF_Priority.MultiLine = false;
      this.newTextBox_ClampF_Priority.Name = "newTextBox_ClampF_Priority";
      this.newTextBox_ClampF_Priority.ReadOnly = false;
      this.newTextBox_ClampF_Priority.Size = new Size(250, 23);
      this.newTextBox_ClampF_Priority.TabIndex = 232;
      this.newTextBox_ClampF_Priority.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_ClampF_Priority.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_ClampF_Priority.TextForeColor = SystemColors.WindowText;
      this.newTextBox_ClampF_Priority.Value = "";
      this.label_ClampF.BackColor = Color.Lavender;
      this.label_ClampF.BorderStyle = BorderStyle.FixedSingle;
      this.label_ClampF.Location = new Point(310, 563);
      this.label_ClampF.Name = "label_ClampF";
      this.label_ClampF.Size = new Size(247, 23);
      this.label_ClampF.TabIndex = 230;
      this.label_ClampF.Text = "Clamp Force";
      this.label_ClampF.TextAlign = ContentAlignment.MiddleCenter;
      this.label_AnalysisType.BackColor = Color.FromArgb(229, 238, 248);
      this.label_AnalysisType.BorderStyle = BorderStyle.FixedSingle;
      this.label_AnalysisType.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_AnalysisType.ForeColor = Color.MidnightBlue;
      this.label_AnalysisType.Location = new Point(310, 5);
      this.label_AnalysisType.Name = "label_AnalysisType";
      this.label_AnalysisType.Size = new Size(495, 20);
      this.label_AnalysisType.TabIndex = 234;
      this.label_AnalysisType.Text = "Analysis Type";
      this.label_AnalysisType.TextAlign = ContentAlignment.MiddleCenter;
      this.newComboBox_AnalysisType.BackColor = Color.White;
      this.newComboBox_AnalysisType.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_AnalysisType.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_AnalysisType.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_AnalysisType.isSameSelect = false;
      this.newComboBox_AnalysisType.Location = new Point(310, 24);
      this.newComboBox_AnalysisType.Name = "newComboBox_AnalysisType";
      this.newComboBox_AnalysisType.SelectedIndex = -1;
      this.newComboBox_AnalysisType.Size = new Size(495, 23);
      this.newComboBox_AnalysisType.TabIndex = 233;
      this.newComboBox_AnalysisType.TabStop = false;
      this.newComboBox_AnalysisType.TextAlign = HorizontalAlignment.Center;
      this.newComboBox_AnalysisType.Value = "";
      this.newButton_LoadMaterial.ButtonBackColor = Color.White;
      this.newButton_LoadMaterial.ButtonText = "설정값 가져오기";
      this.newButton_LoadMaterial.FlatBorderSize = 1;
      this.newButton_LoadMaterial.FlatStyle = FlatStyle.Flat;
      this.newButton_LoadMaterial.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_LoadMaterial.ForeColor = Color.Navy;
      this.newButton_LoadMaterial.Image = (Image) null;
      this.newButton_LoadMaterial.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_LoadMaterial.Location = new Point(557, 270);
      this.newButton_LoadMaterial.Name = "newButton_LoadMaterial";
      this.newButton_LoadMaterial.Size = new Size(248, 30);
      this.newButton_LoadMaterial.TabIndex = 236;
      this.newButton_LoadMaterial.TabStop = false;
      this.newButton_LoadMaterial.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_LoadMaterial.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_LoadMaterial.NewClick += new EventHandler(this.newButton_System_NewClick);
      this.newButton_System.ButtonBackColor = Color.White;
      this.newButton_System.ButtonText = "시스템";
      this.newButton_System.FlatBorderSize = 1;
      this.newButton_System.FlatStyle = FlatStyle.Flat;
      this.newButton_System.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_System.ForeColor = Color.Navy;
      this.newButton_System.Image = (Image) null;
      this.newButton_System.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_System.Location = new Point(310, 270);
      this.newButton_System.Name = "newButton_System";
      this.newButton_System.Size = new Size(248, 30);
      this.newButton_System.TabIndex = 235;
      this.newButton_System.TabStop = false;
      this.newButton_System.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_System.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_System.NewClick += new EventHandler(this.newButton_System_NewClick);
      this.newComboBox_Company.BackColor = Color.White;
      this.newComboBox_Company.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Company.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_Company.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Company.isSameSelect = false;
      this.newComboBox_Company.Location = new Point(310, 321);
      this.newComboBox_Company.Name = "newComboBox_Company";
      this.newComboBox_Company.SelectedIndex = -1;
      this.newComboBox_Company.Size = new Size(248, 23);
      this.newComboBox_Company.TabIndex = 237;
      this.newComboBox_Company.TabStop = false;
      this.newComboBox_Company.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Company.Value = "";
      this.newComboBox_Company.SelectedIndexChanged += new EventHandler(this.newComboBox_Company_SelectedIndexChanged);
      this.newComboBox_TradeName.BackColor = Color.White;
      this.newComboBox_TradeName.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_TradeName.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_TradeName.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_TradeName.isSameSelect = false;
      this.newComboBox_TradeName.Location = new Point(557, 321);
      this.newComboBox_TradeName.Name = "newComboBox_TradeName";
      this.newComboBox_TradeName.SelectedIndex = -1;
      this.newComboBox_TradeName.Size = new Size(248, 23);
      this.newComboBox_TradeName.TabIndex = 238;
      this.newComboBox_TradeName.TabStop = false;
      this.newComboBox_TradeName.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_TradeName.Value = "";
      this.newComboBox_TradeName.SelectedIndexChanged += new EventHandler(this.newComboBox_TradeName_SelectedIndexChanged);
      this.newTextBox_InjectionPressureLimit.BackColor = SystemColors.Window;
      this.newTextBox_InjectionPressureLimit.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_InjectionPressureLimit.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_InjectionPressureLimit.IsDigit = false;
      this.newTextBox_InjectionPressureLimit.Lines = new string[0];
      this.newTextBox_InjectionPressureLimit.Location = new Point(556, 696);
      this.newTextBox_InjectionPressureLimit.MultiLine = false;
      this.newTextBox_InjectionPressureLimit.Name = "newTextBox_InjectionPressureLimit";
      this.newTextBox_InjectionPressureLimit.ReadOnly = false;
      this.newTextBox_InjectionPressureLimit.Size = new Size(249, 23);
      this.newTextBox_InjectionPressureLimit.TabIndex = 249;
      this.newTextBox_InjectionPressureLimit.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_InjectionPressureLimit.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_InjectionPressureLimit.TextForeColor = SystemColors.WindowText;
      this.newTextBox_InjectionPressureLimit.Value = "";
      this.newTextBox_ClampForceLimit.BackColor = SystemColors.Window;
      this.newTextBox_ClampForceLimit.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_ClampForceLimit.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_ClampForceLimit.IsDigit = false;
      this.newTextBox_ClampForceLimit.Lines = new string[0];
      this.newTextBox_ClampForceLimit.Location = new Point(556, 674);
      this.newTextBox_ClampForceLimit.MultiLine = false;
      this.newTextBox_ClampForceLimit.Name = "newTextBox_ClampForceLimit";
      this.newTextBox_ClampForceLimit.ReadOnly = false;
      this.newTextBox_ClampForceLimit.Size = new Size(249, 23);
      this.newTextBox_ClampForceLimit.TabIndex = 248;
      this.newTextBox_ClampForceLimit.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_ClampForceLimit.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_ClampForceLimit.TextForeColor = SystemColors.WindowText;
      this.newTextBox_ClampForceLimit.Value = "";
      this.newTextBox_DeflectionLimit.BackColor = SystemColors.Window;
      this.newTextBox_DeflectionLimit.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_DeflectionLimit.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_DeflectionLimit.IsDigit = false;
      this.newTextBox_DeflectionLimit.Lines = new string[0];
      this.newTextBox_DeflectionLimit.Location = new Point(556, 652);
      this.newTextBox_DeflectionLimit.MultiLine = false;
      this.newTextBox_DeflectionLimit.Name = "newTextBox_DeflectionLimit";
      this.newTextBox_DeflectionLimit.ReadOnly = false;
      this.newTextBox_DeflectionLimit.Size = new Size(249, 23);
      this.newTextBox_DeflectionLimit.TabIndex = 247;
      this.newTextBox_DeflectionLimit.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_DeflectionLimit.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_DeflectionLimit.TextForeColor = SystemColors.WindowText;
      this.newTextBox_DeflectionLimit.Value = "";
      this.label_InjectionPressureLimit.BackColor = Color.Lavender;
      this.label_InjectionPressureLimit.BorderStyle = BorderStyle.FixedSingle;
      this.label_InjectionPressureLimit.Location = new Point(310, 696);
      this.label_InjectionPressureLimit.Name = "label_InjectionPressureLimit";
      this.label_InjectionPressureLimit.Size = new Size(247, 23);
      this.label_InjectionPressureLimit.TabIndex = 246;
      this.label_InjectionPressureLimit.Text = "사출압력 제한 값";
      this.label_InjectionPressureLimit.TextAlign = ContentAlignment.MiddleCenter;
      this.newComboBox_LoopNum.BackColor = Color.White;
      this.newComboBox_LoopNum.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_LoopNum.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_LoopNum.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_LoopNum.isSameSelect = false;
      this.newComboBox_LoopNum.Location = new Point(556, 630);
      this.newComboBox_LoopNum.Name = "newComboBox_LoopNum";
      this.newComboBox_LoopNum.SelectedIndex = -1;
      this.newComboBox_LoopNum.Size = new Size(249, 23);
      this.newComboBox_LoopNum.TabIndex = 245;
      this.newComboBox_LoopNum.TabStop = false;
      this.newComboBox_LoopNum.TextAlign = HorizontalAlignment.Center;
      this.newComboBox_LoopNum.Value = "";
      this.newComboBox_ValveOption.BackColor = Color.White;
      this.newComboBox_ValveOption.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_ValveOption.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_ValveOption.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_ValveOption.isSameSelect = false;
      this.newComboBox_ValveOption.Location = new Point(556, 608);
      this.newComboBox_ValveOption.Name = "newComboBox_ValveOption";
      this.newComboBox_ValveOption.SelectedIndex = -1;
      this.newComboBox_ValveOption.Size = new Size(249, 23);
      this.newComboBox_ValveOption.TabIndex = 244;
      this.newComboBox_ValveOption.TabStop = false;
      this.newComboBox_ValveOption.TextAlign = HorizontalAlignment.Center;
      this.newComboBox_ValveOption.Value = "";
      this.label_Option.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Option.BorderStyle = BorderStyle.FixedSingle;
      this.label_Option.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Option.ForeColor = Color.MidnightBlue;
      this.label_Option.Location = new Point(310, 589);
      this.label_Option.Name = "label_Option";
      this.label_Option.Size = new Size(495, 20);
      this.label_Option.TabIndex = 239;
      this.label_Option.Text = "옵션";
      this.label_Option.TextAlign = ContentAlignment.MiddleCenter;
      this.label_ValveOption.BackColor = Color.Lavender;
      this.label_ValveOption.BorderStyle = BorderStyle.FixedSingle;
      this.label_ValveOption.Location = new Point(310, 608);
      this.label_ValveOption.Name = "label_ValveOption";
      this.label_ValveOption.Size = new Size(247, 23);
      this.label_ValveOption.TabIndex = 240;
      this.label_ValveOption.Text = "Valve Option";
      this.label_ValveOption.TextAlign = ContentAlignment.MiddleCenter;
      this.label_ClampForceLimit.BackColor = Color.Lavender;
      this.label_ClampForceLimit.BorderStyle = BorderStyle.FixedSingle;
      this.label_ClampForceLimit.Location = new Point(310, 674);
      this.label_ClampForceLimit.Name = "label_ClampForceLimit";
      this.label_ClampForceLimit.Size = new Size(247, 23);
      this.label_ClampForceLimit.TabIndex = 241;
      this.label_ClampForceLimit.Text = "형체력 제한 값";
      this.label_ClampForceLimit.TextAlign = ContentAlignment.MiddleCenter;
      this.label_DeflectionLimit.BackColor = Color.Lavender;
      this.label_DeflectionLimit.BorderStyle = BorderStyle.FixedSingle;
      this.label_DeflectionLimit.Location = new Point(310, 652);
      this.label_DeflectionLimit.Name = "label_DeflectionLimit";
      this.label_DeflectionLimit.Size = new Size(247, 23);
      this.label_DeflectionLimit.TabIndex = 242;
      this.label_DeflectionLimit.Text = "변형 제한 값";
      this.label_DeflectionLimit.TextAlign = ContentAlignment.MiddleCenter;
      this.label_LoopNum.BackColor = Color.Lavender;
      this.label_LoopNum.BorderStyle = BorderStyle.FixedSingle;
      this.label_LoopNum.Location = new Point(310, 630);
      this.label_LoopNum.Name = "label_LoopNum";
      this.label_LoopNum.Size = new Size(247, 23);
      this.label_LoopNum.TabIndex = 243;
      this.label_LoopNum.Text = "최적화 Cycle";
      this.label_LoopNum.TextAlign = ContentAlignment.MiddleCenter;
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(809, 722);
      this.Controls.Add((Control) this.newTextBox_InjectionPressureLimit);
      this.Controls.Add((Control) this.newTextBox_ClampForceLimit);
      this.Controls.Add((Control) this.newTextBox_DeflectionLimit);
      this.Controls.Add((Control) this.label_InjectionPressureLimit);
      this.Controls.Add((Control) this.newComboBox_LoopNum);
      this.Controls.Add((Control) this.newComboBox_ValveOption);
      this.Controls.Add((Control) this.label_Option);
      this.Controls.Add((Control) this.label_ValveOption);
      this.Controls.Add((Control) this.label_ClampForceLimit);
      this.Controls.Add((Control) this.label_DeflectionLimit);
      this.Controls.Add((Control) this.label_LoopNum);
      this.Controls.Add((Control) this.newComboBox_TradeName);
      this.Controls.Add((Control) this.newComboBox_Company);
      this.Controls.Add((Control) this.newButton_LoadMaterial);
      this.Controls.Add((Control) this.newButton_System);
      this.Controls.Add((Control) this.label_AnalysisType);
      this.Controls.Add((Control) this.newComboBox_AnalysisType);
      this.Controls.Add((Control) this.newTextBox_ClampF_Priority);
      this.Controls.Add((Control) this.label_ClampF);
      this.Controls.Add((Control) this.newTextBox_Type);
      this.Controls.Add((Control) this.label_Type);
      this.Controls.Add((Control) this.newTextBox_GateNum_Min);
      this.Controls.Add((Control) this.newTextBox_GateNum_Max);
      this.Controls.Add((Control) this.label_GateNum);
      this.Controls.Add((Control) this.newTextBox_MoldTemp_Min);
      this.Controls.Add((Control) this.newTextBox_MoldTemp_Max);
      this.Controls.Add((Control) this.label_MoldTemp);
      this.Controls.Add((Control) this.newTextBox_MeltTemp_Min);
      this.Controls.Add((Control) this.newTextBox_MeltTemp_Max);
      this.Controls.Add((Control) this.newTextBox_CoolingInletTemp_Min);
      this.Controls.Add((Control) this.newTextBox_PackingNum_Min);
      this.Controls.Add((Control) this.newTextBox_CoolingTime_Min);
      this.Controls.Add((Control) this.newTextBox_VPSwitchOver_Min);
      this.Controls.Add((Control) this.newTextBox_FillTime_Min);
      this.Controls.Add((Control) this.newTextBox_CoolingInletTemp_Max);
      this.Controls.Add((Control) this.label_CoolingInletTemp);
      this.Controls.Add((Control) this.label_CoolingTime);
      this.Controls.Add((Control) this.label_FillTime);
      this.Controls.Add((Control) this.label_MeltTemp);
      this.Controls.Add((Control) this.newTextBox_PackingNum_Max);
      this.Controls.Add((Control) this.newTextBox_CoolingTime_Max);
      this.Controls.Add((Control) this.newTextBox_VPSwitchOver_Max);
      this.Controls.Add((Control) this.newTextBox_FillTime_Max);
      this.Controls.Add((Control) this.label_PackingNum);
      this.Controls.Add((Control) this.label_VPSwitchOver);
      this.Controls.Add((Control) this.newTextBox_Option);
      this.Controls.Add((Control) this.newTextBox_Item);
      this.Controls.Add((Control) this.newTextBox_Company);
      this.Controls.Add((Control) this.label_DB_Opt);
      this.Controls.Add((Control) this.label_DB_Item);
      this.Controls.Add((Control) this.label_DB_Company);
      this.Controls.Add((Control) this.label_Result);
      this.Controls.Add((Control) this.newTextBox_Weight_Priority);
      this.Controls.Add((Control) this.newTextBox_TempAtFlowFront_Priority);
      this.Controls.Add((Control) this.newTextBox_InjPressure_Priority);
      this.Controls.Add((Control) this.newTextBox_Sinkmark_Priority);
      this.Controls.Add((Control) this.newTextBox_CycleTime_Priority);
      this.Controls.Add((Control) this.newTextBox_VolShrinkage_Priority);
      this.Controls.Add((Control) this.newTextBox_Deflection_Priority);
      this.Controls.Add((Control) this.label_InjPressure);
      this.Controls.Add((Control) this.label_CycleTime);
      this.Controls.Add((Control) this.label_Deflection);
      this.Controls.Add((Control) this.label_Weight);
      this.Controls.Add((Control) this.label_TempAtFlowFront);
      this.Controls.Add((Control) this.label_Sinkmark);
      this.Controls.Add((Control) this.label_VolShrinkage);
      this.Controls.Add((Control) this.newTextBox_Density);
      this.Controls.Add((Control) this.label_Material);
      this.Controls.Add((Control) this.label_Density);
      this.Controls.Add((Control) this.label_TradeName);
      this.Controls.Add((Control) this.label_Company);
      this.Controls.Add((Control) this.label_Data);
      this.Controls.Add((Control) this.newButton_Export);
      this.Controls.Add((Control) this.newButton_Import);
      this.Controls.Add((Control) this.newButton_Del);
      this.Controls.Add((Control) this.newButton_Edit);
      this.Controls.Add((Control) this.newButton_Add);
      this.Controls.Add((Control) this.listBox_DB);
      this.Controls.Add((Control) this.label_DB_List);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmInputDB);
      this.ShowInTaskbar = false;
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "Input Data DB";
      this.Load += new EventHandler(this.frmInputDB_Load);
      this.KeyDown += new KeyEventHandler(this.frmInputDB_KeyDown);
      this.ResumeLayout(false);
    }
  }
}
