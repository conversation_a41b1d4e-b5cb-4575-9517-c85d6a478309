﻿// Decompiled with JetBrains decompiler
// Type: HDMFSummary.clsSummaryExcel
// Assembly: HDMFSummary, Version=2.3.0.0, Culture=neutral, PublicKeyToken=null
// MVID: ********-3B9F-45E9-9723-0DEF72C057B2
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFSummary.dll

using HDLog4Net;
using HDMoldFlowLibrary;
using Microsoft.CSharp.RuntimeBinder;
using Microsoft.Office.Interop.Excel;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Text.RegularExpressions;

namespace HDMFSummary
{
  internal class clsSummaryExcel
  {
    internal static void ExportSummary(DataRow[] p_arr_drStudy, string p_strSummaryName)
    {
      int num1 = 0;
      bool flag1 = false;
      uint lpdwProcessId = 0;
      DataSet dataSet = (DataSet) null;
      List<string> stringList = new List<string>();
      try
      {
        FileInfo fileInfo1 = new FileInfo(clsSummaryDefine.g_fiProject.Directory.Parent.FullName + "\\" + clsSummaryDefine.g_fiProject.Directory.Name + "_Export\\" + Path.GetFileNameWithoutExtension(clsSummaryDefine.g_fiProject.FullName) + "_" + p_strSummaryName + "." + clsSummaryDefine.g_dicExtension["Excel"]);
        FileInfo fileInfo2 = new FileInfo(clsSummaryDefine.g_diTemplate?.ToString() + "\\" + p_strSummaryName + ".hde");
        if (!fileInfo2.Exists)
          return;
        FileInfo fileInfo3 = new FileInfo(clsSummaryDefine.g_diTemplate?.ToString() + "\\" + p_strSummaryName + ".xlsx");
        if (!fileInfo3.Exists)
        {
          fileInfo2.CopyTo(fileInfo3.FullName);
          fileInfo3.Refresh();
          if (!fileInfo3.Exists)
            return;
        }
        if (!fileInfo1.Directory.Exists)
          fileInfo1.Directory.Create();
        if (fileInfo1.Exists)
        {
          if (!fileInfo1.Extension.Contains("xl"))
          {
            fileInfo3 = new FileInfo(fileInfo1.FullName.Replace(fileInfo1.Extension, ".xlsx"));
            fileInfo1.CopyTo(fileInfo3.FullName);
          }
          else
            fileInfo3 = new FileInfo(fileInfo1.FullName);
        }
        fileInfo3.Refresh();
        if (fileInfo3.Exists)
        {
          // ISSUE: variable of a compiler-generated type
          Application instance = (Application) Activator.CreateInstance(Marshal.GetTypeFromCLSID(new Guid("00024500-0000-0000-C000-000000000046")));
          instance.Visible = false;
          // ISSUE: variable of a compiler-generated type
          Application p_obj = instance;
          try
          {
            // ISSUE: reference to a compiler-generated method
            p_obj.Workbooks.Open(fileInfo3.FullName, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing);
            if (p_obj.Sheets.Count > 1)
            {
              dataSet = new DataSet();
              foreach (Worksheet sheet in p_obj.Sheets)
              {
                if (!(sheet.Name == "Sheet1"))
                {
                  DataTable workSheetData = clsSummaryUtill.GetWorkSheetData(sheet);
                  if (workSheetData != null)
                    dataSet.Tables.Add(workSheetData);
                }
              }
            }
          }
          catch (Exception ex)
          {
            HDLog.Instance().Error((object) ("Exception([HDMFSummary][clsSummaryExcel]ExportSummary(1)):" + ex.Message));
          }
          int windowThreadProcessId = (int) clsHDMFLibUtil.GetWindowThreadProcessId(new IntPtr(p_obj.Hwnd), ref lpdwProcessId);
          // ISSUE: reference to a compiler-generated method
          p_obj.Quit();
          clsHDMFLib.ReleaseComObject((object) p_obj);
          if (lpdwProcessId != 0U)
          {
            Process processById = Process.GetProcessById((int) lpdwProcessId);
            processById.CloseMainWindow();
            processById.Refresh();
            processById.Kill();
          }
        }
        if (dataSet != null && dataSet.Tables.Count == 0)
          dataSet = (DataSet) null;
        // ISSUE: variable of a compiler-generated type
        Application instance1 = (Application) Activator.CreateInstance(Marshal.GetTypeFromCLSID(new Guid("00024500-0000-0000-C000-000000000046")));
        instance1.Visible = false;
        // ISSUE: variable of a compiler-generated type
        Application p_obj1 = instance1;
        try
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          Workbook workbook = p_obj1.Workbooks.Open(fileInfo3.FullName, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing);
          // ISSUE: variable of a compiler-generated type
          Worksheet worksheet = workbook.Sheets.Cast<Worksheet>().Where<Worksheet>((System.Func<Worksheet, bool>) (Temp => Temp.Name == "Sheet1")).FirstOrDefault<Worksheet>();
          if (worksheet != null)
          {
            foreach (DataRow dataRow in p_arr_drStudy)
            {
              DataRow drTmpStudy = dataRow;
              if (!workbook.Sheets.Cast<Worksheet>().Any<Worksheet>((System.Func<Worksheet, bool>) (Temp => Temp.Name == drTmpStudy.Table.TableName)))
              {
                // ISSUE: reference to a compiler-generated field
                if (clsSummaryExcel.\u003C\u003Eo__0.\u003C\u003Ep__0 == null)
                {
                  // ISSUE: reference to a compiler-generated field
                  clsSummaryExcel.\u003C\u003Eo__0.\u003C\u003Ep__0 = CallSite<System.Action<CallSite, Worksheet, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.ResultDiscarded, "Copy", (IEnumerable<Type>) null, typeof (clsSummaryExcel), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[3]
                  {
                    CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType, (string) null),
                    CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType, (string) null),
                    CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
                  }));
                }
                // ISSUE: reference to a compiler-generated field
                // ISSUE: reference to a compiler-generated field
                clsSummaryExcel.\u003C\u003Eo__0.\u003C\u003Ep__0.Target((CallSite) clsSummaryExcel.\u003C\u003Eo__0.\u003C\u003Ep__0, worksheet, Type.Missing, workbook.Sheets[(object) workbook.Sheets.Count]);
                // ISSUE: reference to a compiler-generated field
                if (clsSummaryExcel.\u003C\u003Eo__0.\u003C\u003Ep__1 == null)
                {
                  // ISSUE: reference to a compiler-generated field
                  clsSummaryExcel.\u003C\u003Eo__0.\u003C\u003Ep__1 = CallSite<Func<CallSite, object, Worksheet>>.Create(Binder.Convert(CSharpBinderFlags.ConvertExplicit, typeof (Worksheet), typeof (clsSummaryExcel)));
                }
                // ISSUE: reference to a compiler-generated field
                // ISSUE: reference to a compiler-generated field
                clsSummaryExcel.\u003C\u003Eo__0.\u003C\u003Ep__1.Target((CallSite) clsSummaryExcel.\u003C\u003Eo__0.\u003C\u003Ep__1, workbook.Sheets[(object) workbook.Sheets.Count]).Name = drTmpStudy.Table.TableName;
              }
            }
            DataTable dtWSExcel = (DataTable) null;
            foreach (Worksheet sheet in workbook.Sheets)
            {
              // ISSUE: variable of a compiler-generated type
              Worksheet wsTmp = sheet;
              if (!(wsTmp.Name == "Sheet1"))
              {
                DataRow[] array = ((IEnumerable<DataRow>) p_arr_drStudy).Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp.Table.TableName == wsTmp.Name)).ToArray<DataRow>();
                if (array.Length != 0)
                {
                  dtWSExcel = dataSet == null ? clsSummaryUtill.GetWorkSheetData(wsTmp) : dataSet.Tables.Cast<DataTable>().Where<DataTable>((System.Func<DataTable, bool>) (Temp => Temp.TableName == wsTmp.Name)).FirstOrDefault<DataTable>();
                  foreach (DataRow dataRow1 in array)
                  {
                    Dictionary<string, string> source = JsonConvert.DeserializeObject<Dictionary<string, string>>(dataRow1["Summary"].ToString());
                    bool flag2 = true;
                    int num2 = 0;
                    if (dtWSExcel != null && dtWSExcel.Rows.Count != 0)
                    {
                      foreach (DataRow dataRow2 in dtWSExcel.AsEnumerable())
                      {
                        flag2 = true;
                        foreach (DataColumn column in (InternalDataCollectionBase) dtWSExcel.Columns)
                        {
                          DataColumn dcWSExcel = column;
                          string input1 = "";
                          if (dataRow2[dcWSExcel.ColumnName] != DBNull.Value)
                            input1 = dataRow2[dcWSExcel.ColumnName].ToString();
                          string input2 = "";
                          if (source.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == dcWSExcel.ColumnName)))
                            input2 = source[dcWSExcel.ColumnName];
                          if (input1 != input2)
                          {
                            if (dcWSExcel.ColumnName == "Study")
                            {
                              flag2 = false;
                              break;
                            }
                            double num3 = clsHDMFLibUtil.ConvertToDouble(Regex.Match(input1, "([-+]?[0-9]+[\\.,]?[0-9]*)").Groups[0].Value);
                            string str1 = num3.ToString();
                            num3 = clsHDMFLibUtil.ConvertToDouble(Regex.Match(input2, "([-+]?[0-9]+[\\.,]?[0-9]*)").Groups[0].Value);
                            string str2 = num3.ToString();
                            if (str1 != str2)
                            {
                              flag2 = false;
                              break;
                            }
                          }
                        }
                      }
                      if (flag2)
                      {
                        num1 = num2 + 1;
                        break;
                      }
                    }
                    if (num2 <= 0)
                    {
                      int RowIndex = dtWSExcel.Rows.Count + 3;
                      dtWSExcel.Rows.Add();
                      string empty1 = string.Empty;
                      string empty2 = string.Empty;
                      for (int i = 0; i < dtWSExcel.Columns.Count; i++)
                      {
                        if (source.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == dtWSExcel.Columns[i].ColumnName)) || dtWSExcel.Columns[i].ColumnName == "Number")
                        {
                          int columnIndex = clsSummaryUtill.GetColumnIndex(wsTmp, dtWSExcel.Columns[i].ColumnName);
                          if (columnIndex >= 0)
                          {
                            string str = !(dtWSExcel.Columns[i].ColumnName == "Number") ? source[dtWSExcel.Columns[i].ColumnName] : (RowIndex - 2).ToString();
                            if (str.Contains("|"))
                            {
                              string[] strArray = str.Split('|');
                              str = strArray[0];
                              empty2 = strArray[1];
                            }
                            wsTmp.Cells[(object) RowIndex, (object) columnIndex] = (object) str;
                            if (empty2 != string.Empty)
                            {
                              wsTmp.Cells[(object) RowIndex, (object) (columnIndex + 1)] = (object) empty2;
                              empty2 = string.Empty;
                            }
                          }
                        }
                      }
                      flag1 = true;
                    }
                  }
                }
              }
            }
          }
          if (flag1)
          {
            if (fileInfo1.Extension == ".xlsm")
            {
              if (fileInfo3.FullName != fileInfo2.FullName.Replace(fileInfo2.Extension, ".xlsm"))
              {
                // ISSUE: reference to a compiler-generated method
                workbook.Save();
              }
              else
              {
                // ISSUE: reference to a compiler-generated method
                workbook.SaveAs((object) fileInfo1.FullName, (object) XlFileFormat.xlOpenXMLWorkbookMacroEnabled, Type.Missing, Type.Missing, Type.Missing, Type.Missing, ConflictResolution: Type.Missing, AddToMru: Type.Missing, TextCodepage: Type.Missing, TextVisualLayout: Type.Missing, Local: Type.Missing);
              }
            }
            else if (fileInfo3.FullName != fileInfo2.FullName.Replace(fileInfo2.Extension, ".xlsx"))
            {
              // ISSUE: reference to a compiler-generated method
              workbook.Save();
            }
            else
            {
              // ISSUE: reference to a compiler-generated method
              workbook.SaveAs((object) fileInfo1.FullName, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, ConflictResolution: Type.Missing, AddToMru: Type.Missing, TextCodepage: Type.Missing, TextVisualLayout: Type.Missing, Local: Type.Missing);
            }
          }
          // ISSUE: reference to a compiler-generated method
          workbook.Close(Type.Missing, Type.Missing, Type.Missing);
        }
        catch (Exception ex)
        {
          HDLog.Instance().Error((object) ("Exception([HDMFSummary][clsSummary_AI]ExportSummary(2)):" + ex.Message));
        }
        int windowThreadProcessId1 = (int) clsHDMFLibUtil.GetWindowThreadProcessId(new IntPtr(p_obj1.Hwnd), ref lpdwProcessId);
        // ISSUE: reference to a compiler-generated method
        p_obj1.Quit();
        clsHDMFLib.ReleaseComObject((object) p_obj1);
        if (lpdwProcessId != 0U)
        {
          Process processById = Process.GetProcessById((int) lpdwProcessId);
          processById.CloseMainWindow();
          processById.Refresh();
          processById.Kill();
        }
        if (fileInfo1.Extension.Contains(".xl"))
          return;
        if (flag1 && fileInfo3.FullName != fileInfo2.FullName.Replace(fileInfo2.Extension, ".xlsx"))
        {
          if (fileInfo1.Exists)
            fileInfo1.Delete();
          File.Move(fileInfo3.FullName, fileInfo1.FullName);
        }
        if (!fileInfo3.Exists)
          return;
        fileInfo3.Delete();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFSummary][clsSummary_Excel]ExportSummary):" + ex.Message));
      }
    }
  }
}
