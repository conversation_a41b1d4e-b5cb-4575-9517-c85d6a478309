'%RunPerInstance
'@Note: El script/macro se proporciona 'tal cual' y no se hace responsable de ningún problema causado.
'@
'@ DESCRIPCIÓN
'@ Crea un gráfico de usuario de espesor que funciona para todos los tipos de malla
'@
'@ SINTAXIS
'@ ThicknessPlot
'@
'@ NOTAS
'@ Fecha       Comentario
'@ 2023-05-15  Versión simplificada basada en el script original de Berndt Nordh

SetLocale("en-us")

' Inicializar Synergy
Dim SynergyGetter, Synergy
On Error Resume Next
Set SynergyGetter = GetObject(CreateObject("WScript.Shell").ExpandEnvironmentStrings("%SAInstance%"))
On Error GoTo 0
If (Not IsEmpty(SynergyGetter)) Then
	Set Synergy = SynergyGetter.GetSASynergy
Else
	Set Synergy = CreateObject("synergy.Synergy")
End If

' Configurar unidades y obtener documento de estudio
Synergy.SetUnits "Metric"
Set StudyDoc = Synergy.StudyDoc()
CurrentMeshType = StudyDoc.MeshType

' Determinar el nombre del gráfico según el tipo de malla
If (CurrentMeshType = "Fusion") or (CurrentMeshType = "Midplane") Then
  PlotName = "Thickness"
Else
  PlotName = "Dimension 3D"
End If

' Obtener datos de espesor
Set DiagnosisManager = Synergy.DiagnosisManager()
Set IntArr = Synergy.CreateIntegerArray()
Set DoubleArr = Synergy.CreateDoubleArray()

' Usar el método apropiado según el tipo de malla
On Error Resume Next
If CurrentMeshType = "3D" Then
  ' Para mallas 3D, usar GetThicknessDiagnosis2
  DiagnosisManager.GetThicknessDiagnosis2 0.0, 1000.0, True, IntArr, DoubleArr
  If Err.Number <> 0 Then
    Err.Clear
    ' Intentar con GetThicknessDiagnosis como alternativa
    DiagnosisManager.GetThicknessDiagnosis 0.0, 1000.0, IntArr, DoubleArr
  End If
Else
  ' Para mallas 2D (Midplane, Fusion), usar GetThicknessDiagnosis
  DiagnosisManager.GetThicknessDiagnosis 0.0, 0.0, IntArr, DoubleArr
End If
On Error GoTo 0

' Crear gráfico de usuario
Set PlotMgr = Synergy.PlotManager()

' Eliminar gráfico existente con el mismo nombre si existe
Set Plot = PlotMgr.FindPlotByName(PlotName)
If Not Plot Is Nothing Then
  PlotMgr.DeletePlot(Plot)
End If

' Crear nuevo gráfico
Set ThicknessPlot = PlotMgr.CreateUserPlot()
ThicknessPlot.SetDataType "ELDT"
ThicknessPlot.SetName PlotName

' Añadir datos escalares
On Error Resume Next
ThicknessPlot.AddScalarData 0.0, IntArr, DoubleArr
If Err.Number <> 0 Then
  MsgBox "Error al añadir datos de espesor: " & Err.Description
  WScript.Quit
End If
On Error GoTo 0

' Construir el gráfico
ThicknessPlot.Build

' Configurar propiedades de visualización
Set Viewer = Synergy.Viewer()
Set Plot = Viewer.GetActivePlot()
If Not Plot Is Nothing Then
  ' Configurar método de visualización y promediado
  Plot.SetNodalAveraging False    ' Desactivar promedio nodal para mejor precisión
  Plot.SetSmoothShading True      ' Activar sombreado suave para mejor visualización
  Plot.SetEdgeDisplay 1           ' Mostrar bordes de elementos
  Plot.SetScaleOption 2          ' Escala manual para mejor control
  Plot.SetExtendedColor True      ' Usar paleta de colores extendida
  
  ' Establecer rango de valores para mejor visualización
  If CurrentMeshType = "3D" Then
    Plot.SetMinValue 0.1          ' Valor mínimo para mallas 3D
    Plot.SetMaxValue 10           ' Valor máximo para mallas 3D
  Else
    Plot.SetMinValue 0            ' Valor mínimo para mallas 2D
    Plot.SetMaxValue 5            ' Valor máximo para mallas 2D
  End If
  
  Plot.SetPlotMethod 2           ' 2 = Sombreado (mejor para visualización de espesor)
  Plot.Regenerate
  
  ' Mostrar mensaje de éxito con información del tipo de malla
  MsgBox "Gráfico de espesor creado correctamente para malla tipo: " & CurrentMeshType
Else
  MsgBox "No se pudo crear el gráfico de espesor. Verifique que el modelo tenga una malla válida."
End If