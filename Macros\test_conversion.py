#!/usr/bin/env python3
"""
Test script for the 3D Surface Sink Mark Depth Calculator Python conversion.

This script performs basic validation of the converted modules without requiring
a full Moldflow Synergy environment.

Author: AI Assistant
Version: 1.0
"""

import sys
import os
import tempfile
import logging
from pathlib import Path

# Configure logging for testing
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_imports():
    """Test that all required modules can be imported."""
    print("Testing module imports...")

    try:
        # Test basic Python modules
        import numpy as np
        print("✓ numpy imported successfully")

        # Test Windows-specific modules (may fail on non-Windows)
        try:
            import win32com.client
            print("✓ win32com.client imported successfully")
            win32_available = True
        except ImportError:
            print("⚠ win32com.client not available (Windows required)")
            win32_available = False

        # Test our custom modules
        from moldflow_mesh_utils import Node, Tet, MeshProcessor
        print("✓ moldflow_mesh_utils imported successfully")

        # Only test Synergy wrapper if win32com is available
        if win32_available:
            from moldflow_synergy_wrapper import SynergyWrapper, SynergyError
            print("✓ moldflow_synergy_wrapper imported successfully")
        else:
            print("⚠ moldflow_synergy_wrapper skipped (requires win32com)")

        # Test main module (may fail due to missing dependencies)
        if win32_available:
            try:
                import importlib.util
                spec = importlib.util.spec_from_file_location(
                    "sink_mark_calculator",
                    "3DSurfaceSinkMarkDepth.py"
                )
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                print("✓ 3DSurfaceSinkMarkDepth imported successfully")
            except Exception as e:
                print(f"⚠ 3DSurfaceSinkMarkDepth import failed: {e}")
        else:
            print("⚠ 3DSurfaceSinkMarkDepth skipped (requires win32com)")

        return True

    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False


def test_node_class():
    """Test the Node class functionality."""
    print("\nTesting Node class...")

    try:
        from moldflow_mesh_utils import Node

        # Create a node
        node = Node()
        assert node.label == 0
        assert len(node.coord) == 3
        assert node.number_of_connected_elems == 0

        # Test reading from a UDM line (format should have exactly 9 words)
        test_line = "NODE{ 1 0 0 0 0 1.0 2.0 3.0 }"
        success = node.read_from_line(test_line)

        # Debug the parsing first
        words = node._udm_split(test_line)
        print(f"Debug: Words parsed: {words}, length: {len(words)}")

        if success:
            print(f"Debug: node.label={node.label}, coord={node.coord}")
            assert node.label == 1
            assert abs(node.coord[0] - 1000.0) < 1e-6  # Converted to mm
            assert abs(node.coord[1] - 2000.0) < 1e-6
            assert abs(node.coord[2] - 3000.0) < 1e-6
            print("✓ Node class tests passed")
            return True
        else:
            print(f"✗ Node reading failed.")
            return False

    except Exception as e:
        print(f"✗ Node class test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_tet_class():
    """Test the Tet class functionality."""
    print("\nTesting Tet class...")

    try:
        from moldflow_mesh_utils import Tet

        # Create a tetrahedron
        tet = Tet()
        assert tet.label == 0
        assert len(tet.node_labels) == 4
        assert tet.surface_face == -1

        # Test reading from a UDM line (format should have exactly 13 words)
        test_line = "TET4{ 1 0 0 0 0 0 50400 1 10 20 30 40 }"
        success = tet.read_from_line(test_line)

        # Debug the parsing first
        words = tet._udm_split(test_line)
        print(f"Debug: Words parsed: {words}, length: {len(words)}")

        if success:
            print(f"Debug: tet.label={tet.label}, t_set={tet.t_set}, t_set_sub_id={tet.t_set_sub_id}, node_labels={tet.node_labels}")
            assert tet.label == 1
            assert tet.t_set == 50400
            assert tet.t_set_sub_id == 1
            # Node labels should be sorted
            assert tet.node_labels == [10, 20, 30, 40]
            print("✓ Tet class tests passed")
            return True
        else:
            print(f"✗ Tet reading failed.")
            return False

    except Exception as e:
        print(f"✗ Tet class test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_mesh_processor():
    """Test the MeshProcessor class with a simple UDM file."""
    print("\nTesting MeshProcessor class...")

    try:
        from moldflow_mesh_utils import MeshProcessor

        # Create a simple test UDM file
        udm_content = """  NOND{ 4 }
  NOT4{ 2 }
NODE{ 1 0 0 0 0 0.0 0.0 0.0 }
NODE{ 2 0 0 0 0 1.0 0.0 0.0 }
NODE{ 3 0 0 0 0 0.0 1.0 0.0 }
NODE{ 4 0 0 0 0 0.0 0.0 1.0 }
TET4{ 1 0 0 0 0 0 50400 1 1 2 3 4 }
TET4{ 2 0 0 0 0 0 50400 1 1 2 3 4 }
"""

        # Write to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.udm', delete=False) as f:
            f.write(udm_content)
            temp_file = f.name

        try:
            # Test mesh processing
            processor = MeshProcessor()
            success = processor.read_udm_file(temp_file)

            if success:
                print(f"Debug: num_tets={processor.get_num_tets()}, highest_node_label={processor.get_highest_node_label()}")
                assert processor.get_num_tets() == 2
                assert processor.get_highest_node_label() == 4
                print("✓ MeshProcessor tests passed")
                return True
            else:
                print("✗ MeshProcessor reading failed")
                return False

        finally:
            # Clean up
            os.unlink(temp_file)

    except Exception as e:
        print(f"✗ MeshProcessor test failed: {e}")
        return False


def test_synergy_wrapper():
    """Test the SynergyWrapper class (without actual Synergy connection)."""
    print("\nTesting SynergyWrapper class...")

    try:
        # Check if win32com is available
        try:
            import win32com.client
        except ImportError:
            print("⚠ SynergyWrapper test skipped (requires win32com on Windows)")
            return True

        from moldflow_synergy_wrapper import SynergyWrapper, SynergyError

        # Create wrapper instance
        wrapper = SynergyWrapper()
        assert not wrapper.is_connected()

        # Test build code detection
        build_code = wrapper._get_build_code()
        assert build_code in ["Synergy", "Scandium"]

        print("✓ SynergyWrapper basic tests passed")
        print("⚠ Full Synergy connection test requires Moldflow Synergy")
        return True

    except Exception as e:
        print(f"✗ SynergyWrapper test failed: {e}")
        return False


def run_all_tests():
    """Run all test functions."""
    print("=" * 60)
    print("3D Surface Sink Mark Depth Calculator - Test Suite")
    print("=" * 60)

    tests = [
        test_imports,
        test_node_class,
        test_tet_class,
        test_mesh_processor,
        test_synergy_wrapper,
    ]

    passed = 0
    total = len(tests)

    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test_func.__name__} failed with exception: {e}")

    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed!")
        return True
    else:
        print("⚠ Some tests failed - check output above")
        return False


def main():
    """Main test entry point."""
    success = run_all_tests()

    if not success:
        sys.exit(1)

    print("\nTest suite completed successfully!")
    print("\nNext steps:")
    print("1. Install dependencies: pip install -r requirements.txt")
    print("2. Open a 3D study in Moldflow Synergy")
    print("3. Run: python 3DSurfaceSinkMarkDepth.py")


if __name__ == "__main__":
    main()
