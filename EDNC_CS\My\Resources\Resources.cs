﻿// Decompiled with JetBrains decompiler
// Type: Moldflow_Esay_Tool_Kit.My.Resources.Resources
// Assembly: Moldflow Esay Tool Kit, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: EE4F1197-F36A-4D67-AE33-DA541A327629
// Assembly location: C:\Users\<USER>\Documents\20210315_Moldflow Esay Tool Kit_V3.exe

using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;

#nullable disable
namespace Moldflow_Esay_Tool_Kit.My.Resources
{
  [GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
  [StandardModule]
  [HideModuleName]
  [CompilerGenerated]
  [DebuggerNonUserCode]
  internal sealed class Resources
  {
    private static ResourceManager resourceMan;
    private static CultureInfo resourceCulture;

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    internal static ResourceManager ResourceManager
    {
      get
      {
        if (object.ReferenceEquals((object) Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceMan, (object) null))
          Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceMan = new ResourceManager("Moldflow_Esay_Tool_Kit.Resources", typeof (Moldflow_Esay_Tool_Kit.My.Resources.Resources).Assembly);
        return Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceMan;
      }
    }

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    internal static CultureInfo Culture
    {
      get => Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture;
      set => Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture = value;
    }

    internal static Bitmap Cold_Runner
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Cold_Runner), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Cold_Runner_Manifold_Type_Label
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Cold_Runner_Manifold_Type_Label), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Cold_Runner_Trapezoidal
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Cold_Runner_Trapezoidal), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Hot_Runner
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Hot_Runner), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Hot_Runner_Annular
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Hot_Runner_Annular), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Hot_Runner_Drop_Type_Label
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Hot_Runner_Drop_Type_Label), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap logo_투명
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (logo_투명), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Process_Setting
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Process_Setting), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Process_Setting_1
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Process_Setting_1), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Process_Setting_2
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Process_Setting_2), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Process_Setting_3
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Process_Setting_3), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Process_Setting_4
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Process_Setting_4), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Process_Setting_5
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Process_Setting_5), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Process_Setting_6
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Process_Setting_6), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Process_Setting_7
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Process_Setting_7), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Process_Setting_8
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Process_Setting_8), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Process_Setting_Start_Button_Image
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Process_Setting_Start_Button_Image), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Process_Setting_Tap_Warp_Menu_CE
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Process_Setting_Tap_Warp_Menu_CE), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Process_Setting_Tap_Warp_Menu_IC
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Process_Setting_Tap_Warp_Menu_IC), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Process_Setting_Tap_Warp_Menu_MA
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Process_Setting_Tap_Warp_Menu_MA), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Process_Setting_Tap_Warp_Menu_ME
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Process_Setting_Tap_Warp_Menu_ME), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Process_Setting_Tap_Warp_Menu_MIR
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Process_Setting_Tap_Warp_Menu_MIR), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Process_Setting_Tap_Warp_Menu_MIRU
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Process_Setting_Tap_Warp_Menu_MIRU), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Process_Setting_Tap_Warp_Menu_MT
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Process_Setting_Tap_Warp_Menu_MT), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Process_Setting_Tap_Warp_Menu_SD
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Process_Setting_Tap_Warp_Menu_SD), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Process_Setting_Tap_Warp_Menu_SDU
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Process_Setting_Tap_Warp_Menu_SDU), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Result
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Result), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Result2
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Result2), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Result3
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Result3), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Result4
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Result4), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Result5
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Result5), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }

    internal static Bitmap Runner_Type_Label
    {
      get
      {
        return (Bitmap) RuntimeHelpers.GetObjectValue(Moldflow_Esay_Tool_Kit.My.Resources.Resources.ResourceManager.GetObject(nameof (Runner_Type_Label), Moldflow_Esay_Tool_Kit.My.Resources.Resources.resourceCulture));
      }
    }
  }
}
