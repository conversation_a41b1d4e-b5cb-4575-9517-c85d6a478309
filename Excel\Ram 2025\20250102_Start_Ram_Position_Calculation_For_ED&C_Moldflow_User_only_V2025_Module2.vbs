Attribute VB_Name = "Module2"

Sub import_shot_Moldflow()



Dim SynergyGetter, Synergy

On Error Resume Next

Set SynergyGetter = GetObject(CreateObject("WScript.Shell").ExpandEnvironmentStrings("%SAInstance%"))

On Error GoTo 0

If (Not IsEmpty(SynergyGetter)) Then

    Set Synergy = SynergyGetter.GetSASynergy

Else

    Set Synergy = CreateObject("synergy.Synergy")

End If

Synergy.SetUnits "Metric"

Set PropEd = Synergy.PropertyEditor()



Set Prop = PropEd.FindProperty(30007, 1)

Set DVec = Synergy.CreateDoubleArray()

DVec.AddDouble Sheet2.Cells(7, "F").Value

Prop.FieldValues 10005, DVec

Set DVec = Synergy.CreateDoubleArray()

DVec.AddDouble Sheet2.Cells(5, "F").Value

Prop.FieldValues 10008, DVec

PropEd.CommitChanges "Process Conditions"



Set Prop = PropEd.FindProperty(30011, 1)

Set DVec = Synergy.CreateDoubleArray()

DVec.AddDouble 6

Prop.FieldValues 10109, DVec

Set DVec = Synergy.CreateDoubleArray()

DVec.AddDouble 3

Prop.FieldValues 10603, DVec

Set DVec = Synergy.CreateDoubleArray()

DVec.AddDouble Sheet2.Cells(6, "G").Value

DVec.AddDouble Sheet2.Cells(17, "E").Value

Prop.FieldValues 10306, DVec

Set DVec = Synergy.CreateDoubleArray()

Prop.FieldValues 10604, DVec

Set DVec = Synergy.CreateDoubleArray()

DVec.AddDouble 8

Prop.FieldValues 10310, DVec

Set DVec = Synergy.CreateDoubleArray()

DVec.AddDouble Sheet2.Cells(8, "L")

Prop.FieldValues 10313, DVec

Set DVec = Synergy.CreateDoubleArray()

For i = 17 To 42

    If Sheet2.Cells(i, "H") = "" Then Exit For

    DVec.AddDouble Sheet2.Cells(i, "H")

    DVec.AddDouble Sheet2.Cells(i, "I")

Next i

Prop.FieldValues 10604, DVec



PropEd.CommitChanges "Process Conditions"

    

End Sub













