﻿// Decompiled with JetBrains decompiler
// Type: HDMFUserControl.NewButton
// Assembly: HDMFUserControl, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 4651D75A-87CE-415F-80A5-EBC4E2EC2106
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFUserControl.dll

using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace HDMFUserControl
{
  public class NewButton : UserControl
  {
    private IContainer components;
    private Button button_Main;

    protected override CreateParams CreateParams
    {
      get
      {
        CreateParams createParams = base.CreateParams;
        createParams.ExStyle |= 32;
        return createParams;
      }
    }

    public NewButton()
    {
      this.InitializeComponent();
      this.button_Main.GotFocus += new EventHandler(this.Button_Main_GotFocus);
      this.button_Main.Click += new EventHandler(this.Button_Main_Click);
      this.button_Main.MouseUp += new MouseEventHandler(this.Button_Main_MouseUp);
    }

    private void Button_Main_GotFocus(object sender, EventArgs e) => this.ActiveControl = (Control) null;

    public event EventHandler NewClick;

    private void Button_Main_Click(object sender, EventArgs e)
    {
      if (this.NewClick == null)
        return;
      this.NewClick((object) this, e);
    }

    public event MouseEventHandler NewMouseUp;

    private void Button_Main_MouseUp(object sender, MouseEventArgs e)
    {
      if (this.NewMouseUp == null)
        return;
      this.NewMouseUp((object) this, e);
    }

    public Image Image
    {
      get => this.button_Main.Image;
      set => this.button_Main.Image = value;
    }

    public FlatStyle FlatStyle
    {
      get => this.button_Main.FlatStyle;
      set => this.button_Main.FlatStyle = value;
    }

    public Color ButtonBackColor
    {
      get => this.button_Main.BackColor;
      set => this.button_Main.BackColor = value;
    }

    public string ButtonText
    {
      get => this.button_Main.Text;
      set => this.button_Main.Text = value;
    }

    public TextImageRelation TextImageRelocation
    {
      get => this.button_Main.TextImageRelation;
      set => this.button_Main.TextImageRelation = value;
    }

    public ContentAlignment TextAlign
    {
      get => this.button_Main.TextAlign;
      set => this.button_Main.TextAlign = value;
    }

    public ContentAlignment ImageAlign
    {
      get => this.button_Main.ImageAlign;
      set => this.button_Main.ImageAlign = value;
    }

    public int FlatBorderSize
    {
      get => this.button_Main.FlatAppearance.BorderSize;
      set => this.button_Main.FlatAppearance.BorderSize = value;
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.button_Main = new Button();
      this.SuspendLayout();
      this.button_Main.BackColor = Color.White;
      this.button_Main.Dock = DockStyle.Fill;
      this.button_Main.FlatAppearance.BorderColor = Color.Gray;
      this.button_Main.FlatAppearance.MouseDownBackColor = Color.LightGray;
      this.button_Main.FlatStyle = FlatStyle.Flat;
      this.button_Main.Location = new Point(0, 0);
      this.button_Main.Name = "button_Main";
      this.button_Main.Size = new Size(75, 23);
      this.button_Main.TabIndex = 0;
      this.button_Main.TabStop = false;
      this.button_Main.Text = "button1";
      this.button_Main.UseVisualStyleBackColor = false;
      this.AutoScaleMode = AutoScaleMode.None;
      this.Controls.Add((Control) this.button_Main);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.Name = nameof (NewButton);
      this.Size = new Size(75, 23);
      this.ResumeLayout(false);
    }
  }
}
