﻿// Decompiled with JetBrains decompiler
// Type: Microsoft.Office.Interop.PowerPoint._Presentation
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using Microsoft.Office.Core;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

namespace Microsoft.Office.Interop.PowerPoint
{
  [CompilerGenerated]
  [Guid("9149349D-5A91-11CF-8700-00AA0060263B")]
  [TypeIdentifier]
  [ComImport]
  public interface _Presentation
  {
    [SpecialName]
    [MethodImpl(MethodCodeType = MethodCodeType.Runtime)]
    sealed extern void _VtblGap1_10();

    [DispId(2011)]
    Slides Slides { [DispId(2011), MethodImpl(MethodImplOptions.InternalCall, MethodCodeType = MethodCodeType.Runtime)] [return: MarshalAs(UnmanagedType.Interface)] get; }

    [SpecialName]
    [MethodImpl(MethodCodeType = MethodCodeType.Runtime)]
    sealed extern void _VtblGap2_26();

    [DispId(2036)]
    [MethodImpl(MethodImplOptions.InternalCall, MethodCodeType = MethodCodeType.Runtime)]
    void SaveAs([MarshalAs(UnmanagedType.BStr), In] string FileName, [In] PpSaveAsFileType FileFormat = PpSaveAsFileType.ppSaveAsDefault, [In] MsoTriState EmbedTrueTypeFonts = MsoTriState.msoTriStateMixed);

    [SpecialName]
    [MethodImpl(MethodCodeType = MethodCodeType.Runtime)]
    sealed extern void _VtblGap3_2();

    [DispId(2039)]
    [MethodImpl(MethodImplOptions.InternalCall, MethodCodeType = MethodCodeType.Runtime)]
    void Close();
  }
}
