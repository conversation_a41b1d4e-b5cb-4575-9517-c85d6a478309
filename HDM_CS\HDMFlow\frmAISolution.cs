﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmAISolution
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDLog4Net;
using HDMFUserControl;
using HDMoldFlow.Properties;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public class frmAISolution : Form
  {
    private const int WS_EX_COMPOSITED = 33554432;
    private IContainer components = (IContainer) null;
    private Label label_Data;
    private Panel panel_Gate;
    private Label label_PackingTime;
    private UnitTextBox unitTextBox_PackingTime1;
    private Label label_VolShrinkage;
    private NewButton newButton_Apply;
    private DataGridView dataGridView_Gate;
    private Label label_FillTime;
    private UnitTextBox unitTextBox_FillTime;
    private UnitTextBox unitTextBox_VPSwitch;
    private Label label_VPSwitch;
    private UnitTextBox unitTextBox_PackingPressure1;
    private UnitTextBox unitTextBox_PackingPressure3;
    private UnitTextBox unitTextBox_PackingTime3;
    private UnitTextBox unitTextBox_PackingPressure2;
    private UnitTextBox unitTextBox_PackingTime2;
    private UnitTextBox unitTextBox_MeltTemp;
    private UnitTextBox unitTextBox_MoldTemp;
    private Label label3;
    private UnitTextBox unitTextBox_CoolingTemp;
    private Label label_CoolingTemp;
    private UnitTextBox unitTextBox_CoolingTime;
    private Label label_CoolingTime;
    private Label label_Company;
    private Label label_GateNum;
    private Label label_TradeName;
    private NewTextBox newTextBox_GateNum;
    private NewTextBox newTextBox_TradeName;
    private NewTextBox newTextBox_Company;
    private NewTextBox newTextBox_Density;
    private Label label_Density;
    private DataGridViewTextBoxColumn Column_X;
    private DataGridViewTextBoxColumn Column_Y;
    private DataGridViewTextBoxColumn Column_Z;
    private DataGridViewTextBoxColumn Column_Open;
    private DataGridViewTextBoxColumn Column_Close;
    private Label label_AIData;
    private Panel panel1;
    private RadioButton radioButton_Option2;
    private RadioButton radioButton_Option1;
    private TabControl tabControl1;
    private TabPage tabPage_ProcessSetting;
    private TabPage tabPage_PredictionResult;
    private Panel panel_TabMain;
    private Panel panel_TabButton;
    private NewButton newButton_PredictionResult;
    private NewButton newButton_ProcessSetting;
    private NewTextBox newTextBox_Sinkmark;
    private NewTextBox newTextBox_CycleTime;
    private NewTextBox newTextBox_InjPressure;
    private NewTextBox newTextBox_Deflection;
    private Label label_VShrinkage;
    private Label label_TempAtFlow;
    private Label label_TimeEjectionTemp;
    private Label label_FilledTime;
    private Label label_Deflection;
    private Label label_CavitySurface;
    private Label label_Sinkmark;
    private Label label_InjPressure;
    private Label label_CycleTime;
    private NewTextBox newTextBox_ZComponentMax;
    private NewTextBox newTextBox_ZComponentMin;
    private NewTextBox newTextBox_YComponentMax;
    private NewTextBox newTextBox_YComponentMin;
    private NewTextBox newTextBox_XComponentMax;
    private NewTextBox newTextBox_XComponentMin;
    private NewTextBox newTextBox_VShrinkageMax;
    private NewTextBox newTextBox_VShrinkageMin;
    private NewTextBox newTextBox_TempAtFlowMax;
    private NewTextBox newTextBox_ClampF;
    private NewTextBox newTextBox_TempAtFlowMin;
    private NewTextBox newTextBox_Totalmass;
    private NewTextBox newTextBox_ProjectedArea;
    private NewTextBox newTextBox_TimeEjectionTemp;
    private NewTextBox newTextBox_CavitySurface;
    private NewTextBox newTextBox_FilledTime;
    private Label label_ClampF;
    private Label label_Totalmass;
    private Label label_ProjectedArea;
    private Label label_ZComponent;
    private Label label_YComponent;
    private Label label_XComponent;

    protected override CreateParams CreateParams
    {
      get
      {
        CreateParams createParams = base.CreateParams;
        createParams.ExStyle |= 33554432;
        return createParams;
      }
    }

    public Dictionary<string, string> m_dicAIData { get; set; }

    public DataRow m_drStudy { get; set; }

    public frmAISolution()
    {
      this.InitializeComponent();
      this.newButton_Apply.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Apply.Image);
      this.label_Data.Text = LocaleControl.getInstance().GetString("IDS_GATELOCATION");
      this.newButton_ProcessSetting.Text = LocaleControl.getInstance().GetString("IDS_PROCESS_SETTING");
      this.newButton_Apply.ButtonText = "AI Solution " + LocaleControl.getInstance().GetString("IDS_APPLY");
    }

    private void frmAISolution_Load(object sender, EventArgs e)
    {
      this.ActiveControl = (Control) this.label_Data;
      this.RefreshData();
    }

    private void RefreshData()
    {
      string strOption = string.Empty;
      string empty = string.Empty;
      try
      {
        if (this.radioButton_Option1.Checked)
          strOption = "Option1";
        else if (this.radioButton_Option2.Checked)
          strOption = "Option2";
        this.DefaultData();
        if (this.m_dicAIData == null)
          return;
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Filling_Control_Injection_Time")))
          this.unitTextBox_FillTime.Value = this.m_dicAIData[strOption + "_Filling_Control_Injection_Time"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_VP_Switch_Over")))
          this.unitTextBox_VPSwitch.Value = this.m_dicAIData[strOption + "_VP_Switch_Over"];
        double num;
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Cooling_time")))
        {
          string p_strValue = this.m_dicAIData[strOption + "_Cooling_time"];
          UnitTextBox textBoxCoolingTime = this.unitTextBox_CoolingTime;
          num = Math.Round(clsUtill.ConvertToDouble(p_strValue), 0);
          string str = num.ToString();
          textBoxCoolingTime.Value = str;
        }
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Circuit_Coolant_Temp_Core_Min")))
        {
          string p_strValue = this.m_dicAIData[strOption + "_Circuit_Coolant_Temp_Core_Min"];
          UnitTextBox textBoxCoolingTemp = this.unitTextBox_CoolingTemp;
          num = Math.Round(clsUtill.ConvertToDouble(p_strValue), 0);
          string str = num.ToString();
          textBoxCoolingTemp.Value = str;
        }
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Melt_Temp")))
          this.unitTextBox_MeltTemp.Value = this.m_dicAIData[strOption + "_Melt_Temp"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Mold_Temp")))
          this.unitTextBox_MoldTemp.Value = this.m_dicAIData[strOption + "_Mold_Temp"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Gate_Number")))
          this.newTextBox_GateNum.Value = this.m_dicAIData[strOption + "_Gate_Number"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Material_Company")))
          this.newTextBox_Company.Value = this.m_dicAIData["Material_Company"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Trade_Name")))
          this.newTextBox_TradeName.Value = this.m_dicAIData["Trade_Name"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Density")))
          this.newTextBox_Density.Value = this.m_dicAIData["Density"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Packing_Time_Pressure")))
        {
          string[] strArray1 = this.m_dicAIData[strOption + "_Packing_Time_Pressure"].Split('/');
          for (int index = 0; index < strArray1.Length; ++index)
          {
            string[] strArray2 = strArray1[index].Split(':');
            UnitTextBox unitTextBox1 = (UnitTextBox) this.Controls.Find("unitTextBox_PackingTime" + (object) (index + 1), true)[0];
            num = Math.Round(clsUtill.ConvertToDouble(strArray2[0]), 0);
            string str1 = num.ToString();
            unitTextBox1.Value = str1;
            UnitTextBox unitTextBox2 = (UnitTextBox) this.Controls.Find("unitTextBox_PackingPressure" + (object) (index + 1), true)[0];
            num = Math.Round(clsUtill.ConvertToDouble(strArray2[1]), 0);
            string str2 = num.ToString();
            unitTextBox2.Value = str2;
          }
        }
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Gate")))
        {
          string str3 = this.m_dicAIData[strOption + "_Gate"];
          char[] chArray1 = new char[1]{ '/' };
          foreach (string str4 in str3.Split(chArray1))
          {
            char[] chArray2 = new char[1]{ ',' };
            string[] strArray = str4.Split(chArray2);
            DataGridViewRow row = this.dataGridView_Gate.Rows[this.dataGridView_Gate.Rows.Add()];
            row.Cells[0].Value = (object) strArray[0];
            row.Cells[1].Value = (object) strArray[1];
            row.Cells[2].Value = (object) strArray[2];
            row.Cells[3].Value = (object) strArray[3];
            row.Cells[4].Value = (object) strArray[4];
          }
        }
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Range_Deflection")))
          this.newTextBox_Deflection.Value = this.m_dicAIData[strOption + "_Range_Deflection"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Cycle_Time")))
          this.newTextBox_CycleTime.Value = this.m_dicAIData[strOption + "_Cycle_Time"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Injection_Pressure")))
          this.newTextBox_InjPressure.Value = this.m_dicAIData[strOption + "_Injection_Pressure"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Sink_Mark")))
          this.newTextBox_Sinkmark.Value = this.m_dicAIData[strOption + "_Sink_Mark"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Filled_Time")))
          this.newTextBox_FilledTime.Value = this.m_dicAIData[strOption + "_Filled_Time"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Time_To_Reach_Ejection_Temp_Cool_Max")))
          this.newTextBox_TimeEjectionTemp.Value = this.m_dicAIData[strOption + "_Time_To_Reach_Ejection_Temp_Cool_Max"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Cavity_Surface_Temp_Average")))
          this.newTextBox_CavitySurface.Value = this.m_dicAIData[strOption + "_Cavity_Surface_Temp_Average"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Projected_Area")))
          this.newTextBox_ProjectedArea.Value = this.m_dicAIData[strOption + "_Projected_Area"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Total_Mass")))
          this.newTextBox_Totalmass.Value = this.m_dicAIData[strOption + "_Total_Mass"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Clamp_Force")))
          this.newTextBox_ClampF.Value = this.m_dicAIData[strOption + "_Clamp_Force"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Temperature_At_Flow_Front_Min")))
          this.newTextBox_TempAtFlowMin.Value = this.m_dicAIData[strOption + "_Temperature_At_Flow_Front_Min"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Temperature_At_Flow_Front_Max")))
          this.newTextBox_TempAtFlowMax.Value = this.m_dicAIData[strOption + "_Temperature_At_Flow_Front_Max"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Volumetric_Shrinkage_At_Ejection_Min")))
          this.newTextBox_VShrinkageMin.Value = this.m_dicAIData[strOption + "_Volumetric_Shrinkage_At_Ejection_Min"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Volumetric_Shrinkage_At_Ejection_Max")))
          this.newTextBox_VShrinkageMax.Value = this.m_dicAIData[strOption + "_Volumetric_Shrinkage_At_Ejection_Max"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Range_X_Component_Min")))
          this.newTextBox_XComponentMin.Value = this.m_dicAIData[strOption + "_Range_X_Component_Min"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Range_X_Component_Max")))
          this.newTextBox_XComponentMax.Value = this.m_dicAIData[strOption + "_Range_X_Component_Max"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Range_Y_Component_Min")))
          this.newTextBox_YComponentMin.Value = this.m_dicAIData[strOption + "_Range_Y_Component_Min"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Range_Y_Component_Max")))
          this.newTextBox_YComponentMax.Value = this.m_dicAIData[strOption + "_Range_Y_Component_Max"];
        if (this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Range_Z_Component_Min")))
          this.newTextBox_ZComponentMin.Value = this.m_dicAIData[strOption + "_Range_Z_Component_Min"];
        if (!this.m_dicAIData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strOption + "_Range_Z_Component_Max")))
          return;
        this.newTextBox_ZComponentMax.Value = this.m_dicAIData[strOption + "_Range_Z_Component_Max"];
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmAISolution]RefreshData):" + ex.Message));
      }
    }

    private void DefaultData()
    {
      this.dataGridView_Gate.Rows.Clear();
      this.unitTextBox_FillTime.Value = string.Empty;
      this.unitTextBox_VPSwitch.Value = string.Empty;
      this.unitTextBox_CoolingTime.Value = string.Empty;
      this.unitTextBox_CoolingTemp.Value = string.Empty;
      this.unitTextBox_MeltTemp.Value = string.Empty;
      this.unitTextBox_MoldTemp.Value = string.Empty;
      this.newTextBox_GateNum.Value = string.Empty;
      this.newTextBox_Company.Value = string.Empty;
      this.newTextBox_TradeName.Value = string.Empty;
      this.newTextBox_Density.Value = string.Empty;
      this.unitTextBox_PackingPressure1.Value = string.Empty;
      this.unitTextBox_PackingTime1.Value = string.Empty;
      this.unitTextBox_PackingPressure2.Value = string.Empty;
      this.unitTextBox_PackingTime2.Value = string.Empty;
      this.unitTextBox_PackingPressure3.Value = string.Empty;
      this.unitTextBox_PackingTime3.Value = string.Empty;
      this.newTextBox_Deflection.Value = string.Empty;
      this.newTextBox_CycleTime.Value = string.Empty;
      this.newTextBox_InjPressure.Value = string.Empty;
      this.newTextBox_Sinkmark.Value = string.Empty;
      this.newTextBox_FilledTime.Value = string.Empty;
      this.newTextBox_TimeEjectionTemp.Value = string.Empty;
      this.newTextBox_CavitySurface.Value = string.Empty;
      this.newTextBox_ProjectedArea.Value = string.Empty;
      this.newTextBox_Totalmass.Value = string.Empty;
      this.newTextBox_ClampF.Value = string.Empty;
      this.newTextBox_TempAtFlowMin.Value = string.Empty;
      this.newTextBox_TempAtFlowMax.Value = string.Empty;
      this.newTextBox_VShrinkageMin.Value = string.Empty;
      this.newTextBox_VShrinkageMax.Value = string.Empty;
      this.newTextBox_XComponentMin.Value = string.Empty;
      this.newTextBox_XComponentMax.Value = string.Empty;
      this.newTextBox_YComponentMin.Value = string.Empty;
      this.newTextBox_YComponentMax.Value = string.Empty;
      this.newTextBox_ZComponentMin.Value = string.Empty;
      this.newTextBox_ZComponentMax.Value = string.Empty;
    }

    private void newButton_NewClick(object sender, EventArgs e) => this.Apply();

    private void Apply()
    {
      bool flag = false;
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      StringBuilder stringBuilder = new StringBuilder();
      Dictionary<string, string> dictionary = new Dictionary<string, string>();
      try
      {
        string str1 = this.m_dicAIData["Analysis_Type"].Replace('_', '|');
        if (str1.ToLower().Contains("cool"))
          flag = true;
        dictionary.Add("Sequence", str1);
        dictionary.Add("Fill_Time", this.unitTextBox_FillTime.Value);
        dictionary.Add("VP_Switch_Over", this.unitTextBox_VPSwitch.Value);
        dictionary.Add("Cooling_Time", flag.ToString() + "|" + this.unitTextBox_CoolingTime.Value);
        dictionary.Add("Cooling_Inlet_Temperature", this.unitTextBox_CoolingTemp.Value);
        dictionary.Add("Melt_Temperature", this.unitTextBox_MeltTemp.Value);
        dictionary.Add("Mold_Temperature", this.unitTextBox_MoldTemp.Value);
        dictionary.Add("Gate_Number", this.newTextBox_GateNum.Value);
        dictionary.Add("Material_Company", this.newTextBox_Company.Value);
        dictionary.Add("Trade_Name", this.newTextBox_TradeName.Value);
        dictionary.Add("FamilyAbb", this.m_dicAIData["FamilyAbb"]);
        dictionary.Add("MaterialID", this.m_dicAIData["MaterialID"]);
        dictionary.Add("Density", this.newTextBox_Density.Value);
        stringBuilder.Clear();
        string empty3 = string.Empty;
        string empty4 = string.Empty;
        for (int index = 0; index < 3; ++index)
        {
          string str2 = ((UnitTextBox) this.Controls.Find("unitTextBox_PackingTime" + (object) (index + 1), true)[0]).Value;
          string str3 = ((UnitTextBox) this.Controls.Find("unitTextBox_PackingPressure" + (object) (index + 1), true)[0]).Value;
          if (!(str2 == string.Empty) && !(str3 == string.Empty))
          {
            if (stringBuilder.Length != 0)
              stringBuilder.Append("/");
            stringBuilder.Append(str2);
            stringBuilder.Append(":");
            stringBuilder.Append(str3);
          }
        }
        dictionary.Add("Packing_Time_Pressure", stringBuilder.ToString());
        stringBuilder.Clear();
        foreach (DataGridViewRow row in (IEnumerable) this.dataGridView_Gate.Rows)
        {
          if (stringBuilder.Length != 0)
            stringBuilder.Append("/");
          stringBuilder.Append(row.Cells[0].Value);
          stringBuilder.Append(",");
          stringBuilder.Append(row.Cells[1].Value);
          stringBuilder.Append(",");
          stringBuilder.Append(row.Cells[2].Value);
          stringBuilder.Append(",");
          stringBuilder.Append(row.Cells[3].Value);
          stringBuilder.Append(",");
          stringBuilder.Append(row.Cells[4].Value);
        }
        dictionary.Add("Gate", stringBuilder.ToString());
        foreach (KeyValuePair<string, string> keyValuePair in dictionary)
          clsUtill.WriteINI("AIData", keyValuePair.Key, keyValuePair.Value, clsDefine.g_diTmpAI.ToString() + "\\" + this.m_drStudy["Name"] + "\\AIResult.ini");
        this.DialogResult = DialogResult.OK;
        this.Close();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][frmHDSolutions]Apply):" + ex.Message));
      }
    }

    private void frmAISolution_KeyDown(object sender, KeyEventArgs e)
    {
      if (e.KeyCode != Keys.Escape)
        return;
      this.Close();
    }

    private void radioButton_Option_CheckedChanged(object sender, EventArgs e) => this.RefreshData();

    private void newButton_Setting_NewClick(object sender, EventArgs e)
    {
      if (sender as NewButton == this.newButton_ProcessSetting)
      {
        this.tabControl1.SelectedIndex = 0;
        this.newButton_ProcessSetting.ButtonBackColor = Color.LightSteelBlue;
        this.newButton_PredictionResult.ButtonBackColor = Color.White;
      }
      else
      {
        this.tabControl1.SelectedIndex = 1;
        this.newButton_ProcessSetting.ButtonBackColor = Color.White;
        this.newButton_PredictionResult.ButtonBackColor = Color.LightSteelBlue;
      }
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      DataGridViewCellStyle gridViewCellStyle1 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle2 = new DataGridViewCellStyle();
      this.label_Data = new Label();
      this.panel_Gate = new Panel();
      this.dataGridView_Gate = new DataGridView();
      this.Column_X = new DataGridViewTextBoxColumn();
      this.Column_Y = new DataGridViewTextBoxColumn();
      this.Column_Z = new DataGridViewTextBoxColumn();
      this.Column_Open = new DataGridViewTextBoxColumn();
      this.Column_Close = new DataGridViewTextBoxColumn();
      this.label_PackingTime = new Label();
      this.unitTextBox_PackingTime1 = new UnitTextBox();
      this.label_VolShrinkage = new Label();
      this.newButton_Apply = new NewButton();
      this.label_FillTime = new Label();
      this.unitTextBox_FillTime = new UnitTextBox();
      this.unitTextBox_VPSwitch = new UnitTextBox();
      this.label_VPSwitch = new Label();
      this.unitTextBox_PackingPressure1 = new UnitTextBox();
      this.unitTextBox_PackingPressure3 = new UnitTextBox();
      this.unitTextBox_PackingTime3 = new UnitTextBox();
      this.unitTextBox_PackingPressure2 = new UnitTextBox();
      this.unitTextBox_PackingTime2 = new UnitTextBox();
      this.unitTextBox_MeltTemp = new UnitTextBox();
      this.unitTextBox_MoldTemp = new UnitTextBox();
      this.label3 = new Label();
      this.unitTextBox_CoolingTemp = new UnitTextBox();
      this.label_CoolingTemp = new Label();
      this.unitTextBox_CoolingTime = new UnitTextBox();
      this.label_CoolingTime = new Label();
      this.label_Company = new Label();
      this.label_GateNum = new Label();
      this.label_TradeName = new Label();
      this.newTextBox_GateNum = new NewTextBox();
      this.newTextBox_TradeName = new NewTextBox();
      this.newTextBox_Company = new NewTextBox();
      this.newTextBox_Density = new NewTextBox();
      this.label_Density = new Label();
      this.label_AIData = new Label();
      this.panel1 = new Panel();
      this.radioButton_Option2 = new RadioButton();
      this.radioButton_Option1 = new RadioButton();
      this.tabControl1 = new TabControl();
      this.tabPage_ProcessSetting = new TabPage();
      this.tabPage_PredictionResult = new TabPage();
      this.newTextBox_ZComponentMax = new NewTextBox();
      this.newTextBox_ZComponentMin = new NewTextBox();
      this.newTextBox_YComponentMax = new NewTextBox();
      this.newTextBox_YComponentMin = new NewTextBox();
      this.newTextBox_XComponentMax = new NewTextBox();
      this.newTextBox_XComponentMin = new NewTextBox();
      this.newTextBox_VShrinkageMax = new NewTextBox();
      this.newTextBox_VShrinkageMin = new NewTextBox();
      this.newTextBox_TempAtFlowMax = new NewTextBox();
      this.newTextBox_ClampF = new NewTextBox();
      this.newTextBox_TempAtFlowMin = new NewTextBox();
      this.newTextBox_Totalmass = new NewTextBox();
      this.newTextBox_ProjectedArea = new NewTextBox();
      this.newTextBox_TimeEjectionTemp = new NewTextBox();
      this.newTextBox_CavitySurface = new NewTextBox();
      this.newTextBox_FilledTime = new NewTextBox();
      this.label_ClampF = new Label();
      this.label_Totalmass = new Label();
      this.label_ProjectedArea = new Label();
      this.label_ZComponent = new Label();
      this.label_YComponent = new Label();
      this.label_XComponent = new Label();
      this.label_VShrinkage = new Label();
      this.label_TempAtFlow = new Label();
      this.label_TimeEjectionTemp = new Label();
      this.label_FilledTime = new Label();
      this.label_Deflection = new Label();
      this.label_CavitySurface = new Label();
      this.label_Sinkmark = new Label();
      this.label_InjPressure = new Label();
      this.label_CycleTime = new Label();
      this.newTextBox_Sinkmark = new NewTextBox();
      this.newTextBox_CycleTime = new NewTextBox();
      this.newTextBox_InjPressure = new NewTextBox();
      this.newTextBox_Deflection = new NewTextBox();
      this.panel_TabMain = new Panel();
      this.panel_TabButton = new Panel();
      this.newButton_ProcessSetting = new NewButton();
      this.newButton_PredictionResult = new NewButton();
      this.panel_Gate.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_Gate).BeginInit();
      this.panel1.SuspendLayout();
      this.tabControl1.SuspendLayout();
      this.tabPage_ProcessSetting.SuspendLayout();
      this.tabPage_PredictionResult.SuspendLayout();
      this.panel_TabMain.SuspendLayout();
      this.panel_TabButton.SuspendLayout();
      this.SuspendLayout();
      this.label_Data.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Data.BorderStyle = BorderStyle.FixedSingle;
      this.label_Data.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Data.ForeColor = Color.MidnightBlue;
      this.label_Data.Location = new Point(5, 55);
      this.label_Data.Name = "label_Data";
      this.label_Data.Size = new Size(495, 20);
      this.label_Data.TabIndex = 97;
      this.label_Data.Text = "게이트 위치";
      this.label_Data.TextAlign = ContentAlignment.MiddleCenter;
      this.panel_Gate.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Gate.Controls.Add((Control) this.dataGridView_Gate);
      this.panel_Gate.Location = new Point(5, 73);
      this.panel_Gate.Name = "panel_Gate";
      this.panel_Gate.Size = new Size(495, 191);
      this.panel_Gate.TabIndex = 137;
      this.dataGridView_Gate.AllowUserToAddRows = false;
      this.dataGridView_Gate.AllowUserToDeleteRows = false;
      this.dataGridView_Gate.AllowUserToResizeColumns = false;
      this.dataGridView_Gate.AllowUserToResizeRows = false;
      this.dataGridView_Gate.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_Gate.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_Gate.BackgroundColor = Color.White;
      gridViewCellStyle1.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle1.BackColor = Color.Lavender;
      gridViewCellStyle1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle1.ForeColor = SystemColors.WindowText;
      gridViewCellStyle1.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle1.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle1.WrapMode = DataGridViewTriState.True;
      this.dataGridView_Gate.ColumnHeadersDefaultCellStyle = gridViewCellStyle1;
      this.dataGridView_Gate.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_Gate.Columns.AddRange((DataGridViewColumn) this.Column_X, (DataGridViewColumn) this.Column_Y, (DataGridViewColumn) this.Column_Z, (DataGridViewColumn) this.Column_Open, (DataGridViewColumn) this.Column_Close);
      gridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleLeft;
      gridViewCellStyle2.BackColor = Color.White;
      gridViewCellStyle2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle2.ForeColor = SystemColors.ControlText;
      gridViewCellStyle2.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle2.WrapMode = DataGridViewTriState.False;
      this.dataGridView_Gate.DefaultCellStyle = gridViewCellStyle2;
      this.dataGridView_Gate.EnableHeadersVisualStyles = false;
      this.dataGridView_Gate.Location = new Point(-2, -2);
      this.dataGridView_Gate.Name = "dataGridView_Gate";
      this.dataGridView_Gate.RowHeadersVisible = false;
      this.dataGridView_Gate.RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.DisableResizing;
      this.dataGridView_Gate.RowTemplate.Height = 23;
      this.dataGridView_Gate.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
      this.dataGridView_Gate.Size = new Size(497, 192);
      this.dataGridView_Gate.TabIndex = 77;
      this.dataGridView_Gate.TabStop = false;
      this.Column_X.HeaderText = "X";
      this.Column_X.Name = "Column_X";
      this.Column_X.Resizable = DataGridViewTriState.False;
      this.Column_X.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.Column_Y.HeaderText = "Y";
      this.Column_Y.Name = "Column_Y";
      this.Column_Y.Resizable = DataGridViewTriState.False;
      this.Column_Y.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.Column_Z.HeaderText = "Z";
      this.Column_Z.Name = "Column_Z";
      this.Column_Z.Resizable = DataGridViewTriState.False;
      this.Column_Z.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.Column_Open.HeaderText = "Open Time";
      this.Column_Open.Name = "Column_Open";
      this.Column_Open.Resizable = DataGridViewTriState.False;
      this.Column_Open.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.Column_Close.HeaderText = "Close Time";
      this.Column_Close.Name = "Column_Close";
      this.Column_Close.Resizable = DataGridViewTriState.False;
      this.Column_Close.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.label_PackingTime.BackColor = Color.Lavender;
      this.label_PackingTime.BorderStyle = BorderStyle.FixedSingle;
      this.label_PackingTime.Location = new Point(5, 47);
      this.label_PackingTime.Name = "label_PackingTime";
      this.label_PackingTime.Size = new Size(248, 67);
      this.label_PackingTime.TabIndex = 148;
      this.label_PackingTime.Text = "Packing Time / Pressure";
      this.label_PackingTime.TextAlign = ContentAlignment.MiddleCenter;
      this.unitTextBox_PackingTime1.BackColor = Color.White;
      this.unitTextBox_PackingTime1.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_PackingTime1.ControlBackColor = Color.White;
      this.unitTextBox_PackingTime1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_PackingTime1.IsDigit = false;
      this.unitTextBox_PackingTime1.Location = new Point(252, 47);
      this.unitTextBox_PackingTime1.Name = "unitTextBox_PackingTime1";
      this.unitTextBox_PackingTime1.ReadOnly = false;
      this.unitTextBox_PackingTime1.Size = new Size(125, 23);
      this.unitTextBox_PackingTime1.TabIndex = 143;
      this.unitTextBox_PackingTime1.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_PackingTime1.Unit = "s";
      this.unitTextBox_PackingTime1.Value = "";
      this.label_VolShrinkage.BackColor = Color.Lavender;
      this.label_VolShrinkage.BorderStyle = BorderStyle.FixedSingle;
      this.label_VolShrinkage.Location = new Point(5, 113);
      this.label_VolShrinkage.Name = "label_VolShrinkage";
      this.label_VolShrinkage.Size = new Size(248, 23);
      this.label_VolShrinkage.TabIndex = 158;
      this.label_VolShrinkage.Text = "Melt Temperature";
      this.label_VolShrinkage.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.ButtonBackColor = Color.White;
      this.newButton_Apply.ButtonText = "AI Solution 적용";
      this.newButton_Apply.FlatBorderSize = 1;
      this.newButton_Apply.FlatStyle = FlatStyle.Flat;
      this.newButton_Apply.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Apply.ForeColor = Color.Navy;
      this.newButton_Apply.Image = (Image) Resources.OK;
      this.newButton_Apply.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Apply.Location = new Point(6, 630);
      this.newButton_Apply.Name = "newButton_Apply";
      this.newButton_Apply.Size = new Size(495, 28);
      this.newButton_Apply.TabIndex = 173;
      this.newButton_Apply.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Apply.NewClick += new EventHandler(this.newButton_NewClick);
      this.label_FillTime.BackColor = Color.Lavender;
      this.label_FillTime.BorderStyle = BorderStyle.FixedSingle;
      this.label_FillTime.Location = new Point(5, 3);
      this.label_FillTime.Name = "label_FillTime";
      this.label_FillTime.Size = new Size(248, 23);
      this.label_FillTime.TabIndex = 174;
      this.label_FillTime.Text = "Fill Time";
      this.label_FillTime.TextAlign = ContentAlignment.MiddleCenter;
      this.unitTextBox_FillTime.BackColor = Color.White;
      this.unitTextBox_FillTime.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_FillTime.ControlBackColor = Color.White;
      this.unitTextBox_FillTime.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_FillTime.IsDigit = false;
      this.unitTextBox_FillTime.Location = new Point(252, 3);
      this.unitTextBox_FillTime.Name = "unitTextBox_FillTime";
      this.unitTextBox_FillTime.ReadOnly = false;
      this.unitTextBox_FillTime.Size = new Size(248, 23);
      this.unitTextBox_FillTime.TabIndex = 175;
      this.unitTextBox_FillTime.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_FillTime.Unit = "s";
      this.unitTextBox_FillTime.Value = "";
      this.unitTextBox_VPSwitch.BackColor = Color.White;
      this.unitTextBox_VPSwitch.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_VPSwitch.ControlBackColor = Color.White;
      this.unitTextBox_VPSwitch.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_VPSwitch.IsDigit = false;
      this.unitTextBox_VPSwitch.Location = new Point(252, 25);
      this.unitTextBox_VPSwitch.Name = "unitTextBox_VPSwitch";
      this.unitTextBox_VPSwitch.ReadOnly = false;
      this.unitTextBox_VPSwitch.Size = new Size(248, 23);
      this.unitTextBox_VPSwitch.TabIndex = 177;
      this.unitTextBox_VPSwitch.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_VPSwitch.Unit = "%";
      this.unitTextBox_VPSwitch.Value = "";
      this.label_VPSwitch.BackColor = Color.Lavender;
      this.label_VPSwitch.BorderStyle = BorderStyle.FixedSingle;
      this.label_VPSwitch.Location = new Point(5, 25);
      this.label_VPSwitch.Name = "label_VPSwitch";
      this.label_VPSwitch.Size = new Size(248, 23);
      this.label_VPSwitch.TabIndex = 176;
      this.label_VPSwitch.Text = "V/P Switch Over";
      this.label_VPSwitch.TextAlign = ContentAlignment.MiddleCenter;
      this.unitTextBox_PackingPressure1.BackColor = Color.White;
      this.unitTextBox_PackingPressure1.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_PackingPressure1.ControlBackColor = Color.White;
      this.unitTextBox_PackingPressure1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_PackingPressure1.IsDigit = false;
      this.unitTextBox_PackingPressure1.Location = new Point(375, 47);
      this.unitTextBox_PackingPressure1.Name = "unitTextBox_PackingPressure1";
      this.unitTextBox_PackingPressure1.ReadOnly = false;
      this.unitTextBox_PackingPressure1.Size = new Size(125, 23);
      this.unitTextBox_PackingPressure1.TabIndex = 178;
      this.unitTextBox_PackingPressure1.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_PackingPressure1.Unit = "Mpa";
      this.unitTextBox_PackingPressure1.Value = "";
      this.unitTextBox_PackingPressure3.BackColor = Color.White;
      this.unitTextBox_PackingPressure3.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_PackingPressure3.ControlBackColor = Color.White;
      this.unitTextBox_PackingPressure3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_PackingPressure3.IsDigit = false;
      this.unitTextBox_PackingPressure3.Location = new Point(375, 91);
      this.unitTextBox_PackingPressure3.Name = "unitTextBox_PackingPressure3";
      this.unitTextBox_PackingPressure3.ReadOnly = false;
      this.unitTextBox_PackingPressure3.Size = new Size(125, 23);
      this.unitTextBox_PackingPressure3.TabIndex = 180;
      this.unitTextBox_PackingPressure3.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_PackingPressure3.Unit = "Mpa";
      this.unitTextBox_PackingPressure3.Value = "";
      this.unitTextBox_PackingTime3.BackColor = Color.White;
      this.unitTextBox_PackingTime3.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_PackingTime3.ControlBackColor = Color.White;
      this.unitTextBox_PackingTime3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_PackingTime3.IsDigit = false;
      this.unitTextBox_PackingTime3.Location = new Point(252, 91);
      this.unitTextBox_PackingTime3.Name = "unitTextBox_PackingTime3";
      this.unitTextBox_PackingTime3.ReadOnly = false;
      this.unitTextBox_PackingTime3.Size = new Size(125, 23);
      this.unitTextBox_PackingTime3.TabIndex = 179;
      this.unitTextBox_PackingTime3.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_PackingTime3.Unit = "s";
      this.unitTextBox_PackingTime3.Value = "";
      this.unitTextBox_PackingPressure2.BackColor = Color.White;
      this.unitTextBox_PackingPressure2.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_PackingPressure2.ControlBackColor = Color.White;
      this.unitTextBox_PackingPressure2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_PackingPressure2.IsDigit = false;
      this.unitTextBox_PackingPressure2.Location = new Point(375, 69);
      this.unitTextBox_PackingPressure2.Name = "unitTextBox_PackingPressure2";
      this.unitTextBox_PackingPressure2.ReadOnly = false;
      this.unitTextBox_PackingPressure2.Size = new Size(125, 23);
      this.unitTextBox_PackingPressure2.TabIndex = 182;
      this.unitTextBox_PackingPressure2.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_PackingPressure2.Unit = "Mpa";
      this.unitTextBox_PackingPressure2.Value = "";
      this.unitTextBox_PackingTime2.BackColor = Color.White;
      this.unitTextBox_PackingTime2.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_PackingTime2.ControlBackColor = Color.White;
      this.unitTextBox_PackingTime2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_PackingTime2.IsDigit = false;
      this.unitTextBox_PackingTime2.Location = new Point(252, 69);
      this.unitTextBox_PackingTime2.Name = "unitTextBox_PackingTime2";
      this.unitTextBox_PackingTime2.ReadOnly = false;
      this.unitTextBox_PackingTime2.Size = new Size(125, 23);
      this.unitTextBox_PackingTime2.TabIndex = 181;
      this.unitTextBox_PackingTime2.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_PackingTime2.Unit = "s";
      this.unitTextBox_PackingTime2.Value = "";
      this.unitTextBox_MeltTemp.BackColor = Color.White;
      this.unitTextBox_MeltTemp.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_MeltTemp.ControlBackColor = Color.White;
      this.unitTextBox_MeltTemp.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_MeltTemp.IsDigit = false;
      this.unitTextBox_MeltTemp.Location = new Point(252, 113);
      this.unitTextBox_MeltTemp.Name = "unitTextBox_MeltTemp";
      this.unitTextBox_MeltTemp.ReadOnly = false;
      this.unitTextBox_MeltTemp.Size = new Size(248, 23);
      this.unitTextBox_MeltTemp.TabIndex = 183;
      this.unitTextBox_MeltTemp.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_MeltTemp.Unit = "℃";
      this.unitTextBox_MeltTemp.Value = "";
      this.unitTextBox_MoldTemp.BackColor = Color.White;
      this.unitTextBox_MoldTemp.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_MoldTemp.ControlBackColor = Color.White;
      this.unitTextBox_MoldTemp.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_MoldTemp.IsDigit = false;
      this.unitTextBox_MoldTemp.Location = new Point(252, 135);
      this.unitTextBox_MoldTemp.Name = "unitTextBox_MoldTemp";
      this.unitTextBox_MoldTemp.ReadOnly = false;
      this.unitTextBox_MoldTemp.Size = new Size(248, 23);
      this.unitTextBox_MoldTemp.TabIndex = 185;
      this.unitTextBox_MoldTemp.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_MoldTemp.Unit = "℃";
      this.unitTextBox_MoldTemp.Value = "";
      this.label3.BackColor = Color.Lavender;
      this.label3.BorderStyle = BorderStyle.FixedSingle;
      this.label3.Location = new Point(5, 135);
      this.label3.Name = "label3";
      this.label3.Size = new Size(248, 23);
      this.label3.TabIndex = 184;
      this.label3.Text = "Mold Temperature";
      this.label3.TextAlign = ContentAlignment.MiddleCenter;
      this.unitTextBox_CoolingTemp.BackColor = Color.White;
      this.unitTextBox_CoolingTemp.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_CoolingTemp.ControlBackColor = Color.White;
      this.unitTextBox_CoolingTemp.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_CoolingTemp.IsDigit = false;
      this.unitTextBox_CoolingTemp.Location = new Point(252, 179);
      this.unitTextBox_CoolingTemp.Name = "unitTextBox_CoolingTemp";
      this.unitTextBox_CoolingTemp.ReadOnly = false;
      this.unitTextBox_CoolingTemp.Size = new Size(248, 23);
      this.unitTextBox_CoolingTemp.TabIndex = 189;
      this.unitTextBox_CoolingTemp.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_CoolingTemp.Unit = "℃";
      this.unitTextBox_CoolingTemp.Value = "";
      this.label_CoolingTemp.BackColor = Color.Lavender;
      this.label_CoolingTemp.BorderStyle = BorderStyle.FixedSingle;
      this.label_CoolingTemp.Location = new Point(5, 179);
      this.label_CoolingTemp.Name = "label_CoolingTemp";
      this.label_CoolingTemp.Size = new Size(248, 23);
      this.label_CoolingTemp.TabIndex = 188;
      this.label_CoolingTemp.Text = "Cooling Inlet Temperature";
      this.label_CoolingTemp.TextAlign = ContentAlignment.MiddleCenter;
      this.unitTextBox_CoolingTime.BackColor = Color.White;
      this.unitTextBox_CoolingTime.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_CoolingTime.ControlBackColor = Color.White;
      this.unitTextBox_CoolingTime.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_CoolingTime.IsDigit = false;
      this.unitTextBox_CoolingTime.Location = new Point(252, 157);
      this.unitTextBox_CoolingTime.Name = "unitTextBox_CoolingTime";
      this.unitTextBox_CoolingTime.ReadOnly = false;
      this.unitTextBox_CoolingTime.Size = new Size(248, 23);
      this.unitTextBox_CoolingTime.TabIndex = 187;
      this.unitTextBox_CoolingTime.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_CoolingTime.Unit = "s";
      this.unitTextBox_CoolingTime.Value = "";
      this.label_CoolingTime.BackColor = Color.Lavender;
      this.label_CoolingTime.BorderStyle = BorderStyle.FixedSingle;
      this.label_CoolingTime.Location = new Point(5, 157);
      this.label_CoolingTime.Name = "label_CoolingTime";
      this.label_CoolingTime.Size = new Size(248, 23);
      this.label_CoolingTime.TabIndex = 186;
      this.label_CoolingTime.Text = "Cooling Time";
      this.label_CoolingTime.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Company.BackColor = Color.Lavender;
      this.label_Company.BorderStyle = BorderStyle.FixedSingle;
      this.label_Company.Location = new Point(5, 223);
      this.label_Company.Name = "label_Company";
      this.label_Company.Size = new Size(248, 23);
      this.label_Company.TabIndex = 192;
      this.label_Company.Text = "Material - Company";
      this.label_Company.TextAlign = ContentAlignment.MiddleCenter;
      this.label_GateNum.BackColor = Color.Lavender;
      this.label_GateNum.BorderStyle = BorderStyle.FixedSingle;
      this.label_GateNum.Location = new Point(5, 201);
      this.label_GateNum.Name = "label_GateNum";
      this.label_GateNum.Size = new Size(248, 23);
      this.label_GateNum.TabIndex = 190;
      this.label_GateNum.Text = "Gate Number";
      this.label_GateNum.TextAlign = ContentAlignment.MiddleCenter;
      this.label_TradeName.BackColor = Color.Lavender;
      this.label_TradeName.BorderStyle = BorderStyle.FixedSingle;
      this.label_TradeName.Location = new Point(5, 245);
      this.label_TradeName.Name = "label_TradeName";
      this.label_TradeName.Size = new Size(248, 23);
      this.label_TradeName.TabIndex = 194;
      this.label_TradeName.Text = "Material - Trade Name";
      this.label_TradeName.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_GateNum.BackColor = Color.White;
      this.newTextBox_GateNum.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_GateNum.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_GateNum.IsDigit = false;
      this.newTextBox_GateNum.Lines = new string[0];
      this.newTextBox_GateNum.Location = new Point(252, 201);
      this.newTextBox_GateNum.MultiLine = false;
      this.newTextBox_GateNum.Name = "newTextBox_GateNum";
      this.newTextBox_GateNum.ReadOnly = false;
      this.newTextBox_GateNum.Size = new Size(248, 23);
      this.newTextBox_GateNum.TabIndex = 136;
      this.newTextBox_GateNum.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_GateNum.TextBoxBackColor = Color.White;
      this.newTextBox_GateNum.TextForeColor = SystemColors.WindowText;
      this.newTextBox_GateNum.Value = "";
      this.newTextBox_TradeName.BackColor = Color.White;
      this.newTextBox_TradeName.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_TradeName.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_TradeName.IsDigit = false;
      this.newTextBox_TradeName.Lines = new string[0];
      this.newTextBox_TradeName.Location = new Point(252, 245);
      this.newTextBox_TradeName.MultiLine = false;
      this.newTextBox_TradeName.Name = "newTextBox_TradeName";
      this.newTextBox_TradeName.ReadOnly = false;
      this.newTextBox_TradeName.Size = new Size(248, 23);
      this.newTextBox_TradeName.TabIndex = 195;
      this.newTextBox_TradeName.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_TradeName.TextBoxBackColor = Color.White;
      this.newTextBox_TradeName.TextForeColor = SystemColors.WindowText;
      this.newTextBox_TradeName.Value = "";
      this.newTextBox_Company.BackColor = Color.White;
      this.newTextBox_Company.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Company.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Company.IsDigit = false;
      this.newTextBox_Company.Lines = new string[0];
      this.newTextBox_Company.Location = new Point(252, 223);
      this.newTextBox_Company.MultiLine = false;
      this.newTextBox_Company.Name = "newTextBox_Company";
      this.newTextBox_Company.ReadOnly = false;
      this.newTextBox_Company.Size = new Size(248, 23);
      this.newTextBox_Company.TabIndex = 196;
      this.newTextBox_Company.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Company.TextBoxBackColor = Color.White;
      this.newTextBox_Company.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Company.Value = "";
      this.newTextBox_Density.BackColor = Color.White;
      this.newTextBox_Density.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Density.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Density.IsDigit = false;
      this.newTextBox_Density.Lines = new string[0];
      this.newTextBox_Density.Location = new Point(252, 267);
      this.newTextBox_Density.MultiLine = false;
      this.newTextBox_Density.Name = "newTextBox_Density";
      this.newTextBox_Density.ReadOnly = false;
      this.newTextBox_Density.Size = new Size(248, 23);
      this.newTextBox_Density.TabIndex = 198;
      this.newTextBox_Density.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Density.TextBoxBackColor = Color.White;
      this.newTextBox_Density.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Density.Value = "";
      this.label_Density.BackColor = Color.Lavender;
      this.label_Density.BorderStyle = BorderStyle.FixedSingle;
      this.label_Density.Location = new Point(5, 267);
      this.label_Density.Name = "label_Density";
      this.label_Density.Size = new Size(248, 23);
      this.label_Density.TabIndex = 197;
      this.label_Density.Text = "Material - Density";
      this.label_Density.TextAlign = ContentAlignment.MiddleCenter;
      this.label_AIData.BackColor = Color.FromArgb(229, 238, 248);
      this.label_AIData.BorderStyle = BorderStyle.FixedSingle;
      this.label_AIData.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_AIData.ForeColor = Color.MidnightBlue;
      this.label_AIData.Location = new Point(5, 4);
      this.label_AIData.Name = "label_AIData";
      this.label_AIData.Size = new Size(495, 20);
      this.label_AIData.TabIndex = 199;
      this.label_AIData.Text = "AI Data";
      this.label_AIData.TextAlign = ContentAlignment.MiddleCenter;
      this.panel1.BorderStyle = BorderStyle.FixedSingle;
      this.panel1.Controls.Add((Control) this.radioButton_Option2);
      this.panel1.Controls.Add((Control) this.radioButton_Option1);
      this.panel1.Location = new Point(5, 23);
      this.panel1.Name = "panel1";
      this.panel1.Size = new Size(495, 25);
      this.panel1.TabIndex = 138;
      this.radioButton_Option2.AutoSize = true;
      this.radioButton_Option2.Location = new Point(305, 1);
      this.radioButton_Option2.Name = "radioButton_Option2";
      this.radioButton_Option2.Size = new Size(71, 19);
      this.radioButton_Option2.TabIndex = 1;
      this.radioButton_Option2.Text = "Option 2";
      this.radioButton_Option2.UseVisualStyleBackColor = true;
      this.radioButton_Option2.CheckedChanged += new EventHandler(this.radioButton_Option_CheckedChanged);
      this.radioButton_Option1.AutoSize = true;
      this.radioButton_Option1.Checked = true;
      this.radioButton_Option1.Location = new Point(109, 1);
      this.radioButton_Option1.Name = "radioButton_Option1";
      this.radioButton_Option1.Size = new Size(71, 19);
      this.radioButton_Option1.TabIndex = 0;
      this.radioButton_Option1.TabStop = true;
      this.radioButton_Option1.Text = "Option 1";
      this.radioButton_Option1.UseVisualStyleBackColor = true;
      this.radioButton_Option1.CheckedChanged += new EventHandler(this.radioButton_Option_CheckedChanged);
      this.tabControl1.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.tabControl1.Controls.Add((Control) this.tabPage_ProcessSetting);
      this.tabControl1.Controls.Add((Control) this.tabPage_PredictionResult);
      this.tabControl1.Location = new Point(-9, -4);
      this.tabControl1.Name = "tabControl1";
      this.tabControl1.SelectedIndex = 0;
      this.tabControl1.Size = new Size(509, 365);
      this.tabControl1.TabIndex = 200;
      this.tabPage_ProcessSetting.Controls.Add((Control) this.label_VolShrinkage);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.unitTextBox_PackingTime1);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.newTextBox_Density);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.label_PackingTime);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.label_Density);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.label_FillTime);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.unitTextBox_FillTime);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.newTextBox_Company);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.label_VPSwitch);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.newTextBox_TradeName);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.unitTextBox_VPSwitch);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.newTextBox_GateNum);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.unitTextBox_PackingPressure1);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.label_TradeName);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.unitTextBox_PackingTime3);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.label_Company);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.unitTextBox_PackingPressure3);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.label_GateNum);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.unitTextBox_PackingTime2);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.unitTextBox_CoolingTemp);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.unitTextBox_PackingPressure2);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.label_CoolingTemp);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.unitTextBox_MeltTemp);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.unitTextBox_CoolingTime);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.label3);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.label_CoolingTime);
      this.tabPage_ProcessSetting.Controls.Add((Control) this.unitTextBox_MoldTemp);
      this.tabPage_ProcessSetting.Location = new Point(4, 24);
      this.tabPage_ProcessSetting.Name = "tabPage_ProcessSetting";
      this.tabPage_ProcessSetting.Padding = new Padding(3);
      this.tabPage_ProcessSetting.Size = new Size(501, 337);
      this.tabPage_ProcessSetting.TabIndex = 0;
      this.tabPage_ProcessSetting.Text = "tabPage1";
      this.tabPage_ProcessSetting.UseVisualStyleBackColor = true;
      this.tabPage_PredictionResult.Controls.Add((Control) this.newTextBox_ZComponentMax);
      this.tabPage_PredictionResult.Controls.Add((Control) this.newTextBox_ZComponentMin);
      this.tabPage_PredictionResult.Controls.Add((Control) this.newTextBox_YComponentMax);
      this.tabPage_PredictionResult.Controls.Add((Control) this.newTextBox_YComponentMin);
      this.tabPage_PredictionResult.Controls.Add((Control) this.newTextBox_XComponentMax);
      this.tabPage_PredictionResult.Controls.Add((Control) this.newTextBox_XComponentMin);
      this.tabPage_PredictionResult.Controls.Add((Control) this.newTextBox_VShrinkageMax);
      this.tabPage_PredictionResult.Controls.Add((Control) this.newTextBox_VShrinkageMin);
      this.tabPage_PredictionResult.Controls.Add((Control) this.newTextBox_TempAtFlowMax);
      this.tabPage_PredictionResult.Controls.Add((Control) this.newTextBox_ClampF);
      this.tabPage_PredictionResult.Controls.Add((Control) this.newTextBox_TempAtFlowMin);
      this.tabPage_PredictionResult.Controls.Add((Control) this.newTextBox_Totalmass);
      this.tabPage_PredictionResult.Controls.Add((Control) this.newTextBox_ProjectedArea);
      this.tabPage_PredictionResult.Controls.Add((Control) this.newTextBox_TimeEjectionTemp);
      this.tabPage_PredictionResult.Controls.Add((Control) this.newTextBox_CavitySurface);
      this.tabPage_PredictionResult.Controls.Add((Control) this.newTextBox_FilledTime);
      this.tabPage_PredictionResult.Controls.Add((Control) this.label_ClampF);
      this.tabPage_PredictionResult.Controls.Add((Control) this.label_Totalmass);
      this.tabPage_PredictionResult.Controls.Add((Control) this.label_ProjectedArea);
      this.tabPage_PredictionResult.Controls.Add((Control) this.label_ZComponent);
      this.tabPage_PredictionResult.Controls.Add((Control) this.label_YComponent);
      this.tabPage_PredictionResult.Controls.Add((Control) this.label_XComponent);
      this.tabPage_PredictionResult.Controls.Add((Control) this.label_VShrinkage);
      this.tabPage_PredictionResult.Controls.Add((Control) this.label_TempAtFlow);
      this.tabPage_PredictionResult.Controls.Add((Control) this.label_TimeEjectionTemp);
      this.tabPage_PredictionResult.Controls.Add((Control) this.label_FilledTime);
      this.tabPage_PredictionResult.Controls.Add((Control) this.label_Deflection);
      this.tabPage_PredictionResult.Controls.Add((Control) this.label_CavitySurface);
      this.tabPage_PredictionResult.Controls.Add((Control) this.label_Sinkmark);
      this.tabPage_PredictionResult.Controls.Add((Control) this.label_InjPressure);
      this.tabPage_PredictionResult.Controls.Add((Control) this.label_CycleTime);
      this.tabPage_PredictionResult.Controls.Add((Control) this.newTextBox_Sinkmark);
      this.tabPage_PredictionResult.Controls.Add((Control) this.newTextBox_CycleTime);
      this.tabPage_PredictionResult.Controls.Add((Control) this.newTextBox_InjPressure);
      this.tabPage_PredictionResult.Controls.Add((Control) this.newTextBox_Deflection);
      this.tabPage_PredictionResult.Location = new Point(4, 24);
      this.tabPage_PredictionResult.Name = "tabPage_PredictionResult";
      this.tabPage_PredictionResult.Padding = new Padding(3);
      this.tabPage_PredictionResult.Size = new Size(501, 337);
      this.tabPage_PredictionResult.TabIndex = 1;
      this.tabPage_PredictionResult.Text = "tabPage2";
      this.tabPage_PredictionResult.UseVisualStyleBackColor = true;
      this.newTextBox_ZComponentMax.BackColor = Color.White;
      this.newTextBox_ZComponentMax.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_ZComponentMax.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_ZComponentMax.IsDigit = false;
      this.newTextBox_ZComponentMax.Lines = new string[0];
      this.newTextBox_ZComponentMax.Location = new Point(375, 311);
      this.newTextBox_ZComponentMax.MultiLine = false;
      this.newTextBox_ZComponentMax.Name = "newTextBox_ZComponentMax";
      this.newTextBox_ZComponentMax.ReadOnly = false;
      this.newTextBox_ZComponentMax.Size = new Size(125, 23);
      this.newTextBox_ZComponentMax.TabIndex = 238;
      this.newTextBox_ZComponentMax.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_ZComponentMax.TextBoxBackColor = Color.White;
      this.newTextBox_ZComponentMax.TextForeColor = SystemColors.WindowText;
      this.newTextBox_ZComponentMax.Value = "";
      this.newTextBox_ZComponentMin.BackColor = Color.White;
      this.newTextBox_ZComponentMin.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_ZComponentMin.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_ZComponentMin.IsDigit = false;
      this.newTextBox_ZComponentMin.Lines = new string[0];
      this.newTextBox_ZComponentMin.Location = new Point(252, 311);
      this.newTextBox_ZComponentMin.MultiLine = false;
      this.newTextBox_ZComponentMin.Name = "newTextBox_ZComponentMin";
      this.newTextBox_ZComponentMin.ReadOnly = false;
      this.newTextBox_ZComponentMin.Size = new Size(125, 23);
      this.newTextBox_ZComponentMin.TabIndex = 237;
      this.newTextBox_ZComponentMin.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_ZComponentMin.TextBoxBackColor = Color.White;
      this.newTextBox_ZComponentMin.TextForeColor = SystemColors.WindowText;
      this.newTextBox_ZComponentMin.Value = "";
      this.newTextBox_YComponentMax.BackColor = Color.White;
      this.newTextBox_YComponentMax.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_YComponentMax.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_YComponentMax.IsDigit = false;
      this.newTextBox_YComponentMax.Lines = new string[0];
      this.newTextBox_YComponentMax.Location = new Point(375, 289);
      this.newTextBox_YComponentMax.MultiLine = false;
      this.newTextBox_YComponentMax.Name = "newTextBox_YComponentMax";
      this.newTextBox_YComponentMax.ReadOnly = false;
      this.newTextBox_YComponentMax.Size = new Size(125, 23);
      this.newTextBox_YComponentMax.TabIndex = 236;
      this.newTextBox_YComponentMax.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_YComponentMax.TextBoxBackColor = Color.White;
      this.newTextBox_YComponentMax.TextForeColor = SystemColors.WindowText;
      this.newTextBox_YComponentMax.Value = "";
      this.newTextBox_YComponentMin.BackColor = Color.White;
      this.newTextBox_YComponentMin.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_YComponentMin.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_YComponentMin.IsDigit = false;
      this.newTextBox_YComponentMin.Lines = new string[0];
      this.newTextBox_YComponentMin.Location = new Point(252, 289);
      this.newTextBox_YComponentMin.MultiLine = false;
      this.newTextBox_YComponentMin.Name = "newTextBox_YComponentMin";
      this.newTextBox_YComponentMin.ReadOnly = false;
      this.newTextBox_YComponentMin.Size = new Size(125, 23);
      this.newTextBox_YComponentMin.TabIndex = 235;
      this.newTextBox_YComponentMin.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_YComponentMin.TextBoxBackColor = Color.White;
      this.newTextBox_YComponentMin.TextForeColor = SystemColors.WindowText;
      this.newTextBox_YComponentMin.Value = "";
      this.newTextBox_XComponentMax.BackColor = Color.White;
      this.newTextBox_XComponentMax.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_XComponentMax.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_XComponentMax.IsDigit = false;
      this.newTextBox_XComponentMax.Lines = new string[0];
      this.newTextBox_XComponentMax.Location = new Point(375, 267);
      this.newTextBox_XComponentMax.MultiLine = false;
      this.newTextBox_XComponentMax.Name = "newTextBox_XComponentMax";
      this.newTextBox_XComponentMax.ReadOnly = false;
      this.newTextBox_XComponentMax.Size = new Size(125, 23);
      this.newTextBox_XComponentMax.TabIndex = 234;
      this.newTextBox_XComponentMax.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_XComponentMax.TextBoxBackColor = Color.White;
      this.newTextBox_XComponentMax.TextForeColor = SystemColors.WindowText;
      this.newTextBox_XComponentMax.Value = "";
      this.newTextBox_XComponentMin.BackColor = Color.White;
      this.newTextBox_XComponentMin.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_XComponentMin.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_XComponentMin.IsDigit = false;
      this.newTextBox_XComponentMin.Lines = new string[0];
      this.newTextBox_XComponentMin.Location = new Point(252, 267);
      this.newTextBox_XComponentMin.MultiLine = false;
      this.newTextBox_XComponentMin.Name = "newTextBox_XComponentMin";
      this.newTextBox_XComponentMin.ReadOnly = false;
      this.newTextBox_XComponentMin.Size = new Size(125, 23);
      this.newTextBox_XComponentMin.TabIndex = 233;
      this.newTextBox_XComponentMin.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_XComponentMin.TextBoxBackColor = Color.White;
      this.newTextBox_XComponentMin.TextForeColor = SystemColors.WindowText;
      this.newTextBox_XComponentMin.Value = "";
      this.newTextBox_VShrinkageMax.BackColor = Color.White;
      this.newTextBox_VShrinkageMax.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_VShrinkageMax.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_VShrinkageMax.IsDigit = false;
      this.newTextBox_VShrinkageMax.Lines = new string[0];
      this.newTextBox_VShrinkageMax.Location = new Point(375, 245);
      this.newTextBox_VShrinkageMax.MultiLine = false;
      this.newTextBox_VShrinkageMax.Name = "newTextBox_VShrinkageMax";
      this.newTextBox_VShrinkageMax.ReadOnly = false;
      this.newTextBox_VShrinkageMax.Size = new Size(125, 23);
      this.newTextBox_VShrinkageMax.TabIndex = 232;
      this.newTextBox_VShrinkageMax.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_VShrinkageMax.TextBoxBackColor = Color.White;
      this.newTextBox_VShrinkageMax.TextForeColor = SystemColors.WindowText;
      this.newTextBox_VShrinkageMax.Value = "";
      this.newTextBox_VShrinkageMin.BackColor = Color.White;
      this.newTextBox_VShrinkageMin.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_VShrinkageMin.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_VShrinkageMin.IsDigit = false;
      this.newTextBox_VShrinkageMin.Lines = new string[0];
      this.newTextBox_VShrinkageMin.Location = new Point(252, 245);
      this.newTextBox_VShrinkageMin.MultiLine = false;
      this.newTextBox_VShrinkageMin.Name = "newTextBox_VShrinkageMin";
      this.newTextBox_VShrinkageMin.ReadOnly = false;
      this.newTextBox_VShrinkageMin.Size = new Size(125, 23);
      this.newTextBox_VShrinkageMin.TabIndex = 231;
      this.newTextBox_VShrinkageMin.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_VShrinkageMin.TextBoxBackColor = Color.White;
      this.newTextBox_VShrinkageMin.TextForeColor = SystemColors.WindowText;
      this.newTextBox_VShrinkageMin.Value = "";
      this.newTextBox_TempAtFlowMax.BackColor = Color.White;
      this.newTextBox_TempAtFlowMax.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_TempAtFlowMax.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_TempAtFlowMax.IsDigit = false;
      this.newTextBox_TempAtFlowMax.Lines = new string[0];
      this.newTextBox_TempAtFlowMax.Location = new Point(375, 223);
      this.newTextBox_TempAtFlowMax.MultiLine = false;
      this.newTextBox_TempAtFlowMax.Name = "newTextBox_TempAtFlowMax";
      this.newTextBox_TempAtFlowMax.ReadOnly = false;
      this.newTextBox_TempAtFlowMax.Size = new Size(125, 23);
      this.newTextBox_TempAtFlowMax.TabIndex = 230;
      this.newTextBox_TempAtFlowMax.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_TempAtFlowMax.TextBoxBackColor = Color.White;
      this.newTextBox_TempAtFlowMax.TextForeColor = SystemColors.WindowText;
      this.newTextBox_TempAtFlowMax.Value = "";
      this.newTextBox_ClampF.BackColor = Color.White;
      this.newTextBox_ClampF.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_ClampF.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_ClampF.IsDigit = false;
      this.newTextBox_ClampF.Lines = new string[0];
      this.newTextBox_ClampF.Location = new Point(252, 201);
      this.newTextBox_ClampF.MultiLine = false;
      this.newTextBox_ClampF.Name = "newTextBox_ClampF";
      this.newTextBox_ClampF.ReadOnly = false;
      this.newTextBox_ClampF.Size = new Size(248, 23);
      this.newTextBox_ClampF.TabIndex = 228;
      this.newTextBox_ClampF.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_ClampF.TextBoxBackColor = Color.White;
      this.newTextBox_ClampF.TextForeColor = SystemColors.WindowText;
      this.newTextBox_ClampF.Value = "";
      this.newTextBox_TempAtFlowMin.BackColor = Color.White;
      this.newTextBox_TempAtFlowMin.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_TempAtFlowMin.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_TempAtFlowMin.IsDigit = false;
      this.newTextBox_TempAtFlowMin.Lines = new string[0];
      this.newTextBox_TempAtFlowMin.Location = new Point(252, 223);
      this.newTextBox_TempAtFlowMin.MultiLine = false;
      this.newTextBox_TempAtFlowMin.Name = "newTextBox_TempAtFlowMin";
      this.newTextBox_TempAtFlowMin.ReadOnly = false;
      this.newTextBox_TempAtFlowMin.Size = new Size(125, 23);
      this.newTextBox_TempAtFlowMin.TabIndex = 227;
      this.newTextBox_TempAtFlowMin.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_TempAtFlowMin.TextBoxBackColor = Color.White;
      this.newTextBox_TempAtFlowMin.TextForeColor = SystemColors.WindowText;
      this.newTextBox_TempAtFlowMin.Value = "";
      this.newTextBox_Totalmass.BackColor = Color.White;
      this.newTextBox_Totalmass.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Totalmass.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Totalmass.IsDigit = false;
      this.newTextBox_Totalmass.Lines = new string[0];
      this.newTextBox_Totalmass.Location = new Point(252, 179);
      this.newTextBox_Totalmass.MultiLine = false;
      this.newTextBox_Totalmass.Name = "newTextBox_Totalmass";
      this.newTextBox_Totalmass.ReadOnly = false;
      this.newTextBox_Totalmass.Size = new Size(248, 23);
      this.newTextBox_Totalmass.TabIndex = 226;
      this.newTextBox_Totalmass.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Totalmass.TextBoxBackColor = Color.White;
      this.newTextBox_Totalmass.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Totalmass.Value = "";
      this.newTextBox_ProjectedArea.BackColor = Color.White;
      this.newTextBox_ProjectedArea.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_ProjectedArea.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_ProjectedArea.IsDigit = false;
      this.newTextBox_ProjectedArea.Lines = new string[0];
      this.newTextBox_ProjectedArea.Location = new Point(252, 157);
      this.newTextBox_ProjectedArea.MultiLine = false;
      this.newTextBox_ProjectedArea.Name = "newTextBox_ProjectedArea";
      this.newTextBox_ProjectedArea.ReadOnly = false;
      this.newTextBox_ProjectedArea.Size = new Size(248, 23);
      this.newTextBox_ProjectedArea.TabIndex = 225;
      this.newTextBox_ProjectedArea.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_ProjectedArea.TextBoxBackColor = Color.White;
      this.newTextBox_ProjectedArea.TextForeColor = SystemColors.WindowText;
      this.newTextBox_ProjectedArea.Value = "";
      this.newTextBox_TimeEjectionTemp.BackColor = Color.White;
      this.newTextBox_TimeEjectionTemp.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_TimeEjectionTemp.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_TimeEjectionTemp.IsDigit = false;
      this.newTextBox_TimeEjectionTemp.Lines = new string[0];
      this.newTextBox_TimeEjectionTemp.Location = new Point(252, 113);
      this.newTextBox_TimeEjectionTemp.MultiLine = false;
      this.newTextBox_TimeEjectionTemp.Name = "newTextBox_TimeEjectionTemp";
      this.newTextBox_TimeEjectionTemp.ReadOnly = false;
      this.newTextBox_TimeEjectionTemp.Size = new Size(248, 23);
      this.newTextBox_TimeEjectionTemp.TabIndex = 224;
      this.newTextBox_TimeEjectionTemp.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_TimeEjectionTemp.TextBoxBackColor = Color.White;
      this.newTextBox_TimeEjectionTemp.TextForeColor = SystemColors.WindowText;
      this.newTextBox_TimeEjectionTemp.Value = "";
      this.newTextBox_CavitySurface.BackColor = Color.White;
      this.newTextBox_CavitySurface.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_CavitySurface.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_CavitySurface.IsDigit = false;
      this.newTextBox_CavitySurface.Lines = new string[0];
      this.newTextBox_CavitySurface.Location = new Point(252, 135);
      this.newTextBox_CavitySurface.MultiLine = false;
      this.newTextBox_CavitySurface.Name = "newTextBox_CavitySurface";
      this.newTextBox_CavitySurface.ReadOnly = false;
      this.newTextBox_CavitySurface.Size = new Size(248, 23);
      this.newTextBox_CavitySurface.TabIndex = 223;
      this.newTextBox_CavitySurface.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_CavitySurface.TextBoxBackColor = Color.White;
      this.newTextBox_CavitySurface.TextForeColor = SystemColors.WindowText;
      this.newTextBox_CavitySurface.Value = "";
      this.newTextBox_FilledTime.BackColor = Color.White;
      this.newTextBox_FilledTime.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_FilledTime.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_FilledTime.IsDigit = false;
      this.newTextBox_FilledTime.Lines = new string[0];
      this.newTextBox_FilledTime.Location = new Point(252, 91);
      this.newTextBox_FilledTime.MultiLine = false;
      this.newTextBox_FilledTime.Name = "newTextBox_FilledTime";
      this.newTextBox_FilledTime.ReadOnly = false;
      this.newTextBox_FilledTime.Size = new Size(248, 23);
      this.newTextBox_FilledTime.TabIndex = 222;
      this.newTextBox_FilledTime.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_FilledTime.TextBoxBackColor = Color.White;
      this.newTextBox_FilledTime.TextForeColor = SystemColors.WindowText;
      this.newTextBox_FilledTime.Value = "";
      this.label_ClampF.BackColor = Color.Lavender;
      this.label_ClampF.BorderStyle = BorderStyle.FixedSingle;
      this.label_ClampF.Location = new Point(5, 201);
      this.label_ClampF.Name = "label_ClampF";
      this.label_ClampF.Size = new Size(248, 23);
      this.label_ClampF.TabIndex = 221;
      this.label_ClampF.Text = "Clamp Force";
      this.label_ClampF.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Totalmass.BackColor = Color.Lavender;
      this.label_Totalmass.BorderStyle = BorderStyle.FixedSingle;
      this.label_Totalmass.Location = new Point(5, 179);
      this.label_Totalmass.Name = "label_Totalmass";
      this.label_Totalmass.Size = new Size(248, 23);
      this.label_Totalmass.TabIndex = 220;
      this.label_Totalmass.Text = "Total Mass";
      this.label_Totalmass.TextAlign = ContentAlignment.MiddleCenter;
      this.label_ProjectedArea.BackColor = Color.Lavender;
      this.label_ProjectedArea.BorderStyle = BorderStyle.FixedSingle;
      this.label_ProjectedArea.Location = new Point(5, 157);
      this.label_ProjectedArea.Name = "label_ProjectedArea";
      this.label_ProjectedArea.Size = new Size(248, 23);
      this.label_ProjectedArea.TabIndex = 219;
      this.label_ProjectedArea.Text = "Projected Area";
      this.label_ProjectedArea.TextAlign = ContentAlignment.MiddleCenter;
      this.label_ZComponent.BackColor = Color.Lavender;
      this.label_ZComponent.BorderStyle = BorderStyle.FixedSingle;
      this.label_ZComponent.Location = new Point(5, 311);
      this.label_ZComponent.Name = "label_ZComponent";
      this.label_ZComponent.Size = new Size(248, 23);
      this.label_ZComponent.TabIndex = 218;
      this.label_ZComponent.Text = "Range Z Component";
      this.label_ZComponent.TextAlign = ContentAlignment.MiddleCenter;
      this.label_YComponent.BackColor = Color.Lavender;
      this.label_YComponent.BorderStyle = BorderStyle.FixedSingle;
      this.label_YComponent.Location = new Point(5, 289);
      this.label_YComponent.Name = "label_YComponent";
      this.label_YComponent.Size = new Size(248, 23);
      this.label_YComponent.TabIndex = 217;
      this.label_YComponent.Text = "Range Y Component";
      this.label_YComponent.TextAlign = ContentAlignment.MiddleCenter;
      this.label_XComponent.BackColor = Color.Lavender;
      this.label_XComponent.BorderStyle = BorderStyle.FixedSingle;
      this.label_XComponent.Location = new Point(5, 267);
      this.label_XComponent.Name = "label_XComponent";
      this.label_XComponent.Size = new Size(248, 23);
      this.label_XComponent.TabIndex = 216;
      this.label_XComponent.Text = "Range X Component";
      this.label_XComponent.TextAlign = ContentAlignment.MiddleCenter;
      this.label_VShrinkage.BackColor = Color.Lavender;
      this.label_VShrinkage.BorderStyle = BorderStyle.FixedSingle;
      this.label_VShrinkage.Location = new Point(5, 245);
      this.label_VShrinkage.Name = "label_VShrinkage";
      this.label_VShrinkage.Size = new Size(248, 23);
      this.label_VShrinkage.TabIndex = 215;
      this.label_VShrinkage.Text = "Volumetric Shrinkage";
      this.label_VShrinkage.TextAlign = ContentAlignment.MiddleCenter;
      this.label_TempAtFlow.BackColor = Color.Lavender;
      this.label_TempAtFlow.BorderStyle = BorderStyle.FixedSingle;
      this.label_TempAtFlow.Location = new Point(5, 223);
      this.label_TempAtFlow.Name = "label_TempAtFlow";
      this.label_TempAtFlow.Size = new Size(248, 23);
      this.label_TempAtFlow.TabIndex = 214;
      this.label_TempAtFlow.Text = "Temperature at Flow Front";
      this.label_TempAtFlow.TextAlign = ContentAlignment.MiddleCenter;
      this.label_TimeEjectionTemp.BackColor = Color.Lavender;
      this.label_TimeEjectionTemp.BorderStyle = BorderStyle.FixedSingle;
      this.label_TimeEjectionTemp.Location = new Point(5, 113);
      this.label_TimeEjectionTemp.Name = "label_TimeEjectionTemp";
      this.label_TimeEjectionTemp.Size = new Size(248, 23);
      this.label_TimeEjectionTemp.TabIndex = 211;
      this.label_TimeEjectionTemp.Text = "Time to reach ejection Temp Cool";
      this.label_TimeEjectionTemp.TextAlign = ContentAlignment.MiddleCenter;
      this.label_FilledTime.BackColor = Color.Lavender;
      this.label_FilledTime.BorderStyle = BorderStyle.FixedSingle;
      this.label_FilledTime.Location = new Point(5, 91);
      this.label_FilledTime.Name = "label_FilledTime";
      this.label_FilledTime.Size = new Size(248, 23);
      this.label_FilledTime.TabIndex = 210;
      this.label_FilledTime.Text = "Filled Time";
      this.label_FilledTime.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Deflection.BackColor = Color.Lavender;
      this.label_Deflection.BorderStyle = BorderStyle.FixedSingle;
      this.label_Deflection.Location = new Point(5, 3);
      this.label_Deflection.Name = "label_Deflection";
      this.label_Deflection.Size = new Size(248, 23);
      this.label_Deflection.TabIndex = 207;
      this.label_Deflection.Text = "Range Deflection";
      this.label_Deflection.TextAlign = ContentAlignment.MiddleCenter;
      this.label_CavitySurface.BackColor = Color.Lavender;
      this.label_CavitySurface.BorderStyle = BorderStyle.FixedSingle;
      this.label_CavitySurface.Location = new Point(5, 135);
      this.label_CavitySurface.Name = "label_CavitySurface";
      this.label_CavitySurface.Size = new Size(248, 23);
      this.label_CavitySurface.TabIndex = 208;
      this.label_CavitySurface.Text = "Cavity Surface Temp Average";
      this.label_CavitySurface.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Sinkmark.BackColor = Color.Lavender;
      this.label_Sinkmark.BorderStyle = BorderStyle.FixedSingle;
      this.label_Sinkmark.Location = new Point(5, 69);
      this.label_Sinkmark.Name = "label_Sinkmark";
      this.label_Sinkmark.Size = new Size(248, 23);
      this.label_Sinkmark.TabIndex = 209;
      this.label_Sinkmark.Text = "Sink mark";
      this.label_Sinkmark.TextAlign = ContentAlignment.MiddleCenter;
      this.label_InjPressure.BackColor = Color.Lavender;
      this.label_InjPressure.BorderStyle = BorderStyle.FixedSingle;
      this.label_InjPressure.Location = new Point(5, 47);
      this.label_InjPressure.Name = "label_InjPressure";
      this.label_InjPressure.Size = new Size(248, 23);
      this.label_InjPressure.TabIndex = 212;
      this.label_InjPressure.Text = "Injection Pressure";
      this.label_InjPressure.TextAlign = ContentAlignment.MiddleCenter;
      this.label_CycleTime.BackColor = Color.Lavender;
      this.label_CycleTime.BorderStyle = BorderStyle.FixedSingle;
      this.label_CycleTime.Location = new Point(5, 25);
      this.label_CycleTime.Name = "label_CycleTime";
      this.label_CycleTime.Size = new Size(248, 23);
      this.label_CycleTime.TabIndex = 213;
      this.label_CycleTime.Text = "Cycle Time";
      this.label_CycleTime.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Sinkmark.BackColor = Color.White;
      this.newTextBox_Sinkmark.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Sinkmark.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Sinkmark.IsDigit = false;
      this.newTextBox_Sinkmark.Lines = new string[0];
      this.newTextBox_Sinkmark.Location = new Point(252, 69);
      this.newTextBox_Sinkmark.MultiLine = false;
      this.newTextBox_Sinkmark.Name = "newTextBox_Sinkmark";
      this.newTextBox_Sinkmark.ReadOnly = false;
      this.newTextBox_Sinkmark.Size = new Size(248, 23);
      this.newTextBox_Sinkmark.TabIndex = 206;
      this.newTextBox_Sinkmark.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Sinkmark.TextBoxBackColor = Color.White;
      this.newTextBox_Sinkmark.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Sinkmark.Value = "";
      this.newTextBox_CycleTime.BackColor = Color.White;
      this.newTextBox_CycleTime.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_CycleTime.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_CycleTime.IsDigit = false;
      this.newTextBox_CycleTime.Lines = new string[0];
      this.newTextBox_CycleTime.Location = new Point(252, 25);
      this.newTextBox_CycleTime.MultiLine = false;
      this.newTextBox_CycleTime.Name = "newTextBox_CycleTime";
      this.newTextBox_CycleTime.ReadOnly = false;
      this.newTextBox_CycleTime.Size = new Size(248, 23);
      this.newTextBox_CycleTime.TabIndex = 204;
      this.newTextBox_CycleTime.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_CycleTime.TextBoxBackColor = Color.White;
      this.newTextBox_CycleTime.TextForeColor = SystemColors.WindowText;
      this.newTextBox_CycleTime.Value = "";
      this.newTextBox_InjPressure.BackColor = Color.White;
      this.newTextBox_InjPressure.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_InjPressure.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_InjPressure.IsDigit = false;
      this.newTextBox_InjPressure.Lines = new string[0];
      this.newTextBox_InjPressure.Location = new Point(252, 47);
      this.newTextBox_InjPressure.MultiLine = false;
      this.newTextBox_InjPressure.Name = "newTextBox_InjPressure";
      this.newTextBox_InjPressure.ReadOnly = false;
      this.newTextBox_InjPressure.Size = new Size(248, 23);
      this.newTextBox_InjPressure.TabIndex = 203;
      this.newTextBox_InjPressure.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_InjPressure.TextBoxBackColor = Color.White;
      this.newTextBox_InjPressure.TextForeColor = SystemColors.WindowText;
      this.newTextBox_InjPressure.Value = "";
      this.newTextBox_Deflection.BackColor = Color.White;
      this.newTextBox_Deflection.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Deflection.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Deflection.IsDigit = false;
      this.newTextBox_Deflection.Lines = new string[0];
      this.newTextBox_Deflection.Location = new Point(252, 3);
      this.newTextBox_Deflection.MultiLine = false;
      this.newTextBox_Deflection.Name = "newTextBox_Deflection";
      this.newTextBox_Deflection.ReadOnly = false;
      this.newTextBox_Deflection.Size = new Size(248, 23);
      this.newTextBox_Deflection.TabIndex = 199;
      this.newTextBox_Deflection.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Deflection.TextBoxBackColor = Color.White;
      this.newTextBox_Deflection.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Deflection.Value = "";
      this.panel_TabMain.Controls.Add((Control) this.panel_TabButton);
      this.panel_TabMain.Controls.Add((Control) this.tabControl1);
      this.panel_TabMain.Location = new Point(5, 271);
      this.panel_TabMain.Name = "panel_TabMain";
      this.panel_TabMain.Size = new Size(495, 356);
      this.panel_TabMain.TabIndex = 78;
      this.panel_TabButton.BorderStyle = BorderStyle.FixedSingle;
      this.panel_TabButton.Controls.Add((Control) this.newButton_ProcessSetting);
      this.panel_TabButton.Controls.Add((Control) this.newButton_PredictionResult);
      this.panel_TabButton.Location = new Point(0, 0);
      this.panel_TabButton.Name = "panel_TabButton";
      this.panel_TabButton.Size = new Size(495, 24);
      this.panel_TabButton.TabIndex = 200;
      this.newButton_ProcessSetting.ButtonBackColor = Color.LightSteelBlue;
      this.newButton_ProcessSetting.ButtonText = "프로세스 설정";
      this.newButton_ProcessSetting.FlatBorderSize = 1;
      this.newButton_ProcessSetting.FlatStyle = FlatStyle.Flat;
      this.newButton_ProcessSetting.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_ProcessSetting.Image = (Image) null;
      this.newButton_ProcessSetting.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_ProcessSetting.Location = new Point(-1, -1);
      this.newButton_ProcessSetting.Name = "newButton_ProcessSetting";
      this.newButton_ProcessSetting.Size = new Size(248, 25);
      this.newButton_ProcessSetting.TabIndex = 6;
      this.newButton_ProcessSetting.TabStop = false;
      this.newButton_ProcessSetting.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_ProcessSetting.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_ProcessSetting.NewClick += new EventHandler(this.newButton_Setting_NewClick);
      this.newButton_PredictionResult.ButtonBackColor = Color.White;
      this.newButton_PredictionResult.ButtonText = "예측값";
      this.newButton_PredictionResult.FlatBorderSize = 1;
      this.newButton_PredictionResult.FlatStyle = FlatStyle.Flat;
      this.newButton_PredictionResult.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_PredictionResult.Image = (Image) null;
      this.newButton_PredictionResult.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_PredictionResult.Location = new Point(246, -1);
      this.newButton_PredictionResult.Name = "newButton_PredictionResult";
      this.newButton_PredictionResult.Size = new Size(248, 25);
      this.newButton_PredictionResult.TabIndex = 7;
      this.newButton_PredictionResult.TabStop = false;
      this.newButton_PredictionResult.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_PredictionResult.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_PredictionResult.NewClick += new EventHandler(this.newButton_Setting_NewClick);
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(505, 661);
      this.Controls.Add((Control) this.panel_TabMain);
      this.Controls.Add((Control) this.panel1);
      this.Controls.Add((Control) this.label_AIData);
      this.Controls.Add((Control) this.newButton_Apply);
      this.Controls.Add((Control) this.panel_Gate);
      this.Controls.Add((Control) this.label_Data);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmAISolution);
      this.ShowInTaskbar = false;
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "AI Solution";
      this.Load += new EventHandler(this.frmAISolution_Load);
      this.KeyDown += new KeyEventHandler(this.frmAISolution_KeyDown);
      this.panel_Gate.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_Gate).EndInit();
      this.panel1.ResumeLayout(false);
      this.panel1.PerformLayout();
      this.tabControl1.ResumeLayout(false);
      this.tabPage_ProcessSetting.ResumeLayout(false);
      this.tabPage_PredictionResult.ResumeLayout(false);
      this.panel_TabMain.ResumeLayout(false);
      this.panel_TabButton.ResumeLayout(false);
      this.ResumeLayout(false);
    }
  }
}
