﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlowLibrary.My.MyComputer
// Assembly: HDMoldFlowLibrary, Version=2.24.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 16E5D508-B892-4B5D-A02C-2C1930D27867
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMoldFlowLibrary.dll

using Microsoft.VisualBasic.Devices;
using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;

#nullable disable
namespace HDMoldFlowLibrary.My
{
  [GeneratedCode("MyTemplate", "11.0.0.0")]
  [EditorBrowsable(EditorBrowsableState.Never)]
  internal class MyComputer : Computer
  {
    [DebuggerHidden]
    [EditorBrowsable(EditorBrowsableState.Never)]
    public MyComputer()
    {
    }
  }
}
