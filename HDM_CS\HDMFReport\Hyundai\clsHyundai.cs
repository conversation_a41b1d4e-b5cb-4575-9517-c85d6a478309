﻿// Decompiled with JetBrains decompiler
// Type: HDMFReport.clsHyundai
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using HDLog4Net;
using HDMoldFlowLibrary;
using Microsoft.Office.Core;
using Microsoft.Office.Interop.PowerPoint;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace HDMFReport
{
  internal class clsHyundai : clsBase
  {
    private string m_strStudyName = string.Empty;

    public override void ExportReport(
      DataRow p_drStudy,
      string p_strLangType,
      clsHDMFLibDefine.Company p_enumCompany)
    {
      Dictionary<string, string> p_dicValue = new Dictionary<string, string>();
      Dictionary<string, string> p_dicView = new Dictionary<string, string>();
      Dictionary<string, string> dictionary = new Dictionary<string, string>();
      List<string> p_lstDelete = new List<string>();
      try
      {
        p_dicValue.Clear();
        p_dicView.Clear();
        clsHyundaiData.GetReportUser(p_drStudy, out p_dicValue, out p_dicView, out p_lstDelete);
        this.StartExport(p_drStudy, p_dicValue, p_dicView, p_lstDelete, p_enumCompany, p_strLangType);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]ExportReport):" + ex.Message));
      }
    }

    protected override void StartExport(
      DataRow p_drStudy,
      Dictionary<string, string> p_dicValue,
      Dictionary<string, string> p_dicView,
      List<string> p_lstDelete,
      clsHDMFLibDefine.Company p_enumCompany,
      string p_strLangType = "KOR")
    {
      Dictionary<string, string> analysisData = clsHyundaiData.GetAnalysisData(p_drStudy, p_dicValue, p_dicView);
      this.m_strStudyName = analysisData["Item[User]"];
      int num = 1;
      FileInfo fileInfo;
      while (true)
      {
        fileInfo = new FileInfo(clsReportDefine.g_diProject.Parent.FullName + "\\" + clsReportDefine.g_diProject.Name + "_Export\\" + p_drStudy["Name"] + "_" + DateTime.Now.ToString("yyMMdd") + "_Report_Rev" + (object) num + "." + clsReportDefine.g_dicExtension["PowerPoint"]);
        if (fileInfo.Exists)
          ++num;
        else
          break;
      }
      if (!fileInfo.Directory.Exists)
        fileInfo.Directory.Create();
      try
      {
        // ISSUE: variable of a compiler-generated type
        Presentations presentations = ((_Application) Activator.CreateInstance(Marshal.GetTypeFromCLSID(new Guid("91493441-5A91-11CF-8700-00AA0060263B")))).Presentations;
        FileInfo template = clsReportData.GetTemplate(p_enumCompany, p_strLangType);
        template.Refresh();
        if (template.Exists)
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          Presentation presentation = presentations.Open(template.FullName, MsoTriState.msoTrue, WithWindow: MsoTriState.msoFalse);
          List<Slide> allSlide = this.GetAllSlide(presentation);
          string empty = string.Empty;
          List<string> stringList = new List<string>();
          for (int index = 0; index < p_lstDelete.Count; ++index)
          {
            switch (p_lstDelete[index])
            {
              case "10":
                stringList.Add("Result5");
                break;
              case "11":
                stringList.Add("Result6");
                break;
              case "12":
                stringList.Add("Result7");
                break;
              case "13":
                stringList.Add("Result8");
                break;
              case "14":
                stringList.Add("Result9");
                stringList.Add("Result9_1");
                break;
              case "16":
                stringList.Add("Result9_2");
                stringList.Add("Result9_3");
                break;
              case "18":
                stringList.Add("Result9_4");
                stringList.Add("Result9_5");
                break;
              case "20":
                stringList.Add("Result9_6");
                stringList.Add("Result9_7");
                break;
              case "3":
                stringList.Add("Thickness");
                break;
              case "4":
                stringList.Add("Result1");
                break;
              case "5":
                stringList.Add("Result2");
                stringList.Add("Result2_1");
                stringList.Add("Result2_2");
                break;
              case "8":
                stringList.Add("Result3");
                break;
              case "9":
                stringList.Add("Result4");
                break;
            }
          }
          if (p_lstDelete.Count != 0)
          {
            List<Slide> slideList = new List<Slide>();
            foreach (string str in stringList)
            {
              string strData = str;
              // ISSUE: variable of a compiler-generated type
              Slide slide = allSlide.Where<Slide>((System.Func<Slide, bool>) (Temp => Temp.Name == strData)).FirstOrDefault<Slide>();
              if (slide != null)
                slideList.Add(slide);
            }
            for (int index = 0; index < slideList.Count; ++index)
            {
              // ISSUE: reference to a compiler-generated method
              slideList[index].Delete();
              allSlide.Remove(slideList[index]);
            }
          }
          if (!p_drStudy["Sequence"].ToString().Contains("Cool"))
          {
            List<Slide> slideList = new List<Slide>();
            foreach (Slide slide in allSlide)
            {
              if (slide.Name == "Result7" || slide.Name == "Result10")
                slideList.Add(slide);
            }
            for (int index = 0; index < slideList.Count; ++index)
            {
              // ISSUE: reference to a compiler-generated method
              slideList[index].Delete();
              allSlide.Remove(slideList[index]);
            }
          }
          if (p_drStudy["Sequence"].ToString().Contains("Warp"))
          {
            List<Slide> slideList = new List<Slide>();
            foreach (Slide slide in allSlide)
            {
              if (slide.Name.Contains("Result9_1"))
              {
                if (p_dicValue["DefAllType"] == "Best fit")
                  slideList.Add(slide);
              }
              else if (slide.Name.Contains("Result9_3"))
              {
                if (p_dicValue["DefXType"] == "Best fit")
                  slideList.Add(slide);
              }
              else if (slide.Name.Contains("Result9_5"))
              {
                if (p_dicValue["DefYType"] == "Best fit")
                  slideList.Add(slide);
              }
              else if (slide.Name.Contains("Result9_7") && p_dicValue["DefZType"] == "Best fit")
                slideList.Add(slide);
            }
            for (int index = 0; index < slideList.Count; ++index)
            {
              // ISSUE: reference to a compiler-generated method
              slideList[index].Delete();
              allSlide.Remove(slideList[index]);
            }
          }
          else
          {
            List<Slide> slideList = new List<Slide>();
            foreach (Slide slide in allSlide)
            {
              if (slide.Name.Contains("Result9"))
                slideList.Add(slide);
            }
            for (int index = 0; index < slideList.Count; ++index)
            {
              // ISSUE: reference to a compiler-generated method
              slideList[index].Delete();
              allSlide.Remove(slideList[index]);
            }
          }
          foreach (Slide p_slData in allSlide)
          {
            switch (p_slData.Name)
            {
              case "Conditions":
                this.SetSlide2(p_slData, this.GetSlide2(analysisData));
                continue;
              case "Result1":
                this.SetSlide4(p_slData, this.GetSlide4(analysisData));
                continue;
              case "Result10":
                this.SetSlide22(p_slData, this.GetSlide22(analysisData));
                continue;
              case "Result2":
                this.SetSlide5(p_slData, this.GetSlide5(analysisData));
                continue;
              case "Result2_1":
                this.SetSlide6(p_slData, this.GetSlide6(analysisData));
                continue;
              case "Result2_2":
                this.SetSlide7(p_slData, this.GetSlide7(analysisData));
                continue;
              case "Result3":
                this.SetSlide8(p_slData, this.GetSlide8(analysisData));
                continue;
              case "Result4":
                this.SetSlide9(p_slData, this.GetSlide9(analysisData));
                continue;
              case "Result5":
                this.SetSlide10(p_slData, this.GetSlide10(analysisData));
                continue;
              case "Result6":
                this.SetSlide11(p_slData, this.GetSlide11(analysisData));
                continue;
              case "Result7":
                this.SetSlide12(p_slData, this.GetSlide12(analysisData));
                continue;
              case "Result8":
                this.SetSlide13(p_slData, this.GetSlide13(analysisData));
                continue;
              case "Result9":
                this.SetSlide14(p_slData, this.GetSlide14(analysisData));
                continue;
              case "Result9_1":
                this.SetSlide15(p_slData, this.GetSlide15(analysisData));
                continue;
              case "Result9_2":
                this.SetSlide16(p_slData, this.GetSlide16(analysisData));
                continue;
              case "Result9_3":
                this.SetSlide17(p_slData, this.GetSlide17(analysisData));
                continue;
              case "Result9_4":
                this.SetSlide18(p_slData, this.GetSlide18(analysisData));
                continue;
              case "Result9_5":
                this.SetSlide19(p_slData, this.GetSlide19(analysisData));
                continue;
              case "Result9_6":
                this.SetSlide20(p_slData, this.GetSlide20(analysisData));
                continue;
              case "Result9_7":
                this.SetSlide21(p_slData, this.GetSlide21(analysisData));
                continue;
              case "Thickness":
                this.SetSlide3(p_slData, this.GetSlide3(analysisData));
                continue;
              case "Title":
                this.SetSlide1(p_slData, this.GetSlide1(analysisData));
                continue;
              default:
                continue;
            }
          }
          if (!fileInfo.Extension.Contains(".ppt"))
          {
            // ISSUE: reference to a compiler-generated method
            presentation.SaveAs(fileInfo.FullName.Replace(fileInfo.Extension, ".pptx"));
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            presentation.SaveAs(fileInfo.FullName);
          }
          // ISSUE: reference to a compiler-generated method
          presentation.Close();
          clsReportUtill.ReleaseComObject((object) presentation);
        }
        if (fileInfo.Extension.Contains(".ppt"))
          return;
        if (fileInfo.Exists)
          fileInfo.Delete();
        File.Move(fileInfo.FullName.Replace(fileInfo.Extension, ".pptx"), fileInfo.FullName);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]StartExport):" + ex.Message));
      }
    }

    private List<Slide> GetAllSlide(Presentation p_pptPre)
    {
      List<Slide> allSlide = new List<Slide>((IEnumerable<Slide>) p_pptPre.Slides.Cast<Slide>().ToArray<Slide>());
      try
      {
        for (int index = 0; index < allSlide.Count; ++index)
        {
          switch (index)
          {
            case 0:
              allSlide[index].Name = "Title";
              break;
            case 1:
              allSlide[index].Name = "Conditions";
              break;
            case 2:
              allSlide[index].Name = "Thickness";
              break;
            case 3:
              allSlide[index].Name = "Result1";
              break;
            case 4:
              allSlide[index].Name = "Result2";
              break;
            case 5:
              allSlide[index].Name = "Result2_1";
              break;
            case 6:
              allSlide[index].Name = "Result2_2";
              break;
            case 7:
              allSlide[index].Name = "Result3";
              break;
            case 8:
              allSlide[index].Name = "Result4";
              break;
            case 9:
              allSlide[index].Name = "Result5";
              break;
            case 10:
              allSlide[index].Name = "Result6";
              break;
            case 11:
              allSlide[index].Name = "Result7";
              break;
            case 12:
              allSlide[index].Name = "Result8";
              break;
            case 13:
              allSlide[index].Name = "Result9";
              break;
            case 14:
              allSlide[index].Name = "Result9_1";
              break;
            case 15:
              allSlide[index].Name = "Result9_2";
              break;
            case 16:
              allSlide[index].Name = "Result9_3";
              break;
            case 17:
              allSlide[index].Name = "Result9_4";
              break;
            case 18:
              allSlide[index].Name = "Result9_5";
              break;
            case 19:
              allSlide[index].Name = "Result9_6";
              break;
            case 20:
              allSlide[index].Name = "Result9_7";
              break;
            case 21:
              allSlide[index].Name = "Result10";
              break;
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetAllSlide):" + ex.Message));
      }
      return allSlide;
    }

    private Dictionary<string, string> GetSlide1(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide1 = new Dictionary<string, string>();
      try
      {
        slide1.Add("ReportType", p_dicData["ReportType[User]"]);
        slide1.Add("Date", p_dicData["Date[User]"]);
        slide1.Add("Engineer", p_dicData["Engineer[User]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetSlide1):" + ex.Message));
      }
      return slide1;
    }

    private Dictionary<string, string> GetSlide2(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide2 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Manufacturer[MF]")))
          slide2.Add("Manufacturer", p_dicData["Manufacturer[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TradeName[MF]")))
          slide2.Add("Trade Name", p_dicData["TradeName[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FamilyAbbreviation[MF]")))
          slide2.Add("Family Abbreviation", p_dicData["FamilyAbbreviation[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MI[MF]")))
          slide2.Add("MI", p_dicData["MI[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TransitionTemp[MF]")))
          slide2.Add("Transition Temp", p_dicData["TransitionTemp[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "EjectionTemp[MF]")))
          slide2.Add("Ejection Temp", p_dicData["EjectionTemp[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillingControl[MF]")))
          slide2.Add("Flow Rate", p_dicData["FillingControl[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MeltTemp[MF]")))
          slide2.Add("Melt Temp", p_dicData["MeltTemp[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CavityCoreTemp[MF]")))
          slide2.Add("Cavity Core Temp", p_dicData["CavityCoreTemp[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VPSwitchOver[MF]")))
          slide2.Add("V/P Switch-over", p_dicData["VPSwitchOver[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PackHoldingControl[MF]")))
          slide2.Add("Pack/Holding Control", p_dicData["PackHoldingControl[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "LastFillingTime[Log]")))
          slide2.Add("Fill Time", p_dicData["LastFillingTime[Log]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SpruePressure[Log]")))
          slide2.Add("Pressure", p_dicData["SpruePressure[Log]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ClampForce[Log]")))
          slide2.Add("Clamp Force", p_dicData["ClampForce[Log]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "LastGatePressure[Log]")))
          slide2.Add("Last Gate Pressure", p_dicData["LastGatePressure[Log]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureAtFlowFront[Plot]")))
        {
          string[] strArray = p_dicData["TemperatureAtFlowFront[Plot]"].Split('|');
          slide2.Add("Temperature At Flow Front", strArray[0] + "|" + strArray[1]);
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ElementNumber[Log]")))
          slide2.Add("Element Number", p_dicData["ElementNumber[Log]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MeshType[Log]")))
          slide2.Add("Mesh Type", p_dicData["MeshType[Log]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Sequence[MF]")))
          slide2.Add("Analysis Sequence", p_dicData["Sequence[MF]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PartVolume[Log]")))
          slide2.Add("Part Volume", p_dicData["PartVolume[Log]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ProjectedArea[Log]")))
          slide2.Add("Projected Area", p_dicData["ProjectedArea[Log]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetSlide2):" + ex.Message));
      }
      return slide2;
    }

    private Dictionary<string, string> GetSlide3(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide3 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Thickness[IMG]")))
          slide3.Add("Image", p_dicData["Thickness[IMG]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetSlide3):" + ex.Message));
      }
      return slide3;
    }

    private Dictionary<string, string> GetSlide4(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide4 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ModelRunner[IMG]")))
          slide4.Add("Image", p_dicData["ModelRunner[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Coord[Gate]")))
          slide4.Add("Gate Coord", p_dicData["Coord[Gate]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Dimension[Gate]")))
          slide4.Add("Gate Dimension", p_dicData["Dimension[Gate]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetSlide4):" + ex.Message));
      }
      return slide4;
    }

    private Dictionary<string, string> GetSlide5(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide5 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern1[IMG]")))
          slide5.Add("Image1", p_dicData["FlowPattern1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern2[IMG]")))
          slide5.Add("Image2", p_dicData["FlowPattern2[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern3[IMG]")))
          slide5.Add("Image3", p_dicData["FlowPattern3[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern4[IMG]")))
          slide5.Add("Image4", p_dicData["FlowPattern4[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillAnimation[IMG]")))
          slide5.Add("Animation", p_dicData["FillAnimation[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern1[Plot]")))
          slide5.Add("Time1", p_dicData["FlowPattern1[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern2[Plot]")))
          slide5.Add("Time2", p_dicData["FlowPattern2[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern3[Plot]")))
          slide5.Add("Time3", p_dicData["FlowPattern3[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern4[Plot]")))
          slide5.Add("Time4", p_dicData["FlowPattern4[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "LastFillingTime[Log]")))
          slide5.Add("Last Filling Time", p_dicData["LastFillingTime[Log]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "OpenTime[Gate]")))
          slide5.Add("Gate Open Time", p_dicData["OpenTime[Gate]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetSlide5):" + ex.Message));
      }
      return slide5;
    }

    private Dictionary<string, string> GetSlide6(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide6 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern5[IMG]")))
          slide6.Add("Image1", p_dicData["FlowPattern5[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern6[IMG]")))
          slide6.Add("Image2", p_dicData["FlowPattern6[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern7[IMG]")))
          slide6.Add("Image3", p_dicData["FlowPattern7[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern8[IMG]")))
          slide6.Add("Image4", p_dicData["FlowPattern8[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern5[Plot]")))
          slide6.Add("Time1", p_dicData["FlowPattern5[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern6[Plot]")))
          slide6.Add("Time2", p_dicData["FlowPattern6[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern7[Plot]")))
          slide6.Add("Time3", p_dicData["FlowPattern7[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern8[Plot]")))
          slide6.Add("Time4", p_dicData["FlowPattern8[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "LastFillingTime[Log]")))
          slide6.Add("Last Filling Time", p_dicData["LastFillingTime[Log]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "OpenTime[Gate]")))
          slide6.Add("Gate Open Time", p_dicData["OpenTime[Gate]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetSlide6):" + ex.Message));
      }
      return slide6;
    }

    private Dictionary<string, string> GetSlide7(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide7 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern1[IMG]")))
          slide7.Add("Image1", p_dicData["FlowPattern1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern2[IMG]")))
          slide7.Add("Image2", p_dicData["FlowPattern2[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern3[IMG]")))
          slide7.Add("Image3", p_dicData["FlowPattern3[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern4[IMG]")))
          slide7.Add("Image4", p_dicData["FlowPattern4[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern5[IMG]")))
          slide7.Add("Image5", p_dicData["FlowPattern5[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern6[IMG]")))
          slide7.Add("Image6", p_dicData["FlowPattern6[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern7[IMG]")))
          slide7.Add("Image7", p_dicData["FlowPattern7[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern8[IMG]")))
          slide7.Add("Image8", p_dicData["FlowPattern8[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern1[Plot]")))
          slide7.Add("Time1", p_dicData["FlowPattern1[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern2[Plot]")))
          slide7.Add("Time2", p_dicData["FlowPattern2[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern3[Plot]")))
          slide7.Add("Time3", p_dicData["FlowPattern3[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern4[Plot]")))
          slide7.Add("Time4", p_dicData["FlowPattern4[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern5[Plot]")))
          slide7.Add("Time5", p_dicData["FlowPattern5[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern6[Plot]")))
          slide7.Add("Time6", p_dicData["FlowPattern6[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern7[Plot]")))
          slide7.Add("Time7", p_dicData["FlowPattern7[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FlowPattern8[Plot]")))
          slide7.Add("Time8", p_dicData["FlowPattern8[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "LastFillingTime[Log]")))
          slide7.Add("Last Filling Time", p_dicData["LastFillingTime[Log]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "OpenTime[Gate]")))
          slide7.Add("Gate Open Time", p_dicData["OpenTime[Gate]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetSlide7):" + ex.Message));
      }
      return slide7;
    }

    private Dictionary<string, string> GetSlide8(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide8 = new Dictionary<string, string>();
      string empty = string.Empty;
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "WeldLine[IMG]")))
          slide8.Add("Image1", p_dicData["WeldLine[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "WeldLineOverlap[IMG]")))
          slide8.Add("Image2", p_dicData["WeldLineOverlap[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "WeldLineScaling[IMG]")))
          slide8.Add("Image3", p_dicData["WeldLineScaling[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "WeldLine[User]")))
          slide8.Add("Weld Line", p_dicData["WeldLine[User]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "WeldLineScaling[Plot]")))
          slide8.Add("Weld Line Temp", p_dicData["WeldLineScaling[Plot]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetSlide8):" + ex.Message));
      }
      return slide8;
    }

    private Dictionary<string, string> GetSlide9(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide9 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SinkMark1[IMG]")))
          slide9.Add("Image1", p_dicData["SinkMark1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SinkMark2[IMG]")))
          slide9.Add("Image2", p_dicData["SinkMark2[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SinkMarkOverlayTemp[IMG]")))
          slide9.Add("Image3", p_dicData["SinkMarkOverlayTemp[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SinkMarkAll[Plot]")))
        {
          string[] strArray = p_dicData["SinkMarkAll[Plot]"].Split('|');
          slide9.Add("Sink Mark1", strArray[0] + "~" + strArray[1]);
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SinkMark[Plot]")))
        {
          string[] strArray = p_dicData["SinkMark[Plot]"].Split('|');
          slide9.Add("Sink Mark2", strArray[0] + "~" + strArray[1]);
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SinkMark[User]")))
          slide9.Add("SinkMark", p_dicData["SinkMark[User]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetSlide9):" + ex.Message));
      }
      return slide9;
    }

    private Dictionary<string, string> GetSlide10(Dictionary<string, string> p_dicData)
    {
      StringBuilder stringBuilder = new StringBuilder();
      Dictionary<string, string> slide10 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VolumetricShrinkage2[IMG]")))
          slide10.Add("Image1", p_dicData["VolumetricShrinkage2[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VolumetricShrinkage3[IMG]")))
          slide10.Add("Image2", p_dicData["VolumetricShrinkage3[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VolumetricShrinkage2[Plot]")))
        {
          string[] strArray = p_dicData["VolumetricShrinkage2[Plot]"].Split('|');
          slide10.Add("Shrinkage1", strArray[0] + "~" + strArray[1]);
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VolumetricShrinkage3[Plot]")))
        {
          string[] strArray = p_dicData["VolumetricShrinkage3[Plot]"].Split('|');
          slide10.Add("Shrinkage2", strArray[0] + "~" + strArray[1]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetSlide10):" + ex.Message));
      }
      return slide10;
    }

    private Dictionary<string, string> GetSlide11(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide11 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFraction1[IMG]")))
          slide11.Add("Image1", p_dicData["FrozenLayerFraction1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFraction2[IMG]")))
          slide11.Add("Image2", p_dicData["FrozenLayerFraction2[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFraction3[IMG]")))
          slide11.Add("Image3", p_dicData["FrozenLayerFraction3[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFraction4[IMG]")))
          slide11.Add("Image4", p_dicData["FrozenLayerFraction4[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFraction1[Plot]")))
          slide11.Add("Time1", p_dicData["FrozenLayerFraction1[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFraction2[Plot]")))
          slide11.Add("Time2", p_dicData["FrozenLayerFraction2[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFraction3[Plot]")))
          slide11.Add("Time3", p_dicData["FrozenLayerFraction3[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenLayerFraction4[Plot]")))
          slide11.Add("Time4", p_dicData["FrozenLayerFraction4[Plot]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetSlide11):" + ex.Message));
      }
      return slide11;
    }

    private Dictionary<string, string> GetSlide12(Dictionary<string, string> p_dicData)
    {
      StringBuilder stringBuilder = new StringBuilder();
      Dictionary<string, string> slide12 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperature[IMG]")))
          slide12.Add("Image1", p_dicData["CircuitCoolantTemperature[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MoldTempCavity[IMG]")))
          slide12.Add("Image2", p_dicData["MoldTempCavity[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MoldTempCore[IMG]")))
          slide12.Add("Image3", p_dicData["MoldTempCore[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperatureCavity[Log]")) && p_dicData["CircuitCoolantTemperatureCavity[Log]"] != null && p_dicData["CircuitCoolantTemperatureCavity[Log]"] != "")
          slide12.Add("Circuit Coolant Temperature(Cavity)", p_dicData["CircuitCoolantTemperatureCavity[Log]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperatureCore[Log]")) && p_dicData["CircuitCoolantTemperatureCore[Log]"] != null && p_dicData["CircuitCoolantTemperatureCore[Log]"] != "")
          slide12.Add("Circuit Coolant Temperature(Core)", p_dicData["CircuitCoolantTemperatureCore[Log]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MoldTempCavityCore[Plot]")))
          slide12.Add("Temperature Mold", p_dicData["MoldTempCavityCore[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cooling[User]")))
          slide12.Add("Cooling", p_dicData["Cooling[User]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetSlide12):" + ex.Message));
      }
      return slide12;
    }

    private Dictionary<string, string> GetSlide13(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide13 = new Dictionary<string, string>();
      double num1 = 0.0;
      double num2 = 0.0;
      double num3 = 0.0;
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperature1[IMG]")))
          slide13.Add("Image1", p_dicData["TimeToReachEjectionTemperature1[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperature2[IMG]")))
          slide13.Add("Image2", p_dicData["TimeToReachEjectionTemperature2[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperature[Plot]")))
          slide13.Add("Time To Reach Ejection Temperature", p_dicData["TimeToReachEjectionTemperature[Plot]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FillingTime[User]")))
        {
          num1 = Math.Round(clsReportUtill.ConvertToDouble(p_dicData["FillingTime[User]"]), 2);
          slide13.Add("Injection Time", num1.ToString());
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "PackingTime[User]")))
        {
          num2 = Math.Round(clsReportUtill.ConvertToDouble(p_dicData["PackingTime[User]"]), 2);
          slide13.Add("Pack/Holding Time", num2.ToString());
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "FrozenTime100%[Log]")) && p_dicData["FrozenTime100%[Log]"] != string.Empty)
          num3 = Math.Round(clsReportUtill.ConvertToDouble(p_dicData["FrozenTime100%[Log]"]) * 0.8, 2);
        else if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperature[Plot]")))
          num3 = Math.Round(clsReportUtill.ConvertToDouble(p_dicData["TimeToReachEjectionTemperature[Plot]"]), 2);
        double num4 = num3 - num1 - num2 <= 0.0 ? 0.0 : Math.Round(num3 - num1 - num2, 2);
        slide13.Add("Cooling Time", num4.ToString());
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Schizonepeta[User]")))
          slide13.Add("Schizonepeta", p_dicData["Schizonepeta[User]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetSlide13):" + ex.Message));
      }
      return slide13;
    }

    private Dictionary<string, string> GetSlide14(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide14 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll1_BestFit[IMG]")))
          slide14.Add("Image1", p_dicData["DefAll1_BestFit[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll2_BestFit[IMG]")))
          slide14.Add("Image2", p_dicData["DefAll2_BestFit[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll3_BestFit[IMG]")))
          slide14.Add("Image3", p_dicData["DefAll3_BestFit[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll_BestFit[Plot]")))
          slide14.Add("DefAll", p_dicData["DefAll_BestFit[Plot]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetSlide14):" + ex.Message));
      }
      return slide14;
    }

    private Dictionary<string, string> GetSlide15(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide15 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll1_AnchorPlan[IMG]")))
          slide15.Add("Image1", p_dicData["DefAll1_AnchorPlan[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll2_AnchorPlan[IMG]")))
          slide15.Add("Image2", p_dicData["DefAll2_AnchorPlan[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll3_AnchorPlan[IMG]")))
          slide15.Add("Image3", p_dicData["DefAll3_AnchorPlan[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll_AnchorPlan[Plot]")))
          slide15.Add("DefAll", p_dicData["DefAll_AnchorPlan[Plot]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetSlide15):" + ex.Message));
      }
      return slide15;
    }

    private Dictionary<string, string> GetSlide16(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide16 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX1_BestFit[IMG]")))
          slide16.Add("Image1", p_dicData["DefX1_BestFit[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX2_BestFit[IMG]")))
          slide16.Add("Image2", p_dicData["DefX2_BestFit[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX3_BestFit[IMG]")))
          slide16.Add("Image3", p_dicData["DefX3_BestFit[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX_BestFit[Plot]")))
          slide16.Add("DefX", p_dicData["DefX_BestFit[Plot]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetSlide16):" + ex.Message));
      }
      return slide16;
    }

    private Dictionary<string, string> GetSlide17(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide17 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX1_AnchorPlan[IMG]")))
          slide17.Add("Image1", p_dicData["DefX1_AnchorPlan[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX2_AnchorPlan[IMG]")))
          slide17.Add("Image2", p_dicData["DefX2_AnchorPlan[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX3_AnchorPlan[IMG]")))
          slide17.Add("Image3", p_dicData["DefX3_AnchorPlan[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX_AnchorPlan[Plot]")))
          slide17.Add("DefX", p_dicData["DefX_AnchorPlan[Plot]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetSlide17):" + ex.Message));
      }
      return slide17;
    }

    private Dictionary<string, string> GetSlide18(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide18 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY1_BestFit[IMG]")))
          slide18.Add("Image1", p_dicData["DefY1_BestFit[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY2_BestFit[IMG]")))
          slide18.Add("Image2", p_dicData["DefY2_BestFit[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY3_BestFit[IMG]")))
          slide18.Add("Image3", p_dicData["DefY3_BestFit[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY_BestFit[Plot]")))
          slide18.Add("DefY", p_dicData["DefY_BestFit[Plot]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetSlide18):" + ex.Message));
      }
      return slide18;
    }

    private Dictionary<string, string> GetSlide19(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide19 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY1_AnchorPlan[IMG]")))
          slide19.Add("Image1", p_dicData["DefY1_AnchorPlan[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY2_AnchorPlan[IMG]")))
          slide19.Add("Image2", p_dicData["DefY2_AnchorPlan[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY3_AnchorPlan[IMG]")))
          slide19.Add("Image3", p_dicData["DefY3_AnchorPlan[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY_AnchorPlan[Plot]")))
          slide19.Add("DefY", p_dicData["DefY_AnchorPlan[Plot]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetSlide19):" + ex.Message));
      }
      return slide19;
    }

    private Dictionary<string, string> GetSlide20(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide20 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ1_BestFit[IMG]")))
          slide20.Add("Image1", p_dicData["DefZ1_BestFit[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ2_BestFit[IMG]")))
          slide20.Add("Image2", p_dicData["DefZ2_BestFit[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ3_BestFit[IMG]")))
          slide20.Add("Image3", p_dicData["DefZ3_BestFit[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ_BestFit[Plot]")))
          slide20.Add("DefZ", p_dicData["DefZ_BestFit[Plot]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetSlide20):" + ex.Message));
      }
      return slide20;
    }

    private Dictionary<string, string> GetSlide21(Dictionary<string, string> p_dicData)
    {
      Dictionary<string, string> slide21 = new Dictionary<string, string>();
      try
      {
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ1_AnchorPlan[IMG]")))
          slide21.Add("Image1", p_dicData["DefZ1_AnchorPlan[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ2_AnchorPlan[IMG]")))
          slide21.Add("Image2", p_dicData["DefZ2_AnchorPlan[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ3_AnchorPlan[IMG]")))
          slide21.Add("Image3", p_dicData["DefZ3_AnchorPlan[IMG]"]);
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ_AnchorPlan[Plot]")))
          slide21.Add("DefZ", p_dicData["DefZ_AnchorPlan[Plot]"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetSlide21):" + ex.Message));
      }
      return slide21;
    }

    private Dictionary<string, string> GetSlide22(Dictionary<string, string> p_dicData)
    {
      string empty = string.Empty;
      string str = "|";
      StringBuilder stringBuilder = new StringBuilder();
      Dictionary<string, string> slide22 = new Dictionary<string, string>();
      try
      {
        stringBuilder.Clear();
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperatureCavity[Log]")))
        {
          if (p_dicData["CircuitCoolantTemperatureCavity[Log]"] == null || p_dicData["CircuitCoolantTemperatureCavity[Log]"] == "")
          {
            stringBuilder.Append("||");
          }
          else
          {
            double num = clsReportUtill.ConvertToDouble(p_dicData["CircuitCoolantTemperatureCavity[Log]"].Split('|')[2]);
            stringBuilder.Append(num);
            if (num <= 3.0)
              stringBuilder.Append("|NO Problem|OK");
            else
              stringBuilder.Append("|Discussion|NG");
          }
          slide22.Add("Circuit Coolant Temperature(Cavity)", stringBuilder.ToString());
        }
        stringBuilder.Clear();
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitCoolantTemperatureCore[Log]")))
        {
          if (p_dicData["CircuitCoolantTemperatureCore[Log]"] == null || p_dicData["CircuitCoolantTemperatureCore[Log]"] == "")
          {
            stringBuilder.Append("||");
          }
          else
          {
            double num = clsReportUtill.ConvertToDouble(p_dicData["CircuitCoolantTemperatureCore[Log]"].Split('|')[2]);
            stringBuilder.Append(num);
            if (num <= 3.0)
              stringBuilder.Append("|NO Problem|OK");
            else
              stringBuilder.Append("|Discussion|NG");
          }
          slide22.Add("Circuit Coolant Temperature(Core)", stringBuilder.ToString());
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cooling[User]")))
        {
          string[] strArray = p_dicData["Cooling[User]"].Split('|');
          str = (!(strArray[0] == "OK") ? (!(strArray[0] == "Discussion") ? "Problem" : "Discussion") : "NO Problem") + "|" + strArray[0];
        }
        stringBuilder.Clear();
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitFlowRate[Plot]")) && p_dicData["CircuitFlowRate[Plot]"] != null && p_dicData["CircuitFlowRate[Plot]"] != "")
          stringBuilder.Append(p_dicData["CircuitFlowRate[Plot]"]);
        stringBuilder.Append("|");
        stringBuilder.Append(str);
        slide22.Add("Circuit Flow Rate", stringBuilder.ToString());
        stringBuilder.Clear();
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "CircuitPressure[Plot]")) && p_dicData["CircuitPressure[Plot]"] != null && p_dicData["CircuitPressure[Plot]"] != "")
          stringBuilder.Append(p_dicData["CircuitPressure[Plot]"]);
        stringBuilder.Append("|");
        stringBuilder.Append(str);
        slide22.Add("Circuit Pressure", stringBuilder.ToString());
        stringBuilder.Clear();
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TimeToReachEjectionTemperature[Plot]")))
        {
          if (p_dicData["TimeToReachEjectionTemperature[Plot]"] == null || p_dicData["TimeToReachEjectionTemperature[Plot]"] == "")
          {
            stringBuilder.Append("||");
          }
          else
          {
            double num1 = clsReportUtill.ConvertToDouble(p_dicData["TimeToReachEjectionTemperature[Plot]"]);
            stringBuilder.Append(num1);
            double num2 = !(p_dicData["FrozenTime95%[Log]"] == string.Empty) ? clsReportUtill.ConvertToDouble(p_dicData["FrozenTime95%[Log]"]) : num1;
            if (num1 >= num2)
              stringBuilder.Append("|NO Problem|OK");
            else
              stringBuilder.Append("|Discussion|NG");
          }
          slide22.Add("Time To Reach Ejection Temperature", stringBuilder.ToString());
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperatureMold[Plot]")))
        {
          if (p_dicData["TemperatureMold[Plot]"] == null || p_dicData["TemperatureMold[Plot]"] == "")
            slide22.Add("Temperature, Mold", "||");
          else
            slide22.Add("Temperature, Mold", p_dicData["TemperatureMold[Plot]"].Split('|')[1] + "||");
        }
        if (p_dicData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "TemperaturePart[Plot]")))
        {
          if (p_dicData["TemperaturePart[Plot]"] == null || p_dicData["TemperaturePart[Plot]"] == "")
            slide22.Add("Temperature, Part", "||");
          else
            slide22.Add("Temperature, Part", p_dicData["TemperaturePart[Plot]"].Split('|')[1] + "||");
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]GetSlide22):" + ex.Message));
      }
      return slide22;
    }

    private void SetSlide1(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S1_Title 1")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName + " 해석결과");
          textRange.Font.Bold = MsoTriState.msoTrue;
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S1_Table 1")).FirstOrDefault<Shape>().Table;
        if (table != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "ReportType")))
        {
          int num = clsReportUtill.ConvertToInt(p_dicSData["ReportType"]);
          for (int index = 0; index < 3; ++index)
          {
            string NewText = index != num ? "○" : "●";
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = table.Rows[index + 1].Cells[1].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter(NewText);
          }
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S1_TextBox 1")).FirstOrDefault<Shape>();
        if (shape2 != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Date")) && p_dicSData["Date"] != string.Empty)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape2.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(p_dicSData["Date"]);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape3 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S1_TextBox 2")).FirstOrDefault<Shape>();
        if (shape3 == null || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Engineer")) || !(p_dicSData["Engineer"] != string.Empty))
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape3.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange1.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange1.InsertAfter(p_dicSData["Engineer"]);
        textRange1.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetSlide1):" + ex.Message));
      }
    }

    private void SetSlide2(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      // ISSUE: variable of a compiler-generated type
      TextRange textRange1 = (TextRange) null;
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      string empty3 = string.Empty;
      string empty4 = string.Empty;
      string empty5 = string.Empty;
      StringBuilder stringBuilder = new StringBuilder();
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S2_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange2 = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange2.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange2.InsertAfter(this.m_strStudyName);
          textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        // ISSUE: variable of a compiler-generated type
        Table table1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S2_Table 1")).FirstOrDefault<Shape>().Table;
        if (table1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange3 = table1.Rows[1].Cells[3].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange3.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Manufacturer")))
          {
            // ISSUE: reference to a compiler-generated method
            textRange3.InsertAfter(p_dicSData["Manufacturer"]);
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange4 = table1.Rows[2].Cells[3].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange4.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Trade Name")))
          {
            // ISSUE: reference to a compiler-generated method
            textRange4.InsertAfter(p_dicSData["Trade Name"]);
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange5 = table1.Rows[3].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange5.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Family Abbreviation")))
          {
            // ISSUE: reference to a compiler-generated method
            textRange5.InsertAfter(p_dicSData["Family Abbreviation"]);
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange6 = table1.Rows[3].Cells[4].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange6.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Transition Temp")))
          {
            // ISSUE: reference to a compiler-generated method
            textRange6.InsertAfter(p_dicSData["Transition Temp"] + "℃");
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange7 = table1.Rows[4].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange7.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "MI")))
          {
            string NewText = !(p_dicSData["MI"] == "0") ? p_dicSData["MI"] + "g/10min" : "-";
            // ISSUE: reference to a compiler-generated method
            textRange7.InsertAfter(NewText);
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange8 = table1.Rows[4].Cells[4].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange8.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Ejection Temp")))
          {
            // ISSUE: reference to a compiler-generated method
            textRange8.InsertAfter(p_dicSData["Ejection Temp"] + "℃");
          }
        }
        // ISSUE: variable of a compiler-generated type
        Table table2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S2_Table 2")).FirstOrDefault<Shape>().Table;
        if (table2 != null)
        {
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Flow Rate")))
          {
            string[] strArray = p_dicSData["Flow Rate"].Replace("\r\n", "").Split(new string[1]
            {
              "|"
            }, StringSplitOptions.RemoveEmptyEntries);
            string NewText1 = string.Empty;
            string NewText2 = string.Empty;
            if (strArray.Length != 0)
            {
              switch (strArray[0])
              {
                case "Injection Time":
                  NewText1 = "사출시간(Injection time)";
                  NewText2 = strArray[1] + strArray[2];
                  break;
                case "Flow rate":
                  NewText1 = "사출속도(Flow rate)";
                  NewText2 = strArray[1] + strArray[2];
                  break;
                case "Relative ram speed profile(Injection)":
                  NewText1 = "사출시간(Injection time)";
                  NewText2 = strArray[1] + strArray[2];
                  break;
                case "Relative ram speed profile(flow)":
                  NewText1 = "사출속도(Flow rate)";
                  NewText2 = strArray[1] + strArray[2];
                  break;
                case "Absolute ram speed profile":
                  NewText1 = "다단 사출" + Environment.NewLine + "사출조건표 참고";
                  break;
                case "Automatic":
                  NewText1 = "자동(Automatic)";
                  break;
              }
              // ISSUE: variable of a compiler-generated type
              TextRange textRange9 = table2.Rows[1].Cells[1].Shape.TextFrame.TextRange;
              // ISSUE: reference to a compiler-generated method
              textRange9.Delete();
              // ISSUE: reference to a compiler-generated method
              textRange9.InsertAfter(NewText1);
              if (NewText2 != string.Empty)
              {
                // ISSUE: variable of a compiler-generated type
                TextRange textRange10 = table2.Rows[1].Cells[2].Shape.TextFrame.TextRange;
                // ISSUE: reference to a compiler-generated method
                textRange10.Delete();
                // ISSUE: reference to a compiler-generated method
                textRange10.InsertAfter(NewText2);
              }
            }
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange11 = table2.Rows[2].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange11.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Melt Temp")))
          {
            // ISSUE: reference to a compiler-generated method
            textRange11.InsertAfter(p_dicSData["Melt Temp"] + "℃");
          }
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cavity Core Temp")))
          {
            string[] strArray = p_dicSData["Cavity Core Temp"].Split('|');
            // ISSUE: variable of a compiler-generated type
            TextRange textRange12 = table2.Rows[1].Cells[6].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange12.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange12.InsertAfter(strArray[0] + "℃");
            // ISSUE: variable of a compiler-generated type
            TextRange textRange13 = table2.Rows[3].Cells[6].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange13.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange13.InsertAfter(strArray[1] + "℃");
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange14 = table2.Rows[4].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange14.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "V/P Switch-over")))
          {
            // ISSUE: reference to a compiler-generated method
            textRange14.InsertAfter(p_dicSData["V/P Switch-over"] + " 충진시");
          }
          stringBuilder.Clear();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Pack/Holding Control")))
          {
            string[] strArray1 = p_dicSData["Pack/Holding Control"].Replace("\r\n", "").Split('/');
            string[] strArray2 = strArray1[0].Replace("\r\n", "").Split('|');
            string str1 = strArray2[1];
            string str2 = strArray2[3];
            if (strArray1.Length > 1)
            {
              for (int index = 1; index < strArray1.Length && index <= 3; ++index)
              {
                string[] strArray3 = strArray1[index].Replace("\r\n", "").Split('|');
                // ISSUE: variable of a compiler-generated type
                TextRange textRange15 = table2.Rows[5 + index].Cells[2].Shape.TextFrame.TextRange;
                // ISSUE: reference to a compiler-generated method
                textRange15.Delete();
                // ISSUE: reference to a compiler-generated method
                textRange15.InsertAfter(strArray3[0] + str1);
                // ISSUE: variable of a compiler-generated type
                TextRange textRange16 = table2.Rows[5 + index].Cells[5].Shape.TextFrame.TextRange;
                // ISSUE: reference to a compiler-generated method
                textRange16.Delete();
                // ISSUE: reference to a compiler-generated method
                textRange16.InsertAfter(strArray3[1] + str2);
              }
            }
          }
        }
        // ISSUE: variable of a compiler-generated type
        Table table3 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S2_Table 3")).FirstOrDefault<Shape>().Table;
        if (table3 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange17 = table3.Rows[1].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange17.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Fill Time")) && !string.IsNullOrEmpty(p_dicSData["Fill Time"]))
          {
            string[] strArray = p_dicSData["Fill Time"].Split(new string[1]
            {
              "|"
            }, StringSplitOptions.RemoveEmptyEntries);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange18 = textRange17.InsertAfter(strArray[0] + "s");
            if (strArray.Length > 1)
            {
              // ISSUE: reference to a compiler-generated method
              textRange1 = textRange18.InsertAfter(" + Short shot");
            }
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange19 = table3.Rows[2].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange19.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Pressure")))
          {
            // ISSUE: reference to a compiler-generated method
            textRange1 = textRange19.InsertAfter(p_dicSData["Pressure"] + "MPa");
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange20 = table3.Rows[3].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange20.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Clamp Force")))
          {
            // ISSUE: reference to a compiler-generated method
            textRange1 = textRange20.InsertAfter(p_dicSData["Clamp Force"] + "t");
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange21 = table3.Rows[4].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange21.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Last Gate Pressure")))
          {
            string NewText = !(p_dicSData["Last Gate Pressure"] != string.Empty) ? "-" : p_dicSData["Last Gate Pressure"] + "MPa";
            // ISSUE: reference to a compiler-generated method
            textRange21.InsertAfter(NewText);
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange22 = table3.Rows[5].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange22.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Temperature At Flow Front")))
          {
            string[] strArray = p_dicSData["Temperature At Flow Front"].Split('|');
            // ISSUE: reference to a compiler-generated method
            textRange1 = textRange22.InsertAfter(strArray[0] + "℃ ~ " + strArray[1] + "℃");
          }
        }
        // ISSUE: variable of a compiler-generated type
        Table table4 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S2_Table 4")).FirstOrDefault<Shape>().Table;
        if (table4 == null)
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange23 = table4.Rows[1].Cells[2].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange23.Delete();
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Element Number")))
        {
          // ISSUE: reference to a compiler-generated method
          textRange23.InsertAfter(p_dicSData["Element Number"]);
        }
        // ISSUE: variable of a compiler-generated type
        TextRange textRange24 = table4.Rows[2].Cells[2].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange24.Delete();
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Mesh Type")))
        {
          // ISSUE: reference to a compiler-generated method
          textRange24.InsertAfter(p_dicSData["Mesh Type"]);
        }
        // ISSUE: variable of a compiler-generated type
        TextRange textRange25 = table4.Rows[3].Cells[2].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange25.Delete();
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Analysis Sequence")))
        {
          // ISSUE: reference to a compiler-generated method
          textRange25.InsertAfter(p_dicSData["Analysis Sequence"]);
        }
        // ISSUE: variable of a compiler-generated type
        TextRange textRange26 = table4.Rows[4].Cells[2].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange26.Delete();
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Part Volume")))
        {
          // ISSUE: reference to a compiler-generated method
          textRange26.InsertBefore(p_dicSData["Part Volume"] + "㎤");
        }
        // ISSUE: variable of a compiler-generated type
        TextRange textRange27 = table4.Rows[5].Cells[2].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange27.Delete();
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Projected Area")))
          return;
        // ISSUE: reference to a compiler-generated method
        textRange27.InsertBefore(p_dicSData["Projected Area"] + "㎤");
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetSlide2):" + ex.Message));
      }
    }

    private void SetSlide3(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S3_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
          return;
        // ISSUE: reference to a compiler-generated method
        p_slData.Shapes.AddPicture(p_dicSData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 33.44882f, 64.34646f, 713.7638f, 445.6063f);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetSlide3):" + ex.Message));
      }
    }

    private void SetSlide4(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      string empty3 = string.Empty;
      string empty4 = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S4_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image"], MsoTriState.msoFalse, MsoTriState.msoTrue, 293.102356f, 77.95276f, 481.89f, 306.1417f);
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S4_Table 1")).FirstOrDefault<Shape>().Table;
        if (table == null)
          return;
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Gate Coord")))
        {
          string[] strArray1 = p_dicSData["Gate Coord"].Split(new string[1]
          {
            "/"
          }, StringSplitOptions.RemoveEmptyEntries);
          for (int index = 0; index < strArray1.Length; ++index)
          {
            string[] strArray2 = strArray1[index].Split('|');
            if (strArray2.Length >= 4)
            {
              // ISSUE: variable of a compiler-generated type
              TextRange textRange1 = table.Rows[3 + index].Cells[2].Shape.TextFrame.TextRange;
              // ISSUE: reference to a compiler-generated method
              textRange1.Delete();
              // ISSUE: reference to a compiler-generated method
              textRange1.InsertAfter(Math.Round(clsReportUtill.ConvertToDouble(strArray2[1]), 1).ToString());
              // ISSUE: variable of a compiler-generated type
              TextRange textRange2 = table.Rows[3 + index].Cells[3].Shape.TextFrame.TextRange;
              // ISSUE: reference to a compiler-generated method
              textRange2.Delete();
              // ISSUE: reference to a compiler-generated method
              textRange2.InsertAfter(Math.Round(clsReportUtill.ConvertToDouble(strArray2[2]), 1).ToString());
            }
          }
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Gate Dimension")))
          return;
        string[] strArray3 = p_dicSData["Gate Dimension"].Split(new string[1]
        {
          "/"
        }, StringSplitOptions.RemoveEmptyEntries);
        for (int index = 0; index < strArray3.Length; ++index)
        {
          string[] strArray4 = new string[2]{ "-", "-" };
          string[] strArray5 = strArray3[index].Split('|');
          if (strArray5[1] == "Pin")
          {
            // ISSUE: reference to a compiler-generated method
            table.Rows[3 + index].Cells[4].Merge(table.Rows[3 + index].Cells[5]);
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = table.Rows[3 + index].Cells[4].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter("다이렉트게이트");
          }
          else
          {
            if (strArray5.Length > 3)
            {
              strArray4[0] = strArray5[2] + "mm";
              strArray4[1] = strArray5[3] + "mm";
            }
            else
            {
              strArray4[0] = strArray5[2] + "mm";
              strArray4[1] = "Circle";
            }
            // ISSUE: variable of a compiler-generated type
            TextRange textRange3 = table.Rows[3 + index].Cells[4].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange3.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange3.InsertAfter(strArray4[0]);
            // ISSUE: variable of a compiler-generated type
            TextRange textRange4 = table.Rows[3 + index].Cells[5].Shape.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange4.Delete();
            // ISSUE: reference to a compiler-generated method
            textRange4.InsertAfter(strArray4[1]);
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetSlide4):" + ex.Message));
      }
    }

    private void SetSlide5(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S5_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 102.3307f, 77.95276f, 323.433075f, 215.433f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 443.3386f, 77.95276f, 323.433075f, 215.433f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 102.3307f, 297.6378f, 323.433075f, 215.433f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image4")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image4"], MsoTriState.msoFalse, MsoTriState.msoTrue, 443.3386f, 297.6378f, 323.433075f, 215.433f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Animation")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddOLEObject(703.8425f, 55.84252f, 51.59055f, 43.65354f, FileName: p_dicSData["Animation"]);
        }
        for (int i = 0; i < 4; i++)
        {
          // ISSUE: variable of a compiler-generated type
          Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S5_Rectangle " + (object) (i + 1))).FirstOrDefault<Shape>();
          if (shape2 != null)
          {
            // ISSUE: reference to a compiler-generated method
            shape2.ZOrder(MsoZOrderCmd.msoBringToFront);
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = shape2.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Time" + (object) (i + 1))) && p_dicSData["Time" + (object) (i + 1)] != string.Empty)
            {
              // ISSUE: reference to a compiler-generated method
              textRange.InsertAfter(p_dicSData["Time" + (object) (i + 1)] + "s");
            }
          }
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape3 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S5_TextBox 1")).FirstOrDefault<Shape>();
        if (shape3 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape3.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Last Filling Time")))
          {
            string[] strArray = p_dicSData["Last Filling Time"].Split(new string[1]
            {
              "/"
            }, StringSplitOptions.RemoveEmptyEntries);
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter("* Fill Time: " + strArray[0] + "s");
          }
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S5_Table 1")).FirstOrDefault<Shape>().Table;
        if (table == null || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Gate Open Time")))
          return;
        string[] strArray1 = p_dicSData["Gate Open Time"].Split(new string[1]
        {
          "/"
        }, StringSplitOptions.RemoveEmptyEntries);
        for (int index = 0; index < strArray1.Length; ++index)
        {
          string[] strArray2 = strArray1[index].Split('|');
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = table.Rows[2 + index].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          if (strArray2.Length >= 2)
          {
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter(Math.Round(clsReportUtill.ConvertToDouble(strArray2[1]), 1).ToString() + "s");
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetSlide5):" + ex.Message));
      }
    }

    private void SetSlide6(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S6_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 102.3307f, 77.95276f, 323.433075f, 215.433f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 443.3386f, 77.95276f, 323.433075f, 215.433f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 102.3307f, 297.6378f, 323.433075f, 215.433f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image4")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image4"], MsoTriState.msoFalse, MsoTriState.msoTrue, 443.3386f, 297.6378f, 323.433075f, 215.433f);
        }
        for (int i = 0; i < 4; i++)
        {
          // ISSUE: variable of a compiler-generated type
          Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S6_Rectangle " + (object) (i + 1))).FirstOrDefault<Shape>();
          if (shape2 != null)
          {
            // ISSUE: reference to a compiler-generated method
            shape2.ZOrder(MsoZOrderCmd.msoBringToFront);
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = shape2.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Time" + (object) (i + 1))) && p_dicSData["Time" + (object) (i + 1)] != string.Empty)
            {
              // ISSUE: reference to a compiler-generated method
              textRange.InsertAfter(p_dicSData["Time" + (object) (i + 1)] + "s");
            }
          }
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape3 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S6_TextBox 1")).FirstOrDefault<Shape>();
        if (shape3 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape3.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Last Filling Time")))
          {
            string[] strArray = p_dicSData["Last Filling Time"].Split(new string[1]
            {
              "/"
            }, StringSplitOptions.RemoveEmptyEntries);
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter("* Fill Time: " + strArray[0] + "s");
          }
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S6_Table 1")).FirstOrDefault<Shape>().Table;
        if (table == null || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Gate Open Time")))
          return;
        string[] strArray1 = p_dicSData["Gate Open Time"].Split(new string[1]
        {
          "/"
        }, StringSplitOptions.RemoveEmptyEntries);
        for (int index = 0; index < strArray1.Length; ++index)
        {
          string[] strArray2 = strArray1[index].Split('|');
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = table.Rows[2 + index].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          if (strArray2.Length >= 2)
          {
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter(Math.Round(clsReportUtill.ConvertToDouble(strArray2[1]), 1).ToString() + "s");
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetSlide6):" + ex.Message));
      }
    }

    private void SetSlide7(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S7_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          Shape shape2 = p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 542.2677f, 98.64567f, 225.0709f, 149.9528f);
          shape2.Rotation = 90f;
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          Shape shape3 = p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 380.125977f, 98.64567f, 225.0709f, 149.9528f);
          shape3.Rotation = 90f;
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          Shape shape4 = p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 217.7008f, 98.64567f, 225.0709f, 149.9528f);
          shape4.Rotation = 90f;
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image4")))
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          Shape shape5 = p_slData.Shapes.AddPicture(p_dicSData["Image4"], MsoTriState.msoFalse, MsoTriState.msoTrue, 55.27559f, 98.64567f, 225.0709f, 149.9528f);
          shape5.Rotation = 90f;
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image5")))
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          Shape shape6 = p_slData.Shapes.AddPicture(p_dicSData["Image5"], MsoTriState.msoFalse, MsoTriState.msoTrue, 542.2677f, 324.283447f, 225.0709f, 149.9528f);
          shape6.Rotation = 90f;
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image6")))
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          Shape shape7 = p_slData.Shapes.AddPicture(p_dicSData["Image6"], MsoTriState.msoFalse, MsoTriState.msoTrue, 380.125977f, 324.283447f, 225.0709f, 149.9528f);
          shape7.Rotation = 90f;
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image7")))
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          Shape shape8 = p_slData.Shapes.AddPicture(p_dicSData["Image7"], MsoTriState.msoFalse, MsoTriState.msoTrue, 217.7008f, 324.283447f, 225.0709f, 149.9528f);
          shape8.Rotation = 90f;
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image8")))
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          Shape shape9 = p_slData.Shapes.AddPicture(p_dicSData["Image8"], MsoTriState.msoFalse, MsoTriState.msoTrue, 55.27559f, 324.283447f, 225.0709f, 149.9528f);
          shape9.Rotation = 90f;
        }
        for (int i = 0; i < 8; i++)
        {
          // ISSUE: variable of a compiler-generated type
          Shape shape10 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S7_Rectangle " + (object) (i + 1))).FirstOrDefault<Shape>();
          if (shape10 != null)
          {
            // ISSUE: reference to a compiler-generated method
            shape10.ZOrder(MsoZOrderCmd.msoBringToFront);
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = shape10.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Time" + (object) (i + 1))) && p_dicSData["Time" + (object) (i + 1)] != string.Empty)
            {
              // ISSUE: reference to a compiler-generated method
              textRange.InsertAfter(p_dicSData["Time" + (object) (i + 1)] + "s");
            }
          }
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape11 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S7_TextBox 1")).FirstOrDefault<Shape>();
        if (shape11 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape11.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Last Filling Time")))
          {
            string[] strArray = p_dicSData["Last Filling Time"].Split(new string[1]
            {
              "/"
            }, StringSplitOptions.RemoveEmptyEntries);
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter("* Fill Time: " + strArray[0] + "s");
          }
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S7_Table 1")).FirstOrDefault<Shape>().Table;
        if (table == null || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Gate Open Time")))
          return;
        string[] strArray1 = p_dicSData["Gate Open Time"].Split(new string[1]
        {
          "/"
        }, StringSplitOptions.RemoveEmptyEntries);
        for (int index = 0; index < strArray1.Length; ++index)
        {
          string[] strArray2 = strArray1[index].Split('|');
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = table.Rows[2 + index].Cells[2].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          if (strArray2.Length >= 2)
          {
            // ISSUE: reference to a compiler-generated method
            textRange.InsertAfter(Math.Round(clsReportUtill.ConvertToDouble(strArray2[1]), 1).ToString());
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetSlide7):" + ex.Message));
      }
    }

    private void SetSlide8(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S8_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 73.98425f, 193.6063f, 498.330719f, 315.779541f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 866.2677f, -188.7874f, 489.5433f, 310.3937f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 866.2677f, 208.6299f, 489.5433f, 310.3937f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S8_TextBox 1")).FirstOrDefault<Shape>();
        if (shape2 != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Weld Line")))
        {
          string[] strArray = p_dicSData["Weld Line"].Split('|');
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape2.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(strArray[0] + Environment.NewLine + strArray[1]);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape3 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S8_Rectangle 1")).FirstOrDefault<Shape>();
        if (shape3 == null || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Weld Line Temp")))
          return;
        string[] strArray1 = p_dicSData["Weld Line Temp"].Split('|');
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = shape3.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange1.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange1.InsertAfter("Temperature at flow front(" + strArray1[0] + " ~ " + strArray1[1] + "℃)");
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetSlide8):" + ex.Message));
      }
    }

    private void SetSlide9(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S9_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 117.0709f, 79.65354f, 265.6063f, 170.079f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 117.0709f, 289.700775f, 265.6063f, 170.079f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 857.4803f, -28.06299f, 524.4094f, 335.905518f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S9_Rectangle 1")).FirstOrDefault<Shape>();
        if (shape2 != null)
        {
          // ISSUE: reference to a compiler-generated method
          shape2.ZOrder(MsoZOrderCmd.msoBringToFront);
          // ISSUE: variable of a compiler-generated type
          TextRange textRange1 = shape2.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange1.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Sink Mark1")))
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = textRange1.InsertAfter("Scale : " + p_dicSData["Sink Mark1"]);
            textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          }
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape3 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S9_Rectangle 2")).FirstOrDefault<Shape>();
        if (shape3 != null)
        {
          // ISSUE: reference to a compiler-generated method
          shape3.ZOrder(MsoZOrderCmd.msoBringToFront);
          // ISSUE: variable of a compiler-generated type
          TextRange textRange3 = shape3.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange3.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Sink Mark2")))
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange4 = textRange3.InsertAfter("Scale : " + p_dicSData["Sink Mark2"]);
            textRange4.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          }
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape4 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S9_Rectangle 3")).FirstOrDefault<Shape>();
        if (shape4 == null)
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange5 = shape4.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange5.Delete();
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "SinkMark")))
          return;
        string[] strArray = p_dicSData["SinkMark"].Split('|');
        // ISSUE: variable of a compiler-generated type
        TextRange textRange6 = shape4.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange6.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange6.InsertAfter(strArray[0] + Environment.NewLine + strArray[1]);
        textRange6.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetSlide9):" + ex.Message));
      }
    }

    private void SetSlide10(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S10_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 121.6063f, 79.65354f, 256.5354f, 170.079f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 121.6063f, 289.700775f, 256.5354f, 170.079f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S10_Rectangle 1")).FirstOrDefault<Shape>();
        if (shape2 != null)
        {
          // ISSUE: reference to a compiler-generated method
          shape2.ZOrder(MsoZOrderCmd.msoBringToFront);
          // ISSUE: variable of a compiler-generated type
          TextRange textRange1 = shape2.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange1.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Shrinkage1")))
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = textRange1.InsertAfter("Scale : " + p_dicSData["Shrinkage1"]);
            textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          }
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape3 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S10_Rectangle 2")).FirstOrDefault<Shape>();
        if (shape3 == null)
          return;
        // ISSUE: reference to a compiler-generated method
        shape3.ZOrder(MsoZOrderCmd.msoBringToFront);
        // ISSUE: variable of a compiler-generated type
        TextRange textRange3 = shape3.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange3.Delete();
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Shrinkage2")))
          return;
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        TextRange textRange4 = textRange3.InsertAfter("Scale : " + p_dicSData["Shrinkage2"]);
        textRange4.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetSlide10):" + ex.Message));
      }
    }

    private void SetSlide11(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S11_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 97.22835f, 95.24409f, 261.6378f, 170.6457f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 446.173218f, 95.24409f, 261.6378f, 170.6457f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 97.22835f, 289.1339f, 261.6378f, 170.6457f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image4")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image4"], MsoTriState.msoFalse, MsoTriState.msoTrue, 446.173218f, 289.1339f, 261.6378f, 170.6457f);
        }
        for (int i = 0; i < 4; i++)
        {
          // ISSUE: variable of a compiler-generated type
          Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S11_Rectangle " + (object) (i + 1))).FirstOrDefault<Shape>();
          if (shape2 != null)
          {
            // ISSUE: reference to a compiler-generated method
            shape2.ZOrder(MsoZOrderCmd.msoBringToFront);
            // ISSUE: variable of a compiler-generated type
            TextRange textRange = shape2.TextFrame.TextRange;
            // ISSUE: reference to a compiler-generated method
            textRange.Delete();
            if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Time" + (object) (i + 1))))
            {
              // ISSUE: reference to a compiler-generated method
              textRange.InsertAfter("Time " + p_dicSData["Time" + (object) (i + 1)] + "s");
            }
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetSlide11):" + ex.Message));
      }
    }

    private void SetSlide12(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string[] strArray1 = (string[]) null;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S12_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 75.40157f, 79.93701f, 297.6378f, 195.874f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 75.40157f, 295.3701f, 297.6378f, 195.874f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 377.291351f, 295.3701f, 297.6378f, 195.874f);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "Vertical Content Placeholder 1")).FirstOrDefault<Shape>();
        if (shape2 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange1 = shape2.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange1.Delete();
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Coolant Temperature(Cavity)")))
          {
            strArray1 = p_dicSData["Circuit Coolant Temperature(Cavity)"].Split('|');
            // ISSUE: reference to a compiler-generated method
            textRange1.InsertAfter("냉각수(캐비티)의 온도는 ");
            textRange1.Font.Bold = MsoTriState.msoTrue;
            textRange1.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = textRange1.InsertAfter(strArray1[0]);
            textRange2.Font.Bold = MsoTriState.msoTrue;
            textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange3 = textRange2.InsertAfter("℃ ~ ");
            textRange3.Font.Bold = MsoTriState.msoTrue;
            textRange3.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange4 = textRange3.InsertAfter(strArray1[1]);
            textRange4.Font.Bold = MsoTriState.msoTrue;
            textRange4.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange5 = textRange4.InsertAfter("℃로 편차는 ");
            textRange5.Font.Bold = MsoTriState.msoTrue;
            textRange5.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange6 = textRange5.InsertAfter(strArray1[2]);
            textRange6.Font.Bold = MsoTriState.msoTrue;
            textRange6.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            // ISSUE: reference to a compiler-generated method
            textRange1 = textRange6.InsertAfter("℃입니다.");
            textRange1.Font.Bold = MsoTriState.msoTrue;
            textRange1.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          }
          if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Coolant Temperature(Core)")))
          {
            if (textRange1.Text.Count<char>() > 0)
            {
              // ISSUE: reference to a compiler-generated method
              textRange1.InsertAfter(Environment.NewLine + Environment.NewLine);
            }
            // ISSUE: reference to a compiler-generated method
            textRange1.InsertAfter("냉각수(코어)의 온도는 ");
            textRange1.Font.Bold = MsoTriState.msoTrue;
            textRange1.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange7 = textRange1.InsertAfter(strArray1[0]);
            textRange7.Font.Bold = MsoTriState.msoTrue;
            textRange7.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange8 = textRange7.InsertAfter("℃ ~ ");
            textRange8.Font.Bold = MsoTriState.msoTrue;
            textRange8.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange9 = textRange8.InsertAfter(strArray1[1]);
            textRange9.Font.Bold = MsoTriState.msoTrue;
            textRange9.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange10 = textRange9.InsertAfter("℃로 편차는 ");
            textRange10.Font.Bold = MsoTriState.msoTrue;
            textRange10.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange11 = textRange10.InsertAfter(strArray1[2]);
            textRange11.Font.Bold = MsoTriState.msoTrue;
            textRange11.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            // ISSUE: reference to a compiler-generated method
            textRange1 = textRange11.InsertAfter("℃입니다.");
            textRange1.Font.Bold = MsoTriState.msoTrue;
            textRange1.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          }
          if (textRange1.Text.Count<char>() > 0)
          {
            // ISSUE: reference to a compiler-generated method
            textRange1.InsertAfter(Environment.NewLine + Environment.NewLine);
          }
          // ISSUE: reference to a compiler-generated method
          textRange1.InsertAfter("이론상 냉각수 온도는 2~3℃ 이내로 관리 되는 것을 추천합니다.");
          textRange1.Font.Bold = MsoTriState.msoTrue;
          textRange1.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape3 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S12_Rectangle 2")).FirstOrDefault<Shape>();
        if (shape3 != null && p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Temperature Mold")))
        {
          // ISSUE: reference to a compiler-generated method
          shape3.ZOrder(MsoZOrderCmd.msoBringToFront);
          string[] strArray2 = p_dicSData["Temperature Mold"].Split('|');
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape3.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(strArray2[0] + " ~ " + strArray2[1]);
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape4 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S12_Rectangle 1")).FirstOrDefault<Shape>();
        if (shape4 == null || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cooling")))
          return;
        string[] strArray3 = p_dicSData["Cooling"].Split('|');
        // ISSUE: variable of a compiler-generated type
        TextRange textRange12 = shape4.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange12.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange12.InsertAfter(strArray3[0] + Environment.NewLine + strArray3[1]);
        textRange12.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetSlide12):" + ex.Message));
      }
    }

    private void SetSlide13(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string empty = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape1 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S13_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape1 != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape1.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 126.7087f, 79.65354f, 246.0472f, 170.079f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 126.7087f, 289.700775f, 246.0472f, 170.079f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Time To Reach Ejection Temperature")))
          empty = p_dicSData["Time To Reach Ejection Temperature"];
        // ISSUE: variable of a compiler-generated type
        Shape shape2 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S13_Rectangle 1")).FirstOrDefault<Shape>();
        if (shape2 != null)
        {
          // ISSUE: reference to a compiler-generated method
          shape2.ZOrder(MsoZOrderCmd.msoBringToFront);
          // ISSUE: variable of a compiler-generated type
          TextRange textRange1 = shape2.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange1.Delete();
          if (empty != string.Empty)
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = textRange1.InsertAfter("취출 가능한 시간 : " + empty + "s");
            textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          }
        }
        // ISSUE: variable of a compiler-generated type
        Shape shape3 = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S13_Rectangle 2")).FirstOrDefault<Shape>();
        if (shape3 != null)
        {
          // ISSUE: reference to a compiler-generated method
          shape3.ZOrder(MsoZOrderCmd.msoBringToFront);
          // ISSUE: variable of a compiler-generated type
          TextRange textRange3 = shape3.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange3.Delete();
          if (empty != string.Empty)
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange4 = textRange3.InsertAfter(empty + "s 이후 고화되어야 할 부위");
            textRange4.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          }
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S13_Table 1")).FirstOrDefault<Shape>().Table;
        if (table == null)
          return;
        // ISSUE: variable of a compiler-generated type
        TextRange textRange5 = table.Rows[1].Cells[2].Shape.TextFrame.TextRange;
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Injection Time")))
        {
          // ISSUE: reference to a compiler-generated method
          textRange5.InsertAfter(p_dicSData["Injection Time"]);
        }
        // ISSUE: variable of a compiler-generated type
        TextRange textRange6 = table.Rows[2].Cells[2].Shape.TextFrame.TextRange;
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Pack/Holding Time")))
        {
          // ISSUE: reference to a compiler-generated method
          textRange6.InsertAfter(p_dicSData["Pack/Holding Time"]);
        }
        // ISSUE: variable of a compiler-generated type
        TextRange textRange7 = table.Rows[3].Cells[2].Shape.TextFrame.TextRange;
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Cooling Time")))
        {
          // ISSUE: reference to a compiler-generated method
          textRange7.InsertAfter(p_dicSData["Cooling Time"]);
        }
        // ISSUE: variable of a compiler-generated type
        TextRange textRange8 = table.Rows[4].Cells[2].Shape.TextFrame.TextRange;
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Schizonepeta")))
          return;
        // ISSUE: reference to a compiler-generated method
        textRange8.InsertAfter(p_dicSData["Schizonepeta"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetSlide13):" + ex.Message));
      }
    }

    private void SetSlide14(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string empty = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S14_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 104.0315f, 95.24409f, 248.5984f, 170.6457f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 104.0315f, 289.1339f, 248.5984f, 170.6457f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 428.598419f, 95.24409f, 248.5984f, 170.6457f);
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S14_Table 1")).FirstOrDefault<Shape>().Table;
        if (table == null || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll")))
          return;
        string[] strArray = p_dicSData["DefAll"].Split('|');
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = table.Rows[1].Cells[2].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange1.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange1.InsertAfter(strArray[1]);
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = table.Rows[2].Cells[2].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange2.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange2.InsertAfter(strArray[0]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetSlide14):" + ex.Message));
      }
    }

    private void SetSlide15(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string empty = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S15_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 104.0315f, 95.24409f, 248.5984f, 170.6457f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 104.0315f, 289.1339f, 248.5984f, 170.6457f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 428.598419f, 95.24409f, 248.5984f, 170.6457f);
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S15_Table 1")).FirstOrDefault<Shape>().Table;
        if (table == null || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefAll")))
          return;
        string[] strArray = p_dicSData["DefAll"].Split('|');
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = table.Rows[1].Cells[2].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange1.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange1.InsertAfter(strArray[1]);
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = table.Rows[2].Cells[2].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange2.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange2.InsertAfter(strArray[0]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetSlide15):" + ex.Message));
      }
    }

    private void SetSlide16(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string empty = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S16_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 104.0315f, 95.24409f, 248.5984f, 170.6457f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 104.0315f, 289.1339f, 248.5984f, 170.6457f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 428.598419f, 95.24409f, 248.5984f, 170.6457f);
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S16_Table 1")).FirstOrDefault<Shape>().Table;
        if (table == null || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX")))
          return;
        string[] strArray = p_dicSData["DefX"].Split('|');
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = table.Rows[1].Cells[2].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange1.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange1.InsertAfter(strArray[1]);
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = table.Rows[2].Cells[2].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange2.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange2.InsertAfter(strArray[0]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetSlide16):" + ex.Message));
      }
    }

    private void SetSlide17(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string empty = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S17_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 104.0315f, 95.24409f, 248.5984f, 170.6457f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 104.0315f, 289.1339f, 248.5984f, 170.6457f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 428.598419f, 95.24409f, 248.5984f, 170.6457f);
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S17_Table 1")).FirstOrDefault<Shape>().Table;
        if (table == null || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefX")))
          return;
        string[] strArray = p_dicSData["DefX"].Split('|');
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = table.Rows[1].Cells[2].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange1.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange1.InsertAfter(strArray[1]);
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = table.Rows[2].Cells[2].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange2.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange2.InsertAfter(strArray[0]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetSlide17):" + ex.Message));
      }
    }

    private void SetSlide18(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string empty = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S18_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 104.0315f, 95.24409f, 248.5984f, 170.6457f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 104.0315f, 289.1339f, 248.5984f, 170.6457f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 428.598419f, 95.24409f, 248.5984f, 170.6457f);
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S18_Table 1")).FirstOrDefault<Shape>().Table;
        if (table == null || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY")))
          return;
        string[] strArray = p_dicSData["DefY"].Split('|');
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = table.Rows[1].Cells[2].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange1.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange1.InsertAfter(strArray[1]);
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = table.Rows[2].Cells[2].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange2.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange2.InsertAfter(strArray[0]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetSlide18):" + ex.Message));
      }
    }

    private void SetSlide19(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string empty = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S19_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 104.0315f, 95.24409f, 248.5984f, 170.6457f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 104.0315f, 289.1339f, 248.5984f, 170.6457f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 428.598419f, 95.24409f, 248.5984f, 170.6457f);
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S19_Table 1")).FirstOrDefault<Shape>().Table;
        if (table == null || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefY")))
          return;
        string[] strArray = p_dicSData["DefY"].Split('|');
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = table.Rows[1].Cells[2].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange1.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange1.InsertAfter(strArray[1]);
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = table.Rows[2].Cells[2].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange2.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange2.InsertAfter(strArray[0]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetSlide19):" + ex.Message));
      }
    }

    private void SetSlide20(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string empty = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S20_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 104.0315f, 95.24409f, 248.5984f, 170.6457f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 104.0315f, 289.1339f, 248.5984f, 170.6457f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 428.598419f, 95.24409f, 248.5984f, 170.6457f);
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S20_Table 1")).FirstOrDefault<Shape>().Table;
        if (table == null || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ")))
          return;
        string[] strArray = p_dicSData["DefZ"].Split('|');
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = table.Rows[1].Cells[2].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange1.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange1.InsertAfter(strArray[1]);
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = table.Rows[2].Cells[2].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange2.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange2.InsertAfter(strArray[0]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetSlide20):" + ex.Message));
      }
    }

    private void SetSlide21(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      string empty = string.Empty;
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S21_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image1")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image1"], MsoTriState.msoFalse, MsoTriState.msoTrue, 104.0315f, 95.24409f, 248.5984f, 170.6457f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image2")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image2"], MsoTriState.msoFalse, MsoTriState.msoTrue, 104.0315f, 289.1339f, 248.5984f, 170.6457f);
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Image3")))
        {
          // ISSUE: reference to a compiler-generated method
          p_slData.Shapes.AddPicture(p_dicSData["Image3"], MsoTriState.msoFalse, MsoTriState.msoTrue, 428.598419f, 95.24409f, 248.5984f, 170.6457f);
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S21_Table 1")).FirstOrDefault<Shape>().Table;
        if (table == null || !p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "DefZ")))
          return;
        string[] strArray = p_dicSData["DefZ"].Split('|');
        // ISSUE: variable of a compiler-generated type
        TextRange textRange1 = table.Rows[1].Cells[2].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange1.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange1.InsertAfter(strArray[1]);
        // ISSUE: variable of a compiler-generated type
        TextRange textRange2 = table.Rows[2].Cells[2].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange2.Delete();
        // ISSUE: reference to a compiler-generated method
        textRange2.InsertAfter(strArray[0]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetSlide21):" + ex.Message));
      }
    }

    private void SetSlide22(Slide p_slData, Dictionary<string, string> p_dicSData)
    {
      try
      {
        // ISSUE: variable of a compiler-generated type
        Shape shape = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S22_SubTitle 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: variable of a compiler-generated type
          TextRange textRange = shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange.Delete();
          // ISSUE: reference to a compiler-generated method
          textRange.InsertAfter(this.m_strStudyName);
          textRange.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        // ISSUE: variable of a compiler-generated type
        Table table = p_slData.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Name == "S22_Table 1")).FirstOrDefault<Shape>().Table;
        if (table == null)
          return;
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Coolant Temperature(Cavity)")))
        {
          string[] strArray = p_dicSData["Circuit Coolant Temperature(Cavity)"].Split('|');
          // ISSUE: variable of a compiler-generated type
          TextRange textRange1 = table.Rows[2].Cells[3].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange1.Delete();
          if (strArray[0] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange2 = textRange1.InsertAfter(strArray[0]);
            textRange2.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange3 = textRange2.InsertAfter("℃");
            textRange3.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange4 = textRange1.InsertAfter("-");
            textRange4.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange5 = table.Rows[2].Cells[4].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange5.Delete();
          if (strArray[1] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange6 = textRange5.InsertAfter(strArray[1]);
            textRange6.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange7 = textRange5.InsertAfter("-");
            textRange7.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange8 = table.Rows[2].Cells[5].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange8.Delete();
          if (strArray[2] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange9 = textRange8.InsertAfter(strArray[2]);
            textRange9.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange10 = textRange8.InsertAfter("-");
            textRange10.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Coolant Temperature(Core)")))
        {
          string[] strArray = p_dicSData["Circuit Coolant Temperature(Core)"].Split('|');
          // ISSUE: variable of a compiler-generated type
          TextRange textRange11 = table.Rows[3].Cells[3].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange11.Delete();
          if (strArray[0] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange12 = textRange11.InsertAfter(strArray[0]);
            textRange12.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange13 = textRange12.InsertAfter("℃");
            textRange13.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange14 = textRange11.InsertAfter("-");
            textRange14.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange15 = table.Rows[3].Cells[4].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange15.Delete();
          if (strArray[1] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange16 = textRange15.InsertAfter(strArray[1]);
            textRange16.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange17 = textRange15.InsertAfter("-");
            textRange17.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange18 = table.Rows[3].Cells[5].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange18.Delete();
          if (strArray[2] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange19 = textRange18.InsertAfter(strArray[2]);
            textRange19.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange20 = textRange18.InsertAfter("-");
            textRange20.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Flow Rate")))
        {
          string[] strArray = p_dicSData["Circuit Flow Rate"].Split('|');
          // ISSUE: variable of a compiler-generated type
          TextRange textRange21 = table.Rows[4].Cells[3].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange21.Delete();
          if (strArray[0] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange22 = textRange21.InsertAfter(strArray[0]);
            textRange22.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange23 = textRange22.InsertAfter("lit/min");
            textRange23.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange24 = textRange21.InsertAfter("-");
            textRange24.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange25 = table.Rows[4].Cells[4].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange25.Delete();
          if (strArray[1] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange26 = textRange25.InsertAfter(strArray[1]);
            textRange26.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange27 = textRange25.InsertAfter("-");
            textRange27.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange28 = table.Rows[4].Cells[5].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange28.Delete();
          if (strArray[2] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange29 = textRange28.InsertAfter(strArray[2]);
            textRange29.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange30 = textRange28.InsertAfter("-");
            textRange30.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Circuit Pressure")))
        {
          string[] strArray = p_dicSData["Circuit Pressure"].Split('|');
          // ISSUE: variable of a compiler-generated type
          TextRange textRange31 = table.Rows[5].Cells[3].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange31.Delete();
          if (strArray[0] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange32 = textRange31.InsertAfter(strArray[0]);
            textRange32.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange33 = textRange32.InsertAfter("kPa");
            textRange33.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange34 = textRange31.InsertAfter("-");
            textRange34.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange35 = table.Rows[5].Cells[4].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange35.Delete();
          if (strArray[1] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange36 = textRange35.InsertAfter(strArray[1]);
            textRange36.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange37 = textRange35.InsertAfter("-");
            textRange37.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange38 = table.Rows[5].Cells[5].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange38.Delete();
          if (strArray[2] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange39 = textRange38.InsertAfter(strArray[2]);
            textRange39.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange40 = textRange38.InsertAfter("-");
            textRange40.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Time To Reach Ejection Temperature")))
        {
          string[] strArray = p_dicSData["Time To Reach Ejection Temperature"].Split('|');
          // ISSUE: variable of a compiler-generated type
          TextRange textRange41 = table.Rows[6].Cells[3].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange41.Delete();
          if (strArray[0] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange42 = textRange41.InsertAfter(strArray[0]);
            textRange42.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange43 = textRange42.InsertAfter("s");
            textRange43.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange44 = textRange41.InsertAfter("-");
            textRange44.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange45 = table.Rows[6].Cells[4].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange45.Delete();
          if (strArray[1] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange46 = textRange45.InsertAfter(strArray[1]);
            textRange46.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange47 = textRange45.InsertAfter("-");
            textRange47.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange48 = table.Rows[6].Cells[5].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange48.Delete();
          if (strArray[2] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange49 = textRange48.InsertAfter(strArray[2]);
            textRange49.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange50 = textRange48.InsertAfter("-");
            textRange50.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
        }
        if (p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Temperature, Mold")))
        {
          string[] strArray = p_dicSData["Temperature, Mold"].Split('|');
          // ISSUE: variable of a compiler-generated type
          TextRange textRange51 = table.Rows[7].Cells[3].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange51.Delete();
          if (strArray[0] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange52 = textRange51.InsertAfter(strArray[0]);
            textRange52.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange53 = textRange52.InsertAfter("℃");
            textRange53.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange54 = textRange51.InsertAfter("-");
            textRange54.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange55 = table.Rows[7].Cells[4].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange55.Delete();
          if (strArray[1] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange56 = textRange55.InsertAfter(strArray[1]);
            textRange56.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange57 = textRange55.InsertAfter("-");
            textRange57.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          // ISSUE: variable of a compiler-generated type
          TextRange textRange58 = table.Rows[7].Cells[5].Shape.TextFrame.TextRange;
          // ISSUE: reference to a compiler-generated method
          textRange58.Delete();
          if (strArray[2] != "")
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange59 = textRange58.InsertAfter(strArray[2]);
            textRange59.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
          else
          {
            // ISSUE: reference to a compiler-generated method
            // ISSUE: variable of a compiler-generated type
            TextRange textRange60 = textRange58.InsertAfter("-");
            textRange60.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          }
        }
        if (!p_dicSData.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Temperature, Part")))
          return;
        string[] strArray1 = p_dicSData["Temperature, Part"].Split('|');
        // ISSUE: variable of a compiler-generated type
        TextRange textRange61 = table.Rows[8].Cells[3].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange61.Delete();
        if (strArray1[0] != "")
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange62 = textRange61.InsertAfter(strArray1[0]);
          textRange62.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange63 = textRange62.InsertAfter("℃");
          textRange63.Font.Color.RGB = ColorTranslator.ToOle(Color.Black);
        }
        else
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange64 = textRange61.InsertAfter("-");
          textRange64.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
        }
        // ISSUE: variable of a compiler-generated type
        TextRange textRange65 = table.Rows[8].Cells[4].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange65.Delete();
        if (strArray1[1] != "")
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange66 = textRange65.InsertAfter(strArray1[1]);
          textRange66.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
        }
        else
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange67 = textRange65.InsertAfter("-");
          textRange67.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
        }
        // ISSUE: variable of a compiler-generated type
        TextRange textRange68 = table.Rows[8].Cells[5].Shape.TextFrame.TextRange;
        // ISSUE: reference to a compiler-generated method
        textRange68.Delete();
        if (strArray1[2] != "")
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange69 = textRange68.InsertAfter(strArray1[2]);
          textRange69.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
        }
        else
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          TextRange textRange70 = textRange68.InsertAfter("-");
          textRange70.Font.Color.RGB = ColorTranslator.ToOle(Color.Red);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFReport][clsHyundai]SetSlide22):" + ex.Message));
      }
    }
  }
}
