﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmInjection
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDLog4Net;
using HDMFUserControl;
using HDMoldFlow.Properties;
using Microsoft.WindowsAPICodePack.Dialogs;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using System.Xml;

namespace HDMoldFlow
{
  public class frmInjection : Form
  {
    private IContainer components = (IContainer) null;
    private ListBox listBox_FileName;
    private Label label_List;
    private Label label_Data;
    private Label label_ScrewDia;
    private Label label_MaxRate;
    private UnitTextBox unitTextBox_MaxRate;
    private NewTextBox newTextBox_DataLastModified;
    private UnitTextBox unitTextBox_ScrewDia;
    private Label label_Company;
    private Label label_MaxPressure;
    private NewTextBox newTextBox_DataSource;
    private UnitTextBox unitTextBox_MaxPressure;
    private UnitTextBox unitTextBox_MaxStroke;
    private Label label_MaxStroke;
    private Label label_Name;
    private NewTextBox newTextBox_PreRatio;
    private NewTextBox newTextBox_Company;
    private NewTextBox newTextBox_Name;
    private Label label5;
    private Label label_PreRatio;
    private UnitTextBox unitTextBox_MaxClampForce;
    private Label label_MaxClamp;
    private Label label4;
    private NewButton newButton_Del;
    private NewButton newButton_Edit;
    private NewButton newButton_Add;
    private NewTextBox newTextBox_FileName;
    private NewButton newButton_Export;
    private NewButton newButton_Import;

    public frmInjection()
    {
      this.InitializeComponent();
      this.newButton_Add.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Add.Image);
      this.newButton_Edit.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Edit.Image);
      this.newButton_Del.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Del.Image);
      this.Text = LocaleControl.getInstance().GetString("IDS_SET_INJ");
      this.label_List.Text = LocaleControl.getInstance().GetString("IDS_INJ_LIST");
      this.label_Data.Text = LocaleControl.getInstance().GetString("IDS_INJ_DATA");
      this.label_Name.Text = LocaleControl.getInstance().GetString("IDS_INJ_NAME");
      this.label_Company.Text = LocaleControl.getInstance().GetString("IDS_INJ_COMPANY");
      this.label_MaxStroke.Text = LocaleControl.getInstance().GetString("IDS_INJ_MAXSTROKE");
      this.label_MaxRate.Text = LocaleControl.getInstance().GetString("IDS_INJ_MAXRATE");
      this.label_ScrewDia.Text = LocaleControl.getInstance().GetString("IDS_INJ_SCREWDIA");
      this.label_MaxPressure.Text = LocaleControl.getInstance().GetString("IDS_INJ_MAXPRESSURE");
      this.label_MaxClamp.Text = LocaleControl.getInstance().GetString("IDS_INJ_MAXCLAMP");
      this.label_PreRatio.Text = LocaleControl.getInstance().GetString("IDS_INJ_PRERATIO");
      this.newButton_Add.ButtonText = LocaleControl.getInstance().GetString("IDS_ADD");
      this.newButton_Edit.ButtonText = LocaleControl.getInstance().GetString("IDS_EDIT");
      this.newButton_Del.ButtonText = LocaleControl.getInstance().GetString("IDS_DELETE");
      this.newButton_Import.ButtonText = LocaleControl.getInstance().GetString("IDS_IMPORT");
      this.newButton_Export.ButtonText = LocaleControl.getInstance().GetString("IDS_EXPORT");
    }

    private void frmInjection_Load(object sender, EventArgs e)
    {
      try
      {
        List<string> stringList = new List<string>();
        foreach (DataRow row in (InternalDataCollectionBase) clsDefine.g_dtInjectionDB.Rows)
          stringList.Add(row["FileName"].ToString());
        stringList.Sort();
        this.listBox_FileName.Items.AddRange((object[]) stringList.ToArray());
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInjection]frmInjection_Load):" + ex.Message));
      }
    }

    private void listBox_FileName_SelectedIndexChanged(object sender, EventArgs e)
    {
      try
      {
        DataRow dataRow = clsDefine.g_dtInjectionDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["FileName"].ToString() == this.listBox_FileName.Text)).FirstOrDefault<DataRow>();
        if (dataRow == null)
          return;
        this.newTextBox_FileName.Value = this.listBox_FileName.Text;
        this.newTextBox_Name.Value = dataRow["Name"].ToString();
        this.newTextBox_Company.Value = dataRow["Company"].ToString();
        this.newTextBox_DataSource.Value = dataRow["DataSource"].ToString();
        this.newTextBox_DataLastModified.Value = dataRow["LastModified"].ToString();
        this.unitTextBox_MaxStroke.Value = dataRow["MaxStroke"].ToString();
        this.unitTextBox_MaxRate.Value = dataRow["MaxRate"].ToString();
        this.unitTextBox_ScrewDia.Value = dataRow["ScrewDia"].ToString();
        this.unitTextBox_MaxPressure.Value = dataRow["MaxPressure"].ToString();
        this.unitTextBox_MaxClampForce.Value = dataRow["MaxClamp"].ToString();
        this.newTextBox_PreRatio.Value = dataRow["PreRatio"].ToString();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInjection]frmInjection_Load):" + ex.Message));
      }
    }

    private void frmInjection_KeyDown(object sender, KeyEventArgs e)
    {
      if (e.KeyCode != Keys.Escape)
        return;
      this.Close();
    }

    private void newButton_NewClick(object sender, EventArgs e)
    {
      NewButton newButton = sender as NewButton;
      if (newButton == this.newButton_Add)
        this.AddData();
      else if (newButton == this.newButton_Edit)
        this.EditData();
      else if (newButton == this.newButton_Del)
        this.DelData();
      else if (newButton == this.newButton_Import)
        this.ImportData();
      else
        this.ExportData();
    }

    private void AddData()
    {
      try
      {
        if (this.listBox_FileName.Items.Contains((object) this.newTextBox_FileName.Value))
          return;
        DataRow p_drInjection = clsDefine.g_dtInjectionDB.Rows.Add();
        p_drInjection["FileName"] = (object) this.newTextBox_FileName.Value;
        p_drInjection["Name"] = (object) this.newTextBox_Name.Value;
        p_drInjection["Company"] = (object) this.newTextBox_Company.Value;
        p_drInjection["DataSource"] = (object) this.newTextBox_DataSource.Value;
        p_drInjection["LastModified"] = (object) this.newTextBox_DataLastModified.Value;
        p_drInjection["MaxStroke"] = (object) this.unitTextBox_MaxStroke.Value;
        p_drInjection["MaxRate"] = (object) this.unitTextBox_MaxRate.Value;
        p_drInjection["ScrewDia"] = (object) this.unitTextBox_ScrewDia.Value;
        p_drInjection["MaxPressure"] = (object) this.unitTextBox_MaxPressure.Value;
        p_drInjection["MaxClamp"] = (object) this.unitTextBox_MaxClampForce.Value;
        p_drInjection["PreRatio"] = (object) this.newTextBox_PreRatio.Value;
        this.CreateInjDataFile(p_drInjection);
        List<string> stringList = new List<string>();
        foreach (DataRow row in (InternalDataCollectionBase) clsDefine.g_dtInjectionDB.Rows)
          stringList.Add(row["FileName"].ToString());
        stringList.Sort();
        this.listBox_FileName.Items.Clear();
        this.listBox_FileName.Items.AddRange((object[]) stringList.ToArray());
        this.listBox_FileName.SelectedIndex = this.listBox_FileName.Items.IndexOf((object) this.newTextBox_FileName.Value);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInjection]AddData):" + ex.Message));
      }
    }

    private void EditData()
    {
      try
      {
        if (this.listBox_FileName.Items.Count == 0 || this.listBox_FileName.SelectedIndex == -1)
          return;
        DataRow p_drInjection = clsDefine.g_dtInjectionDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["FileName"].ToString() == this.listBox_FileName.Text)).FirstOrDefault<DataRow>();
        if (p_drInjection == null)
          return;
        FileInfo fileInfo = new FileInfo(clsDefine.g_diInjectionDBCfg.FullName + "\\" + p_drInjection["FileName"] + ".xml");
        if (fileInfo.Exists)
          fileInfo.Delete();
        p_drInjection["FileName"] = (object) this.newTextBox_FileName.Value;
        p_drInjection["Name"] = (object) this.newTextBox_Name.Value;
        p_drInjection["Company"] = (object) this.newTextBox_Company.Value;
        p_drInjection["DataSource"] = (object) this.newTextBox_DataSource.Value;
        p_drInjection["LastModified"] = (object) this.newTextBox_DataLastModified.Value;
        p_drInjection["MaxStroke"] = (object) this.unitTextBox_MaxStroke.Value;
        p_drInjection["MaxRate"] = (object) this.unitTextBox_MaxRate.Value;
        p_drInjection["ScrewDia"] = (object) this.unitTextBox_ScrewDia.Value;
        p_drInjection["MaxPressure"] = (object) this.unitTextBox_MaxPressure.Value;
        p_drInjection["MaxClamp"] = (object) this.unitTextBox_MaxClampForce.Value;
        p_drInjection["PreRatio"] = (object) this.newTextBox_PreRatio.Value;
        this.CreateInjDataFile(p_drInjection);
        List<string> stringList = new List<string>();
        foreach (DataRow row in (InternalDataCollectionBase) clsDefine.g_dtInjectionDB.Rows)
          stringList.Add(row["FileName"].ToString());
        stringList.Sort();
        this.listBox_FileName.Items.Clear();
        this.listBox_FileName.Items.AddRange((object[]) stringList.ToArray());
        this.listBox_FileName.SelectedIndex = this.listBox_FileName.Items.IndexOf((object) this.newTextBox_FileName.Value);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInjection]EditData):" + ex.Message));
      }
    }

    private void DelData()
    {
      try
      {
        if (this.listBox_FileName.Items.Count == 0 || this.listBox_FileName.SelectedItems.Count == 0 || clsUtill.ShowMessageBox((Form) this, LocaleControl.getInstance().GetString("IDS_WANT_DELETE"), LocaleControl.getInstance().GetString("IDS_DELETE"), MessageBoxButtons.YesNo, MessageBoxIcon.Asterisk) == DialogResult.No)
          return;
        List<string> stringList = new List<string>();
        foreach (string selectedItem in this.listBox_FileName.SelectedItems)
        {
          string strInjectionDB = selectedItem;
          DataRow row = clsDefine.g_dtInjectionDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["FileName"].ToString() == strInjectionDB)).FirstOrDefault<DataRow>();
          if (row == null)
            return;
          clsDefine.g_dtInjectionDB.Rows.Remove(row);
          FileInfo fileInfo = new FileInfo(clsDefine.g_diInjectionDBCfg.FullName + "\\" + strInjectionDB + ".xml");
          if (fileInfo.Exists)
            fileInfo.Delete();
          stringList.Add(strInjectionDB);
        }
        for (int index = 0; index < stringList.Count; ++index)
          this.listBox_FileName.Items.Remove((object) stringList[index]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInjection]DelData):" + ex.Message));
      }
    }

    private void ImportData()
    {
      bool flag = false;
      string strInjectionDB = "";
      try
      {
        OpenFileDialog openFileDialog = new OpenFileDialog();
        openFileDialog.Filter = "DB Files(*.xml)|*.xml";
        openFileDialog.Multiselect = true;
        if (openFileDialog.ShowDialog((IWin32Window) this) != DialogResult.OK)
          return;
        foreach (string fileName in openFileDialog.FileNames)
        {
          strInjectionDB = Path.GetFileNameWithoutExtension(fileName);
          if (!clsDefine.g_dtInjectionDB.AsEnumerable().Any<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["FileName"].ToString() == strInjectionDB)))
          {
            FileInfo fileInfo = new FileInfo(clsDefine.g_diInjectionDBCfg.FullName + "\\" + strInjectionDB + ".xml");
            File.Copy(fileName, fileInfo.FullName, true);
            flag = true;
          }
        }
        if (flag)
        {
          clsData.LoadInjection();
          List<string> stringList = new List<string>();
          foreach (DataRow row in (InternalDataCollectionBase) clsDefine.g_dtInjectionDB.Rows)
            stringList.Add(row["FileName"].ToString());
          stringList.Sort();
          this.listBox_FileName.Items.Clear();
          this.listBox_FileName.Items.AddRange((object[]) stringList.ToArray());
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInjection]ImportData):" + ex.Message));
      }
    }

    private void ExportData()
    {
      try
      {
        if (this.listBox_FileName.Items.Count == 0 || this.listBox_FileName.SelectedItems.Count == 0)
          return;
        CommonOpenFileDialog commonOpenFileDialog = new CommonOpenFileDialog()
        {
          IsFolderPicker = true
        };
        if (commonOpenFileDialog.ShowDialog() != CommonFileDialogResult.Ok)
          return;
        foreach (string selectedItem in this.listBox_FileName.SelectedItems)
        {
          string strInjectionDB = selectedItem;
          DataRow dataRow = clsDefine.g_dtInjectionDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["FileName"].ToString() == strInjectionDB)).FirstOrDefault<DataRow>();
          if (dataRow != null)
          {
            FileInfo fileInfo = new FileInfo(clsDefine.g_diInjectionDBCfg.FullName + "\\" + dataRow["FileName"].ToString() + ".xml");
            if (fileInfo.Exists)
              fileInfo.CopyTo(commonOpenFileDialog.FileName + "\\" + fileInfo.Name, true);
          }
        }
        Process.Start(commonOpenFileDialog.FileName);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInjection]ExportData):" + ex.Message));
      }
    }

    private void CreateInjDataFile(DataRow p_drInjection)
    {
      try
      {
        FileInfo fileInfo = new FileInfo(clsDefine.g_diInjectionDBCfg.FullName + "\\" + p_drInjection["FileName"] + ".xml");
        if (!fileInfo.Directory.Exists)
          fileInfo.Directory.Create();
        if (fileInfo.Exists)
          fileInfo.Delete();
        using (XmlWriter xmlWriter = XmlWriter.Create(fileInfo.FullName, new XmlWriterSettings()
        {
          Indent = true,
          OmitXmlDeclaration = true,
          NewLineOnAttributes = true
        }))
        {
          xmlWriter.WriteStartDocument();
          xmlWriter.WriteStartElement("DB");
          xmlWriter.WriteEndElement();
          xmlWriter.Flush();
        }
        using (XmlReader reader = XmlReader.Create(fileInfo.FullName))
        {
          XmlDocument xmlDocument = new XmlDocument();
          xmlDocument.Load(reader);
          XmlElement documentElement = xmlDocument.DocumentElement;
          documentElement.RemoveAll();
          foreach (DataColumn column in (InternalDataCollectionBase) clsDefine.g_dtInjectionDB.Columns)
          {
            XmlNode element = (XmlNode) xmlDocument.CreateElement(column.ColumnName);
            element.InnerText = p_drInjection[column.ColumnName].ToString();
            documentElement.AppendChild(element);
          }
          reader.Close();
          xmlDocument.Save(fileInfo.FullName);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmInjection]CreateInjDataFile):" + ex.Message));
      }
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.listBox_FileName = new ListBox();
      this.label_List = new Label();
      this.label_Data = new Label();
      this.label_ScrewDia = new Label();
      this.label_MaxRate = new Label();
      this.label_Company = new Label();
      this.label_MaxPressure = new Label();
      this.label_MaxStroke = new Label();
      this.label_Name = new Label();
      this.label5 = new Label();
      this.label_PreRatio = new Label();
      this.label_MaxClamp = new Label();
      this.label4 = new Label();
      this.newButton_Del = new NewButton();
      this.newButton_Edit = new NewButton();
      this.newButton_Add = new NewButton();
      this.unitTextBox_MaxRate = new UnitTextBox();
      this.newTextBox_DataLastModified = new NewTextBox();
      this.unitTextBox_ScrewDia = new UnitTextBox();
      this.newTextBox_DataSource = new NewTextBox();
      this.unitTextBox_MaxPressure = new UnitTextBox();
      this.unitTextBox_MaxStroke = new UnitTextBox();
      this.newTextBox_PreRatio = new NewTextBox();
      this.newTextBox_Company = new NewTextBox();
      this.newTextBox_FileName = new NewTextBox();
      this.newTextBox_Name = new NewTextBox();
      this.unitTextBox_MaxClampForce = new UnitTextBox();
      this.newButton_Export = new NewButton();
      this.newButton_Import = new NewButton();
      this.SuspendLayout();
      this.listBox_FileName.FormattingEnabled = true;
      this.listBox_FileName.ItemHeight = 15;
      this.listBox_FileName.Location = new Point(5, 24);
      this.listBox_FileName.Name = "listBox_FileName";
      this.listBox_FileName.SelectionMode = SelectionMode.MultiExtended;
      this.listBox_FileName.Size = new Size(303, 154);
      this.listBox_FileName.TabIndex = 18;
      this.listBox_FileName.SelectedIndexChanged += new EventHandler(this.listBox_FileName_SelectedIndexChanged);
      this.label_List.BackColor = Color.FromArgb(229, 238, 248);
      this.label_List.BorderStyle = BorderStyle.FixedSingle;
      this.label_List.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_List.ForeColor = Color.MidnightBlue;
      this.label_List.Location = new Point(5, 5);
      this.label_List.Name = "label_List";
      this.label_List.Size = new Size(303, 20);
      this.label_List.TabIndex = 24;
      this.label_List.Text = "사출기 리스트";
      this.label_List.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Data.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Data.BorderStyle = BorderStyle.FixedSingle;
      this.label_Data.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Data.ForeColor = Color.MidnightBlue;
      this.label_Data.Location = new Point(312, 5);
      this.label_Data.Name = "label_Data";
      this.label_Data.Size = new Size(251, 20);
      this.label_Data.TabIndex = 25;
      this.label_Data.Text = "사출기 데이터";
      this.label_Data.TextAlign = ContentAlignment.MiddleCenter;
      this.label_ScrewDia.BackColor = Color.Lavender;
      this.label_ScrewDia.BorderStyle = BorderStyle.FixedSingle;
      this.label_ScrewDia.Location = new Point(312, 150);
      this.label_ScrewDia.Name = "label_ScrewDia";
      this.label_ScrewDia.Size = new Size(126, 22);
      this.label_ScrewDia.TabIndex = 28;
      this.label_ScrewDia.Text = "스크류 직경";
      this.label_ScrewDia.TextAlign = ContentAlignment.MiddleLeft;
      this.label_MaxRate.BackColor = Color.Lavender;
      this.label_MaxRate.BorderStyle = BorderStyle.FixedSingle;
      this.label_MaxRate.Location = new Point(312, 129);
      this.label_MaxRate.Name = "label_MaxRate";
      this.label_MaxRate.Size = new Size(126, 22);
      this.label_MaxRate.TabIndex = 29;
      this.label_MaxRate.Text = "최대 사출률";
      this.label_MaxRate.TextAlign = ContentAlignment.MiddleLeft;
      this.label_Company.BackColor = Color.Lavender;
      this.label_Company.BorderStyle = BorderStyle.FixedSingle;
      this.label_Company.Location = new Point(312, 45);
      this.label_Company.Name = "label_Company";
      this.label_Company.Size = new Size(126, 22);
      this.label_Company.TabIndex = 32;
      this.label_Company.Text = "사출기 회사";
      this.label_Company.TextAlign = ContentAlignment.MiddleLeft;
      this.label_MaxPressure.BackColor = Color.Lavender;
      this.label_MaxPressure.BorderStyle = BorderStyle.FixedSingle;
      this.label_MaxPressure.Location = new Point(312, 171);
      this.label_MaxPressure.Name = "label_MaxPressure";
      this.label_MaxPressure.Size = new Size(126, 22);
      this.label_MaxPressure.TabIndex = 33;
      this.label_MaxPressure.Text = "사출기 최대 사출 압력";
      this.label_MaxPressure.TextAlign = ContentAlignment.MiddleLeft;
      this.label_MaxStroke.BackColor = Color.Lavender;
      this.label_MaxStroke.BorderStyle = BorderStyle.FixedSingle;
      this.label_MaxStroke.Location = new Point(312, 108);
      this.label_MaxStroke.Name = "label_MaxStroke";
      this.label_MaxStroke.Size = new Size(126, 22);
      this.label_MaxStroke.TabIndex = 35;
      this.label_MaxStroke.Text = "최대 사출 스트로크";
      this.label_MaxStroke.TextAlign = ContentAlignment.MiddleLeft;
      this.label_Name.BackColor = Color.Lavender;
      this.label_Name.BorderStyle = BorderStyle.FixedSingle;
      this.label_Name.Location = new Point(312, 24);
      this.label_Name.Name = "label_Name";
      this.label_Name.Size = new Size(126, 22);
      this.label_Name.TabIndex = 36;
      this.label_Name.Text = "사출기 이름";
      this.label_Name.TextAlign = ContentAlignment.MiddleLeft;
      this.label5.BackColor = Color.Lavender;
      this.label5.BorderStyle = BorderStyle.FixedSingle;
      this.label5.Location = new Point(312, 87);
      this.label5.Name = "label5";
      this.label5.Size = new Size(126, 22);
      this.label5.TabIndex = 31;
      this.label5.Text = "Data last modified";
      this.label5.TextAlign = ContentAlignment.MiddleLeft;
      this.label_PreRatio.BackColor = Color.Lavender;
      this.label_PreRatio.BorderStyle = BorderStyle.FixedSingle;
      this.label_PreRatio.Location = new Point(312, 213);
      this.label_PreRatio.Name = "label_PreRatio";
      this.label_PreRatio.Size = new Size(126, 22);
      this.label_PreRatio.TabIndex = 30;
      this.label_PreRatio.Text = "증압비";
      this.label_PreRatio.TextAlign = ContentAlignment.MiddleLeft;
      this.label_MaxClamp.BackColor = Color.Lavender;
      this.label_MaxClamp.BorderStyle = BorderStyle.FixedSingle;
      this.label_MaxClamp.Location = new Point(312, 192);
      this.label_MaxClamp.Name = "label_MaxClamp";
      this.label_MaxClamp.Size = new Size(126, 22);
      this.label_MaxClamp.TabIndex = 34;
      this.label_MaxClamp.Text = "사출기 최대 형체력";
      this.label_MaxClamp.TextAlign = ContentAlignment.MiddleLeft;
      this.label4.BackColor = Color.Lavender;
      this.label4.BorderStyle = BorderStyle.FixedSingle;
      this.label4.Location = new Point(312, 66);
      this.label4.Name = "label4";
      this.label4.Size = new Size(126, 22);
      this.label4.TabIndex = 27;
      this.label4.Text = "Data Source";
      this.label4.TextAlign = ContentAlignment.MiddleLeft;
      this.newButton_Del.ButtonBackColor = Color.White;
      this.newButton_Del.ButtonText = "삭제";
      this.newButton_Del.FlatBorderSize = 1;
      this.newButton_Del.FlatStyle = FlatStyle.Flat;
      this.newButton_Del.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Del.Image = (Image) Resources.Del;
      this.newButton_Del.ImageAlign = ContentAlignment.MiddleLeft;
      this.newButton_Del.Location = new Point(123, 198);
      this.newButton_Del.Name = "newButton_Del";
      this.newButton_Del.Size = new Size(60, 37);
      this.newButton_Del.TabIndex = 48;
      this.newButton_Del.TextAlign = ContentAlignment.MiddleRight;
      this.newButton_Del.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Del.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_Edit.ButtonBackColor = Color.White;
      this.newButton_Edit.ButtonText = "수정";
      this.newButton_Edit.FlatBorderSize = 1;
      this.newButton_Edit.FlatStyle = FlatStyle.Flat;
      this.newButton_Edit.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Edit.Image = (Image) Resources.Edit;
      this.newButton_Edit.ImageAlign = ContentAlignment.MiddleLeft;
      this.newButton_Edit.Location = new Point(64, 198);
      this.newButton_Edit.Name = "newButton_Edit";
      this.newButton_Edit.Size = new Size(60, 37);
      this.newButton_Edit.TabIndex = 49;
      this.newButton_Edit.TextAlign = ContentAlignment.MiddleRight;
      this.newButton_Edit.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Edit.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_Add.ButtonBackColor = Color.White;
      this.newButton_Add.ButtonText = "추가";
      this.newButton_Add.FlatBorderSize = 1;
      this.newButton_Add.FlatStyle = FlatStyle.Flat;
      this.newButton_Add.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Add.Image = (Image) Resources.Add;
      this.newButton_Add.ImageAlign = ContentAlignment.MiddleLeft;
      this.newButton_Add.Location = new Point(5, 198);
      this.newButton_Add.Name = "newButton_Add";
      this.newButton_Add.Size = new Size(60, 37);
      this.newButton_Add.TabIndex = 50;
      this.newButton_Add.TextAlign = ContentAlignment.MiddleRight;
      this.newButton_Add.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Add.NewClick += new EventHandler(this.newButton_NewClick);
      this.unitTextBox_MaxRate.BackColor = Color.White;
      this.unitTextBox_MaxRate.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_MaxRate.ControlBackColor = Color.White;
      this.unitTextBox_MaxRate.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_MaxRate.IsDigit = true;
      this.unitTextBox_MaxRate.Location = new Point(437, 129);
      this.unitTextBox_MaxRate.Name = "unitTextBox_MaxRate";
      this.unitTextBox_MaxRate.Size = new Size(126, 22);
      this.unitTextBox_MaxRate.TabIndex = 5;
      this.unitTextBox_MaxRate.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_MaxRate.Unit = "cm\u00B3/s";
      this.unitTextBox_MaxRate.Value = "";
      this.newTextBox_DataLastModified.BackColor = SystemColors.Window;
      this.newTextBox_DataLastModified.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_DataLastModified.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_DataLastModified.IsDigit = false;
      this.newTextBox_DataLastModified.Lines = new string[0];
      this.newTextBox_DataLastModified.Location = new Point(437, 87);
      this.newTextBox_DataLastModified.MultiLine = false;
      this.newTextBox_DataLastModified.Name = "newTextBox_DataLastModified";
      this.newTextBox_DataLastModified.ReadOnly = false;
      this.newTextBox_DataLastModified.Size = new Size(126, 22);
      this.newTextBox_DataLastModified.TabIndex = 3;
      this.newTextBox_DataLastModified.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_DataLastModified.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_DataLastModified.TextForeColor = SystemColors.WindowText;
      this.newTextBox_DataLastModified.Value = "";
      this.unitTextBox_ScrewDia.BackColor = Color.White;
      this.unitTextBox_ScrewDia.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_ScrewDia.ControlBackColor = Color.White;
      this.unitTextBox_ScrewDia.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_ScrewDia.IsDigit = true;
      this.unitTextBox_ScrewDia.Location = new Point(437, 150);
      this.unitTextBox_ScrewDia.Name = "unitTextBox_ScrewDia";
      this.unitTextBox_ScrewDia.Size = new Size(126, 22);
      this.unitTextBox_ScrewDia.TabIndex = 6;
      this.unitTextBox_ScrewDia.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_ScrewDia.Unit = "mm";
      this.unitTextBox_ScrewDia.Value = "";
      this.newTextBox_DataSource.BackColor = SystemColors.Window;
      this.newTextBox_DataSource.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_DataSource.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_DataSource.IsDigit = false;
      this.newTextBox_DataSource.Lines = new string[0];
      this.newTextBox_DataSource.Location = new Point(437, 66);
      this.newTextBox_DataSource.MultiLine = false;
      this.newTextBox_DataSource.Name = "newTextBox_DataSource";
      this.newTextBox_DataSource.ReadOnly = false;
      this.newTextBox_DataSource.Size = new Size(126, 22);
      this.newTextBox_DataSource.TabIndex = 2;
      this.newTextBox_DataSource.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_DataSource.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_DataSource.TextForeColor = SystemColors.WindowText;
      this.newTextBox_DataSource.Value = "";
      this.unitTextBox_MaxPressure.BackColor = Color.White;
      this.unitTextBox_MaxPressure.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_MaxPressure.ControlBackColor = Color.White;
      this.unitTextBox_MaxPressure.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_MaxPressure.IsDigit = true;
      this.unitTextBox_MaxPressure.Location = new Point(437, 171);
      this.unitTextBox_MaxPressure.Name = "unitTextBox_MaxPressure";
      this.unitTextBox_MaxPressure.Size = new Size(126, 22);
      this.unitTextBox_MaxPressure.TabIndex = 7;
      this.unitTextBox_MaxPressure.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_MaxPressure.Unit = "MPa";
      this.unitTextBox_MaxPressure.Value = "";
      this.unitTextBox_MaxStroke.BackColor = Color.White;
      this.unitTextBox_MaxStroke.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_MaxStroke.ControlBackColor = Color.White;
      this.unitTextBox_MaxStroke.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_MaxStroke.IsDigit = true;
      this.unitTextBox_MaxStroke.Location = new Point(437, 108);
      this.unitTextBox_MaxStroke.Name = "unitTextBox_MaxStroke";
      this.unitTextBox_MaxStroke.Size = new Size(126, 22);
      this.unitTextBox_MaxStroke.TabIndex = 4;
      this.unitTextBox_MaxStroke.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_MaxStroke.Unit = "mm";
      this.unitTextBox_MaxStroke.Value = "";
      this.newTextBox_PreRatio.BackColor = SystemColors.Window;
      this.newTextBox_PreRatio.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_PreRatio.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_PreRatio.IsDigit = false;
      this.newTextBox_PreRatio.Lines = new string[0];
      this.newTextBox_PreRatio.Location = new Point(437, 213);
      this.newTextBox_PreRatio.MultiLine = false;
      this.newTextBox_PreRatio.Name = "newTextBox_PreRatio";
      this.newTextBox_PreRatio.ReadOnly = false;
      this.newTextBox_PreRatio.Size = new Size(126, 22);
      this.newTextBox_PreRatio.TabIndex = 9;
      this.newTextBox_PreRatio.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_PreRatio.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_PreRatio.TextForeColor = SystemColors.WindowText;
      this.newTextBox_PreRatio.Value = "";
      this.newTextBox_Company.BackColor = SystemColors.Window;
      this.newTextBox_Company.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Company.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Company.IsDigit = false;
      this.newTextBox_Company.Lines = new string[0];
      this.newTextBox_Company.Location = new Point(437, 45);
      this.newTextBox_Company.MultiLine = false;
      this.newTextBox_Company.Name = "newTextBox_Company";
      this.newTextBox_Company.ReadOnly = false;
      this.newTextBox_Company.Size = new Size(126, 22);
      this.newTextBox_Company.TabIndex = 1;
      this.newTextBox_Company.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Company.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Company.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Company.Value = "";
      this.newTextBox_FileName.BackColor = SystemColors.Window;
      this.newTextBox_FileName.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_FileName.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_FileName.IsDigit = false;
      this.newTextBox_FileName.Lines = new string[0];
      this.newTextBox_FileName.Location = new Point(5, 177);
      this.newTextBox_FileName.MultiLine = false;
      this.newTextBox_FileName.Name = "newTextBox_FileName";
      this.newTextBox_FileName.ReadOnly = false;
      this.newTextBox_FileName.Size = new Size(303, 22);
      this.newTextBox_FileName.TabIndex = 0;
      this.newTextBox_FileName.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_FileName.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_FileName.TextForeColor = SystemColors.WindowText;
      this.newTextBox_FileName.Value = "";
      this.newTextBox_Name.BackColor = SystemColors.Window;
      this.newTextBox_Name.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Name.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Name.IsDigit = false;
      this.newTextBox_Name.Lines = new string[0];
      this.newTextBox_Name.Location = new Point(437, 24);
      this.newTextBox_Name.MultiLine = false;
      this.newTextBox_Name.Name = "newTextBox_Name";
      this.newTextBox_Name.ReadOnly = false;
      this.newTextBox_Name.Size = new Size(126, 22);
      this.newTextBox_Name.TabIndex = 0;
      this.newTextBox_Name.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Name.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Name.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Name.Value = "";
      this.unitTextBox_MaxClampForce.BackColor = Color.White;
      this.unitTextBox_MaxClampForce.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_MaxClampForce.ControlBackColor = Color.White;
      this.unitTextBox_MaxClampForce.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_MaxClampForce.IsDigit = true;
      this.unitTextBox_MaxClampForce.Location = new Point(437, 192);
      this.unitTextBox_MaxClampForce.Name = "unitTextBox_MaxClampForce";
      this.unitTextBox_MaxClampForce.Size = new Size(126, 22);
      this.unitTextBox_MaxClampForce.TabIndex = 8;
      this.unitTextBox_MaxClampForce.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_MaxClampForce.Unit = "tone";
      this.unitTextBox_MaxClampForce.Value = "";
      this.newButton_Export.ButtonBackColor = Color.White;
      this.newButton_Export.ButtonText = "출력";
      this.newButton_Export.FlatBorderSize = 1;
      this.newButton_Export.FlatStyle = FlatStyle.Flat;
      this.newButton_Export.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Export.ForeColor = Color.Navy;
      this.newButton_Export.Image = (Image) Resources.Export;
      this.newButton_Export.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Export.Location = new Point(248, 198);
      this.newButton_Export.Name = "newButton_Export";
      this.newButton_Export.Size = new Size(60, 37);
      this.newButton_Export.TabIndex = 97;
      this.newButton_Export.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Export.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Export.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_Import.ButtonBackColor = Color.White;
      this.newButton_Import.ButtonText = "삽입";
      this.newButton_Import.FlatBorderSize = 1;
      this.newButton_Import.FlatStyle = FlatStyle.Flat;
      this.newButton_Import.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Import.ForeColor = Color.Navy;
      this.newButton_Import.Image = (Image) Resources.Import;
      this.newButton_Import.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Import.Location = new Point(189, 198);
      this.newButton_Import.Name = "newButton_Import";
      this.newButton_Import.Size = new Size(60, 37);
      this.newButton_Import.TabIndex = 98;
      this.newButton_Import.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Import.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Import.NewClick += new EventHandler(this.newButton_NewClick);
      this.AutoScaleMode = AutoScaleMode.None;
      this.ClientSize = new Size(569, 241);
      this.Controls.Add((Control) this.newButton_Export);
      this.Controls.Add((Control) this.newButton_Import);
      this.Controls.Add((Control) this.listBox_FileName);
      this.Controls.Add((Control) this.newButton_Del);
      this.Controls.Add((Control) this.newButton_Edit);
      this.Controls.Add((Control) this.newButton_Add);
      this.Controls.Add((Control) this.label_List);
      this.Controls.Add((Control) this.label_Data);
      this.Controls.Add((Control) this.label_ScrewDia);
      this.Controls.Add((Control) this.label_MaxRate);
      this.Controls.Add((Control) this.unitTextBox_MaxRate);
      this.Controls.Add((Control) this.newTextBox_DataLastModified);
      this.Controls.Add((Control) this.unitTextBox_ScrewDia);
      this.Controls.Add((Control) this.label_Company);
      this.Controls.Add((Control) this.label_MaxPressure);
      this.Controls.Add((Control) this.newTextBox_DataSource);
      this.Controls.Add((Control) this.unitTextBox_MaxPressure);
      this.Controls.Add((Control) this.unitTextBox_MaxStroke);
      this.Controls.Add((Control) this.label_MaxStroke);
      this.Controls.Add((Control) this.label_Name);
      this.Controls.Add((Control) this.newTextBox_PreRatio);
      this.Controls.Add((Control) this.newTextBox_Company);
      this.Controls.Add((Control) this.newTextBox_FileName);
      this.Controls.Add((Control) this.newTextBox_Name);
      this.Controls.Add((Control) this.label5);
      this.Controls.Add((Control) this.label_PreRatio);
      this.Controls.Add((Control) this.unitTextBox_MaxClampForce);
      this.Controls.Add((Control) this.label_MaxClamp);
      this.Controls.Add((Control) this.label4);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmInjection);
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "사출기 설정";
      this.Load += new EventHandler(this.frmInjection_Load);
      this.KeyDown += new KeyEventHandler(this.frmInjection_KeyDown);
      this.ResumeLayout(false);
    }
  }
}
