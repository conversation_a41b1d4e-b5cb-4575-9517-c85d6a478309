
# Extractor de Códigos MSCD de Moldflow

Este directorio contiene archivos generados para analizar los códigos MSCD utilizados en Moldflow.

## Estructura del archivo CSV

El archivo `mscd_codes.csv` contiene los siguientes campos:

- **mscd_code**: Código identificador único MSCD
- **num_lines**: Número de líneas del mensaje
- **format_param1**: Parámetro de formato 1
- **format_param2**: Parámetro de formato 2
- **format_param3**: Parámetro de formato 3
- **format_param4**: Parámetro de formato 4
- **num_format_params**: Número de parámetros de formato que se esperan
- **category_severity**: Categoría o severidad del mensaje
- **message**: Texto del mensaje
- **format_info**: Información de formato y unidades (separados por punto y coma)

## Uso

Este CSV puede utilizarse para relacionar los códigos MSCD con los valores correspondientes en archivos de resultados .out de Moldflow.
