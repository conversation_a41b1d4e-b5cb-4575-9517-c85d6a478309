﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmProject
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDLog4Net;
using HDMFUserControl;
using HDMoldFlow.Properties;
using Microsoft.WindowsAPICodePack.Dialogs;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public class frmProject : Form
  {
    public List<string> m_lst_strCAD = new List<string>();
    public string m_strProject = "";
    public bool m_isMoveZero = true;
    public bool m_isCreate3D = false;
    private IContainer components = (IContainer) null;
    private NewTextBox newTextBox_ProjPath;
    private Label label_Project_Path;
    private NewButton newButton_ProjPath;
    private ListBox listBox_Cad;
    private Label label_CAD_File;
    private NewButton newButton_Apply;
    private NewButton newButton_AddCad;
    private NewButton newButton_RemoveCad;
    private Label label_Project_Name;
    private NewTextBox newTextBox_ProjName;
    private CheckBox checkBox_UseCadName;
    private CheckBox checkBox_MoveZero;
    private CheckBox checkBox_Create3D;

    public frmProject()
    {
      this.InitializeComponent();
      this.newButton_AddCad.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_AddCad.Image);
      this.newButton_RemoveCad.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_RemoveCad.Image);
      this.newButton_Apply.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Apply.Image);
      this.Text = LocaleControl.getInstance().GetString("IDS_CREATE_PROJECT");
      this.label_Project_Path.Text = LocaleControl.getInstance().GetString("IDS_PROJECT_PATH");
      this.label_Project_Name.Text = LocaleControl.getInstance().GetString("IDS_PROJECT_NAME");
      this.checkBox_UseCadName.Text = LocaleControl.getInstance().GetString("IDS_APPLY_CAD_FILE_NAME");
      this.checkBox_MoveZero.Text = LocaleControl.getInstance().GetString("IDS_MOVE_ZERO");
      this.checkBox_Create3D.Text = LocaleControl.getInstance().GetString("IDS_CREATE_3D_MESH");
      this.label_CAD_File.Text = LocaleControl.getInstance().GetString("IDS_CAD_FILE");
      this.newButton_ProjPath.ButtonText = LocaleControl.getInstance().GetString("IDS_CHANGE");
      this.newButton_Apply.ButtonText = LocaleControl.getInstance().GetString("IDS_CREATE");
    }

    private void frmProject_Load(object sender, EventArgs e)
    {
      this.ActiveControl = (Control) this.label_CAD_File;
      this.newTextBox_ProjPath.Value = clsDefine.g_diBasicProject.FullName;
      this.checkBox_UseCadName.Checked = true;
      this.checkBox_MoveZero.Checked = true;
      this.checkBox_Create3D.Checked = false;
    }

    private void newButton_ProjPath_NewClick(object sender, EventArgs e)
    {
      CommonOpenFileDialog commonOpenFileDialog = new CommonOpenFileDialog()
      {
        IsFolderPicker = true
      };
      commonOpenFileDialog.DefaultFileName = this.newTextBox_ProjPath.Value;
      if (commonOpenFileDialog.ShowDialog() != CommonFileDialogResult.Ok)
        return;
      clsDefine.g_diBasicProject = new DirectoryInfo(commonOpenFileDialog.FileName);
      this.newTextBox_ProjPath.Value = commonOpenFileDialog.FileName;
      clsUtill.WriteINI("Project", "Path", clsDefine.g_diBasicProject.FullName, clsDefine.g_fiProjCfg.FullName);
    }

    private void checkBox_UseCadName_CheckedChanged(object sender, EventArgs e)
    {
      if (!this.checkBox_UseCadName.Checked)
      {
        this.newTextBox_ProjName.TextBoxBackColor = Color.White;
        this.newTextBox_ProjName.Enabled = true;
      }
      else
      {
        this.newTextBox_ProjName.TextBoxBackColor = Color.WhiteSmoke;
        this.newTextBox_ProjName.Enabled = false;
        this.newTextBox_ProjName.Value = "";
      }
    }

    private void newButton_Cad_NewClick(object sender, EventArgs e)
    {
      if (sender as NewButton == this.newButton_AddCad)
        this.AddCAD();
      else
        this.RemoveCAD();
    }

    private void AddCAD()
    {
      OpenFileDialog openFileDialog = new OpenFileDialog();
      openFileDialog.Multiselect = true;
      if (openFileDialog.ShowDialog((IWin32Window) this) != DialogResult.OK)
        return;
      foreach (string fileName in openFileDialog.FileNames)
      {
        if (!this.listBox_Cad.Items.Contains((object) Path.GetFileNameWithoutExtension(fileName)))
        {
          this.listBox_Cad.Items.Add((object) Path.GetFileNameWithoutExtension(fileName));
          this.m_lst_strCAD.Add(fileName);
        }
      }
      this.listBox_Cad.ClearSelected();
    }

    private void RemoveCAD()
    {
      if (this.listBox_Cad.Items.Count == 0 || this.listBox_Cad.SelectedItems.Count == 0)
        return;
      try
      {
        List<string> source = new List<string>();
        foreach (string selectedItem in this.listBox_Cad.SelectedItems)
          source.Add(this.m_lst_strCAD[this.listBox_Cad.Items.IndexOf((object) selectedItem)]);
        while (true)
        {
          if (source.Count != 0)
          {
            string path = source.First<string>();
            this.listBox_Cad.Items.Remove((object) Path.GetFileNameWithoutExtension(path));
            source.Remove(path);
            this.m_lst_strCAD.Remove(path);
          }
          else
            break;
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProject]RemoveCAD):" + ex.Message));
      }
    }

    private void newButton_Apply_NewClick(object sender, EventArgs e)
    {
      if (!this.checkBox_UseCadName.Checked && (this.newTextBox_ProjName.Value == null || this.newTextBox_ProjName.Value == ""))
        return;
      this.m_strProject = this.newTextBox_ProjName.Value;
      this.m_isMoveZero = this.checkBox_MoveZero.Checked;
      this.m_isCreate3D = this.checkBox_Create3D.Checked;
      this.DialogResult = DialogResult.OK;
      this.Close();
    }

    private void frmProject_KeyDown(object sender, KeyEventArgs e)
    {
      if (e.KeyCode != Keys.Escape)
        return;
      this.Close();
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.label_Project_Path = new Label();
      this.listBox_Cad = new ListBox();
      this.label_CAD_File = new Label();
      this.label_Project_Name = new Label();
      this.checkBox_UseCadName = new CheckBox();
      this.newButton_RemoveCad = new NewButton();
      this.newButton_AddCad = new NewButton();
      this.newButton_Apply = new NewButton();
      this.newButton_ProjPath = new NewButton();
      this.newTextBox_ProjName = new NewTextBox();
      this.newTextBox_ProjPath = new NewTextBox();
      this.checkBox_MoveZero = new CheckBox();
      this.checkBox_Create3D = new CheckBox();
      this.SuspendLayout();
      this.label_Project_Path.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Project_Path.BorderStyle = BorderStyle.FixedSingle;
      this.label_Project_Path.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Project_Path.ForeColor = Color.MidnightBlue;
      this.label_Project_Path.Location = new Point(5, 4);
      this.label_Project_Path.Name = "label_Project_Path";
      this.label_Project_Path.Size = new Size(406, 20);
      this.label_Project_Path.TabIndex = 22;
      this.label_Project_Path.Text = "프로젝트 경로";
      this.label_Project_Path.TextAlign = ContentAlignment.MiddleCenter;
      this.listBox_Cad.BackColor = Color.White;
      this.listBox_Cad.FormattingEnabled = true;
      this.listBox_Cad.ItemHeight = 15;
      this.listBox_Cad.Location = new Point(5, 143);
      this.listBox_Cad.Name = "listBox_Cad";
      this.listBox_Cad.SelectionMode = SelectionMode.MultiExtended;
      this.listBox_Cad.Size = new Size(406, 229);
      this.listBox_Cad.TabIndex = 24;
      this.label_CAD_File.BackColor = Color.FromArgb(229, 238, 248);
      this.label_CAD_File.BorderStyle = BorderStyle.FixedSingle;
      this.label_CAD_File.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_CAD_File.ForeColor = Color.MidnightBlue;
      this.label_CAD_File.Location = new Point(5, 124);
      this.label_CAD_File.Name = "label_CAD_File";
      this.label_CAD_File.Size = new Size(406, 20);
      this.label_CAD_File.TabIndex = 22;
      this.label_CAD_File.Text = "CAD 파일";
      this.label_CAD_File.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Project_Name.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Project_Name.BorderStyle = BorderStyle.FixedSingle;
      this.label_Project_Name.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Project_Name.ForeColor = Color.MidnightBlue;
      this.label_Project_Name.Location = new Point(5, 53);
      this.label_Project_Name.Name = "label_Project_Name";
      this.label_Project_Name.Size = new Size(406, 20);
      this.label_Project_Name.TabIndex = 22;
      this.label_Project_Name.Text = "프로젝트 명";
      this.label_Project_Name.TextAlign = ContentAlignment.MiddleCenter;
      this.checkBox_UseCadName.Location = new Point(5, 100);
      this.checkBox_UseCadName.Name = "checkBox_UseCadName";
      this.checkBox_UseCadName.Size = new Size(118, 19);
      this.checkBox_UseCadName.TabIndex = 29;
      this.checkBox_UseCadName.Text = "CAD 파일명 적용";
      this.checkBox_UseCadName.UseVisualStyleBackColor = true;
      this.checkBox_UseCadName.CheckedChanged += new EventHandler(this.checkBox_UseCadName_CheckedChanged);
      this.newButton_RemoveCad.BackColor = Color.FromArgb(229, 238, 248);
      this.newButton_RemoveCad.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_RemoveCad.ButtonText = "";
      this.newButton_RemoveCad.FlatBorderSize = 0;
      this.newButton_RemoveCad.FlatStyle = FlatStyle.Flat;
      this.newButton_RemoveCad.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_RemoveCad.Image = (Image) Resources.Del;
      this.newButton_RemoveCad.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_RemoveCad.Location = new Point(28, 125);
      this.newButton_RemoveCad.Name = "newButton_RemoveCad";
      this.newButton_RemoveCad.Size = new Size(18, 18);
      this.newButton_RemoveCad.TabIndex = 28;
      this.newButton_RemoveCad.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_RemoveCad.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_RemoveCad.NewClick += new EventHandler(this.newButton_Cad_NewClick);
      this.newButton_AddCad.BackColor = Color.FromArgb(229, 238, 248);
      this.newButton_AddCad.ButtonBackColor = Color.FromArgb(229, 238, 248);
      this.newButton_AddCad.ButtonText = "";
      this.newButton_AddCad.FlatBorderSize = 0;
      this.newButton_AddCad.FlatStyle = FlatStyle.Flat;
      this.newButton_AddCad.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_AddCad.Image = (Image) Resources.Add;
      this.newButton_AddCad.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_AddCad.Location = new Point(6, 125);
      this.newButton_AddCad.Name = "newButton_AddCad";
      this.newButton_AddCad.Size = new Size(18, 18);
      this.newButton_AddCad.TabIndex = 28;
      this.newButton_AddCad.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_AddCad.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_AddCad.NewClick += new EventHandler(this.newButton_Cad_NewClick);
      this.newButton_Apply.ButtonBackColor = Color.White;
      this.newButton_Apply.ButtonText = "생성";
      this.newButton_Apply.FlatBorderSize = 1;
      this.newButton_Apply.FlatStyle = FlatStyle.Flat;
      this.newButton_Apply.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Apply.Image = (Image) Resources.OK;
      this.newButton_Apply.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Apply.Location = new Point(5, 376);
      this.newButton_Apply.Name = "newButton_Apply";
      this.newButton_Apply.Size = new Size(406, 23);
      this.newButton_Apply.TabIndex = 25;
      this.newButton_Apply.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Apply.NewClick += new EventHandler(this.newButton_Apply_NewClick);
      this.newButton_ProjPath.ButtonBackColor = Color.White;
      this.newButton_ProjPath.ButtonText = "변경";
      this.newButton_ProjPath.FlatBorderSize = 1;
      this.newButton_ProjPath.FlatStyle = FlatStyle.Flat;
      this.newButton_ProjPath.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_ProjPath.Image = (Image) null;
      this.newButton_ProjPath.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_ProjPath.Location = new Point(362, 23);
      this.newButton_ProjPath.Name = "newButton_ProjPath";
      this.newButton_ProjPath.Size = new Size(49, 23);
      this.newButton_ProjPath.TabIndex = 23;
      this.newButton_ProjPath.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_ProjPath.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_ProjPath.NewClick += new EventHandler(this.newButton_ProjPath_NewClick);
      this.newTextBox_ProjName.BackColor = Color.White;
      this.newTextBox_ProjName.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_ProjName.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_ProjName.IsDigit = false;
      this.newTextBox_ProjName.Lines = new string[0];
      this.newTextBox_ProjName.Location = new Point(5, 72);
      this.newTextBox_ProjName.MultiLine = false;
      this.newTextBox_ProjName.Name = "newTextBox_ProjName";
      this.newTextBox_ProjName.ReadOnly = false;
      this.newTextBox_ProjName.Size = new Size(406, 23);
      this.newTextBox_ProjName.TabIndex = 0;
      this.newTextBox_ProjName.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_ProjName.TextBoxBackColor = Color.White;
      this.newTextBox_ProjName.TextForeColor = SystemColors.WindowText;
      this.newTextBox_ProjName.Value = "";
      this.newTextBox_ProjPath.BackColor = Color.WhiteSmoke;
      this.newTextBox_ProjPath.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_ProjPath.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_ProjPath.IsDigit = false;
      this.newTextBox_ProjPath.Lines = new string[0];
      this.newTextBox_ProjPath.Location = new Point(5, 23);
      this.newTextBox_ProjPath.MultiLine = false;
      this.newTextBox_ProjPath.Name = "newTextBox_ProjPath";
      this.newTextBox_ProjPath.ReadOnly = false;
      this.newTextBox_ProjPath.Size = new Size(358, 23);
      this.newTextBox_ProjPath.TabIndex = 0;
      this.newTextBox_ProjPath.TextAlign = HorizontalAlignment.Left;
      this.newTextBox_ProjPath.TextBoxBackColor = Color.WhiteSmoke;
      this.newTextBox_ProjPath.TextForeColor = SystemColors.WindowText;
      this.newTextBox_ProjPath.Value = "";
      this.checkBox_MoveZero.Location = new Point(149, 100);
      this.checkBox_MoveZero.Name = "checkBox_MoveZero";
      this.checkBox_MoveZero.Size = new Size(118, 19);
      this.checkBox_MoveZero.TabIndex = 30;
      this.checkBox_MoveZero.Text = "원점으로 이동";
      this.checkBox_MoveZero.UseVisualStyleBackColor = true;
      this.checkBox_Create3D.Location = new Point(285, 100);
      this.checkBox_Create3D.Name = "checkBox_Create3D";
      this.checkBox_Create3D.Size = new Size(118, 19);
      this.checkBox_Create3D.TabIndex = 31;
      this.checkBox_Create3D.Text = "3D 메쉬 생성";
      this.checkBox_Create3D.UseVisualStyleBackColor = true;
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(417, 405);
      this.Controls.Add((Control) this.checkBox_Create3D);
      this.Controls.Add((Control) this.checkBox_MoveZero);
      this.Controls.Add((Control) this.checkBox_UseCadName);
      this.Controls.Add((Control) this.newButton_RemoveCad);
      this.Controls.Add((Control) this.newButton_AddCad);
      this.Controls.Add((Control) this.newButton_Apply);
      this.Controls.Add((Control) this.listBox_Cad);
      this.Controls.Add((Control) this.newButton_ProjPath);
      this.Controls.Add((Control) this.label_CAD_File);
      this.Controls.Add((Control) this.label_Project_Name);
      this.Controls.Add((Control) this.label_Project_Path);
      this.Controls.Add((Control) this.newTextBox_ProjName);
      this.Controls.Add((Control) this.newTextBox_ProjPath);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmProject);
      this.ShowInTaskbar = false;
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "프로젝트 생성";
      this.Load += new EventHandler(this.frmProject_Load);
      this.KeyDown += new KeyEventHandler(this.frmProject_KeyDown);
      this.ResumeLayout(false);
    }
  }
}
