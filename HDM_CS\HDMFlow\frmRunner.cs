﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmRunner
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDLog4Net;
using HDMFUserControl;
using HDMoldFlow.Properties;
using HDMoldFlowLibrary;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using System.Xml;

namespace HDMoldFlow
{
  public class frmRunner : Form
  {
    private const int WS_EX_COMPOSITED = 33554432;
    public DataRow m_drStudy;
    private FileInfo m_fiNodeData = (FileInfo) null;
    private Dictionary<string, string> m_dicGate = (Dictionary<string, string>) null;
    private Dictionary<string, string> m_dicGroup = (Dictionary<string, string>) null;
    private IContainer components = (IContainer) null;
    private NewComboBox newComboBox_Item;
    private NewComboBox newComboBox_Company;
    private Label label_Hor_Two_Length;
    private Label label_Sprue_Dim2;
    private Label label_Sprue_Length;
    private Label label_Cavity_Occ;
    private Label label_DB_Item;
    private Label label_DB_Company;
    private Label label_Hor_Two_Dia;
    private Label label_Sprue_Dim1;
    private Panel panel_Hor_Runner_TwoType;
    private RadioButton radioButton_Hor_Two_Type3;
    private RadioButton radioButton_Hor_Two_Type2;
    private RadioButton radioButton_Hor_Two_Type1;
    private Panel panel_Sprue_Type;
    private RadioButton radioButton_Sprue_Hot;
    private RadioButton radioButton_Sprue_Cold;
    private Panel panel_Pin_Type;
    private RadioButton radioButton_Pin_HotSystem;
    private RadioButton radioButton_Pin_ColdSystem;
    private ListBox listBox_DB;
    private Label label_Sprue;
    private Label label_Cavity;
    private Label label_Hor_Runner;
    private Label label_Pin;
    private Label label_DB_List;
    private NewButton newButton_Apply;
    private NewTextBox newTextBox_Node;
    private Panel panel_Node;
    private NewButton newButton_Edit;
    private NewTextBox newTextBox_Sprue_Dim2;
    private NewTextBox newTextBox_Sprue_Dim1;
    private NewTextBox newTextBox_Sprue_Length;
    private NewTextBox newTextBox_Cavity_Occ;
    private NewTextBox newTextBox_Hor_Two_Length;
    private NewTextBox newTextBox_Hor_Two_Dia;
    private NewButton newButton_Node_Del;
    private NewButton newButton_Node_Add;
    private Label label7;
    private Label label8;
    private Label label_Pin_VavLoc;
    private Label label12;
    private Label label13;
    private NewTextBox newTextBox_VavOpen1;
    private NewComboBox newComboBox_VavTrigger;
    private NewComboBox newComboBox_VavState;
    private Panel panel_Valve;
    private NewTextBox newTextBox_VavNode;
    private NewTextBox newTextBox_VavClose1;
    private CheckBox checkBox_ValveCheck;
    private Panel panel_Valve_Sub;
    private Label label_CenterPoint;
    private Label label_CenterX;
    private NewTextBox newTextBox_CenterX;
    private Label label_CenterY;
    private NewTextBox newTextBox_CenterY;
    private RadioButton radioButton_Hor_Two_Type4;
    private Label label_Hor_Two_Dim1;
    private Label label_Hor_Two_Dim2;
    private Label label_Hor_Two_Angle;
    private NewTextBox newTextBox_Hor_Two_Dim1;
    private NewTextBox newTextBox_Hor_Two_Dim2;
    private NewTextBox newTextBox_Hor_Two_Angle;
    private ToolTip toolTip_Hor_Angle;
    private CheckBox checkBox_Hor_Two_Inter;
    private Label label_Hor_Two_Direction;
    private NewComboBox newComboBox_Hor_Two_Direction;
    private NewButton newButton_VavNode;
    private TabControl tabControl_Group;
    private TabPage tabPage_G1;
    private TabPage tabPage_G2;
    private Label label_Pin_Gate_G1;
    private Label label_Pin_Runner_G1;
    private Label label_Pin_GateDim1_G1;
    private Label label_Pin_RunnerLength_G1;
    private Label label_Pin_RunnerDim1_G1;
    private NewButton newButton_Pin_GateLength_G1;
    private Label label_Pin_GateDim2_G1;
    private Label label_Pin_RunnerDim2_G1;
    private NewTextBox newTextBox_Pin_GateLength_G1;
    private NewTextBox newTextBox_Pin_GateDim1_G1;
    private NewTextBox newTextBox_Pin_GateDim2_G1;
    private NewTextBox newTextBox_Pin_RunnerLength_G1;
    private NewTextBox newTextBox_Pin_RunnerDim1_G1;
    private NewTextBox newTextBox_Pin_RunnerDim2_G1;
    private Label label_Pin_RunnerLength_G2;
    private Label label_Pin_RunnerDim1_G2;
    private NewButton newButton_Pin_GateLength_G2;
    private Label label_Pin_RunnerDim2_G2;
    private NewTextBox newTextBox_Pin_GateLength_G2;
    private NewTextBox newTextBox_Pin_GateDim1_G2;
    private NewTextBox newTextBox_Pin_GateDim2_G2;
    private NewTextBox newTextBox_Pin_RunnerLength_G2;
    private NewTextBox newTextBox_Pin_RunnerDim1_G2;
    private NewTextBox newTextBox_Pin_RunnerDim2_G2;
    private TabPage tabPage_G3;
    private Label label_Pin_Gate_G3;
    private Label label_Pin_Runner_G3;
    private Label label_Pin_GateDim1_G3;
    private Label label_Pin_RunnerLength_G3;
    private Label label_Pin_RunnerDim1_G3;
    private NewButton newButton_Pin_GateLength_G3;
    private Label label_Pin_GateDim2_G3;
    private Label label_Pin_RunnerDim2_G3;
    private NewTextBox newTextBox_Pin_GateLength_G3;
    private NewTextBox newTextBox_Pin_GateDim1_G3;
    private NewTextBox newTextBox_Pin_GateDim2_G3;
    private NewTextBox newTextBox_Pin_RunnerLength_G3;
    private NewTextBox newTextBox_Pin_RunnerDim1_G3;
    private NewTextBox newTextBox_Pin_RunnerDim2_G3;
    private TabPage tabPage_G4;
    private Label label_Pin_Gate_G4;
    private Label label_Pin_Runner_G4;
    private Label label_Pin_GateDim1_G4;
    private Label label_Pin_RunnerLength_G4;
    private Label label_Pin_RunnerDim1_G4;
    private NewButton newButton_Pin_GateLength_G4;
    private Label label_Pin_GateDim2_G4;
    private Label label_Pin_RunnerDim2_G4;
    private NewTextBox newTextBox_Pin_GateLength_G4;
    private NewTextBox newTextBox_Pin_GateDim1_G4;
    private NewTextBox newTextBox_Pin_GateDim2_G4;
    private NewTextBox newTextBox_Pin_RunnerLength_G4;
    private NewTextBox newTextBox_Pin_RunnerDim1_G4;
    private NewTextBox newTextBox_Pin_RunnerDim2_G4;
    private Panel panel_Group;
    private NewButton newButton_G1;
    private NewButton newButton_G4;
    private NewButton newButton_G3;
    private NewButton newButton_G2;
    private Label label_Side_Gate_G1;
    private Label label_Side_Runner_G1;
    private Label label_Side_GateLength_G1;
    private Label label_Side_GateDim1_G1;
    private Label label_Side_RunnerLength_G1;
    private Label label_Side_GateDim3_G1;
    private NewTextBox newTextBox_Side_RunnerDim4_G1;
    private Label label_Side_RunnerDim2_G1;
    private Label label_Side_RunnerDim4_G1;
    private Label label_Side_GateDim2_G1;
    private Label label_Side_RunnerDim1_G1;
    private Label label_Side_GateDim4_G1;
    private Label label_Side_RunnerDim3_G1;
    private NewTextBox newTextBox_Side_GateLength_G1;
    private NewTextBox newTextBox_Side_GateDim1_G1;
    private NewTextBox newTextBox_Side_GateDim2_G1;
    private NewTextBox newTextBox_Side_GateDim3_G1;
    private NewTextBox newTextBox_Side_GateDim4_G1;
    private NewTextBox newTextBox_Side_RunnerLength_G1;
    private NewTextBox newTextBox_Side_RunnerDim1_G1;
    private NewTextBox newTextBox_Side_RunnerDim2_G1;
    private NewTextBox newTextBox_Side_RunnerDim3_G1;
    private DataGridView dataGridView_Node;
    private Panel panel_Side_GateType1_G1;
    private RadioButton radioButton_Side_GateCircle_G1;
    private RadioButton radioButton_Side_GateRect_G1;
    private Panel panel_Side_GateType2_G1;
    private RadioButton radioButton_Side_GateSubMarine_G1;
    private RadioButton radioButton_Side_GateNormal_G1;
    private Panel panel_Side_RunnerType_G1;
    private RadioButton radioButton_Side_RunnerRectangle_G1;
    private RadioButton radioButton_Side_RunnerCircle_G1;
    private RadioButton radioButton_Side_RunnerTrepezoidal_G1;
    private NewButton newButton_LoadPrevNode;
    private TabControl tabControl_Hor_RunnerType;
    private TabPage tabPage_Hor_TwoStage;
    private TabPage tabPage_Hor_ThreeStage;
    private Label label1;
    private NewTextBox newTextBox_VavDelayTime;
    private Label label_VavDelayTime;
    private NewButton newButton_Node_Pin_Select;
    private Label label_Pin_GateDim2_G2;
    private Label label_Pin_GateDim1_G2;
    private Label label_Pin_Gate_G2;
    private Label label_Pin_Runner_G2;
    private Label label_Side_Runner_G2;
    private NewTextBox newTextBox_Side_RunnerLength_G2;
    private Panel panel_Side_RunnerType_G2;
    private RadioButton radioButton_Side_RunnerRectangle_G2;
    private RadioButton radioButton_Side_RunnerCircle_G2;
    private RadioButton radioButton_Side_RunnerTrepezoidal_G2;
    private Panel panel_Side_GateType1_G2;
    private RadioButton radioButton_Side_GateCircle_G2;
    private RadioButton radioButton_Side_GateRect_G2;
    private Label label_Side_RunnerLength_G2;
    private NewTextBox newTextBox_Side_RunnerDim4_G2;
    private Panel panel_Side_GateType2_G2;
    private RadioButton radioButton_Side_GateSubMarine_G2;
    private RadioButton radioButton_Side_GateNormal_G2;
    private Label label_Side_RunnerDim2_G2;
    private Label label_Side_RunnerDim4_G2;
    private Label label_Side_RunnerDim1_G2;
    private Label label_Side_Gate_G2;
    private Label label_Side_RunnerDim3_G2;
    private Label label_Side_GateLength_G2;
    private NewTextBox newTextBox_Side_RunnerDim1_G2;
    private NewTextBox newTextBox_Side_RunnerDim2_G2;
    private Label label_Side_GateDim1_G2;
    private NewTextBox newTextBox_Side_RunnerDim3_G2;
    private Label label_Side_GateDim3_G2;
    private Label label_Side_GateDim2_G2;
    private Label label_Side_GateDim4_G2;
    private NewTextBox newTextBox_Side_GateDim4_G2;
    private NewTextBox newTextBox_Side_GateLength_G2;
    private NewTextBox newTextBox_Side_GateDim3_G2;
    private NewTextBox newTextBox_Side_GateDim1_G2;
    private NewTextBox newTextBox_Side_GateDim2_G2;
    private Label label_Side_Runner_G3;
    private Panel panel_Side_RunnerType_G3;
    private RadioButton radioButton_Side_RunnerRectangle_G3;
    private RadioButton radioButton_Side_RunnerCircle_G3;
    private RadioButton radioButton_Side_RunnerTrepezoidal_G3;
    private NewTextBox newTextBox_Side_RunnerLength_G3;
    private Panel panel_Side_GateType1_G3;
    private RadioButton radioButton_Side_GateCircle_G3;
    private RadioButton radioButton_Side_GateRect_G3;
    private Label label_Side_RunnerLength_G3;
    private NewTextBox newTextBox_Side_RunnerDim4_G3;
    private Panel panel_Side_GateType2_G3;
    private RadioButton radioButton_Side_GateSubMarine_G3;
    private RadioButton radioButton_Side_GateNormal_G3;
    private Label label_Side_RunnerDim2_G3;
    private Label label_Side_RunnerDim4_G3;
    private Label label_Side_RunnerDim1_G3;
    private Label label_Side_Gate_G3;
    private Label label_Side_RunnerDim3_G3;
    private Label label_Side_GateLength_G3;
    private NewTextBox newTextBox_Side_RunnerDim1_G3;
    private NewTextBox newTextBox_Side_RunnerDim2_G3;
    private Label label_Side_GateDim1_G3;
    private NewTextBox newTextBox_Side_RunnerDim3_G3;
    private Label label_Side_GateDim3_G3;
    private Label label_Side_GateDim2_G3;
    private Label label_Side_GateDim4_G3;
    private NewTextBox newTextBox_Side_GateDim4_G3;
    private NewTextBox newTextBox_Side_GateLength_G3;
    private NewTextBox newTextBox_Side_GateDim3_G3;
    private NewTextBox newTextBox_Side_GateDim1_G3;
    private NewTextBox newTextBox_Side_GateDim2_G3;
    private Label label_Side_Runner_G4;
    private Panel panel_Side_RunnerType_G4;
    private RadioButton radioButton_Side_RunnerRectangle_G4;
    private RadioButton radioButton_Side_RunnerCircle_G4;
    private RadioButton radioButton_Side_RunnerTrepezoidal_G4;
    private NewTextBox newTextBox_Side_RunnerLength_G4;
    private Panel panel_Side_GateType1_G4;
    private RadioButton radioButton_Side_GateCircle_G4;
    private RadioButton radioButton_Side_GateRect_G4;
    private Label label_Side_RunnerLength_G4;
    private NewTextBox newTextBox_Side_RunnerDim4_G4;
    private Panel panel_Side_GateType2_G4;
    private RadioButton radioButton_Side_GateSubMarine_G4;
    private RadioButton radioButton_Side_GateNormal_G4;
    private Label label_Side_RunnerDim2_G4;
    private Label label_Side_RunnerDim4_G4;
    private Label label_Side_RunnerDim1_G4;
    private Label label_Side_Gate_G4;
    private Label label_Side_RunnerDim3_G4;
    private Label label_Side_GateLength_G4;
    private NewTextBox newTextBox_Side_RunnerDim1_G4;
    private NewTextBox newTextBox_Side_RunnerDim2_G4;
    private Label label_Side_GateDim1_G4;
    private NewTextBox newTextBox_Side_RunnerDim3_G4;
    private Label label_Side_GateDim3_G4;
    private Label label_Side_GateDim2_G4;
    private Label label_Side_GateDim4_G4;
    private NewTextBox newTextBox_Side_GateDim4_G4;
    private NewTextBox newTextBox_Side_GateLength_G4;
    private NewTextBox newTextBox_Side_GateDim3_G4;
    private NewTextBox newTextBox_Side_GateDim1_G4;
    private NewTextBox newTextBox_Side_GateDim2_G4;
    private Panel panel_Hor_RunnerType;
    private NewButton newButton_Hor_Runner_ThreeStage;
    private NewButton newButton_Hor_Runner_TwoStage;
    private Panel panel_Hor_Runner_ThreeType;
    private RadioButton radioButton_Hor_Three_Type2;
    private RadioButton radioButton_Hor_Three_Type1;
    private Label label_Hor_Three_Dia;
    private NewTextBox newTextBox_Hor_Three_Dia;
    private NewTextBox newTextBox_Hor_Two_CenterTol;
    private Label label_Hor_Two_CenterTol;
    private DataGridViewComboBoxColumn Column_Node_Type;
    private DataGridViewComboBoxColumn Column_Group;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn1;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn2;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn3;
    private DataGridViewComboBoxColumn Column_Direction;
    private DataGridViewTextBoxColumn Column_VavCheck;
    private DataGridViewTextBoxColumn Column_VavTrigger;
    private DataGridViewTextBoxColumn Column_VavState;
    private DataGridViewTextBoxColumn Column_VavLoc;
    private DataGridViewTextBoxColumn Column_VavOpen1;
    private DataGridViewTextBoxColumn Column_VavClose1;
    private DataGridViewTextBoxColumn Column_VavDelay;
    private NewTextBox newTextBox_Hor_Three_CenterTol;
    private Label label_Hor_Three_CenterTol;
    private Panel panel_Quadrant;
    private RadioButton radioButton_Quadrant4;
    private RadioButton radioButton_Quadrant2;
    private NewButton newButton_Node_Side_Select;
    private NewTextBox newTextBox_Hor_Three_Length;
    private Label label_Hor_Three_Length;
    private NewButton newButton_Node_Update;
    private Panel panel1;
    private RadioButton radioButton_Node4;
    private RadioButton radioButton_Node3;
    private RadioButton radioButton_Node2;
    private RadioButton radioButton_Node1;

    protected override CreateParams CreateParams
    {
      get
      {
        CreateParams createParams = base.CreateParams;
        createParams.ExStyle |= 33554432;
        return createParams;
      }
    }

    public frmRunner()
    {
      this.InitializeComponent();
      this.newButton_Edit.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Edit.Image);
      this.newButton_Node_Add.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Node_Add.Image);
      this.newButton_Node_Del.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Node_Del.Image);
      this.newButton_Apply.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Apply.Image);
    }

    private void frmRunner_Load(object sender, EventArgs e)
    {
      this.Text = LocaleControl.getInstance().GetString("IDS_RUNNER") + " [" + LocaleControl.getInstance().GetString("IDS_STUDY") + ": " + this.m_drStudy["Name"].ToString() + "]";
      this.label_DB_List.Text = "DB " + LocaleControl.getInstance().GetString("IDS_LIST");
      this.label_DB_Company.Text = LocaleControl.getInstance().GetString("IDS_COMPANY_NAME");
      this.label_DB_Item.Text = LocaleControl.getInstance().GetString("IDS_ITEM_NAME");
      this.newButton_Edit.ButtonText = LocaleControl.getInstance().GetString("IDS_EDIT");
      this.label_CenterPoint.Text = "[" + LocaleControl.getInstance().GetString("IDS_CENTER_POINT") + "]";
      this.newButton_LoadPrevNode.ButtonText = LocaleControl.getInstance().GetString("IDS_LOAD_PREVNODEDATA");
      this.newButton_G1.ButtonText = LocaleControl.getInstance().GetString("IDS_GROUP") + "1";
      this.newButton_G2.ButtonText = LocaleControl.getInstance().GetString("IDS_GROUP") + "2";
      this.newButton_G3.ButtonText = LocaleControl.getInstance().GetString("IDS_GROUP") + "3";
      this.newButton_G4.ButtonText = LocaleControl.getInstance().GetString("IDS_GROUP") + "4";
      this.newButton_Node_Pin_Select.ButtonText = LocaleControl.getInstance().GetString("IDS_SELECT_PIN_NODE");
      this.newButton_Node_Side_Select.ButtonText = LocaleControl.getInstance().GetString("IDS_SELECT_SIDE_NODE");
      this.dataGridView_Node.Columns[0].HeaderText = LocaleControl.getInstance().GetString("IDS_TYPE");
      this.dataGridView_Node.Columns[1].HeaderText = LocaleControl.getInstance().GetString("IDS_GROUP");
      this.dataGridView_Node.Columns[5].HeaderText = LocaleControl.getInstance().GetString("IDS_DIRECTION");
      this.radioButton_Pin_ColdSystem.Text = LocaleControl.getInstance().GetString("IDS_COLD_SYSTEM");
      this.radioButton_Pin_HotSystem.Text = LocaleControl.getInstance().GetString("IDS_HOT_SYSTEM");
      this.label_Pin.Text = "[" + LocaleControl.getInstance().GetString("IDS_PIN") + "]";
      this.label_Pin_Gate_G1.Text = "<" + LocaleControl.getInstance().GetString("IDS_PIN") + " " + LocaleControl.getInstance().GetString("IDS_GATE") + ">";
      this.label_Pin_Gate_G2.Text = "<" + LocaleControl.getInstance().GetString("IDS_PIN") + " " + LocaleControl.getInstance().GetString("IDS_GATE") + ">";
      this.label_Pin_Gate_G3.Text = "<" + LocaleControl.getInstance().GetString("IDS_PIN") + " " + LocaleControl.getInstance().GetString("IDS_GATE") + ">";
      this.label_Pin_Gate_G4.Text = "<" + LocaleControl.getInstance().GetString("IDS_PIN") + " " + LocaleControl.getInstance().GetString("IDS_GATE") + ">";
      this.label_Pin_Runner_G1.Text = "<" + LocaleControl.getInstance().GetString("IDS_PIN") + " " + LocaleControl.getInstance().GetString("IDS_RUNNER") + ">";
      this.label_Pin_Runner_G2.Text = "<" + LocaleControl.getInstance().GetString("IDS_PIN") + " " + LocaleControl.getInstance().GetString("IDS_RUNNER") + ">";
      this.label_Pin_Runner_G3.Text = "<" + LocaleControl.getInstance().GetString("IDS_PIN") + " " + LocaleControl.getInstance().GetString("IDS_RUNNER") + ">";
      this.label_Pin_Runner_G4.Text = "<" + LocaleControl.getInstance().GetString("IDS_PIN") + " " + LocaleControl.getInstance().GetString("IDS_RUNNER") + ">";
      this.newButton_Pin_GateLength_G1.ButtonText = LocaleControl.getInstance().GetString("IDS_GATE_LENGTH");
      this.newButton_Pin_GateLength_G2.ButtonText = LocaleControl.getInstance().GetString("IDS_GATE_LENGTH");
      this.newButton_Pin_GateLength_G3.ButtonText = LocaleControl.getInstance().GetString("IDS_GATE_LENGTH");
      this.newButton_Pin_GateLength_G4.ButtonText = LocaleControl.getInstance().GetString("IDS_GATE_LENGTH");
      this.label_Pin_RunnerLength_G1.Text = LocaleControl.getInstance().GetString("IDS_RUNNER_LENGTH");
      this.label_Pin_RunnerLength_G2.Text = LocaleControl.getInstance().GetString("IDS_RUNNER_LENGTH");
      this.label_Pin_RunnerLength_G3.Text = LocaleControl.getInstance().GetString("IDS_RUNNER_LENGTH");
      this.label_Pin_RunnerLength_G4.Text = LocaleControl.getInstance().GetString("IDS_RUNNER_LENGTH");
      this.radioButton_Side_GateNormal_G1.Text = LocaleControl.getInstance().GetString("IDS_NORMAL");
      this.radioButton_Side_GateNormal_G2.Text = LocaleControl.getInstance().GetString("IDS_NORMAL");
      this.radioButton_Side_GateNormal_G3.Text = LocaleControl.getInstance().GetString("IDS_NORMAL");
      this.radioButton_Side_GateNormal_G4.Text = LocaleControl.getInstance().GetString("IDS_NORMAL");
      this.radioButton_Side_GateSubMarine_G1.Text = LocaleControl.getInstance().GetString("IDS_SUBMARINE");
      this.radioButton_Side_GateSubMarine_G2.Text = LocaleControl.getInstance().GetString("IDS_SUBMARINE");
      this.radioButton_Side_GateSubMarine_G3.Text = LocaleControl.getInstance().GetString("IDS_SUBMARINE");
      this.radioButton_Side_GateSubMarine_G4.Text = LocaleControl.getInstance().GetString("IDS_SUBMARINE");
      this.label_Side_Gate_G1.Text = "<" + LocaleControl.getInstance().GetString("IDS_SIDE") + " " + LocaleControl.getInstance().GetString("IDS_GATE") + ">";
      this.label_Side_Gate_G2.Text = "<" + LocaleControl.getInstance().GetString("IDS_SIDE") + " " + LocaleControl.getInstance().GetString("IDS_GATE") + ">";
      this.label_Side_Gate_G3.Text = "<" + LocaleControl.getInstance().GetString("IDS_SIDE") + " " + LocaleControl.getInstance().GetString("IDS_GATE") + ">";
      this.label_Side_Gate_G4.Text = "<" + LocaleControl.getInstance().GetString("IDS_SIDE") + " " + LocaleControl.getInstance().GetString("IDS_GATE") + ">";
      this.label_Side_Runner_G1.Text = "<" + LocaleControl.getInstance().GetString("IDS_SIDE") + " " + LocaleControl.getInstance().GetString("IDS_RUNNER") + ">";
      this.label_Side_Runner_G2.Text = "<" + LocaleControl.getInstance().GetString("IDS_SIDE") + " " + LocaleControl.getInstance().GetString("IDS_RUNNER") + ">";
      this.label_Side_Runner_G3.Text = "<" + LocaleControl.getInstance().GetString("IDS_SIDE") + " " + LocaleControl.getInstance().GetString("IDS_RUNNER") + ">";
      this.label_Side_Runner_G4.Text = "<" + LocaleControl.getInstance().GetString("IDS_SIDE") + " " + LocaleControl.getInstance().GetString("IDS_RUNNER") + ">";
      this.label_Side_GateLength_G1.Text = LocaleControl.getInstance().GetString("IDS_GATE_LENGTH");
      this.label_Side_GateLength_G2.Text = LocaleControl.getInstance().GetString("IDS_GATE_LENGTH");
      this.label_Side_GateLength_G3.Text = LocaleControl.getInstance().GetString("IDS_GATE_LENGTH");
      this.label_Side_GateLength_G4.Text = LocaleControl.getInstance().GetString("IDS_GATE_LENGTH");
      this.label_Side_RunnerLength_G1.Text = LocaleControl.getInstance().GetString("IDS_RUNNER_LENGTH");
      this.label_Side_RunnerLength_G2.Text = LocaleControl.getInstance().GetString("IDS_RUNNER_LENGTH");
      this.label_Side_RunnerLength_G3.Text = LocaleControl.getInstance().GetString("IDS_RUNNER_LENGTH");
      this.label_Side_RunnerLength_G4.Text = LocaleControl.getInstance().GetString("IDS_RUNNER_LENGTH");
      this.newButton_Hor_Runner_TwoStage.ButtonText = "2" + LocaleControl.getInstance().GetString("IDS_STAGE_MANIFOLD");
      this.newButton_Hor_Runner_ThreeStage.ButtonText = "3" + LocaleControl.getInstance().GetString("IDS_STAGE_MANIFOLD");
      this.label_Hor_Runner.Text = LocaleControl.getInstance().GetString("IDS_HORIZON_RUNNER");
      this.radioButton_Hor_Two_Type1.Text = "I " + LocaleControl.getInstance().GetString("IDS_TYPE");
      this.radioButton_Hor_Two_Type2.Text = "H " + LocaleControl.getInstance().GetString("IDS_TYPE");
      this.radioButton_Hor_Two_Type3.Text = "☆ - + " + LocaleControl.getInstance().GetString("IDS_TYPE");
      this.radioButton_Hor_Two_Type4.Text = "☆ - x " + LocaleControl.getInstance().GetString("IDS_TYPE");
      this.radioButton_Quadrant2.Text = "2" + LocaleControl.getInstance().GetString("IDS_QUADRANT");
      this.radioButton_Quadrant4.Text = "4" + LocaleControl.getInstance().GetString("IDS_QUADRANT");
      this.radioButton_Hor_Three_Type1.Text = "H " + LocaleControl.getInstance().GetString("IDS_TYPE");
      this.radioButton_Hor_Three_Type2.Text = "☆ - + " + LocaleControl.getInstance().GetString("IDS_TYPE");
      this.label_Hor_Two_Dia.Text = LocaleControl.getInstance().GetString("IDS_HORIZON_RUNNER_DIA");
      this.label_Hor_Two_Length.Text = LocaleControl.getInstance().GetString("IDS_HORIZON_RUNNER_LENGTH");
      this.label_Hor_Two_Direction.Text = LocaleControl.getInstance().GetString("IDS_HORIZON_RUNNER_DIRECTION");
      this.label_Hor_Two_Dim1.Text = "[ X ]" + LocaleControl.getInstance().GetString("IDS_HORIZON_LENGTH");
      this.label_Hor_Two_Dim2.Text = "[ Y ]" + LocaleControl.getInstance().GetString("IDS_HORIZON_LENGTH");
      this.label_Hor_Two_Angle.Text = "∠ " + LocaleControl.getInstance().GetString("IDS_HORIZON_ANGLE");
      this.label_Hor_Two_CenterTol.Text = "Center " + LocaleControl.getInstance().GetString("IDS_TOLERANCE");
      this.label_Hor_Three_CenterTol.Text = "Center " + LocaleControl.getInstance().GetString("IDS_TOLERANCE");
      this.label_Hor_Three_Dia.Text = LocaleControl.getInstance().GetString("IDS_HORIZON_RUNNER_DIA");
      this.label_Hor_Three_Length.Text = LocaleControl.getInstance().GetString("IDS_THREE_RUNNERLENGTH");
      this.radioButton_Sprue_Cold.Text = LocaleControl.getInstance().GetString("IDS_COLD_SPRUE");
      this.radioButton_Sprue_Hot.Text = LocaleControl.getInstance().GetString("IDS_HOT_SPRUE");
      this.label_Sprue_Length.Text = LocaleControl.getInstance().GetString("IDS_SPRUE_LENGTH");
      this.label_Sprue.Text = "[" + LocaleControl.getInstance().GetString("IDS_SPRUE") + "]";
      this.label_Cavity.Text = "[" + LocaleControl.getInstance().GetString("IDS_CAVITY") + "]";
      this.newButton_Apply.ButtonText = LocaleControl.getInstance().GetString("IDS_CREATE_RUNNER_SYSTEM");
      string str1 = this.m_drStudy.Table.TableName + "_" + this.m_drStudy["Mesh"].ToString();
      this.m_fiNodeData = new FileInfo(clsDefine.g_diProject.FullName + "\\" + str1 + ".ini");
      this.radioButton_Node1.Checked = true;
      DataRow dataRow = (DataRow) null;
      this.Text = LocaleControl.getInstance().GetString("IDS_RUNNER") + " [" + LocaleControl.getInstance().GetString("IDS_STUDY") + ": " + this.m_drStudy["Name"].ToString() + "]";
      string modelXyLength = clsHDMFLib.GetModelXYLength();
      if (!string.IsNullOrEmpty(modelXyLength))
      {
        string[] strArray = modelXyLength.Split(',');
        double num1 = clsUtill.ConvertToDouble(strArray[0]);
        double num2 = clsUtill.ConvertToDouble(strArray[1]);
        double num3 = Math.Round(num2 / num1, 2);
        this.tabControl_Hor_RunnerType.SelectedIndex = num1 <= 1100.0 || num2 <= 1100.0 ? 0 : 1;
        if (num3 > 0.35)
          this.radioButton_Quadrant4.Checked = true;
        else
          this.radioButton_Quadrant2.Checked = true;
        this.radioButton_Hor_Three_Type1.Checked = true;
      }
      this.RefreshDBList();
      this.RefreshCompanyUI();
      this.RefreshItemUI();
      double[] pointFromStudyNote = clsHDMFLib.GetCenterPointFromStudyNote();
      if (pointFromStudyNote != null)
      {
        this.newTextBox_CenterX.Value = pointFromStudyNote[0].ToString();
        this.newTextBox_CenterY.Value = pointFromStudyNote[1].ToString();
      }
      this.newComboBox_VavTrigger.Items.AddRange((object[]) new string[2]
      {
        "Time",
        "Flow front"
      });
      this.newComboBox_VavTrigger.SelectedIndex = 0;
      this.newComboBox_VavState.Items.AddRange((object[]) new string[2]
      {
        "Open",
        "Close"
      });
      this.newComboBox_VavState.SelectedIndex = 0;
      string str2 = "15";
      if (clsDefine.g_dicValve.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VavClose")))
        str2 = clsDefine.g_dicValve["VavClose"];
      this.newTextBox_VavClose1.Value = str2;
      string str3 = "0";
      if (clsDefine.g_dicValve.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VavDelayTime")))
        str3 = clsDefine.g_dicValve["VavDelayTime"];
      this.newTextBox_VavDelayTime.Value = str3;
      this.newTextBox_Hor_Two_Angle.SetToolTip(this.toolTip_Hor_Angle, LocaleControl.getInstance().GetString("IDS_HORIZON_ANGLERANGE"));
      this.m_dicGroup = new Dictionary<string, string>();
      this.m_dicGroup.Add("G1", LocaleControl.getInstance().GetString("IDS_GROUP") + (object) 1);
      this.m_dicGroup.Add("G2", LocaleControl.getInstance().GetString("IDS_GROUP") + (object) 2);
      this.m_dicGroup.Add("G3", LocaleControl.getInstance().GetString("IDS_GROUP") + (object) 3);
      this.m_dicGroup.Add("G4", LocaleControl.getInstance().GetString("IDS_GROUP") + (object) 4);
      this.m_dicGate = new Dictionary<string, string>();
      for (int index = 0; index < this.tabControl_Group.TabCount; ++index)
      {
        foreach (KeyValuePair<string, string> keyValuePair in clsDefine.g_dicGate.Where<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key.Contains("Pin"))))
          this.m_dicGate.Add(keyValuePair.Key + "_G" + (object) (index + 1), keyValuePair.Value);
        double num = clsUtill.ConvertToDouble(this.m_dicGate["PinLength1_G" + (object) (index + 1)]) + clsUtill.ConvertToDouble(this.m_dicGate["PinLength2_G" + (object) (index + 1)]) + clsUtill.ConvertToDouble(this.m_dicGate["PinLength3_G" + (object) (index + 1)]) + clsUtill.ConvertToDouble(this.m_dicGate["PinLength4_G" + (object) (index + 1)]);
        ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Pin_GateLength_G" + (object) (index + 1), true)[0]).Value = num.ToString();
      }
      foreach (KeyValuePair<string, string> keyValuePair in clsDefine.g_dicGate.Where<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key.Contains("Side"))))
        this.m_dicGate.Add(keyValuePair.Key, keyValuePair.Value);
      this.newComboBox_Hor_Two_Direction.Items.AddRange((object[]) new string[4]
      {
        "+X",
        "-X",
        "+Y",
        "-Y"
      });
      this.newComboBox_Hor_Two_Direction.SelectedIndex = 0;
      if (this.listBox_DB.Items.Count <= 0)
        return;
      string strName = this.m_drStudy["Name"].ToString().Split('_')[0];
      string strItem = this.m_drStudy["Name"].ToString().Split('_')[1];
      DataRow[] array = clsDefine.g_dtRunnerDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp =>
      {
        if (!(Temp["Name"].ToString().Split('_')[0] == strName))
        {
          if (!(Temp["Name"].ToString().Split('_')[0].ToLower() == "basic"))
            return false;
        }
        return Temp["Name"].ToString().Split('_')[1] == strItem;
      })).ToArray<DataRow>();
      if (array.Length != 0)
        dataRow = ((IEnumerable<DataRow>) array).Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString().Contains("_Default"))).FirstOrDefault<DataRow>() ?? array[0];
      if (dataRow != null)
        this.listBox_DB.SelectedIndex = this.listBox_DB.Items.IndexOf((object) dataRow["Name"].ToString());
    }

    public void RefreshDBList(string p_strCompany = null, string p_strItem = null)
    {
      this.listBox_DB.Items.Clear();
      if (p_strCompany == null)
        p_strCompany = LocaleControl.getInstance().GetString("IDS_ALL");
      if (p_strItem == null)
        p_strItem = LocaleControl.getInstance().GetString("IDS_ALL");
      try
      {
        DataRow[] array = clsDefine.g_dtRunnerDB.AsEnumerable().ToArray<DataRow>();
        if (p_strCompany != LocaleControl.getInstance().GetString("IDS_ALL"))
          array = ((IEnumerable<DataRow>) array).Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Company"].ToString() == p_strCompany)).ToArray<DataRow>();
        if (array.Length == 0)
          return;
        if (p_strItem != LocaleControl.getInstance().GetString("IDS_ALL"))
          array = ((IEnumerable<DataRow>) array).Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Item"].ToString() == p_strItem)).ToArray<DataRow>();
        if (array.Length != 0)
        {
          List<string> stringList = new List<string>();
          foreach (DataRow dataRow in array)
            stringList.Add(dataRow["Name"].ToString());
          stringList.Sort();
          this.listBox_DB.Items.AddRange((object[]) stringList.ToArray());
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunnerDB]RefreshDBList):" + ex.Message));
      }
    }

    public void RefreshCompanyUI(string p_strCompany = null)
    {
      this.newComboBox_Company.Items.Clear();
      if (p_strCompany == null)
        p_strCompany = LocaleControl.getInstance().GetString("IDS_ALL");
      try
      {
        this.newComboBox_Company.Items.Add((object) LocaleControl.getInstance().GetString("IDS_ALL"));
        DataRow[] array = clsDefine.g_dtRunnerDB.AsEnumerable().ToArray<DataRow>();
        List<string> stringList = new List<string>();
        foreach (DataRow dataRow in array)
        {
          if (!stringList.Contains(dataRow["Company"].ToString()))
            stringList.Add(dataRow["Company"].ToString());
        }
        stringList.Sort();
        this.newComboBox_Company.Items.AddRange((object[]) stringList.ToArray());
        this.newComboBox_Company.SelectedIndex = this.newComboBox_Company.Items.IndexOf((object) p_strCompany);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]RefreshDB):" + ex.Message));
      }
    }

    public void RefreshItemUI(string p_strCompany = null)
    {
      this.newComboBox_Item.Items.Clear();
      if (p_strCompany == null)
        p_strCompany = LocaleControl.getInstance().GetString("IDS_ALL");
      try
      {
        this.newComboBox_Item.Items.Add((object) LocaleControl.getInstance().GetString("IDS_ALL"));
        DataRow[] array = clsDefine.g_dtRunnerDB.AsEnumerable().ToArray<DataRow>();
        if (p_strCompany != LocaleControl.getInstance().GetString("IDS_ALL"))
          array = ((IEnumerable<DataRow>) array).Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Company"].ToString() == p_strCompany)).ToArray<DataRow>();
        if (array.Length != 0)
        {
          List<string> stringList = new List<string>();
          foreach (DataRow dataRow in array)
          {
            if (!stringList.Contains(dataRow["Item"].ToString()))
              stringList.Add(dataRow["Item"].ToString());
          }
          stringList.Sort();
          this.newComboBox_Item.Items.AddRange((object[]) stringList.ToArray());
          this.newComboBox_Item.SelectedIndex = 0;
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunnerDB]RefreshItemUI):" + ex.Message));
      }
      if (this.newComboBox_Item.Items.Count == 1)
      {
        this.newComboBox_Item.Items.Clear();
        this.newComboBox_Item.Enabled = false;
      }
      else
        this.newComboBox_Item.Enabled = true;
    }

    private void listBox_DB_SelectedIndexChanged(object sender, EventArgs e)
    {
      try
      {
        DataRow dataRow = clsDefine.g_dtRunnerDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == this.listBox_DB.Text)).FirstOrDefault<DataRow>();
        if (dataRow == null)
          return;
        if (dataRow["Pin_System"].ToString() == "0")
          this.radioButton_Pin_ColdSystem.Checked = true;
        else
          this.radioButton_Pin_HotSystem.Checked = true;
        for (int index = 0; index < this.tabControl_Group.TabCount; ++index)
        {
          ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Pin_GateDim1_G" + (object) (index + 1), true)[0]).Value = dataRow["Pin_GateDim1_G" + (object) (index + 1)].ToString();
          ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Pin_GateDim2_G" + (object) (index + 1), true)[0]).Value = dataRow["Pin_GateDim2_G" + (object) (index + 1)].ToString();
          ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Pin_RunnerLength_G" + (object) (index + 1), true)[0]).Value = dataRow["Pin_RunnerLength_G" + (object) (index + 1)].ToString();
          ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Pin_RunnerDim1_G" + (object) (index + 1), true)[0]).Value = dataRow["Pin_RunnerDim1_G" + (object) (index + 1)].ToString();
          ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Pin_RunnerDim2_G" + (object) (index + 1), true)[0]).Value = dataRow["Pin_RunnerDim2_G" + (object) (index + 1)].ToString();
          if (dataRow["Side_GateType1_G" + (object) (index + 1)].ToString() == "0")
            ((RadioButton) this.tabControl_Group.TabPages[index].Controls.Find("radioButton_Side_GateRect_G" + (object) (index + 1), true)[0]).Checked = true;
          else
            ((RadioButton) this.tabControl_Group.TabPages[index].Controls.Find("radioButton_Side_GateCircle_G" + (object) (index + 1), true)[0]).Checked = true;
          if (dataRow["Side_GateType2_G" + (object) (index + 1)].ToString() == "0")
            ((RadioButton) this.tabControl_Group.TabPages[index].Controls.Find("radioButton_Side_GateNormal_G" + (object) (index + 1), true)[0]).Checked = true;
          else
            ((RadioButton) this.tabControl_Group.TabPages[index].Controls.Find("radioButton_Side_GateSubMarine_G" + (object) (index + 1), true)[0]).Checked = true;
          ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Side_GateLength_G" + (object) (index + 1), true)[0]).Value = dataRow["Side_GateLength_G" + (object) (index + 1)].ToString();
          ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Side_GateDim1_G" + (object) (index + 1), true)[0]).Value = dataRow["Side_GateDim1_G" + (object) (index + 1)].ToString();
          ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Side_GateDim2_G" + (object) (index + 1), true)[0]).Value = dataRow["Side_GateDim2_G" + (object) (index + 1)].ToString();
          ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Side_GateDim3_G" + (object) (index + 1), true)[0]).Value = dataRow["Side_GateDim3_G" + (object) (index + 1)].ToString();
          ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Side_GateDim4_G" + (object) (index + 1), true)[0]).Value = dataRow["Side_GateDim4_G" + (object) (index + 1)].ToString();
          switch (dataRow["Side_RunnerType_G" + (object) (index + 1)].ToString())
          {
            case "0":
              ((RadioButton) this.tabControl_Group.TabPages[index].Controls.Find("radioButton_Side_RunnerTrepezoidal_G" + (object) (index + 1), true)[0]).Checked = true;
              break;
            case "1":
              ((RadioButton) this.tabControl_Group.TabPages[index].Controls.Find("radioButton_Side_RunnerCircle_G" + (object) (index + 1), true)[0]).Checked = true;
              break;
            default:
              ((RadioButton) this.tabControl_Group.TabPages[index].Controls.Find("radioButton_Side_RunnerRectangle_G" + (object) (index + 1), true)[0]).Checked = true;
              break;
          }
          ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Side_RunnerLength_G" + (object) (index + 1), true)[0]).Value = dataRow["Side_RunnerLength_G" + (object) (index + 1)].ToString();
          ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Side_RunnerDim1_G" + (object) (index + 1), true)[0]).Value = dataRow["Side_RunnerDim1_G" + (object) (index + 1)].ToString();
          ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Side_RunnerDim2_G" + (object) (index + 1), true)[0]).Value = dataRow["Side_RunnerDim2_G" + (object) (index + 1)].ToString();
          ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Side_RunnerDim3_G" + (object) (index + 1), true)[0]).Value = dataRow["Side_RunnerDim3_G" + (object) (index + 1)].ToString();
          ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Side_RunnerDim4_G" + (object) (index + 1), true)[0]).Value = dataRow["Side_RunnerDim4_G" + (object) (index + 1)].ToString();
        }
        this.newTextBox_Cavity_Occ.Value = dataRow["OccNumber"].ToString();
        this.tabControl_Hor_RunnerType.SelectedIndex = clsUtill.ConvertToInt(dataRow["Hor_Stage"].ToString());
        switch (dataRow["Hor_Two_Type"].ToString())
        {
          case "0":
            this.radioButton_Hor_Two_Type1.Checked = true;
            break;
          case "1":
            this.radioButton_Hor_Two_Type2.Checked = true;
            break;
          case "2":
            this.radioButton_Hor_Two_Type3.Checked = true;
            break;
          default:
            this.radioButton_Hor_Two_Type4.Checked = true;
            break;
        }
        this.newTextBox_Hor_Two_Dia.Value = dataRow["Hor_Two_Dia"].ToString();
        this.newTextBox_Hor_Two_Length.Value = dataRow["Hor_Two_Length"].ToString();
        switch (dataRow["Hor_Two_Dir"].ToString())
        {
          case "0":
            this.newComboBox_Hor_Two_Direction.SelectedIndex = 0;
            break;
          case "1":
            this.newComboBox_Hor_Two_Direction.SelectedIndex = 1;
            break;
          case "2":
            this.newComboBox_Hor_Two_Direction.SelectedIndex = 2;
            break;
          case "3":
            this.newComboBox_Hor_Two_Direction.SelectedIndex = 3;
            break;
          default:
            this.newComboBox_Hor_Two_Direction.SelectedIndex = 4;
            break;
        }
        this.newTextBox_Hor_Two_CenterTol.Value = dataRow["Hor_Two_CenterTol"].ToString();
        this.newTextBox_Hor_Two_Dim1.Value = dataRow["Hor_Two_Dim1"].ToString();
        this.newTextBox_Hor_Two_Dim2.Value = dataRow["Hor_Two_Dim2"].ToString();
        this.newTextBox_Hor_Two_Angle.Value = dataRow["Hor_Two_Angle"].ToString();
        this.checkBox_Hor_Two_Inter.Checked = !dataRow["Hor_Two_Inter"].ToString().ToLower().Equals("false");
        switch (dataRow["Hor_Three_Quadrant"].ToString())
        {
          case "0":
            this.radioButton_Quadrant2.Checked = true;
            break;
          case "1":
            this.radioButton_Quadrant4.Checked = true;
            break;
        }
        switch (dataRow["Hor_Three_Type"].ToString())
        {
          case "0":
            this.radioButton_Hor_Three_Type1.Checked = true;
            break;
          case "1":
            this.radioButton_Hor_Three_Type2.Checked = true;
            break;
        }
        this.newTextBox_Hor_Three_Length.Value = dataRow["Hor_Three_Length"].ToString();
        this.newTextBox_Hor_Three_Dia.Value = dataRow["Hor_Three_Dia"].ToString();
        this.newTextBox_Hor_Three_CenterTol.Value = dataRow["Hor_Three_CenterTol"].ToString();
        if (dataRow["Sprue_Type"].ToString() == "0")
          this.radioButton_Sprue_Cold.Checked = true;
        else
          this.radioButton_Sprue_Hot.Checked = true;
        this.newTextBox_Sprue_Length.Value = dataRow["Sprue_Length"].ToString();
        this.newTextBox_Sprue_Dim1.Value = dataRow["Sprue_Dim1"].ToString();
        this.newTextBox_Sprue_Dim2.Value = dataRow["Sprue_Dim2"].ToString();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]listBox_DB_SelectedIndexChanged):" + ex.Message));
      }
    }

    private void newComboBox_Company_SelectedIndexChanged(object sender, EventArgs e)
    {
      this.RefreshDBList(this.newComboBox_Company.Value);
      this.RefreshItemUI(this.newComboBox_Company.Value);
    }

    private void newComboBox_Item_SelectedIndexChanged(object sender, EventArgs e) => this.RefreshDBList(this.newComboBox_Company.Value, this.newComboBox_Item.Value);

    private void radioButton_Pin_ColdSystem_CheckedChanged(object sender, EventArgs e)
    {
      this.label_Pin_RunnerDim1_G1.Text = "Start Dimension";
      this.label_Pin_RunnerDim2_G1.Text = "End Dimension";
      this.label_Pin_RunnerDim1_G2.Text = "Start Dimension";
      this.label_Pin_RunnerDim2_G2.Text = "End Dimension";
      this.label_Pin_RunnerDim1_G3.Text = "Start Dimension";
      this.label_Pin_RunnerDim2_G3.Text = "End Dimension";
      this.label_Pin_RunnerDim1_G4.Text = "Start Dimension";
      this.label_Pin_RunnerDim2_G4.Text = "End Dimension";
      this.panel_Valve.Enabled = false;
    }

    private void radioButton_Pin_HotSystem_CheckedChanged(object sender, EventArgs e)
    {
      this.label_Pin_RunnerDim1_G1.Text = "Inner Dimension";
      this.label_Pin_RunnerDim2_G1.Text = "Outer Dimension";
      this.label_Pin_RunnerDim1_G2.Text = "Inner Dimension";
      this.label_Pin_RunnerDim2_G2.Text = "Outer Dimension";
      this.label_Pin_RunnerDim1_G3.Text = "Inner Dimension";
      this.label_Pin_RunnerDim2_G3.Text = "Outer Dimension";
      this.label_Pin_RunnerDim1_G4.Text = "Inner Dimension";
      this.label_Pin_RunnerDim2_G4.Text = "Outer Dimension";
      this.checkBox_ValveCheck.Checked = true;
      foreach (DataGridViewRow row in (IEnumerable) this.dataGridView_Node.Rows)
        row.Cells["Column_VavCheck"].Value = (object) true;
      if (this.GetCurrentValveRow() == null)
        return;
      this.panel_Valve.Enabled = true;
    }

    private void radioButton_Side_GateRect_CheckedChanged(object sender, EventArgs e)
    {
      int num = clsUtill.ConvertToInt(Regex.Match((sender as RadioButton).Name, "_G(\\d)").Groups[1].Value);
      try
      {
        this.tabControl_Group.TabPages[num - 1].Controls.Find("label_Side_GateDim1_G" + (object) num, true)[0].Text = "Start Width";
        this.tabControl_Group.TabPages[num - 1].Controls.Find("label_Side_GateDim2_G" + (object) num, true)[0].Text = "Start Height";
        Label label1 = (Label) this.tabControl_Group.TabPages[num - 1].Controls.Find("label_Side_GateDim3_G" + (object) num, true)[0];
        label1.Text = "End Width";
        label1.BackColor = Color.Lavender;
        Label label2 = (Label) this.tabControl_Group.TabPages[num - 1].Controls.Find("label_Side_GateDim4_G" + (object) num, true)[0];
        label2.Text = "End Height";
        label2.BackColor = Color.Lavender;
        NewTextBox newTextBox1 = (NewTextBox) this.tabControl_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_GateDim3_G" + (object) num, true)[0];
        newTextBox1.TextBoxBackColor = Color.White;
        newTextBox1.TextForeColor = Color.Black;
        newTextBox1.ReadOnly = false;
        if (newTextBox1.Value == "")
          newTextBox1.Value = "0";
        NewTextBox newTextBox2 = (NewTextBox) this.tabControl_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_GateDim4_G" + (object) num, true)[0];
        newTextBox2.TextBoxBackColor = Color.White;
        newTextBox2.TextForeColor = Color.Black;
        newTextBox2.ReadOnly = false;
        if (newTextBox2.Value == "")
          newTextBox2.Value = "0";
        Panel panel = (Panel) this.tabControl_Group.TabPages[num - 1].Controls.Find("panel_Side_GateType2_G" + (object) num, true)[0];
        RadioButton radioButton = (RadioButton) this.tabControl_Group.TabPages[num - 1].Controls.Find("radioButton_Side_GateNormal_G" + (object) num, true)[0];
        panel.Enabled = false;
        radioButton.Checked = true;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]radioButton_Side_GateRect_CheckedChanged):" + ex.Message));
      }
    }

    private void radioButton_Side_GateCircle_CheckedChanged(object sender, EventArgs e)
    {
      int num = clsUtill.ConvertToInt(Regex.Match((sender as RadioButton).Name, "_G(\\d)").Groups[1].Value);
      try
      {
        this.tabControl_Group.TabPages[num - 1].Controls.Find("label_Side_GateDim1_G" + (object) num, true)[0].Text = "Start Dimension";
        this.tabControl_Group.TabPages[num - 1].Controls.Find("label_Side_GateDim2_G" + (object) num, true)[0].Text = "End Dimension";
        Label label1 = (Label) this.tabControl_Group.TabPages[num - 1].Controls.Find("label_Side_GateDim3_G" + (object) num, true)[0];
        label1.Text = "";
        label1.BackColor = Color.LightGray;
        Label label2 = (Label) this.tabControl_Group.TabPages[num - 1].Controls.Find("label_Side_GateDim4_G" + (object) num, true)[0];
        label2.Text = "";
        label2.BackColor = Color.LightGray;
        NewTextBox newTextBox1 = (NewTextBox) this.tabControl_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_GateDim3_G" + (object) num, true)[0];
        newTextBox1.TextBoxBackColor = Color.LightGray;
        newTextBox1.TextForeColor = Color.LightGray;
        newTextBox1.ReadOnly = true;
        NewTextBox newTextBox2 = (NewTextBox) this.tabControl_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_GateDim4_G" + (object) num, true)[0];
        newTextBox2.TextBoxBackColor = Color.LightGray;
        newTextBox2.TextForeColor = Color.LightGray;
        newTextBox2.ReadOnly = true;
        Panel panel = (Panel) this.tabControl_Group.TabPages[num - 1].Controls.Find("panel_Side_GateType2_G" + (object) num, true)[0];
        RadioButton radioButton1 = (RadioButton) this.tabControl_Group.TabPages[num - 1].Controls.Find("radioButton_Side_GateNormal_G" + (object) num, true)[0];
        RadioButton radioButton2 = (RadioButton) this.tabControl_Group.TabPages[num - 1].Controls.Find("radioButton_Side_GateSubMarine_G" + (object) num, true)[0];
        panel.Enabled = true;
        if (radioButton1.Checked || radioButton2.Checked)
          return;
        radioButton1.Checked = true;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]radioButton_Side_GateCircle_CheckedChanged):" + ex.Message));
      }
    }

    private void radioButton_Side_RunnerTrepezoidal_CheckedChanged(object sender, EventArgs e)
    {
      int num = clsUtill.ConvertToInt(Regex.Match((sender as RadioButton).Name, "_G(\\d)").Groups[1].Value);
      try
      {
        Label label1 = (Label) this.tabControl_Group.TabPages[num - 1].Controls.Find("label_Side_RunnerDim1_G" + (object) num, true)[0];
        label1.Text = "Top Width";
        label1.BackColor = Color.Lavender;
        Label label2 = (Label) this.tabControl_Group.TabPages[num - 1].Controls.Find("label_Side_RunnerDim2_G" + (object) num, true)[0];
        label2.Text = "Bottom Width";
        label2.BackColor = Color.Lavender;
        Label label3 = (Label) this.tabControl_Group.TabPages[num - 1].Controls.Find("label_Side_RunnerDim3_G" + (object) num, true)[0];
        label3.Text = "Height";
        label3.BackColor = Color.Lavender;
        Label label4 = (Label) this.tabControl_Group.TabPages[num - 1].Controls.Find("label_Side_RunnerDim4_G" + (object) num, true)[0];
        label4.Text = "";
        label4.BackColor = Color.LightGray;
        NewTextBox newTextBox1 = (NewTextBox) this.tabControl_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_RunnerDim2_G" + (object) num, true)[0];
        if (newTextBox1.Value == "")
          newTextBox1.Value = "0";
        newTextBox1.ReadOnly = false;
        newTextBox1.TextBoxBackColor = Color.White;
        newTextBox1.TextForeColor = Color.Black;
        NewTextBox newTextBox2 = (NewTextBox) this.tabControl_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_RunnerDim3_G" + (object) num, true)[0];
        if (newTextBox2.Value == "")
          newTextBox2.Value = "0";
        newTextBox2.TextBoxBackColor = Color.White;
        newTextBox2.TextForeColor = Color.Black;
        newTextBox2.ReadOnly = false;
        NewTextBox newTextBox3 = (NewTextBox) this.tabControl_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_RunnerDim4_G" + (object) num, true)[0];
        if (newTextBox3.Value == "")
          newTextBox3.Value = "0";
        newTextBox3.TextBoxBackColor = Color.LightGray;
        newTextBox3.TextForeColor = Color.LightGray;
        newTextBox3.ReadOnly = true;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]radioButton_Side_RunnerTrepezoidal_CheckedChanged):" + ex.Message));
      }
    }

    private void radioButton_Side_RunnerCircle_CheckedChanged(object sender, EventArgs e)
    {
      int num = clsUtill.ConvertToInt(Regex.Match((sender as RadioButton).Name, "_G(\\d)").Groups[1].Value);
      try
      {
        Label label1 = (Label) this.tabControl_Group.TabPages[num - 1].Controls.Find("label_Side_RunnerDim1_G" + (object) num, true)[0];
        label1.Text = "Dimension";
        label1.BackColor = Color.Lavender;
        Label label2 = (Label) this.tabControl_Group.TabPages[num - 1].Controls.Find("label_Side_RunnerDim2_G" + (object) num, true)[0];
        label2.Text = "";
        label2.BackColor = Color.LightGray;
        Label label3 = (Label) this.tabControl_Group.TabPages[num - 1].Controls.Find("label_Side_RunnerDim3_G" + (object) num, true)[0];
        label3.Text = "";
        label3.BackColor = Color.LightGray;
        Label label4 = (Label) this.tabControl_Group.TabPages[num - 1].Controls.Find("label_Side_RunnerDim4_G" + (object) num, true)[0];
        label4.Text = "";
        label4.BackColor = Color.LightGray;
        NewTextBox newTextBox1 = (NewTextBox) this.tabControl_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_RunnerDim2_G" + (object) num, true)[0];
        newTextBox1.TextBoxBackColor = Color.LightGray;
        newTextBox1.TextForeColor = Color.LightGray;
        newTextBox1.ReadOnly = true;
        NewTextBox newTextBox2 = (NewTextBox) this.tabControl_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_RunnerDim3_G" + (object) num, true)[0];
        newTextBox2.TextBoxBackColor = Color.LightGray;
        newTextBox2.TextForeColor = Color.LightGray;
        newTextBox2.ReadOnly = true;
        NewTextBox newTextBox3 = (NewTextBox) this.tabControl_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_RunnerDim4_G" + (object) num, true)[0];
        newTextBox3.TextBoxBackColor = Color.LightGray;
        newTextBox3.TextForeColor = Color.LightGray;
        newTextBox3.ReadOnly = true;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]radioButton_Side_RunnerCircle_CheckedChanged):" + ex.Message));
      }
    }

    private void radioButton_Side_RunnerRectangle_CheckedChanged(object sender, EventArgs e)
    {
      int num = clsUtill.ConvertToInt(Regex.Match((sender as RadioButton).Name, "_G(\\d)").Groups[1].Value);
      try
      {
        Label label1 = (Label) this.tabControl_Group.TabPages[num - 1].Controls.Find("label_Side_RunnerDim1_G" + (object) num, true)[0];
        label1.Text = "Start Width";
        label1.BackColor = Color.Lavender;
        Label label2 = (Label) this.tabControl_Group.TabPages[num - 1].Controls.Find("label_Side_RunnerDim2_G" + (object) num, true)[0];
        label2.Text = "Start Height";
        label2.BackColor = Color.Lavender;
        Label label3 = (Label) this.tabControl_Group.TabPages[num - 1].Controls.Find("label_Side_RunnerDim3_G" + (object) num, true)[0];
        label3.Text = "End Width";
        label3.BackColor = Color.Lavender;
        Label label4 = (Label) this.tabControl_Group.TabPages[num - 1].Controls.Find("label_Side_RunnerDim4_G" + (object) num, true)[0];
        label4.Text = "End Height";
        label4.BackColor = Color.Lavender;
        NewTextBox newTextBox1 = (NewTextBox) this.tabControl_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_RunnerDim2_G" + (object) num, true)[0];
        if (newTextBox1.Value == "")
          newTextBox1.Value = "0";
        newTextBox1.ReadOnly = false;
        newTextBox1.TextBoxBackColor = Color.White;
        newTextBox1.TextForeColor = Color.Black;
        NewTextBox newTextBox2 = (NewTextBox) this.tabControl_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_RunnerDim3_G" + (object) num, true)[0];
        if (newTextBox2.Value == "")
          newTextBox2.Value = "0";
        newTextBox2.TextBoxBackColor = Color.White;
        newTextBox2.TextForeColor = Color.Black;
        newTextBox2.ReadOnly = false;
        NewTextBox newTextBox3 = (NewTextBox) this.tabControl_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_RunnerDim4_G" + (object) num, true)[0];
        if (newTextBox3.Value == "")
          newTextBox3.Value = "0";
        newTextBox3.TextBoxBackColor = Color.White;
        newTextBox3.TextForeColor = Color.Black;
        newTextBox3.ReadOnly = false;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]radioButton_Side_RunnerRectangle_CheckedChanged):" + ex.Message));
      }
    }

    private void newButton_Group_NewClick(object sender, EventArgs e)
    {
      if (!(sender is NewButton p_nbSelected))
        return;
      this.tabControl_Group.SelectedIndex = !p_nbSelected.Name.Contains("1") ? (!p_nbSelected.Name.Contains("2") ? (!p_nbSelected.Name.Contains("3") ? 3 : 2) : 1) : 0;
      this.SetTabButtonColor(p_nbSelected, this.panel_Group, this.tabControl_Group);
    }

    private void newButton_Hor_RunnerType_NewClick(object sender, EventArgs e)
    {
      if (!(sender is NewButton newButton))
        return;
      if (newButton.Name.Contains("Two"))
        this.tabControl_Hor_RunnerType.SelectedIndex = 0;
      else
        this.tabControl_Hor_RunnerType.SelectedIndex = 1;
    }

    private void tabControl_Hor_RunnerType_SelectedIndexChanged(object sender, EventArgs e) => this.SetTabButtonColor(this.tabControl_Hor_RunnerType.SelectedIndex != 0 ? this.newButton_Hor_Runner_ThreeStage : this.newButton_Hor_Runner_TwoStage, this.panel_Hor_RunnerType, this.tabControl_Hor_RunnerType);

    private void SetTabButtonColor(NewButton p_nbSelected, Panel p_pnMain, TabControl p_tabMain)
    {
      foreach (NewButton newButton in p_pnMain.Controls.OfType<NewButton>())
      {
        int selectedIndex = p_tabMain.SelectedIndex;
        newButton.ButtonBackColor = !newButton.Name.Equals(p_nbSelected.Name) ? Color.White : Color.LightSteelBlue;
      }
    }

    private void newButton_Runner_NewClick(object sender, EventArgs e)
    {
      string empty1 = string.Empty;
      List<string> p_lst_strNode = new List<string>();
      bool p_isValve = false;
      if (this.radioButton_Pin_HotSystem.Checked)
        p_isValve = true;
      else if (this.radioButton_Pin_ColdSystem.Checked)
        p_isValve = false;
      NewButton newButton = sender as NewButton;
      if (newButton == this.newButton_Node_Pin_Select || newButton == this.newButton_Node_Side_Select || newButton == this.newButton_Node_Add || newButton == this.newButton_Node_Del)
      {
        if (newButton == this.newButton_Node_Pin_Select || newButton == this.newButton_Node_Side_Select)
        {
          string empty2 = string.Empty;
          string p_strType = newButton != this.newButton_Node_Pin_Select ? "Side" : "Pin";
          Dictionary<string, string> selectedNode = clsHDMFLib.GetSelectedNode();
          if (selectedNode.Count > 0)
          {
            foreach (KeyValuePair<string, string> keyValuePair in selectedNode)
            {
              string[] p_arr_strData = keyValuePair.Value.Split(',');
              p_lst_strNode.Add(keyValuePair.Value);
              this.AddNodeData(p_arr_strData, p_isValve, p_strType);
            }
            clsHDMFLib.ChangeColorOfNodes(p_lst_strNode);
          }
          this.newTextBox_Node.Value = this.dataGridView_Node.Rows.Count.ToString();
        }
        else if (newButton == this.newButton_Node_Add)
        {
          this.AddNodeData(new string[3]{ "0", "0", "0" }, p_isValve);
          this.newTextBox_Node.Value = this.dataGridView_Node.Rows.Count.ToString();
        }
        else if (newButton == this.newButton_Node_Del)
        {
          List<DataGridViewRow> dataGridViewRowList = new List<DataGridViewRow>();
          foreach (DataGridViewRow selectedRow in (BaseCollection) this.dataGridView_Node.SelectedRows)
          {
            if (!dataGridViewRowList.Contains(this.dataGridView_Node.Rows[selectedRow.Index]))
            {
              dataGridViewRowList.Add(this.dataGridView_Node.Rows[selectedRow.Index]);
              string str = Math.Round(clsUtill.ConvertToDouble(selectedRow.Cells[2].Value.ToString()), 3).ToString() + "," + (object) Math.Round(clsUtill.ConvertToDouble(selectedRow.Cells[3].Value.ToString()), 3) + "," + (object) Math.Round(clsUtill.ConvertToDouble(selectedRow.Cells[4].Value.ToString()), 3);
              p_lst_strNode.Add(str);
            }
          }
          foreach (DataGridViewRow dataGridViewRow in dataGridViewRowList)
            this.dataGridView_Node.Rows.Remove(dataGridViewRow);
          this.newTextBox_Node.Value = this.dataGridView_Node.Rows.Count.ToString();
          clsHDMFLib.ResetColorOfSelectedNodes(p_lst_strNode);
        }
        this.dataGridView_Node.CurrentCell = (DataGridViewCell) null;
        this.dataGridView_Node.ClearSelection();
      }
      else if (newButton == this.newButton_Node_Update)
        this.RefreshNodeColor();
      else if (newButton == this.newButton_LoadPrevNode)
        this.LoadPrevNodeData(p_isValve);
      else if (newButton == this.newButton_VavNode)
        this.GetSelectedNode();
      else if (newButton == this.newButton_Apply)
        this.CreateRunner();
      else
        this.EditDB();
    }

    private void newButton_Pin_GateLength_NewClick(object sender, EventArgs e)
    {
      int selectedIndex = this.tabControl_Group.SelectedIndex;
      frmPinGate frmPinGate = new frmPinGate();
      frmPinGate.m_strPinLength1 = this.m_dicGate["PinLength1_G" + (object) (selectedIndex + 1)];
      frmPinGate.m_strPinLength2 = this.m_dicGate["PinLength2_G" + (object) (selectedIndex + 1)];
      frmPinGate.m_strPinLength3 = this.m_dicGate["PinLength3_G" + (object) (selectedIndex + 1)];
      frmPinGate.m_strPinLength4 = this.m_dicGate["PinLength4_G" + (object) (selectedIndex + 1)];
      frmPinGate.m_strPinDiameter = this.m_dicGate["PinDiameter_G" + (object) (selectedIndex + 1)];
      if (frmPinGate.ShowDialog() != DialogResult.OK)
        return;
      this.m_dicGate["PinLength1_G" + (object) (selectedIndex + 1)] = frmPinGate.m_strPinLength1;
      this.m_dicGate["PinLength2_G" + (object) (selectedIndex + 1)] = frmPinGate.m_strPinLength2;
      this.m_dicGate["PinLength3_G" + (object) (selectedIndex + 1)] = frmPinGate.m_strPinLength3;
      this.m_dicGate["PinLength4_G" + (object) (selectedIndex + 1)] = frmPinGate.m_strPinLength4;
      this.m_dicGate["PinDiameter_G" + (object) (selectedIndex + 1)] = frmPinGate.m_strPinDiameter;
      ((NewTextBox) this.tabControl_Group.TabPages[selectedIndex].Controls.Find("newTextBox_Pin_GateLength_G" + (object) (selectedIndex + 1), true)[0]).Value = frmPinGate.m_strTotalLength;
    }

    private void GetSelectedNode()
    {
      Dictionary<string, string> dictionary1 = new Dictionary<string, string>();
      Dictionary<string, string> dictionary2 = new Dictionary<string, string>();
      string empty = string.Empty;
      try
      {
        DataGridViewSelectedRowCollection currentValveRows = this.GetCurrentValveRows();
        if (currentValveRows == null)
          return;
        Dictionary<string, string> selectedNode = clsHDMFLib.GetSelectedNode();
        Dictionary<string, string> allNodes = clsHDMFLib.GetAllNodes();
        if (selectedNode.Count == 0)
          return;
        this.newTextBox_VavNode.Value = clsHDMFLib.GetNodeFromCoord(allNodes, selectedNode.FirstOrDefault<KeyValuePair<string, string>>().Value).Replace("N", "");
        foreach (DataGridViewRow dataGridViewRow in (BaseCollection) currentValveRows)
          dataGridViewRow.Cells["Column_VavLoc"].Value = (object) this.newTextBox_VavNode.Value;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]GetSelectedNode):" + ex.Message));
      }
    }

    private void RefreshNodeColor()
    {
      string empty = string.Empty;
      List<string> p_lst_strNode = new List<string>();
      try
      {
        foreach (DataGridViewRow row in (IEnumerable) this.dataGridView_Node.Rows)
        {
          string str = Math.Round(clsUtill.ConvertToDouble(row.Cells[2].Value.ToString()), 3).ToString() + "," + (object) Math.Round(clsUtill.ConvertToDouble(row.Cells[3].Value.ToString()), 3) + "," + (object) Math.Round(clsUtill.ConvertToDouble(row.Cells[4].Value.ToString()), 3);
          p_lst_strNode.Add(str);
        }
        clsHDMFLib.ResetColorOfAllNodes();
        clsHDMFLib.ChangeColorOfNodes(p_lst_strNode);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]RefreshGateColor):" + ex.Message));
      }
    }

    private void LoadPrevNodeData(bool p_isValve)
    {
      string strSection = string.Empty;
      List<string> p_lst_strNode = new List<string>();
      StringBuilder stringBuilder = new StringBuilder();
      if (!this.m_fiNodeData.Exists)
        return;
      DataTable iniData = clsUtill.GetINIData(this.m_fiNodeData.FullName);
      try
      {
        if (this.radioButton_Node1.Checked)
          strSection = "Node1";
        else if (this.radioButton_Node2.Checked)
          strSection = "Node2";
        else if (this.radioButton_Node3.Checked)
          strSection = "Node3";
        else if (this.radioButton_Node4.Checked)
          strSection = "Node4";
        DataRow dataRow = iniData.Rows.Cast<DataRow>().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Section"].ToString() == strSection)).FirstOrDefault<DataRow>();
        if (dataRow == null)
          return;
        for (int index = 1; index < dataRow.ItemArray.Length; ++index)
        {
          string[] strArray = dataRow.ItemArray[index].ToString().Split('|');
          if (strArray.Length >= 3)
          {
            string p_strType = strArray[0];
            string p_strGroup = strArray[1];
            this.AddNodeData(strArray[2].Split(','), p_isValve, p_strType, p_strGroup);
            p_lst_strNode.Add(strArray[2]);
            if (strArray.Length > 3)
              this.SetValveData(this.dataGridView_Node.Rows[this.dataGridView_Node.Rows.GetLastRow(DataGridViewElementStates.None)], strArray[3]);
          }
        }
        clsHDMFLib.ChangeColorOfNodes(p_lst_strNode);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]LoadPreNode):" + ex.Message));
      }
      this.newTextBox_Node.Value = this.dataGridView_Node.Rows.Count.ToString();
      this.dataGridView_Node.ClearSelection();
    }

    private void AddNodeData(
      string[] p_arr_strData,
      bool p_isValve,
      string p_strType = "Pin",
      string p_strGroup = "G1")
    {
      try
      {
        DataGridViewRow row = this.dataGridView_Node.Rows[this.dataGridView_Node.Rows.Add()];
        DataGridViewComboBoxCell cell1 = (DataGridViewComboBoxCell) row.Cells[0];
        cell1.Items.Add((object) "Pin");
        cell1.Items.Add((object) "Side");
        cell1.Value = (object) p_strType;
        DataGridViewComboBoxCell cell2 = (DataGridViewComboBoxCell) row.Cells[1];
        foreach (KeyValuePair<string, string> keyValuePair in this.m_dicGroup)
          cell2.Items.Add((object) keyValuePair.Value);
        cell2.Value = (object) this.m_dicGroup[p_strGroup];
        row.Cells[2].Value = (object) p_arr_strData[0];
        row.Cells[3].Value = (object) p_arr_strData[1];
        row.Cells[4].Value = (object) p_arr_strData[2];
        string str = "15";
        if (clsDefine.g_dicValve.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "VavClose")))
          str = clsDefine.g_dicValve["VavClose"];
        row.Cells["Column_VavCheck"].Value = (object) p_isValve;
        row.Cells["Column_VavTrigger"].Value = (object) "Time";
        row.Cells["Column_VavState"].Value = (object) "Open";
        row.Cells["Column_VavLoc"].Value = (object) "";
        row.Cells["Column_VavOpen1"].Value = (object) "0";
        row.Cells["Column_VavClose1"].Value = (object) str;
        row.Cells["Column_VavDelay"].Value = (object) "0";
        DataGridViewComboBoxCell cell3 = (DataGridViewComboBoxCell) row.Cells[5];
        cell3.Items.Clear();
        cell3.Items.Add((object) "+X");
        cell3.Items.Add((object) "-X");
        cell3.Items.Add((object) "+Y");
        cell3.Items.Add((object) "-Y");
        if (p_strType == "Side")
        {
          cell3.ReadOnly = false;
          if (p_arr_strData.Length > 3)
            cell3.Value = (object) p_arr_strData[3];
          else
            cell3.Value = cell3.Items[0];
        }
        else
          cell3.ReadOnly = true;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]AddNodeData):" + ex.Message));
      }
    }

    private void SetValveData(DataGridViewRow p_dgvrNode, string p_strValveData)
    {
      try
      {
        string[] strArray = p_strValveData.Split(',');
        p_dgvrNode.Cells["Column_VavCheck"].Value = (object) clsUtill.ConvertToBoolean(strArray[0]);
        p_dgvrNode.Cells["Column_VavTrigger"].Value = (object) strArray[1];
        p_dgvrNode.Cells["Column_VavState"].Value = (object) strArray[2];
        p_dgvrNode.Cells["Column_VavLoc"].Value = (object) strArray[3];
        p_dgvrNode.Cells["Column_VavOpen1"].Value = (object) strArray[4];
        p_dgvrNode.Cells["Column_VavClose1"].Value = (object) strArray[5];
        p_dgvrNode.Cells["Column_VavDelay"].Value = (object) strArray[6];
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]SetValveData):" + ex.Message));
      }
    }

    private void SaveNodeData()
    {
      string p_strSection = string.Empty;
      string empty = string.Empty;
      StringBuilder stringBuilder = new StringBuilder();
      try
      {
        if (this.radioButton_Node1.Checked)
          p_strSection = "Node1";
        else if (this.radioButton_Node2.Checked)
          p_strSection = "Node2";
        else if (this.radioButton_Node3.Checked)
          p_strSection = "Node3";
        else if (this.radioButton_Node4.Checked)
          p_strSection = "Node4";
        clsUtill.WriteINI(p_strSection, (string) null, (string) null, this.m_fiNodeData.FullName);
        for (int j = 0; j < this.dataGridView_Node.Rows.Count; j++)
        {
          stringBuilder.Clear();
          string str = this.dataGridView_Node.Rows[j].Cells[0].Value.ToString();
          stringBuilder.Append(str);
          stringBuilder.Append("|");
          string key = this.m_dicGroup.FirstOrDefault<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Value.Equals(this.dataGridView_Node.Rows[j].Cells[1].Value.ToString()))).Key;
          stringBuilder.Append(key);
          stringBuilder.Append("|");
          stringBuilder.Append(Math.Round(clsUtill.ConvertToDouble(this.dataGridView_Node.Rows[j].Cells[2].Value.ToString()), 3));
          stringBuilder.Append(",");
          stringBuilder.Append(Math.Round(clsUtill.ConvertToDouble(this.dataGridView_Node.Rows[j].Cells[3].Value.ToString()), 3));
          stringBuilder.Append(",");
          stringBuilder.Append(Math.Round(clsUtill.ConvertToDouble(this.dataGridView_Node.Rows[j].Cells[4].Value.ToString()), 3));
          if (str == "Side")
          {
            stringBuilder.Append(",");
            stringBuilder.Append(this.dataGridView_Node.Rows[j].Cells[5].Value.ToString());
          }
          if (this.radioButton_Pin_HotSystem.Checked)
          {
            stringBuilder.Append("|");
            stringBuilder.Append(this.dataGridView_Node.Rows[j].Cells["Column_VavCheck"].Value);
            stringBuilder.Append(",");
            stringBuilder.Append(this.dataGridView_Node.Rows[j].Cells["Column_VavTrigger"].Value);
            stringBuilder.Append(",");
            stringBuilder.Append(this.dataGridView_Node.Rows[j].Cells["Column_VavState"].Value);
            stringBuilder.Append(",");
            stringBuilder.Append(this.dataGridView_Node.Rows[j].Cells["Column_VavLoc"].Value);
            stringBuilder.Append(",");
            stringBuilder.Append(this.dataGridView_Node.Rows[j].Cells["Column_VavOpen1"].Value);
            stringBuilder.Append(",");
            stringBuilder.Append(this.dataGridView_Node.Rows[j].Cells["Column_VavClose1"].Value);
            stringBuilder.Append(",");
            stringBuilder.Append(this.dataGridView_Node.Rows[j].Cells["Column_VavDelay"].Value);
          }
          clsUtill.WriteINI(p_strSection, "N" + (object) (j + 1), stringBuilder.ToString(), this.m_fiNodeData.FullName);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]SaveNodeData):" + ex.Message));
      }
    }

    private void CreateRunner()
    {
      bool flag1 = false;
      bool flag2 = false;
      string strTmp = "";
      string empty = string.Empty;
      StringBuilder stringBuilder = new StringBuilder();
      KeyValuePair<string, string> keyValuePair1 = new KeyValuePair<string, string>();
      Dictionary<string, string> p_dicData = new Dictionary<string, string>();
      List<string> stringList1 = new List<string>();
      List<string> stringList2 = new List<string>();
      List<string> p_lst_strUsingGroup1 = new List<string>();
      List<string> p_lst_strUsingGroup2 = new List<string>();
      if (this.dataGridView_Node.Rows.Count == 0 || this.newTextBox_CenterX.Value == string.Empty || this.newTextBox_CenterY.Value == string.Empty)
        return;
      this.Hide();
      clsUtill.StartProgress("create Runner...", (Form) this);
      try
      {
        foreach (DataGridViewRow row in (IEnumerable) this.dataGridView_Node.Rows)
        {
          DataGridViewRow dgvrNode = row;
          string str = dgvrNode.Cells[0].Value.ToString();
          strTmp = this.m_dicGroup.FirstOrDefault<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Value.Equals(dgvrNode.Cells[1].Value.ToString()))).Key;
          if (!p_lst_strUsingGroup1.Contains(strTmp))
            p_lst_strUsingGroup1.Add(strTmp);
          if (str == "Side")
            flag1 = true;
          if (str == "Side" && !p_lst_strUsingGroup2.Contains(strTmp))
            p_lst_strUsingGroup2.Add(strTmp);
          strTmp = strTmp + "|" + (object) Math.Round(clsUtill.ConvertToDouble(dgvrNode.Cells[2].Value.ToString()), 3) + "," + (object) Math.Round(clsUtill.ConvertToDouble(dgvrNode.Cells[3].Value.ToString()), 3) + "," + (object) Math.Round(clsUtill.ConvertToDouble(dgvrNode.Cells[4].Value.ToString()), 3);
          if (str == "Side")
            strTmp = strTmp + "," + dgvrNode.Cells[5].Value;
          stringList2.Add(strTmp);
          if (this.radioButton_Pin_HotSystem.Checked && clsUtill.ConvertToBoolean(dgvrNode.Cells["Column_VavCheck"].Value.ToString()))
            strTmp = strTmp + "|" + dgvrNode.Cells["Column_VavCheck"].Value + "," + dgvrNode.Cells["Column_VavTrigger"].Value + "," + dgvrNode.Cells["Column_VavState"].Value + "," + dgvrNode.Cells["Column_VavLoc"].Value + "," + dgvrNode.Cells["Column_VavOpen1"].Value + "," + dgvrNode.Cells["Column_VavClose1"].Value + "," + dgvrNode.Cells["Column_VavDelay"].Value;
          stringList1.Add(strTmp);
        }
        stringList1.Sort(new Comparison<string>(this.SortPoint));
        double num1 = double.MinValue;
        for (int index = 0; index < stringList1.Count; ++index)
        {
          double num2;
          if (!stringList1[index].Contains("|"))
          {
            if (this.radioButton_Side_GateSubMarine_G1.Checked)
              num2 = Math.Round(clsUtill.ConvertToDouble(stringList1[index].Split('|')[1].Split(',')[2]) + clsUtill.ConvertToDouble(this.newTextBox_Side_GateLength_G1.Value), 3);
            else
              num2 = Math.Round(clsUtill.ConvertToDouble(stringList1[index].Split('|')[1].Split(',')[2]), 3);
          }
          else
            num2 = Math.Round(clsUtill.ConvertToDouble(stringList1[index].Split('|')[1].Split(',')[2]), 3);
          if (num1 < num2)
            num1 = num2;
          p_dicData.Add("POS_" + (object) index, stringList1[index]);
        }
        p_dicData.Add("CenterX", this.newTextBox_CenterX.Value);
        p_dicData.Add("CenterY", this.newTextBox_CenterY.Value);
        string modelXyLength = clsHDMFLib.GetModelXYLength();
        if (!string.IsNullOrEmpty(modelXyLength))
        {
          string[] strArray = modelXyLength.Split(',');
          p_dicData.Add("XLength", strArray[0]);
          p_dicData.Add("YLength", strArray[1]);
        }
        if (this.radioButton_Pin_ColdSystem.Checked)
          p_dicData.Add("Pin_SysType", "0");
        else
          p_dicData.Add("Pin_SysType", "1");
        stringBuilder.Clear();
        foreach (string str in p_lst_strUsingGroup1)
        {
          p_dicData.Add("Pin_GateLen1_" + str, this.m_dicGate["PinLength1_" + str]);
          p_dicData.Add("Pin_GateLen2_" + str, this.m_dicGate["PinLength2_" + str]);
          p_dicData.Add("Pin_GateLen3_" + str, this.m_dicGate["PinLength3_" + str]);
          p_dicData.Add("Pin_GateLen4_" + str, this.m_dicGate["PinLength4_" + str]);
          p_dicData.Add("Pin_GateDia_" + str, this.m_dicGate["PinDiameter_" + str]);
          p_dicData.Add("Pin_GateTotalLen_" + str, ((NewTextBox) this.tabControl_Group.TabPages["tabPage_" + str].Controls.Find("newTextBox_Pin_GateLength_" + str, true)[0]).Value);
          p_dicData.Add("Pin_GateDim1_" + str, ((NewTextBox) this.tabControl_Group.TabPages["tabPage_" + str].Controls.Find("newTextBox_Pin_GateDim1_" + str, true)[0]).Value);
          p_dicData.Add("Pin_GateDim2_" + str, ((NewTextBox) this.tabControl_Group.TabPages["tabPage_" + str].Controls.Find("newTextBox_Pin_GateDim2_" + str, true)[0]).Value);
          p_dicData.Add("Pin_RunnerLen_" + str, ((NewTextBox) this.tabControl_Group.TabPages["tabPage_" + str].Controls.Find("newTextBox_Pin_RunnerLength_" + str, true)[0]).Value);
          p_dicData.Add("Pin_RunnerDim1_" + str, ((NewTextBox) this.tabControl_Group.TabPages["tabPage_" + str].Controls.Find("newTextBox_Pin_RunnerDim1_" + str, true)[0]).Value);
          p_dicData.Add("Pin_RunnerDim2_" + str, ((NewTextBox) this.tabControl_Group.TabPages["tabPage_" + str].Controls.Find("newTextBox_Pin_RunnerDim2_" + str, true)[0]).Value);
          stringBuilder.Append(str + "|");
        }
        p_dicData.Add("Pin_UsingGroup", stringBuilder.ToString());
        stringBuilder.Clear();
        foreach (string str in p_lst_strUsingGroup2)
        {
          if (((RadioButton) this.tabControl_Group.TabPages["tabPage_" + str].Controls.Find("radioButton_Side_GateRect_" + str, true)[0]).Checked)
            p_dicData.Add("Side_GateAttr1_" + str, "0");
          else
            p_dicData.Add("Side_GateAttr1_" + str, "1");
          if (((RadioButton) this.tabControl_Group.TabPages["tabPage_" + str].Controls.Find("radioButton_Side_GateNormal_" + str, true)[0]).Checked)
            p_dicData.Add("Side_GateAttr2_" + str, "0");
          else
            p_dicData.Add("Side_GateAttr2_" + str, "1");
          p_dicData.Add("Side_GateLen_" + str, ((NewTextBox) this.tabControl_Group.TabPages["tabPage_" + str].Controls.Find("newTextBox_Side_GateLength_" + str, true)[0]).Value);
          p_dicData.Add("Side_GateDim1_" + str, ((NewTextBox) this.tabControl_Group.TabPages["tabPage_" + str].Controls.Find("newTextBox_Side_GateDim1_" + str, true)[0]).Value);
          p_dicData.Add("Side_GateDim2_" + str, ((NewTextBox) this.tabControl_Group.TabPages["tabPage_" + str].Controls.Find("newTextBox_Side_GateDim2_" + str, true)[0]).Value);
          p_dicData.Add("Side_GateDim3_" + str, ((NewTextBox) this.tabControl_Group.TabPages["tabPage_" + str].Controls.Find("newTextBox_Side_GateDim3_" + str, true)[0]).Value);
          p_dicData.Add("Side_GateDim4_" + str, ((NewTextBox) this.tabControl_Group.TabPages["tabPage_" + str].Controls.Find("newTextBox_Side_GateDim4_" + str, true)[0]).Value);
          RadioButton radioButton1 = (RadioButton) this.tabControl_Group.TabPages["tabPage_" + str].Controls.Find("radioButton_Side_RunnerTrepezoidal_" + str, true)[0];
          RadioButton radioButton2 = (RadioButton) this.tabControl_Group.TabPages["tabPage_" + str].Controls.Find("radioButton_Side_RunnerCircle_" + str, true)[0];
          if (radioButton1.Checked)
            p_dicData.Add("Side_RunnerAttr_" + str, "0");
          else if (radioButton2.Checked)
            p_dicData.Add("Side_RunnerAttr_" + str, "1");
          else
            p_dicData.Add("Side_RunnerAttr_" + str, "2");
          p_dicData.Add("Side_RunnerLen_" + str, ((NewTextBox) this.tabControl_Group.TabPages["tabPage_" + str].Controls.Find("newTextBox_Side_RunnerLength_" + str, true)[0]).Value);
          p_dicData.Add("Side_RunnerDim1_" + str, ((NewTextBox) this.tabControl_Group.TabPages["tabPage_" + str].Controls.Find("newTextBox_Side_RunnerDim1_" + str, true)[0]).Value);
          p_dicData.Add("Side_RunnerDim2_" + str, ((NewTextBox) this.tabControl_Group.TabPages["tabPage_" + str].Controls.Find("newTextBox_Side_RunnerDim2_" + str, true)[0]).Value);
          p_dicData.Add("Side_RunnerDim3_" + str, ((NewTextBox) this.tabControl_Group.TabPages["tabPage_" + str].Controls.Find("newTextBox_Side_RunnerDim3_" + str, true)[0]).Value);
          p_dicData.Add("Side_RunnerDim4_" + str, ((NewTextBox) this.tabControl_Group.TabPages["tabPage_" + str].Controls.Find("newTextBox_Side_RunnerDim4_" + str, true)[0]).Value);
          stringBuilder.Append(str + "|");
        }
        p_dicData.Add("Side_UsingGroup", stringBuilder.ToString());
        p_dicData.Add("Side_SprueDia1", this.m_dicGate["SideDiameter1"]);
        p_dicData.Add("Side_SprueDia2", this.m_dicGate["SideDiameter2"]);
        p_dicData.Add("Side_SprueLen", this.m_dicGate["SideLength"]);
        if (this.tabControl_Hor_RunnerType.SelectedIndex == 0)
          p_dicData.Add("Hor_Stage_Type", "0");
        else
          p_dicData.Add("Hor_Stage_Type", "1");
        if (this.radioButton_Hor_Two_Type1.Checked)
          p_dicData.Add("Hor_Two_Type", "0");
        else if (this.radioButton_Hor_Two_Type2.Checked)
          p_dicData.Add("Hor_Two_Type", "1");
        else if (this.radioButton_Hor_Two_Type3.Checked)
          p_dicData.Add("Hor_Two_Type", "2");
        else
          p_dicData.Add("Hor_Two_Type", "3");
        p_dicData.Add("Hor_Two_Dia", this.newTextBox_Hor_Two_Dia.Value);
        p_dicData.Add("Hor_Two_Len", this.newTextBox_Hor_Two_Length.Value);
        p_dicData.Add("Hor_Two_Dir", this.newComboBox_Hor_Two_Direction.SelectedIndex.ToString());
        p_dicData.Add("Hor_Two_Dim1", this.newTextBox_Hor_Two_Dim1.Value);
        p_dicData.Add("Hor_Two_Dim2", this.newTextBox_Hor_Two_Dim2.Value);
        p_dicData.Add("Hor_Two_Angle", this.newTextBox_Hor_Two_Angle.Value);
        p_dicData.Add("Hor_Two_Inter", this.checkBox_Hor_Two_Inter.Checked.ToString());
        p_dicData.Add("Hor_Two_CenterTol", this.newTextBox_Hor_Two_CenterTol.Value);
        if (this.radioButton_Hor_Three_Type1.Checked)
          p_dicData.Add("Hor_Three_Type", "0");
        else
          p_dicData.Add("Hor_Three_Type", "1");
        p_dicData.Add("Hor_Three_Length", this.newTextBox_Hor_Three_Length.Value);
        p_dicData.Add("Hor_Three_Dia", this.newTextBox_Hor_Three_Dia.Value);
        p_dicData.Add("Hor_Three_CenterTol", this.newTextBox_Hor_Three_CenterTol.Value);
        if (this.radioButton_Quadrant2.Checked)
          p_dicData.Add("Hor_Three_QuadrantType", "0");
        else
          p_dicData.Add("Hor_Three_QuadrantType", "1");
        if (this.radioButton_Sprue_Cold.Checked)
          p_dicData.Add("Sprue_Type", "0");
        else
          p_dicData.Add("Sprue_Type", "1");
        p_dicData.Add("Sprue_Len", this.newTextBox_Sprue_Length.Value);
        p_dicData.Add("Sprue_Dim1", this.newTextBox_Sprue_Dim1.Value);
        p_dicData.Add("Sprue_Dim2", this.newTextBox_Sprue_Dim2.Value);
        p_dicData.Add("OccNumber", this.newTextBox_Cavity_Occ.Value);
        double maxValue1 = this.GetMaxValue(p_dicData, "Pin_RunnerLen");
        double maxValue2 = this.GetMaxValue(p_dicData, "Pin_GateTotalLen");
        double num3 = num1 + maxValue2 + maxValue1;
        double num4 = !flag1 ? num3 + 4.0 : num3 + clsUtill.ConvertToDouble(this.m_dicGate["SideLength"]);
        p_dicData.Add("Sprue_Z", num4.ToString());
        if (this.VerifyGroupData(p_lst_strUsingGroup1, ref empty) && this.VerifyGroupData(p_lst_strUsingGroup2, ref empty))
          flag2 = clsHDMFLib.CreateRunner(this.m_drStudy, p_dicData);
        if (flag2)
        {
          clsUtill.UpdateProgress("write to Study Notes(Node Infomation)...");
          Dictionary<string, string> allNodes = clsHDMFLib.GetAllNodes();
          List<string> p_lst_strNode = new List<string>();
          stringBuilder.Clear();
          foreach (string str in stringList2)
          {
            char[] chArray = new char[1]{ '|' };
            string[] strArray = str.Split(chArray)[1].Split(',');
            strTmp = strArray[0] + "," + strArray[1] + "," + strArray[2];
            p_lst_strNode.Add(strTmp);
            KeyValuePair<string, string> keyValuePair2 = allNodes.Where<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Value == strTmp)).FirstOrDefault<KeyValuePair<string, string>>();
            if (keyValuePair2.Key != null && !(keyValuePair2.Key == ""))
            {
              if (stringBuilder.Length > 0)
                stringBuilder.Append(" ");
              stringBuilder.Append(keyValuePair2.Key);
            }
          }
          clsHDMFLib.WriteGateNodeToStudyNote(stringBuilder.ToString());
          clsHDMFLib.ChangeColorOfNodes(p_lst_strNode);
        }
        this.SaveNodeData();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]CreateRunner):" + ex.Message));
      }
      GC.Collect();
      clsUtill.EndProgress();
      if (flag2)
      {
        this.DialogResult = DialogResult.OK;
        this.Close();
      }
      else
      {
        clsUtill.ShowForm((Form) this);
        if (empty != string.Empty)
        {
          int num = (int) clsUtill.ShowMessageBox((Form) this, empty, "Error", p_msgIcon: MessageBoxIcon.Hand);
        }
      }
    }

    private bool VerifyGroupData(List<string> p_lst_strUsingGroup, ref string strErrMsg)
    {
      bool flag = true;
      try
      {
        foreach (string str in p_lst_strUsingGroup)
        {
          foreach (NewTextBox newTextBox in this.tabControl_Group.TabPages["tabPage_" + str].Controls.OfType<NewTextBox>())
          {
            if (newTextBox.Value == string.Empty && newTextBox.BackColor != Color.LightGray)
            {
              strErrMsg = "[" + str + "] " + LocaleControl.getInstance().GetString("IDS_CHECK_DATA");
              flag = false;
              return flag;
            }
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]VerifyGroupData):" + ex.Message));
        strErrMsg = ex.Message;
        flag = false;
      }
      return flag;
    }

    private double GetMaxValue(Dictionary<string, string> p_dicData, string p_strKey)
    {
      double maxValue = double.MinValue;
      try
      {
        foreach (KeyValuePair<string, string> keyValuePair in p_dicData.Where<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key.Contains(p_strKey))))
        {
          double num = Math.Round(clsUtill.ConvertToDouble(keyValuePair.Value), 3);
          if (maxValue < num)
            maxValue = num;
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]GetMaxValue):" + ex.Message));
      }
      return maxValue;
    }

    private int SortPoint(string p_strPt1, string p_strPt2)
    {
      string[] strArray1 = p_strPt1.Split('|')[1].Split(',');
      string[] strArray2 = p_strPt2.Split('|')[1].Split(',');
      return Convert.ToDouble(strArray1[1]) > Convert.ToDouble(strArray2[1]) || Convert.ToDouble(strArray1[1]) >= Convert.ToDouble(strArray2[1]) && Convert.ToDouble(strArray1[0]) < Convert.ToDouble(strArray2[0]) ? -1 : 1;
    }

    private void newComboBox_Pin_VavTrigger_SelectedValueChanged(object sender, EventArgs e)
    {
      if (this.newComboBox_VavTrigger.SelectedIndex == -1)
        return;
      DataGridViewSelectedRowCollection currentValveRows = this.GetCurrentValveRows();
      if (currentValveRows == null)
        return;
      try
      {
        foreach (DataGridViewRow dataGridViewRow in (BaseCollection) currentValveRows)
          dataGridViewRow.Cells["Column_VavTrigger"].Value = (object) this.newComboBox_VavTrigger.Value;
        if (this.newComboBox_VavTrigger.SelectedIndex == 0)
        {
          this.label_Pin_VavLoc.Text = "";
          this.label_Pin_VavLoc.BackColor = Color.WhiteSmoke;
          this.newButton_VavNode.ButtonText = "";
          this.newButton_VavNode.ButtonBackColor = Color.WhiteSmoke;
          this.newComboBox_VavState.Enabled = true;
          this.newComboBox_VavState.SelectedIndex = 1;
          this.newTextBox_VavNode.Value = "";
          this.newTextBox_VavNode.ReadOnly = true;
          this.newTextBox_VavNode.TextBoxBackColor = Color.WhiteSmoke;
          this.newTextBox_VavNode.ForeColor = Color.WhiteSmoke;
        }
        else
        {
          this.label_Pin_VavLoc.Text = "Trigger location";
          this.label_Pin_VavLoc.BackColor = Color.Lavender;
          this.newButton_VavNode.ButtonText = "Specified Node";
          this.newButton_VavNode.ButtonBackColor = Color.LavenderBlush;
          this.newComboBox_VavState.Enabled = false;
          this.newComboBox_VavState.SelectedIndex = 1;
          this.newTextBox_VavNode.TextBoxBackColor = Color.White;
          this.newTextBox_VavNode.ReadOnly = false;
          this.newTextBox_VavNode.Value = currentValveRows[0].Cells["Column_VavLoc"].Value.ToString();
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]newComboBox_Pin_VavTrigger_SelectedValueChanged):" + ex.Message));
      }
    }

    private void newComboBox_Pin_VavState_SelectedValueChanged(object sender, EventArgs e)
    {
      if (this.newComboBox_VavState.SelectedIndex == -1)
        return;
      DataGridViewSelectedRowCollection currentValveRows = this.GetCurrentValveRows();
      if (currentValveRows == null)
        return;
      try
      {
        foreach (DataGridViewRow dataGridViewRow in (BaseCollection) currentValveRows)
          dataGridViewRow.Cells["Column_VavState"].Value = (object) this.newComboBox_VavState.Value;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]newComboBox_Pin_VavState_SelectedValueChanged):" + ex.Message));
      }
    }

    private void newTextBox_Vav_TextBoxKeyUp(object sender, KeyEventArgs e)
    {
      NewTextBox newTextBox = sender as NewTextBox;
      DataGridViewSelectedRowCollection currentValveRows = this.GetCurrentValveRows();
      if (currentValveRows == null)
        return;
      try
      {
        foreach (DataGridViewRow dataGridViewRow in (BaseCollection) currentValveRows)
        {
          if (newTextBox == this.newTextBox_VavNode)
          {
            if (e.KeyCode == Keys.V && e.Modifiers == Keys.Control && Clipboard.ContainsText())
            {
              string text = Clipboard.GetText();
              if (Regex.IsMatch(text, "N\\d+\\d$"))
                newTextBox.Value = text.Replace("N", "");
            }
            dataGridViewRow.Cells["Column_VavLoc"].Value = (object) newTextBox.Value;
          }
          else if (newTextBox == this.newTextBox_VavOpen1)
            dataGridViewRow.Cells["Column_VavOpen1"].Value = (object) newTextBox.Value;
          else if (newTextBox == this.newTextBox_VavClose1)
            dataGridViewRow.Cells["Column_VavClose1"].Value = (object) newTextBox.Value;
          else if (newTextBox == this.newTextBox_VavDelayTime)
            dataGridViewRow.Cells["Column_VavDelay"].Value = (object) newTextBox.Value;
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]newTextBox_Vav_TextBoxKeyUp):" + ex.Message));
      }
    }

    private void EditDB()
    {
      if (this.listBox_DB.SelectedIndex == -1)
        return;
      try
      {
        string strRunnerDB = this.listBox_DB.Text;
        string p_strCompany = this.newComboBox_Company.Value;
        string p_strItem = this.newComboBox_Item.Value;
        DataRow p_drRunnerDB = clsDefine.g_dtRunnerDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == strRunnerDB)).FirstOrDefault<DataRow>();
        if (p_drRunnerDB == null)
          return;
        this.UpdateDB(p_drRunnerDB);
        FileInfo fileInfo = new FileInfo(clsDefine.g_diRunnerDBCfg.ToString() + "\\" + p_drRunnerDB["Name"] + ".xml");
        if (fileInfo.Exists)
          fileInfo.Delete();
        this.CreateDBFile(p_drRunnerDB);
        this.RefreshDBList(p_strCompany, p_strItem);
        this.RefreshCompanyUI(p_strCompany);
        this.RefreshItemUI(p_strCompany);
        this.listBox_DB.SelectedIndex = this.listBox_DB.Items.IndexOf((object) strRunnerDB);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]EditDB):" + ex.Message));
      }
    }

    private void UpdateDB(DataRow p_drRunnerDB)
    {
      try
      {
        p_drRunnerDB["Pin_System"] = !this.radioButton_Pin_ColdSystem.Checked ? (object) "1" : (object) "0";
        for (int index = 0; index < this.tabControl_Group.TabCount; ++index)
        {
          p_drRunnerDB["Pin_GateDim1_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Pin_GateDim1_G" + (object) (index + 1), true)[0]).Value;
          p_drRunnerDB["Pin_GateDim2_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Pin_GateDim2_G" + (object) (index + 1), true)[0]).Value;
          p_drRunnerDB["Pin_RunnerLength_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Pin_RunnerLength_G" + (object) (index + 1), true)[0]).Value;
          p_drRunnerDB["Pin_RunnerDim1_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Pin_RunnerDim1_G" + (object) (index + 1), true)[0]).Value;
          p_drRunnerDB["Pin_RunnerDim2_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Pin_RunnerDim2_G" + (object) (index + 1), true)[0]).Value;
        }
        for (int index = 0; index < this.tabControl_Group.TabCount; ++index)
        {
          if (((RadioButton) this.tabControl_Group.TabPages[index].Controls.Find("radioButton_Side_GateRect_G" + (object) (index + 1), true)[0]).Checked)
            p_drRunnerDB["Side_GateType1_G" + (object) (index + 1)] = (object) "0";
          else
            p_drRunnerDB["Side_GateType1_G" + (object) (index + 1)] = (object) "1";
          if (((RadioButton) this.tabControl_Group.TabPages[index].Controls.Find("radioButton_Side_GateNormal_G" + (object) (index + 1), true)[0]).Checked)
            p_drRunnerDB["Side_GateType2_G" + (object) (index + 1)] = (object) "0";
          else
            p_drRunnerDB["Side_GateType2_G" + (object) (index + 1)] = (object) "1";
          p_drRunnerDB["Side_GateLength_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Side_GateLength_G" + (object) (index + 1), true)[0]).Value;
          p_drRunnerDB["Side_GateDim1_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Side_GateDim1_G" + (object) (index + 1), true)[0]).Value;
          p_drRunnerDB["Side_GateDim2_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Side_GateDim2_G" + (object) (index + 1), true)[0]).Value;
          p_drRunnerDB["Side_GateDim3_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Side_GateDim3_G" + (object) (index + 1), true)[0]).Value;
          p_drRunnerDB["Side_GateDim4_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Side_GateDim4_G" + (object) (index + 1), true)[0]).Value;
          RadioButton radioButton1 = (RadioButton) this.tabControl_Group.TabPages[index].Controls.Find("radioButton_Side_RunnerTrepezoidal_G" + (object) (index + 1), true)[0];
          RadioButton radioButton2 = (RadioButton) this.tabControl_Group.TabPages[index].Controls.Find("radioButton_Side_RunnerCircle_G" + (object) (index + 1), true)[0];
          if (radioButton1.Checked)
            p_drRunnerDB["Side_RunnerType_G" + (object) (index + 1)] = (object) "0";
          else if (radioButton2.Checked)
            p_drRunnerDB["Side_RunnerType_G" + (object) (index + 1)] = (object) "1";
          else
            p_drRunnerDB["Side_RunnerType_G" + (object) (index + 1)] = (object) "2";
          p_drRunnerDB["Side_RunnerLength_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Side_RunnerLength_G" + (object) (index + 1), true)[0]).Value;
          p_drRunnerDB["Side_RunnerDim1_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Side_RunnerDim1_G" + (object) (index + 1), true)[0]).Value;
          p_drRunnerDB["Side_RunnerDim2_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Side_RunnerDim2_G" + (object) (index + 1), true)[0]).Value;
          p_drRunnerDB["Side_RunnerDim3_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Side_RunnerDim3_G" + (object) (index + 1), true)[0]).Value;
          p_drRunnerDB["Side_RunnerDim4_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Group.TabPages[index].Controls.Find("newTextBox_Side_RunnerDim4_G" + (object) (index + 1), true)[0]).Value;
        }
        p_drRunnerDB["OccNumber"] = (object) this.newTextBox_Cavity_Occ.Value;
        p_drRunnerDB["Hor_Stage"] = (object) this.tabControl_Hor_RunnerType.SelectedIndex;
        p_drRunnerDB["Hor_Two_Type"] = !this.radioButton_Hor_Two_Type1.Checked ? (!this.radioButton_Hor_Two_Type2.Checked ? (!this.radioButton_Hor_Two_Type3.Checked ? (object) "3" : (object) "2") : (object) "1") : (object) "0";
        p_drRunnerDB["Hor_Two_Dia"] = (object) this.newTextBox_Hor_Two_Dia.Value;
        p_drRunnerDB["Hor_Two_Length"] = (object) this.newTextBox_Hor_Two_Length.Value;
        p_drRunnerDB["Hor_Two_Dir"] = (object) this.newComboBox_Hor_Two_Direction.SelectedIndex.ToString();
        p_drRunnerDB["Hor_Two_Dim1"] = (object) this.newTextBox_Hor_Two_Dim1.Value;
        p_drRunnerDB["Hor_Two_Dim2"] = (object) this.newTextBox_Hor_Two_Dim2.Value;
        p_drRunnerDB["Hor_Two_Angle"] = (object) this.newTextBox_Hor_Two_Angle.Value;
        p_drRunnerDB["Hor_Two_CenterTol"] = (object) this.newTextBox_Hor_Two_CenterTol.Value;
        p_drRunnerDB["Hor_Two_Inter"] = (object) this.checkBox_Hor_Two_Inter.Checked;
        p_drRunnerDB["Hor_Three_Quadrant"] = !this.radioButton_Quadrant2.Checked ? (object) "1" : (object) "0";
        p_drRunnerDB["Hor_Three_Type"] = !this.radioButton_Hor_Three_Type1.Checked ? (object) "1" : (object) "0";
        p_drRunnerDB["Hor_Three_Length"] = (object) this.newTextBox_Hor_Three_Length.Value;
        p_drRunnerDB["Hor_Three_Dia"] = (object) this.newTextBox_Hor_Three_Dia.Value;
        p_drRunnerDB["Hor_Three_CenterTol"] = (object) this.newTextBox_Hor_Three_CenterTol.Value;
        p_drRunnerDB["Sprue_Type"] = !this.radioButton_Sprue_Cold.Checked ? (object) "1" : (object) "0";
        p_drRunnerDB["Sprue_Length"] = (object) this.newTextBox_Sprue_Length.Value;
        p_drRunnerDB["Sprue_Dim1"] = (object) this.newTextBox_Sprue_Dim1.Value;
        p_drRunnerDB["Sprue_Dim2"] = (object) this.newTextBox_Sprue_Dim2.Value;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]UpdateDB):" + ex.Message));
      }
    }

    private void CreateDBFile(DataRow p_drRunnerDB)
    {
      try
      {
        FileInfo fileInfo = new FileInfo(clsDefine.g_diRunnerDBCfg.ToString() + "\\" + p_drRunnerDB["Name"].ToString() + ".xml");
        if (!fileInfo.Directory.Exists)
          fileInfo.Directory.Create();
        using (XmlWriter xmlWriter = XmlWriter.Create(fileInfo.FullName, new XmlWriterSettings()
        {
          Indent = true,
          OmitXmlDeclaration = true,
          NewLineOnAttributes = true
        }))
        {
          xmlWriter.WriteStartDocument();
          xmlWriter.WriteStartElement("DB");
          xmlWriter.WriteEndElement();
          xmlWriter.Flush();
        }
        using (XmlReader reader = XmlReader.Create(fileInfo.FullName))
        {
          XmlDocument xmlDocument = new XmlDocument();
          xmlDocument.Load(reader);
          XmlElement documentElement = xmlDocument.DocumentElement;
          documentElement.RemoveAll();
          XmlNode element1 = (XmlNode) xmlDocument.CreateElement("Pin");
          XmlNode element2 = (XmlNode) xmlDocument.CreateElement("SysType");
          element2.InnerText = p_drRunnerDB["Pin_System"].ToString();
          element1.AppendChild(element2);
          for (int index = 0; index < 4; ++index)
          {
            XmlNode element3 = (XmlNode) xmlDocument.CreateElement("Group" + (object) (index + 1));
            XmlNode element4 = (XmlNode) xmlDocument.CreateElement("Gate");
            XmlAttribute attribute1 = xmlDocument.CreateAttribute("Dim1");
            attribute1.Value = p_drRunnerDB["Pin_GateDim1_G" + (object) (index + 1)].ToString();
            element4.Attributes.Append(attribute1);
            XmlAttribute attribute2 = xmlDocument.CreateAttribute("Dim2");
            attribute2.Value = p_drRunnerDB["Pin_GateDim2_G" + (object) (index + 1)].ToString();
            element4.Attributes.Append(attribute2);
            element3.AppendChild(element4);
            XmlNode element5 = (XmlNode) xmlDocument.CreateElement("Runner");
            XmlAttribute attribute3 = xmlDocument.CreateAttribute("Length");
            attribute3.Value = p_drRunnerDB["Pin_RunnerLength_G" + (object) (index + 1)].ToString();
            element5.Attributes.Append(attribute3);
            XmlAttribute attribute4 = xmlDocument.CreateAttribute("Dim1");
            attribute4.Value = p_drRunnerDB["Pin_RunnerDim1_G" + (object) (index + 1)].ToString();
            element5.Attributes.Append(attribute4);
            XmlAttribute attribute5 = xmlDocument.CreateAttribute("Dim2");
            attribute5.Value = p_drRunnerDB["Pin_RunnerDim2_G" + (object) (index + 1)].ToString();
            element5.Attributes.Append(attribute5);
            element3.AppendChild(element5);
            element1.AppendChild(element3);
          }
          documentElement.AppendChild(element1);
          XmlNode element6 = (XmlNode) xmlDocument.CreateElement("Side");
          for (int index = 0; index < 4; ++index)
          {
            XmlNode element7 = (XmlNode) xmlDocument.CreateElement("Group" + (object) (index + 1));
            XmlNode element8 = (XmlNode) xmlDocument.CreateElement("Gate");
            XmlAttribute attribute6 = xmlDocument.CreateAttribute("Type1");
            attribute6.Value = p_drRunnerDB["Side_GateType1_G" + (object) (index + 1)].ToString();
            element8.Attributes.Append(attribute6);
            XmlAttribute attribute7 = xmlDocument.CreateAttribute("Type2");
            attribute7.Value = p_drRunnerDB["Side_GateType2_G" + (object) (index + 1)].ToString();
            element8.Attributes.Append(attribute7);
            XmlAttribute attribute8 = xmlDocument.CreateAttribute("Length");
            attribute8.Value = p_drRunnerDB["Side_GateLength_G" + (object) (index + 1)].ToString();
            element8.Attributes.Append(attribute8);
            XmlAttribute attribute9 = xmlDocument.CreateAttribute("Dim1");
            attribute9.Value = p_drRunnerDB["Side_GateDim1_G" + (object) (index + 1)].ToString();
            element8.Attributes.Append(attribute9);
            XmlAttribute attribute10 = xmlDocument.CreateAttribute("Dim2");
            attribute10.Value = p_drRunnerDB["Side_GateDim2_G" + (object) (index + 1)].ToString();
            element8.Attributes.Append(attribute10);
            XmlAttribute attribute11 = xmlDocument.CreateAttribute("Dim3");
            attribute11.Value = p_drRunnerDB["Side_GateDim3_G" + (object) (index + 1)].ToString();
            element8.Attributes.Append(attribute11);
            XmlAttribute attribute12 = xmlDocument.CreateAttribute("Dim4");
            attribute12.Value = p_drRunnerDB["Side_GateDim4_G" + (object) (index + 1)].ToString();
            element8.Attributes.Append(attribute12);
            element7.AppendChild(element8);
            XmlNode element9 = (XmlNode) xmlDocument.CreateElement("Runner");
            XmlAttribute attribute13 = xmlDocument.CreateAttribute("Type");
            attribute13.Value = p_drRunnerDB["Side_RunnerType_G" + (object) (index + 1)].ToString();
            element9.Attributes.Append(attribute13);
            XmlAttribute attribute14 = xmlDocument.CreateAttribute("Length");
            attribute14.Value = p_drRunnerDB["Side_RunnerLength_G" + (object) (index + 1)].ToString();
            element9.Attributes.Append(attribute14);
            XmlAttribute attribute15 = xmlDocument.CreateAttribute("Dim1");
            attribute15.Value = p_drRunnerDB["Side_RunnerDim1_G" + (object) (index + 1)].ToString();
            element9.Attributes.Append(attribute15);
            XmlAttribute attribute16 = xmlDocument.CreateAttribute("Dim2");
            attribute16.Value = p_drRunnerDB["Side_RunnerDim2_G" + (object) (index + 1)].ToString();
            element9.Attributes.Append(attribute16);
            XmlAttribute attribute17 = xmlDocument.CreateAttribute("Dim3");
            attribute17.Value = p_drRunnerDB["Side_RunnerDim3_G" + (object) (index + 1)].ToString();
            element9.Attributes.Append(attribute17);
            XmlAttribute attribute18 = xmlDocument.CreateAttribute("Dim4");
            attribute18.Value = p_drRunnerDB["Side_RunnerDim4_G" + (object) (index + 1)].ToString();
            element9.Attributes.Append(attribute18);
            element7.AppendChild(element9);
            element6.AppendChild(element7);
          }
          documentElement.AppendChild(element6);
          XmlNode element10 = (XmlNode) xmlDocument.CreateElement("OccurenceNumber");
          element10.InnerText = p_drRunnerDB["OccNumber"].ToString();
          documentElement.AppendChild(element10);
          XmlNode element11 = (XmlNode) xmlDocument.CreateElement("Horizon");
          XmlAttribute attribute19 = xmlDocument.CreateAttribute("Stage");
          attribute19.Value = p_drRunnerDB["Hor_Stage"].ToString();
          element11.Attributes.Append(attribute19);
          XmlNode element12 = (XmlNode) xmlDocument.CreateElement("Two");
          XmlAttribute attribute20 = xmlDocument.CreateAttribute("Type");
          attribute20.Value = p_drRunnerDB["Hor_Two_Type"].ToString();
          element12.Attributes.Append(attribute20);
          XmlAttribute attribute21 = xmlDocument.CreateAttribute("Dia");
          attribute21.Value = p_drRunnerDB["Hor_Two_Dia"].ToString();
          element12.Attributes.Append(attribute21);
          XmlAttribute attribute22 = xmlDocument.CreateAttribute("Length");
          attribute22.Value = p_drRunnerDB["Hor_Two_Length"].ToString();
          element12.Attributes.Append(attribute22);
          XmlAttribute attribute23 = xmlDocument.CreateAttribute("Dir");
          attribute23.Value = p_drRunnerDB["Hor_Two_Dir"].ToString();
          element12.Attributes.Append(attribute23);
          XmlAttribute attribute24 = xmlDocument.CreateAttribute("Dim1");
          attribute24.Value = p_drRunnerDB["Hor_Two_Dim1"].ToString();
          element12.Attributes.Append(attribute24);
          XmlAttribute attribute25 = xmlDocument.CreateAttribute("Dim2");
          attribute25.Value = p_drRunnerDB["Hor_Two_Dim2"].ToString();
          element12.Attributes.Append(attribute25);
          XmlAttribute attribute26 = xmlDocument.CreateAttribute("Angle");
          attribute26.Value = p_drRunnerDB["Hor_Two_Angle"].ToString();
          element12.Attributes.Append(attribute26);
          XmlAttribute attribute27 = xmlDocument.CreateAttribute("CenterTol");
          attribute27.Value = p_drRunnerDB["Hor_Two_CenterTol"].ToString();
          element12.Attributes.Append(attribute27);
          XmlAttribute attribute28 = xmlDocument.CreateAttribute("Inter");
          attribute28.Value = p_drRunnerDB["Hor_Two_Inter"].ToString();
          element12.Attributes.Append(attribute28);
          element11.AppendChild(element12);
          XmlNode element13 = (XmlNode) xmlDocument.CreateElement("Three");
          XmlAttribute attribute29 = xmlDocument.CreateAttribute("Quadrant");
          attribute29.Value = p_drRunnerDB["Hor_Three_Quadrant"].ToString();
          element13.Attributes.Append(attribute29);
          XmlAttribute attribute30 = xmlDocument.CreateAttribute("Type");
          attribute30.Value = p_drRunnerDB["Hor_Three_Type"].ToString();
          element13.Attributes.Append(attribute30);
          XmlAttribute attribute31 = xmlDocument.CreateAttribute("Length");
          attribute31.Value = p_drRunnerDB["Hor_Three_Length"].ToString();
          element13.Attributes.Append(attribute31);
          XmlAttribute attribute32 = xmlDocument.CreateAttribute("Dia");
          attribute32.Value = p_drRunnerDB["Hor_Three_Dia"].ToString();
          element13.Attributes.Append(attribute32);
          XmlAttribute attribute33 = xmlDocument.CreateAttribute("CenterTol");
          attribute33.Value = p_drRunnerDB["Hor_Three_CenterTol"].ToString();
          element13.Attributes.Append(attribute33);
          element11.AppendChild(element13);
          documentElement.AppendChild(element11);
          XmlNode element14 = (XmlNode) xmlDocument.CreateElement("Sprue");
          XmlAttribute attribute34 = xmlDocument.CreateAttribute("Type");
          attribute34.Value = p_drRunnerDB["Sprue_Type"].ToString();
          element14.Attributes.Append(attribute34);
          XmlAttribute attribute35 = xmlDocument.CreateAttribute("Length");
          attribute35.Value = p_drRunnerDB["Sprue_Length"].ToString();
          element14.Attributes.Append(attribute35);
          XmlAttribute attribute36 = xmlDocument.CreateAttribute("Dim1");
          attribute36.Value = p_drRunnerDB["Sprue_Dim1"].ToString();
          element14.Attributes.Append(attribute36);
          XmlAttribute attribute37 = xmlDocument.CreateAttribute("Dim2");
          attribute37.Value = p_drRunnerDB["Sprue_Dim2"].ToString();
          element14.Attributes.Append(attribute37);
          documentElement.AppendChild(element14);
          reader.Close();
          xmlDocument.Save(fileInfo.FullName);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]CreateDBFile):" + ex.Message));
      }
    }

    private void checkBox_Pin_ValveCheck_CheckedChanged(object sender, EventArgs e)
    {
      DataGridViewSelectedRowCollection currentValveRows = this.GetCurrentValveRows();
      if (currentValveRows == null)
        return;
      try
      {
        foreach (DataGridViewRow dataGridViewRow in (BaseCollection) currentValveRows)
          dataGridViewRow.Cells["Column_VavCheck"].Value = (object) this.checkBox_ValveCheck.Checked;
        if (!this.checkBox_ValveCheck.Checked)
        {
          this.panel_Valve_Sub.Enabled = false;
        }
        else
        {
          this.panel_Valve_Sub.Enabled = true;
          string[] array1 = currentValveRows.Cast<DataGridViewRow>().Select<DataGridViewRow, string>((System.Func<DataGridViewRow, string>) (Temp => Temp.Cells["Column_VavTrigger"].Value.ToString())).Distinct<string>().ToArray<string>();
          if (array1.Length < 2)
            this.newComboBox_VavTrigger.SelectedIndex = this.newComboBox_VavTrigger.Items.IndexOf((object) array1[0]);
          string[] array2 = currentValveRows.Cast<DataGridViewRow>().Select<DataGridViewRow, string>((System.Func<DataGridViewRow, string>) (Temp => Temp.Cells["Column_VavState"].Value.ToString())).Distinct<string>().ToArray<string>();
          if (array2.Length < 2)
            this.newComboBox_VavState.SelectedIndex = this.newComboBox_VavState.Items.IndexOf((object) array2[0]);
          string[] array3 = currentValveRows.Cast<DataGridViewRow>().Select<DataGridViewRow, string>((System.Func<DataGridViewRow, string>) (Temp => Temp.Cells["Column_VavLoc"].Value.ToString())).Distinct<string>().ToArray<string>();
          if (!this.newTextBox_VavNode.ReadOnly && array3.Length < 2)
            this.newTextBox_VavNode.Value = array3[0];
          string[] array4 = currentValveRows.Cast<DataGridViewRow>().Select<DataGridViewRow, string>((System.Func<DataGridViewRow, string>) (Temp => Temp.Cells["Column_VavOpen1"].Value.ToString())).Distinct<string>().ToArray<string>();
          if (array4.Length < 2)
            this.newTextBox_VavOpen1.Value = array4[0];
          string[] array5 = currentValveRows.Cast<DataGridViewRow>().Select<DataGridViewRow, string>((System.Func<DataGridViewRow, string>) (Temp => Temp.Cells["Column_VavClose1"].Value.ToString())).Distinct<string>().ToArray<string>();
          if (array5.Length < 2)
            this.newTextBox_VavClose1.Value = array5[0];
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]checkBox_Pin_ValveCheck_CheckedChanged):" + ex.Message));
      }
    }

    private DataGridViewRow GetCurrentValveRow()
    {
      DataGridViewRow currentValveRow = (DataGridViewRow) null;
      try
      {
        if (this.dataGridView_Node.Rows.Count > 0 && this.dataGridView_Node.SelectedRows.Count > 0 && this.dataGridView_Node.CurrentRow != null)
          currentValveRow = this.dataGridView_Node.Rows[this.dataGridView_Node.CurrentRow.Index];
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]GetCurrentValveCell):" + ex.Message));
      }
      return currentValveRow;
    }

    private DataGridViewSelectedRowCollection GetCurrentValveRows()
    {
      DataGridViewSelectedRowCollection currentValveRows = (DataGridViewSelectedRowCollection) null;
      try
      {
        if (this.dataGridView_Node.Rows.Count > 0 && this.dataGridView_Node.SelectedRows.Count > 0 && this.dataGridView_Node.CurrentRow != null)
          currentValveRows = this.dataGridView_Node.SelectedRows;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]GetCurrentValveRows):" + ex.Message));
      }
      return currentValveRows;
    }

    private void dataGridView_Node_CurrentCellChanged(object sender, EventArgs e)
    {
      DataGridViewSelectedRowCollection p_dgvrsrValve = (DataGridViewSelectedRowCollection) null;
      if (this.dataGridView_Node.Rows.Count > 0 && this.dataGridView_Node.SelectedCells.Count > 0 && this.dataGridView_Node.CurrentRow != null)
        p_dgvrsrValve = this.dataGridView_Node.SelectedRows;
      if (p_dgvrsrValve == null)
        this.panel_Valve.Enabled = false;
      else
        this.SetValveDataforSelectedRows(p_dgvrsrValve);
    }

    private void SetValveDataforSelectedRows(DataGridViewSelectedRowCollection p_dgvrsrValve)
    {
      try
      {
        if (this.radioButton_Pin_ColdSystem.Checked || !this.radioButton_Pin_HotSystem.Checked)
          this.panel_Valve.Enabled = false;
        else
          this.panel_Valve.Enabled = true;
        this.newComboBox_VavTrigger.SelectedIndex = -1;
        this.newComboBox_VavState.SelectedIndex = -1;
        this.newTextBox_VavNode.Value = "";
        this.newTextBox_VavOpen1.Value = "";
        this.newTextBox_VavClose1.Value = "";
        this.newTextBox_VavDelayTime.Value = "";
        bool[] array1 = p_dgvrsrValve.Cast<DataGridViewRow>().Select<DataGridViewRow, bool>((System.Func<DataGridViewRow, bool>) (Temp => clsUtill.ConvertToBoolean(Temp.Cells["Column_VavCheck"].Value.ToString()))).Distinct<bool>().ToArray<bool>();
        this.checkBox_ValveCheck.Checked = array1.Length <= 1 && array1[0];
        if (this.checkBox_ValveCheck.Checked)
          this.panel_Valve_Sub.Enabled = true;
        else
          this.panel_Valve_Sub.Enabled = false;
        string[] array2 = p_dgvrsrValve.Cast<DataGridViewRow>().Select<DataGridViewRow, string>((System.Func<DataGridViewRow, string>) (Temp => Temp.Cells["Column_VavTrigger"].Value.ToString())).Distinct<string>().ToArray<string>();
        if (array2 == null)
          return;
        if (array2.Length < 2)
          this.newComboBox_VavTrigger.SelectedIndex = this.newComboBox_VavTrigger.Items.IndexOf((object) array2[0]);
        string[] array3 = p_dgvrsrValve.Cast<DataGridViewRow>().Select<DataGridViewRow, string>((System.Func<DataGridViewRow, string>) (Temp => Temp.Cells["Column_VavState"].Value.ToString())).Distinct<string>().ToArray<string>();
        if (array3.Length < 2)
          this.newComboBox_VavState.SelectedIndex = this.newComboBox_VavState.Items.IndexOf((object) array3[0]);
        string[] array4 = p_dgvrsrValve.Cast<DataGridViewRow>().Select<DataGridViewRow, string>((System.Func<DataGridViewRow, string>) (Temp => Temp.Cells["Column_VavLoc"].Value.ToString())).Distinct<string>().ToArray<string>();
        if (!this.newTextBox_VavNode.ReadOnly && array4.Length < 2)
          this.newTextBox_VavNode.Value = array4[0];
        string[] array5 = p_dgvrsrValve.Cast<DataGridViewRow>().Select<DataGridViewRow, string>((System.Func<DataGridViewRow, string>) (Temp => Temp.Cells["Column_VavOpen1"].Value.ToString())).Distinct<string>().ToArray<string>();
        if (array5.Length < 2)
          this.newTextBox_VavOpen1.Value = array5[0];
        string[] array6 = p_dgvrsrValve.Cast<DataGridViewRow>().Select<DataGridViewRow, string>((System.Func<DataGridViewRow, string>) (Temp => Temp.Cells["Column_VavClose1"].Value.ToString())).Distinct<string>().ToArray<string>();
        if (array6.Length < 2)
          this.newTextBox_VavClose1.Value = array6[0];
        string[] array7 = p_dgvrsrValve.Cast<DataGridViewRow>().Select<DataGridViewRow, string>((System.Func<DataGridViewRow, string>) (Temp => Temp.Cells["Column_VavDelay"].Value.ToString())).Distinct<string>().ToArray<string>();
        if (array7.Length < 2)
          this.newTextBox_VavDelayTime.Value = array7[0];
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]SetValveDataforSelectedRows):" + ex.Message));
      }
    }

    private void radioButton_Hor_Type1_CheckedChanged(object sender, EventArgs e)
    {
      this.label_Hor_Two_CenterTol.Text = "Center " + LocaleControl.getInstance().GetString("IDS_TOLERANCE");
      this.label_Hor_Two_Dim1.Text = "";
      this.label_Hor_Two_Dim2.Text = "";
      this.label_Hor_Two_Angle.Text = "";
      this.label_Hor_Two_CenterTol.BackColor = Color.Lavender;
      this.label_Hor_Two_Dim1.BackColor = Color.LightGray;
      this.label_Hor_Two_Dim2.BackColor = Color.LightGray;
      this.label_Hor_Two_Angle.BackColor = Color.LightGray;
      this.newTextBox_Hor_Two_CenterTol.TextBoxBackColor = Color.White;
      this.newTextBox_Hor_Two_CenterTol.TextForeColor = Color.Black;
      this.newTextBox_Hor_Two_CenterTol.ReadOnly = false;
      this.newTextBox_Hor_Two_Dim1.TextBoxBackColor = Color.LightGray;
      this.newTextBox_Hor_Two_Dim1.TextForeColor = Color.LightGray;
      this.newTextBox_Hor_Two_Dim1.ReadOnly = true;
      this.newTextBox_Hor_Two_Dim2.TextBoxBackColor = Color.LightGray;
      this.newTextBox_Hor_Two_Dim2.TextForeColor = Color.LightGray;
      this.newTextBox_Hor_Two_Dim2.ReadOnly = true;
      this.newTextBox_Hor_Two_Angle.TextBoxBackColor = Color.LightGray;
      this.newTextBox_Hor_Two_Angle.TextForeColor = Color.LightGray;
      this.newTextBox_Hor_Two_Angle.ReadOnly = true;
      this.checkBox_Hor_Two_Inter.SendToBack();
      this.checkBox_Hor_Two_Inter.Visible = false;
    }

    private void radioButton_Hor_Type2_CheckedChanged(object sender, EventArgs e)
    {
      this.label_Hor_Two_CenterTol.Text = "Center " + LocaleControl.getInstance().GetString("IDS_TOLERANCE");
      this.label_Hor_Two_Dim1.Text = LocaleControl.getInstance().GetString("IDS_HORIZON_RUNNER_INTERFERENCE");
      this.label_Hor_Two_Dim2.Text = "";
      this.label_Hor_Two_Angle.Text = "";
      this.label_Hor_Two_CenterTol.BackColor = Color.Lavender;
      this.label_Hor_Two_Dim1.BackColor = Color.Lavender;
      this.label_Hor_Two_Dim2.BackColor = Color.LightGray;
      this.label_Hor_Two_Angle.BackColor = Color.LightGray;
      this.newTextBox_Hor_Two_CenterTol.TextBoxBackColor = Color.White;
      this.newTextBox_Hor_Two_CenterTol.TextForeColor = Color.Black;
      this.newTextBox_Hor_Two_CenterTol.ReadOnly = false;
      this.newTextBox_Hor_Two_Dim1.TextBoxBackColor = Color.White;
      this.newTextBox_Hor_Two_Dim1.TextForeColor = Color.LightGray;
      this.newTextBox_Hor_Two_Dim1.ReadOnly = true;
      this.newTextBox_Hor_Two_Dim2.TextBoxBackColor = Color.LightGray;
      this.newTextBox_Hor_Two_Dim2.TextForeColor = Color.LightGray;
      this.newTextBox_Hor_Two_Dim2.ReadOnly = true;
      this.newTextBox_Hor_Two_Angle.TextBoxBackColor = Color.LightGray;
      this.newTextBox_Hor_Two_Angle.TextForeColor = Color.LightGray;
      this.newTextBox_Hor_Two_Angle.ReadOnly = true;
      this.checkBox_Hor_Two_Inter.BringToFront();
      this.checkBox_Hor_Two_Inter.Visible = true;
      this.checkBox_Hor_Two_Inter.Checked = true;
    }

    private void radioButton_Hor_Type3_CheckedChanged(object sender, EventArgs e)
    {
      this.label_Hor_Two_CenterTol.Text = "Center " + LocaleControl.getInstance().GetString("IDS_TOLERANCE");
      this.label_Hor_Two_Dim1.Text = "[ X ] " + LocaleControl.getInstance().GetString("IDS_HORIZON_LENGTH");
      this.label_Hor_Two_Dim2.Text = "[ Y ] " + LocaleControl.getInstance().GetString("IDS_HORIZON_LENGTH");
      this.label_Hor_Two_Angle.Text = "";
      this.label_Hor_Two_CenterTol.BackColor = Color.Lavender;
      this.label_Hor_Two_Dim1.BackColor = Color.Lavender;
      this.label_Hor_Two_Dim2.BackColor = Color.Lavender;
      this.label_Hor_Two_Angle.BackColor = Color.LightGray;
      this.newTextBox_Hor_Two_CenterTol.TextBoxBackColor = Color.White;
      this.newTextBox_Hor_Two_CenterTol.TextForeColor = Color.Black;
      this.newTextBox_Hor_Two_CenterTol.ReadOnly = false;
      this.newTextBox_Hor_Two_Dim1.TextBoxBackColor = Color.White;
      this.newTextBox_Hor_Two_Dim1.TextForeColor = Color.Black;
      this.newTextBox_Hor_Two_Dim1.ReadOnly = false;
      this.newTextBox_Hor_Two_Dim2.TextBoxBackColor = Color.White;
      this.newTextBox_Hor_Two_Dim2.TextForeColor = Color.Black;
      this.newTextBox_Hor_Two_Dim2.ReadOnly = false;
      this.newTextBox_Hor_Two_Angle.TextBoxBackColor = Color.LightGray;
      this.newTextBox_Hor_Two_Angle.TextForeColor = Color.LightGray;
      this.newTextBox_Hor_Two_Angle.ReadOnly = true;
      this.checkBox_Hor_Two_Inter.SendToBack();
      this.checkBox_Hor_Two_Inter.Visible = false;
    }

    private void radioButton_Hor_Type4_CheckedChanged(object sender, EventArgs e)
    {
      this.label_Hor_Two_CenterTol.Text = "";
      this.label_Hor_Two_Dim1.Text = "[／] " + LocaleControl.getInstance().GetString("IDS_HORIZON_LENGTH");
      this.label_Hor_Two_Dim2.Text = "[＼] " + LocaleControl.getInstance().GetString("IDS_HORIZON_LENGTH");
      this.label_Hor_Two_Angle.Text = "∠ " + LocaleControl.getInstance().GetString("IDS_HORIZON_ANGLE");
      this.label_Hor_Two_CenterTol.BackColor = Color.LightGray;
      this.label_Hor_Two_Dim1.BackColor = Color.Lavender;
      this.label_Hor_Two_Dim2.BackColor = Color.Lavender;
      this.label_Hor_Two_Angle.BackColor = Color.Lavender;
      this.newTextBox_Hor_Two_CenterTol.TextBoxBackColor = Color.LightGray;
      this.newTextBox_Hor_Two_CenterTol.TextForeColor = Color.LightGray;
      this.newTextBox_Hor_Two_CenterTol.ReadOnly = true;
      this.newTextBox_Hor_Two_Dim1.TextBoxBackColor = Color.White;
      this.newTextBox_Hor_Two_Dim1.TextForeColor = Color.Black;
      this.newTextBox_Hor_Two_Dim1.ReadOnly = false;
      this.newTextBox_Hor_Two_Dim2.TextBoxBackColor = Color.White;
      this.newTextBox_Hor_Two_Dim2.TextForeColor = Color.Black;
      this.newTextBox_Hor_Two_Dim2.ReadOnly = false;
      this.newTextBox_Hor_Two_Angle.TextBoxBackColor = Color.White;
      this.newTextBox_Hor_Two_Angle.TextForeColor = Color.Black;
      this.newTextBox_Hor_Two_Angle.ReadOnly = false;
      this.checkBox_Hor_Two_Inter.SendToBack();
      this.checkBox_Hor_Two_Inter.Visible = false;
    }

    private void dataGridView_Node_MouseUp(object sender, MouseEventArgs e)
    {
      DataGridView dataGridView = sender as DataGridView;
      if (dataGridView.SelectedCells.Count == 0 || e.Button != MouseButtons.Right)
        return;
      ContextMenu contextMenu = new ContextMenu();
      for (int index = 0; index < 3; ++index)
      {
        MenuItem menuItem = new MenuItem();
        menuItem.Text = index != 0 ? (index != 1 ? "YZ" + LocaleControl.getInstance().GetString("IDS_PLANE_SYMMETRIC_TRANSPOSITION") : "XZ" + LocaleControl.getInstance().GetString("IDS_PLANE_SYMMETRIC_TRANSPOSITION")) : "XY" + LocaleControl.getInstance().GetString("IDS_PLANE_SYMMETRIC_TRANSPOSITION");
        menuItem.Click += new EventHandler(this.miPlane_Click);
        contextMenu.MenuItems.Add(menuItem);
      }
      contextMenu.Show((Control) dataGridView, e.Location);
    }

    private void miPlane_Click(object sender, EventArgs e)
    {
      List<DataGridViewRow> dataGridViewRowList = new List<DataGridViewRow>();
      try
      {
        MenuItem menuItem = sender as MenuItem;
        DataGridView sourceControl = (menuItem.Parent as ContextMenu).SourceControl as DataGridView;
        List<DataGridViewRow> list = sourceControl.SelectedCells.Cast<DataGridViewCell>().Select<DataGridViewCell, DataGridViewRow>((System.Func<DataGridViewCell, DataGridViewRow>) (Temp => Temp.OwningRow)).OrderByDescending<DataGridViewRow, int>((System.Func<DataGridViewRow, int>) (Temp => Temp.Index)).Distinct<DataGridViewRow>().ToList<DataGridViewRow>();
        double[] pointFromStudyNote = clsHDMFLib.GetCenterPointFromStudyNote();
        if (pointFromStudyNote == null)
          return;
        foreach (DataGridViewRow dataGridViewRow1 in list)
        {
          double num1 = clsUtill.ConvertToDouble(dataGridViewRow1.Cells[2].Value.ToString());
          double num2 = clsUtill.ConvertToDouble(dataGridViewRow1.Cells[3].Value.ToString());
          double num3 = clsUtill.ConvertToDouble(dataGridViewRow1.Cells[4].Value.ToString());
          if (menuItem.Text.Contains("XY"))
            num3 = pointFromStudyNote[2] * 2.0 - num3;
          else if (menuItem.Text.Contains("XZ"))
            num2 = pointFromStudyNote[1] * 2.0 - num2;
          else
            num1 = pointFromStudyNote[0] * 2.0 - num1;
          DataGridViewRow dataGridViewRow2 = (DataGridViewRow) dataGridViewRow1.Clone();
          for (int index = 0; index < dataGridViewRow1.Cells.Count; ++index)
            dataGridViewRow2.Cells[index].Value = dataGridViewRow1.Cells[index].EditedFormattedValue;
          DataGridViewRow row = sourceControl.Rows[sourceControl.Rows.Add(dataGridViewRow2)];
          row.Cells[2].Value = (object) num1;
          row.Cells[3].Value = (object) num2;
          row.Cells[4].Value = (object) num3;
          if (row.Cells[0].Value.ToString().Equals("Side"))
          {
            string str = row.Cells[5].Value.ToString();
            if (menuItem.Text.Contains("XZ"))
            {
              switch (str)
              {
                case "+Y":
                  str = "-Y";
                  break;
                case "-Y":
                  str = "+Y";
                  break;
              }
            }
            else if (menuItem.Text.Contains("YZ"))
            {
              switch (str)
              {
                case "+X":
                  str = "-X";
                  break;
                case "-X":
                  str = "+X";
                  break;
              }
            }
            row.Cells[5].Value = (object) str;
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]miPlane_Click):" + ex.Message));
      }
    }

    private void dataGridView_Node_MouseMove(object sender, MouseEventArgs e)
    {
      if ((e.Button & MouseButtons.Left) != MouseButtons.Left)
        return;
      DataGridView dataGridView = sender as DataGridView;
      DataGridView.HitTestInfo hitTestInfo = dataGridView.HitTest(e.X, e.Y);
      if (hitTestInfo.RowIndex >= 0 && hitTestInfo.Type == DataGridViewHitTestType.RowHeader)
        dataGridView.DoDragDrop((object) dataGridView.SelectedRows, DragDropEffects.Move);
    }

    private void dataGridView_Node_DragOver(object sender, DragEventArgs e)
    {
      if (!(sender is DataGridView dataGridView) || dataGridView.SelectedRows.Count <= 0)
        return;
      e.Effect = DragDropEffects.Move;
    }

    private void dataGridView_Node_DragDrop(object sender, DragEventArgs e)
    {
      try
      {
        if (!(sender is DataGridView dataGridView) || e.Effect != DragDropEffects.Move || dataGridView.SelectedRows.Count <= 0)
          return;
        Point client = dataGridView.PointToClient(new Point(e.X, e.Y));
        int rowIndex = dataGridView.HitTest(client.X, client.Y).RowIndex;
        int index1 = dataGridView.SelectedRows.Cast<DataGridViewRow>().Last<DataGridViewRow>().Index;
        if (rowIndex < 0)
          return;
        if (rowIndex >= index1)
          ++rowIndex;
        DataGridViewRow[] array = dataGridView.SelectedRows.Cast<DataGridViewRow>().OrderBy<DataGridViewRow, int>((System.Func<DataGridViewRow, int>) (Temp => Temp.Index)).ToArray<DataGridViewRow>();
        List<DataGridViewRow> dataGridViewRowList = new List<DataGridViewRow>();
        for (int index2 = 0; index2 < dataGridView.Rows.Count; ++index2)
        {
          if (index2 == rowIndex)
            dataGridViewRowList.AddRange((IEnumerable<DataGridViewRow>) array);
          if (!((IEnumerable<DataGridViewRow>) array).Contains<DataGridViewRow>(dataGridView.Rows[index2]))
            dataGridViewRowList.Add(dataGridView.Rows[index2]);
        }
        if (!dataGridViewRowList.Contains(array[0]))
          dataGridViewRowList.AddRange((IEnumerable<DataGridViewRow>) array);
        dataGridView.Rows.Clear();
        dataGridView.Rows.AddRange(dataGridViewRowList.ToArray());
        dataGridView.ClearSelection();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]dataGridView_Node_DragDrop):" + ex.Message));
      }
    }

    private void dataGridView_Node_CellMouseDoubleClick(
      object sender,
      DataGridViewCellMouseEventArgs e)
    {
      DataGridView dataGridView = sender as DataGridView;
      if (e.Button != MouseButtons.Left)
        return;
      dataGridView.BeginEdit(true);
    }

    private void dataGridView_Node_CellValueChanged(object sender, DataGridViewCellEventArgs e)
    {
      try
      {
        if (!(this.dataGridView_Node.Columns[e.ColumnIndex].Name == "Column_Node_Type") || e.RowIndex < 0)
          return;
        DataGridViewComboBoxCell cell = (DataGridViewComboBoxCell) this.dataGridView_Node.Rows[e.RowIndex].Cells["Column_Direction"];
        cell.Value = (object) null;
        cell.Items.Clear();
        if (this.dataGridView_Node.Rows[e.RowIndex].Cells["Column_Node_Type"].Value.ToString() == "Pin")
        {
          this.dataGridView_Node.Rows[e.RowIndex].Cells["Column_Direction"].ReadOnly = true;
        }
        else
        {
          this.dataGridView_Node.Rows[e.RowIndex].Cells["Column_Direction"].ReadOnly = false;
          cell.Items.AddRange((object) "+X", (object) "-X", (object) "+Y", (object) "-Y");
          cell.Value = (object) "+X";
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]dataGridView_Node_CellValueChanged):" + ex.Message));
      }
    }

    private void dataGridView_Node_CurrentCellDirtyStateChanged(object sender, EventArgs e)
    {
      if (!this.dataGridView_Node.IsCurrentCellDirty)
        return;
      this.dataGridView_Node.CommitEdit(DataGridViewDataErrorContexts.Commit);
    }

    private void radioButton_Hor_Three_Type1_CheckedChanged(object sender, EventArgs e)
    {
      try
      {
        this.label_Hor_Three_CenterTol.Text = "Center " + LocaleControl.getInstance().GetString("IDS_TOLERANCE");
        this.label_Hor_Three_CenterTol.BackColor = Color.Lavender;
        this.newTextBox_Hor_Three_CenterTol.TextBoxBackColor = Color.White;
        this.newTextBox_Hor_Three_CenterTol.TextForeColor = Color.Black;
        this.newTextBox_Hor_Three_CenterTol.ReadOnly = false;
        if (!(this.newTextBox_Hor_Three_CenterTol.Value == string.Empty))
          return;
        this.newTextBox_Hor_Three_CenterTol.Value = "0";
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]radioButton_Hor_Three_Type1_CheckedChanged):" + ex.Message));
      }
    }

    private void radioButton_Hor_Three_Type2_CheckedChanged(object sender, EventArgs e)
    {
      try
      {
        this.label_Hor_Three_CenterTol.Text = "";
        this.label_Hor_Three_CenterTol.BackColor = Color.LightGray;
        this.newTextBox_Hor_Three_CenterTol.TextBoxBackColor = Color.LightGray;
        this.newTextBox_Hor_Three_CenterTol.TextForeColor = Color.LightGray;
        this.newTextBox_Hor_Three_CenterTol.ReadOnly = true;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]radioButton_Hor_Three_Type2_CheckedChanged):" + ex.Message));
      }
    }

    private void radioButton_Quadrant2_CheckedChanged(object sender, EventArgs e)
    {
      try
      {
        this.panel_Hor_Runner_ThreeType.Enabled = false;
        this.radioButton_Hor_Three_Type1.Checked = true;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]radioButton_Quadrant2_CheckedChanged):" + ex.Message));
      }
    }

    private void radioButton_Quadrant4_CheckedChanged(object sender, EventArgs e)
    {
      try
      {
        this.panel_Hor_Runner_ThreeType.Enabled = true;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]radioButton_Quadrant4_CheckedChanged):" + ex.Message));
      }
    }

    private void dataGridView_Node_RowPostPaint(object sender, DataGridViewRowPostPaintEventArgs e)
    {
      DataGridView dataGridView = sender as DataGridView;
      string s = (e.RowIndex + 1).ToString();
      StringFormat format = new StringFormat()
      {
        Alignment = StringAlignment.Center,
        LineAlignment = StringAlignment.Center
      };
      Rectangle layoutRectangle;
      ref Rectangle local = ref layoutRectangle;
      Rectangle rowBounds = e.RowBounds;
      int left = rowBounds.Left;
      rowBounds = e.RowBounds;
      int top = rowBounds.Top;
      int rowHeadersWidth = dataGridView.RowHeadersWidth;
      rowBounds = e.RowBounds;
      int height = rowBounds.Height;
      local = new Rectangle(left, top, rowHeadersWidth, height);
      e.Graphics.DrawString(s, this.Font, SystemBrushes.ControlText, (RectangleF) layoutRectangle, format);
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.components = (IContainer) new System.ComponentModel.Container();
      DataGridViewCellStyle gridViewCellStyle1 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle2 = new DataGridViewCellStyle();
      this.label_Hor_Two_Length = new Label();
      this.label_Sprue_Dim2 = new Label();
      this.label_Sprue_Length = new Label();
      this.label_Cavity_Occ = new Label();
      this.label_DB_Item = new Label();
      this.label_DB_Company = new Label();
      this.label_Hor_Two_Dia = new Label();
      this.label_Sprue_Dim1 = new Label();
      this.panel_Hor_Runner_TwoType = new Panel();
      this.radioButton_Hor_Two_Type4 = new RadioButton();
      this.radioButton_Hor_Two_Type3 = new RadioButton();
      this.radioButton_Hor_Two_Type2 = new RadioButton();
      this.radioButton_Hor_Two_Type1 = new RadioButton();
      this.panel_Sprue_Type = new Panel();
      this.radioButton_Sprue_Hot = new RadioButton();
      this.radioButton_Sprue_Cold = new RadioButton();
      this.panel_Pin_Type = new Panel();
      this.radioButton_Pin_HotSystem = new RadioButton();
      this.radioButton_Pin_ColdSystem = new RadioButton();
      this.listBox_DB = new ListBox();
      this.label_Sprue = new Label();
      this.label_Cavity = new Label();
      this.label_Hor_Runner = new Label();
      this.label_Pin = new Label();
      this.label_DB_List = new Label();
      this.panel_Node = new Panel();
      this.dataGridView_Node = new DataGridView();
      this.Column_Node_Type = new DataGridViewComboBoxColumn();
      this.Column_Group = new DataGridViewComboBoxColumn();
      this.dataGridViewTextBoxColumn1 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn2 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn3 = new DataGridViewTextBoxColumn();
      this.Column_Direction = new DataGridViewComboBoxColumn();
      this.Column_VavCheck = new DataGridViewTextBoxColumn();
      this.Column_VavTrigger = new DataGridViewTextBoxColumn();
      this.Column_VavState = new DataGridViewTextBoxColumn();
      this.Column_VavLoc = new DataGridViewTextBoxColumn();
      this.Column_VavOpen1 = new DataGridViewTextBoxColumn();
      this.Column_VavClose1 = new DataGridViewTextBoxColumn();
      this.Column_VavDelay = new DataGridViewTextBoxColumn();
      this.label7 = new Label();
      this.label8 = new Label();
      this.label_Pin_VavLoc = new Label();
      this.label12 = new Label();
      this.label13 = new Label();
      this.panel_Valve = new Panel();
      this.checkBox_ValveCheck = new CheckBox();
      this.label1 = new Label();
      this.panel_Valve_Sub = new Panel();
      this.newTextBox_VavDelayTime = new NewTextBox();
      this.label_VavDelayTime = new Label();
      this.newTextBox_VavClose1 = new NewTextBox();
      this.newTextBox_VavOpen1 = new NewTextBox();
      this.newComboBox_VavState = new NewComboBox();
      this.newTextBox_VavNode = new NewTextBox();
      this.newComboBox_VavTrigger = new NewComboBox();
      this.newButton_VavNode = new NewButton();
      this.label_CenterPoint = new Label();
      this.label_CenterX = new Label();
      this.label_CenterY = new Label();
      this.label_Hor_Two_Dim1 = new Label();
      this.label_Hor_Two_Dim2 = new Label();
      this.label_Hor_Two_Angle = new Label();
      this.toolTip_Hor_Angle = new ToolTip(this.components);
      this.newTextBox_Hor_Two_Angle = new NewTextBox();
      this.checkBox_Hor_Two_Inter = new CheckBox();
      this.label_Hor_Two_Direction = new Label();
      this.tabControl_Group = new TabControl();
      this.tabPage_G1 = new TabPage();
      this.label_Side_RunnerLength_G1 = new Label();
      this.label_Side_RunnerDim2_G1 = new Label();
      this.label_Side_RunnerDim4_G1 = new Label();
      this.label_Side_RunnerDim1_G1 = new Label();
      this.label_Side_RunnerDim3_G1 = new Label();
      this.label_Pin_GateDim2_G1 = new Label();
      this.label_Pin_GateDim1_G1 = new Label();
      this.newButton_Pin_GateLength_G1 = new NewButton();
      this.label_Pin_RunnerDim2_G1 = new Label();
      this.label_Pin_RunnerDim1_G1 = new Label();
      this.label_Pin_RunnerLength_G1 = new Label();
      this.label_Pin_Runner_G1 = new Label();
      this.newTextBox_Pin_GateDim2_G1 = new NewTextBox();
      this.newTextBox_Pin_GateDim1_G1 = new NewTextBox();
      this.newTextBox_Pin_GateLength_G1 = new NewTextBox();
      this.label_Pin_Gate_G1 = new Label();
      this.label_Side_Runner_G1 = new Label();
      this.newTextBox_Side_RunnerLength_G1 = new NewTextBox();
      this.panel_Side_GateType1_G1 = new Panel();
      this.radioButton_Side_GateCircle_G1 = new RadioButton();
      this.radioButton_Side_GateRect_G1 = new RadioButton();
      this.newTextBox_Side_RunnerDim4_G1 = new NewTextBox();
      this.panel_Side_GateType2_G1 = new Panel();
      this.radioButton_Side_GateSubMarine_G1 = new RadioButton();
      this.radioButton_Side_GateNormal_G1 = new RadioButton();
      this.label_Side_Gate_G1 = new Label();
      this.label_Side_GateLength_G1 = new Label();
      this.newTextBox_Side_RunnerDim1_G1 = new NewTextBox();
      this.newTextBox_Side_RunnerDim2_G1 = new NewTextBox();
      this.label_Side_GateDim1_G1 = new Label();
      this.newTextBox_Side_RunnerDim3_G1 = new NewTextBox();
      this.label_Side_GateDim3_G1 = new Label();
      this.label_Side_GateDim2_G1 = new Label();
      this.newTextBox_Pin_RunnerLength_G1 = new NewTextBox();
      this.newTextBox_Pin_RunnerDim1_G1 = new NewTextBox();
      this.label_Side_GateDim4_G1 = new Label();
      this.newTextBox_Pin_RunnerDim2_G1 = new NewTextBox();
      this.newTextBox_Side_GateDim4_G1 = new NewTextBox();
      this.newTextBox_Side_GateLength_G1 = new NewTextBox();
      this.newTextBox_Side_GateDim3_G1 = new NewTextBox();
      this.newTextBox_Side_GateDim1_G1 = new NewTextBox();
      this.newTextBox_Side_GateDim2_G1 = new NewTextBox();
      this.panel_Side_RunnerType_G1 = new Panel();
      this.radioButton_Side_RunnerRectangle_G1 = new RadioButton();
      this.radioButton_Side_RunnerCircle_G1 = new RadioButton();
      this.radioButton_Side_RunnerTrepezoidal_G1 = new RadioButton();
      this.tabPage_G2 = new TabPage();
      this.label_Side_RunnerLength_G2 = new Label();
      this.label_Side_RunnerDim2_G2 = new Label();
      this.label_Side_RunnerDim4_G2 = new Label();
      this.label_Side_RunnerDim1_G2 = new Label();
      this.label_Side_RunnerDim3_G2 = new Label();
      this.label_Pin_GateDim1_G2 = new Label();
      this.label_Pin_RunnerDim2_G2 = new Label();
      this.label_Pin_RunnerDim1_G2 = new Label();
      this.label_Pin_RunnerLength_G2 = new Label();
      this.label_Pin_Runner_G2 = new Label();
      this.label_Pin_GateDim2_G2 = new Label();
      this.label_Pin_Gate_G2 = new Label();
      this.label_Side_Runner_G2 = new Label();
      this.panel_Side_GateType1_G2 = new Panel();
      this.radioButton_Side_GateCircle_G2 = new RadioButton();
      this.radioButton_Side_GateRect_G2 = new RadioButton();
      this.panel_Side_GateType2_G2 = new Panel();
      this.radioButton_Side_GateSubMarine_G2 = new RadioButton();
      this.radioButton_Side_GateNormal_G2 = new RadioButton();
      this.label_Side_Gate_G2 = new Label();
      this.label_Side_GateLength_G2 = new Label();
      this.label_Side_GateDim1_G2 = new Label();
      this.label_Side_GateDim3_G2 = new Label();
      this.label_Side_GateDim2_G2 = new Label();
      this.label_Side_GateDim4_G2 = new Label();
      this.newButton_Pin_GateLength_G2 = new NewButton();
      this.newTextBox_Side_RunnerLength_G2 = new NewTextBox();
      this.newTextBox_Pin_RunnerLength_G2 = new NewTextBox();
      this.newTextBox_Pin_RunnerDim1_G2 = new NewTextBox();
      this.newTextBox_Pin_RunnerDim2_G2 = new NewTextBox();
      this.newTextBox_Side_RunnerDim4_G2 = new NewTextBox();
      this.newTextBox_Pin_GateLength_G2 = new NewTextBox();
      this.newTextBox_Pin_GateDim1_G2 = new NewTextBox();
      this.newTextBox_Pin_GateDim2_G2 = new NewTextBox();
      this.newTextBox_Side_RunnerDim1_G2 = new NewTextBox();
      this.newTextBox_Side_RunnerDim2_G2 = new NewTextBox();
      this.newTextBox_Side_RunnerDim3_G2 = new NewTextBox();
      this.newTextBox_Side_GateDim4_G2 = new NewTextBox();
      this.newTextBox_Side_GateLength_G2 = new NewTextBox();
      this.newTextBox_Side_GateDim3_G2 = new NewTextBox();
      this.newTextBox_Side_GateDim1_G2 = new NewTextBox();
      this.newTextBox_Side_GateDim2_G2 = new NewTextBox();
      this.panel_Side_RunnerType_G2 = new Panel();
      this.radioButton_Side_RunnerRectangle_G2 = new RadioButton();
      this.radioButton_Side_RunnerCircle_G2 = new RadioButton();
      this.radioButton_Side_RunnerTrepezoidal_G2 = new RadioButton();
      this.tabPage_G3 = new TabPage();
      this.label_Pin_Gate_G3 = new Label();
      this.label_Pin_Runner_G3 = new Label();
      this.label_Pin_GateDim1_G3 = new Label();
      this.panel_Side_GateType1_G3 = new Panel();
      this.radioButton_Side_GateCircle_G3 = new RadioButton();
      this.radioButton_Side_GateRect_G3 = new RadioButton();
      this.label_Pin_RunnerLength_G3 = new Label();
      this.label_Side_RunnerLength_G3 = new Label();
      this.label_Pin_RunnerDim1_G3 = new Label();
      this.panel_Side_GateType2_G3 = new Panel();
      this.radioButton_Side_GateSubMarine_G3 = new RadioButton();
      this.radioButton_Side_GateNormal_G3 = new RadioButton();
      this.label_Side_RunnerDim2_G3 = new Label();
      this.label_Side_RunnerDim4_G3 = new Label();
      this.label_Side_RunnerDim1_G3 = new Label();
      this.label_Pin_GateDim2_G3 = new Label();
      this.label_Side_Gate_G3 = new Label();
      this.label_Side_RunnerDim3_G3 = new Label();
      this.label_Pin_RunnerDim2_G3 = new Label();
      this.label_Side_GateLength_G3 = new Label();
      this.label_Side_GateDim1_G3 = new Label();
      this.label_Side_GateDim3_G3 = new Label();
      this.label_Side_GateDim2_G3 = new Label();
      this.label_Side_GateDim4_G3 = new Label();
      this.newTextBox_Side_RunnerLength_G3 = new NewTextBox();
      this.newTextBox_Side_RunnerDim4_G3 = new NewTextBox();
      this.newButton_Pin_GateLength_G3 = new NewButton();
      this.newTextBox_Side_RunnerDim1_G3 = new NewTextBox();
      this.newTextBox_Pin_GateLength_G3 = new NewTextBox();
      this.newTextBox_Side_RunnerDim2_G3 = new NewTextBox();
      this.newTextBox_Pin_GateDim1_G3 = new NewTextBox();
      this.newTextBox_Side_RunnerDim3_G3 = new NewTextBox();
      this.newTextBox_Pin_GateDim2_G3 = new NewTextBox();
      this.newTextBox_Pin_RunnerLength_G3 = new NewTextBox();
      this.newTextBox_Side_GateDim4_G3 = new NewTextBox();
      this.newTextBox_Side_GateLength_G3 = new NewTextBox();
      this.newTextBox_Pin_RunnerDim1_G3 = new NewTextBox();
      this.newTextBox_Side_GateDim3_G3 = new NewTextBox();
      this.newTextBox_Side_GateDim1_G3 = new NewTextBox();
      this.newTextBox_Pin_RunnerDim2_G3 = new NewTextBox();
      this.newTextBox_Side_GateDim2_G3 = new NewTextBox();
      this.label_Side_Runner_G3 = new Label();
      this.panel_Side_RunnerType_G3 = new Panel();
      this.radioButton_Side_RunnerRectangle_G3 = new RadioButton();
      this.radioButton_Side_RunnerCircle_G3 = new RadioButton();
      this.radioButton_Side_RunnerTrepezoidal_G3 = new RadioButton();
      this.tabPage_G4 = new TabPage();
      this.label_Side_RunnerDim4_G4 = new Label();
      this.label_Side_RunnerLength_G4 = new Label();
      this.label_Side_RunnerDim2_G4 = new Label();
      this.label_Side_RunnerDim1_G4 = new Label();
      this.label_Side_RunnerDim3_G4 = new Label();
      this.newTextBox_Side_RunnerLength_G4 = new NewTextBox();
      this.newTextBox_Side_RunnerDim4_G4 = new NewTextBox();
      this.newTextBox_Side_RunnerDim3_G4 = new NewTextBox();
      this.newTextBox_Side_RunnerDim2_G4 = new NewTextBox();
      this.newTextBox_Side_RunnerDim1_G4 = new NewTextBox();
      this.label_Pin_GateDim2_G4 = new Label();
      this.label_Pin_GateDim1_G4 = new Label();
      this.newButton_Pin_GateLength_G4 = new NewButton();
      this.label_Pin_Gate_G4 = new Label();
      this.label_Side_Runner_G4 = new Label();
      this.label_Pin_Runner_G4 = new Label();
      this.panel_Side_RunnerType_G4 = new Panel();
      this.radioButton_Side_RunnerRectangle_G4 = new RadioButton();
      this.radioButton_Side_RunnerCircle_G4 = new RadioButton();
      this.radioButton_Side_RunnerTrepezoidal_G4 = new RadioButton();
      this.panel_Side_GateType1_G4 = new Panel();
      this.radioButton_Side_GateCircle_G4 = new RadioButton();
      this.radioButton_Side_GateRect_G4 = new RadioButton();
      this.label_Pin_RunnerLength_G4 = new Label();
      this.label_Pin_RunnerDim1_G4 = new Label();
      this.panel_Side_GateType2_G4 = new Panel();
      this.radioButton_Side_GateSubMarine_G4 = new RadioButton();
      this.radioButton_Side_GateNormal_G4 = new RadioButton();
      this.label_Side_Gate_G4 = new Label();
      this.label_Pin_RunnerDim2_G4 = new Label();
      this.label_Side_GateLength_G4 = new Label();
      this.label_Side_GateDim1_G4 = new Label();
      this.label_Side_GateDim3_G4 = new Label();
      this.label_Side_GateDim2_G4 = new Label();
      this.label_Side_GateDim4_G4 = new Label();
      this.newTextBox_Pin_GateLength_G4 = new NewTextBox();
      this.newTextBox_Pin_GateDim1_G4 = new NewTextBox();
      this.newTextBox_Pin_GateDim2_G4 = new NewTextBox();
      this.newTextBox_Pin_RunnerLength_G4 = new NewTextBox();
      this.newTextBox_Side_GateDim4_G4 = new NewTextBox();
      this.newTextBox_Side_GateLength_G4 = new NewTextBox();
      this.newTextBox_Pin_RunnerDim1_G4 = new NewTextBox();
      this.newTextBox_Side_GateDim3_G4 = new NewTextBox();
      this.newTextBox_Side_GateDim1_G4 = new NewTextBox();
      this.newTextBox_Pin_RunnerDim2_G4 = new NewTextBox();
      this.newTextBox_Side_GateDim2_G4 = new NewTextBox();
      this.panel_Group = new Panel();
      this.newButton_G3 = new NewButton();
      this.newButton_G2 = new NewButton();
      this.newButton_G4 = new NewButton();
      this.newButton_G1 = new NewButton();
      this.tabControl_Hor_RunnerType = new TabControl();
      this.tabPage_Hor_TwoStage = new TabPage();
      this.newTextBox_Hor_Two_Length = new NewTextBox();
      this.newTextBox_Hor_Two_CenterTol = new NewTextBox();
      this.label_Hor_Two_CenterTol = new Label();
      this.newComboBox_Hor_Two_Direction = new NewComboBox();
      this.newTextBox_Hor_Two_Dia = new NewTextBox();
      this.newTextBox_Hor_Two_Dim2 = new NewTextBox();
      this.newTextBox_Hor_Two_Dim1 = new NewTextBox();
      this.tabPage_Hor_ThreeStage = new TabPage();
      this.newTextBox_Hor_Three_Length = new NewTextBox();
      this.label_Hor_Three_Length = new Label();
      this.newTextBox_Hor_Three_Dia = new NewTextBox();
      this.panel_Quadrant = new Panel();
      this.radioButton_Quadrant4 = new RadioButton();
      this.radioButton_Quadrant2 = new RadioButton();
      this.newTextBox_Hor_Three_CenterTol = new NewTextBox();
      this.label_Hor_Three_CenterTol = new Label();
      this.label_Hor_Three_Dia = new Label();
      this.panel_Hor_Runner_ThreeType = new Panel();
      this.radioButton_Hor_Three_Type2 = new RadioButton();
      this.radioButton_Hor_Three_Type1 = new RadioButton();
      this.panel_Hor_RunnerType = new Panel();
      this.newButton_Hor_Runner_ThreeStage = new NewButton();
      this.newButton_Hor_Runner_TwoStage = new NewButton();
      this.newButton_Node_Pin_Select = new NewButton();
      this.newButton_LoadPrevNode = new NewButton();
      this.newButton_Node_Del = new NewButton();
      this.newButton_Node_Add = new NewButton();
      this.newTextBox_Cavity_Occ = new NewTextBox();
      this.newTextBox_Sprue_Dim2 = new NewTextBox();
      this.newTextBox_Sprue_Dim1 = new NewTextBox();
      this.newTextBox_Sprue_Length = new NewTextBox();
      this.newButton_Edit = new NewButton();
      this.newButton_Apply = new NewButton();
      this.newComboBox_Item = new NewComboBox();
      this.newComboBox_Company = new NewComboBox();
      this.newTextBox_CenterX = new NewTextBox();
      this.newTextBox_CenterY = new NewTextBox();
      this.newTextBox_Node = new NewTextBox();
      this.newButton_Node_Side_Select = new NewButton();
      this.newButton_Node_Update = new NewButton();
      this.panel1 = new Panel();
      this.radioButton_Node4 = new RadioButton();
      this.radioButton_Node3 = new RadioButton();
      this.radioButton_Node2 = new RadioButton();
      this.radioButton_Node1 = new RadioButton();
      this.panel_Hor_Runner_TwoType.SuspendLayout();
      this.panel_Sprue_Type.SuspendLayout();
      this.panel_Pin_Type.SuspendLayout();
      this.panel_Node.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_Node).BeginInit();
      this.panel_Valve.SuspendLayout();
      this.panel_Valve_Sub.SuspendLayout();
      this.tabControl_Group.SuspendLayout();
      this.tabPage_G1.SuspendLayout();
      this.panel_Side_GateType1_G1.SuspendLayout();
      this.panel_Side_GateType2_G1.SuspendLayout();
      this.panel_Side_RunnerType_G1.SuspendLayout();
      this.tabPage_G2.SuspendLayout();
      this.panel_Side_GateType1_G2.SuspendLayout();
      this.panel_Side_GateType2_G2.SuspendLayout();
      this.panel_Side_RunnerType_G2.SuspendLayout();
      this.tabPage_G3.SuspendLayout();
      this.panel_Side_GateType1_G3.SuspendLayout();
      this.panel_Side_GateType2_G3.SuspendLayout();
      this.panel_Side_RunnerType_G3.SuspendLayout();
      this.tabPage_G4.SuspendLayout();
      this.panel_Side_RunnerType_G4.SuspendLayout();
      this.panel_Side_GateType1_G4.SuspendLayout();
      this.panel_Side_GateType2_G4.SuspendLayout();
      this.panel_Group.SuspendLayout();
      this.tabControl_Hor_RunnerType.SuspendLayout();
      this.tabPage_Hor_TwoStage.SuspendLayout();
      this.tabPage_Hor_ThreeStage.SuspendLayout();
      this.panel_Quadrant.SuspendLayout();
      this.panel_Hor_Runner_ThreeType.SuspendLayout();
      this.panel_Hor_RunnerType.SuspendLayout();
      this.panel1.SuspendLayout();
      this.SuspendLayout();
      this.label_Hor_Two_Length.BackColor = Color.Lavender;
      this.label_Hor_Two_Length.BorderStyle = BorderStyle.FixedSingle;
      this.label_Hor_Two_Length.Location = new Point(170, 26);
      this.label_Hor_Two_Length.Name = "label_Hor_Two_Length";
      this.label_Hor_Two_Length.Size = new Size(120, 23);
      this.label_Hor_Two_Length.TabIndex = 55;
      this.label_Hor_Two_Length.Text = "수평 런너 길이";
      this.label_Hor_Two_Length.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Sprue_Dim2.BackColor = Color.Lavender;
      this.label_Sprue_Dim2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Sprue_Dim2.Location = new Point(333, 775);
      this.label_Sprue_Dim2.Name = "label_Sprue_Dim2";
      this.label_Sprue_Dim2.Size = new Size(120, 23);
      this.label_Sprue_Dim2.TabIndex = 54;
      this.label_Sprue_Dim2.Text = "End Dimension";
      this.label_Sprue_Dim2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Sprue_Length.BackColor = Color.Lavender;
      this.label_Sprue_Length.BorderStyle = BorderStyle.FixedSingle;
      this.label_Sprue_Length.Location = new Point(333, 731);
      this.label_Sprue_Length.Name = "label_Sprue_Length";
      this.label_Sprue_Length.Size = new Size(120, 23);
      this.label_Sprue_Length.TabIndex = 52;
      this.label_Sprue_Length.Text = "스프루 길이";
      this.label_Sprue_Length.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Cavity_Occ.BackColor = Color.Lavender;
      this.label_Cavity_Occ.BorderStyle = BorderStyle.FixedSingle;
      this.label_Cavity_Occ.Location = new Point(498, 709);
      this.label_Cavity_Occ.Name = "label_Cavity_Occ";
      this.label_Cavity_Occ.Size = new Size(120, 23);
      this.label_Cavity_Occ.TabIndex = 36;
      this.label_Cavity_Occ.Text = "Occurence Number";
      this.label_Cavity_Occ.TextAlign = ContentAlignment.MiddleCenter;
      this.label_DB_Item.BackColor = Color.Lavender;
      this.label_DB_Item.BorderStyle = BorderStyle.FixedSingle;
      this.label_DB_Item.Location = new Point(334, 24);
      this.label_DB_Item.Name = "label_DB_Item";
      this.label_DB_Item.Size = new Size(86, 23);
      this.label_DB_Item.TabIndex = 33;
      this.label_DB_Item.Text = "아이템명";
      this.label_DB_Item.TextAlign = ContentAlignment.MiddleCenter;
      this.label_DB_Company.BackColor = Color.Lavender;
      this.label_DB_Company.BorderStyle = BorderStyle.FixedSingle;
      this.label_DB_Company.Location = new Point(4, 24);
      this.label_DB_Company.Name = "label_DB_Company";
      this.label_DB_Company.Size = new Size(86, 23);
      this.label_DB_Company.TabIndex = 38;
      this.label_DB_Company.Text = "회사명";
      this.label_DB_Company.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Hor_Two_Dia.BackColor = Color.Lavender;
      this.label_Hor_Two_Dia.BorderStyle = BorderStyle.FixedSingle;
      this.label_Hor_Two_Dia.Location = new Point(5, 26);
      this.label_Hor_Two_Dia.Name = "label_Hor_Two_Dia";
      this.label_Hor_Two_Dia.Size = new Size(120, 23);
      this.label_Hor_Two_Dia.TabIndex = 40;
      this.label_Hor_Two_Dia.Text = "수평 런너 직경";
      this.label_Hor_Two_Dia.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Sprue_Dim1.BackColor = Color.Lavender;
      this.label_Sprue_Dim1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Sprue_Dim1.Location = new Point(333, 753);
      this.label_Sprue_Dim1.Name = "label_Sprue_Dim1";
      this.label_Sprue_Dim1.Size = new Size(120, 23);
      this.label_Sprue_Dim1.TabIndex = 50;
      this.label_Sprue_Dim1.Text = "Start Dimension";
      this.label_Sprue_Dim1.TextAlign = ContentAlignment.MiddleCenter;
      this.panel_Hor_Runner_TwoType.BackColor = Color.White;
      this.panel_Hor_Runner_TwoType.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Hor_Runner_TwoType.Controls.Add((Control) this.radioButton_Hor_Two_Type4);
      this.panel_Hor_Runner_TwoType.Controls.Add((Control) this.radioButton_Hor_Two_Type3);
      this.panel_Hor_Runner_TwoType.Controls.Add((Control) this.radioButton_Hor_Two_Type2);
      this.panel_Hor_Runner_TwoType.Controls.Add((Control) this.radioButton_Hor_Two_Type1);
      this.panel_Hor_Runner_TwoType.Location = new Point(5, 4);
      this.panel_Hor_Runner_TwoType.Name = "panel_Hor_Runner_TwoType";
      this.panel_Hor_Runner_TwoType.Size = new Size(331, 23);
      this.panel_Hor_Runner_TwoType.TabIndex = 54;
      this.radioButton_Hor_Two_Type4.BackColor = Color.White;
      this.radioButton_Hor_Two_Type4.Location = new Point(241, 2);
      this.radioButton_Hor_Two_Type4.Name = "radioButton_Hor_Two_Type4";
      this.radioButton_Hor_Two_Type4.Size = new Size(85, 19);
      this.radioButton_Hor_Two_Type4.TabIndex = 58;
      this.radioButton_Hor_Two_Type4.Text = "☆ - x 타입";
      this.radioButton_Hor_Two_Type4.UseVisualStyleBackColor = false;
      this.radioButton_Hor_Two_Type4.CheckedChanged += new EventHandler(this.radioButton_Hor_Type4_CheckedChanged);
      this.radioButton_Hor_Two_Type3.BackColor = Color.White;
      this.radioButton_Hor_Two_Type3.Location = new Point(147, 2);
      this.radioButton_Hor_Two_Type3.Name = "radioButton_Hor_Two_Type3";
      this.radioButton_Hor_Two_Type3.Size = new Size(85, 19);
      this.radioButton_Hor_Two_Type3.TabIndex = 57;
      this.radioButton_Hor_Two_Type3.Text = "☆ - + 타입";
      this.radioButton_Hor_Two_Type3.UseVisualStyleBackColor = false;
      this.radioButton_Hor_Two_Type3.CheckedChanged += new EventHandler(this.radioButton_Hor_Type3_CheckedChanged);
      this.radioButton_Hor_Two_Type2.BackColor = Color.White;
      this.radioButton_Hor_Two_Type2.Location = new Point(72, 2);
      this.radioButton_Hor_Two_Type2.Name = "radioButton_Hor_Two_Type2";
      this.radioButton_Hor_Two_Type2.Size = new Size(68, 19);
      this.radioButton_Hor_Two_Type2.TabIndex = 56;
      this.radioButton_Hor_Two_Type2.Text = "H 타입";
      this.radioButton_Hor_Two_Type2.UseVisualStyleBackColor = false;
      this.radioButton_Hor_Two_Type2.CheckedChanged += new EventHandler(this.radioButton_Hor_Type2_CheckedChanged);
      this.radioButton_Hor_Two_Type1.BackColor = Color.White;
      this.radioButton_Hor_Two_Type1.Location = new Point(6, 2);
      this.radioButton_Hor_Two_Type1.Name = "radioButton_Hor_Two_Type1";
      this.radioButton_Hor_Two_Type1.Size = new Size(55, 19);
      this.radioButton_Hor_Two_Type1.TabIndex = 55;
      this.radioButton_Hor_Two_Type1.Text = "I 타입";
      this.radioButton_Hor_Two_Type1.UseVisualStyleBackColor = false;
      this.radioButton_Hor_Two_Type1.CheckedChanged += new EventHandler(this.radioButton_Hor_Type1_CheckedChanged);
      this.panel_Sprue_Type.BackColor = Color.White;
      this.panel_Sprue_Type.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Sprue_Type.Controls.Add((Control) this.radioButton_Sprue_Hot);
      this.panel_Sprue_Type.Controls.Add((Control) this.radioButton_Sprue_Cold);
      this.panel_Sprue_Type.Location = new Point(333, 709);
      this.panel_Sprue_Type.Name = "panel_Sprue_Type";
      this.panel_Sprue_Type.Size = new Size(166, 23);
      this.panel_Sprue_Type.TabIndex = 65;
      this.radioButton_Sprue_Hot.Location = new Point(88, 1);
      this.radioButton_Sprue_Hot.Name = "radioButton_Sprue_Hot";
      this.radioButton_Sprue_Hot.Size = new Size(78, 19);
      this.radioButton_Sprue_Hot.TabIndex = 67;
      this.radioButton_Sprue_Hot.Text = "핫 스프루";
      this.radioButton_Sprue_Hot.UseVisualStyleBackColor = true;
      this.radioButton_Sprue_Cold.Location = new Point(2, 1);
      this.radioButton_Sprue_Cold.Name = "radioButton_Sprue_Cold";
      this.radioButton_Sprue_Cold.Size = new Size(89, 19);
      this.radioButton_Sprue_Cold.TabIndex = 66;
      this.radioButton_Sprue_Cold.Text = "콜드 스프루";
      this.radioButton_Sprue_Cold.UseVisualStyleBackColor = true;
      this.panel_Pin_Type.BackColor = Color.White;
      this.panel_Pin_Type.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Pin_Type.Controls.Add((Control) this.radioButton_Pin_HotSystem);
      this.panel_Pin_Type.Controls.Add((Control) this.radioButton_Pin_ColdSystem);
      this.panel_Pin_Type.Location = new Point(4, 219);
      this.panel_Pin_Type.Name = "panel_Pin_Type";
      this.panel_Pin_Type.Size = new Size(331, 23);
      this.panel_Pin_Type.TabIndex = 8;
      this.radioButton_Pin_HotSystem.Location = new Point(188, 1);
      this.radioButton_Pin_HotSystem.Name = "radioButton_Pin_HotSystem";
      this.radioButton_Pin_HotSystem.Size = new Size(76, 19);
      this.radioButton_Pin_HotSystem.TabIndex = 10;
      this.radioButton_Pin_HotSystem.Text = "핫 시스템";
      this.radioButton_Pin_HotSystem.UseVisualStyleBackColor = true;
      this.radioButton_Pin_HotSystem.CheckedChanged += new EventHandler(this.radioButton_Pin_HotSystem_CheckedChanged);
      this.radioButton_Pin_ColdSystem.Location = new Point(38, 2);
      this.radioButton_Pin_ColdSystem.Name = "radioButton_Pin_ColdSystem";
      this.radioButton_Pin_ColdSystem.Size = new Size(89, 19);
      this.radioButton_Pin_ColdSystem.TabIndex = 9;
      this.radioButton_Pin_ColdSystem.Text = "콜드 시스템";
      this.radioButton_Pin_ColdSystem.UseVisualStyleBackColor = true;
      this.radioButton_Pin_ColdSystem.CheckedChanged += new EventHandler(this.radioButton_Pin_ColdSystem_CheckedChanged);
      this.listBox_DB.BackColor = Color.LavenderBlush;
      this.listBox_DB.FormattingEnabled = true;
      this.listBox_DB.ItemHeight = 15;
      this.listBox_DB.Location = new Point(4, 46);
      this.listBox_DB.Name = "listBox_DB";
      this.listBox_DB.SelectionMode = SelectionMode.MultiExtended;
      this.listBox_DB.Size = new Size(660, 94);
      this.listBox_DB.TabIndex = 3;
      this.listBox_DB.TabStop = false;
      this.listBox_DB.SelectedIndexChanged += new EventHandler(this.listBox_DB_SelectedIndexChanged);
      this.label_Sprue.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Sprue.BorderStyle = BorderStyle.FixedSingle;
      this.label_Sprue.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Sprue.ForeColor = Color.MidnightBlue;
      this.label_Sprue.Location = new Point(333, 690);
      this.label_Sprue.Name = "label_Sprue";
      this.label_Sprue.Size = new Size(166, 20);
      this.label_Sprue.TabIndex = 18;
      this.label_Sprue.Text = "[스프루]";
      this.label_Sprue.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Cavity.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Cavity.BorderStyle = BorderStyle.FixedSingle;
      this.label_Cavity.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Cavity.ForeColor = Color.MidnightBlue;
      this.label_Cavity.Location = new Point(498, 690);
      this.label_Cavity.Name = "label_Cavity";
      this.label_Cavity.Size = new Size(166, 20);
      this.label_Cavity.TabIndex = 20;
      this.label_Cavity.Text = "[캐비티]";
      this.label_Cavity.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Hor_Runner.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Hor_Runner.BorderStyle = BorderStyle.FixedSingle;
      this.label_Hor_Runner.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Hor_Runner.ForeColor = Color.MidnightBlue;
      this.label_Hor_Runner.Location = new Point(4, 690);
      this.label_Hor_Runner.Name = "label_Hor_Runner";
      this.label_Hor_Runner.Size = new Size(331, 20);
      this.label_Hor_Runner.TabIndex = 25;
      this.label_Hor_Runner.Text = "[수평런너]";
      this.label_Hor_Runner.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Pin.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Pin.ForeColor = Color.MidnightBlue;
      this.label_Pin.Location = new Point(4, 200);
      this.label_Pin.Name = "label_Pin";
      this.label_Pin.Size = new Size(331, 20);
      this.label_Pin.TabIndex = 21;
      this.label_Pin.Text = "[핀]";
      this.label_Pin.TextAlign = ContentAlignment.MiddleCenter;
      this.label_DB_List.BackColor = Color.FromArgb(229, 238, 248);
      this.label_DB_List.BorderStyle = BorderStyle.FixedSingle;
      this.label_DB_List.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_DB_List.ForeColor = Color.MidnightBlue;
      this.label_DB_List.Location = new Point(4, 5);
      this.label_DB_List.Name = "label_DB_List";
      this.label_DB_List.Size = new Size(660, 20);
      this.label_DB_List.TabIndex = 16;
      this.label_DB_List.Text = "DB 리스트";
      this.label_DB_List.TextAlign = ContentAlignment.MiddleCenter;
      this.panel_Node.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Node.Controls.Add((Control) this.dataGridView_Node);
      this.panel_Node.Location = new Point(196, 263);
      this.panel_Node.Name = "panel_Node";
      this.panel_Node.Size = new Size(468, 221);
      this.panel_Node.TabIndex = 75;
      this.dataGridView_Node.AllowDrop = true;
      this.dataGridView_Node.AllowUserToAddRows = false;
      this.dataGridView_Node.AllowUserToDeleteRows = false;
      this.dataGridView_Node.AllowUserToResizeColumns = false;
      this.dataGridView_Node.AllowUserToResizeRows = false;
      this.dataGridView_Node.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_Node.BackgroundColor = Color.White;
      gridViewCellStyle1.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle1.BackColor = Color.Lavender;
      gridViewCellStyle1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle1.ForeColor = SystemColors.WindowText;
      gridViewCellStyle1.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle1.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle1.WrapMode = DataGridViewTriState.True;
      this.dataGridView_Node.ColumnHeadersDefaultCellStyle = gridViewCellStyle1;
      this.dataGridView_Node.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_Node.Columns.AddRange((DataGridViewColumn) this.Column_Node_Type, (DataGridViewColumn) this.Column_Group, (DataGridViewColumn) this.dataGridViewTextBoxColumn1, (DataGridViewColumn) this.dataGridViewTextBoxColumn2, (DataGridViewColumn) this.dataGridViewTextBoxColumn3, (DataGridViewColumn) this.Column_Direction, (DataGridViewColumn) this.Column_VavCheck, (DataGridViewColumn) this.Column_VavTrigger, (DataGridViewColumn) this.Column_VavState, (DataGridViewColumn) this.Column_VavLoc, (DataGridViewColumn) this.Column_VavOpen1, (DataGridViewColumn) this.Column_VavClose1, (DataGridViewColumn) this.Column_VavDelay);
      gridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleLeft;
      gridViewCellStyle2.BackColor = Color.White;
      gridViewCellStyle2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle2.ForeColor = SystemColors.ControlText;
      gridViewCellStyle2.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle2.WrapMode = DataGridViewTriState.False;
      this.dataGridView_Node.DefaultCellStyle = gridViewCellStyle2;
      this.dataGridView_Node.EnableHeadersVisualStyles = false;
      this.dataGridView_Node.Location = new Point(-2, -2);
      this.dataGridView_Node.Name = "dataGridView_Node";
      this.dataGridView_Node.RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.DisableResizing;
      this.dataGridView_Node.RowTemplate.Height = 23;
      this.dataGridView_Node.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
      this.dataGridView_Node.Size = new Size(470, 222);
      this.dataGridView_Node.TabIndex = 76;
      this.dataGridView_Node.TabStop = false;
      this.dataGridView_Node.CellMouseDoubleClick += new DataGridViewCellMouseEventHandler(this.dataGridView_Node_CellMouseDoubleClick);
      this.dataGridView_Node.CellValueChanged += new DataGridViewCellEventHandler(this.dataGridView_Node_CellValueChanged);
      this.dataGridView_Node.CurrentCellChanged += new EventHandler(this.dataGridView_Node_CurrentCellChanged);
      this.dataGridView_Node.CurrentCellDirtyStateChanged += new EventHandler(this.dataGridView_Node_CurrentCellDirtyStateChanged);
      this.dataGridView_Node.RowPostPaint += new DataGridViewRowPostPaintEventHandler(this.dataGridView_Node_RowPostPaint);
      this.dataGridView_Node.DragDrop += new DragEventHandler(this.dataGridView_Node_DragDrop);
      this.dataGridView_Node.DragOver += new DragEventHandler(this.dataGridView_Node_DragOver);
      this.dataGridView_Node.MouseMove += new MouseEventHandler(this.dataGridView_Node_MouseMove);
      this.dataGridView_Node.MouseUp += new MouseEventHandler(this.dataGridView_Node_MouseUp);
      this.Column_Node_Type.HeaderText = "종류";
      this.Column_Node_Type.Name = "Column_Node_Type";
      this.Column_Group.HeaderText = "그룹";
      this.Column_Group.Name = "Column_Group";
      this.dataGridViewTextBoxColumn1.HeaderText = "X";
      this.dataGridViewTextBoxColumn1.Name = "dataGridViewTextBoxColumn1";
      this.dataGridViewTextBoxColumn1.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.dataGridViewTextBoxColumn2.HeaderText = "Y";
      this.dataGridViewTextBoxColumn2.Name = "dataGridViewTextBoxColumn2";
      this.dataGridViewTextBoxColumn2.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.dataGridViewTextBoxColumn3.HeaderText = "Z";
      this.dataGridViewTextBoxColumn3.Name = "dataGridViewTextBoxColumn3";
      this.dataGridViewTextBoxColumn3.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.Column_Direction.HeaderText = "방향";
      this.Column_Direction.Name = "Column_Direction";
      this.Column_Direction.Resizable = DataGridViewTriState.True;
      this.Column_VavCheck.HeaderText = "VavCheck";
      this.Column_VavCheck.Name = "Column_VavCheck";
      this.Column_VavCheck.ReadOnly = true;
      this.Column_VavCheck.Visible = false;
      this.Column_VavTrigger.HeaderText = "VavTrigger";
      this.Column_VavTrigger.Name = "Column_VavTrigger";
      this.Column_VavTrigger.ReadOnly = true;
      this.Column_VavTrigger.Visible = false;
      this.Column_VavState.HeaderText = "VavState";
      this.Column_VavState.Name = "Column_VavState";
      this.Column_VavState.ReadOnly = true;
      this.Column_VavState.Visible = false;
      this.Column_VavLoc.HeaderText = "VavLoc";
      this.Column_VavLoc.Name = "Column_VavLoc";
      this.Column_VavLoc.ReadOnly = true;
      this.Column_VavLoc.Visible = false;
      this.Column_VavOpen1.HeaderText = "VavOpen1";
      this.Column_VavOpen1.Name = "Column_VavOpen1";
      this.Column_VavOpen1.ReadOnly = true;
      this.Column_VavOpen1.Visible = false;
      this.Column_VavClose1.HeaderText = "VavClose1";
      this.Column_VavClose1.Name = "Column_VavClose1";
      this.Column_VavClose1.ReadOnly = true;
      this.Column_VavClose1.Visible = false;
      this.Column_VavDelay.HeaderText = "VavDelay";
      this.Column_VavDelay.Name = "Column_VavDelay";
      this.Column_VavDelay.ReadOnly = true;
      this.Column_VavDelay.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.Column_VavDelay.Visible = false;
      this.label7.BackColor = Color.Lavender;
      this.label7.BorderStyle = BorderStyle.FixedSingle;
      this.label7.Location = new Point(0, 0);
      this.label7.Name = "label7";
      this.label7.Size = new Size(193, 23);
      this.label7.TabIndex = 49;
      this.label7.Text = "Valve gate trigger";
      this.label7.TextAlign = ContentAlignment.MiddleCenter;
      this.label8.BackColor = Color.Lavender;
      this.label8.BorderStyle = BorderStyle.FixedSingle;
      this.label8.Location = new Point(0, 44);
      this.label8.Name = "label8";
      this.label8.Size = new Size(193, 23);
      this.label8.TabIndex = 49;
      this.label8.Text = "Valve gate initial state";
      this.label8.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_VavLoc.BackColor = Color.Lavender;
      this.label_Pin_VavLoc.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_VavLoc.Location = new Point(0, 88);
      this.label_Pin_VavLoc.Name = "label_Pin_VavLoc";
      this.label_Pin_VavLoc.Size = new Size(193, 23);
      this.label_Pin_VavLoc.TabIndex = 49;
      this.label_Pin_VavLoc.Text = "Trigger location";
      this.label_Pin_VavLoc.TextAlign = ContentAlignment.MiddleCenter;
      this.label12.BackColor = Color.Lavender;
      this.label12.BorderStyle = BorderStyle.FixedSingle;
      this.label12.Location = new Point(0, 132);
      this.label12.Name = "label12";
      this.label12.Size = new Size(97, 23);
      this.label12.TabIndex = 49;
      this.label12.Text = "Open at";
      this.label12.TextAlign = ContentAlignment.MiddleCenter;
      this.label13.BackColor = Color.Lavender;
      this.label13.BorderStyle = BorderStyle.FixedSingle;
      this.label13.Location = new Point(96, 132);
      this.label13.Name = "label13";
      this.label13.Size = new Size(97, 23);
      this.label13.TabIndex = 49;
      this.label13.Text = "Close at";
      this.label13.TextAlign = ContentAlignment.MiddleCenter;
      this.panel_Valve.Controls.Add((Control) this.checkBox_ValveCheck);
      this.panel_Valve.Controls.Add((Control) this.label1);
      this.panel_Valve.Controls.Add((Control) this.panel_Valve_Sub);
      this.panel_Valve.Enabled = false;
      this.panel_Valve.Location = new Point(4, 241);
      this.panel_Valve.Name = "panel_Valve";
      this.panel_Valve.Size = new Size(193, 243);
      this.panel_Valve.TabIndex = 16;
      this.checkBox_ValveCheck.BackColor = Color.FromArgb(229, 238, 248);
      this.checkBox_ValveCheck.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.checkBox_ValveCheck.Location = new Point(56, 2);
      this.checkBox_ValveCheck.Name = "checkBox_ValveCheck";
      this.checkBox_ValveCheck.Size = new Size(72, 18);
      this.checkBox_ValveCheck.TabIndex = 17;
      this.checkBox_ValveCheck.TabStop = false;
      this.checkBox_ValveCheck.Text = "<Valve>";
      this.checkBox_ValveCheck.UseVisualStyleBackColor = false;
      this.checkBox_ValveCheck.CheckedChanged += new EventHandler(this.checkBox_Pin_ValveCheck_CheckedChanged);
      this.label1.BackColor = Color.FromArgb(229, 238, 248);
      this.label1.BorderStyle = BorderStyle.FixedSingle;
      this.label1.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label1.ForeColor = Color.MidnightBlue;
      this.label1.Location = new Point(0, 0);
      this.label1.Name = "label1";
      this.label1.Size = new Size(193, 23);
      this.label1.TabIndex = 24;
      this.label1.Text = "<Valve>";
      this.label1.TextAlign = ContentAlignment.MiddleCenter;
      this.panel_Valve_Sub.Controls.Add((Control) this.newTextBox_VavDelayTime);
      this.panel_Valve_Sub.Controls.Add((Control) this.label_VavDelayTime);
      this.panel_Valve_Sub.Controls.Add((Control) this.newTextBox_VavClose1);
      this.panel_Valve_Sub.Controls.Add((Control) this.label7);
      this.panel_Valve_Sub.Controls.Add((Control) this.newTextBox_VavOpen1);
      this.panel_Valve_Sub.Controls.Add((Control) this.label13);
      this.panel_Valve_Sub.Controls.Add((Control) this.label8);
      this.panel_Valve_Sub.Controls.Add((Control) this.label12);
      this.panel_Valve_Sub.Controls.Add((Control) this.newComboBox_VavState);
      this.panel_Valve_Sub.Controls.Add((Control) this.newTextBox_VavNode);
      this.panel_Valve_Sub.Controls.Add((Control) this.newComboBox_VavTrigger);
      this.panel_Valve_Sub.Controls.Add((Control) this.label_Pin_VavLoc);
      this.panel_Valve_Sub.Controls.Add((Control) this.newButton_VavNode);
      this.panel_Valve_Sub.Location = new Point(0, 22);
      this.panel_Valve_Sub.Name = "panel_Valve_Sub";
      this.panel_Valve_Sub.Size = new Size(193, 221);
      this.panel_Valve_Sub.TabIndex = 18;
      this.newTextBox_VavDelayTime.BackColor = Color.White;
      this.newTextBox_VavDelayTime.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_VavDelayTime.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_VavDelayTime.IsDigit = true;
      this.newTextBox_VavDelayTime.Lines = new string[1]
      {
        "15"
      };
      this.newTextBox_VavDelayTime.Location = new Point(0, 198);
      this.newTextBox_VavDelayTime.MultiLine = false;
      this.newTextBox_VavDelayTime.Name = "newTextBox_VavDelayTime";
      this.newTextBox_VavDelayTime.ReadOnly = false;
      this.newTextBox_VavDelayTime.Size = new Size(193, 23);
      this.newTextBox_VavDelayTime.TabIndex = 51;
      this.newTextBox_VavDelayTime.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_VavDelayTime.TextBoxBackColor = Color.White;
      this.newTextBox_VavDelayTime.TextForeColor = SystemColors.WindowText;
      this.newTextBox_VavDelayTime.Value = "15";
      this.newTextBox_VavDelayTime.TextBoxKeyUp += new KeyEventHandler(this.newTextBox_Vav_TextBoxKeyUp);
      this.label_VavDelayTime.BackColor = Color.Lavender;
      this.label_VavDelayTime.BorderStyle = BorderStyle.FixedSingle;
      this.label_VavDelayTime.Location = new Point(0, 176);
      this.label_VavDelayTime.Name = "label_VavDelayTime";
      this.label_VavDelayTime.Size = new Size(193, 23);
      this.label_VavDelayTime.TabIndex = 50;
      this.label_VavDelayTime.Text = "Delay Time";
      this.label_VavDelayTime.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_VavClose1.BackColor = Color.White;
      this.newTextBox_VavClose1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_VavClose1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_VavClose1.IsDigit = true;
      this.newTextBox_VavClose1.Lines = new string[1]
      {
        "15"
      };
      this.newTextBox_VavClose1.Location = new Point(96, 154);
      this.newTextBox_VavClose1.MultiLine = false;
      this.newTextBox_VavClose1.Name = "newTextBox_VavClose1";
      this.newTextBox_VavClose1.ReadOnly = false;
      this.newTextBox_VavClose1.Size = new Size(97, 23);
      this.newTextBox_VavClose1.TabIndex = 23;
      this.newTextBox_VavClose1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_VavClose1.TextBoxBackColor = Color.White;
      this.newTextBox_VavClose1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_VavClose1.Value = "15";
      this.newTextBox_VavClose1.TextBoxKeyUp += new KeyEventHandler(this.newTextBox_Vav_TextBoxKeyUp);
      this.newTextBox_VavOpen1.BackColor = Color.White;
      this.newTextBox_VavOpen1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_VavOpen1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_VavOpen1.IsDigit = true;
      this.newTextBox_VavOpen1.Lines = new string[1]{ "0" };
      this.newTextBox_VavOpen1.Location = new Point(0, 154);
      this.newTextBox_VavOpen1.MultiLine = false;
      this.newTextBox_VavOpen1.Name = "newTextBox_VavOpen1";
      this.newTextBox_VavOpen1.ReadOnly = false;
      this.newTextBox_VavOpen1.Size = new Size(97, 23);
      this.newTextBox_VavOpen1.TabIndex = 22;
      this.newTextBox_VavOpen1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_VavOpen1.TextBoxBackColor = Color.White;
      this.newTextBox_VavOpen1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_VavOpen1.Value = "0";
      this.newTextBox_VavOpen1.TextBoxKeyUp += new KeyEventHandler(this.newTextBox_Vav_TextBoxKeyUp);
      this.newComboBox_VavState.BackColor = Color.White;
      this.newComboBox_VavState.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_VavState.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_VavState.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_VavState.isSameSelect = false;
      this.newComboBox_VavState.Location = new Point(0, 66);
      this.newComboBox_VavState.Name = "newComboBox_VavState";
      this.newComboBox_VavState.SelectedIndex = -1;
      this.newComboBox_VavState.Size = new Size(193, 23);
      this.newComboBox_VavState.TabIndex = 20;
      this.newComboBox_VavState.TabStop = false;
      this.newComboBox_VavState.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_VavState.Value = "";
      this.newComboBox_VavState.SelectedValueChanged += new EventHandler(this.newComboBox_Pin_VavState_SelectedValueChanged);
      this.newTextBox_VavNode.BackColor = Color.White;
      this.newTextBox_VavNode.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_VavNode.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_VavNode.IsDigit = true;
      this.newTextBox_VavNode.Lines = new string[1]{ "1" };
      this.newTextBox_VavNode.Location = new Point(96, 110);
      this.newTextBox_VavNode.MultiLine = false;
      this.newTextBox_VavNode.Name = "newTextBox_VavNode";
      this.newTextBox_VavNode.ReadOnly = false;
      this.newTextBox_VavNode.Size = new Size(97, 23);
      this.newTextBox_VavNode.TabIndex = 21;
      this.newTextBox_VavNode.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_VavNode.TextBoxBackColor = Color.White;
      this.newTextBox_VavNode.TextForeColor = SystemColors.WindowText;
      this.newTextBox_VavNode.Value = "1";
      this.newTextBox_VavNode.TextBoxKeyUp += new KeyEventHandler(this.newTextBox_Vav_TextBoxKeyUp);
      this.newComboBox_VavTrigger.BackColor = Color.White;
      this.newComboBox_VavTrigger.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_VavTrigger.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_VavTrigger.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_VavTrigger.isSameSelect = false;
      this.newComboBox_VavTrigger.Location = new Point(0, 22);
      this.newComboBox_VavTrigger.Name = "newComboBox_VavTrigger";
      this.newComboBox_VavTrigger.SelectedIndex = -1;
      this.newComboBox_VavTrigger.Size = new Size(193, 23);
      this.newComboBox_VavTrigger.TabIndex = 19;
      this.newComboBox_VavTrigger.TabStop = false;
      this.newComboBox_VavTrigger.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_VavTrigger.Value = "";
      this.newComboBox_VavTrigger.SelectedValueChanged += new EventHandler(this.newComboBox_Pin_VavTrigger_SelectedValueChanged);
      this.newButton_VavNode.BackColor = Color.LavenderBlush;
      this.newButton_VavNode.ButtonBackColor = Color.LavenderBlush;
      this.newButton_VavNode.ButtonText = "Specified Node";
      this.newButton_VavNode.FlatBorderSize = 1;
      this.newButton_VavNode.FlatStyle = FlatStyle.Flat;
      this.newButton_VavNode.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_VavNode.ForeColor = SystemColors.ControlText;
      this.newButton_VavNode.Image = (Image) null;
      this.newButton_VavNode.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_VavNode.Location = new Point(0, 108);
      this.newButton_VavNode.Margin = new Padding(3, 0, 3, 0);
      this.newButton_VavNode.Name = "newButton_VavNode";
      this.newButton_VavNode.Size = new Size(98, 27);
      this.newButton_VavNode.TabIndex = 49;
      this.newButton_VavNode.TabStop = false;
      this.newButton_VavNode.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_VavNode.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_VavNode.NewClick += new EventHandler(this.newButton_Runner_NewClick);
      this.label_CenterPoint.BackColor = Color.FromArgb(229, 238, 248);
      this.label_CenterPoint.BorderStyle = BorderStyle.FixedSingle;
      this.label_CenterPoint.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_CenterPoint.ForeColor = Color.MidnightBlue;
      this.label_CenterPoint.Location = new Point(333, 200);
      this.label_CenterPoint.Name = "label_CenterPoint";
      this.label_CenterPoint.Size = new Size(331, 20);
      this.label_CenterPoint.TabIndex = 121;
      this.label_CenterPoint.Text = "[중심좌표]";
      this.label_CenterPoint.TextAlign = ContentAlignment.MiddleCenter;
      this.label_CenterX.BackColor = Color.Lavender;
      this.label_CenterX.BorderStyle = BorderStyle.FixedSingle;
      this.label_CenterX.Location = new Point(334, 219);
      this.label_CenterX.Name = "label_CenterX";
      this.label_CenterX.Size = new Size(114, 23);
      this.label_CenterX.TabIndex = 122;
      this.label_CenterX.Text = "X";
      this.label_CenterX.TextAlign = ContentAlignment.MiddleCenter;
      this.label_CenterY.BackColor = Color.Lavender;
      this.label_CenterY.BorderStyle = BorderStyle.FixedSingle;
      this.label_CenterY.Location = new Point(498, 219);
      this.label_CenterY.Name = "label_CenterY";
      this.label_CenterY.Size = new Size(114, 23);
      this.label_CenterY.TabIndex = 124;
      this.label_CenterY.Text = "Y";
      this.label_CenterY.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Hor_Two_Dim1.BackColor = Color.Lavender;
      this.label_Hor_Two_Dim1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Hor_Two_Dim1.Location = new Point(5, 92);
      this.label_Hor_Two_Dim1.Name = "label_Hor_Two_Dim1";
      this.label_Hor_Two_Dim1.Size = new Size(285, 23);
      this.label_Hor_Two_Dim1.TabIndex = 126;
      this.label_Hor_Two_Dim1.Text = "[ X ] 길이";
      this.label_Hor_Two_Dim1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Hor_Two_Dim2.BackColor = Color.Lavender;
      this.label_Hor_Two_Dim2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Hor_Two_Dim2.Location = new Point(5, 114);
      this.label_Hor_Two_Dim2.Name = "label_Hor_Two_Dim2";
      this.label_Hor_Two_Dim2.Size = new Size(285, 23);
      this.label_Hor_Two_Dim2.TabIndex = (int) sbyte.MaxValue;
      this.label_Hor_Two_Dim2.Text = "[ Y ] 길이";
      this.label_Hor_Two_Dim2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Hor_Two_Angle.BackColor = Color.Lavender;
      this.label_Hor_Two_Angle.BorderStyle = BorderStyle.FixedSingle;
      this.label_Hor_Two_Angle.Location = new Point(5, 136);
      this.label_Hor_Two_Angle.Name = "label_Hor_Two_Angle";
      this.label_Hor_Two_Angle.Size = new Size(285, 23);
      this.label_Hor_Two_Angle.TabIndex = 128;
      this.label_Hor_Two_Angle.Text = "∠ 각도";
      this.label_Hor_Two_Angle.TextAlign = ContentAlignment.MiddleCenter;
      this.toolTip_Hor_Angle.ShowAlways = true;
      this.newTextBox_Hor_Two_Angle.BackColor = SystemColors.Window;
      this.newTextBox_Hor_Two_Angle.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Hor_Two_Angle.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Hor_Two_Angle.IsDigit = true;
      this.newTextBox_Hor_Two_Angle.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Hor_Two_Angle.Location = new Point(288, 136);
      this.newTextBox_Hor_Two_Angle.MultiLine = false;
      this.newTextBox_Hor_Two_Angle.Name = "newTextBox_Hor_Two_Angle";
      this.newTextBox_Hor_Two_Angle.ReadOnly = false;
      this.newTextBox_Hor_Two_Angle.Size = new Size(47, 23);
      this.newTextBox_Hor_Two_Angle.TabIndex = 64;
      this.newTextBox_Hor_Two_Angle.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Hor_Two_Angle.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Hor_Two_Angle.TextForeColor = SystemColors.WindowText;
      this.toolTip_Hor_Angle.SetToolTip((Control) this.newTextBox_Hor_Two_Angle, "30~60");
      this.newTextBox_Hor_Two_Angle.Value = "0";
      this.checkBox_Hor_Two_Inter.BackColor = SystemColors.Window;
      this.checkBox_Hor_Two_Inter.CheckAlign = ContentAlignment.MiddleCenter;
      this.checkBox_Hor_Two_Inter.Checked = true;
      this.checkBox_Hor_Two_Inter.CheckState = CheckState.Checked;
      this.checkBox_Hor_Two_Inter.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.checkBox_Hor_Two_Inter.Location = new Point(293, 94);
      this.checkBox_Hor_Two_Inter.Name = "checkBox_Hor_Two_Inter";
      this.checkBox_Hor_Two_Inter.Size = new Size(40, 20);
      this.checkBox_Hor_Two_Inter.TabIndex = 132;
      this.checkBox_Hor_Two_Inter.UseVisualStyleBackColor = false;
      this.checkBox_Hor_Two_Inter.Visible = false;
      this.label_Hor_Two_Direction.BackColor = Color.Lavender;
      this.label_Hor_Two_Direction.BorderStyle = BorderStyle.FixedSingle;
      this.label_Hor_Two_Direction.Location = new Point(5, 48);
      this.label_Hor_Two_Direction.Name = "label_Hor_Two_Direction";
      this.label_Hor_Two_Direction.Size = new Size(285, 23);
      this.label_Hor_Two_Direction.TabIndex = 133;
      this.label_Hor_Two_Direction.Text = "수평 런너 방향";
      this.label_Hor_Two_Direction.TextAlign = ContentAlignment.MiddleCenter;
      this.tabControl_Group.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
      this.tabControl_Group.Controls.Add((Control) this.tabPage_G1);
      this.tabControl_Group.Controls.Add((Control) this.tabPage_G2);
      this.tabControl_Group.Controls.Add((Control) this.tabPage_G3);
      this.tabControl_Group.Controls.Add((Control) this.tabPage_G4);
      this.tabControl_Group.Location = new Point(-5, -5);
      this.tabControl_Group.Name = "tabControl_Group";
      this.tabControl_Group.SelectedIndex = 0;
      this.tabControl_Group.Size = new Size(670, 208);
      this.tabControl_Group.TabIndex = 29;
      this.tabPage_G1.Controls.Add((Control) this.label_Side_RunnerLength_G1);
      this.tabPage_G1.Controls.Add((Control) this.label_Side_RunnerDim2_G1);
      this.tabPage_G1.Controls.Add((Control) this.label_Side_RunnerDim4_G1);
      this.tabPage_G1.Controls.Add((Control) this.label_Side_RunnerDim1_G1);
      this.tabPage_G1.Controls.Add((Control) this.label_Side_RunnerDim3_G1);
      this.tabPage_G1.Controls.Add((Control) this.label_Pin_GateDim2_G1);
      this.tabPage_G1.Controls.Add((Control) this.label_Pin_GateDim1_G1);
      this.tabPage_G1.Controls.Add((Control) this.newButton_Pin_GateLength_G1);
      this.tabPage_G1.Controls.Add((Control) this.label_Pin_RunnerDim2_G1);
      this.tabPage_G1.Controls.Add((Control) this.label_Pin_RunnerDim1_G1);
      this.tabPage_G1.Controls.Add((Control) this.label_Pin_RunnerLength_G1);
      this.tabPage_G1.Controls.Add((Control) this.label_Pin_Runner_G1);
      this.tabPage_G1.Controls.Add((Control) this.newTextBox_Pin_GateDim2_G1);
      this.tabPage_G1.Controls.Add((Control) this.newTextBox_Pin_GateDim1_G1);
      this.tabPage_G1.Controls.Add((Control) this.newTextBox_Pin_GateLength_G1);
      this.tabPage_G1.Controls.Add((Control) this.label_Pin_Gate_G1);
      this.tabPage_G1.Controls.Add((Control) this.label_Side_Runner_G1);
      this.tabPage_G1.Controls.Add((Control) this.newTextBox_Side_RunnerLength_G1);
      this.tabPage_G1.Controls.Add((Control) this.panel_Side_GateType1_G1);
      this.tabPage_G1.Controls.Add((Control) this.newTextBox_Side_RunnerDim4_G1);
      this.tabPage_G1.Controls.Add((Control) this.panel_Side_GateType2_G1);
      this.tabPage_G1.Controls.Add((Control) this.label_Side_Gate_G1);
      this.tabPage_G1.Controls.Add((Control) this.label_Side_GateLength_G1);
      this.tabPage_G1.Controls.Add((Control) this.newTextBox_Side_RunnerDim1_G1);
      this.tabPage_G1.Controls.Add((Control) this.newTextBox_Side_RunnerDim2_G1);
      this.tabPage_G1.Controls.Add((Control) this.label_Side_GateDim1_G1);
      this.tabPage_G1.Controls.Add((Control) this.newTextBox_Side_RunnerDim3_G1);
      this.tabPage_G1.Controls.Add((Control) this.label_Side_GateDim3_G1);
      this.tabPage_G1.Controls.Add((Control) this.label_Side_GateDim2_G1);
      this.tabPage_G1.Controls.Add((Control) this.newTextBox_Pin_RunnerLength_G1);
      this.tabPage_G1.Controls.Add((Control) this.newTextBox_Pin_RunnerDim1_G1);
      this.tabPage_G1.Controls.Add((Control) this.label_Side_GateDim4_G1);
      this.tabPage_G1.Controls.Add((Control) this.newTextBox_Pin_RunnerDim2_G1);
      this.tabPage_G1.Controls.Add((Control) this.newTextBox_Side_GateDim4_G1);
      this.tabPage_G1.Controls.Add((Control) this.newTextBox_Side_GateLength_G1);
      this.tabPage_G1.Controls.Add((Control) this.newTextBox_Side_GateDim3_G1);
      this.tabPage_G1.Controls.Add((Control) this.newTextBox_Side_GateDim1_G1);
      this.tabPage_G1.Controls.Add((Control) this.newTextBox_Side_GateDim2_G1);
      this.tabPage_G1.Controls.Add((Control) this.panel_Side_RunnerType_G1);
      this.tabPage_G1.Location = new Point(4, 24);
      this.tabPage_G1.Name = "tabPage_G1";
      this.tabPage_G1.Padding = new Padding(3);
      this.tabPage_G1.Size = new Size(662, 180);
      this.tabPage_G1.TabIndex = 0;
      this.tabPage_G1.UseVisualStyleBackColor = true;
      this.label_Side_RunnerLength_G1.BackColor = Color.Lavender;
      this.label_Side_RunnerLength_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerLength_G1.Location = new Point(166, 66);
      this.label_Side_RunnerLength_G1.Name = "label_Side_RunnerLength_G1";
      this.label_Side_RunnerLength_G1.Size = new Size(120, 23);
      this.label_Side_RunnerLength_G1.TabIndex = 145;
      this.label_Side_RunnerLength_G1.Text = "런너 길이";
      this.label_Side_RunnerLength_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim2_G1.BackColor = Color.Lavender;
      this.label_Side_RunnerDim2_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim2_G1.Location = new Point(166, 110);
      this.label_Side_RunnerDim2_G1.Name = "label_Side_RunnerDim2_G1";
      this.label_Side_RunnerDim2_G1.Size = new Size(120, 23);
      this.label_Side_RunnerDim2_G1.TabIndex = 143;
      this.label_Side_RunnerDim2_G1.Text = "Bottom Width";
      this.label_Side_RunnerDim2_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim4_G1.BackColor = Color.Lavender;
      this.label_Side_RunnerDim4_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim4_G1.Location = new Point(166, 154);
      this.label_Side_RunnerDim4_G1.Name = "label_Side_RunnerDim4_G1";
      this.label_Side_RunnerDim4_G1.Size = new Size(120, 23);
      this.label_Side_RunnerDim4_G1.TabIndex = 160;
      this.label_Side_RunnerDim4_G1.Text = "End Height";
      this.label_Side_RunnerDim4_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim1_G1.BackColor = Color.Lavender;
      this.label_Side_RunnerDim1_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim1_G1.Location = new Point(166, 88);
      this.label_Side_RunnerDim1_G1.Name = "label_Side_RunnerDim1_G1";
      this.label_Side_RunnerDim1_G1.Size = new Size(120, 23);
      this.label_Side_RunnerDim1_G1.TabIndex = 147;
      this.label_Side_RunnerDim1_G1.Text = "Top Width";
      this.label_Side_RunnerDim1_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim3_G1.BackColor = Color.Lavender;
      this.label_Side_RunnerDim3_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim3_G1.Location = new Point(166, 132);
      this.label_Side_RunnerDim3_G1.Name = "label_Side_RunnerDim3_G1";
      this.label_Side_RunnerDim3_G1.Size = new Size(120, 23);
      this.label_Side_RunnerDim3_G1.TabIndex = 149;
      this.label_Side_RunnerDim3_G1.Text = "Height";
      this.label_Side_RunnerDim3_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_GateDim2_G1.BackColor = Color.Lavender;
      this.label_Pin_GateDim2_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_GateDim2_G1.Location = new Point(330, 66);
      this.label_Pin_GateDim2_G1.Name = "label_Pin_GateDim2_G1";
      this.label_Pin_GateDim2_G1.Size = new Size(120, 23);
      this.label_Pin_GateDim2_G1.TabIndex = 101;
      this.label_Pin_GateDim2_G1.Text = "End Dimension";
      this.label_Pin_GateDim2_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_GateDim1_G1.BackColor = Color.Lavender;
      this.label_Pin_GateDim1_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_GateDim1_G1.Location = new Point(330, 44);
      this.label_Pin_GateDim1_G1.Name = "label_Pin_GateDim1_G1";
      this.label_Pin_GateDim1_G1.Size = new Size(120, 23);
      this.label_Pin_GateDim1_G1.TabIndex = 104;
      this.label_Pin_GateDim1_G1.Text = "Start Dimension";
      this.label_Pin_GateDim1_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Pin_GateLength_G1.BackColor = Color.Lavender;
      this.newButton_Pin_GateLength_G1.ButtonBackColor = Color.Lavender;
      this.newButton_Pin_GateLength_G1.ButtonText = "게이트 길이";
      this.newButton_Pin_GateLength_G1.FlatBorderSize = 1;
      this.newButton_Pin_GateLength_G1.FlatStyle = FlatStyle.Flat;
      this.newButton_Pin_GateLength_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Pin_GateLength_G1.ForeColor = SystemColors.ControlText;
      this.newButton_Pin_GateLength_G1.Image = (Image) null;
      this.newButton_Pin_GateLength_G1.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Pin_GateLength_G1.Location = new Point(330, 22);
      this.newButton_Pin_GateLength_G1.Margin = new Padding(3, 0, 3, 0);
      this.newButton_Pin_GateLength_G1.Name = "newButton_Pin_GateLength_G1";
      this.newButton_Pin_GateLength_G1.Size = new Size(120, 23);
      this.newButton_Pin_GateLength_G1.TabIndex = 106;
      this.newButton_Pin_GateLength_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Pin_GateLength_G1.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Pin_GateLength_G1.NewClick += new EventHandler(this.newButton_Pin_GateLength_NewClick);
      this.label_Pin_RunnerDim2_G1.BackColor = Color.Lavender;
      this.label_Pin_RunnerDim2_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_RunnerDim2_G1.Location = new Point(495, 66);
      this.label_Pin_RunnerDim2_G1.Name = "label_Pin_RunnerDim2_G1";
      this.label_Pin_RunnerDim2_G1.Size = new Size(120, 23);
      this.label_Pin_RunnerDim2_G1.TabIndex = 105;
      this.label_Pin_RunnerDim2_G1.Text = "Outer Dimension";
      this.label_Pin_RunnerDim2_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_RunnerDim1_G1.BackColor = Color.Lavender;
      this.label_Pin_RunnerDim1_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_RunnerDim1_G1.Location = new Point(495, 44);
      this.label_Pin_RunnerDim1_G1.Name = "label_Pin_RunnerDim1_G1";
      this.label_Pin_RunnerDim1_G1.Size = new Size(120, 23);
      this.label_Pin_RunnerDim1_G1.TabIndex = 103;
      this.label_Pin_RunnerDim1_G1.Text = "Inner Dimension";
      this.label_Pin_RunnerDim1_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_RunnerLength_G1.BackColor = Color.Lavender;
      this.label_Pin_RunnerLength_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_RunnerLength_G1.Location = new Point(495, 22);
      this.label_Pin_RunnerLength_G1.Name = "label_Pin_RunnerLength_G1";
      this.label_Pin_RunnerLength_G1.Size = new Size(120, 23);
      this.label_Pin_RunnerLength_G1.TabIndex = 102;
      this.label_Pin_RunnerLength_G1.Text = "런너 길이";
      this.label_Pin_RunnerLength_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_Runner_G1.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Pin_Runner_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_Runner_G1.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Pin_Runner_G1.ForeColor = Color.MidnightBlue;
      this.label_Pin_Runner_G1.Location = new Point(495, 3);
      this.label_Pin_Runner_G1.Name = "label_Pin_Runner_G1";
      this.label_Pin_Runner_G1.Size = new Size(166, 20);
      this.label_Pin_Runner_G1.TabIndex = 99;
      this.label_Pin_Runner_G1.Text = "<핀 런너>";
      this.label_Pin_Runner_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Pin_GateDim2_G1.BackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim2_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_GateDim2_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_GateDim2_G1.IsDigit = true;
      this.newTextBox_Pin_GateDim2_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_GateDim2_G1.Location = new Point(449, 66);
      this.newTextBox_Pin_GateDim2_G1.MultiLine = false;
      this.newTextBox_Pin_GateDim2_G1.Name = "newTextBox_Pin_GateDim2_G1";
      this.newTextBox_Pin_GateDim2_G1.ReadOnly = false;
      this.newTextBox_Pin_GateDim2_G1.Size = new Size(47, 23);
      this.newTextBox_Pin_GateDim2_G1.TabIndex = 32;
      this.newTextBox_Pin_GateDim2_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_GateDim2_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim2_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_GateDim2_G1.Value = "0";
      this.newTextBox_Pin_GateDim1_G1.BackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim1_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_GateDim1_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_GateDim1_G1.IsDigit = true;
      this.newTextBox_Pin_GateDim1_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_GateDim1_G1.Location = new Point(449, 44);
      this.newTextBox_Pin_GateDim1_G1.MultiLine = false;
      this.newTextBox_Pin_GateDim1_G1.Name = "newTextBox_Pin_GateDim1_G1";
      this.newTextBox_Pin_GateDim1_G1.ReadOnly = false;
      this.newTextBox_Pin_GateDim1_G1.Size = new Size(47, 23);
      this.newTextBox_Pin_GateDim1_G1.TabIndex = 31;
      this.newTextBox_Pin_GateDim1_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_GateDim1_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim1_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_GateDim1_G1.Value = "0";
      this.newTextBox_Pin_GateLength_G1.BackColor = SystemColors.Window;
      this.newTextBox_Pin_GateLength_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_GateLength_G1.Enabled = false;
      this.newTextBox_Pin_GateLength_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_GateLength_G1.IsDigit = true;
      this.newTextBox_Pin_GateLength_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_GateLength_G1.Location = new Point(449, 22);
      this.newTextBox_Pin_GateLength_G1.MultiLine = false;
      this.newTextBox_Pin_GateLength_G1.Name = "newTextBox_Pin_GateLength_G1";
      this.newTextBox_Pin_GateLength_G1.ReadOnly = true;
      this.newTextBox_Pin_GateLength_G1.Size = new Size(47, 23);
      this.newTextBox_Pin_GateLength_G1.TabIndex = 30;
      this.newTextBox_Pin_GateLength_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_GateLength_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_GateLength_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_GateLength_G1.Value = "0";
      this.label_Pin_Gate_G1.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Pin_Gate_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_Gate_G1.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Pin_Gate_G1.ForeColor = Color.MidnightBlue;
      this.label_Pin_Gate_G1.Location = new Point(330, 3);
      this.label_Pin_Gate_G1.Name = "label_Pin_Gate_G1";
      this.label_Pin_Gate_G1.Size = new Size(166, 20);
      this.label_Pin_Gate_G1.TabIndex = 100;
      this.label_Pin_Gate_G1.Text = "<핀 게이트>";
      this.label_Pin_Gate_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_Runner_G1.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Side_Runner_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_Runner_G1.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Side_Runner_G1.ForeColor = Color.MidnightBlue;
      this.label_Side_Runner_G1.Location = new Point(166, 3);
      this.label_Side_Runner_G1.Name = "label_Side_Runner_G1";
      this.label_Side_Runner_G1.Size = new Size(166, 20);
      this.label_Side_Runner_G1.TabIndex = 137;
      this.label_Side_Runner_G1.Text = "<사이드 런너>";
      this.label_Side_Runner_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Side_RunnerLength_G1.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerLength_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerLength_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerLength_G1.IsDigit = true;
      this.newTextBox_Side_RunnerLength_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerLength_G1.Location = new Point(284, 66);
      this.newTextBox_Side_RunnerLength_G1.MultiLine = false;
      this.newTextBox_Side_RunnerLength_G1.Name = "newTextBox_Side_RunnerLength_G1";
      this.newTextBox_Side_RunnerLength_G1.ReadOnly = false;
      this.newTextBox_Side_RunnerLength_G1.Size = new Size(47, 23);
      this.newTextBox_Side_RunnerLength_G1.TabIndex = 97;
      this.newTextBox_Side_RunnerLength_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerLength_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerLength_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerLength_G1.Value = "0";
      this.panel_Side_GateType1_G1.BackColor = Color.White;
      this.panel_Side_GateType1_G1.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Side_GateType1_G1.Controls.Add((Control) this.radioButton_Side_GateCircle_G1);
      this.panel_Side_GateType1_G1.Controls.Add((Control) this.radioButton_Side_GateRect_G1);
      this.panel_Side_GateType1_G1.Location = new Point(1, 22);
      this.panel_Side_GateType1_G1.Name = "panel_Side_GateType1_G1";
      this.panel_Side_GateType1_G1.Size = new Size(166, 23);
      this.panel_Side_GateType1_G1.TabIndex = 82;
      this.radioButton_Side_GateCircle_G1.AutoSize = true;
      this.radioButton_Side_GateCircle_G1.Location = new Point(85, 2);
      this.radioButton_Side_GateCircle_G1.Name = "radioButton_Side_GateCircle_G1";
      this.radioButton_Side_GateCircle_G1.Size = new Size(55, 19);
      this.radioButton_Side_GateCircle_G1.TabIndex = 84;
      this.radioButton_Side_GateCircle_G1.Text = "Circle";
      this.radioButton_Side_GateCircle_G1.UseVisualStyleBackColor = true;
      this.radioButton_Side_GateCircle_G1.CheckedChanged += new EventHandler(this.radioButton_Side_GateCircle_CheckedChanged);
      this.radioButton_Side_GateRect_G1.AutoSize = true;
      this.radioButton_Side_GateRect_G1.Location = new Point(7, 2);
      this.radioButton_Side_GateRect_G1.Name = "radioButton_Side_GateRect_G1";
      this.radioButton_Side_GateRect_G1.Size = new Size(77, 19);
      this.radioButton_Side_GateRect_G1.TabIndex = 83;
      this.radioButton_Side_GateRect_G1.Text = "Rectangle";
      this.radioButton_Side_GateRect_G1.UseVisualStyleBackColor = true;
      this.radioButton_Side_GateRect_G1.CheckedChanged += new EventHandler(this.radioButton_Side_GateRect_CheckedChanged);
      this.newTextBox_Side_RunnerDim4_G1.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim4_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim4_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim4_G1.IsDigit = true;
      this.newTextBox_Side_RunnerDim4_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim4_G1.Location = new Point(284, 154);
      this.newTextBox_Side_RunnerDim4_G1.MultiLine = false;
      this.newTextBox_Side_RunnerDim4_G1.Name = "newTextBox_Side_RunnerDim4_G1";
      this.newTextBox_Side_RunnerDim4_G1.ReadOnly = false;
      this.newTextBox_Side_RunnerDim4_G1.Size = new Size(47, 23);
      this.newTextBox_Side_RunnerDim4_G1.TabIndex = 101;
      this.newTextBox_Side_RunnerDim4_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim4_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim4_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim4_G1.Value = "0";
      this.panel_Side_GateType2_G1.BackColor = Color.White;
      this.panel_Side_GateType2_G1.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Side_GateType2_G1.Controls.Add((Control) this.radioButton_Side_GateSubMarine_G1);
      this.panel_Side_GateType2_G1.Controls.Add((Control) this.radioButton_Side_GateNormal_G1);
      this.panel_Side_GateType2_G1.Location = new Point(1, 44);
      this.panel_Side_GateType2_G1.Name = "panel_Side_GateType2_G1";
      this.panel_Side_GateType2_G1.Size = new Size(166, 23);
      this.panel_Side_GateType2_G1.TabIndex = 85;
      this.radioButton_Side_GateSubMarine_G1.AutoSize = true;
      this.radioButton_Side_GateSubMarine_G1.Location = new Point(85, 2);
      this.radioButton_Side_GateSubMarine_G1.Name = "radioButton_Side_GateSubMarine_G1";
      this.radioButton_Side_GateSubMarine_G1.Size = new Size(73, 19);
      this.radioButton_Side_GateSubMarine_G1.TabIndex = 87;
      this.radioButton_Side_GateSubMarine_G1.Text = "서브마린";
      this.radioButton_Side_GateSubMarine_G1.UseVisualStyleBackColor = true;
      this.radioButton_Side_GateNormal_G1.AutoSize = true;
      this.radioButton_Side_GateNormal_G1.Location = new Point(7, 2);
      this.radioButton_Side_GateNormal_G1.Name = "radioButton_Side_GateNormal_G1";
      this.radioButton_Side_GateNormal_G1.Size = new Size(49, 19);
      this.radioButton_Side_GateNormal_G1.TabIndex = 86;
      this.radioButton_Side_GateNormal_G1.Text = "일반";
      this.radioButton_Side_GateNormal_G1.UseVisualStyleBackColor = true;
      this.label_Side_Gate_G1.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Side_Gate_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_Gate_G1.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Side_Gate_G1.ForeColor = Color.MidnightBlue;
      this.label_Side_Gate_G1.Location = new Point(1, 3);
      this.label_Side_Gate_G1.Name = "label_Side_Gate_G1";
      this.label_Side_Gate_G1.Size = new Size(166, 20);
      this.label_Side_Gate_G1.TabIndex = 138;
      this.label_Side_Gate_G1.Text = "<사이드 게이트>";
      this.label_Side_Gate_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateLength_G1.BackColor = Color.Lavender;
      this.label_Side_GateLength_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateLength_G1.Location = new Point(1, 66);
      this.label_Side_GateLength_G1.Name = "label_Side_GateLength_G1";
      this.label_Side_GateLength_G1.Size = new Size(120, 23);
      this.label_Side_GateLength_G1.TabIndex = 148;
      this.label_Side_GateLength_G1.Text = "게이트 길이";
      this.label_Side_GateLength_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Side_RunnerDim1_G1.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim1_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim1_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim1_G1.IsDigit = true;
      this.newTextBox_Side_RunnerDim1_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim1_G1.Location = new Point(284, 88);
      this.newTextBox_Side_RunnerDim1_G1.MultiLine = false;
      this.newTextBox_Side_RunnerDim1_G1.Name = "newTextBox_Side_RunnerDim1_G1";
      this.newTextBox_Side_RunnerDim1_G1.ReadOnly = false;
      this.newTextBox_Side_RunnerDim1_G1.Size = new Size(47, 23);
      this.newTextBox_Side_RunnerDim1_G1.TabIndex = 98;
      this.newTextBox_Side_RunnerDim1_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim1_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim1_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim1_G1.Value = "0";
      this.newTextBox_Side_RunnerDim2_G1.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim2_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim2_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim2_G1.IsDigit = true;
      this.newTextBox_Side_RunnerDim2_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim2_G1.Location = new Point(284, 110);
      this.newTextBox_Side_RunnerDim2_G1.MultiLine = false;
      this.newTextBox_Side_RunnerDim2_G1.Name = "newTextBox_Side_RunnerDim2_G1";
      this.newTextBox_Side_RunnerDim2_G1.ReadOnly = false;
      this.newTextBox_Side_RunnerDim2_G1.Size = new Size(47, 23);
      this.newTextBox_Side_RunnerDim2_G1.TabIndex = 99;
      this.newTextBox_Side_RunnerDim2_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim2_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim2_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim2_G1.Value = "0";
      this.label_Side_GateDim1_G1.BackColor = Color.Lavender;
      this.label_Side_GateDim1_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim1_G1.Location = new Point(1, 88);
      this.label_Side_GateDim1_G1.Name = "label_Side_GateDim1_G1";
      this.label_Side_GateDim1_G1.Size = new Size(120, 23);
      this.label_Side_GateDim1_G1.TabIndex = 146;
      this.label_Side_GateDim1_G1.Text = "Start Width";
      this.label_Side_GateDim1_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Side_RunnerDim3_G1.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim3_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim3_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim3_G1.IsDigit = true;
      this.newTextBox_Side_RunnerDim3_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim3_G1.Location = new Point(284, 132);
      this.newTextBox_Side_RunnerDim3_G1.MultiLine = false;
      this.newTextBox_Side_RunnerDim3_G1.Name = "newTextBox_Side_RunnerDim3_G1";
      this.newTextBox_Side_RunnerDim3_G1.ReadOnly = false;
      this.newTextBox_Side_RunnerDim3_G1.Size = new Size(47, 23);
      this.newTextBox_Side_RunnerDim3_G1.TabIndex = 100;
      this.newTextBox_Side_RunnerDim3_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim3_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim3_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim3_G1.Value = "0";
      this.label_Side_GateDim3_G1.BackColor = Color.Lavender;
      this.label_Side_GateDim3_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim3_G1.Location = new Point(1, 132);
      this.label_Side_GateDim3_G1.Name = "label_Side_GateDim3_G1";
      this.label_Side_GateDim3_G1.Size = new Size(120, 23);
      this.label_Side_GateDim3_G1.TabIndex = 144;
      this.label_Side_GateDim3_G1.Text = "End Width";
      this.label_Side_GateDim3_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim2_G1.BackColor = Color.Lavender;
      this.label_Side_GateDim2_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim2_G1.Location = new Point(1, 110);
      this.label_Side_GateDim2_G1.Name = "label_Side_GateDim2_G1";
      this.label_Side_GateDim2_G1.Size = new Size(120, 23);
      this.label_Side_GateDim2_G1.TabIndex = 142;
      this.label_Side_GateDim2_G1.Text = "Start Height";
      this.label_Side_GateDim2_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Pin_RunnerLength_G1.BackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerLength_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_RunnerLength_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_RunnerLength_G1.IsDigit = true;
      this.newTextBox_Pin_RunnerLength_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_RunnerLength_G1.Location = new Point(614, 22);
      this.newTextBox_Pin_RunnerLength_G1.MultiLine = false;
      this.newTextBox_Pin_RunnerLength_G1.Name = "newTextBox_Pin_RunnerLength_G1";
      this.newTextBox_Pin_RunnerLength_G1.ReadOnly = false;
      this.newTextBox_Pin_RunnerLength_G1.Size = new Size(47, 23);
      this.newTextBox_Pin_RunnerLength_G1.TabIndex = 33;
      this.newTextBox_Pin_RunnerLength_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_RunnerLength_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerLength_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_RunnerLength_G1.Value = "0";
      this.newTextBox_Pin_RunnerDim1_G1.BackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim1_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_RunnerDim1_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_RunnerDim1_G1.IsDigit = true;
      this.newTextBox_Pin_RunnerDim1_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_RunnerDim1_G1.Location = new Point(614, 44);
      this.newTextBox_Pin_RunnerDim1_G1.MultiLine = false;
      this.newTextBox_Pin_RunnerDim1_G1.Name = "newTextBox_Pin_RunnerDim1_G1";
      this.newTextBox_Pin_RunnerDim1_G1.ReadOnly = false;
      this.newTextBox_Pin_RunnerDim1_G1.Size = new Size(47, 23);
      this.newTextBox_Pin_RunnerDim1_G1.TabIndex = 34;
      this.newTextBox_Pin_RunnerDim1_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_RunnerDim1_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim1_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_RunnerDim1_G1.Value = "0";
      this.label_Side_GateDim4_G1.BackColor = Color.Lavender;
      this.label_Side_GateDim4_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim4_G1.Location = new Point(1, 154);
      this.label_Side_GateDim4_G1.Name = "label_Side_GateDim4_G1";
      this.label_Side_GateDim4_G1.Size = new Size(120, 23);
      this.label_Side_GateDim4_G1.TabIndex = 150;
      this.label_Side_GateDim4_G1.Text = "End Height";
      this.label_Side_GateDim4_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Pin_RunnerDim2_G1.BackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim2_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_RunnerDim2_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_RunnerDim2_G1.IsDigit = true;
      this.newTextBox_Pin_RunnerDim2_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_RunnerDim2_G1.Location = new Point(614, 66);
      this.newTextBox_Pin_RunnerDim2_G1.MultiLine = false;
      this.newTextBox_Pin_RunnerDim2_G1.Name = "newTextBox_Pin_RunnerDim2_G1";
      this.newTextBox_Pin_RunnerDim2_G1.ReadOnly = false;
      this.newTextBox_Pin_RunnerDim2_G1.Size = new Size(47, 23);
      this.newTextBox_Pin_RunnerDim2_G1.TabIndex = 35;
      this.newTextBox_Pin_RunnerDim2_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_RunnerDim2_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim2_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_RunnerDim2_G1.Value = "0";
      this.newTextBox_Side_GateDim4_G1.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim4_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim4_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim4_G1.IsDigit = true;
      this.newTextBox_Side_GateDim4_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim4_G1.Location = new Point(120, 154);
      this.newTextBox_Side_GateDim4_G1.MultiLine = false;
      this.newTextBox_Side_GateDim4_G1.Name = "newTextBox_Side_GateDim4_G1";
      this.newTextBox_Side_GateDim4_G1.ReadOnly = false;
      this.newTextBox_Side_GateDim4_G1.Size = new Size(47, 23);
      this.newTextBox_Side_GateDim4_G1.TabIndex = 92;
      this.newTextBox_Side_GateDim4_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim4_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim4_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim4_G1.Value = "0";
      this.newTextBox_Side_GateLength_G1.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateLength_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateLength_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateLength_G1.IsDigit = true;
      this.newTextBox_Side_GateLength_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateLength_G1.Location = new Point(120, 66);
      this.newTextBox_Side_GateLength_G1.MultiLine = false;
      this.newTextBox_Side_GateLength_G1.Name = "newTextBox_Side_GateLength_G1";
      this.newTextBox_Side_GateLength_G1.ReadOnly = false;
      this.newTextBox_Side_GateLength_G1.Size = new Size(47, 23);
      this.newTextBox_Side_GateLength_G1.TabIndex = 88;
      this.newTextBox_Side_GateLength_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateLength_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateLength_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateLength_G1.Value = "0";
      this.newTextBox_Side_GateDim3_G1.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim3_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim3_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim3_G1.IsDigit = true;
      this.newTextBox_Side_GateDim3_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim3_G1.Location = new Point(120, 132);
      this.newTextBox_Side_GateDim3_G1.MultiLine = false;
      this.newTextBox_Side_GateDim3_G1.Name = "newTextBox_Side_GateDim3_G1";
      this.newTextBox_Side_GateDim3_G1.ReadOnly = false;
      this.newTextBox_Side_GateDim3_G1.Size = new Size(47, 23);
      this.newTextBox_Side_GateDim3_G1.TabIndex = 91;
      this.newTextBox_Side_GateDim3_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim3_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim3_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim3_G1.Value = "0";
      this.newTextBox_Side_GateDim1_G1.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim1_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim1_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim1_G1.IsDigit = true;
      this.newTextBox_Side_GateDim1_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim1_G1.Location = new Point(120, 88);
      this.newTextBox_Side_GateDim1_G1.MultiLine = false;
      this.newTextBox_Side_GateDim1_G1.Name = "newTextBox_Side_GateDim1_G1";
      this.newTextBox_Side_GateDim1_G1.ReadOnly = false;
      this.newTextBox_Side_GateDim1_G1.Size = new Size(47, 23);
      this.newTextBox_Side_GateDim1_G1.TabIndex = 89;
      this.newTextBox_Side_GateDim1_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim1_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim1_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim1_G1.Value = "0";
      this.newTextBox_Side_GateDim2_G1.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim2_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim2_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim2_G1.IsDigit = true;
      this.newTextBox_Side_GateDim2_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim2_G1.Location = new Point(120, 110);
      this.newTextBox_Side_GateDim2_G1.MultiLine = false;
      this.newTextBox_Side_GateDim2_G1.Name = "newTextBox_Side_GateDim2_G1";
      this.newTextBox_Side_GateDim2_G1.ReadOnly = false;
      this.newTextBox_Side_GateDim2_G1.Size = new Size(47, 23);
      this.newTextBox_Side_GateDim2_G1.TabIndex = 90;
      this.newTextBox_Side_GateDim2_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim2_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim2_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim2_G1.Value = "0";
      this.panel_Side_RunnerType_G1.BackColor = Color.White;
      this.panel_Side_RunnerType_G1.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Side_RunnerType_G1.Controls.Add((Control) this.radioButton_Side_RunnerRectangle_G1);
      this.panel_Side_RunnerType_G1.Controls.Add((Control) this.radioButton_Side_RunnerCircle_G1);
      this.panel_Side_RunnerType_G1.Controls.Add((Control) this.radioButton_Side_RunnerTrepezoidal_G1);
      this.panel_Side_RunnerType_G1.Location = new Point(166, 22);
      this.panel_Side_RunnerType_G1.Name = "panel_Side_RunnerType_G1";
      this.panel_Side_RunnerType_G1.Size = new Size(166, 45);
      this.panel_Side_RunnerType_G1.TabIndex = 93;
      this.radioButton_Side_RunnerRectangle_G1.AutoSize = true;
      this.radioButton_Side_RunnerRectangle_G1.Location = new Point(6, 23);
      this.radioButton_Side_RunnerRectangle_G1.Name = "radioButton_Side_RunnerRectangle_G1";
      this.radioButton_Side_RunnerRectangle_G1.Size = new Size(77, 19);
      this.radioButton_Side_RunnerRectangle_G1.TabIndex = 96;
      this.radioButton_Side_RunnerRectangle_G1.Text = "Rectangle";
      this.radioButton_Side_RunnerRectangle_G1.UseVisualStyleBackColor = true;
      this.radioButton_Side_RunnerRectangle_G1.CheckedChanged += new EventHandler(this.radioButton_Side_RunnerRectangle_CheckedChanged);
      this.radioButton_Side_RunnerCircle_G1.AutoSize = true;
      this.radioButton_Side_RunnerCircle_G1.Location = new Point(96, 2);
      this.radioButton_Side_RunnerCircle_G1.Name = "radioButton_Side_RunnerCircle_G1";
      this.radioButton_Side_RunnerCircle_G1.Size = new Size(55, 19);
      this.radioButton_Side_RunnerCircle_G1.TabIndex = 95;
      this.radioButton_Side_RunnerCircle_G1.Text = "Circle";
      this.radioButton_Side_RunnerCircle_G1.UseVisualStyleBackColor = true;
      this.radioButton_Side_RunnerCircle_G1.CheckedChanged += new EventHandler(this.radioButton_Side_RunnerCircle_CheckedChanged);
      this.radioButton_Side_RunnerTrepezoidal_G1.AutoSize = true;
      this.radioButton_Side_RunnerTrepezoidal_G1.Location = new Point(6, 2);
      this.radioButton_Side_RunnerTrepezoidal_G1.Name = "radioButton_Side_RunnerTrepezoidal_G1";
      this.radioButton_Side_RunnerTrepezoidal_G1.Size = new Size(84, 19);
      this.radioButton_Side_RunnerTrepezoidal_G1.TabIndex = 94;
      this.radioButton_Side_RunnerTrepezoidal_G1.Text = "Trepezoidal";
      this.radioButton_Side_RunnerTrepezoidal_G1.UseVisualStyleBackColor = true;
      this.radioButton_Side_RunnerTrepezoidal_G1.CheckedChanged += new EventHandler(this.radioButton_Side_RunnerTrepezoidal_CheckedChanged);
      this.tabPage_G2.Controls.Add((Control) this.label_Side_RunnerLength_G2);
      this.tabPage_G2.Controls.Add((Control) this.label_Side_RunnerDim2_G2);
      this.tabPage_G2.Controls.Add((Control) this.label_Side_RunnerDim4_G2);
      this.tabPage_G2.Controls.Add((Control) this.label_Side_RunnerDim1_G2);
      this.tabPage_G2.Controls.Add((Control) this.label_Side_RunnerDim3_G2);
      this.tabPage_G2.Controls.Add((Control) this.label_Pin_GateDim1_G2);
      this.tabPage_G2.Controls.Add((Control) this.label_Pin_RunnerDim2_G2);
      this.tabPage_G2.Controls.Add((Control) this.label_Pin_RunnerDim1_G2);
      this.tabPage_G2.Controls.Add((Control) this.label_Pin_RunnerLength_G2);
      this.tabPage_G2.Controls.Add((Control) this.label_Pin_Runner_G2);
      this.tabPage_G2.Controls.Add((Control) this.label_Pin_GateDim2_G2);
      this.tabPage_G2.Controls.Add((Control) this.label_Pin_Gate_G2);
      this.tabPage_G2.Controls.Add((Control) this.label_Side_Runner_G2);
      this.tabPage_G2.Controls.Add((Control) this.panel_Side_GateType1_G2);
      this.tabPage_G2.Controls.Add((Control) this.panel_Side_GateType2_G2);
      this.tabPage_G2.Controls.Add((Control) this.label_Side_Gate_G2);
      this.tabPage_G2.Controls.Add((Control) this.label_Side_GateLength_G2);
      this.tabPage_G2.Controls.Add((Control) this.label_Side_GateDim1_G2);
      this.tabPage_G2.Controls.Add((Control) this.label_Side_GateDim3_G2);
      this.tabPage_G2.Controls.Add((Control) this.label_Side_GateDim2_G2);
      this.tabPage_G2.Controls.Add((Control) this.label_Side_GateDim4_G2);
      this.tabPage_G2.Controls.Add((Control) this.newButton_Pin_GateLength_G2);
      this.tabPage_G2.Controls.Add((Control) this.newTextBox_Side_RunnerLength_G2);
      this.tabPage_G2.Controls.Add((Control) this.newTextBox_Pin_RunnerLength_G2);
      this.tabPage_G2.Controls.Add((Control) this.newTextBox_Pin_RunnerDim1_G2);
      this.tabPage_G2.Controls.Add((Control) this.newTextBox_Pin_RunnerDim2_G2);
      this.tabPage_G2.Controls.Add((Control) this.newTextBox_Side_RunnerDim4_G2);
      this.tabPage_G2.Controls.Add((Control) this.newTextBox_Pin_GateLength_G2);
      this.tabPage_G2.Controls.Add((Control) this.newTextBox_Pin_GateDim1_G2);
      this.tabPage_G2.Controls.Add((Control) this.newTextBox_Pin_GateDim2_G2);
      this.tabPage_G2.Controls.Add((Control) this.newTextBox_Side_RunnerDim1_G2);
      this.tabPage_G2.Controls.Add((Control) this.newTextBox_Side_RunnerDim2_G2);
      this.tabPage_G2.Controls.Add((Control) this.newTextBox_Side_RunnerDim3_G2);
      this.tabPage_G2.Controls.Add((Control) this.newTextBox_Side_GateDim4_G2);
      this.tabPage_G2.Controls.Add((Control) this.newTextBox_Side_GateLength_G2);
      this.tabPage_G2.Controls.Add((Control) this.newTextBox_Side_GateDim3_G2);
      this.tabPage_G2.Controls.Add((Control) this.newTextBox_Side_GateDim1_G2);
      this.tabPage_G2.Controls.Add((Control) this.newTextBox_Side_GateDim2_G2);
      this.tabPage_G2.Controls.Add((Control) this.panel_Side_RunnerType_G2);
      this.tabPage_G2.Location = new Point(4, 22);
      this.tabPage_G2.Name = "tabPage_G2";
      this.tabPage_G2.Padding = new Padding(3);
      this.tabPage_G2.Size = new Size(662, 182);
      this.tabPage_G2.TabIndex = 1;
      this.tabPage_G2.UseVisualStyleBackColor = true;
      this.label_Side_RunnerLength_G2.BackColor = Color.Lavender;
      this.label_Side_RunnerLength_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerLength_G2.Location = new Point(166, 66);
      this.label_Side_RunnerLength_G2.Name = "label_Side_RunnerLength_G2";
      this.label_Side_RunnerLength_G2.Size = new Size(120, 23);
      this.label_Side_RunnerLength_G2.TabIndex = 193;
      this.label_Side_RunnerLength_G2.Text = "런너 길이";
      this.label_Side_RunnerLength_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim2_G2.BackColor = Color.Lavender;
      this.label_Side_RunnerDim2_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim2_G2.Location = new Point(166, 110);
      this.label_Side_RunnerDim2_G2.Name = "label_Side_RunnerDim2_G2";
      this.label_Side_RunnerDim2_G2.Size = new Size(120, 23);
      this.label_Side_RunnerDim2_G2.TabIndex = 191;
      this.label_Side_RunnerDim2_G2.Text = "Bottom Width";
      this.label_Side_RunnerDim2_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim4_G2.BackColor = Color.Lavender;
      this.label_Side_RunnerDim4_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim4_G2.Location = new Point(166, 154);
      this.label_Side_RunnerDim4_G2.Name = "label_Side_RunnerDim4_G2";
      this.label_Side_RunnerDim4_G2.Size = new Size(120, 23);
      this.label_Side_RunnerDim4_G2.TabIndex = 199;
      this.label_Side_RunnerDim4_G2.Text = "End Height";
      this.label_Side_RunnerDim4_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim1_G2.BackColor = Color.Lavender;
      this.label_Side_RunnerDim1_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim1_G2.Location = new Point(166, 88);
      this.label_Side_RunnerDim1_G2.Name = "label_Side_RunnerDim1_G2";
      this.label_Side_RunnerDim1_G2.Size = new Size(120, 23);
      this.label_Side_RunnerDim1_G2.TabIndex = 195;
      this.label_Side_RunnerDim1_G2.Text = "Top Width";
      this.label_Side_RunnerDim1_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim3_G2.BackColor = Color.Lavender;
      this.label_Side_RunnerDim3_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim3_G2.Location = new Point(166, 132);
      this.label_Side_RunnerDim3_G2.Name = "label_Side_RunnerDim3_G2";
      this.label_Side_RunnerDim3_G2.Size = new Size(120, 23);
      this.label_Side_RunnerDim3_G2.TabIndex = 197;
      this.label_Side_RunnerDim3_G2.Text = "Height";
      this.label_Side_RunnerDim3_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_GateDim1_G2.BackColor = Color.Lavender;
      this.label_Pin_GateDim1_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_GateDim1_G2.Location = new Point(330, 44);
      this.label_Pin_GateDim1_G2.Name = "label_Pin_GateDim1_G2";
      this.label_Pin_GateDim1_G2.Size = new Size(120, 23);
      this.label_Pin_GateDim1_G2.TabIndex = 185;
      this.label_Pin_GateDim1_G2.Text = "Start Dimension";
      this.label_Pin_GateDim1_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_RunnerDim2_G2.BackColor = Color.Lavender;
      this.label_Pin_RunnerDim2_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_RunnerDim2_G2.Location = new Point(495, 66);
      this.label_Pin_RunnerDim2_G2.Name = "label_Pin_RunnerDim2_G2";
      this.label_Pin_RunnerDim2_G2.Size = new Size(120, 23);
      this.label_Pin_RunnerDim2_G2.TabIndex = 119;
      this.label_Pin_RunnerDim2_G2.Text = "Outer Dimension";
      this.label_Pin_RunnerDim2_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_RunnerDim1_G2.BackColor = Color.Lavender;
      this.label_Pin_RunnerDim1_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_RunnerDim1_G2.Location = new Point(495, 44);
      this.label_Pin_RunnerDim1_G2.Name = "label_Pin_RunnerDim1_G2";
      this.label_Pin_RunnerDim1_G2.Size = new Size(120, 23);
      this.label_Pin_RunnerDim1_G2.TabIndex = 117;
      this.label_Pin_RunnerDim1_G2.Text = "Inner Dimension";
      this.label_Pin_RunnerDim1_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_RunnerLength_G2.BackColor = Color.Lavender;
      this.label_Pin_RunnerLength_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_RunnerLength_G2.Location = new Point(495, 22);
      this.label_Pin_RunnerLength_G2.Name = "label_Pin_RunnerLength_G2";
      this.label_Pin_RunnerLength_G2.Size = new Size(120, 23);
      this.label_Pin_RunnerLength_G2.TabIndex = 116;
      this.label_Pin_RunnerLength_G2.Text = "런너 길이";
      this.label_Pin_RunnerLength_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_Runner_G2.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Pin_Runner_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_Runner_G2.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Pin_Runner_G2.ForeColor = Color.MidnightBlue;
      this.label_Pin_Runner_G2.Location = new Point(495, 3);
      this.label_Pin_Runner_G2.Name = "label_Pin_Runner_G2";
      this.label_Pin_Runner_G2.Size = new Size(166, 20);
      this.label_Pin_Runner_G2.TabIndex = 177;
      this.label_Pin_Runner_G2.Text = "<핀 런너>";
      this.label_Pin_Runner_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_GateDim2_G2.BackColor = Color.Lavender;
      this.label_Pin_GateDim2_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_GateDim2_G2.Location = new Point(330, 66);
      this.label_Pin_GateDim2_G2.Name = "label_Pin_GateDim2_G2";
      this.label_Pin_GateDim2_G2.Size = new Size(120, 23);
      this.label_Pin_GateDim2_G2.TabIndex = 181;
      this.label_Pin_GateDim2_G2.Text = "End Dimension";
      this.label_Pin_GateDim2_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_Gate_G2.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Pin_Gate_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_Gate_G2.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Pin_Gate_G2.ForeColor = Color.MidnightBlue;
      this.label_Pin_Gate_G2.Location = new Point(330, 3);
      this.label_Pin_Gate_G2.Name = "label_Pin_Gate_G2";
      this.label_Pin_Gate_G2.Size = new Size(166, 20);
      this.label_Pin_Gate_G2.TabIndex = 179;
      this.label_Pin_Gate_G2.Text = "<핀 게이트>";
      this.label_Pin_Gate_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_Runner_G2.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Side_Runner_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_Runner_G2.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Side_Runner_G2.ForeColor = Color.MidnightBlue;
      this.label_Side_Runner_G2.Location = new Point(166, 3);
      this.label_Side_Runner_G2.Name = "label_Side_Runner_G2";
      this.label_Side_Runner_G2.Size = new Size(166, 20);
      this.label_Side_Runner_G2.TabIndex = 188;
      this.label_Side_Runner_G2.Text = "<사이드 런너>";
      this.label_Side_Runner_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.panel_Side_GateType1_G2.BackColor = Color.White;
      this.panel_Side_GateType1_G2.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Side_GateType1_G2.Controls.Add((Control) this.radioButton_Side_GateCircle_G2);
      this.panel_Side_GateType1_G2.Controls.Add((Control) this.radioButton_Side_GateRect_G2);
      this.panel_Side_GateType1_G2.Location = new Point(1, 22);
      this.panel_Side_GateType1_G2.Name = "panel_Side_GateType1_G2";
      this.panel_Side_GateType1_G2.Size = new Size(166, 23);
      this.panel_Side_GateType1_G2.TabIndex = 167;
      this.radioButton_Side_GateCircle_G2.AutoSize = true;
      this.radioButton_Side_GateCircle_G2.Location = new Point(85, 2);
      this.radioButton_Side_GateCircle_G2.Name = "radioButton_Side_GateCircle_G2";
      this.radioButton_Side_GateCircle_G2.Size = new Size(55, 19);
      this.radioButton_Side_GateCircle_G2.TabIndex = 84;
      this.radioButton_Side_GateCircle_G2.Text = "Circle";
      this.radioButton_Side_GateCircle_G2.UseVisualStyleBackColor = true;
      this.radioButton_Side_GateCircle_G2.CheckedChanged += new EventHandler(this.radioButton_Side_GateCircle_CheckedChanged);
      this.radioButton_Side_GateRect_G2.AutoSize = true;
      this.radioButton_Side_GateRect_G2.Location = new Point(7, 2);
      this.radioButton_Side_GateRect_G2.Name = "radioButton_Side_GateRect_G2";
      this.radioButton_Side_GateRect_G2.Size = new Size(77, 19);
      this.radioButton_Side_GateRect_G2.TabIndex = 83;
      this.radioButton_Side_GateRect_G2.Text = "Rectangle";
      this.radioButton_Side_GateRect_G2.UseVisualStyleBackColor = true;
      this.radioButton_Side_GateRect_G2.CheckedChanged += new EventHandler(this.radioButton_Side_GateRect_CheckedChanged);
      this.panel_Side_GateType2_G2.BackColor = Color.White;
      this.panel_Side_GateType2_G2.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Side_GateType2_G2.Controls.Add((Control) this.radioButton_Side_GateSubMarine_G2);
      this.panel_Side_GateType2_G2.Controls.Add((Control) this.radioButton_Side_GateNormal_G2);
      this.panel_Side_GateType2_G2.Location = new Point(1, 44);
      this.panel_Side_GateType2_G2.Name = "panel_Side_GateType2_G2";
      this.panel_Side_GateType2_G2.Size = new Size(166, 23);
      this.panel_Side_GateType2_G2.TabIndex = 168;
      this.radioButton_Side_GateSubMarine_G2.AutoSize = true;
      this.radioButton_Side_GateSubMarine_G2.Location = new Point(85, 2);
      this.radioButton_Side_GateSubMarine_G2.Name = "radioButton_Side_GateSubMarine_G2";
      this.radioButton_Side_GateSubMarine_G2.Size = new Size(73, 19);
      this.radioButton_Side_GateSubMarine_G2.TabIndex = 87;
      this.radioButton_Side_GateSubMarine_G2.Text = "서브마린";
      this.radioButton_Side_GateSubMarine_G2.UseVisualStyleBackColor = true;
      this.radioButton_Side_GateNormal_G2.AutoSize = true;
      this.radioButton_Side_GateNormal_G2.Location = new Point(8, 2);
      this.radioButton_Side_GateNormal_G2.Name = "radioButton_Side_GateNormal_G2";
      this.radioButton_Side_GateNormal_G2.Size = new Size(49, 19);
      this.radioButton_Side_GateNormal_G2.TabIndex = 86;
      this.radioButton_Side_GateNormal_G2.Text = "일반";
      this.radioButton_Side_GateNormal_G2.UseVisualStyleBackColor = true;
      this.label_Side_Gate_G2.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Side_Gate_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_Gate_G2.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Side_Gate_G2.ForeColor = Color.MidnightBlue;
      this.label_Side_Gate_G2.Location = new Point(1, 3);
      this.label_Side_Gate_G2.Name = "label_Side_Gate_G2";
      this.label_Side_Gate_G2.Size = new Size(166, 20);
      this.label_Side_Gate_G2.TabIndex = 189;
      this.label_Side_Gate_G2.Text = "<사이드 게이트>";
      this.label_Side_Gate_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateLength_G2.BackColor = Color.Lavender;
      this.label_Side_GateLength_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateLength_G2.Location = new Point(1, 66);
      this.label_Side_GateLength_G2.Name = "label_Side_GateLength_G2";
      this.label_Side_GateLength_G2.Size = new Size(120, 23);
      this.label_Side_GateLength_G2.TabIndex = 196;
      this.label_Side_GateLength_G2.Text = "게이트 길이";
      this.label_Side_GateLength_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim1_G2.BackColor = Color.Lavender;
      this.label_Side_GateDim1_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim1_G2.Location = new Point(1, 88);
      this.label_Side_GateDim1_G2.Name = "label_Side_GateDim1_G2";
      this.label_Side_GateDim1_G2.Size = new Size(120, 23);
      this.label_Side_GateDim1_G2.TabIndex = 194;
      this.label_Side_GateDim1_G2.Text = "Start Width";
      this.label_Side_GateDim1_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim3_G2.BackColor = Color.Lavender;
      this.label_Side_GateDim3_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim3_G2.Location = new Point(1, 132);
      this.label_Side_GateDim3_G2.Name = "label_Side_GateDim3_G2";
      this.label_Side_GateDim3_G2.Size = new Size(120, 23);
      this.label_Side_GateDim3_G2.TabIndex = 192;
      this.label_Side_GateDim3_G2.Text = "End Width";
      this.label_Side_GateDim3_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim2_G2.BackColor = Color.Lavender;
      this.label_Side_GateDim2_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim2_G2.Location = new Point(1, 110);
      this.label_Side_GateDim2_G2.Name = "label_Side_GateDim2_G2";
      this.label_Side_GateDim2_G2.Size = new Size(120, 23);
      this.label_Side_GateDim2_G2.TabIndex = 190;
      this.label_Side_GateDim2_G2.Text = "Start Height";
      this.label_Side_GateDim2_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim4_G2.BackColor = Color.Lavender;
      this.label_Side_GateDim4_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim4_G2.Location = new Point(1, 154);
      this.label_Side_GateDim4_G2.Name = "label_Side_GateDim4_G2";
      this.label_Side_GateDim4_G2.Size = new Size(120, 23);
      this.label_Side_GateDim4_G2.TabIndex = 198;
      this.label_Side_GateDim4_G2.Text = "End Height";
      this.label_Side_GateDim4_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Pin_GateLength_G2.BackColor = Color.Lavender;
      this.newButton_Pin_GateLength_G2.ButtonBackColor = Color.Lavender;
      this.newButton_Pin_GateLength_G2.ButtonText = "게이트 길이";
      this.newButton_Pin_GateLength_G2.FlatBorderSize = 1;
      this.newButton_Pin_GateLength_G2.FlatStyle = FlatStyle.Flat;
      this.newButton_Pin_GateLength_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Pin_GateLength_G2.ForeColor = SystemColors.ControlText;
      this.newButton_Pin_GateLength_G2.Image = (Image) null;
      this.newButton_Pin_GateLength_G2.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Pin_GateLength_G2.Location = new Point(330, 22);
      this.newButton_Pin_GateLength_G2.Margin = new Padding(3, 0, 3, 0);
      this.newButton_Pin_GateLength_G2.Name = "newButton_Pin_GateLength_G2";
      this.newButton_Pin_GateLength_G2.Size = new Size(120, 23);
      this.newButton_Pin_GateLength_G2.TabIndex = 120;
      this.newButton_Pin_GateLength_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Pin_GateLength_G2.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Pin_GateLength_G2.NewClick += new EventHandler(this.newButton_Pin_GateLength_NewClick);
      this.newTextBox_Side_RunnerLength_G2.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerLength_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerLength_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerLength_G2.IsDigit = true;
      this.newTextBox_Side_RunnerLength_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerLength_G2.Location = new Point(284, 66);
      this.newTextBox_Side_RunnerLength_G2.MultiLine = false;
      this.newTextBox_Side_RunnerLength_G2.Name = "newTextBox_Side_RunnerLength_G2";
      this.newTextBox_Side_RunnerLength_G2.ReadOnly = false;
      this.newTextBox_Side_RunnerLength_G2.Size = new Size(47, 23);
      this.newTextBox_Side_RunnerLength_G2.TabIndex = 175;
      this.newTextBox_Side_RunnerLength_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerLength_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerLength_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerLength_G2.Value = "0";
      this.newTextBox_Pin_RunnerLength_G2.BackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerLength_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_RunnerLength_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_RunnerLength_G2.IsDigit = true;
      this.newTextBox_Pin_RunnerLength_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_RunnerLength_G2.Location = new Point(614, 22);
      this.newTextBox_Pin_RunnerLength_G2.MultiLine = false;
      this.newTextBox_Pin_RunnerLength_G2.Name = "newTextBox_Pin_RunnerLength_G2";
      this.newTextBox_Pin_RunnerLength_G2.ReadOnly = false;
      this.newTextBox_Pin_RunnerLength_G2.Size = new Size(47, 23);
      this.newTextBox_Pin_RunnerLength_G2.TabIndex = 39;
      this.newTextBox_Pin_RunnerLength_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_RunnerLength_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerLength_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_RunnerLength_G2.Value = "0";
      this.newTextBox_Pin_RunnerDim1_G2.BackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim1_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_RunnerDim1_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_RunnerDim1_G2.IsDigit = true;
      this.newTextBox_Pin_RunnerDim1_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_RunnerDim1_G2.Location = new Point(614, 44);
      this.newTextBox_Pin_RunnerDim1_G2.MultiLine = false;
      this.newTextBox_Pin_RunnerDim1_G2.Name = "newTextBox_Pin_RunnerDim1_G2";
      this.newTextBox_Pin_RunnerDim1_G2.ReadOnly = false;
      this.newTextBox_Pin_RunnerDim1_G2.Size = new Size(47, 23);
      this.newTextBox_Pin_RunnerDim1_G2.TabIndex = 40;
      this.newTextBox_Pin_RunnerDim1_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_RunnerDim1_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim1_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_RunnerDim1_G2.Value = "0";
      this.newTextBox_Pin_RunnerDim2_G2.BackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim2_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_RunnerDim2_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_RunnerDim2_G2.IsDigit = true;
      this.newTextBox_Pin_RunnerDim2_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_RunnerDim2_G2.Location = new Point(614, 66);
      this.newTextBox_Pin_RunnerDim2_G2.MultiLine = false;
      this.newTextBox_Pin_RunnerDim2_G2.Name = "newTextBox_Pin_RunnerDim2_G2";
      this.newTextBox_Pin_RunnerDim2_G2.ReadOnly = false;
      this.newTextBox_Pin_RunnerDim2_G2.Size = new Size(47, 23);
      this.newTextBox_Pin_RunnerDim2_G2.TabIndex = 41;
      this.newTextBox_Pin_RunnerDim2_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_RunnerDim2_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim2_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_RunnerDim2_G2.Value = "0";
      this.newTextBox_Side_RunnerDim4_G2.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim4_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim4_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim4_G2.IsDigit = true;
      this.newTextBox_Side_RunnerDim4_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim4_G2.Location = new Point(284, 154);
      this.newTextBox_Side_RunnerDim4_G2.MultiLine = false;
      this.newTextBox_Side_RunnerDim4_G2.Name = "newTextBox_Side_RunnerDim4_G2";
      this.newTextBox_Side_RunnerDim4_G2.ReadOnly = false;
      this.newTextBox_Side_RunnerDim4_G2.Size = new Size(47, 23);
      this.newTextBox_Side_RunnerDim4_G2.TabIndex = 182;
      this.newTextBox_Side_RunnerDim4_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim4_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim4_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim4_G2.Value = "0";
      this.newTextBox_Pin_GateLength_G2.BackColor = SystemColors.Window;
      this.newTextBox_Pin_GateLength_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_GateLength_G2.Enabled = false;
      this.newTextBox_Pin_GateLength_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_GateLength_G2.IsDigit = true;
      this.newTextBox_Pin_GateLength_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_GateLength_G2.Location = new Point(449, 22);
      this.newTextBox_Pin_GateLength_G2.MultiLine = false;
      this.newTextBox_Pin_GateLength_G2.Name = "newTextBox_Pin_GateLength_G2";
      this.newTextBox_Pin_GateLength_G2.ReadOnly = true;
      this.newTextBox_Pin_GateLength_G2.Size = new Size(47, 23);
      this.newTextBox_Pin_GateLength_G2.TabIndex = 36;
      this.newTextBox_Pin_GateLength_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_GateLength_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_GateLength_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_GateLength_G2.Value = "0";
      this.newTextBox_Pin_GateDim1_G2.BackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim1_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_GateDim1_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_GateDim1_G2.IsDigit = true;
      this.newTextBox_Pin_GateDim1_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_GateDim1_G2.Location = new Point(449, 44);
      this.newTextBox_Pin_GateDim1_G2.MultiLine = false;
      this.newTextBox_Pin_GateDim1_G2.Name = "newTextBox_Pin_GateDim1_G2";
      this.newTextBox_Pin_GateDim1_G2.ReadOnly = false;
      this.newTextBox_Pin_GateDim1_G2.Size = new Size(47, 23);
      this.newTextBox_Pin_GateDim1_G2.TabIndex = 37;
      this.newTextBox_Pin_GateDim1_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_GateDim1_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim1_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_GateDim1_G2.Value = "0";
      this.newTextBox_Pin_GateDim2_G2.BackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim2_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_GateDim2_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_GateDim2_G2.IsDigit = true;
      this.newTextBox_Pin_GateDim2_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_GateDim2_G2.Location = new Point(449, 66);
      this.newTextBox_Pin_GateDim2_G2.MultiLine = false;
      this.newTextBox_Pin_GateDim2_G2.Name = "newTextBox_Pin_GateDim2_G2";
      this.newTextBox_Pin_GateDim2_G2.ReadOnly = false;
      this.newTextBox_Pin_GateDim2_G2.Size = new Size(47, 23);
      this.newTextBox_Pin_GateDim2_G2.TabIndex = 38;
      this.newTextBox_Pin_GateDim2_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_GateDim2_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim2_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_GateDim2_G2.Value = "0";
      this.newTextBox_Side_RunnerDim1_G2.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim1_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim1_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim1_G2.IsDigit = true;
      this.newTextBox_Side_RunnerDim1_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim1_G2.Location = new Point(284, 88);
      this.newTextBox_Side_RunnerDim1_G2.MultiLine = false;
      this.newTextBox_Side_RunnerDim1_G2.Name = "newTextBox_Side_RunnerDim1_G2";
      this.newTextBox_Side_RunnerDim1_G2.ReadOnly = false;
      this.newTextBox_Side_RunnerDim1_G2.Size = new Size(47, 23);
      this.newTextBox_Side_RunnerDim1_G2.TabIndex = 176;
      this.newTextBox_Side_RunnerDim1_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim1_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim1_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim1_G2.Value = "0";
      this.newTextBox_Side_RunnerDim2_G2.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim2_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim2_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim2_G2.IsDigit = true;
      this.newTextBox_Side_RunnerDim2_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim2_G2.Location = new Point(284, 110);
      this.newTextBox_Side_RunnerDim2_G2.MultiLine = false;
      this.newTextBox_Side_RunnerDim2_G2.Name = "newTextBox_Side_RunnerDim2_G2";
      this.newTextBox_Side_RunnerDim2_G2.ReadOnly = false;
      this.newTextBox_Side_RunnerDim2_G2.Size = new Size(47, 23);
      this.newTextBox_Side_RunnerDim2_G2.TabIndex = 178;
      this.newTextBox_Side_RunnerDim2_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim2_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim2_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim2_G2.Value = "0";
      this.newTextBox_Side_RunnerDim3_G2.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim3_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim3_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim3_G2.IsDigit = true;
      this.newTextBox_Side_RunnerDim3_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim3_G2.Location = new Point(284, 132);
      this.newTextBox_Side_RunnerDim3_G2.MultiLine = false;
      this.newTextBox_Side_RunnerDim3_G2.Name = "newTextBox_Side_RunnerDim3_G2";
      this.newTextBox_Side_RunnerDim3_G2.ReadOnly = false;
      this.newTextBox_Side_RunnerDim3_G2.Size = new Size(47, 23);
      this.newTextBox_Side_RunnerDim3_G2.TabIndex = 180;
      this.newTextBox_Side_RunnerDim3_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim3_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim3_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim3_G2.Value = "0";
      this.newTextBox_Side_GateDim4_G2.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim4_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim4_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim4_G2.IsDigit = true;
      this.newTextBox_Side_GateDim4_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim4_G2.Location = new Point(120, 154);
      this.newTextBox_Side_GateDim4_G2.MultiLine = false;
      this.newTextBox_Side_GateDim4_G2.Name = "newTextBox_Side_GateDim4_G2";
      this.newTextBox_Side_GateDim4_G2.ReadOnly = false;
      this.newTextBox_Side_GateDim4_G2.Size = new Size(47, 23);
      this.newTextBox_Side_GateDim4_G2.TabIndex = 173;
      this.newTextBox_Side_GateDim4_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim4_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim4_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim4_G2.Value = "0";
      this.newTextBox_Side_GateLength_G2.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateLength_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateLength_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateLength_G2.IsDigit = true;
      this.newTextBox_Side_GateLength_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateLength_G2.Location = new Point(120, 66);
      this.newTextBox_Side_GateLength_G2.MultiLine = false;
      this.newTextBox_Side_GateLength_G2.Name = "newTextBox_Side_GateLength_G2";
      this.newTextBox_Side_GateLength_G2.ReadOnly = false;
      this.newTextBox_Side_GateLength_G2.Size = new Size(47, 23);
      this.newTextBox_Side_GateLength_G2.TabIndex = 169;
      this.newTextBox_Side_GateLength_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateLength_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateLength_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateLength_G2.Value = "0";
      this.newTextBox_Side_GateDim3_G2.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim3_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim3_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim3_G2.IsDigit = true;
      this.newTextBox_Side_GateDim3_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim3_G2.Location = new Point(120, 132);
      this.newTextBox_Side_GateDim3_G2.MultiLine = false;
      this.newTextBox_Side_GateDim3_G2.Name = "newTextBox_Side_GateDim3_G2";
      this.newTextBox_Side_GateDim3_G2.ReadOnly = false;
      this.newTextBox_Side_GateDim3_G2.Size = new Size(47, 23);
      this.newTextBox_Side_GateDim3_G2.TabIndex = 172;
      this.newTextBox_Side_GateDim3_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim3_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim3_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim3_G2.Value = "0";
      this.newTextBox_Side_GateDim1_G2.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim1_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim1_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim1_G2.IsDigit = true;
      this.newTextBox_Side_GateDim1_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim1_G2.Location = new Point(120, 88);
      this.newTextBox_Side_GateDim1_G2.MultiLine = false;
      this.newTextBox_Side_GateDim1_G2.Name = "newTextBox_Side_GateDim1_G2";
      this.newTextBox_Side_GateDim1_G2.ReadOnly = false;
      this.newTextBox_Side_GateDim1_G2.Size = new Size(47, 23);
      this.newTextBox_Side_GateDim1_G2.TabIndex = 170;
      this.newTextBox_Side_GateDim1_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim1_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim1_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim1_G2.Value = "0";
      this.newTextBox_Side_GateDim2_G2.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim2_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim2_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim2_G2.IsDigit = true;
      this.newTextBox_Side_GateDim2_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim2_G2.Location = new Point(120, 110);
      this.newTextBox_Side_GateDim2_G2.MultiLine = false;
      this.newTextBox_Side_GateDim2_G2.Name = "newTextBox_Side_GateDim2_G2";
      this.newTextBox_Side_GateDim2_G2.ReadOnly = false;
      this.newTextBox_Side_GateDim2_G2.Size = new Size(47, 23);
      this.newTextBox_Side_GateDim2_G2.TabIndex = 171;
      this.newTextBox_Side_GateDim2_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim2_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim2_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim2_G2.Value = "0";
      this.panel_Side_RunnerType_G2.BackColor = Color.White;
      this.panel_Side_RunnerType_G2.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Side_RunnerType_G2.Controls.Add((Control) this.radioButton_Side_RunnerRectangle_G2);
      this.panel_Side_RunnerType_G2.Controls.Add((Control) this.radioButton_Side_RunnerCircle_G2);
      this.panel_Side_RunnerType_G2.Controls.Add((Control) this.radioButton_Side_RunnerTrepezoidal_G2);
      this.panel_Side_RunnerType_G2.Location = new Point(166, 22);
      this.panel_Side_RunnerType_G2.Name = "panel_Side_RunnerType_G2";
      this.panel_Side_RunnerType_G2.Size = new Size(166, 45);
      this.panel_Side_RunnerType_G2.TabIndex = 174;
      this.radioButton_Side_RunnerRectangle_G2.AutoSize = true;
      this.radioButton_Side_RunnerRectangle_G2.Location = new Point(6, 23);
      this.radioButton_Side_RunnerRectangle_G2.Name = "radioButton_Side_RunnerRectangle_G2";
      this.radioButton_Side_RunnerRectangle_G2.Size = new Size(77, 19);
      this.radioButton_Side_RunnerRectangle_G2.TabIndex = 96;
      this.radioButton_Side_RunnerRectangle_G2.Text = "Rectangle";
      this.radioButton_Side_RunnerRectangle_G2.UseVisualStyleBackColor = true;
      this.radioButton_Side_RunnerRectangle_G2.CheckedChanged += new EventHandler(this.radioButton_Side_RunnerRectangle_CheckedChanged);
      this.radioButton_Side_RunnerCircle_G2.AutoSize = true;
      this.radioButton_Side_RunnerCircle_G2.Location = new Point(96, 2);
      this.radioButton_Side_RunnerCircle_G2.Name = "radioButton_Side_RunnerCircle_G2";
      this.radioButton_Side_RunnerCircle_G2.Size = new Size(55, 19);
      this.radioButton_Side_RunnerCircle_G2.TabIndex = 95;
      this.radioButton_Side_RunnerCircle_G2.Text = "Circle";
      this.radioButton_Side_RunnerCircle_G2.UseVisualStyleBackColor = true;
      this.radioButton_Side_RunnerCircle_G2.CheckedChanged += new EventHandler(this.radioButton_Side_RunnerCircle_CheckedChanged);
      this.radioButton_Side_RunnerTrepezoidal_G2.AutoSize = true;
      this.radioButton_Side_RunnerTrepezoidal_G2.Location = new Point(6, 2);
      this.radioButton_Side_RunnerTrepezoidal_G2.Name = "radioButton_Side_RunnerTrepezoidal_G2";
      this.radioButton_Side_RunnerTrepezoidal_G2.Size = new Size(84, 19);
      this.radioButton_Side_RunnerTrepezoidal_G2.TabIndex = 94;
      this.radioButton_Side_RunnerTrepezoidal_G2.Text = "Trepezoidal";
      this.radioButton_Side_RunnerTrepezoidal_G2.UseVisualStyleBackColor = true;
      this.radioButton_Side_RunnerTrepezoidal_G2.CheckedChanged += new EventHandler(this.radioButton_Side_RunnerTrepezoidal_CheckedChanged);
      this.tabPage_G3.Controls.Add((Control) this.label_Pin_Gate_G3);
      this.tabPage_G3.Controls.Add((Control) this.label_Pin_Runner_G3);
      this.tabPage_G3.Controls.Add((Control) this.label_Pin_GateDim1_G3);
      this.tabPage_G3.Controls.Add((Control) this.panel_Side_GateType1_G3);
      this.tabPage_G3.Controls.Add((Control) this.label_Pin_RunnerLength_G3);
      this.tabPage_G3.Controls.Add((Control) this.label_Side_RunnerLength_G3);
      this.tabPage_G3.Controls.Add((Control) this.label_Pin_RunnerDim1_G3);
      this.tabPage_G3.Controls.Add((Control) this.panel_Side_GateType2_G3);
      this.tabPage_G3.Controls.Add((Control) this.label_Side_RunnerDim2_G3);
      this.tabPage_G3.Controls.Add((Control) this.label_Side_RunnerDim4_G3);
      this.tabPage_G3.Controls.Add((Control) this.label_Side_RunnerDim1_G3);
      this.tabPage_G3.Controls.Add((Control) this.label_Pin_GateDim2_G3);
      this.tabPage_G3.Controls.Add((Control) this.label_Side_Gate_G3);
      this.tabPage_G3.Controls.Add((Control) this.label_Side_RunnerDim3_G3);
      this.tabPage_G3.Controls.Add((Control) this.label_Pin_RunnerDim2_G3);
      this.tabPage_G3.Controls.Add((Control) this.label_Side_GateLength_G3);
      this.tabPage_G3.Controls.Add((Control) this.label_Side_GateDim1_G3);
      this.tabPage_G3.Controls.Add((Control) this.label_Side_GateDim3_G3);
      this.tabPage_G3.Controls.Add((Control) this.label_Side_GateDim2_G3);
      this.tabPage_G3.Controls.Add((Control) this.label_Side_GateDim4_G3);
      this.tabPage_G3.Controls.Add((Control) this.newTextBox_Side_RunnerLength_G3);
      this.tabPage_G3.Controls.Add((Control) this.newTextBox_Side_RunnerDim4_G3);
      this.tabPage_G3.Controls.Add((Control) this.newButton_Pin_GateLength_G3);
      this.tabPage_G3.Controls.Add((Control) this.newTextBox_Side_RunnerDim1_G3);
      this.tabPage_G3.Controls.Add((Control) this.newTextBox_Pin_GateLength_G3);
      this.tabPage_G3.Controls.Add((Control) this.newTextBox_Side_RunnerDim2_G3);
      this.tabPage_G3.Controls.Add((Control) this.newTextBox_Pin_GateDim1_G3);
      this.tabPage_G3.Controls.Add((Control) this.newTextBox_Side_RunnerDim3_G3);
      this.tabPage_G3.Controls.Add((Control) this.newTextBox_Pin_GateDim2_G3);
      this.tabPage_G3.Controls.Add((Control) this.newTextBox_Pin_RunnerLength_G3);
      this.tabPage_G3.Controls.Add((Control) this.newTextBox_Side_GateDim4_G3);
      this.tabPage_G3.Controls.Add((Control) this.newTextBox_Side_GateLength_G3);
      this.tabPage_G3.Controls.Add((Control) this.newTextBox_Pin_RunnerDim1_G3);
      this.tabPage_G3.Controls.Add((Control) this.newTextBox_Side_GateDim3_G3);
      this.tabPage_G3.Controls.Add((Control) this.newTextBox_Side_GateDim1_G3);
      this.tabPage_G3.Controls.Add((Control) this.newTextBox_Pin_RunnerDim2_G3);
      this.tabPage_G3.Controls.Add((Control) this.newTextBox_Side_GateDim2_G3);
      this.tabPage_G3.Controls.Add((Control) this.label_Side_Runner_G3);
      this.tabPage_G3.Controls.Add((Control) this.panel_Side_RunnerType_G3);
      this.tabPage_G3.Location = new Point(4, 22);
      this.tabPage_G3.Name = "tabPage_G3";
      this.tabPage_G3.Padding = new Padding(3);
      this.tabPage_G3.Size = new Size(662, 182);
      this.tabPage_G3.TabIndex = 2;
      this.tabPage_G3.UseVisualStyleBackColor = true;
      this.label_Pin_Gate_G3.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Pin_Gate_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_Gate_G3.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Pin_Gate_G3.ForeColor = Color.MidnightBlue;
      this.label_Pin_Gate_G3.Location = new Point(330, 3);
      this.label_Pin_Gate_G3.Name = "label_Pin_Gate_G3";
      this.label_Pin_Gate_G3.Size = new Size(166, 20);
      this.label_Pin_Gate_G3.TabIndex = 114;
      this.label_Pin_Gate_G3.Text = "<핀 게이트>";
      this.label_Pin_Gate_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_Runner_G3.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Pin_Runner_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_Runner_G3.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Pin_Runner_G3.ForeColor = Color.MidnightBlue;
      this.label_Pin_Runner_G3.Location = new Point(495, 3);
      this.label_Pin_Runner_G3.Name = "label_Pin_Runner_G3";
      this.label_Pin_Runner_G3.Size = new Size(166, 20);
      this.label_Pin_Runner_G3.TabIndex = 113;
      this.label_Pin_Runner_G3.Text = "<핀 런너>";
      this.label_Pin_Runner_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_GateDim1_G3.BackColor = Color.Lavender;
      this.label_Pin_GateDim1_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_GateDim1_G3.Location = new Point(330, 44);
      this.label_Pin_GateDim1_G3.Name = "label_Pin_GateDim1_G3";
      this.label_Pin_GateDim1_G3.Size = new Size(120, 23);
      this.label_Pin_GateDim1_G3.TabIndex = 118;
      this.label_Pin_GateDim1_G3.Text = "Start Dimension";
      this.label_Pin_GateDim1_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.panel_Side_GateType1_G3.BackColor = Color.White;
      this.panel_Side_GateType1_G3.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Side_GateType1_G3.Controls.Add((Control) this.radioButton_Side_GateCircle_G3);
      this.panel_Side_GateType1_G3.Controls.Add((Control) this.radioButton_Side_GateRect_G3);
      this.panel_Side_GateType1_G3.Location = new Point(1, 22);
      this.panel_Side_GateType1_G3.Name = "panel_Side_GateType1_G3";
      this.panel_Side_GateType1_G3.Size = new Size(166, 23);
      this.panel_Side_GateType1_G3.TabIndex = 210;
      this.radioButton_Side_GateCircle_G3.AutoSize = true;
      this.radioButton_Side_GateCircle_G3.Location = new Point(85, 2);
      this.radioButton_Side_GateCircle_G3.Name = "radioButton_Side_GateCircle_G3";
      this.radioButton_Side_GateCircle_G3.Size = new Size(55, 19);
      this.radioButton_Side_GateCircle_G3.TabIndex = 84;
      this.radioButton_Side_GateCircle_G3.Text = "Circle";
      this.radioButton_Side_GateCircle_G3.UseVisualStyleBackColor = true;
      this.radioButton_Side_GateCircle_G3.CheckedChanged += new EventHandler(this.radioButton_Side_GateCircle_CheckedChanged);
      this.radioButton_Side_GateRect_G3.AutoSize = true;
      this.radioButton_Side_GateRect_G3.Location = new Point(7, 2);
      this.radioButton_Side_GateRect_G3.Name = "radioButton_Side_GateRect_G3";
      this.radioButton_Side_GateRect_G3.Size = new Size(77, 19);
      this.radioButton_Side_GateRect_G3.TabIndex = 83;
      this.radioButton_Side_GateRect_G3.Text = "Rectangle";
      this.radioButton_Side_GateRect_G3.UseVisualStyleBackColor = true;
      this.radioButton_Side_GateRect_G3.CheckedChanged += new EventHandler(this.radioButton_Side_GateRect_CheckedChanged);
      this.label_Pin_RunnerLength_G3.BackColor = Color.Lavender;
      this.label_Pin_RunnerLength_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_RunnerLength_G3.Location = new Point(495, 22);
      this.label_Pin_RunnerLength_G3.Name = "label_Pin_RunnerLength_G3";
      this.label_Pin_RunnerLength_G3.Size = new Size(120, 23);
      this.label_Pin_RunnerLength_G3.TabIndex = 116;
      this.label_Pin_RunnerLength_G3.Text = "런너 길이";
      this.label_Pin_RunnerLength_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerLength_G3.BackColor = Color.Lavender;
      this.label_Side_RunnerLength_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerLength_G3.Location = new Point(166, 66);
      this.label_Side_RunnerLength_G3.Name = "label_Side_RunnerLength_G3";
      this.label_Side_RunnerLength_G3.Size = new Size(120, 23);
      this.label_Side_RunnerLength_G3.TabIndex = 232;
      this.label_Side_RunnerLength_G3.Text = "런너 길이";
      this.label_Side_RunnerLength_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_RunnerDim1_G3.BackColor = Color.Lavender;
      this.label_Pin_RunnerDim1_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_RunnerDim1_G3.Location = new Point(495, 44);
      this.label_Pin_RunnerDim1_G3.Name = "label_Pin_RunnerDim1_G3";
      this.label_Pin_RunnerDim1_G3.Size = new Size(120, 23);
      this.label_Pin_RunnerDim1_G3.TabIndex = 117;
      this.label_Pin_RunnerDim1_G3.Text = "Inner Dimension";
      this.label_Pin_RunnerDim1_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.panel_Side_GateType2_G3.BackColor = Color.White;
      this.panel_Side_GateType2_G3.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Side_GateType2_G3.Controls.Add((Control) this.radioButton_Side_GateSubMarine_G3);
      this.panel_Side_GateType2_G3.Controls.Add((Control) this.radioButton_Side_GateNormal_G3);
      this.panel_Side_GateType2_G3.Location = new Point(1, 44);
      this.panel_Side_GateType2_G3.Name = "panel_Side_GateType2_G3";
      this.panel_Side_GateType2_G3.Size = new Size(166, 23);
      this.panel_Side_GateType2_G3.TabIndex = 211;
      this.radioButton_Side_GateSubMarine_G3.AutoSize = true;
      this.radioButton_Side_GateSubMarine_G3.Location = new Point(85, 2);
      this.radioButton_Side_GateSubMarine_G3.Name = "radioButton_Side_GateSubMarine_G3";
      this.radioButton_Side_GateSubMarine_G3.Size = new Size(73, 19);
      this.radioButton_Side_GateSubMarine_G3.TabIndex = 87;
      this.radioButton_Side_GateSubMarine_G3.Text = "서브마린";
      this.radioButton_Side_GateSubMarine_G3.UseVisualStyleBackColor = true;
      this.radioButton_Side_GateNormal_G3.AutoSize = true;
      this.radioButton_Side_GateNormal_G3.Location = new Point(8, 2);
      this.radioButton_Side_GateNormal_G3.Name = "radioButton_Side_GateNormal_G3";
      this.radioButton_Side_GateNormal_G3.Size = new Size(49, 19);
      this.radioButton_Side_GateNormal_G3.TabIndex = 86;
      this.radioButton_Side_GateNormal_G3.Text = "일반";
      this.radioButton_Side_GateNormal_G3.UseVisualStyleBackColor = true;
      this.label_Side_RunnerDim2_G3.BackColor = Color.Lavender;
      this.label_Side_RunnerDim2_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim2_G3.Location = new Point(166, 110);
      this.label_Side_RunnerDim2_G3.Name = "label_Side_RunnerDim2_G3";
      this.label_Side_RunnerDim2_G3.Size = new Size(120, 23);
      this.label_Side_RunnerDim2_G3.TabIndex = 230;
      this.label_Side_RunnerDim2_G3.Text = "Bottom Width";
      this.label_Side_RunnerDim2_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim4_G3.BackColor = Color.Lavender;
      this.label_Side_RunnerDim4_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim4_G3.Location = new Point(166, 154);
      this.label_Side_RunnerDim4_G3.Name = "label_Side_RunnerDim4_G3";
      this.label_Side_RunnerDim4_G3.Size = new Size(120, 23);
      this.label_Side_RunnerDim4_G3.TabIndex = 238;
      this.label_Side_RunnerDim4_G3.Text = "End Height";
      this.label_Side_RunnerDim4_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim1_G3.BackColor = Color.Lavender;
      this.label_Side_RunnerDim1_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim1_G3.Location = new Point(166, 88);
      this.label_Side_RunnerDim1_G3.Name = "label_Side_RunnerDim1_G3";
      this.label_Side_RunnerDim1_G3.Size = new Size(120, 23);
      this.label_Side_RunnerDim1_G3.TabIndex = 234;
      this.label_Side_RunnerDim1_G3.Text = "Top Width";
      this.label_Side_RunnerDim1_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_GateDim2_G3.BackColor = Color.Lavender;
      this.label_Pin_GateDim2_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_GateDim2_G3.Location = new Point(330, 66);
      this.label_Pin_GateDim2_G3.Name = "label_Pin_GateDim2_G3";
      this.label_Pin_GateDim2_G3.Size = new Size(120, 23);
      this.label_Pin_GateDim2_G3.TabIndex = 115;
      this.label_Pin_GateDim2_G3.Text = "End Dimension";
      this.label_Pin_GateDim2_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_Gate_G3.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Side_Gate_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_Gate_G3.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Side_Gate_G3.ForeColor = Color.MidnightBlue;
      this.label_Side_Gate_G3.Location = new Point(1, 3);
      this.label_Side_Gate_G3.Name = "label_Side_Gate_G3";
      this.label_Side_Gate_G3.Size = new Size(166, 20);
      this.label_Side_Gate_G3.TabIndex = 228;
      this.label_Side_Gate_G3.Text = "<사이드 게이트>";
      this.label_Side_Gate_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim3_G3.BackColor = Color.Lavender;
      this.label_Side_RunnerDim3_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim3_G3.Location = new Point(166, 132);
      this.label_Side_RunnerDim3_G3.Name = "label_Side_RunnerDim3_G3";
      this.label_Side_RunnerDim3_G3.Size = new Size(120, 23);
      this.label_Side_RunnerDim3_G3.TabIndex = 236;
      this.label_Side_RunnerDim3_G3.Text = "Height";
      this.label_Side_RunnerDim3_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_RunnerDim2_G3.BackColor = Color.Lavender;
      this.label_Pin_RunnerDim2_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_RunnerDim2_G3.Location = new Point(495, 66);
      this.label_Pin_RunnerDim2_G3.Name = "label_Pin_RunnerDim2_G3";
      this.label_Pin_RunnerDim2_G3.Size = new Size(120, 23);
      this.label_Pin_RunnerDim2_G3.TabIndex = 119;
      this.label_Pin_RunnerDim2_G3.Text = "Outer Dimension";
      this.label_Pin_RunnerDim2_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateLength_G3.BackColor = Color.Lavender;
      this.label_Side_GateLength_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateLength_G3.Location = new Point(1, 66);
      this.label_Side_GateLength_G3.Name = "label_Side_GateLength_G3";
      this.label_Side_GateLength_G3.Size = new Size(120, 23);
      this.label_Side_GateLength_G3.TabIndex = 235;
      this.label_Side_GateLength_G3.Text = "게이트 길이";
      this.label_Side_GateLength_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim1_G3.BackColor = Color.Lavender;
      this.label_Side_GateDim1_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim1_G3.Location = new Point(1, 88);
      this.label_Side_GateDim1_G3.Name = "label_Side_GateDim1_G3";
      this.label_Side_GateDim1_G3.Size = new Size(120, 23);
      this.label_Side_GateDim1_G3.TabIndex = 233;
      this.label_Side_GateDim1_G3.Text = "Start Width";
      this.label_Side_GateDim1_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim3_G3.BackColor = Color.Lavender;
      this.label_Side_GateDim3_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim3_G3.Location = new Point(1, 132);
      this.label_Side_GateDim3_G3.Name = "label_Side_GateDim3_G3";
      this.label_Side_GateDim3_G3.Size = new Size(120, 23);
      this.label_Side_GateDim3_G3.TabIndex = 231;
      this.label_Side_GateDim3_G3.Text = "End Width";
      this.label_Side_GateDim3_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim2_G3.BackColor = Color.Lavender;
      this.label_Side_GateDim2_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim2_G3.Location = new Point(1, 110);
      this.label_Side_GateDim2_G3.Name = "label_Side_GateDim2_G3";
      this.label_Side_GateDim2_G3.Size = new Size(120, 23);
      this.label_Side_GateDim2_G3.TabIndex = 229;
      this.label_Side_GateDim2_G3.Text = "Start Height";
      this.label_Side_GateDim2_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim4_G3.BackColor = Color.Lavender;
      this.label_Side_GateDim4_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim4_G3.Location = new Point(1, 154);
      this.label_Side_GateDim4_G3.Name = "label_Side_GateDim4_G3";
      this.label_Side_GateDim4_G3.Size = new Size(120, 23);
      this.label_Side_GateDim4_G3.TabIndex = 237;
      this.label_Side_GateDim4_G3.Text = "End Height";
      this.label_Side_GateDim4_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Side_RunnerLength_G3.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerLength_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerLength_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerLength_G3.IsDigit = true;
      this.newTextBox_Side_RunnerLength_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerLength_G3.Location = new Point(284, 66);
      this.newTextBox_Side_RunnerLength_G3.MultiLine = false;
      this.newTextBox_Side_RunnerLength_G3.Name = "newTextBox_Side_RunnerLength_G3";
      this.newTextBox_Side_RunnerLength_G3.ReadOnly = false;
      this.newTextBox_Side_RunnerLength_G3.Size = new Size(47, 23);
      this.newTextBox_Side_RunnerLength_G3.TabIndex = 218;
      this.newTextBox_Side_RunnerLength_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerLength_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerLength_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerLength_G3.Value = "0";
      this.newTextBox_Side_RunnerDim4_G3.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim4_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim4_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim4_G3.IsDigit = true;
      this.newTextBox_Side_RunnerDim4_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim4_G3.Location = new Point(284, 154);
      this.newTextBox_Side_RunnerDim4_G3.MultiLine = false;
      this.newTextBox_Side_RunnerDim4_G3.Name = "newTextBox_Side_RunnerDim4_G3";
      this.newTextBox_Side_RunnerDim4_G3.ReadOnly = false;
      this.newTextBox_Side_RunnerDim4_G3.Size = new Size(47, 23);
      this.newTextBox_Side_RunnerDim4_G3.TabIndex = 225;
      this.newTextBox_Side_RunnerDim4_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim4_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim4_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim4_G3.Value = "0";
      this.newButton_Pin_GateLength_G3.BackColor = Color.Lavender;
      this.newButton_Pin_GateLength_G3.ButtonBackColor = Color.Lavender;
      this.newButton_Pin_GateLength_G3.ButtonText = "게이트 길이";
      this.newButton_Pin_GateLength_G3.FlatBorderSize = 1;
      this.newButton_Pin_GateLength_G3.FlatStyle = FlatStyle.Flat;
      this.newButton_Pin_GateLength_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Pin_GateLength_G3.ForeColor = SystemColors.ControlText;
      this.newButton_Pin_GateLength_G3.Image = (Image) null;
      this.newButton_Pin_GateLength_G3.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Pin_GateLength_G3.Location = new Point(330, 22);
      this.newButton_Pin_GateLength_G3.Margin = new Padding(3, 0, 3, 0);
      this.newButton_Pin_GateLength_G3.Name = "newButton_Pin_GateLength_G3";
      this.newButton_Pin_GateLength_G3.Size = new Size(120, 23);
      this.newButton_Pin_GateLength_G3.TabIndex = 120;
      this.newButton_Pin_GateLength_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Pin_GateLength_G3.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Pin_GateLength_G3.NewClick += new EventHandler(this.newButton_Pin_GateLength_NewClick);
      this.newTextBox_Side_RunnerDim1_G3.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim1_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim1_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim1_G3.IsDigit = true;
      this.newTextBox_Side_RunnerDim1_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim1_G3.Location = new Point(284, 88);
      this.newTextBox_Side_RunnerDim1_G3.MultiLine = false;
      this.newTextBox_Side_RunnerDim1_G3.Name = "newTextBox_Side_RunnerDim1_G3";
      this.newTextBox_Side_RunnerDim1_G3.ReadOnly = false;
      this.newTextBox_Side_RunnerDim1_G3.Size = new Size(47, 23);
      this.newTextBox_Side_RunnerDim1_G3.TabIndex = 219;
      this.newTextBox_Side_RunnerDim1_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim1_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim1_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim1_G3.Value = "0";
      this.newTextBox_Pin_GateLength_G3.BackColor = SystemColors.Window;
      this.newTextBox_Pin_GateLength_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_GateLength_G3.Enabled = false;
      this.newTextBox_Pin_GateLength_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_GateLength_G3.IsDigit = true;
      this.newTextBox_Pin_GateLength_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_GateLength_G3.Location = new Point(449, 22);
      this.newTextBox_Pin_GateLength_G3.MultiLine = false;
      this.newTextBox_Pin_GateLength_G3.Name = "newTextBox_Pin_GateLength_G3";
      this.newTextBox_Pin_GateLength_G3.ReadOnly = true;
      this.newTextBox_Pin_GateLength_G3.Size = new Size(47, 23);
      this.newTextBox_Pin_GateLength_G3.TabIndex = 42;
      this.newTextBox_Pin_GateLength_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_GateLength_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_GateLength_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_GateLength_G3.Value = "0";
      this.newTextBox_Side_RunnerDim2_G3.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim2_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim2_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim2_G3.IsDigit = true;
      this.newTextBox_Side_RunnerDim2_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim2_G3.Location = new Point(284, 110);
      this.newTextBox_Side_RunnerDim2_G3.MultiLine = false;
      this.newTextBox_Side_RunnerDim2_G3.Name = "newTextBox_Side_RunnerDim2_G3";
      this.newTextBox_Side_RunnerDim2_G3.ReadOnly = false;
      this.newTextBox_Side_RunnerDim2_G3.Size = new Size(47, 23);
      this.newTextBox_Side_RunnerDim2_G3.TabIndex = 221;
      this.newTextBox_Side_RunnerDim2_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim2_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim2_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim2_G3.Value = "0";
      this.newTextBox_Pin_GateDim1_G3.BackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim1_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_GateDim1_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_GateDim1_G3.IsDigit = true;
      this.newTextBox_Pin_GateDim1_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_GateDim1_G3.Location = new Point(449, 44);
      this.newTextBox_Pin_GateDim1_G3.MultiLine = false;
      this.newTextBox_Pin_GateDim1_G3.Name = "newTextBox_Pin_GateDim1_G3";
      this.newTextBox_Pin_GateDim1_G3.ReadOnly = false;
      this.newTextBox_Pin_GateDim1_G3.Size = new Size(47, 23);
      this.newTextBox_Pin_GateDim1_G3.TabIndex = 43;
      this.newTextBox_Pin_GateDim1_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_GateDim1_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim1_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_GateDim1_G3.Value = "0";
      this.newTextBox_Side_RunnerDim3_G3.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim3_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim3_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim3_G3.IsDigit = true;
      this.newTextBox_Side_RunnerDim3_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim3_G3.Location = new Point(284, 132);
      this.newTextBox_Side_RunnerDim3_G3.MultiLine = false;
      this.newTextBox_Side_RunnerDim3_G3.Name = "newTextBox_Side_RunnerDim3_G3";
      this.newTextBox_Side_RunnerDim3_G3.ReadOnly = false;
      this.newTextBox_Side_RunnerDim3_G3.Size = new Size(47, 23);
      this.newTextBox_Side_RunnerDim3_G3.TabIndex = 223;
      this.newTextBox_Side_RunnerDim3_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim3_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim3_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim3_G3.Value = "0";
      this.newTextBox_Pin_GateDim2_G3.BackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim2_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_GateDim2_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_GateDim2_G3.IsDigit = true;
      this.newTextBox_Pin_GateDim2_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_GateDim2_G3.Location = new Point(449, 66);
      this.newTextBox_Pin_GateDim2_G3.MultiLine = false;
      this.newTextBox_Pin_GateDim2_G3.Name = "newTextBox_Pin_GateDim2_G3";
      this.newTextBox_Pin_GateDim2_G3.ReadOnly = false;
      this.newTextBox_Pin_GateDim2_G3.Size = new Size(47, 23);
      this.newTextBox_Pin_GateDim2_G3.TabIndex = 44;
      this.newTextBox_Pin_GateDim2_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_GateDim2_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim2_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_GateDim2_G3.Value = "0";
      this.newTextBox_Pin_RunnerLength_G3.BackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerLength_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_RunnerLength_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_RunnerLength_G3.IsDigit = true;
      this.newTextBox_Pin_RunnerLength_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_RunnerLength_G3.Location = new Point(614, 22);
      this.newTextBox_Pin_RunnerLength_G3.MultiLine = false;
      this.newTextBox_Pin_RunnerLength_G3.Name = "newTextBox_Pin_RunnerLength_G3";
      this.newTextBox_Pin_RunnerLength_G3.ReadOnly = false;
      this.newTextBox_Pin_RunnerLength_G3.Size = new Size(47, 23);
      this.newTextBox_Pin_RunnerLength_G3.TabIndex = 45;
      this.newTextBox_Pin_RunnerLength_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_RunnerLength_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerLength_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_RunnerLength_G3.Value = "0";
      this.newTextBox_Side_GateDim4_G3.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim4_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim4_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim4_G3.IsDigit = true;
      this.newTextBox_Side_GateDim4_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim4_G3.Location = new Point(120, 154);
      this.newTextBox_Side_GateDim4_G3.MultiLine = false;
      this.newTextBox_Side_GateDim4_G3.Name = "newTextBox_Side_GateDim4_G3";
      this.newTextBox_Side_GateDim4_G3.ReadOnly = false;
      this.newTextBox_Side_GateDim4_G3.Size = new Size(47, 23);
      this.newTextBox_Side_GateDim4_G3.TabIndex = 216;
      this.newTextBox_Side_GateDim4_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim4_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim4_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim4_G3.Value = "0";
      this.newTextBox_Side_GateLength_G3.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateLength_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateLength_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateLength_G3.IsDigit = true;
      this.newTextBox_Side_GateLength_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateLength_G3.Location = new Point(120, 66);
      this.newTextBox_Side_GateLength_G3.MultiLine = false;
      this.newTextBox_Side_GateLength_G3.Name = "newTextBox_Side_GateLength_G3";
      this.newTextBox_Side_GateLength_G3.ReadOnly = false;
      this.newTextBox_Side_GateLength_G3.Size = new Size(47, 23);
      this.newTextBox_Side_GateLength_G3.TabIndex = 212;
      this.newTextBox_Side_GateLength_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateLength_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateLength_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateLength_G3.Value = "0";
      this.newTextBox_Pin_RunnerDim1_G3.BackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim1_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_RunnerDim1_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_RunnerDim1_G3.IsDigit = true;
      this.newTextBox_Pin_RunnerDim1_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_RunnerDim1_G3.Location = new Point(614, 44);
      this.newTextBox_Pin_RunnerDim1_G3.MultiLine = false;
      this.newTextBox_Pin_RunnerDim1_G3.Name = "newTextBox_Pin_RunnerDim1_G3";
      this.newTextBox_Pin_RunnerDim1_G3.ReadOnly = false;
      this.newTextBox_Pin_RunnerDim1_G3.Size = new Size(47, 23);
      this.newTextBox_Pin_RunnerDim1_G3.TabIndex = 46;
      this.newTextBox_Pin_RunnerDim1_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_RunnerDim1_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim1_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_RunnerDim1_G3.Value = "0";
      this.newTextBox_Side_GateDim3_G3.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim3_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim3_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim3_G3.IsDigit = true;
      this.newTextBox_Side_GateDim3_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim3_G3.Location = new Point(120, 132);
      this.newTextBox_Side_GateDim3_G3.MultiLine = false;
      this.newTextBox_Side_GateDim3_G3.Name = "newTextBox_Side_GateDim3_G3";
      this.newTextBox_Side_GateDim3_G3.ReadOnly = false;
      this.newTextBox_Side_GateDim3_G3.Size = new Size(47, 23);
      this.newTextBox_Side_GateDim3_G3.TabIndex = 215;
      this.newTextBox_Side_GateDim3_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim3_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim3_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim3_G3.Value = "0";
      this.newTextBox_Side_GateDim1_G3.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim1_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim1_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim1_G3.IsDigit = true;
      this.newTextBox_Side_GateDim1_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim1_G3.Location = new Point(120, 88);
      this.newTextBox_Side_GateDim1_G3.MultiLine = false;
      this.newTextBox_Side_GateDim1_G3.Name = "newTextBox_Side_GateDim1_G3";
      this.newTextBox_Side_GateDim1_G3.ReadOnly = false;
      this.newTextBox_Side_GateDim1_G3.Size = new Size(47, 23);
      this.newTextBox_Side_GateDim1_G3.TabIndex = 213;
      this.newTextBox_Side_GateDim1_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim1_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim1_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim1_G3.Value = "0";
      this.newTextBox_Pin_RunnerDim2_G3.BackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim2_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_RunnerDim2_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_RunnerDim2_G3.IsDigit = true;
      this.newTextBox_Pin_RunnerDim2_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_RunnerDim2_G3.Location = new Point(614, 66);
      this.newTextBox_Pin_RunnerDim2_G3.MultiLine = false;
      this.newTextBox_Pin_RunnerDim2_G3.Name = "newTextBox_Pin_RunnerDim2_G3";
      this.newTextBox_Pin_RunnerDim2_G3.ReadOnly = false;
      this.newTextBox_Pin_RunnerDim2_G3.Size = new Size(47, 23);
      this.newTextBox_Pin_RunnerDim2_G3.TabIndex = 47;
      this.newTextBox_Pin_RunnerDim2_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_RunnerDim2_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim2_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_RunnerDim2_G3.Value = "0";
      this.newTextBox_Side_GateDim2_G3.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim2_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim2_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim2_G3.IsDigit = true;
      this.newTextBox_Side_GateDim2_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim2_G3.Location = new Point(120, 110);
      this.newTextBox_Side_GateDim2_G3.MultiLine = false;
      this.newTextBox_Side_GateDim2_G3.Name = "newTextBox_Side_GateDim2_G3";
      this.newTextBox_Side_GateDim2_G3.ReadOnly = false;
      this.newTextBox_Side_GateDim2_G3.Size = new Size(47, 23);
      this.newTextBox_Side_GateDim2_G3.TabIndex = 214;
      this.newTextBox_Side_GateDim2_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim2_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim2_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim2_G3.Value = "0";
      this.label_Side_Runner_G3.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Side_Runner_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_Runner_G3.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Side_Runner_G3.ForeColor = Color.MidnightBlue;
      this.label_Side_Runner_G3.Location = new Point(166, 3);
      this.label_Side_Runner_G3.Name = "label_Side_Runner_G3";
      this.label_Side_Runner_G3.Size = new Size(166, 20);
      this.label_Side_Runner_G3.TabIndex = 227;
      this.label_Side_Runner_G3.Text = "<사이드 런너>";
      this.label_Side_Runner_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.panel_Side_RunnerType_G3.BackColor = Color.White;
      this.panel_Side_RunnerType_G3.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Side_RunnerType_G3.Controls.Add((Control) this.radioButton_Side_RunnerRectangle_G3);
      this.panel_Side_RunnerType_G3.Controls.Add((Control) this.radioButton_Side_RunnerCircle_G3);
      this.panel_Side_RunnerType_G3.Controls.Add((Control) this.radioButton_Side_RunnerTrepezoidal_G3);
      this.panel_Side_RunnerType_G3.Location = new Point(166, 22);
      this.panel_Side_RunnerType_G3.Name = "panel_Side_RunnerType_G3";
      this.panel_Side_RunnerType_G3.Size = new Size(166, 45);
      this.panel_Side_RunnerType_G3.TabIndex = 217;
      this.radioButton_Side_RunnerRectangle_G3.AutoSize = true;
      this.radioButton_Side_RunnerRectangle_G3.Location = new Point(6, 23);
      this.radioButton_Side_RunnerRectangle_G3.Name = "radioButton_Side_RunnerRectangle_G3";
      this.radioButton_Side_RunnerRectangle_G3.Size = new Size(77, 19);
      this.radioButton_Side_RunnerRectangle_G3.TabIndex = 96;
      this.radioButton_Side_RunnerRectangle_G3.Text = "Rectangle";
      this.radioButton_Side_RunnerRectangle_G3.UseVisualStyleBackColor = true;
      this.radioButton_Side_RunnerRectangle_G3.CheckedChanged += new EventHandler(this.radioButton_Side_RunnerRectangle_CheckedChanged);
      this.radioButton_Side_RunnerCircle_G3.AutoSize = true;
      this.radioButton_Side_RunnerCircle_G3.Location = new Point(96, 2);
      this.radioButton_Side_RunnerCircle_G3.Name = "radioButton_Side_RunnerCircle_G3";
      this.radioButton_Side_RunnerCircle_G3.Size = new Size(55, 19);
      this.radioButton_Side_RunnerCircle_G3.TabIndex = 95;
      this.radioButton_Side_RunnerCircle_G3.Text = "Circle";
      this.radioButton_Side_RunnerCircle_G3.UseVisualStyleBackColor = true;
      this.radioButton_Side_RunnerCircle_G3.CheckedChanged += new EventHandler(this.radioButton_Side_RunnerCircle_CheckedChanged);
      this.radioButton_Side_RunnerTrepezoidal_G3.AutoSize = true;
      this.radioButton_Side_RunnerTrepezoidal_G3.Location = new Point(6, 2);
      this.radioButton_Side_RunnerTrepezoidal_G3.Name = "radioButton_Side_RunnerTrepezoidal_G3";
      this.radioButton_Side_RunnerTrepezoidal_G3.Size = new Size(84, 19);
      this.radioButton_Side_RunnerTrepezoidal_G3.TabIndex = 94;
      this.radioButton_Side_RunnerTrepezoidal_G3.Text = "Trepezoidal";
      this.radioButton_Side_RunnerTrepezoidal_G3.UseVisualStyleBackColor = true;
      this.radioButton_Side_RunnerTrepezoidal_G3.CheckedChanged += new EventHandler(this.radioButton_Side_RunnerTrepezoidal_CheckedChanged);
      this.tabPage_G4.Controls.Add((Control) this.label_Side_RunnerDim4_G4);
      this.tabPage_G4.Controls.Add((Control) this.label_Side_RunnerLength_G4);
      this.tabPage_G4.Controls.Add((Control) this.label_Side_RunnerDim2_G4);
      this.tabPage_G4.Controls.Add((Control) this.label_Side_RunnerDim1_G4);
      this.tabPage_G4.Controls.Add((Control) this.label_Side_RunnerDim3_G4);
      this.tabPage_G4.Controls.Add((Control) this.newTextBox_Side_RunnerLength_G4);
      this.tabPage_G4.Controls.Add((Control) this.newTextBox_Side_RunnerDim4_G4);
      this.tabPage_G4.Controls.Add((Control) this.newTextBox_Side_RunnerDim3_G4);
      this.tabPage_G4.Controls.Add((Control) this.newTextBox_Side_RunnerDim2_G4);
      this.tabPage_G4.Controls.Add((Control) this.newTextBox_Side_RunnerDim1_G4);
      this.tabPage_G4.Controls.Add((Control) this.label_Pin_GateDim2_G4);
      this.tabPage_G4.Controls.Add((Control) this.label_Pin_GateDim1_G4);
      this.tabPage_G4.Controls.Add((Control) this.newButton_Pin_GateLength_G4);
      this.tabPage_G4.Controls.Add((Control) this.label_Pin_Gate_G4);
      this.tabPage_G4.Controls.Add((Control) this.label_Side_Runner_G4);
      this.tabPage_G4.Controls.Add((Control) this.label_Pin_Runner_G4);
      this.tabPage_G4.Controls.Add((Control) this.panel_Side_RunnerType_G4);
      this.tabPage_G4.Controls.Add((Control) this.panel_Side_GateType1_G4);
      this.tabPage_G4.Controls.Add((Control) this.label_Pin_RunnerLength_G4);
      this.tabPage_G4.Controls.Add((Control) this.label_Pin_RunnerDim1_G4);
      this.tabPage_G4.Controls.Add((Control) this.panel_Side_GateType2_G4);
      this.tabPage_G4.Controls.Add((Control) this.label_Side_Gate_G4);
      this.tabPage_G4.Controls.Add((Control) this.label_Pin_RunnerDim2_G4);
      this.tabPage_G4.Controls.Add((Control) this.label_Side_GateLength_G4);
      this.tabPage_G4.Controls.Add((Control) this.label_Side_GateDim1_G4);
      this.tabPage_G4.Controls.Add((Control) this.label_Side_GateDim3_G4);
      this.tabPage_G4.Controls.Add((Control) this.label_Side_GateDim2_G4);
      this.tabPage_G4.Controls.Add((Control) this.label_Side_GateDim4_G4);
      this.tabPage_G4.Controls.Add((Control) this.newTextBox_Pin_GateLength_G4);
      this.tabPage_G4.Controls.Add((Control) this.newTextBox_Pin_GateDim1_G4);
      this.tabPage_G4.Controls.Add((Control) this.newTextBox_Pin_GateDim2_G4);
      this.tabPage_G4.Controls.Add((Control) this.newTextBox_Pin_RunnerLength_G4);
      this.tabPage_G4.Controls.Add((Control) this.newTextBox_Side_GateDim4_G4);
      this.tabPage_G4.Controls.Add((Control) this.newTextBox_Side_GateLength_G4);
      this.tabPage_G4.Controls.Add((Control) this.newTextBox_Pin_RunnerDim1_G4);
      this.tabPage_G4.Controls.Add((Control) this.newTextBox_Side_GateDim3_G4);
      this.tabPage_G4.Controls.Add((Control) this.newTextBox_Side_GateDim1_G4);
      this.tabPage_G4.Controls.Add((Control) this.newTextBox_Pin_RunnerDim2_G4);
      this.tabPage_G4.Controls.Add((Control) this.newTextBox_Side_GateDim2_G4);
      this.tabPage_G4.Location = new Point(4, 22);
      this.tabPage_G4.Name = "tabPage_G4";
      this.tabPage_G4.Padding = new Padding(3);
      this.tabPage_G4.Size = new Size(662, 182);
      this.tabPage_G4.TabIndex = 3;
      this.tabPage_G4.UseVisualStyleBackColor = true;
      this.label_Side_RunnerDim4_G4.BackColor = Color.Lavender;
      this.label_Side_RunnerDim4_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim4_G4.Location = new Point(166, 154);
      this.label_Side_RunnerDim4_G4.Name = "label_Side_RunnerDim4_G4";
      this.label_Side_RunnerDim4_G4.Size = new Size(120, 23);
      this.label_Side_RunnerDim4_G4.TabIndex = 277;
      this.label_Side_RunnerDim4_G4.Text = "End Height";
      this.label_Side_RunnerDim4_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerLength_G4.BackColor = Color.Lavender;
      this.label_Side_RunnerLength_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerLength_G4.Location = new Point(166, 66);
      this.label_Side_RunnerLength_G4.Name = "label_Side_RunnerLength_G4";
      this.label_Side_RunnerLength_G4.Size = new Size(120, 23);
      this.label_Side_RunnerLength_G4.TabIndex = 271;
      this.label_Side_RunnerLength_G4.Text = "런너 길이";
      this.label_Side_RunnerLength_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim2_G4.BackColor = Color.Lavender;
      this.label_Side_RunnerDim2_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim2_G4.Location = new Point(166, 110);
      this.label_Side_RunnerDim2_G4.Name = "label_Side_RunnerDim2_G4";
      this.label_Side_RunnerDim2_G4.Size = new Size(120, 23);
      this.label_Side_RunnerDim2_G4.TabIndex = 269;
      this.label_Side_RunnerDim2_G4.Text = "Bottom Width";
      this.label_Side_RunnerDim2_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim1_G4.BackColor = Color.Lavender;
      this.label_Side_RunnerDim1_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim1_G4.Location = new Point(166, 88);
      this.label_Side_RunnerDim1_G4.Name = "label_Side_RunnerDim1_G4";
      this.label_Side_RunnerDim1_G4.Size = new Size(120, 23);
      this.label_Side_RunnerDim1_G4.TabIndex = 273;
      this.label_Side_RunnerDim1_G4.Text = "Top Width";
      this.label_Side_RunnerDim1_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim3_G4.BackColor = Color.Lavender;
      this.label_Side_RunnerDim3_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim3_G4.Location = new Point(166, 132);
      this.label_Side_RunnerDim3_G4.Name = "label_Side_RunnerDim3_G4";
      this.label_Side_RunnerDim3_G4.Size = new Size(120, 23);
      this.label_Side_RunnerDim3_G4.TabIndex = 275;
      this.label_Side_RunnerDim3_G4.Text = "Height";
      this.label_Side_RunnerDim3_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Side_RunnerLength_G4.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerLength_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerLength_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerLength_G4.IsDigit = true;
      this.newTextBox_Side_RunnerLength_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerLength_G4.Location = new Point(284, 66);
      this.newTextBox_Side_RunnerLength_G4.MultiLine = false;
      this.newTextBox_Side_RunnerLength_G4.Name = "newTextBox_Side_RunnerLength_G4";
      this.newTextBox_Side_RunnerLength_G4.ReadOnly = false;
      this.newTextBox_Side_RunnerLength_G4.Size = new Size(47, 23);
      this.newTextBox_Side_RunnerLength_G4.TabIndex = 261;
      this.newTextBox_Side_RunnerLength_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerLength_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerLength_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerLength_G4.Value = "0";
      this.newTextBox_Side_RunnerDim4_G4.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim4_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim4_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim4_G4.IsDigit = true;
      this.newTextBox_Side_RunnerDim4_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim4_G4.Location = new Point(284, 154);
      this.newTextBox_Side_RunnerDim4_G4.MultiLine = false;
      this.newTextBox_Side_RunnerDim4_G4.Name = "newTextBox_Side_RunnerDim4_G4";
      this.newTextBox_Side_RunnerDim4_G4.ReadOnly = false;
      this.newTextBox_Side_RunnerDim4_G4.Size = new Size(47, 23);
      this.newTextBox_Side_RunnerDim4_G4.TabIndex = 265;
      this.newTextBox_Side_RunnerDim4_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim4_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim4_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim4_G4.Value = "0";
      this.newTextBox_Side_RunnerDim3_G4.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim3_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim3_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim3_G4.IsDigit = true;
      this.newTextBox_Side_RunnerDim3_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim3_G4.Location = new Point(284, 132);
      this.newTextBox_Side_RunnerDim3_G4.MultiLine = false;
      this.newTextBox_Side_RunnerDim3_G4.Name = "newTextBox_Side_RunnerDim3_G4";
      this.newTextBox_Side_RunnerDim3_G4.ReadOnly = false;
      this.newTextBox_Side_RunnerDim3_G4.Size = new Size(47, 23);
      this.newTextBox_Side_RunnerDim3_G4.TabIndex = 264;
      this.newTextBox_Side_RunnerDim3_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim3_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim3_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim3_G4.Value = "0";
      this.newTextBox_Side_RunnerDim2_G4.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim2_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim2_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim2_G4.IsDigit = true;
      this.newTextBox_Side_RunnerDim2_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim2_G4.Location = new Point(284, 110);
      this.newTextBox_Side_RunnerDim2_G4.MultiLine = false;
      this.newTextBox_Side_RunnerDim2_G4.Name = "newTextBox_Side_RunnerDim2_G4";
      this.newTextBox_Side_RunnerDim2_G4.ReadOnly = false;
      this.newTextBox_Side_RunnerDim2_G4.Size = new Size(47, 23);
      this.newTextBox_Side_RunnerDim2_G4.TabIndex = 263;
      this.newTextBox_Side_RunnerDim2_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim2_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim2_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim2_G4.Value = "0";
      this.newTextBox_Side_RunnerDim1_G4.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim1_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim1_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim1_G4.IsDigit = true;
      this.newTextBox_Side_RunnerDim1_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim1_G4.Location = new Point(284, 88);
      this.newTextBox_Side_RunnerDim1_G4.MultiLine = false;
      this.newTextBox_Side_RunnerDim1_G4.Name = "newTextBox_Side_RunnerDim1_G4";
      this.newTextBox_Side_RunnerDim1_G4.ReadOnly = false;
      this.newTextBox_Side_RunnerDim1_G4.Size = new Size(47, 23);
      this.newTextBox_Side_RunnerDim1_G4.TabIndex = 262;
      this.newTextBox_Side_RunnerDim1_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim1_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim1_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim1_G4.Value = "0";
      this.label_Pin_GateDim2_G4.BackColor = Color.Lavender;
      this.label_Pin_GateDim2_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_GateDim2_G4.Location = new Point(330, 66);
      this.label_Pin_GateDim2_G4.Name = "label_Pin_GateDim2_G4";
      this.label_Pin_GateDim2_G4.Size = new Size(120, 23);
      this.label_Pin_GateDim2_G4.TabIndex = 115;
      this.label_Pin_GateDim2_G4.Text = "End Dimension";
      this.label_Pin_GateDim2_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_GateDim1_G4.BackColor = Color.Lavender;
      this.label_Pin_GateDim1_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_GateDim1_G4.Location = new Point(330, 44);
      this.label_Pin_GateDim1_G4.Name = "label_Pin_GateDim1_G4";
      this.label_Pin_GateDim1_G4.Size = new Size(120, 23);
      this.label_Pin_GateDim1_G4.TabIndex = 118;
      this.label_Pin_GateDim1_G4.Text = "Start Dimension";
      this.label_Pin_GateDim1_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Pin_GateLength_G4.BackColor = Color.Lavender;
      this.newButton_Pin_GateLength_G4.ButtonBackColor = Color.Lavender;
      this.newButton_Pin_GateLength_G4.ButtonText = "게이트 길이";
      this.newButton_Pin_GateLength_G4.FlatBorderSize = 1;
      this.newButton_Pin_GateLength_G4.FlatStyle = FlatStyle.Flat;
      this.newButton_Pin_GateLength_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Pin_GateLength_G4.ForeColor = SystemColors.ControlText;
      this.newButton_Pin_GateLength_G4.Image = (Image) null;
      this.newButton_Pin_GateLength_G4.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Pin_GateLength_G4.Location = new Point(330, 22);
      this.newButton_Pin_GateLength_G4.Margin = new Padding(3, 0, 3, 0);
      this.newButton_Pin_GateLength_G4.Name = "newButton_Pin_GateLength_G4";
      this.newButton_Pin_GateLength_G4.Size = new Size(120, 23);
      this.newButton_Pin_GateLength_G4.TabIndex = 120;
      this.newButton_Pin_GateLength_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Pin_GateLength_G4.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Pin_GateLength_G4.NewClick += new EventHandler(this.newButton_Pin_GateLength_NewClick);
      this.label_Pin_Gate_G4.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Pin_Gate_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_Gate_G4.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Pin_Gate_G4.ForeColor = Color.MidnightBlue;
      this.label_Pin_Gate_G4.Location = new Point(330, 3);
      this.label_Pin_Gate_G4.Name = "label_Pin_Gate_G4";
      this.label_Pin_Gate_G4.Size = new Size(166, 20);
      this.label_Pin_Gate_G4.TabIndex = 114;
      this.label_Pin_Gate_G4.Text = "<핀 게이트>";
      this.label_Pin_Gate_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_Runner_G4.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Side_Runner_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_Runner_G4.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Side_Runner_G4.ForeColor = Color.MidnightBlue;
      this.label_Side_Runner_G4.Location = new Point(166, 3);
      this.label_Side_Runner_G4.Name = "label_Side_Runner_G4";
      this.label_Side_Runner_G4.Size = new Size(166, 20);
      this.label_Side_Runner_G4.TabIndex = 266;
      this.label_Side_Runner_G4.Text = "<사이드 런너>";
      this.label_Side_Runner_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_Runner_G4.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Pin_Runner_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_Runner_G4.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Pin_Runner_G4.ForeColor = Color.MidnightBlue;
      this.label_Pin_Runner_G4.Location = new Point(495, 3);
      this.label_Pin_Runner_G4.Name = "label_Pin_Runner_G4";
      this.label_Pin_Runner_G4.Size = new Size(166, 20);
      this.label_Pin_Runner_G4.TabIndex = 113;
      this.label_Pin_Runner_G4.Text = "<핀 런너>";
      this.label_Pin_Runner_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.panel_Side_RunnerType_G4.BackColor = Color.White;
      this.panel_Side_RunnerType_G4.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Side_RunnerType_G4.Controls.Add((Control) this.radioButton_Side_RunnerRectangle_G4);
      this.panel_Side_RunnerType_G4.Controls.Add((Control) this.radioButton_Side_RunnerCircle_G4);
      this.panel_Side_RunnerType_G4.Controls.Add((Control) this.radioButton_Side_RunnerTrepezoidal_G4);
      this.panel_Side_RunnerType_G4.Location = new Point(166, 22);
      this.panel_Side_RunnerType_G4.Name = "panel_Side_RunnerType_G4";
      this.panel_Side_RunnerType_G4.Size = new Size(166, 45);
      this.panel_Side_RunnerType_G4.TabIndex = 260;
      this.radioButton_Side_RunnerRectangle_G4.AutoSize = true;
      this.radioButton_Side_RunnerRectangle_G4.Location = new Point(6, 23);
      this.radioButton_Side_RunnerRectangle_G4.Name = "radioButton_Side_RunnerRectangle_G4";
      this.radioButton_Side_RunnerRectangle_G4.Size = new Size(77, 19);
      this.radioButton_Side_RunnerRectangle_G4.TabIndex = 96;
      this.radioButton_Side_RunnerRectangle_G4.Text = "Rectangle";
      this.radioButton_Side_RunnerRectangle_G4.UseVisualStyleBackColor = true;
      this.radioButton_Side_RunnerRectangle_G4.CheckedChanged += new EventHandler(this.radioButton_Side_RunnerRectangle_CheckedChanged);
      this.radioButton_Side_RunnerCircle_G4.AutoSize = true;
      this.radioButton_Side_RunnerCircle_G4.Location = new Point(96, 2);
      this.radioButton_Side_RunnerCircle_G4.Name = "radioButton_Side_RunnerCircle_G4";
      this.radioButton_Side_RunnerCircle_G4.Size = new Size(55, 19);
      this.radioButton_Side_RunnerCircle_G4.TabIndex = 95;
      this.radioButton_Side_RunnerCircle_G4.Text = "Circle";
      this.radioButton_Side_RunnerCircle_G4.UseVisualStyleBackColor = true;
      this.radioButton_Side_RunnerCircle_G4.CheckedChanged += new EventHandler(this.radioButton_Side_RunnerCircle_CheckedChanged);
      this.radioButton_Side_RunnerTrepezoidal_G4.AutoSize = true;
      this.radioButton_Side_RunnerTrepezoidal_G4.Location = new Point(6, 2);
      this.radioButton_Side_RunnerTrepezoidal_G4.Name = "radioButton_Side_RunnerTrepezoidal_G4";
      this.radioButton_Side_RunnerTrepezoidal_G4.Size = new Size(84, 19);
      this.radioButton_Side_RunnerTrepezoidal_G4.TabIndex = 94;
      this.radioButton_Side_RunnerTrepezoidal_G4.Text = "Trepezoidal";
      this.radioButton_Side_RunnerTrepezoidal_G4.UseVisualStyleBackColor = true;
      this.radioButton_Side_RunnerTrepezoidal_G4.CheckedChanged += new EventHandler(this.radioButton_Side_RunnerTrepezoidal_CheckedChanged);
      this.panel_Side_GateType1_G4.BackColor = Color.White;
      this.panel_Side_GateType1_G4.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Side_GateType1_G4.Controls.Add((Control) this.radioButton_Side_GateCircle_G4);
      this.panel_Side_GateType1_G4.Controls.Add((Control) this.radioButton_Side_GateRect_G4);
      this.panel_Side_GateType1_G4.Location = new Point(1, 22);
      this.panel_Side_GateType1_G4.Name = "panel_Side_GateType1_G4";
      this.panel_Side_GateType1_G4.Size = new Size(166, 23);
      this.panel_Side_GateType1_G4.TabIndex = 253;
      this.radioButton_Side_GateCircle_G4.AutoSize = true;
      this.radioButton_Side_GateCircle_G4.Location = new Point(85, 2);
      this.radioButton_Side_GateCircle_G4.Name = "radioButton_Side_GateCircle_G4";
      this.radioButton_Side_GateCircle_G4.Size = new Size(55, 19);
      this.radioButton_Side_GateCircle_G4.TabIndex = 84;
      this.radioButton_Side_GateCircle_G4.Text = "Circle";
      this.radioButton_Side_GateCircle_G4.UseVisualStyleBackColor = true;
      this.radioButton_Side_GateCircle_G4.CheckedChanged += new EventHandler(this.radioButton_Side_GateCircle_CheckedChanged);
      this.radioButton_Side_GateRect_G4.AutoSize = true;
      this.radioButton_Side_GateRect_G4.Location = new Point(7, 2);
      this.radioButton_Side_GateRect_G4.Name = "radioButton_Side_GateRect_G4";
      this.radioButton_Side_GateRect_G4.Size = new Size(77, 19);
      this.radioButton_Side_GateRect_G4.TabIndex = 83;
      this.radioButton_Side_GateRect_G4.Text = "Rectangle";
      this.radioButton_Side_GateRect_G4.UseVisualStyleBackColor = true;
      this.radioButton_Side_GateRect_G4.CheckedChanged += new EventHandler(this.radioButton_Side_GateRect_CheckedChanged);
      this.label_Pin_RunnerLength_G4.BackColor = Color.Lavender;
      this.label_Pin_RunnerLength_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_RunnerLength_G4.Location = new Point(495, 22);
      this.label_Pin_RunnerLength_G4.Name = "label_Pin_RunnerLength_G4";
      this.label_Pin_RunnerLength_G4.Size = new Size(120, 23);
      this.label_Pin_RunnerLength_G4.TabIndex = 116;
      this.label_Pin_RunnerLength_G4.Text = "런너 길이";
      this.label_Pin_RunnerLength_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_RunnerDim1_G4.BackColor = Color.Lavender;
      this.label_Pin_RunnerDim1_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_RunnerDim1_G4.Location = new Point(495, 44);
      this.label_Pin_RunnerDim1_G4.Name = "label_Pin_RunnerDim1_G4";
      this.label_Pin_RunnerDim1_G4.Size = new Size(120, 23);
      this.label_Pin_RunnerDim1_G4.TabIndex = 117;
      this.label_Pin_RunnerDim1_G4.Text = "Inner Dimension";
      this.label_Pin_RunnerDim1_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.panel_Side_GateType2_G4.BackColor = Color.White;
      this.panel_Side_GateType2_G4.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Side_GateType2_G4.Controls.Add((Control) this.radioButton_Side_GateSubMarine_G4);
      this.panel_Side_GateType2_G4.Controls.Add((Control) this.radioButton_Side_GateNormal_G4);
      this.panel_Side_GateType2_G4.Location = new Point(1, 44);
      this.panel_Side_GateType2_G4.Name = "panel_Side_GateType2_G4";
      this.panel_Side_GateType2_G4.Size = new Size(166, 23);
      this.panel_Side_GateType2_G4.TabIndex = 254;
      this.radioButton_Side_GateSubMarine_G4.AutoSize = true;
      this.radioButton_Side_GateSubMarine_G4.Location = new Point(85, 2);
      this.radioButton_Side_GateSubMarine_G4.Name = "radioButton_Side_GateSubMarine_G4";
      this.radioButton_Side_GateSubMarine_G4.Size = new Size(73, 19);
      this.radioButton_Side_GateSubMarine_G4.TabIndex = 87;
      this.radioButton_Side_GateSubMarine_G4.Text = "서브마린";
      this.radioButton_Side_GateSubMarine_G4.UseVisualStyleBackColor = true;
      this.radioButton_Side_GateNormal_G4.AutoSize = true;
      this.radioButton_Side_GateNormal_G4.Location = new Point(8, 2);
      this.radioButton_Side_GateNormal_G4.Name = "radioButton_Side_GateNormal_G4";
      this.radioButton_Side_GateNormal_G4.Size = new Size(49, 19);
      this.radioButton_Side_GateNormal_G4.TabIndex = 86;
      this.radioButton_Side_GateNormal_G4.Text = "일반";
      this.radioButton_Side_GateNormal_G4.UseVisualStyleBackColor = true;
      this.label_Side_Gate_G4.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Side_Gate_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_Gate_G4.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Side_Gate_G4.ForeColor = Color.MidnightBlue;
      this.label_Side_Gate_G4.Location = new Point(1, 3);
      this.label_Side_Gate_G4.Name = "label_Side_Gate_G4";
      this.label_Side_Gate_G4.Size = new Size(166, 20);
      this.label_Side_Gate_G4.TabIndex = 267;
      this.label_Side_Gate_G4.Text = "<사이드 게이트>";
      this.label_Side_Gate_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_RunnerDim2_G4.BackColor = Color.Lavender;
      this.label_Pin_RunnerDim2_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_RunnerDim2_G4.Location = new Point(495, 66);
      this.label_Pin_RunnerDim2_G4.Name = "label_Pin_RunnerDim2_G4";
      this.label_Pin_RunnerDim2_G4.Size = new Size(120, 23);
      this.label_Pin_RunnerDim2_G4.TabIndex = 119;
      this.label_Pin_RunnerDim2_G4.Text = "Outer Dimension";
      this.label_Pin_RunnerDim2_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateLength_G4.BackColor = Color.Lavender;
      this.label_Side_GateLength_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateLength_G4.Location = new Point(1, 66);
      this.label_Side_GateLength_G4.Name = "label_Side_GateLength_G4";
      this.label_Side_GateLength_G4.Size = new Size(120, 23);
      this.label_Side_GateLength_G4.TabIndex = 274;
      this.label_Side_GateLength_G4.Text = "게이트 길이";
      this.label_Side_GateLength_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim1_G4.BackColor = Color.Lavender;
      this.label_Side_GateDim1_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim1_G4.Location = new Point(1, 88);
      this.label_Side_GateDim1_G4.Name = "label_Side_GateDim1_G4";
      this.label_Side_GateDim1_G4.Size = new Size(120, 23);
      this.label_Side_GateDim1_G4.TabIndex = 272;
      this.label_Side_GateDim1_G4.Text = "Start Width";
      this.label_Side_GateDim1_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim3_G4.BackColor = Color.Lavender;
      this.label_Side_GateDim3_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim3_G4.Location = new Point(1, 132);
      this.label_Side_GateDim3_G4.Name = "label_Side_GateDim3_G4";
      this.label_Side_GateDim3_G4.Size = new Size(120, 23);
      this.label_Side_GateDim3_G4.TabIndex = 270;
      this.label_Side_GateDim3_G4.Text = "End Width";
      this.label_Side_GateDim3_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim2_G4.BackColor = Color.Lavender;
      this.label_Side_GateDim2_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim2_G4.Location = new Point(1, 110);
      this.label_Side_GateDim2_G4.Name = "label_Side_GateDim2_G4";
      this.label_Side_GateDim2_G4.Size = new Size(120, 23);
      this.label_Side_GateDim2_G4.TabIndex = 268;
      this.label_Side_GateDim2_G4.Text = "Start Height";
      this.label_Side_GateDim2_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim4_G4.BackColor = Color.Lavender;
      this.label_Side_GateDim4_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim4_G4.Location = new Point(1, 154);
      this.label_Side_GateDim4_G4.Name = "label_Side_GateDim4_G4";
      this.label_Side_GateDim4_G4.Size = new Size(120, 23);
      this.label_Side_GateDim4_G4.TabIndex = 276;
      this.label_Side_GateDim4_G4.Text = "End Height";
      this.label_Side_GateDim4_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Pin_GateLength_G4.BackColor = SystemColors.Window;
      this.newTextBox_Pin_GateLength_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_GateLength_G4.Enabled = false;
      this.newTextBox_Pin_GateLength_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_GateLength_G4.IsDigit = true;
      this.newTextBox_Pin_GateLength_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_GateLength_G4.Location = new Point(449, 22);
      this.newTextBox_Pin_GateLength_G4.MultiLine = false;
      this.newTextBox_Pin_GateLength_G4.Name = "newTextBox_Pin_GateLength_G4";
      this.newTextBox_Pin_GateLength_G4.ReadOnly = true;
      this.newTextBox_Pin_GateLength_G4.Size = new Size(47, 23);
      this.newTextBox_Pin_GateLength_G4.TabIndex = 48;
      this.newTextBox_Pin_GateLength_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_GateLength_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_GateLength_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_GateLength_G4.Value = "0";
      this.newTextBox_Pin_GateDim1_G4.BackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim1_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_GateDim1_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_GateDim1_G4.IsDigit = true;
      this.newTextBox_Pin_GateDim1_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_GateDim1_G4.Location = new Point(449, 44);
      this.newTextBox_Pin_GateDim1_G4.MultiLine = false;
      this.newTextBox_Pin_GateDim1_G4.Name = "newTextBox_Pin_GateDim1_G4";
      this.newTextBox_Pin_GateDim1_G4.ReadOnly = false;
      this.newTextBox_Pin_GateDim1_G4.Size = new Size(47, 23);
      this.newTextBox_Pin_GateDim1_G4.TabIndex = 49;
      this.newTextBox_Pin_GateDim1_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_GateDim1_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim1_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_GateDim1_G4.Value = "0";
      this.newTextBox_Pin_GateDim2_G4.BackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim2_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_GateDim2_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_GateDim2_G4.IsDigit = true;
      this.newTextBox_Pin_GateDim2_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_GateDim2_G4.Location = new Point(449, 66);
      this.newTextBox_Pin_GateDim2_G4.MultiLine = false;
      this.newTextBox_Pin_GateDim2_G4.Name = "newTextBox_Pin_GateDim2_G4";
      this.newTextBox_Pin_GateDim2_G4.ReadOnly = false;
      this.newTextBox_Pin_GateDim2_G4.Size = new Size(47, 23);
      this.newTextBox_Pin_GateDim2_G4.TabIndex = 50;
      this.newTextBox_Pin_GateDim2_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_GateDim2_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim2_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_GateDim2_G4.Value = "0";
      this.newTextBox_Pin_RunnerLength_G4.BackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerLength_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_RunnerLength_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_RunnerLength_G4.IsDigit = true;
      this.newTextBox_Pin_RunnerLength_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_RunnerLength_G4.Location = new Point(614, 22);
      this.newTextBox_Pin_RunnerLength_G4.MultiLine = false;
      this.newTextBox_Pin_RunnerLength_G4.Name = "newTextBox_Pin_RunnerLength_G4";
      this.newTextBox_Pin_RunnerLength_G4.ReadOnly = false;
      this.newTextBox_Pin_RunnerLength_G4.Size = new Size(47, 23);
      this.newTextBox_Pin_RunnerLength_G4.TabIndex = 51;
      this.newTextBox_Pin_RunnerLength_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_RunnerLength_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerLength_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_RunnerLength_G4.Value = "0";
      this.newTextBox_Side_GateDim4_G4.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim4_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim4_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim4_G4.IsDigit = true;
      this.newTextBox_Side_GateDim4_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim4_G4.Location = new Point(120, 154);
      this.newTextBox_Side_GateDim4_G4.MultiLine = false;
      this.newTextBox_Side_GateDim4_G4.Name = "newTextBox_Side_GateDim4_G4";
      this.newTextBox_Side_GateDim4_G4.ReadOnly = false;
      this.newTextBox_Side_GateDim4_G4.Size = new Size(47, 23);
      this.newTextBox_Side_GateDim4_G4.TabIndex = 259;
      this.newTextBox_Side_GateDim4_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim4_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim4_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim4_G4.Value = "0";
      this.newTextBox_Side_GateLength_G4.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateLength_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateLength_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateLength_G4.IsDigit = true;
      this.newTextBox_Side_GateLength_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateLength_G4.Location = new Point(120, 66);
      this.newTextBox_Side_GateLength_G4.MultiLine = false;
      this.newTextBox_Side_GateLength_G4.Name = "newTextBox_Side_GateLength_G4";
      this.newTextBox_Side_GateLength_G4.ReadOnly = false;
      this.newTextBox_Side_GateLength_G4.Size = new Size(47, 23);
      this.newTextBox_Side_GateLength_G4.TabIndex = (int) byte.MaxValue;
      this.newTextBox_Side_GateLength_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateLength_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateLength_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateLength_G4.Value = "0";
      this.newTextBox_Pin_RunnerDim1_G4.BackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim1_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_RunnerDim1_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_RunnerDim1_G4.IsDigit = true;
      this.newTextBox_Pin_RunnerDim1_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_RunnerDim1_G4.Location = new Point(614, 44);
      this.newTextBox_Pin_RunnerDim1_G4.MultiLine = false;
      this.newTextBox_Pin_RunnerDim1_G4.Name = "newTextBox_Pin_RunnerDim1_G4";
      this.newTextBox_Pin_RunnerDim1_G4.ReadOnly = false;
      this.newTextBox_Pin_RunnerDim1_G4.Size = new Size(47, 23);
      this.newTextBox_Pin_RunnerDim1_G4.TabIndex = 52;
      this.newTextBox_Pin_RunnerDim1_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_RunnerDim1_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim1_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_RunnerDim1_G4.Value = "0";
      this.newTextBox_Side_GateDim3_G4.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim3_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim3_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim3_G4.IsDigit = true;
      this.newTextBox_Side_GateDim3_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim3_G4.Location = new Point(120, 132);
      this.newTextBox_Side_GateDim3_G4.MultiLine = false;
      this.newTextBox_Side_GateDim3_G4.Name = "newTextBox_Side_GateDim3_G4";
      this.newTextBox_Side_GateDim3_G4.ReadOnly = false;
      this.newTextBox_Side_GateDim3_G4.Size = new Size(47, 23);
      this.newTextBox_Side_GateDim3_G4.TabIndex = 258;
      this.newTextBox_Side_GateDim3_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim3_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim3_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim3_G4.Value = "0";
      this.newTextBox_Side_GateDim1_G4.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim1_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim1_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim1_G4.IsDigit = true;
      this.newTextBox_Side_GateDim1_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim1_G4.Location = new Point(120, 88);
      this.newTextBox_Side_GateDim1_G4.MultiLine = false;
      this.newTextBox_Side_GateDim1_G4.Name = "newTextBox_Side_GateDim1_G4";
      this.newTextBox_Side_GateDim1_G4.ReadOnly = false;
      this.newTextBox_Side_GateDim1_G4.Size = new Size(47, 23);
      this.newTextBox_Side_GateDim1_G4.TabIndex = 256;
      this.newTextBox_Side_GateDim1_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim1_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim1_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim1_G4.Value = "0";
      this.newTextBox_Pin_RunnerDim2_G4.BackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim2_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_RunnerDim2_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_RunnerDim2_G4.IsDigit = true;
      this.newTextBox_Pin_RunnerDim2_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_RunnerDim2_G4.Location = new Point(614, 66);
      this.newTextBox_Pin_RunnerDim2_G4.MultiLine = false;
      this.newTextBox_Pin_RunnerDim2_G4.Name = "newTextBox_Pin_RunnerDim2_G4";
      this.newTextBox_Pin_RunnerDim2_G4.ReadOnly = false;
      this.newTextBox_Pin_RunnerDim2_G4.Size = new Size(47, 23);
      this.newTextBox_Pin_RunnerDim2_G4.TabIndex = 53;
      this.newTextBox_Pin_RunnerDim2_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_RunnerDim2_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim2_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_RunnerDim2_G4.Value = "0";
      this.newTextBox_Side_GateDim2_G4.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim2_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim2_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim2_G4.IsDigit = true;
      this.newTextBox_Side_GateDim2_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim2_G4.Location = new Point(120, 110);
      this.newTextBox_Side_GateDim2_G4.MultiLine = false;
      this.newTextBox_Side_GateDim2_G4.Name = "newTextBox_Side_GateDim2_G4";
      this.newTextBox_Side_GateDim2_G4.ReadOnly = false;
      this.newTextBox_Side_GateDim2_G4.Size = new Size(47, 23);
      this.newTextBox_Side_GateDim2_G4.TabIndex = 257;
      this.newTextBox_Side_GateDim2_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim2_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim2_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim2_G4.Value = "0";
      this.panel_Group.Controls.Add((Control) this.newButton_G3);
      this.panel_Group.Controls.Add((Control) this.newButton_G2);
      this.panel_Group.Controls.Add((Control) this.newButton_G4);
      this.panel_Group.Controls.Add((Control) this.newButton_G1);
      this.panel_Group.Controls.Add((Control) this.tabControl_Group);
      this.panel_Group.Location = new Point(4, 490);
      this.panel_Group.Name = "panel_Group";
      this.panel_Group.Size = new Size(660, 197);
      this.panel_Group.TabIndex = 24;
      this.newButton_G3.ButtonBackColor = Color.White;
      this.newButton_G3.ButtonText = "그룹3";
      this.newButton_G3.FlatBorderSize = 1;
      this.newButton_G3.FlatStyle = FlatStyle.Flat;
      this.newButton_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_G3.Image = (Image) null;
      this.newButton_G3.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_G3.Location = new Point(329, 0);
      this.newButton_G3.Name = "newButton_G3";
      this.newButton_G3.Size = new Size(166, 23);
      this.newButton_G3.TabIndex = 27;
      this.newButton_G3.TabStop = false;
      this.newButton_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_G3.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_G3.NewClick += new EventHandler(this.newButton_Group_NewClick);
      this.newButton_G2.ButtonBackColor = Color.White;
      this.newButton_G2.ButtonText = "그룹2";
      this.newButton_G2.FlatBorderSize = 1;
      this.newButton_G2.FlatStyle = FlatStyle.Flat;
      this.newButton_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_G2.Image = (Image) null;
      this.newButton_G2.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_G2.Location = new Point(165, 0);
      this.newButton_G2.Name = "newButton_G2";
      this.newButton_G2.Size = new Size(166, 23);
      this.newButton_G2.TabIndex = 26;
      this.newButton_G2.TabStop = false;
      this.newButton_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_G2.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_G2.NewClick += new EventHandler(this.newButton_Group_NewClick);
      this.newButton_G4.ButtonBackColor = Color.White;
      this.newButton_G4.ButtonText = "그룹4";
      this.newButton_G4.FlatBorderSize = 1;
      this.newButton_G4.FlatStyle = FlatStyle.Flat;
      this.newButton_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_G4.Image = (Image) null;
      this.newButton_G4.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_G4.Location = new Point(494, 0);
      this.newButton_G4.Name = "newButton_G4";
      this.newButton_G4.Size = new Size(166, 23);
      this.newButton_G4.TabIndex = 28;
      this.newButton_G4.TabStop = false;
      this.newButton_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_G4.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_G4.NewClick += new EventHandler(this.newButton_Group_NewClick);
      this.newButton_G1.ButtonBackColor = Color.LightSteelBlue;
      this.newButton_G1.ButtonText = "그룹1";
      this.newButton_G1.FlatBorderSize = 1;
      this.newButton_G1.FlatStyle = FlatStyle.Flat;
      this.newButton_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_G1.Image = (Image) null;
      this.newButton_G1.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_G1.Location = new Point(0, 0);
      this.newButton_G1.Name = "newButton_G1";
      this.newButton_G1.Size = new Size(166, 23);
      this.newButton_G1.TabIndex = 25;
      this.newButton_G1.TabStop = false;
      this.newButton_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_G1.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_G1.NewClick += new EventHandler(this.newButton_Group_NewClick);
      this.tabControl_Hor_RunnerType.Controls.Add((Control) this.tabPage_Hor_TwoStage);
      this.tabControl_Hor_RunnerType.Controls.Add((Control) this.tabPage_Hor_ThreeStage);
      this.tabControl_Hor_RunnerType.Location = new Point(-9, -6);
      this.tabControl_Hor_RunnerType.Name = "tabControl_Hor_RunnerType";
      this.tabControl_Hor_RunnerType.SelectedIndex = 0;
      this.tabControl_Hor_RunnerType.Size = new Size(347, 193);
      this.tabControl_Hor_RunnerType.TabIndex = 163;
      this.tabControl_Hor_RunnerType.SelectedIndexChanged += new EventHandler(this.tabControl_Hor_RunnerType_SelectedIndexChanged);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.newTextBox_Hor_Two_Length);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.newTextBox_Hor_Two_CenterTol);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.label_Hor_Two_CenterTol);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.panel_Hor_Runner_TwoType);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.label_Hor_Two_Dia);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.newComboBox_Hor_Two_Direction);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.label_Hor_Two_Length);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.label_Hor_Two_Direction);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.newTextBox_Hor_Two_Dia);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.newTextBox_Hor_Two_Angle);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.newTextBox_Hor_Two_Dim2);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.label_Hor_Two_Angle);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.label_Hor_Two_Dim2);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.newTextBox_Hor_Two_Dim1);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.checkBox_Hor_Two_Inter);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.label_Hor_Two_Dim1);
      this.tabPage_Hor_TwoStage.Location = new Point(4, 24);
      this.tabPage_Hor_TwoStage.Name = "tabPage_Hor_TwoStage";
      this.tabPage_Hor_TwoStage.Padding = new Padding(3);
      this.tabPage_Hor_TwoStage.Size = new Size(339, 165);
      this.tabPage_Hor_TwoStage.TabIndex = 0;
      this.tabPage_Hor_TwoStage.Text = "tabPage1";
      this.tabPage_Hor_TwoStage.UseVisualStyleBackColor = true;
      this.newTextBox_Hor_Two_Length.BackColor = SystemColors.Window;
      this.newTextBox_Hor_Two_Length.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Hor_Two_Length.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Hor_Two_Length.IsDigit = true;
      this.newTextBox_Hor_Two_Length.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Hor_Two_Length.Location = new Point(288, 26);
      this.newTextBox_Hor_Two_Length.MultiLine = false;
      this.newTextBox_Hor_Two_Length.Name = "newTextBox_Hor_Two_Length";
      this.newTextBox_Hor_Two_Length.ReadOnly = false;
      this.newTextBox_Hor_Two_Length.Size = new Size(47, 23);
      this.newTextBox_Hor_Two_Length.TabIndex = 60;
      this.newTextBox_Hor_Two_Length.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Hor_Two_Length.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Hor_Two_Length.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Hor_Two_Length.Value = "0";
      this.newTextBox_Hor_Two_CenterTol.BackColor = SystemColors.Window;
      this.newTextBox_Hor_Two_CenterTol.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Hor_Two_CenterTol.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Hor_Two_CenterTol.IsDigit = true;
      this.newTextBox_Hor_Two_CenterTol.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Hor_Two_CenterTol.Location = new Point(288, 70);
      this.newTextBox_Hor_Two_CenterTol.MultiLine = false;
      this.newTextBox_Hor_Two_CenterTol.Name = "newTextBox_Hor_Two_CenterTol";
      this.newTextBox_Hor_Two_CenterTol.ReadOnly = false;
      this.newTextBox_Hor_Two_CenterTol.Size = new Size(47, 23);
      this.newTextBox_Hor_Two_CenterTol.TabIndex = 134;
      this.newTextBox_Hor_Two_CenterTol.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Hor_Two_CenterTol.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Hor_Two_CenterTol.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Hor_Two_CenterTol.Value = "0";
      this.label_Hor_Two_CenterTol.BackColor = Color.Lavender;
      this.label_Hor_Two_CenterTol.BorderStyle = BorderStyle.FixedSingle;
      this.label_Hor_Two_CenterTol.Location = new Point(5, 70);
      this.label_Hor_Two_CenterTol.Name = "label_Hor_Two_CenterTol";
      this.label_Hor_Two_CenterTol.Size = new Size(285, 23);
      this.label_Hor_Two_CenterTol.TabIndex = 135;
      this.label_Hor_Two_CenterTol.Text = "Center 공차";
      this.label_Hor_Two_CenterTol.TextAlign = ContentAlignment.MiddleCenter;
      this.newComboBox_Hor_Two_Direction.BackColor = Color.White;
      this.newComboBox_Hor_Two_Direction.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Hor_Two_Direction.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_Hor_Two_Direction.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Hor_Two_Direction.isSameSelect = false;
      this.newComboBox_Hor_Two_Direction.Location = new Point(288, 48);
      this.newComboBox_Hor_Two_Direction.Name = "newComboBox_Hor_Two_Direction";
      this.newComboBox_Hor_Two_Direction.SelectedIndex = -1;
      this.newComboBox_Hor_Two_Direction.Size = new Size(47, 23);
      this.newComboBox_Hor_Two_Direction.TabIndex = 61;
      this.newComboBox_Hor_Two_Direction.TabStop = false;
      this.newComboBox_Hor_Two_Direction.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Hor_Two_Direction.Value = "";
      this.newTextBox_Hor_Two_Dia.BackColor = SystemColors.Window;
      this.newTextBox_Hor_Two_Dia.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Hor_Two_Dia.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Hor_Two_Dia.IsDigit = true;
      this.newTextBox_Hor_Two_Dia.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Hor_Two_Dia.Location = new Point(124, 26);
      this.newTextBox_Hor_Two_Dia.MultiLine = false;
      this.newTextBox_Hor_Two_Dia.Name = "newTextBox_Hor_Two_Dia";
      this.newTextBox_Hor_Two_Dia.ReadOnly = false;
      this.newTextBox_Hor_Two_Dia.Size = new Size(47, 23);
      this.newTextBox_Hor_Two_Dia.TabIndex = 59;
      this.newTextBox_Hor_Two_Dia.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Hor_Two_Dia.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Hor_Two_Dia.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Hor_Two_Dia.Value = "0";
      this.newTextBox_Hor_Two_Dim2.BackColor = SystemColors.Window;
      this.newTextBox_Hor_Two_Dim2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Hor_Two_Dim2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Hor_Two_Dim2.IsDigit = true;
      this.newTextBox_Hor_Two_Dim2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Hor_Two_Dim2.Location = new Point(288, 114);
      this.newTextBox_Hor_Two_Dim2.MultiLine = false;
      this.newTextBox_Hor_Two_Dim2.Name = "newTextBox_Hor_Two_Dim2";
      this.newTextBox_Hor_Two_Dim2.ReadOnly = false;
      this.newTextBox_Hor_Two_Dim2.Size = new Size(47, 23);
      this.newTextBox_Hor_Two_Dim2.TabIndex = 63;
      this.newTextBox_Hor_Two_Dim2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Hor_Two_Dim2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Hor_Two_Dim2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Hor_Two_Dim2.Value = "0";
      this.newTextBox_Hor_Two_Dim1.BackColor = SystemColors.Window;
      this.newTextBox_Hor_Two_Dim1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Hor_Two_Dim1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Hor_Two_Dim1.IsDigit = true;
      this.newTextBox_Hor_Two_Dim1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Hor_Two_Dim1.Location = new Point(288, 92);
      this.newTextBox_Hor_Two_Dim1.MultiLine = false;
      this.newTextBox_Hor_Two_Dim1.Name = "newTextBox_Hor_Two_Dim1";
      this.newTextBox_Hor_Two_Dim1.ReadOnly = false;
      this.newTextBox_Hor_Two_Dim1.Size = new Size(47, 23);
      this.newTextBox_Hor_Two_Dim1.TabIndex = 62;
      this.newTextBox_Hor_Two_Dim1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Hor_Two_Dim1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Hor_Two_Dim1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Hor_Two_Dim1.Value = "0";
      this.tabPage_Hor_ThreeStage.Controls.Add((Control) this.newTextBox_Hor_Three_Length);
      this.tabPage_Hor_ThreeStage.Controls.Add((Control) this.label_Hor_Three_Length);
      this.tabPage_Hor_ThreeStage.Controls.Add((Control) this.newTextBox_Hor_Three_Dia);
      this.tabPage_Hor_ThreeStage.Controls.Add((Control) this.panel_Quadrant);
      this.tabPage_Hor_ThreeStage.Controls.Add((Control) this.newTextBox_Hor_Three_CenterTol);
      this.tabPage_Hor_ThreeStage.Controls.Add((Control) this.label_Hor_Three_CenterTol);
      this.tabPage_Hor_ThreeStage.Controls.Add((Control) this.label_Hor_Three_Dia);
      this.tabPage_Hor_ThreeStage.Controls.Add((Control) this.panel_Hor_Runner_ThreeType);
      this.tabPage_Hor_ThreeStage.Location = new Point(4, 22);
      this.tabPage_Hor_ThreeStage.Name = "tabPage_Hor_ThreeStage";
      this.tabPage_Hor_ThreeStage.Padding = new Padding(3);
      this.tabPage_Hor_ThreeStage.Size = new Size(339, 167);
      this.tabPage_Hor_ThreeStage.TabIndex = 1;
      this.tabPage_Hor_ThreeStage.Text = "tabPage2";
      this.tabPage_Hor_ThreeStage.UseVisualStyleBackColor = true;
      this.newTextBox_Hor_Three_Length.BackColor = SystemColors.Window;
      this.newTextBox_Hor_Three_Length.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Hor_Three_Length.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Hor_Three_Length.IsDigit = true;
      this.newTextBox_Hor_Three_Length.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Hor_Three_Length.Location = new Point(288, 48);
      this.newTextBox_Hor_Three_Length.MultiLine = false;
      this.newTextBox_Hor_Three_Length.Name = "newTextBox_Hor_Three_Length";
      this.newTextBox_Hor_Three_Length.ReadOnly = false;
      this.newTextBox_Hor_Three_Length.Size = new Size(47, 23);
      this.newTextBox_Hor_Three_Length.TabIndex = 139;
      this.newTextBox_Hor_Three_Length.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Hor_Three_Length.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Hor_Three_Length.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Hor_Three_Length.Value = "0";
      this.label_Hor_Three_Length.BackColor = Color.Lavender;
      this.label_Hor_Three_Length.BorderStyle = BorderStyle.FixedSingle;
      this.label_Hor_Three_Length.Location = new Point(5, 48);
      this.label_Hor_Three_Length.Name = "label_Hor_Three_Length";
      this.label_Hor_Three_Length.Size = new Size(285, 23);
      this.label_Hor_Three_Length.TabIndex = 138;
      this.label_Hor_Three_Length.Text = "3단 런너 길이";
      this.label_Hor_Three_Length.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Hor_Three_Dia.BackColor = SystemColors.Window;
      this.newTextBox_Hor_Three_Dia.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Hor_Three_Dia.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Hor_Three_Dia.IsDigit = true;
      this.newTextBox_Hor_Three_Dia.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Hor_Three_Dia.Location = new Point(288, 70);
      this.newTextBox_Hor_Three_Dia.MultiLine = false;
      this.newTextBox_Hor_Three_Dia.Name = "newTextBox_Hor_Three_Dia";
      this.newTextBox_Hor_Three_Dia.ReadOnly = false;
      this.newTextBox_Hor_Three_Dia.Size = new Size(47, 23);
      this.newTextBox_Hor_Three_Dia.TabIndex = 61;
      this.newTextBox_Hor_Three_Dia.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Hor_Three_Dia.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Hor_Three_Dia.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Hor_Three_Dia.Value = "0";
      this.panel_Quadrant.BackColor = Color.White;
      this.panel_Quadrant.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Quadrant.Controls.Add((Control) this.radioButton_Quadrant4);
      this.panel_Quadrant.Controls.Add((Control) this.radioButton_Quadrant2);
      this.panel_Quadrant.Location = new Point(5, 4);
      this.panel_Quadrant.Name = "panel_Quadrant";
      this.panel_Quadrant.Size = new Size(331, 23);
      this.panel_Quadrant.TabIndex = 58;
      this.radioButton_Quadrant4.BackColor = Color.White;
      this.radioButton_Quadrant4.Location = new Point(191, 2);
      this.radioButton_Quadrant4.Name = "radioButton_Quadrant4";
      this.radioButton_Quadrant4.Size = new Size(85, 19);
      this.radioButton_Quadrant4.TabIndex = 57;
      this.radioButton_Quadrant4.Text = "4분면";
      this.radioButton_Quadrant4.UseVisualStyleBackColor = false;
      this.radioButton_Quadrant4.CheckedChanged += new EventHandler(this.radioButton_Quadrant4_CheckedChanged);
      this.radioButton_Quadrant2.BackColor = Color.White;
      this.radioButton_Quadrant2.Location = new Point(70, 2);
      this.radioButton_Quadrant2.Name = "radioButton_Quadrant2";
      this.radioButton_Quadrant2.Size = new Size(85, 19);
      this.radioButton_Quadrant2.TabIndex = 56;
      this.radioButton_Quadrant2.Text = "2분면";
      this.radioButton_Quadrant2.UseVisualStyleBackColor = false;
      this.radioButton_Quadrant2.CheckedChanged += new EventHandler(this.radioButton_Quadrant2_CheckedChanged);
      this.newTextBox_Hor_Three_CenterTol.BackColor = SystemColors.Window;
      this.newTextBox_Hor_Three_CenterTol.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Hor_Three_CenterTol.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Hor_Three_CenterTol.IsDigit = true;
      this.newTextBox_Hor_Three_CenterTol.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Hor_Three_CenterTol.Location = new Point(288, 92);
      this.newTextBox_Hor_Three_CenterTol.MultiLine = false;
      this.newTextBox_Hor_Three_CenterTol.Name = "newTextBox_Hor_Three_CenterTol";
      this.newTextBox_Hor_Three_CenterTol.ReadOnly = false;
      this.newTextBox_Hor_Three_CenterTol.Size = new Size(47, 23);
      this.newTextBox_Hor_Three_CenterTol.TabIndex = 136;
      this.newTextBox_Hor_Three_CenterTol.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Hor_Three_CenterTol.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Hor_Three_CenterTol.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Hor_Three_CenterTol.Value = "0";
      this.label_Hor_Three_CenterTol.BackColor = Color.Lavender;
      this.label_Hor_Three_CenterTol.BorderStyle = BorderStyle.FixedSingle;
      this.label_Hor_Three_CenterTol.Location = new Point(5, 92);
      this.label_Hor_Three_CenterTol.Name = "label_Hor_Three_CenterTol";
      this.label_Hor_Three_CenterTol.Size = new Size(285, 23);
      this.label_Hor_Three_CenterTol.TabIndex = 137;
      this.label_Hor_Three_CenterTol.Text = "Center 공차";
      this.label_Hor_Three_CenterTol.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Hor_Three_Dia.BackColor = Color.Lavender;
      this.label_Hor_Three_Dia.BorderStyle = BorderStyle.FixedSingle;
      this.label_Hor_Three_Dia.Location = new Point(5, 70);
      this.label_Hor_Three_Dia.Name = "label_Hor_Three_Dia";
      this.label_Hor_Three_Dia.Size = new Size(285, 23);
      this.label_Hor_Three_Dia.TabIndex = 60;
      this.label_Hor_Three_Dia.Text = "수평 런너 직경";
      this.label_Hor_Three_Dia.TextAlign = ContentAlignment.MiddleCenter;
      this.panel_Hor_Runner_ThreeType.BackColor = Color.White;
      this.panel_Hor_Runner_ThreeType.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Hor_Runner_ThreeType.Controls.Add((Control) this.radioButton_Hor_Three_Type2);
      this.panel_Hor_Runner_ThreeType.Controls.Add((Control) this.radioButton_Hor_Three_Type1);
      this.panel_Hor_Runner_ThreeType.Location = new Point(5, 26);
      this.panel_Hor_Runner_ThreeType.Name = "panel_Hor_Runner_ThreeType";
      this.panel_Hor_Runner_ThreeType.Size = new Size(331, 23);
      this.panel_Hor_Runner_ThreeType.TabIndex = 55;
      this.radioButton_Hor_Three_Type2.BackColor = Color.White;
      this.radioButton_Hor_Three_Type2.Location = new Point(191, 2);
      this.radioButton_Hor_Three_Type2.Name = "radioButton_Hor_Three_Type2";
      this.radioButton_Hor_Three_Type2.Size = new Size(85, 19);
      this.radioButton_Hor_Three_Type2.TabIndex = 57;
      this.radioButton_Hor_Three_Type2.Text = "☆ - + 타입";
      this.radioButton_Hor_Three_Type2.UseVisualStyleBackColor = false;
      this.radioButton_Hor_Three_Type2.CheckedChanged += new EventHandler(this.radioButton_Hor_Three_Type2_CheckedChanged);
      this.radioButton_Hor_Three_Type1.BackColor = Color.White;
      this.radioButton_Hor_Three_Type1.Location = new Point(70, 2);
      this.radioButton_Hor_Three_Type1.Name = "radioButton_Hor_Three_Type1";
      this.radioButton_Hor_Three_Type1.Size = new Size(68, 19);
      this.radioButton_Hor_Three_Type1.TabIndex = 56;
      this.radioButton_Hor_Three_Type1.Text = "H 타입";
      this.radioButton_Hor_Three_Type1.UseVisualStyleBackColor = false;
      this.radioButton_Hor_Three_Type1.CheckedChanged += new EventHandler(this.radioButton_Hor_Three_Type1_CheckedChanged);
      this.panel_Hor_RunnerType.Controls.Add((Control) this.newButton_Hor_Runner_ThreeStage);
      this.panel_Hor_RunnerType.Controls.Add((Control) this.newButton_Hor_Runner_TwoStage);
      this.panel_Hor_RunnerType.Controls.Add((Control) this.tabControl_Hor_RunnerType);
      this.panel_Hor_RunnerType.Location = new Point(4, 709);
      this.panel_Hor_RunnerType.Name = "panel_Hor_RunnerType";
      this.panel_Hor_RunnerType.Size = new Size(331, 178);
      this.panel_Hor_RunnerType.TabIndex = 164;
      this.newButton_Hor_Runner_ThreeStage.ButtonBackColor = Color.White;
      this.newButton_Hor_Runner_ThreeStage.ButtonText = "3단 매니폴드";
      this.newButton_Hor_Runner_ThreeStage.FlatBorderSize = 1;
      this.newButton_Hor_Runner_ThreeStage.FlatStyle = FlatStyle.Flat;
      this.newButton_Hor_Runner_ThreeStage.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Hor_Runner_ThreeStage.Image = (Image) null;
      this.newButton_Hor_Runner_ThreeStage.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Hor_Runner_ThreeStage.Location = new Point(165, 0);
      this.newButton_Hor_Runner_ThreeStage.Name = "newButton_Hor_Runner_ThreeStage";
      this.newButton_Hor_Runner_ThreeStage.Size = new Size(166, 23);
      this.newButton_Hor_Runner_ThreeStage.TabIndex = 165;
      this.newButton_Hor_Runner_ThreeStage.TabStop = false;
      this.newButton_Hor_Runner_ThreeStage.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Hor_Runner_ThreeStage.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Hor_Runner_ThreeStage.NewClick += new EventHandler(this.newButton_Hor_RunnerType_NewClick);
      this.newButton_Hor_Runner_TwoStage.ButtonBackColor = Color.LightSteelBlue;
      this.newButton_Hor_Runner_TwoStage.ButtonText = "2단 매니폴드";
      this.newButton_Hor_Runner_TwoStage.FlatBorderSize = 1;
      this.newButton_Hor_Runner_TwoStage.FlatStyle = FlatStyle.Flat;
      this.newButton_Hor_Runner_TwoStage.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Hor_Runner_TwoStage.Image = (Image) null;
      this.newButton_Hor_Runner_TwoStage.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Hor_Runner_TwoStage.Location = new Point(0, 0);
      this.newButton_Hor_Runner_TwoStage.Name = "newButton_Hor_Runner_TwoStage";
      this.newButton_Hor_Runner_TwoStage.Size = new Size(166, 23);
      this.newButton_Hor_Runner_TwoStage.TabIndex = 164;
      this.newButton_Hor_Runner_TwoStage.TabStop = false;
      this.newButton_Hor_Runner_TwoStage.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Hor_Runner_TwoStage.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Hor_Runner_TwoStage.NewClick += new EventHandler(this.newButton_Hor_RunnerType_NewClick);
      this.newButton_Node_Pin_Select.ButtonBackColor = Color.White;
      this.newButton_Node_Pin_Select.ButtonText = "핀 노드 선택";
      this.newButton_Node_Pin_Select.FlatBorderSize = 1;
      this.newButton_Node_Pin_Select.FlatStyle = FlatStyle.Flat;
      this.newButton_Node_Pin_Select.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Node_Pin_Select.ForeColor = Color.Navy;
      this.newButton_Node_Pin_Select.Image = (Image) null;
      this.newButton_Node_Pin_Select.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Node_Pin_Select.Location = new Point(196, 241);
      this.newButton_Node_Pin_Select.Name = "newButton_Node_Pin_Select";
      this.newButton_Node_Pin_Select.Size = new Size(139, 23);
      this.newButton_Node_Pin_Select.TabIndex = 72;
      this.newButton_Node_Pin_Select.TabStop = false;
      this.newButton_Node_Pin_Select.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Node_Pin_Select.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Node_Pin_Select.NewClick += new EventHandler(this.newButton_Runner_NewClick);
      this.newButton_LoadPrevNode.ButtonBackColor = Color.White;
      this.newButton_LoadPrevNode.ButtonText = "이전 좌표 불러오기";
      this.newButton_LoadPrevNode.FlatBorderSize = 1;
      this.newButton_LoadPrevNode.FlatStyle = FlatStyle.Flat;
      this.newButton_LoadPrevNode.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_LoadPrevNode.ForeColor = Color.Navy;
      this.newButton_LoadPrevNode.Image = (Image) Resources.Import;
      this.newButton_LoadPrevNode.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_LoadPrevNode.Location = new Point(334, 173);
      this.newButton_LoadPrevNode.Name = "newButton_LoadPrevNode";
      this.newButton_LoadPrevNode.Size = new Size(330, 28);
      this.newButton_LoadPrevNode.TabIndex = 5;
      this.newButton_LoadPrevNode.TabStop = false;
      this.newButton_LoadPrevNode.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_LoadPrevNode.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_LoadPrevNode.NewClick += new EventHandler(this.newButton_Runner_NewClick);
      this.newButton_Node_Del.ButtonBackColor = Color.White;
      this.newButton_Node_Del.ButtonText = "";
      this.newButton_Node_Del.FlatBorderSize = 0;
      this.newButton_Node_Del.FlatStyle = FlatStyle.Flat;
      this.newButton_Node_Del.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Node_Del.Image = (Image) Resources.Del;
      this.newButton_Node_Del.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Node_Del.Location = new Point(504, 244);
      this.newButton_Node_Del.Name = "newButton_Node_Del";
      this.newButton_Node_Del.Size = new Size(19, 19);
      this.newButton_Node_Del.TabIndex = 74;
      this.newButton_Node_Del.TabStop = false;
      this.newButton_Node_Del.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Node_Del.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Node_Del.NewClick += new EventHandler(this.newButton_Runner_NewClick);
      this.newButton_Node_Add.ButtonBackColor = Color.White;
      this.newButton_Node_Add.ButtonText = "";
      this.newButton_Node_Add.FlatBorderSize = 0;
      this.newButton_Node_Add.FlatStyle = FlatStyle.Flat;
      this.newButton_Node_Add.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Node_Add.Image = (Image) Resources.Add;
      this.newButton_Node_Add.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Node_Add.Location = new Point(476, 243);
      this.newButton_Node_Add.Name = "newButton_Node_Add";
      this.newButton_Node_Add.Size = new Size(19, 19);
      this.newButton_Node_Add.TabIndex = 73;
      this.newButton_Node_Add.TabStop = false;
      this.newButton_Node_Add.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Node_Add.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Node_Add.NewClick += new EventHandler(this.newButton_Runner_NewClick);
      this.newTextBox_Cavity_Occ.BackColor = SystemColors.Window;
      this.newTextBox_Cavity_Occ.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Cavity_Occ.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Cavity_Occ.IsDigit = true;
      this.newTextBox_Cavity_Occ.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Cavity_Occ.Location = new Point(617, 709);
      this.newTextBox_Cavity_Occ.MultiLine = false;
      this.newTextBox_Cavity_Occ.Name = "newTextBox_Cavity_Occ";
      this.newTextBox_Cavity_Occ.ReadOnly = false;
      this.newTextBox_Cavity_Occ.Size = new Size(47, 23);
      this.newTextBox_Cavity_Occ.TabIndex = 71;
      this.newTextBox_Cavity_Occ.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Cavity_Occ.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Cavity_Occ.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Cavity_Occ.Value = "0";
      this.newTextBox_Sprue_Dim2.BackColor = SystemColors.Window;
      this.newTextBox_Sprue_Dim2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Sprue_Dim2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Sprue_Dim2.IsDigit = true;
      this.newTextBox_Sprue_Dim2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Sprue_Dim2.Location = new Point(452, 775);
      this.newTextBox_Sprue_Dim2.MultiLine = false;
      this.newTextBox_Sprue_Dim2.Name = "newTextBox_Sprue_Dim2";
      this.newTextBox_Sprue_Dim2.ReadOnly = false;
      this.newTextBox_Sprue_Dim2.Size = new Size(47, 23);
      this.newTextBox_Sprue_Dim2.TabIndex = 70;
      this.newTextBox_Sprue_Dim2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Sprue_Dim2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Sprue_Dim2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Sprue_Dim2.Value = "0";
      this.newTextBox_Sprue_Dim1.BackColor = SystemColors.Window;
      this.newTextBox_Sprue_Dim1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Sprue_Dim1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Sprue_Dim1.IsDigit = true;
      this.newTextBox_Sprue_Dim1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Sprue_Dim1.Location = new Point(452, 753);
      this.newTextBox_Sprue_Dim1.MultiLine = false;
      this.newTextBox_Sprue_Dim1.Name = "newTextBox_Sprue_Dim1";
      this.newTextBox_Sprue_Dim1.ReadOnly = false;
      this.newTextBox_Sprue_Dim1.Size = new Size(47, 23);
      this.newTextBox_Sprue_Dim1.TabIndex = 69;
      this.newTextBox_Sprue_Dim1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Sprue_Dim1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Sprue_Dim1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Sprue_Dim1.Value = "0";
      this.newTextBox_Sprue_Length.BackColor = SystemColors.Window;
      this.newTextBox_Sprue_Length.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Sprue_Length.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Sprue_Length.IsDigit = true;
      this.newTextBox_Sprue_Length.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Sprue_Length.Location = new Point(452, 731);
      this.newTextBox_Sprue_Length.MultiLine = false;
      this.newTextBox_Sprue_Length.Name = "newTextBox_Sprue_Length";
      this.newTextBox_Sprue_Length.ReadOnly = false;
      this.newTextBox_Sprue_Length.Size = new Size(47, 23);
      this.newTextBox_Sprue_Length.TabIndex = 68;
      this.newTextBox_Sprue_Length.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Sprue_Length.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Sprue_Length.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Sprue_Length.Value = "0";
      this.newButton_Edit.ButtonBackColor = Color.White;
      this.newButton_Edit.ButtonText = "수정";
      this.newButton_Edit.FlatBorderSize = 1;
      this.newButton_Edit.FlatStyle = FlatStyle.Flat;
      this.newButton_Edit.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Edit.ForeColor = Color.Navy;
      this.newButton_Edit.Image = (Image) Resources.Edit;
      this.newButton_Edit.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Edit.Location = new Point(4, 139);
      this.newButton_Edit.Name = "newButton_Edit";
      this.newButton_Edit.Size = new Size(660, 28);
      this.newButton_Edit.TabIndex = 4;
      this.newButton_Edit.TabStop = false;
      this.newButton_Edit.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Edit.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Edit.NewClick += new EventHandler(this.newButton_Runner_NewClick);
      this.newButton_Apply.ButtonBackColor = Color.White;
      this.newButton_Apply.ButtonText = "런너 시스템 생성";
      this.newButton_Apply.FlatBorderSize = 1;
      this.newButton_Apply.FlatStyle = FlatStyle.Flat;
      this.newButton_Apply.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Apply.ForeColor = Color.Navy;
      this.newButton_Apply.Image = (Image) Resources.OK;
      this.newButton_Apply.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Apply.Location = new Point(4, 889);
      this.newButton_Apply.Name = "newButton_Apply";
      this.newButton_Apply.Size = new Size(660, 28);
      this.newButton_Apply.TabIndex = 162;
      this.newButton_Apply.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Apply.NewClick += new EventHandler(this.newButton_Runner_NewClick);
      this.newComboBox_Item.BackColor = Color.LavenderBlush;
      this.newComboBox_Item.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Item.ComboBoxBackColor = Color.LavenderBlush;
      this.newComboBox_Item.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Item.isSameSelect = false;
      this.newComboBox_Item.Location = new Point(418, 24);
      this.newComboBox_Item.Name = "newComboBox_Item";
      this.newComboBox_Item.SelectedIndex = -1;
      this.newComboBox_Item.Size = new Size(246, 23);
      this.newComboBox_Item.TabIndex = 2;
      this.newComboBox_Item.TabStop = false;
      this.newComboBox_Item.TextAlign = HorizontalAlignment.Center;
      this.newComboBox_Item.Value = "";
      this.newComboBox_Item.SelectedIndexChanged += new EventHandler(this.newComboBox_Item_SelectedIndexChanged);
      this.newComboBox_Company.BackColor = Color.LavenderBlush;
      this.newComboBox_Company.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Company.ComboBoxBackColor = Color.LavenderBlush;
      this.newComboBox_Company.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Company.isSameSelect = false;
      this.newComboBox_Company.Location = new Point(89, 24);
      this.newComboBox_Company.Name = "newComboBox_Company";
      this.newComboBox_Company.SelectedIndex = -1;
      this.newComboBox_Company.Size = new Size(246, 23);
      this.newComboBox_Company.TabIndex = 1;
      this.newComboBox_Company.TabStop = false;
      this.newComboBox_Company.TextAlign = HorizontalAlignment.Center;
      this.newComboBox_Company.Value = "";
      this.newComboBox_Company.SelectedIndexChanged += new EventHandler(this.newComboBox_Company_SelectedIndexChanged);
      this.newTextBox_CenterX.BackColor = SystemColors.Window;
      this.newTextBox_CenterX.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_CenterX.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_CenterX.IsDigit = true;
      this.newTextBox_CenterX.Lines = new string[1]{ "0" };
      this.newTextBox_CenterX.Location = new Point(446, 219);
      this.newTextBox_CenterX.MultiLine = false;
      this.newTextBox_CenterX.Name = "newTextBox_CenterX";
      this.newTextBox_CenterX.ReadOnly = false;
      this.newTextBox_CenterX.Size = new Size(53, 23);
      this.newTextBox_CenterX.TabIndex = 6;
      this.newTextBox_CenterX.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_CenterX.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_CenterX.TextForeColor = SystemColors.WindowText;
      this.newTextBox_CenterX.Value = "0";
      this.newTextBox_CenterY.BackColor = SystemColors.Window;
      this.newTextBox_CenterY.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_CenterY.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_CenterY.IsDigit = true;
      this.newTextBox_CenterY.Lines = new string[1]{ "0" };
      this.newTextBox_CenterY.Location = new Point(611, 219);
      this.newTextBox_CenterY.MultiLine = false;
      this.newTextBox_CenterY.Name = "newTextBox_CenterY";
      this.newTextBox_CenterY.ReadOnly = false;
      this.newTextBox_CenterY.Size = new Size(53, 23);
      this.newTextBox_CenterY.TabIndex = 7;
      this.newTextBox_CenterY.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_CenterY.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_CenterY.TextForeColor = SystemColors.WindowText;
      this.newTextBox_CenterY.Value = "0";
      this.newTextBox_Node.BackColor = Color.LavenderBlush;
      this.newTextBox_Node.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Node.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Node.IsDigit = true;
      this.newTextBox_Node.Lines = new string[1]{ "0" };
      this.newTextBox_Node.Location = new Point(560, 241);
      this.newTextBox_Node.MultiLine = false;
      this.newTextBox_Node.Name = "newTextBox_Node";
      this.newTextBox_Node.ReadOnly = true;
      this.newTextBox_Node.Size = new Size(104, 23);
      this.newTextBox_Node.TabIndex = 93;
      this.newTextBox_Node.TabStop = false;
      this.newTextBox_Node.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Node.TextBoxBackColor = Color.LavenderBlush;
      this.newTextBox_Node.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Node.Value = "0";
      this.newButton_Node_Side_Select.ButtonBackColor = Color.White;
      this.newButton_Node_Side_Select.ButtonText = "사이드 노드 선택";
      this.newButton_Node_Side_Select.FlatBorderSize = 1;
      this.newButton_Node_Side_Select.FlatStyle = FlatStyle.Flat;
      this.newButton_Node_Side_Select.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Node_Side_Select.ForeColor = Color.Navy;
      this.newButton_Node_Side_Select.Image = (Image) null;
      this.newButton_Node_Side_Select.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Node_Side_Select.Location = new Point(334, 241);
      this.newButton_Node_Side_Select.Name = "newButton_Node_Side_Select";
      this.newButton_Node_Side_Select.Size = new Size(139, 23);
      this.newButton_Node_Side_Select.TabIndex = 165;
      this.newButton_Node_Side_Select.TabStop = false;
      this.newButton_Node_Side_Select.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Node_Side_Select.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Node_Side_Select.NewClick += new EventHandler(this.newButton_Runner_NewClick);
      this.newButton_Node_Update.ButtonBackColor = Color.White;
      this.newButton_Node_Update.ButtonText = "";
      this.newButton_Node_Update.FlatBorderSize = 0;
      this.newButton_Node_Update.FlatStyle = FlatStyle.Flat;
      this.newButton_Node_Update.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Node_Update.Image = (Image) Resources.Update;
      this.newButton_Node_Update.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Node_Update.Location = new Point(535, 244);
      this.newButton_Node_Update.Name = "newButton_Node_Update";
      this.newButton_Node_Update.Size = new Size(19, 19);
      this.newButton_Node_Update.TabIndex = 166;
      this.newButton_Node_Update.TabStop = false;
      this.newButton_Node_Update.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Node_Update.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Node_Update.NewClick += new EventHandler(this.newButton_Runner_NewClick);
      this.panel1.BackColor = Color.White;
      this.panel1.BorderStyle = BorderStyle.FixedSingle;
      this.panel1.Controls.Add((Control) this.radioButton_Node4);
      this.panel1.Controls.Add((Control) this.radioButton_Node3);
      this.panel1.Controls.Add((Control) this.radioButton_Node2);
      this.panel1.Controls.Add((Control) this.radioButton_Node1);
      this.panel1.Location = new Point(4, 173);
      this.panel1.Name = "panel1";
      this.panel1.Size = new Size(331, 28);
      this.panel1.TabIndex = 167;
      this.radioButton_Node4.Location = new Point(253, 4);
      this.radioButton_Node4.Name = "radioButton_Node4";
      this.radioButton_Node4.Size = new Size(65, 19);
      this.radioButton_Node4.TabIndex = 70;
      this.radioButton_Node4.Text = "Node4";
      this.radioButton_Node4.UseVisualStyleBackColor = true;
      this.radioButton_Node3.Location = new Point(175, 4);
      this.radioButton_Node3.Name = "radioButton_Node3";
      this.radioButton_Node3.Size = new Size(65, 19);
      this.radioButton_Node3.TabIndex = 69;
      this.radioButton_Node3.Text = "Node3";
      this.radioButton_Node3.UseVisualStyleBackColor = true;
      this.radioButton_Node2.Location = new Point(97, 4);
      this.radioButton_Node2.Name = "radioButton_Node2";
      this.radioButton_Node2.Size = new Size(65, 19);
      this.radioButton_Node2.TabIndex = 68;
      this.radioButton_Node2.Text = "Node2";
      this.radioButton_Node2.UseVisualStyleBackColor = true;
      this.radioButton_Node1.Location = new Point(19, 4);
      this.radioButton_Node1.Name = "radioButton_Node1";
      this.radioButton_Node1.Size = new Size(65, 19);
      this.radioButton_Node1.TabIndex = 66;
      this.radioButton_Node1.Text = "Node1";
      this.radioButton_Node1.UseVisualStyleBackColor = true;
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(669, 921);
      this.Controls.Add((Control) this.panel1);
      this.Controls.Add((Control) this.newButton_Node_Update);
      this.Controls.Add((Control) this.label_Sprue_Dim2);
      this.Controls.Add((Control) this.label_Sprue_Dim1);
      this.Controls.Add((Control) this.label_Sprue_Length);
      this.Controls.Add((Control) this.panel_Sprue_Type);
      this.Controls.Add((Control) this.label_Sprue);
      this.Controls.Add((Control) this.newButton_Node_Side_Select);
      this.Controls.Add((Control) this.panel_Hor_RunnerType);
      this.Controls.Add((Control) this.label_Hor_Runner);
      this.Controls.Add((Control) this.newButton_Node_Pin_Select);
      this.Controls.Add((Control) this.panel_Pin_Type);
      this.Controls.Add((Control) this.label_Pin);
      this.Controls.Add((Control) this.newButton_LoadPrevNode);
      this.Controls.Add((Control) this.panel_Valve);
      this.Controls.Add((Control) this.panel_Group);
      this.Controls.Add((Control) this.label_CenterPoint);
      this.Controls.Add((Control) this.newButton_Node_Del);
      this.Controls.Add((Control) this.newButton_Node_Add);
      this.Controls.Add((Control) this.newTextBox_Cavity_Occ);
      this.Controls.Add((Control) this.newTextBox_Sprue_Dim2);
      this.Controls.Add((Control) this.newTextBox_Sprue_Dim1);
      this.Controls.Add((Control) this.newTextBox_Sprue_Length);
      this.Controls.Add((Control) this.panel_Node);
      this.Controls.Add((Control) this.newButton_Edit);
      this.Controls.Add((Control) this.newButton_Apply);
      this.Controls.Add((Control) this.newComboBox_Item);
      this.Controls.Add((Control) this.newComboBox_Company);
      this.Controls.Add((Control) this.label_Cavity_Occ);
      this.Controls.Add((Control) this.label_DB_Item);
      this.Controls.Add((Control) this.label_DB_Company);
      this.Controls.Add((Control) this.listBox_DB);
      this.Controls.Add((Control) this.label_Cavity);
      this.Controls.Add((Control) this.label_DB_List);
      this.Controls.Add((Control) this.label_CenterX);
      this.Controls.Add((Control) this.newTextBox_CenterX);
      this.Controls.Add((Control) this.newTextBox_CenterY);
      this.Controls.Add((Control) this.label_CenterY);
      this.Controls.Add((Control) this.newTextBox_Node);
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmRunner);
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = nameof (frmRunner);
      this.Load += new EventHandler(this.frmRunner_Load);
      this.panel_Hor_Runner_TwoType.ResumeLayout(false);
      this.panel_Sprue_Type.ResumeLayout(false);
      this.panel_Pin_Type.ResumeLayout(false);
      this.panel_Node.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_Node).EndInit();
      this.panel_Valve.ResumeLayout(false);
      this.panel_Valve_Sub.ResumeLayout(false);
      this.tabControl_Group.ResumeLayout(false);
      this.tabPage_G1.ResumeLayout(false);
      this.panel_Side_GateType1_G1.ResumeLayout(false);
      this.panel_Side_GateType1_G1.PerformLayout();
      this.panel_Side_GateType2_G1.ResumeLayout(false);
      this.panel_Side_GateType2_G1.PerformLayout();
      this.panel_Side_RunnerType_G1.ResumeLayout(false);
      this.panel_Side_RunnerType_G1.PerformLayout();
      this.tabPage_G2.ResumeLayout(false);
      this.panel_Side_GateType1_G2.ResumeLayout(false);
      this.panel_Side_GateType1_G2.PerformLayout();
      this.panel_Side_GateType2_G2.ResumeLayout(false);
      this.panel_Side_GateType2_G2.PerformLayout();
      this.panel_Side_RunnerType_G2.ResumeLayout(false);
      this.panel_Side_RunnerType_G2.PerformLayout();
      this.tabPage_G3.ResumeLayout(false);
      this.panel_Side_GateType1_G3.ResumeLayout(false);
      this.panel_Side_GateType1_G3.PerformLayout();
      this.panel_Side_GateType2_G3.ResumeLayout(false);
      this.panel_Side_GateType2_G3.PerformLayout();
      this.panel_Side_RunnerType_G3.ResumeLayout(false);
      this.panel_Side_RunnerType_G3.PerformLayout();
      this.tabPage_G4.ResumeLayout(false);
      this.panel_Side_RunnerType_G4.ResumeLayout(false);
      this.panel_Side_RunnerType_G4.PerformLayout();
      this.panel_Side_GateType1_G4.ResumeLayout(false);
      this.panel_Side_GateType1_G4.PerformLayout();
      this.panel_Side_GateType2_G4.ResumeLayout(false);
      this.panel_Side_GateType2_G4.PerformLayout();
      this.panel_Group.ResumeLayout(false);
      this.tabControl_Hor_RunnerType.ResumeLayout(false);
      this.tabPage_Hor_TwoStage.ResumeLayout(false);
      this.tabPage_Hor_ThreeStage.ResumeLayout(false);
      this.panel_Quadrant.ResumeLayout(false);
      this.panel_Hor_Runner_ThreeType.ResumeLayout(false);
      this.panel_Hor_RunnerType.ResumeLayout(false);
      this.panel1.ResumeLayout(false);
      this.ResumeLayout(false);
    }
  }
}
