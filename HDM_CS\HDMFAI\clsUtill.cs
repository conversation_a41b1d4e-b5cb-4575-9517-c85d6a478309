﻿// Decompiled with JetBrains decompiler
// Type: HDMFAI.clsUtill
// Assembly: HDMFAI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: A66A47CE-D63B-4ACB-B9A4-A4F118252C4E
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFAI.dll

using arUtil;
using HDLog4Net;
using Microsoft.CSharp.RuntimeBinder;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;

namespace HDMFAI
{
  internal class clsUtill
  {
    [DllImport("user32.dll", CharSet = CharSet.Auto)]
    public static extern IntPtr SendMessage(IntPtr hWnd, uint Msg, IntPtr w, IntPtr l);

    public static Image ChangeSyncBackgroundImage(Image p_imgTarget)
    {
      Bitmap bitmap = (Bitmap) p_imgTarget;
      bitmap.MakeTransparent(bitmap.GetPixel(0, 0));
      return (Image) bitmap;
    }

    [DllImport("KERNEL32.DLL", EntryPoint = "GetPrivateProfileStringW", CharSet = CharSet.Unicode, CallingConvention = CallingConvention.StdCall, SetLastError = true)]
    private static extern int GetPrivateProfileString(
      string lpAppName,
      string lpKeyName,
      string lpDefault,
      string lpReturnString,
      int nSize,
      string lpFilename);

    [DllImport("kernel32")]
    private static extern int GetPrivateProfileString(
      string section,
      string key,
      string def,
      StringBuilder retVal,
      int size,
      string filePath);

    [DllImport("kernel32.dll")]
    private static extern int GetPrivateProfileSection(
      string section,
      byte[] lpszReturnBuffer,
      int size,
      string filePath);

    [DllImport("kernel32")]
    private static extern long WritePrivateProfileString(
      string section,
      string key,
      string val,
      string filePath);

    public static string ReadINI(string p_strSection, string p_strKey, string p_strIniPath)
    {
      try
      {
        StringBuilder retVal = new StringBuilder(1024);
        if (clsUtill.GetPrivateProfileString(p_strSection, p_strKey, "", retVal, retVal.Capacity, p_strIniPath) > 0)
          return retVal.ToString();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsUtill]ReadINI):" + ex.Message));
      }
      return "";
    }

    public static void WriteINI(
      string p_strSection,
      string p_strKey,
      string p_strValue,
      string p_strIniPath)
    {
      FileInfo fileInfo = new FileInfo(p_strIniPath);
      if (!fileInfo.Directory.Exists)
        fileInfo.Directory.Create();
      clsUtill.WritePrivateProfileString(p_strSection, p_strKey, p_strValue, p_strIniPath);
    }

    public static Dictionary<string, string> GetINIDataFromSection(
      string p_strIniFPath,
      string p_strSection)
    {
      Dictionary<string, string> iniDataFromSection = new Dictionary<string, string>();
      try
      {
        iniDataFromSection = new INIHelper(p_strIniFPath).GetItemList(p_strSection);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsUtill]GetINIDataFromSection):" + ex.Message));
      }
      return iniDataFromSection;
    }

    public static DataTable GetINIData(string p_strIniFPath)
    {
      DataTable iniData = (DataTable) null;
      try
      {
        List<string> sectionList = new INIHelper(p_strIniFPath).GetSectionList();
        if (sectionList.Count > 0)
        {
          iniData = new DataTable();
          foreach (string p_strSection in sectionList)
          {
            Dictionary<string, string> keys = clsUtill.GetKeys(p_strIniFPath, p_strSection);
            if (!iniData.Columns.Contains("Section"))
              iniData.Columns.Add("Section");
            foreach (KeyValuePair<string, string> keyValuePair in keys)
            {
              if (!iniData.Columns.Contains(keyValuePair.Key))
                iniData.Columns.Add(keyValuePair.Key);
            }
            DataRow dataRow = iniData.Rows.Add();
            dataRow["Section"] = (object) p_strSection;
            foreach (KeyValuePair<string, string> keyValuePair in keys)
              dataRow[keyValuePair.Key] = (object) keyValuePair.Value;
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception[HDMFReport][clsUtill]GetINIData):" + ex.Message));
      }
      return iniData;
    }

    private static Dictionary<string, string> GetKeys(string p_strInifile, string p_strSection)
    {
      Dictionary<string, string> keys = new Dictionary<string, string>();
      byte[] numArray = new byte[2048];
      try
      {
        clsUtill.GetPrivateProfileSection(p_strSection, numArray, 2048, p_strInifile);
        foreach (string str in Encoding.UTF8.GetString(numArray).Trim(new char[1]).Split(new char[1]))
        {
          char[] chArray = new char[1]{ '=' };
          string[] strArray = str.Split(chArray);
          keys.Add(strArray[0], strArray[1]);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception[HDMFReport][clsUtill]GetKeys):" + ex.Message));
      }
      return keys;
    }

    public static DialogResult ShowMessageBox(
      Form p_Form,
      string p_strMessage,
      string p_strTitle = null,
      MessageBoxButtons p_msgButton = MessageBoxButtons.OK,
      MessageBoxIcon p_msgIcon = MessageBoxIcon.None)
    {
      if (p_Form.Parent != null)
      {
        Control parent = p_Form.Parent;
        while (!parent.GetType().Name.Contains("frm"))
          parent = parent.Parent;
        p_Form = (Form) parent;
      }
      if (p_strTitle == null)
        p_strTitle = "";
      using (new CenterWinDialog(p_Form))
        return MessageBox.Show((IWin32Window) new Form()
        {
          TopMost = true
        }, p_strMessage, p_strTitle, p_msgButton, p_msgIcon);
    }

    public static double ConvertToDouble(string p_strValue)
    {
      double result = 0.0;
      double.TryParse(p_strValue, out result);
      return result;
    }

    public static int ConvertToInt(string p_strValue)
    {
      int result = 0;
      int.TryParse(p_strValue, out result);
      return result;
    }

    public static bool ConvertToBoolean(string p_strValue)
    {
      bool result = false;
      bool.TryParse(p_strValue, out result);
      return result;
    }

    public static void CopyFolder(string sourceFolder, string destFolder)
    {
      if (!Directory.Exists(destFolder))
        Directory.CreateDirectory(destFolder);
      string[] files = Directory.GetFiles(sourceFolder);
      string[] directories = Directory.GetDirectories(sourceFolder);
      foreach (string str in files)
      {
        string fileName = Path.GetFileName(str);
        File.Copy(str, Path.Combine(destFolder, fileName));
      }
      foreach (string str in directories)
      {
        string fileName = Path.GetFileName(str);
        clsUtill.CopyFolder(str, Path.Combine(destFolder, fileName));
      }
    }

    [DllImport("user32.dll")]
    private static extern bool SetForegroundWindow(IntPtr hWnd);

    public static void ShowForm(Form frmTarget)
    {
      frmTarget.Show();
      clsUtill.SetForegroundWindow(frmTarget.Handle);
    }

    [DllImport("user32.dll", SetLastError = true)]
    public static extern uint GetWindowThreadProcessId(IntPtr hWnd, ref uint lpdwProcessId);

    public static void ReleaseComObject(object p_obj)
    {
      if (p_obj == null)
        return;
      Marshal.ReleaseComObject(p_obj);
    }

    public static DataTable GetWorkSheetDataForAI(Worksheet p_wSheet)
    {
      string columnName = "";
      int num = 0;
      DataTable workSheetDataForAi = (DataTable) null;
      Dictionary<string, string> p_dic_Names = new Dictionary<string, string>();
      if (p_wSheet != null)
      {
        try
        {
          workSheetDataForAi = new DataTable();
          workSheetDataForAi.TableName = p_wSheet.Name;
          bool flag = false;
          List<string> stringList = new List<string>();
          foreach (Name name in p_wSheet.Names)
          {
            string key = ((IEnumerable<string>) (name.RefersToR1C1Local as string).Split('C')).Last<string>();
            string str = ((IEnumerable<string>) name.Name.Split('!')).Last<string>();
            p_dic_Names.Add(key, str);
          }
          for (int index = 0; index < p_wSheet.Columns.Count; ++index)
          {
            // ISSUE: reference to a compiler-generated field
            if (clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__0 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__0 = CallSite<Func<CallSite, object, Microsoft.Office.Interop.Excel.Range>>.Create(Binder.Convert(CSharpBinderFlags.ConvertExplicit, typeof (Microsoft.Office.Interop.Excel.Range), typeof (clsUtill)));
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            // ISSUE: variable of a compiler-generated type
            Microsoft.Office.Interop.Excel.Range range = clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__0.Target((CallSite) clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__0, p_wSheet.Cells[(object) 1, (object) (index + 1)]);
            // ISSUE: reference to a compiler-generated field
            if (clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__2 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__2 = CallSite<Func<CallSite, object, bool>>.Create(Binder.UnaryOperation(CSharpBinderFlags.None, ExpressionType.IsTrue, typeof (clsUtill), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[1]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, object, bool> target = clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__2.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, object, bool>> p2 = clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__2;
            // ISSUE: reference to a compiler-generated field
            if (clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__1 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__1 = CallSite<Func<CallSite, object, bool, object>>.Create(Binder.BinaryOperation(CSharpBinderFlags.None, ExpressionType.Equal, typeof (clsUtill), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            object obj = clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__1.Target((CallSite) clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__1, range.MergeCells, true);
            if (target((CallSite) p2, obj))
            {
              if (flag)
              {
                if (num < 3)
                {
                  ++num;
                  continue;
                }
                if (num == 3)
                {
                  workSheetDataForAi.Columns.Add("[mg]" + columnName);
                  num = 0;
                  flag = false;
                  continue;
                }
                continue;
              }
              flag = true;
              ++num;
            }
            else
              flag = false;
            // ISSUE: reference to a compiler-generated field
            if (clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__3 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__3 = CallSite<Func<CallSite, object, Microsoft.Office.Interop.Excel.Range>>.Create(Binder.Convert(CSharpBinderFlags.ConvertExplicit, typeof (Microsoft.Office.Interop.Excel.Range), typeof (clsUtill)));
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            if (!(clsUtill.GetExcelCellValue(clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__3.Target((CallSite) clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__3, p_wSheet.Cells[(object) 1, (object) (index + 1)])) == ""))
            {
              // ISSUE: reference to a compiler-generated field
              if (clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__4 == null)
              {
                // ISSUE: reference to a compiler-generated field
                clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__4 = CallSite<Func<CallSite, object, Microsoft.Office.Interop.Excel.Range>>.Create(Binder.Convert(CSharpBinderFlags.ConvertExplicit, typeof (Microsoft.Office.Interop.Excel.Range), typeof (clsUtill)));
              }
              // ISSUE: reference to a compiler-generated field
              // ISSUE: reference to a compiler-generated field
              columnName = clsUtill.GetExcelCellName(clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__4.Target((CallSite) clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__4, p_wSheet.Cells[(object) 1, (object) (index + 1)]), p_dic_Names);
              if (columnName != string.Empty)
                workSheetDataForAi.Columns.Add(columnName);
            }
            else
              break;
          }
          int RowIndex = 2;
          while (true)
          {
            // ISSUE: reference to a compiler-generated field
            if (clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__5 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__5 = CallSite<Func<CallSite, object, Microsoft.Office.Interop.Excel.Range>>.Create(Binder.Convert(CSharpBinderFlags.ConvertExplicit, typeof (Microsoft.Office.Interop.Excel.Range), typeof (clsUtill)));
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            if (!(clsUtill.GetExcelCellValue(clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__5.Target((CallSite) clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__5, p_wSheet.Cells[(object) RowIndex, (object) 1])) == ""))
            {
              DataRow dataRow1 = workSheetDataForAi.Rows.Add();
              for (int index = 0; index < workSheetDataForAi.Columns.Count; ++index)
              {
                int ColumnIndex = !workSheetDataForAi.Columns[index].ToString().Contains("[mg]") ? clsUtill.GetColumnIndex(p_wSheet, workSheetDataForAi.Columns[index].ToString()) : clsUtill.GetColumnIndex(p_wSheet, workSheetDataForAi.Columns[index].ToString().Replace("[mg]", "")) + 2;
                DataRow dataRow2 = dataRow1;
                int columnIndex = index;
                // ISSUE: reference to a compiler-generated field
                if (clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__6 == null)
                {
                  // ISSUE: reference to a compiler-generated field
                  clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__6 = CallSite<Func<CallSite, object, Microsoft.Office.Interop.Excel.Range>>.Create(Binder.Convert(CSharpBinderFlags.ConvertExplicit, typeof (Microsoft.Office.Interop.Excel.Range), typeof (clsUtill)));
                }
                // ISSUE: reference to a compiler-generated field
                // ISSUE: reference to a compiler-generated field
                string excelCellValue = clsUtill.GetExcelCellValue(clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__6.Target((CallSite) clsUtill.\u003C\u003Eo__21.\u003C\u003Ep__6, p_wSheet.Cells[(object) RowIndex, (object) ColumnIndex]));
                dataRow2[columnIndex] = (object) excelCellValue;
              }
              ++RowIndex;
            }
            else
              break;
          }
        }
        catch (Exception ex)
        {
          HDLog.Instance().Error((object) ("Exception([HDMFSummary][clsSummaryUtill]GetWorkSheetDataForAI):" + ex.Message));
        }
      }
      return workSheetDataForAi;
    }

    public static string GetExcelCellValue(Microsoft.Office.Interop.Excel.Range p_rgCell)
    {
      string excelCellValue = "";
      if (p_rgCell != null)
      {
        try
        {
          // ISSUE: reference to a compiler-generated field
          if (clsUtill.\u003C\u003Eo__22.\u003C\u003Ep__1 == null)
          {
            // ISSUE: reference to a compiler-generated field
            clsUtill.\u003C\u003Eo__22.\u003C\u003Ep__1 = CallSite<Func<CallSite, object, bool>>.Create(Binder.UnaryOperation(CSharpBinderFlags.None, ExpressionType.IsTrue, typeof (clsUtill), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[1]
            {
              CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
            }));
          }
          // ISSUE: reference to a compiler-generated field
          Func<CallSite, object, bool> target1 = clsUtill.\u003C\u003Eo__22.\u003C\u003Ep__1.Target;
          // ISSUE: reference to a compiler-generated field
          CallSite<Func<CallSite, object, bool>> p1 = clsUtill.\u003C\u003Eo__22.\u003C\u003Ep__1;
          // ISSUE: reference to a compiler-generated field
          if (clsUtill.\u003C\u003Eo__22.\u003C\u003Ep__0 == null)
          {
            // ISSUE: reference to a compiler-generated field
            clsUtill.\u003C\u003Eo__22.\u003C\u003Ep__0 = CallSite<Func<CallSite, object, object, object>>.Create(Binder.BinaryOperation(CSharpBinderFlags.None, ExpressionType.NotEqual, typeof (clsUtill), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[2]
            {
              CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null),
              CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.Constant, (string) null)
            }));
          }
          // ISSUE: reference to a compiler-generated field
          // ISSUE: reference to a compiler-generated field
          object obj1 = clsUtill.\u003C\u003Eo__22.\u003C\u003Ep__0.Target((CallSite) clsUtill.\u003C\u003Eo__22.\u003C\u003Ep__0, p_rgCell.Text, (object) null);
          if (target1((CallSite) p1, obj1))
          {
            // ISSUE: reference to a compiler-generated field
            if (clsUtill.\u003C\u003Eo__22.\u003C\u003Ep__3 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsUtill.\u003C\u003Eo__22.\u003C\u003Ep__3 = CallSite<Func<CallSite, object, string>>.Create(Binder.Convert(CSharpBinderFlags.None, typeof (string), typeof (clsUtill)));
            }
            // ISSUE: reference to a compiler-generated field
            Func<CallSite, object, string> target2 = clsUtill.\u003C\u003Eo__22.\u003C\u003Ep__3.Target;
            // ISSUE: reference to a compiler-generated field
            CallSite<Func<CallSite, object, string>> p3 = clsUtill.\u003C\u003Eo__22.\u003C\u003Ep__3;
            // ISSUE: reference to a compiler-generated field
            if (clsUtill.\u003C\u003Eo__22.\u003C\u003Ep__2 == null)
            {
              // ISSUE: reference to a compiler-generated field
              clsUtill.\u003C\u003Eo__22.\u003C\u003Ep__2 = CallSite<Func<CallSite, object, object>>.Create(Binder.InvokeMember(CSharpBinderFlags.None, "ToString", (IEnumerable<System.Type>) null, typeof (clsUtill), (IEnumerable<CSharpArgumentInfo>) new CSharpArgumentInfo[1]
              {
                CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, (string) null)
              }));
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            object obj2 = clsUtill.\u003C\u003Eo__22.\u003C\u003Ep__2.Target((CallSite) clsUtill.\u003C\u003Eo__22.\u003C\u003Ep__2, p_rgCell.Text);
            excelCellValue = target2((CallSite) p3, obj2);
          }
        }
        catch (Exception ex)
        {
          HDLog.Instance().Error((object) ("Exception([HDMFSummary][clsSummaryUtill]GetExcelCellValue):" + ex.Message));
        }
      }
      return excelCellValue;
    }

    public static string GetExcelCellName(Microsoft.Office.Interop.Excel.Range p_rgCell, Dictionary<string, string> p_dic_Names)
    {
      string empty = string.Empty;
      if (p_rgCell != null)
      {
        try
        {
          Dictionary<string, string>.KeyCollection keys = p_dic_Names.Keys;
          int column = p_rgCell.Column;
          string str = column.ToString();
          if (keys.Contains<string>(str))
          {
            Dictionary<string, string> dictionary = p_dic_Names;
            column = p_rgCell.Column;
            string key = column.ToString();
            empty = dictionary[key];
          }
        }
        catch (Exception ex)
        {
          HDLog.Instance().Error((object) ("Exception([HDMFSummary][clsSummaryUtill]GetExcelCellNamed):" + ex.Message));
        }
      }
      return empty;
    }

    public static int GetColumnIndex(Worksheet p_wSheet, string p_strNamedRange)
    {
      int columnIndex = -1;
      try
      {
        // ISSUE: reference to a compiler-generated method
        columnIndex = p_wSheet.get_Range((object) p_strNamedRange, System.Type.Missing).Column;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFSummary][clsSummaryUtill]GetColumnIndex):" + ex.Message));
      }
      return columnIndex;
    }

    public enum ProgressBarColor
    {
      None,
      Green,
      Red,
      Yellow,
    }
  }
}
