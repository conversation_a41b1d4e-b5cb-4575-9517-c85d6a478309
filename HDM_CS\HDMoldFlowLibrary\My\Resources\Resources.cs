﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlowLibrary.My.Resources.Resources
// Assembly: HDMoldFlowLibrary, Version=2.24.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 16E5D508-B892-4B5D-A02C-2C1930D27867
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMoldFlowLibrary.dll

using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;

#nullable disable
namespace HDMoldFlowLibrary.My.Resources
{
  [StandardModule]
  [GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
  [DebuggerNonUserCode]
  [CompilerGenerated]
  [HideModuleName]
  internal sealed class Resources
  {
    private static ResourceManager resourceMan;
    private static CultureInfo resourceCulture;

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    internal static ResourceManager ResourceManager
    {
      get
      {
        if (object.ReferenceEquals((object) HDMoldFlowLibrary.My.Resources.Resources.resourceMan, (object) null))
          HDMoldFlowLibrary.My.Resources.Resources.resourceMan = new ResourceManager("HDMoldFlowLibrary.Resources", typeof (HDMoldFlowLibrary.My.Resources.Resources).Assembly);
        return HDMoldFlowLibrary.My.Resources.Resources.resourceMan;
      }
    }

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    internal static CultureInfo Culture
    {
      get => HDMoldFlowLibrary.My.Resources.Resources.resourceCulture;
      set => HDMoldFlowLibrary.My.Resources.Resources.resourceCulture = value;
    }
  }
}
