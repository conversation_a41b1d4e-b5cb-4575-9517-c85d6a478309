﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmRunnerDB
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDLog4Net;
using HDMFUserControl;
using HDMoldFlow.Properties;
using Microsoft.WindowsAPICodePack.Dialogs;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using System.Xml;

namespace HDMoldFlow
{
  public class frmRunnerDB : Form
  {
    private const int WS_EX_COMPOSITED = 33554432;
    private IContainer components = (IContainer) null;
    private Label label_DB_List;
    private ListBox listBox_DB;
    private Label label_Pin;
    private Label label_Side;
    private Label label_Hor_Runner;
    private Label label_Sprue;
    private Panel panel1;
    private RadioButton radioButton_Pin_HotSystem;
    private RadioButton radioButton_Pin_ColdSystem;
    private Panel panel2;
    private RadioButton radioButton_Hor_Two_Type3;
    private RadioButton radioButton_Hor_Two_Type1;
    private Label label_Hor_Two_Dia;
    private Label label_Hor_Two_Length;
    private Panel panel5;
    private RadioButton radioButton_Sprue_Hot;
    private RadioButton radioButton_Sprue_Cold;
    private Label label_Sprue_Dim1;
    private Label label_Sprue_Length;
    private Label label_Sprue_Dim2;
    private Label label_Cavity;
    private Label label_Cavity_Occ;
    private Label label_DB_Company;
    private Label label_DB_Item;
    private Label label_DB_Option_W;
    private NewComboBox newComboBox_Company;
    private NewComboBox newComboBox_Item;
    private RadioButton radioButton_Hor_Two_Type2;
    private Label label_DB_Company_W;
    private Label label_DB_Item_W;
    private NewTextBox newTextBox_Company;
    private NewTextBox newTextBox_Item;
    private NewTextBox newTextBox_Option;
    private NewTextBox newTextBox_Cavity_Occ;
    private NewTextBox newTextBox_Hor_Two_Length;
    private NewTextBox newTextBox_Hor_Two_Dia;
    private NewTextBox newTextBox_Sprue_Dim2;
    private NewTextBox newTextBox_Sprue_Dim1;
    private NewTextBox newTextBox_Sprue_Length;
    private NewButton newButton_Add;
    private NewButton newButton_Edit;
    private NewButton newButton_Del;
    private NewButton newButton_Import;
    private NewButton newButton_Export;
    private RadioButton radioButton_Hor_Two_Type4;
    private NewTextBox newTextBox_Hor_Two_Dim1;
    private NewTextBox newTextBox_Hor_Two_Dim2;
    private Label label_Hor_Two_Dim2;
    private Label label_Hor_Two_Dim1;
    private NewTextBox newTextBox_Hor_Two_Angle;
    private Label label_Hor_Two_Angle;
    private Label label_Hor_Two_Direction;
    private NewComboBox newComboBox_Hor_Two_Direction;
    private Panel panel_Pin_Group;
    private NewButton newButton_Pin_G1;
    private NewButton newButton_Pin_G4;
    private NewButton newButton_Pin_G3;
    private NewButton newButton_Pin_G2;
    private TabControl tabControl_Pin_Group;
    private TabPage tabPage_Pin_Group1;
    private Label label_Pin_Gate_G1;
    private Label label_Pin_Runner_G1;
    private Label label_Pin_GateDim1_G1;
    private Label label_Pin_RunnerLength_G1;
    private Label label_Pin_RunnerDim1_G1;
    private Label label_Pin_GateDim2_G1;
    private Label label_Pin_RunnerDim2_G1;
    private NewTextBox newTextBox_Pin_GateDim1_G1;
    private NewTextBox newTextBox_Pin_GateDim2_G1;
    private NewTextBox newTextBox_Pin_RunnerLength_G1;
    private NewTextBox newTextBox_Pin_RunnerDim1_G1;
    private NewTextBox newTextBox_Pin_RunnerDim2_G1;
    private TabPage tabPage_Pin_Group2;
    private Label label_Pin_Gate_G2;
    private Label label_Pin_Runner_G2;
    private Label label_Pin_GateDim1_G2;
    private Label label_Pin_RunnerLength_G2;
    private Label label_Pin_RunnerDim1_G2;
    private Label label_Pin_GateDim2_G2;
    private Label label_Pin_RunnerDim2_G2;
    private NewTextBox newTextBox_Pin_GateDim1_G2;
    private NewTextBox newTextBox_Pin_GateDim2_G2;
    private NewTextBox newTextBox_Pin_RunnerLength_G2;
    private NewTextBox newTextBox_Pin_RunnerDim1_G2;
    private NewTextBox newTextBox_Pin_RunnerDim2_G2;
    private TabPage tabPage_Pin_Group3;
    private Label label_Pin_Gate_G3;
    private Label label_Pin_Runner_G3;
    private Label label_Pin_GateDim1_G3;
    private Label label_Pin_RunnerLength_G3;
    private Label label_Pin_RunnerDim1_G3;
    private Label label_Pin_GateDim2_G3;
    private Label label_Pin_RunnerDim2_G3;
    private NewTextBox newTextBox_Pin_GateDim1_G3;
    private NewTextBox newTextBox_Pin_GateDim2_G3;
    private NewTextBox newTextBox_Pin_RunnerLength_G3;
    private NewTextBox newTextBox_Pin_RunnerDim1_G3;
    private NewTextBox newTextBox_Pin_RunnerDim2_G3;
    private TabPage tabPage_Pin_Group4;
    private Label label_Pin_Gate_G4;
    private Label label_Pin_Runner_G4;
    private Label label_Pin_GateDim1_G4;
    private Label label_Pin_RunnerLength_G4;
    private Label label_Pin_RunnerDim1_G4;
    private Label label_Pin_GateDim2_G4;
    private NewTextBox newTextBox_Pin_GateDim1_G4;
    private NewTextBox newTextBox_Pin_GateDim2_G4;
    private NewTextBox newTextBox_Pin_RunnerLength_G4;
    private NewTextBox newTextBox_Pin_RunnerDim1_G4;
    private NewTextBox newTextBox_Pin_RunnerDim2_G4;
    private TabControl tabControl_Side_Group;
    private TabPage tabPage_Side_Group1;
    private Panel panel9;
    private RadioButton radioButton_Side_GateCircle_G1;
    private RadioButton radioButton_Side_GateRect_G1;
    private Panel panel_Side_GateType2_G1;
    private RadioButton radioButton_Side_GateSubMarine_G1;
    private RadioButton radioButton_Side_GateNormal_G1;
    private Panel panel11;
    private RadioButton radioButton_Side_RunnerRectangle_G1;
    private RadioButton radioButton_Side_RunnerCircle_G1;
    private RadioButton radioButton_Side_RunnerTrepezoidal_G1;
    private Label label_Side_Gate_G1;
    private Label label_Side_Runner_G1;
    private Label label_Side_GateLength_G1;
    private Label label_Side_GateDim1_G1;
    private Label label_Side_RunnerLength_G1;
    private Label label_Side_GateDim3_G1;
    private NewTextBox newTextBox_Side_RunnerDim4_G1;
    private Label label_Side_RunnerDim2_G1;
    private Label label_Side_RunnerDim4_G1;
    private Label label_Side_GateDim2_G1;
    private Label label_Side_RunnerDim1_G1;
    private Label label_Side_GateDim4_G1;
    private Label label_Side_RunnerDim3_G1;
    private NewTextBox newTextBox_Side_GateLength_G1;
    private NewTextBox newTextBox_Side_GateDim1_G1;
    private NewTextBox newTextBox_Side_GateDim2_G1;
    private NewTextBox newTextBox_Side_GateDim3_G1;
    private NewTextBox newTextBox_Side_GateDim4_G1;
    private NewTextBox newTextBox_Side_RunnerLength_G1;
    private NewTextBox newTextBox_Side_RunnerDim1_G1;
    private NewTextBox newTextBox_Side_RunnerDim2_G1;
    private NewTextBox newTextBox_Side_RunnerDim3_G1;
    private TabPage tabPage_Side_Group2;
    private Panel panel12;
    private RadioButton radioButton_Side_GateCircle_G2;
    private RadioButton radioButton_Side_GateRect_G2;
    private Panel panel_Side_GateType2_G2;
    private RadioButton radioButton_Side_GateSubMarine_G2;
    private RadioButton radioButton_Side_GateNormal_G2;
    private Panel panel14;
    private RadioButton radioButton_Side_RunnerRectangle_G2;
    private RadioButton radioButton_Side_RunnerCircle_G2;
    private RadioButton radioButton_Side_RunnerTrepezoidal_G2;
    private Label label_Side_Gate_G2;
    private Label label_Side_Runner_G2;
    private Label label_Side_GateLength_G2;
    private Label label_Side_GateDim1_G2;
    private Label label_Side_RunnerLength_G2;
    private Label label_Side_GateDim3_G2;
    private NewTextBox newTextBox_Side_RunnerDim4_G2;
    private Label label_Side_RunnerDim2_G2;
    private Label label_Side_RunnerDim4_G2;
    private Label label_Side_GateDim2_G2;
    private Label label_Side_RunnerDim1_G2;
    private Label label_Side_GateDim4_G2;
    private Label label_Side_RunnerDim3_G2;
    private NewTextBox newTextBox_Side_GateLength_G2;
    private NewTextBox newTextBox_Side_GateDim1_G2;
    private NewTextBox newTextBox_Side_GateDim2_G2;
    private NewTextBox newTextBox_Side_GateDim3_G2;
    private NewTextBox newTextBox_Side_GateDim4_G2;
    private NewTextBox newTextBox_Side_RunnerLength_G2;
    private NewTextBox newTextBox_Side_RunnerDim1_G2;
    private NewTextBox newTextBox_Side_RunnerDim2_G2;
    private NewTextBox newTextBox_Side_RunnerDim3_G2;
    private TabPage tabPage_Side_Group3;
    private Panel panel6;
    private RadioButton radioButton_Side_GateCircle_G3;
    private RadioButton radioButton_Side_GateRect_G3;
    private Panel panel_Side_GateType2_G3;
    private RadioButton radioButton_Side_GateSubMarine_G3;
    private RadioButton radioButton_Side_GateNormal_G3;
    private Panel panel8;
    private RadioButton radioButton_Side_RunnerRectangle_G3;
    private RadioButton radioButton_Side_RunnerCircle_G3;
    private RadioButton radioButton_Side_RunnerTrepezoidal_G3;
    private Label label_Side_Gate_G3;
    private Label label_Side_Runner_G3;
    private Label label_Side_GateLength_G3;
    private Label label_Side_GateDim1_G3;
    private Label label_Side_RunnerLength_G3;
    private Label label_Side_GateDim3_G3;
    private NewTextBox newTextBox_Side_RunnerDim4_G3;
    private Label label_Side_RunnerDim2_G3;
    private Label label_Side_RunnerDim4_G3;
    private Label label_Side_GateDim2_G3;
    private Label label_Side_RunnerDim1_G3;
    private Label label_Side_GateDim4_G3;
    private Label label_Side_RunnerDim3_G3;
    private NewTextBox newTextBox_Side_GateLength_G3;
    private NewTextBox newTextBox_Side_GateDim1_G3;
    private NewTextBox newTextBox_Side_GateDim2_G3;
    private NewTextBox newTextBox_Side_GateDim3_G3;
    private NewTextBox newTextBox_Side_GateDim4_G3;
    private NewTextBox newTextBox_Side_RunnerLength_G3;
    private NewTextBox newTextBox_Side_RunnerDim1_G3;
    private NewTextBox newTextBox_Side_RunnerDim2_G3;
    private NewTextBox newTextBox_Side_RunnerDim3_G3;
    private TabPage tabPage_Side_Group4;
    private Label label_Side_Gate_G4;
    private Label label_Side_Runner_G4;
    private Panel panel18;
    private RadioButton radioButton_Side_GateCircle_G4;
    private RadioButton radioButton_Side_GateRect_G4;
    private Panel panel_Side_GateType2_G4;
    private RadioButton radioButton_Side_GateSubMarine_G4;
    private RadioButton radioButton_Side_GateNormal_G4;
    private Panel panel20;
    private RadioButton radioButton_Side_RunnerRectangle_G4;
    private RadioButton radioButton_Side_RunnerCircle_G4;
    private RadioButton radioButton_Side_RunnerTrepezoidal_G4;
    private Label label_Side_GateLength_G4;
    private Label label_Side_GateDim1_G4;
    private Label label_Side_RunnerLength_G4;
    private Label label_Side_GateDim3_G4;
    private NewTextBox newTextBox_Side_RunnerDim4_G4;
    private Label label_Side_RunnerDim2_G4;
    private Label label_Side_RunnerDim4_G4;
    private Label label_Side_GateDim2_G4;
    private Label label_Side_RunnerDim1_G4;
    private Label label_Side_GateDim4_G4;
    private Label label_Side_RunnerDim3_G4;
    private NewTextBox newTextBox_Side_GateLength_G4;
    private NewTextBox newTextBox_Side_GateDim1_G4;
    private NewTextBox newTextBox_Side_GateDim2_G4;
    private NewTextBox newTextBox_Side_GateDim3_G4;
    private NewTextBox newTextBox_Side_GateDim4_G4;
    private NewTextBox newTextBox_Side_RunnerLength_G4;
    private NewTextBox newTextBox_Side_RunnerDim1_G4;
    private NewTextBox newTextBox_Side_RunnerDim2_G4;
    private NewTextBox newTextBox_Side_RunnerDim3_G4;
    private NewButton newButton_Side_G3;
    private NewButton newButton_Side_G2;
    private NewButton newButton_Side_G4;
    private NewButton newButton_Side_G1;
    private Panel panel_Side_Group;
    private Label label_Pin_RunnerDim2_G4;
    private Panel panel_Hor_RunnerType;
    private NewButton newButton_Hor_Runner_TwoStage;
    private NewButton newButton_Hor_Runner_ThreeStage;
    private TabControl tabControl_Hor_RunnerType;
    private TabPage tabPage_Hor_TwoStage;
    private TabPage tabPage_Hor_ThreeStage;
    private Panel panel_Quadrant;
    private RadioButton radioButton_Quadrant4;
    private RadioButton radioButton_Quadrant2;
    private NewTextBox newTextBox_Hor_Three_CenterTol;
    private Panel panel_Hor_Runner_ThreeType;
    private RadioButton radioButton_Hor_Three_Type2;
    private RadioButton radioButton_Hor_Three_Type1;
    private Label label_Hor_Three_Dia;
    private Label label_Hor_Three_CenterTol;
    private NewTextBox newTextBox_Hor_Three_Dia;
    private NewTextBox newTextBox_Hor_Two_CenterTol;
    private Label label_Hor_Two_CenterTol;
    private CheckBox checkBox_Hor_Two_Inter;
    private Label label_Hor_Three_Length;
    private NewTextBox newTextBox_Hor_Three_Length;

    protected override CreateParams CreateParams
    {
      get
      {
        CreateParams createParams = base.CreateParams;
        createParams.ExStyle |= 33554432;
        return createParams;
      }
    }

    public frmRunnerDB()
    {
      this.InitializeComponent();
      this.newButton_Add.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Add.Image);
      this.newButton_Edit.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Edit.Image);
      this.newButton_Del.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Del.Image);
      this.newButton_Import.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Import.Image);
      this.newButton_Export.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Export.Image);
      this.Text = LocaleControl.getInstance().GetString("IDS_RUNNER") + " DB";
      this.newButton_Pin_G1.ButtonText = LocaleControl.getInstance().GetString("IDS_GROUP") + "1";
      this.newButton_Pin_G2.ButtonText = LocaleControl.getInstance().GetString("IDS_GROUP") + "2";
      this.newButton_Pin_G3.ButtonText = LocaleControl.getInstance().GetString("IDS_GROUP") + "3";
      this.newButton_Pin_G4.ButtonText = LocaleControl.getInstance().GetString("IDS_GROUP") + "4";
      this.newButton_Side_G1.ButtonText = LocaleControl.getInstance().GetString("IDS_GROUP") + "1";
      this.newButton_Side_G2.ButtonText = LocaleControl.getInstance().GetString("IDS_GROUP") + "2";
      this.newButton_Side_G3.ButtonText = LocaleControl.getInstance().GetString("IDS_GROUP") + "3";
      this.newButton_Side_G4.ButtonText = LocaleControl.getInstance().GetString("IDS_GROUP") + "4";
      this.label_DB_List.Text = "DB " + LocaleControl.getInstance().GetString("IDS_LIST");
      this.label_DB_Company.Text = LocaleControl.getInstance().GetString("IDS_COMPANY_NAME");
      this.label_DB_Item.Text = LocaleControl.getInstance().GetString("IDS_ITEM_NAME");
      this.label_DB_Company_W.Text = LocaleControl.getInstance().GetString("IDS_COMPANY_NAME");
      this.label_DB_Item_W.Text = LocaleControl.getInstance().GetString("IDS_ITEM_NAME");
      this.label_DB_Option_W.Text = LocaleControl.getInstance().GetString("IDS_OPTION");
      this.newButton_Add.ButtonText = LocaleControl.getInstance().GetString("IDS_ADD");
      this.newButton_Edit.ButtonText = LocaleControl.getInstance().GetString("IDS_EDIT");
      this.newButton_Del.ButtonText = LocaleControl.getInstance().GetString("IDS_DELETE");
      this.newButton_Import.ButtonText = LocaleControl.getInstance().GetString("IDS_IMPORT");
      this.newButton_Export.ButtonText = LocaleControl.getInstance().GetString("IDS_EXPORT");
      this.radioButton_Pin_ColdSystem.Text = LocaleControl.getInstance().GetString("IDS_COLD_SYSTEM");
      this.radioButton_Pin_HotSystem.Text = LocaleControl.getInstance().GetString("IDS_HOT_SYSTEM");
      this.label_Pin.Text = "[" + LocaleControl.getInstance().GetString("IDS_PIN") + "]";
      this.label_Pin_Gate_G1.Text = "<" + LocaleControl.getInstance().GetString("IDS_GATE") + ">";
      this.label_Pin_Gate_G2.Text = "<" + LocaleControl.getInstance().GetString("IDS_GATE") + ">";
      this.label_Pin_Gate_G3.Text = "<" + LocaleControl.getInstance().GetString("IDS_GATE") + ">";
      this.label_Pin_Gate_G4.Text = "<" + LocaleControl.getInstance().GetString("IDS_GATE") + ">";
      this.label_Pin_Runner_G1.Text = "<" + LocaleControl.getInstance().GetString("IDS_RUNNER") + ">";
      this.label_Pin_Runner_G2.Text = "<" + LocaleControl.getInstance().GetString("IDS_RUNNER") + ">";
      this.label_Pin_Runner_G3.Text = "<" + LocaleControl.getInstance().GetString("IDS_RUNNER") + ">";
      this.label_Pin_Runner_G4.Text = "<" + LocaleControl.getInstance().GetString("IDS_RUNNER") + ">";
      this.label_Pin_RunnerLength_G1.Text = LocaleControl.getInstance().GetString("IDS_RUNNER_LENGTH");
      this.label_Pin_RunnerLength_G2.Text = LocaleControl.getInstance().GetString("IDS_RUNNER_LENGTH");
      this.label_Pin_RunnerLength_G3.Text = LocaleControl.getInstance().GetString("IDS_RUNNER_LENGTH");
      this.label_Pin_RunnerLength_G4.Text = LocaleControl.getInstance().GetString("IDS_RUNNER_LENGTH");
      this.radioButton_Side_GateNormal_G1.Text = LocaleControl.getInstance().GetString("IDS_NORMAL");
      this.radioButton_Side_GateNormal_G2.Text = LocaleControl.getInstance().GetString("IDS_NORMAL");
      this.radioButton_Side_GateNormal_G3.Text = LocaleControl.getInstance().GetString("IDS_NORMAL");
      this.radioButton_Side_GateNormal_G4.Text = LocaleControl.getInstance().GetString("IDS_NORMAL");
      this.radioButton_Side_GateSubMarine_G1.Text = LocaleControl.getInstance().GetString("IDS_SUBMARINE");
      this.radioButton_Side_GateSubMarine_G2.Text = LocaleControl.getInstance().GetString("IDS_SUBMARINE");
      this.radioButton_Side_GateSubMarine_G3.Text = LocaleControl.getInstance().GetString("IDS_SUBMARINE");
      this.radioButton_Side_GateSubMarine_G4.Text = LocaleControl.getInstance().GetString("IDS_SUBMARINE");
      this.label_Side.Text = "[" + LocaleControl.getInstance().GetString("IDS_SIDE") + "]";
      this.label_Side_Gate_G1.Text = "<" + LocaleControl.getInstance().GetString("IDS_GATE") + ">";
      this.label_Side_Gate_G2.Text = "<" + LocaleControl.getInstance().GetString("IDS_GATE") + ">";
      this.label_Side_Gate_G3.Text = "<" + LocaleControl.getInstance().GetString("IDS_GATE") + ">";
      this.label_Side_Gate_G4.Text = "<" + LocaleControl.getInstance().GetString("IDS_GATE") + ">";
      this.label_Side_Runner_G1.Text = "<" + LocaleControl.getInstance().GetString("IDS_RUNNER") + ">";
      this.label_Side_Runner_G2.Text = "<" + LocaleControl.getInstance().GetString("IDS_RUNNER") + ">";
      this.label_Side_Runner_G3.Text = "<" + LocaleControl.getInstance().GetString("IDS_RUNNER") + ">";
      this.label_Side_Runner_G4.Text = "<" + LocaleControl.getInstance().GetString("IDS_RUNNER") + ">";
      this.label_Side_GateLength_G1.Text = LocaleControl.getInstance().GetString("IDS_GATE_LENGTH");
      this.label_Side_GateLength_G2.Text = LocaleControl.getInstance().GetString("IDS_GATE_LENGTH");
      this.label_Side_GateLength_G3.Text = LocaleControl.getInstance().GetString("IDS_GATE_LENGTH");
      this.label_Side_GateLength_G4.Text = LocaleControl.getInstance().GetString("IDS_GATE_LENGTH");
      this.label_Side_RunnerLength_G1.Text = LocaleControl.getInstance().GetString("IDS_RUNNER_LENGTH");
      this.label_Side_RunnerLength_G2.Text = LocaleControl.getInstance().GetString("IDS_RUNNER_LENGTH");
      this.label_Side_RunnerLength_G3.Text = LocaleControl.getInstance().GetString("IDS_RUNNER_LENGTH");
      this.label_Side_RunnerLength_G4.Text = LocaleControl.getInstance().GetString("IDS_RUNNER_LENGTH");
      this.newButton_Hor_Runner_TwoStage.ButtonText = "2" + LocaleControl.getInstance().GetString("IDS_STAGE_MANIFOLD");
      this.newButton_Hor_Runner_ThreeStage.ButtonText = "3" + LocaleControl.getInstance().GetString("IDS_STAGE_MANIFOLD");
      this.label_Hor_Runner.Text = LocaleControl.getInstance().GetString("IDS_HORIZON_RUNNER");
      this.radioButton_Hor_Two_Type1.Text = "I " + LocaleControl.getInstance().GetString("IDS_TYPE");
      this.radioButton_Hor_Two_Type2.Text = "H " + LocaleControl.getInstance().GetString("IDS_TYPE");
      this.radioButton_Hor_Two_Type3.Text = "☆ - + " + LocaleControl.getInstance().GetString("IDS_TYPE");
      this.radioButton_Hor_Two_Type4.Text = "☆ - x " + LocaleControl.getInstance().GetString("IDS_TYPE");
      this.radioButton_Quadrant2.Text = "2" + LocaleControl.getInstance().GetString("IDS_QUADRANT");
      this.radioButton_Quadrant4.Text = "4" + LocaleControl.getInstance().GetString("IDS_QUADRANT");
      this.radioButton_Hor_Three_Type1.Text = "H " + LocaleControl.getInstance().GetString("IDS_TYPE");
      this.radioButton_Hor_Three_Type2.Text = "☆ - + " + LocaleControl.getInstance().GetString("IDS_TYPE");
      this.label_Hor_Two_Dia.Text = LocaleControl.getInstance().GetString("IDS_HORIZON_RUNNER_DIA");
      this.label_Hor_Two_Length.Text = LocaleControl.getInstance().GetString("IDS_HORIZON_RUNNER_LENGTH");
      this.label_Hor_Two_Direction.Text = LocaleControl.getInstance().GetString("IDS_HORIZON_RUNNER_DIRECTION");
      this.label_Hor_Two_Dim1.Text = "[ X ]" + LocaleControl.getInstance().GetString("IDS_HORIZON_LENGTH");
      this.label_Hor_Two_Dim2.Text = "[ Y ]" + LocaleControl.getInstance().GetString("IDS_HORIZON_LENGTH");
      this.label_Hor_Two_Angle.Text = "∠ " + LocaleControl.getInstance().GetString("IDS_HORIZON_ANGLE");
      this.label_Hor_Two_CenterTol.Text = "Center " + LocaleControl.getInstance().GetString("IDS_TOLERANCE");
      this.label_Hor_Three_Dia.Text = LocaleControl.getInstance().GetString("IDS_HORIZON_RUNNER_DIA");
      this.label_Hor_Three_CenterTol.Text = "Center " + LocaleControl.getInstance().GetString("IDS_TOLERANCE");
      this.label_Hor_Three_Length.Text = LocaleControl.getInstance().GetString("IDS_THREE_RUNNERLENGTH");
      this.radioButton_Sprue_Cold.Text = LocaleControl.getInstance().GetString("IDS_COLD_SPRUE");
      this.radioButton_Sprue_Hot.Text = LocaleControl.getInstance().GetString("IDS_HOT_SPRUE");
      this.label_Sprue_Length.Text = LocaleControl.getInstance().GetString("IDS_SPRUE_LENGTH");
      this.label_Sprue.Text = "[" + LocaleControl.getInstance().GetString("IDS_SPRUE") + "]";
      this.label_Cavity.Text = "[" + LocaleControl.getInstance().GetString("IDS_CAVITY") + "]";
    }

    private void frmRunnerDB_Load(object sender, EventArgs e)
    {
      try
      {
        this.RefreshDBList();
        this.RefreshCompanyUI();
        this.RefreshItemUI();
        this.newComboBox_Hor_Two_Direction.Items.AddRange((object[]) new string[4]
        {
          "+X",
          "-X",
          "+Y",
          "-Y"
        });
        this.newComboBox_Hor_Two_Direction.SelectedIndex = 0;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]frmRunnerDB_Load):" + ex.Message));
      }
    }

    public void RefreshDBList(string p_strCompany = null, string p_strItem = null)
    {
      this.listBox_DB.Items.Clear();
      if (p_strCompany == null)
        p_strCompany = LocaleControl.getInstance().GetString("IDS_ALL");
      if (p_strItem == null)
        p_strItem = LocaleControl.getInstance().GetString("IDS_ALL");
      try
      {
        DataRow[] array = clsDefine.g_dtRunnerDB.AsEnumerable().ToArray<DataRow>();
        if (p_strCompany != LocaleControl.getInstance().GetString("IDS_ALL"))
          array = ((IEnumerable<DataRow>) array).Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Company"].ToString() == p_strCompany)).ToArray<DataRow>();
        if (array.Length != 0)
        {
          if (p_strItem != LocaleControl.getInstance().GetString("IDS_ALL"))
            array = ((IEnumerable<DataRow>) array).Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Item"].ToString() == p_strItem)).ToArray<DataRow>();
          if (array.Length != 0)
          {
            List<string> stringList = new List<string>();
            foreach (DataRow dataRow in array)
              stringList.Add(dataRow["Name"].ToString());
            stringList.Sort();
            this.listBox_DB.Items.AddRange((object[]) stringList.ToArray());
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunnerDB]RefreshDBList):" + ex.Message));
      }
      this.newTextBox_Company.Value = "";
      this.newTextBox_Item.Value = "";
      this.newTextBox_Option.Value = "";
    }

    public void RefreshCompanyUI(string p_strCompany = null)
    {
      this.newComboBox_Company.Items.Clear();
      if (p_strCompany == null)
        p_strCompany = LocaleControl.getInstance().GetString("IDS_ALL");
      try
      {
        this.newComboBox_Company.Items.Add((object) LocaleControl.getInstance().GetString("IDS_ALL"));
        DataRow[] array = clsDefine.g_dtRunnerDB.AsEnumerable().ToArray<DataRow>();
        List<string> stringList = new List<string>();
        foreach (DataRow dataRow in array)
        {
          if (!stringList.Contains(dataRow["Company"].ToString()))
            stringList.Add(dataRow["Company"].ToString());
        }
        stringList.Sort();
        this.newComboBox_Company.Items.AddRange((object[]) stringList.ToArray());
        this.newComboBox_Company.SelectedIndex = this.newComboBox_Company.Items.IndexOf((object) p_strCompany);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]RefreshDB):" + ex.Message));
      }
    }

    public void RefreshItemUI(string p_strCompany = null)
    {
      this.newComboBox_Item.Items.Clear();
      if (p_strCompany == null)
        p_strCompany = LocaleControl.getInstance().GetString("IDS_ALL");
      try
      {
        this.newComboBox_Item.Items.Add((object) LocaleControl.getInstance().GetString("IDS_ALL"));
        DataRow[] array = clsDefine.g_dtRunnerDB.AsEnumerable().ToArray<DataRow>();
        if (p_strCompany != LocaleControl.getInstance().GetString("IDS_ALL"))
          array = ((IEnumerable<DataRow>) array).Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Company"].ToString() == p_strCompany)).ToArray<DataRow>();
        if (array.Length != 0)
        {
          List<string> stringList = new List<string>();
          foreach (DataRow dataRow in array)
          {
            if (!stringList.Contains(dataRow["Item"].ToString()))
              stringList.Add(dataRow["Item"].ToString());
          }
          stringList.Sort();
          this.newComboBox_Item.Items.AddRange((object[]) stringList.ToArray());
          this.newComboBox_Item.SelectedIndex = 0;
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunnerDB]RefreshItemUI):" + ex.Message));
      }
      if (this.newComboBox_Item.Items.Count == 1)
      {
        this.newComboBox_Item.Items.Clear();
        this.newComboBox_Item.Enabled = false;
      }
      else
        this.newComboBox_Item.Enabled = true;
    }

    private void listBox_DB_SelectedIndexChanged(object sender, EventArgs e)
    {
      try
      {
        DataRow dataRow = clsDefine.g_dtRunnerDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == this.listBox_DB.Text)).FirstOrDefault<DataRow>();
        if (dataRow == null)
          return;
        this.newTextBox_Company.Value = dataRow["Company"].ToString();
        this.newTextBox_Item.Value = dataRow["Item"].ToString();
        this.newTextBox_Option.Value = dataRow["Option"].ToString();
        if (dataRow["Pin_System"].ToString() == "0")
          this.radioButton_Pin_ColdSystem.Checked = true;
        else
          this.radioButton_Pin_HotSystem.Checked = true;
        for (int index = 0; index < this.tabControl_Pin_Group.TabCount; ++index)
        {
          ((NewTextBox) this.tabControl_Pin_Group.TabPages[index].Controls.Find("newTextBox_Pin_GateDim1_G" + (object) (index + 1), true)[0]).Value = dataRow["Pin_GateDim1_G" + (object) (index + 1)].ToString();
          ((NewTextBox) this.tabControl_Pin_Group.TabPages[index].Controls.Find("newTextBox_Pin_GateDim2_G" + (object) (index + 1), true)[0]).Value = dataRow["Pin_GateDim2_G" + (object) (index + 1)].ToString();
          ((NewTextBox) this.tabControl_Pin_Group.TabPages[index].Controls.Find("newTextBox_Pin_RunnerLength_G" + (object) (index + 1), true)[0]).Value = dataRow["Pin_RunnerLength_G" + (object) (index + 1)].ToString();
          ((NewTextBox) this.tabControl_Pin_Group.TabPages[index].Controls.Find("newTextBox_Pin_RunnerDim1_G" + (object) (index + 1), true)[0]).Value = dataRow["Pin_RunnerDim1_G" + (object) (index + 1)].ToString();
          ((NewTextBox) this.tabControl_Pin_Group.TabPages[index].Controls.Find("newTextBox_Pin_RunnerDim2_G" + (object) (index + 1), true)[0]).Value = dataRow["Pin_RunnerDim2_G" + (object) (index + 1)].ToString();
        }
        for (int index = 0; index < this.tabControl_Side_Group.TabCount; ++index)
        {
          if (dataRow["Side_GateType1_G" + (object) (index + 1)].ToString() == "0")
            ((RadioButton) this.tabControl_Side_Group.TabPages[index].Controls.Find("radioButton_Side_GateRect_G" + (object) (index + 1), true)[0]).Checked = true;
          else
            ((RadioButton) this.tabControl_Side_Group.TabPages[index].Controls.Find("radioButton_Side_GateCircle_G" + (object) (index + 1), true)[0]).Checked = true;
          if (dataRow["Side_GateType2_G" + (object) (index + 1)].ToString() == "0")
            ((RadioButton) this.tabControl_Side_Group.TabPages[index].Controls.Find("radioButton_Side_GateNormal_G" + (object) (index + 1), true)[0]).Checked = true;
          else
            ((RadioButton) this.tabControl_Side_Group.TabPages[index].Controls.Find("radioButton_Side_GateSubMarine_G" + (object) (index + 1), true)[0]).Checked = true;
          ((NewTextBox) this.tabControl_Side_Group.TabPages[index].Controls.Find("newTextBox_Side_GateLength_G" + (object) (index + 1), true)[0]).Value = dataRow["Side_GateLength_G" + (object) (index + 1)].ToString();
          ((NewTextBox) this.tabControl_Side_Group.TabPages[index].Controls.Find("newTextBox_Side_GateDim1_G" + (object) (index + 1), true)[0]).Value = dataRow["Side_GateDim1_G" + (object) (index + 1)].ToString();
          ((NewTextBox) this.tabControl_Side_Group.TabPages[index].Controls.Find("newTextBox_Side_GateDim2_G" + (object) (index + 1), true)[0]).Value = dataRow["Side_GateDim2_G" + (object) (index + 1)].ToString();
          ((NewTextBox) this.tabControl_Side_Group.TabPages[index].Controls.Find("newTextBox_Side_GateDim3_G" + (object) (index + 1), true)[0]).Value = dataRow["Side_GateDim3_G" + (object) (index + 1)].ToString();
          ((NewTextBox) this.tabControl_Side_Group.TabPages[index].Controls.Find("newTextBox_Side_GateDim4_G" + (object) (index + 1), true)[0]).Value = dataRow["Side_GateDim4_G" + (object) (index + 1)].ToString();
          switch (dataRow["Side_RunnerType_G" + (object) (index + 1)].ToString())
          {
            case "0":
              ((RadioButton) this.tabControl_Side_Group.TabPages[index].Controls.Find("radioButton_Side_RunnerTrepezoidal_G" + (object) (index + 1), true)[0]).Checked = true;
              break;
            case "1":
              ((RadioButton) this.tabControl_Side_Group.TabPages[index].Controls.Find("radioButton_Side_RunnerCircle_G" + (object) (index + 1), true)[0]).Checked = true;
              break;
            default:
              ((RadioButton) this.tabControl_Side_Group.TabPages[index].Controls.Find("radioButton_Side_RunnerRectangle_G" + (object) (index + 1), true)[0]).Checked = true;
              break;
          }
          ((NewTextBox) this.tabControl_Side_Group.TabPages[index].Controls.Find("newTextBox_Side_RunnerLength_G" + (object) (index + 1), true)[0]).Value = dataRow["Side_RunnerLength_G" + (object) (index + 1)].ToString();
          ((NewTextBox) this.tabControl_Side_Group.TabPages[index].Controls.Find("newTextBox_Side_RunnerDim1_G" + (object) (index + 1), true)[0]).Value = dataRow["Side_RunnerDim1_G" + (object) (index + 1)].ToString();
          ((NewTextBox) this.tabControl_Side_Group.TabPages[index].Controls.Find("newTextBox_Side_RunnerDim2_G" + (object) (index + 1), true)[0]).Value = dataRow["Side_RunnerDim2_G" + (object) (index + 1)].ToString();
          ((NewTextBox) this.tabControl_Side_Group.TabPages[index].Controls.Find("newTextBox_Side_RunnerDim3_G" + (object) (index + 1), true)[0]).Value = dataRow["Side_RunnerDim3_G" + (object) (index + 1)].ToString();
          ((NewTextBox) this.tabControl_Side_Group.TabPages[index].Controls.Find("newTextBox_Side_RunnerDim4_G" + (object) (index + 1), true)[0]).Value = dataRow["Side_RunnerDim4_G" + (object) (index + 1)].ToString();
        }
        this.newTextBox_Cavity_Occ.Value = dataRow["OccNumber"].ToString();
        this.tabControl_Hor_RunnerType.SelectedIndex = clsUtill.ConvertToInt(dataRow["Hor_Stage"].ToString());
        switch (dataRow["Hor_Two_Type"].ToString())
        {
          case "0":
            this.radioButton_Hor_Two_Type1.Checked = true;
            break;
          case "1":
            this.radioButton_Hor_Two_Type2.Checked = true;
            break;
          case "2":
            this.radioButton_Hor_Two_Type3.Checked = true;
            break;
          default:
            this.radioButton_Hor_Two_Type4.Checked = true;
            break;
        }
        this.newTextBox_Hor_Two_Dia.Value = dataRow["Hor_Two_Dia"].ToString();
        this.newTextBox_Hor_Two_Length.Value = dataRow["Hor_Two_Length"].ToString();
        switch (dataRow["Hor_Two_Dir"].ToString())
        {
          case "0":
            this.newComboBox_Hor_Two_Direction.SelectedIndex = 0;
            break;
          case "1":
            this.newComboBox_Hor_Two_Direction.SelectedIndex = 1;
            break;
          case "2":
            this.newComboBox_Hor_Two_Direction.SelectedIndex = 2;
            break;
          case "3":
            this.newComboBox_Hor_Two_Direction.SelectedIndex = 3;
            break;
          default:
            this.newComboBox_Hor_Two_Direction.SelectedIndex = 4;
            break;
        }
        this.newTextBox_Hor_Two_CenterTol.Value = dataRow["Hor_Two_CenterTol"].ToString();
        this.newTextBox_Hor_Two_Dim1.Value = dataRow["Hor_Two_Dim1"].ToString();
        this.newTextBox_Hor_Two_Dim2.Value = dataRow["Hor_Two_Dim2"].ToString();
        this.newTextBox_Hor_Two_Angle.Value = dataRow["Hor_Two_Angle"].ToString();
        this.checkBox_Hor_Two_Inter.Checked = !dataRow["Hor_Two_Inter"].ToString().ToLower().Equals("false");
        switch (dataRow["Hor_Three_Quadrant"].ToString())
        {
          case "0":
            this.radioButton_Quadrant2.Checked = true;
            break;
          case "1":
            this.radioButton_Quadrant4.Checked = true;
            break;
        }
        switch (dataRow["Hor_Three_Type"].ToString())
        {
          case "0":
            this.radioButton_Hor_Three_Type1.Checked = true;
            break;
          case "1":
            this.radioButton_Hor_Three_Type2.Checked = true;
            break;
        }
        this.newTextBox_Hor_Three_Length.Value = dataRow["Hor_Three_Length"].ToString();
        this.newTextBox_Hor_Three_Dia.Value = dataRow["Hor_Three_Dia"].ToString();
        this.newTextBox_Hor_Three_CenterTol.Value = dataRow["Hor_Three_CenterTol"].ToString();
        if (dataRow["Sprue_Type"].ToString() == "0")
          this.radioButton_Sprue_Cold.Checked = true;
        else
          this.radioButton_Sprue_Hot.Checked = true;
        this.newTextBox_Sprue_Length.Value = dataRow["Sprue_Length"].ToString();
        this.newTextBox_Sprue_Dim1.Value = dataRow["Sprue_Dim1"].ToString();
        this.newTextBox_Sprue_Dim2.Value = dataRow["Sprue_Dim2"].ToString();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunnerDB]listBox_DB_SelectedIndexChanged):" + ex.Message));
      }
    }

    private void newComboBox_Company_SelectedIndexChanged(object sender, EventArgs e)
    {
      this.RefreshDBList(this.newComboBox_Company.Value);
      this.RefreshItemUI(this.newComboBox_Company.Value);
    }

    private void newComboBox_Item_SelectedIndexChanged(object sender, EventArgs e) => this.RefreshDBList(this.newComboBox_Company.Value, this.newComboBox_Item.Value);

    private void AddDB()
    {
      if (this.newTextBox_Company.Value == "" || this.newTextBox_Item.Value == "" || this.newTextBox_Option.Value == "")
        return;
      try
      {
        if (((IEnumerable<DataRow>) clsDefine.g_dtRunnerDB.AsEnumerable().ToArray<DataRow>()).Any<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Company"].ToString() == this.newTextBox_Company.Value && Temp["Item"].ToString() == this.newTextBox_Item.Value && Temp["Option"].ToString() == this.newTextBox_Option.Value)))
          return;
        DataRow p_drRunnerDB = clsDefine.g_dtRunnerDB.Rows.Add();
        p_drRunnerDB["Name"] = (object) (this.newTextBox_Company.Value + "_" + this.newTextBox_Item.Value + "_" + this.newTextBox_Option.Value);
        p_drRunnerDB["Company"] = (object) this.newTextBox_Company.Value;
        p_drRunnerDB["Item"] = (object) this.newTextBox_Item.Value;
        p_drRunnerDB["Option"] = (object) this.newTextBox_Option.Value;
        this.UpdateDB(p_drRunnerDB);
        this.CreateDBFile(p_drRunnerDB);
        this.RefreshDBList();
        this.RefreshCompanyUI();
        this.RefreshItemUI();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunnerDB]AddDB):" + ex.Message));
      }
    }

    private void EditDB()
    {
      if (this.listBox_DB.SelectedIndex == -1)
        return;
      try
      {
        string strRunnerDB = this.listBox_DB.Text;
        string p_strCompany = this.newComboBox_Company.Value;
        string p_strItem = this.newComboBox_Item.Value;
        DataRow p_drRunnerDB = clsDefine.g_dtRunnerDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == strRunnerDB)).FirstOrDefault<DataRow>();
        if (p_drRunnerDB == null)
          return;
        this.UpdateDB(p_drRunnerDB);
        FileInfo fileInfo = new FileInfo(clsDefine.g_diRunnerDBCfg.ToString() + "\\" + p_drRunnerDB["Name"] + ".xml");
        if (fileInfo.Exists)
          fileInfo.Delete();
        p_drRunnerDB["Name"] = (object) (this.newTextBox_Company.Value + "_" + this.newTextBox_Item.Value + "_" + this.newTextBox_Option.Value);
        p_drRunnerDB["Company"] = (object) this.newTextBox_Company.Value;
        p_drRunnerDB["Item"] = (object) this.newTextBox_Item.Value;
        p_drRunnerDB["Option"] = (object) this.newTextBox_Option.Value;
        this.CreateDBFile(p_drRunnerDB);
        this.RefreshDBList(p_strCompany, p_strItem);
        this.RefreshCompanyUI(p_strCompany);
        this.RefreshItemUI(p_strCompany);
        this.listBox_DB.SelectedIndex = this.listBox_DB.Items.IndexOf((object) strRunnerDB);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunnerDB]EditDB):" + ex.Message));
      }
    }

    private void DeleteDB()
    {
      bool flag = false;
      if (this.listBox_DB.SelectedItems.Count == 0)
        return;
      try
      {
        if (clsUtill.ShowMessageBox((Form) this, LocaleControl.getInstance().GetString("IDS_WANT_DELETE"), this.Text, MessageBoxButtons.YesNo, MessageBoxIcon.Asterisk) != DialogResult.Yes)
          return;
        foreach (string selectedItem in this.listBox_DB.SelectedItems)
        {
          string strRunnerDB = selectedItem;
          DataRow row = clsDefine.g_dtRunnerDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == strRunnerDB)).FirstOrDefault<DataRow>();
          if (row != null)
          {
            FileInfo fileInfo = new FileInfo(clsDefine.g_diRunnerDBCfg.ToString() + "\\" + row["Name"] + ".xml");
            if (fileInfo.Exists)
              fileInfo.Delete();
            clsDefine.g_dtRunnerDB.Rows.Remove(row);
            flag = true;
          }
        }
        if (flag)
        {
          this.RefreshDBList();
          this.RefreshCompanyUI();
          this.RefreshItemUI();
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunnerDB]DeleteDB):" + ex.Message));
      }
    }

    private void ImportDB()
    {
      string strRunnerDB = "";
      bool flag = false;
      try
      {
        OpenFileDialog openFileDialog = new OpenFileDialog();
        openFileDialog.Filter = "DB Files(*.xml)|*.xml";
        openFileDialog.Multiselect = true;
        if (openFileDialog.ShowDialog((IWin32Window) this) != DialogResult.OK)
          return;
        foreach (string fileName in openFileDialog.FileNames)
        {
          strRunnerDB = Path.GetFileNameWithoutExtension(fileName);
          if (!clsDefine.g_dtRunnerDB.AsEnumerable().Any<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == strRunnerDB)))
          {
            if (strRunnerDB.Split('_').Length == 3)
            {
              FileInfo fileInfo = new FileInfo(clsDefine.g_diRunnerDBCfg.ToString() + "\\" + strRunnerDB + ".xml");
              File.Copy(fileName, fileInfo.FullName, true);
              flag = true;
            }
          }
        }
        if (flag)
        {
          clsData.LoadRunnerDB();
          this.RefreshDBList();
          this.RefreshCompanyUI();
          this.RefreshItemUI();
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunnerDB]ImportDB):" + ex.Message));
      }
    }

    private void ExportDB()
    {
      if (this.listBox_DB.SelectedItems.Count == 0)
        return;
      try
      {
        CommonOpenFileDialog commonOpenFileDialog = new CommonOpenFileDialog()
        {
          IsFolderPicker = true
        };
        if (commonOpenFileDialog.ShowDialog() != CommonFileDialogResult.Ok)
          return;
        foreach (string selectedItem in this.listBox_DB.SelectedItems)
        {
          string strRunnerDB = selectedItem;
          DataRow dataRow = clsDefine.g_dtRunnerDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == strRunnerDB)).FirstOrDefault<DataRow>();
          if (dataRow != null)
          {
            FileInfo fileInfo = new FileInfo(clsDefine.g_diRunnerDBCfg.ToString() + "\\" + dataRow["Name"].ToString() + ".xml");
            if (fileInfo.Exists)
              fileInfo.CopyTo(commonOpenFileDialog.FileName + "\\" + fileInfo.Name, true);
          }
        }
        Process.Start(commonOpenFileDialog.FileName);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunnerDB]ExportDB):" + ex.Message));
      }
    }

    private void UpdateDB(DataRow p_drRunnerDB)
    {
      try
      {
        p_drRunnerDB["Pin_System"] = !this.radioButton_Pin_ColdSystem.Checked ? (object) "1" : (object) "0";
        for (int index = 0; index < this.tabControl_Pin_Group.TabCount; ++index)
        {
          p_drRunnerDB["Pin_GateDim1_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Pin_Group.TabPages[index].Controls.Find("newTextBox_Pin_GateDim1_G" + (object) (index + 1), true)[0]).Value;
          p_drRunnerDB["Pin_GateDim2_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Pin_Group.TabPages[index].Controls.Find("newTextBox_Pin_GateDim2_G" + (object) (index + 1), true)[0]).Value;
          p_drRunnerDB["Pin_RunnerLength_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Pin_Group.TabPages[index].Controls.Find("newTextBox_Pin_RunnerLength_G" + (object) (index + 1), true)[0]).Value;
          p_drRunnerDB["Pin_RunnerDim1_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Pin_Group.TabPages[index].Controls.Find("newTextBox_Pin_RunnerDim1_G" + (object) (index + 1), true)[0]).Value;
          p_drRunnerDB["Pin_RunnerDim2_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Pin_Group.TabPages[index].Controls.Find("newTextBox_Pin_RunnerDim2_G" + (object) (index + 1), true)[0]).Value;
        }
        for (int index = 0; index < this.tabControl_Side_Group.TabCount; ++index)
        {
          if (((RadioButton) this.tabControl_Side_Group.TabPages[index].Controls.Find("radioButton_Side_GateRect_G" + (object) (index + 1), true)[0]).Checked)
            p_drRunnerDB["Side_GateType1_G" + (object) (index + 1)] = (object) "0";
          else
            p_drRunnerDB["Side_GateType1_G" + (object) (index + 1)] = (object) "1";
          if (((RadioButton) this.tabControl_Side_Group.TabPages[index].Controls.Find("radioButton_Side_GateNormal_G" + (object) (index + 1), true)[0]).Checked)
            p_drRunnerDB["Side_GateType2_G" + (object) (index + 1)] = (object) "0";
          else
            p_drRunnerDB["Side_GateType2_G" + (object) (index + 1)] = (object) "1";
          p_drRunnerDB["Side_GateLength_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Side_Group.TabPages[index].Controls.Find("newTextBox_Side_GateLength_G" + (object) (index + 1), true)[0]).Value;
          p_drRunnerDB["Side_GateDim1_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Side_Group.TabPages[index].Controls.Find("newTextBox_Side_GateDim1_G" + (object) (index + 1), true)[0]).Value;
          p_drRunnerDB["Side_GateDim2_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Side_Group.TabPages[index].Controls.Find("newTextBox_Side_GateDim2_G" + (object) (index + 1), true)[0]).Value;
          p_drRunnerDB["Side_GateDim3_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Side_Group.TabPages[index].Controls.Find("newTextBox_Side_GateDim3_G" + (object) (index + 1), true)[0]).Value;
          p_drRunnerDB["Side_GateDim4_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Side_Group.TabPages[index].Controls.Find("newTextBox_Side_GateDim4_G" + (object) (index + 1), true)[0]).Value;
          RadioButton radioButton1 = (RadioButton) this.tabControl_Side_Group.TabPages[index].Controls.Find("radioButton_Side_RunnerTrepezoidal_G" + (object) (index + 1), true)[0];
          RadioButton radioButton2 = (RadioButton) this.tabControl_Side_Group.TabPages[index].Controls.Find("radioButton_Side_RunnerCircle_G" + (object) (index + 1), true)[0];
          if (radioButton1.Checked)
            p_drRunnerDB["Side_RunnerType_G" + (object) (index + 1)] = (object) "0";
          else if (radioButton2.Checked)
            p_drRunnerDB["Side_RunnerType_G" + (object) (index + 1)] = (object) "1";
          else
            p_drRunnerDB["Side_RunnerType_G" + (object) (index + 1)] = (object) "2";
          p_drRunnerDB["Side_RunnerLength_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Side_Group.TabPages[index].Controls.Find("newTextBox_Side_RunnerLength_G" + (object) (index + 1), true)[0]).Value;
          p_drRunnerDB["Side_RunnerDim1_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Side_Group.TabPages[index].Controls.Find("newTextBox_Side_RunnerDim1_G" + (object) (index + 1), true)[0]).Value;
          p_drRunnerDB["Side_RunnerDim2_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Side_Group.TabPages[index].Controls.Find("newTextBox_Side_RunnerDim2_G" + (object) (index + 1), true)[0]).Value;
          p_drRunnerDB["Side_RunnerDim3_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Side_Group.TabPages[index].Controls.Find("newTextBox_Side_RunnerDim3_G" + (object) (index + 1), true)[0]).Value;
          p_drRunnerDB["Side_RunnerDim4_G" + (object) (index + 1)] = (object) ((NewTextBox) this.tabControl_Side_Group.TabPages[index].Controls.Find("newTextBox_Side_RunnerDim4_G" + (object) (index + 1), true)[0]).Value;
        }
        p_drRunnerDB["OccNumber"] = (object) this.newTextBox_Cavity_Occ.Value;
        p_drRunnerDB["Hor_Stage"] = (object) this.tabControl_Hor_RunnerType.SelectedIndex;
        p_drRunnerDB["Hor_Two_Type"] = !this.radioButton_Hor_Two_Type1.Checked ? (!this.radioButton_Hor_Two_Type2.Checked ? (!this.radioButton_Hor_Two_Type3.Checked ? (object) "3" : (object) "2") : (object) "1") : (object) "0";
        p_drRunnerDB["Hor_Two_Dia"] = (object) this.newTextBox_Hor_Two_Dia.Value;
        p_drRunnerDB["Hor_Two_Length"] = (object) this.newTextBox_Hor_Two_Length.Value;
        p_drRunnerDB["Hor_Two_Dir"] = (object) this.newComboBox_Hor_Two_Direction.SelectedIndex.ToString();
        p_drRunnerDB["Hor_Two_Dim1"] = (object) this.newTextBox_Hor_Two_Dim1.Value;
        p_drRunnerDB["Hor_Two_Dim2"] = (object) this.newTextBox_Hor_Two_Dim2.Value;
        p_drRunnerDB["Hor_Two_Angle"] = (object) this.newTextBox_Hor_Two_Angle.Value;
        p_drRunnerDB["Hor_Two_CenterTol"] = (object) this.newTextBox_Hor_Two_CenterTol.Value;
        p_drRunnerDB["Hor_Two_Inter"] = (object) this.checkBox_Hor_Two_Inter.Checked;
        p_drRunnerDB["Hor_Three_Quadrant"] = !this.radioButton_Quadrant2.Checked ? (object) "1" : (object) "0";
        p_drRunnerDB["Hor_Three_Type"] = !this.radioButton_Hor_Three_Type1.Checked ? (object) "1" : (object) "0";
        p_drRunnerDB["Hor_Three_Length"] = (object) this.newTextBox_Hor_Three_Length.Value;
        p_drRunnerDB["Hor_Three_Dia"] = (object) this.newTextBox_Hor_Three_Dia.Value;
        p_drRunnerDB["Hor_Three_CenterTol"] = (object) this.newTextBox_Hor_Three_CenterTol.Value;
        p_drRunnerDB["Sprue_Type"] = !this.radioButton_Sprue_Cold.Checked ? (object) "1" : (object) "0";
        p_drRunnerDB["Sprue_Length"] = (object) this.newTextBox_Sprue_Length.Value;
        p_drRunnerDB["Sprue_Dim1"] = (object) this.newTextBox_Sprue_Dim1.Value;
        p_drRunnerDB["Sprue_Dim2"] = (object) this.newTextBox_Sprue_Dim2.Value;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunnerDB]UpdateDB):" + ex.Message));
      }
    }

    private void CreateDBFile(DataRow p_drRunnerDB)
    {
      try
      {
        FileInfo fileInfo = new FileInfo(clsDefine.g_diRunnerDBCfg.ToString() + "\\" + p_drRunnerDB["Name"].ToString() + ".xml");
        if (!fileInfo.Directory.Exists)
          fileInfo.Directory.Create();
        using (XmlWriter xmlWriter = XmlWriter.Create(fileInfo.FullName, new XmlWriterSettings()
        {
          Indent = true,
          OmitXmlDeclaration = true,
          NewLineOnAttributes = true
        }))
        {
          xmlWriter.WriteStartDocument();
          xmlWriter.WriteStartElement("DB");
          xmlWriter.WriteEndElement();
          xmlWriter.Flush();
        }
        using (XmlReader reader = XmlReader.Create(fileInfo.FullName))
        {
          XmlDocument xmlDocument = new XmlDocument();
          xmlDocument.Load(reader);
          XmlElement documentElement = xmlDocument.DocumentElement;
          documentElement.RemoveAll();
          XmlNode element1 = (XmlNode) xmlDocument.CreateElement("Pin");
          XmlNode element2 = (XmlNode) xmlDocument.CreateElement("SysType");
          element2.InnerText = p_drRunnerDB["Pin_System"].ToString();
          element1.AppendChild(element2);
          for (int index = 0; index < 4; ++index)
          {
            XmlNode element3 = (XmlNode) xmlDocument.CreateElement("Group" + (object) (index + 1));
            XmlNode element4 = (XmlNode) xmlDocument.CreateElement("Gate");
            XmlAttribute attribute1 = xmlDocument.CreateAttribute("Dim1");
            attribute1.Value = p_drRunnerDB["Pin_GateDim1_G" + (object) (index + 1)].ToString();
            element4.Attributes.Append(attribute1);
            XmlAttribute attribute2 = xmlDocument.CreateAttribute("Dim2");
            attribute2.Value = p_drRunnerDB["Pin_GateDim2_G" + (object) (index + 1)].ToString();
            element4.Attributes.Append(attribute2);
            element3.AppendChild(element4);
            XmlNode element5 = (XmlNode) xmlDocument.CreateElement("Runner");
            XmlAttribute attribute3 = xmlDocument.CreateAttribute("Length");
            attribute3.Value = p_drRunnerDB["Pin_RunnerLength_G" + (object) (index + 1)].ToString();
            element5.Attributes.Append(attribute3);
            XmlAttribute attribute4 = xmlDocument.CreateAttribute("Dim1");
            attribute4.Value = p_drRunnerDB["Pin_RunnerDim1_G" + (object) (index + 1)].ToString();
            element5.Attributes.Append(attribute4);
            XmlAttribute attribute5 = xmlDocument.CreateAttribute("Dim2");
            attribute5.Value = p_drRunnerDB["Pin_RunnerDim2_G" + (object) (index + 1)].ToString();
            element5.Attributes.Append(attribute5);
            element3.AppendChild(element5);
            element1.AppendChild(element3);
          }
          documentElement.AppendChild(element1);
          XmlNode element6 = (XmlNode) xmlDocument.CreateElement("Side");
          for (int index = 0; index < 4; ++index)
          {
            XmlNode element7 = (XmlNode) xmlDocument.CreateElement("Group" + (object) (index + 1));
            XmlNode element8 = (XmlNode) xmlDocument.CreateElement("Gate");
            XmlAttribute attribute6 = xmlDocument.CreateAttribute("Type1");
            attribute6.Value = p_drRunnerDB["Side_GateType1_G" + (object) (index + 1)].ToString();
            element8.Attributes.Append(attribute6);
            XmlAttribute attribute7 = xmlDocument.CreateAttribute("Type2");
            attribute7.Value = p_drRunnerDB["Side_GateType2_G" + (object) (index + 1)].ToString();
            element8.Attributes.Append(attribute7);
            XmlAttribute attribute8 = xmlDocument.CreateAttribute("Length");
            attribute8.Value = p_drRunnerDB["Side_GateLength_G" + (object) (index + 1)].ToString();
            element8.Attributes.Append(attribute8);
            XmlAttribute attribute9 = xmlDocument.CreateAttribute("Dim1");
            attribute9.Value = p_drRunnerDB["Side_GateDim1_G" + (object) (index + 1)].ToString();
            element8.Attributes.Append(attribute9);
            XmlAttribute attribute10 = xmlDocument.CreateAttribute("Dim2");
            attribute10.Value = p_drRunnerDB["Side_GateDim2_G" + (object) (index + 1)].ToString();
            element8.Attributes.Append(attribute10);
            XmlAttribute attribute11 = xmlDocument.CreateAttribute("Dim3");
            attribute11.Value = p_drRunnerDB["Side_GateDim3_G" + (object) (index + 1)].ToString();
            element8.Attributes.Append(attribute11);
            XmlAttribute attribute12 = xmlDocument.CreateAttribute("Dim4");
            attribute12.Value = p_drRunnerDB["Side_GateDim4_G" + (object) (index + 1)].ToString();
            element8.Attributes.Append(attribute12);
            element7.AppendChild(element8);
            XmlNode element9 = (XmlNode) xmlDocument.CreateElement("Runner");
            XmlAttribute attribute13 = xmlDocument.CreateAttribute("Type");
            attribute13.Value = p_drRunnerDB["Side_RunnerType_G" + (object) (index + 1)].ToString();
            element9.Attributes.Append(attribute13);
            XmlAttribute attribute14 = xmlDocument.CreateAttribute("Length");
            attribute14.Value = p_drRunnerDB["Side_RunnerLength_G" + (object) (index + 1)].ToString();
            element9.Attributes.Append(attribute14);
            XmlAttribute attribute15 = xmlDocument.CreateAttribute("Dim1");
            attribute15.Value = p_drRunnerDB["Side_RunnerDim1_G" + (object) (index + 1)].ToString();
            element9.Attributes.Append(attribute15);
            XmlAttribute attribute16 = xmlDocument.CreateAttribute("Dim2");
            attribute16.Value = p_drRunnerDB["Side_RunnerDim2_G" + (object) (index + 1)].ToString();
            element9.Attributes.Append(attribute16);
            XmlAttribute attribute17 = xmlDocument.CreateAttribute("Dim3");
            attribute17.Value = p_drRunnerDB["Side_RunnerDim3_G" + (object) (index + 1)].ToString();
            element9.Attributes.Append(attribute17);
            XmlAttribute attribute18 = xmlDocument.CreateAttribute("Dim4");
            attribute18.Value = p_drRunnerDB["Side_RunnerDim4_G" + (object) (index + 1)].ToString();
            element9.Attributes.Append(attribute18);
            element7.AppendChild(element9);
            element6.AppendChild(element7);
          }
          documentElement.AppendChild(element6);
          XmlNode element10 = (XmlNode) xmlDocument.CreateElement("OccurenceNumber");
          element10.InnerText = p_drRunnerDB["OccNumber"].ToString();
          documentElement.AppendChild(element10);
          XmlNode element11 = (XmlNode) xmlDocument.CreateElement("Horizon");
          XmlAttribute attribute19 = xmlDocument.CreateAttribute("Stage");
          attribute19.Value = p_drRunnerDB["Hor_Stage"].ToString();
          element11.Attributes.Append(attribute19);
          XmlNode element12 = (XmlNode) xmlDocument.CreateElement("Two");
          XmlAttribute attribute20 = xmlDocument.CreateAttribute("Type");
          attribute20.Value = p_drRunnerDB["Hor_Two_Type"].ToString();
          element12.Attributes.Append(attribute20);
          XmlAttribute attribute21 = xmlDocument.CreateAttribute("Dia");
          attribute21.Value = p_drRunnerDB["Hor_Two_Dia"].ToString();
          element12.Attributes.Append(attribute21);
          XmlAttribute attribute22 = xmlDocument.CreateAttribute("Length");
          attribute22.Value = p_drRunnerDB["Hor_Two_Length"].ToString();
          element12.Attributes.Append(attribute22);
          XmlAttribute attribute23 = xmlDocument.CreateAttribute("Dir");
          attribute23.Value = p_drRunnerDB["Hor_Two_Dir"].ToString();
          element12.Attributes.Append(attribute23);
          XmlAttribute attribute24 = xmlDocument.CreateAttribute("Dim1");
          attribute24.Value = p_drRunnerDB["Hor_Two_Dim1"].ToString();
          element12.Attributes.Append(attribute24);
          XmlAttribute attribute25 = xmlDocument.CreateAttribute("Dim2");
          attribute25.Value = p_drRunnerDB["Hor_Two_Dim2"].ToString();
          element12.Attributes.Append(attribute25);
          XmlAttribute attribute26 = xmlDocument.CreateAttribute("Angle");
          attribute26.Value = p_drRunnerDB["Hor_Two_Angle"].ToString();
          element12.Attributes.Append(attribute26);
          XmlAttribute attribute27 = xmlDocument.CreateAttribute("CenterTol");
          attribute27.Value = p_drRunnerDB["Hor_Two_CenterTol"].ToString();
          element12.Attributes.Append(attribute27);
          XmlAttribute attribute28 = xmlDocument.CreateAttribute("Inter");
          attribute28.Value = p_drRunnerDB["Hor_Two_Inter"].ToString();
          element12.Attributes.Append(attribute28);
          element11.AppendChild(element12);
          XmlNode element13 = (XmlNode) xmlDocument.CreateElement("Three");
          XmlAttribute attribute29 = xmlDocument.CreateAttribute("Quadrant");
          attribute29.Value = p_drRunnerDB["Hor_Three_Quadrant"].ToString();
          element13.Attributes.Append(attribute29);
          XmlAttribute attribute30 = xmlDocument.CreateAttribute("Type");
          attribute30.Value = p_drRunnerDB["Hor_Three_Type"].ToString();
          element13.Attributes.Append(attribute30);
          XmlAttribute attribute31 = xmlDocument.CreateAttribute("Length");
          attribute31.Value = p_drRunnerDB["Hor_Three_Length"].ToString();
          element13.Attributes.Append(attribute31);
          XmlAttribute attribute32 = xmlDocument.CreateAttribute("Dia");
          attribute32.Value = p_drRunnerDB["Hor_Three_Dia"].ToString();
          element13.Attributes.Append(attribute32);
          XmlAttribute attribute33 = xmlDocument.CreateAttribute("CenterTol");
          attribute33.Value = p_drRunnerDB["Hor_Three_CenterTol"].ToString();
          element13.Attributes.Append(attribute33);
          element11.AppendChild(element13);
          documentElement.AppendChild(element11);
          XmlNode element14 = (XmlNode) xmlDocument.CreateElement("Sprue");
          XmlAttribute attribute34 = xmlDocument.CreateAttribute("Type");
          attribute34.Value = p_drRunnerDB["Sprue_Type"].ToString();
          element14.Attributes.Append(attribute34);
          XmlAttribute attribute35 = xmlDocument.CreateAttribute("Length");
          attribute35.Value = p_drRunnerDB["Sprue_Length"].ToString();
          element14.Attributes.Append(attribute35);
          XmlAttribute attribute36 = xmlDocument.CreateAttribute("Dim1");
          attribute36.Value = p_drRunnerDB["Sprue_Dim1"].ToString();
          element14.Attributes.Append(attribute36);
          XmlAttribute attribute37 = xmlDocument.CreateAttribute("Dim2");
          attribute37.Value = p_drRunnerDB["Sprue_Dim2"].ToString();
          element14.Attributes.Append(attribute37);
          documentElement.AppendChild(element14);
          reader.Close();
          xmlDocument.Save(fileInfo.FullName);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunnerDB]CreateDBFile):" + ex.Message));
      }
    }

    private void frmRunnerDB_KeyDown(object sender, KeyEventArgs e)
    {
      if (e.KeyCode != Keys.Escape)
        return;
      this.Close();
    }

    private void radioButton_Pin_ColdSystem_CheckedChanged(object sender, EventArgs e)
    {
      this.label_Pin_RunnerDim1_G1.Text = "Start Dimension";
      this.label_Pin_RunnerDim2_G1.Text = "End Dimension";
      this.label_Pin_RunnerDim1_G2.Text = "Start Dimension";
      this.label_Pin_RunnerDim2_G2.Text = "End Dimension";
      this.label_Pin_RunnerDim1_G3.Text = "Start Dimension";
      this.label_Pin_RunnerDim2_G3.Text = "End Dimension";
      this.label_Pin_RunnerDim1_G4.Text = "Start Dimension";
      this.label_Pin_RunnerDim2_G4.Text = "End Dimension";
    }

    private void radioButton_Pin_HotSystem_CheckedChanged(object sender, EventArgs e)
    {
      this.label_Pin_RunnerDim1_G1.Text = "Inner Dimension";
      this.label_Pin_RunnerDim2_G1.Text = "Outer Dimension";
      this.label_Pin_RunnerDim1_G2.Text = "Inner Dimension";
      this.label_Pin_RunnerDim2_G2.Text = "Outer Dimension";
      this.label_Pin_RunnerDim1_G3.Text = "Inner Dimension";
      this.label_Pin_RunnerDim2_G3.Text = "Outer Dimension";
      this.label_Pin_RunnerDim1_G4.Text = "Inner Dimension";
      this.label_Pin_RunnerDim2_G4.Text = "Outer Dimension";
    }

    private void radioButton_Side_GateRect_CheckedChanged(object sender, EventArgs e)
    {
      int num = clsUtill.ConvertToInt(Regex.Match((sender as RadioButton).Name, "_G(\\d)").Groups[1].Value);
      try
      {
        this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("label_Side_GateDim1_G" + (object) num, true)[0].Text = "Start Width";
        this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("label_Side_GateDim2_G" + (object) num, true)[0].Text = "Start Height";
        Label label1 = (Label) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("label_Side_GateDim3_G" + (object) num, true)[0];
        label1.Text = "End Width";
        label1.BackColor = Color.Lavender;
        Label label2 = (Label) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("label_Side_GateDim4_G" + (object) num, true)[0];
        label2.Text = "End Height";
        label2.BackColor = Color.Lavender;
        NewTextBox newTextBox1 = (NewTextBox) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_GateDim3_G" + (object) num, true)[0];
        newTextBox1.TextBoxBackColor = Color.White;
        newTextBox1.TextForeColor = Color.Black;
        newTextBox1.ReadOnly = false;
        if (newTextBox1.Value == "")
          newTextBox1.Value = "0";
        NewTextBox newTextBox2 = (NewTextBox) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_GateDim4_G" + (object) num, true)[0];
        newTextBox2.TextBoxBackColor = Color.White;
        newTextBox2.TextForeColor = Color.Black;
        newTextBox2.ReadOnly = false;
        if (newTextBox2.Value == "")
          newTextBox2.Value = "0";
        Panel panel = (Panel) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("panel_Side_GateType2_G" + (object) num, true)[0];
        RadioButton radioButton = (RadioButton) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("radioButton_Side_GateNormal_G" + (object) num, true)[0];
        panel.Enabled = false;
        radioButton.Checked = true;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunnerDB]radioButton_Side_GateRect_CheckedChanged):" + ex.Message));
      }
    }

    private void radioButton_Side_GateCircle_CheckedChanged(object sender, EventArgs e)
    {
      int num = clsUtill.ConvertToInt(Regex.Match((sender as RadioButton).Name, "_G(\\d)").Groups[1].Value);
      try
      {
        this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("label_Side_GateDim1_G" + (object) num, true)[0].Text = "Start Dimension";
        this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("label_Side_GateDim2_G" + (object) num, true)[0].Text = "End Dimension";
        Label label1 = (Label) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("label_Side_GateDim3_G" + (object) num, true)[0];
        label1.Text = "";
        label1.BackColor = Color.LightGray;
        Label label2 = (Label) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("label_Side_GateDim4_G" + (object) num, true)[0];
        label2.Text = "";
        label2.BackColor = Color.LightGray;
        NewTextBox newTextBox1 = (NewTextBox) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_GateDim3_G" + (object) num, true)[0];
        newTextBox1.TextBoxBackColor = Color.LightGray;
        newTextBox1.TextForeColor = Color.LightGray;
        newTextBox1.ReadOnly = true;
        NewTextBox newTextBox2 = (NewTextBox) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_GateDim4_G" + (object) num, true)[0];
        newTextBox2.TextBoxBackColor = Color.LightGray;
        newTextBox2.TextForeColor = Color.LightGray;
        newTextBox2.ReadOnly = true;
        Panel panel = (Panel) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("panel_Side_GateType2_G" + (object) num, true)[0];
        RadioButton radioButton1 = (RadioButton) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("radioButton_Side_GateNormal_G" + (object) num, true)[0];
        RadioButton radioButton2 = (RadioButton) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("radioButton_Side_GateSubMarine_G" + (object) num, true)[0];
        panel.Enabled = true;
        if (radioButton1.Checked || radioButton2.Checked)
          return;
        radioButton1.Checked = true;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunnerDB]radioButton_Side_GateCircle_CheckedChanged):" + ex.Message));
      }
    }

    private void radioButton_Side_RunnerTrepezoidal_CheckedChanged(object sender, EventArgs e)
    {
      int num = clsUtill.ConvertToInt(Regex.Match((sender as RadioButton).Name, "_G(\\d)").Groups[1].Value);
      try
      {
        Label label1 = (Label) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("label_Side_RunnerDim1_G" + (object) num, true)[0];
        label1.Text = "Top Width";
        label1.BackColor = Color.Lavender;
        Label label2 = (Label) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("label_Side_RunnerDim2_G" + (object) num, true)[0];
        label2.Text = "Bottom Width";
        label2.BackColor = Color.Lavender;
        Label label3 = (Label) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("label_Side_RunnerDim3_G" + (object) num, true)[0];
        label3.Text = "Height";
        label3.BackColor = Color.Lavender;
        Label label4 = (Label) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("label_Side_RunnerDim4_G" + (object) num, true)[0];
        label4.Text = "";
        label4.BackColor = Color.LightGray;
        NewTextBox newTextBox1 = (NewTextBox) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_RunnerDim2_G" + (object) num, true)[0];
        if (newTextBox1.Value == "")
          newTextBox1.Value = "0";
        newTextBox1.ReadOnly = false;
        newTextBox1.TextBoxBackColor = Color.White;
        newTextBox1.TextForeColor = Color.Black;
        NewTextBox newTextBox2 = (NewTextBox) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_RunnerDim3_G" + (object) num, true)[0];
        if (newTextBox2.Value == "")
          newTextBox2.Value = "0";
        newTextBox2.TextBoxBackColor = Color.White;
        newTextBox2.TextForeColor = Color.Black;
        newTextBox2.ReadOnly = false;
        NewTextBox newTextBox3 = (NewTextBox) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_RunnerDim4_G" + (object) num, true)[0];
        if (newTextBox3.Value == "")
          newTextBox3.Value = "0";
        newTextBox3.TextBoxBackColor = Color.LightGray;
        newTextBox3.TextForeColor = Color.LightGray;
        newTextBox3.ReadOnly = true;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunnerDB]radioButton_Side_RunnerCircle_CheckedChanged):" + ex.Message));
      }
    }

    private void radioButton_Side_RunnerCircle_CheckedChanged(object sender, EventArgs e)
    {
      int num = clsUtill.ConvertToInt(Regex.Match((sender as RadioButton).Name, "_G(\\d)").Groups[1].Value);
      try
      {
        Label label1 = (Label) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("label_Side_RunnerDim1_G" + (object) num, true)[0];
        label1.Text = "Dimension";
        label1.BackColor = Color.Lavender;
        Label label2 = (Label) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("label_Side_RunnerDim2_G" + (object) num, true)[0];
        label2.Text = "";
        label2.BackColor = Color.LightGray;
        Label label3 = (Label) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("label_Side_RunnerDim3_G" + (object) num, true)[0];
        label3.Text = "";
        label3.BackColor = Color.LightGray;
        Label label4 = (Label) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("label_Side_RunnerDim4_G" + (object) num, true)[0];
        label4.Text = "";
        label4.BackColor = Color.LightGray;
        NewTextBox newTextBox1 = (NewTextBox) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_RunnerDim2_G" + (object) num, true)[0];
        newTextBox1.TextBoxBackColor = Color.LightGray;
        newTextBox1.TextForeColor = Color.LightGray;
        newTextBox1.ReadOnly = true;
        NewTextBox newTextBox2 = (NewTextBox) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_RunnerDim3_G" + (object) num, true)[0];
        newTextBox2.TextBoxBackColor = Color.LightGray;
        newTextBox2.TextForeColor = Color.LightGray;
        newTextBox2.ReadOnly = true;
        NewTextBox newTextBox3 = (NewTextBox) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_RunnerDim4_G" + (object) num, true)[0];
        newTextBox3.TextBoxBackColor = Color.LightGray;
        newTextBox3.TextForeColor = Color.LightGray;
        newTextBox3.ReadOnly = true;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunnerDB]radioButton_Side_RunnerCircle_CheckedChanged):" + ex.Message));
      }
    }

    private void radioButton_Side_RunnerRectangle_CheckedChanged(object sender, EventArgs e)
    {
      int num = clsUtill.ConvertToInt(Regex.Match((sender as RadioButton).Name, "_G(\\d)").Groups[1].Value);
      try
      {
        Label label1 = (Label) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("label_Side_RunnerDim1_G" + (object) num, true)[0];
        label1.Text = "Start Width";
        label1.BackColor = Color.Lavender;
        Label label2 = (Label) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("label_Side_RunnerDim2_G" + (object) num, true)[0];
        label2.Text = "Start Height";
        label2.BackColor = Color.Lavender;
        Label label3 = (Label) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("label_Side_RunnerDim3_G" + (object) num, true)[0];
        label3.Text = "End Width";
        label3.BackColor = Color.Lavender;
        Label label4 = (Label) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("label_Side_RunnerDim4_G" + (object) num, true)[0];
        label4.Text = "End Height";
        label4.BackColor = Color.Lavender;
        NewTextBox newTextBox1 = (NewTextBox) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_RunnerDim2_G" + (object) num, true)[0];
        newTextBox1.ReadOnly = false;
        newTextBox1.TextBoxBackColor = Color.White;
        newTextBox1.TextForeColor = Color.Black;
        NewTextBox newTextBox2 = (NewTextBox) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_RunnerDim3_G" + (object) num, true)[0];
        newTextBox2.TextBoxBackColor = Color.White;
        newTextBox2.TextForeColor = Color.Black;
        newTextBox2.ReadOnly = false;
        NewTextBox newTextBox3 = (NewTextBox) this.tabControl_Side_Group.TabPages[num - 1].Controls.Find("newTextBox_Side_RunnerDim4_G" + (object) num, true)[0];
        newTextBox3.TextBoxBackColor = Color.White;
        newTextBox3.TextForeColor = Color.Black;
        newTextBox3.ReadOnly = false;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunnerDB]radioButton_Side_RunnerRectangle_CheckedChanged):" + ex.Message));
      }
    }

    private void newButton_NewClick(object sender, EventArgs e)
    {
      NewButton newButton = sender as NewButton;
      if (newButton == this.newButton_Add)
        this.AddDB();
      else if (newButton == this.newButton_Edit)
        this.EditDB();
      else if (newButton == this.newButton_Del)
        this.DeleteDB();
      else if (newButton == this.newButton_Import)
      {
        this.ImportDB();
      }
      else
      {
        if (newButton != this.newButton_Export)
          return;
        this.ExportDB();
      }
    }

    private void radioButton_Hor_Two_Type3_CheckedChanged(object sender, EventArgs e)
    {
      this.label_Hor_Two_CenterTol.Text = "Center " + LocaleControl.getInstance().GetString("IDS_TOLERANCE");
      this.label_Hor_Two_Dim1.Text = "[ X ] " + LocaleControl.getInstance().GetString("IDS_HORIZON_LENGTH");
      this.label_Hor_Two_Dim2.Text = "[ Y ] " + LocaleControl.getInstance().GetString("IDS_HORIZON_LENGTH");
      this.label_Hor_Two_Angle.Text = "";
      this.label_Hor_Two_CenterTol.BackColor = Color.Lavender;
      this.label_Hor_Two_Dim1.BackColor = Color.Lavender;
      this.label_Hor_Two_Dim2.BackColor = Color.Lavender;
      this.label_Hor_Two_Angle.BackColor = Color.LightGray;
      this.newTextBox_Hor_Two_CenterTol.TextBoxBackColor = Color.White;
      this.newTextBox_Hor_Two_CenterTol.TextForeColor = Color.Black;
      this.newTextBox_Hor_Two_CenterTol.ReadOnly = false;
      this.newTextBox_Hor_Two_Dim1.TextBoxBackColor = Color.White;
      this.newTextBox_Hor_Two_Dim1.TextForeColor = Color.Black;
      this.newTextBox_Hor_Two_Dim1.ReadOnly = false;
      this.newTextBox_Hor_Two_Dim2.TextBoxBackColor = Color.White;
      this.newTextBox_Hor_Two_Dim2.TextForeColor = Color.Black;
      this.newTextBox_Hor_Two_Dim2.ReadOnly = false;
      this.newTextBox_Hor_Two_Angle.TextBoxBackColor = Color.LightGray;
      this.newTextBox_Hor_Two_Angle.TextForeColor = Color.LightGray;
      this.newTextBox_Hor_Two_Angle.ReadOnly = true;
      this.checkBox_Hor_Two_Inter.SendToBack();
      this.checkBox_Hor_Two_Inter.Visible = false;
    }

    private void radioButton_Hor_Two_Type4_CheckedChanged(object sender, EventArgs e)
    {
      this.label_Hor_Two_CenterTol.Text = "";
      this.label_Hor_Two_Dim1.Text = "[／] " + LocaleControl.getInstance().GetString("IDS_HORIZON_LENGTH");
      this.label_Hor_Two_Dim2.Text = "[＼] " + LocaleControl.getInstance().GetString("IDS_HORIZON_LENGTH");
      this.label_Hor_Two_Angle.Text = "∠ " + LocaleControl.getInstance().GetString("IDS_HORIZON_ANGLE");
      this.label_Hor_Two_CenterTol.BackColor = Color.LightGray;
      this.label_Hor_Two_Dim1.BackColor = Color.Lavender;
      this.label_Hor_Two_Dim2.BackColor = Color.Lavender;
      this.label_Hor_Two_Angle.BackColor = Color.Lavender;
      this.newTextBox_Hor_Two_CenterTol.TextBoxBackColor = Color.LightGray;
      this.newTextBox_Hor_Two_CenterTol.TextForeColor = Color.LightGray;
      this.newTextBox_Hor_Two_CenterTol.ReadOnly = true;
      this.newTextBox_Hor_Two_Dim1.TextBoxBackColor = Color.White;
      this.newTextBox_Hor_Two_Dim1.TextForeColor = Color.Black;
      this.newTextBox_Hor_Two_Dim1.ReadOnly = false;
      this.newTextBox_Hor_Two_Dim2.TextBoxBackColor = Color.White;
      this.newTextBox_Hor_Two_Dim2.TextForeColor = Color.Black;
      this.newTextBox_Hor_Two_Dim2.ReadOnly = false;
      this.newTextBox_Hor_Two_Angle.TextBoxBackColor = Color.White;
      this.newTextBox_Hor_Two_Angle.TextForeColor = Color.Black;
      this.newTextBox_Hor_Two_Angle.ReadOnly = false;
      this.checkBox_Hor_Two_Inter.SendToBack();
      this.checkBox_Hor_Two_Inter.Visible = false;
    }

    private void radioButton_Hor_Two_Type_CheckedChanged(object sender, EventArgs e)
    {
      this.label_Hor_Two_CenterTol.Text = "Center " + LocaleControl.getInstance().GetString("IDS_TOLERANCE");
      this.label_Hor_Two_Dim1.Text = "";
      this.label_Hor_Two_Dim2.Text = "";
      this.label_Hor_Two_Angle.Text = "";
      this.label_Hor_Two_CenterTol.BackColor = Color.Lavender;
      this.label_Hor_Two_Dim1.BackColor = Color.LightGray;
      this.label_Hor_Two_Dim2.BackColor = Color.LightGray;
      this.label_Hor_Two_Angle.BackColor = Color.LightGray;
      this.newTextBox_Hor_Two_CenterTol.TextBoxBackColor = Color.White;
      this.newTextBox_Hor_Two_CenterTol.TextForeColor = Color.Black;
      this.newTextBox_Hor_Two_CenterTol.ReadOnly = false;
      this.newTextBox_Hor_Two_Dim1.TextBoxBackColor = Color.LightGray;
      this.newTextBox_Hor_Two_Dim1.TextForeColor = Color.LightGray;
      this.newTextBox_Hor_Two_Dim1.ReadOnly = true;
      this.newTextBox_Hor_Two_Dim2.TextBoxBackColor = Color.LightGray;
      this.newTextBox_Hor_Two_Dim2.TextForeColor = Color.LightGray;
      this.newTextBox_Hor_Two_Dim2.ReadOnly = true;
      this.newTextBox_Hor_Two_Angle.TextBoxBackColor = Color.LightGray;
      this.newTextBox_Hor_Two_Angle.TextForeColor = Color.LightGray;
      this.newTextBox_Hor_Two_Angle.ReadOnly = true;
      this.checkBox_Hor_Two_Inter.SendToBack();
      this.checkBox_Hor_Two_Inter.Visible = false;
    }

    private void radioButton_Hor_Two_Type2_CheckedChanged(object sender, EventArgs e)
    {
      this.label_Hor_Two_CenterTol.Text = "Center " + LocaleControl.getInstance().GetString("IDS_TOLERANCE");
      this.label_Hor_Two_Dim1.Text = LocaleControl.getInstance().GetString("IDS_HORIZON_RUNNER_INTERFERENCE");
      this.label_Hor_Two_Dim2.Text = "";
      this.label_Hor_Two_Angle.Text = "";
      this.label_Hor_Two_CenterTol.BackColor = Color.Lavender;
      this.label_Hor_Two_Dim1.BackColor = Color.Lavender;
      this.label_Hor_Two_Dim2.BackColor = Color.LightGray;
      this.label_Hor_Two_Angle.BackColor = Color.LightGray;
      this.newTextBox_Hor_Two_CenterTol.TextBoxBackColor = Color.White;
      this.newTextBox_Hor_Two_CenterTol.TextForeColor = Color.Black;
      this.newTextBox_Hor_Two_CenterTol.ReadOnly = false;
      this.newTextBox_Hor_Two_Dim1.TextBoxBackColor = Color.White;
      this.newTextBox_Hor_Two_Dim1.TextForeColor = Color.LightGray;
      this.newTextBox_Hor_Two_Dim1.ReadOnly = true;
      this.newTextBox_Hor_Two_Dim2.TextBoxBackColor = Color.LightGray;
      this.newTextBox_Hor_Two_Dim2.TextForeColor = Color.LightGray;
      this.newTextBox_Hor_Two_Dim2.ReadOnly = true;
      this.newTextBox_Hor_Two_Angle.TextBoxBackColor = Color.LightGray;
      this.newTextBox_Hor_Two_Angle.TextForeColor = Color.LightGray;
      this.newTextBox_Hor_Two_Angle.ReadOnly = true;
      this.checkBox_Hor_Two_Inter.BringToFront();
      this.checkBox_Hor_Two_Inter.Visible = true;
      this.checkBox_Hor_Two_Inter.Checked = true;
    }

    private void newButton_Group_NewClick(object sender, EventArgs e)
    {
      if (!(sender is NewButton p_nbSelected))
        return;
      Panel p_pnMain;
      TabControl p_tabMain;
      if (p_nbSelected.Name.Contains("Pin"))
      {
        p_pnMain = this.panel_Pin_Group;
        p_tabMain = this.tabControl_Pin_Group;
      }
      else
      {
        p_pnMain = this.panel_Side_Group;
        p_tabMain = this.tabControl_Side_Group;
      }
      p_tabMain.SelectedIndex = !p_nbSelected.Name.Contains("1") ? (!p_nbSelected.Name.Contains("2") ? (!p_nbSelected.Name.Contains("3") ? 3 : 2) : 1) : 0;
      this.SetTabButtonColor(p_nbSelected, p_pnMain, p_tabMain);
    }

    private void newButton_Hor_RunnerType_NewClick(object sender, EventArgs e)
    {
      if (!(sender is NewButton newButton))
        return;
      if (newButton.Name.Contains("Two"))
        this.tabControl_Hor_RunnerType.SelectedIndex = 0;
      else
        this.tabControl_Hor_RunnerType.SelectedIndex = 1;
    }

    private void tabControl_Hor_RunnerType_SelectedIndexChanged(object sender, EventArgs e) => this.SetTabButtonColor(this.tabControl_Hor_RunnerType.SelectedIndex != 0 ? this.newButton_Hor_Runner_ThreeStage : this.newButton_Hor_Runner_TwoStage, this.panel_Hor_RunnerType, this.tabControl_Hor_RunnerType);

    private void SetTabButtonColor(NewButton p_nbSelected, Panel p_pnMain, TabControl p_tabMain)
    {
      foreach (NewButton newButton in p_pnMain.Controls.OfType<NewButton>())
      {
        int selectedIndex = p_tabMain.SelectedIndex;
        newButton.ButtonBackColor = !newButton.Name.Equals(p_nbSelected.Name) ? Color.White : Color.LightSteelBlue;
      }
    }

    private void radioButton_Quadrant2_CheckedChanged(object sender, EventArgs e)
    {
      try
      {
        this.panel_Hor_Runner_ThreeType.Enabled = false;
        this.radioButton_Hor_Three_Type1.Checked = true;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunnerDB]radioButton_Quadrant2_CheckedChanged):" + ex.Message));
      }
    }

    private void radioButton_Quadrant4_CheckedChanged(object sender, EventArgs e)
    {
      try
      {
        this.panel_Hor_Runner_ThreeType.Enabled = true;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunnerDB]radioButton_Quadrant4_CheckedChanged):" + ex.Message));
      }
    }

    private void radioButton_Hor_Three_Type1_CheckedChanged(object sender, EventArgs e)
    {
      try
      {
        this.label_Hor_Three_CenterTol.Text = "Center " + LocaleControl.getInstance().GetString("IDS_TOLERANCE");
        this.label_Hor_Three_CenterTol.BackColor = Color.Lavender;
        this.newTextBox_Hor_Three_CenterTol.TextBoxBackColor = Color.White;
        this.newTextBox_Hor_Three_CenterTol.TextForeColor = Color.Black;
        this.newTextBox_Hor_Three_CenterTol.ReadOnly = false;
        if (!(this.newTextBox_Hor_Three_CenterTol.Value == string.Empty))
          return;
        this.newTextBox_Hor_Three_CenterTol.Value = "0";
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunnerDB]radioButton_Hor_Three_Type1_CheckedChanged):" + ex.Message));
      }
    }

    private void radioButton_Hor_Three_Type2_CheckedChanged(object sender, EventArgs e)
    {
      try
      {
        this.label_Hor_Three_CenterTol.Text = "";
        this.label_Hor_Three_CenterTol.BackColor = Color.LightGray;
        this.newTextBox_Hor_Three_CenterTol.TextBoxBackColor = Color.LightGray;
        this.newTextBox_Hor_Three_CenterTol.TextForeColor = Color.LightGray;
        this.newTextBox_Hor_Three_CenterTol.ReadOnly = true;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmRunner]radioButton_Hor_Three_Type2_CheckedChanged):" + ex.Message));
      }
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.label_DB_List = new Label();
      this.listBox_DB = new ListBox();
      this.label_Pin = new Label();
      this.label_Side = new Label();
      this.label_Hor_Runner = new Label();
      this.label_Sprue = new Label();
      this.panel1 = new Panel();
      this.radioButton_Pin_HotSystem = new RadioButton();
      this.radioButton_Pin_ColdSystem = new RadioButton();
      this.panel2 = new Panel();
      this.radioButton_Hor_Two_Type4 = new RadioButton();
      this.radioButton_Hor_Two_Type3 = new RadioButton();
      this.radioButton_Hor_Two_Type2 = new RadioButton();
      this.radioButton_Hor_Two_Type1 = new RadioButton();
      this.label_Hor_Two_Dia = new Label();
      this.label_Hor_Two_Length = new Label();
      this.panel5 = new Panel();
      this.radioButton_Sprue_Hot = new RadioButton();
      this.radioButton_Sprue_Cold = new RadioButton();
      this.label_Sprue_Dim1 = new Label();
      this.label_Sprue_Length = new Label();
      this.label_Sprue_Dim2 = new Label();
      this.label_Cavity = new Label();
      this.label_Cavity_Occ = new Label();
      this.label_DB_Company = new Label();
      this.label_DB_Item = new Label();
      this.label_DB_Option_W = new Label();
      this.label_DB_Company_W = new Label();
      this.label_DB_Item_W = new Label();
      this.newButton_Export = new NewButton();
      this.newButton_Import = new NewButton();
      this.newButton_Del = new NewButton();
      this.newButton_Edit = new NewButton();
      this.newButton_Add = new NewButton();
      this.newTextBox_Sprue_Length = new NewTextBox();
      this.newTextBox_Sprue_Dim1 = new NewTextBox();
      this.newTextBox_Sprue_Dim2 = new NewTextBox();
      this.newTextBox_Hor_Two_Dia = new NewTextBox();
      this.newTextBox_Hor_Two_Length = new NewTextBox();
      this.newTextBox_Cavity_Occ = new NewTextBox();
      this.newTextBox_Option = new NewTextBox();
      this.newTextBox_Item = new NewTextBox();
      this.newTextBox_Company = new NewTextBox();
      this.newComboBox_Item = new NewComboBox();
      this.newComboBox_Company = new NewComboBox();
      this.newTextBox_Hor_Two_Dim1 = new NewTextBox();
      this.newTextBox_Hor_Two_Dim2 = new NewTextBox();
      this.label_Hor_Two_Dim2 = new Label();
      this.label_Hor_Two_Dim1 = new Label();
      this.newTextBox_Hor_Two_Angle = new NewTextBox();
      this.label_Hor_Two_Angle = new Label();
      this.label_Hor_Two_Direction = new Label();
      this.newComboBox_Hor_Two_Direction = new NewComboBox();
      this.panel_Pin_Group = new Panel();
      this.newButton_Pin_G1 = new NewButton();
      this.newButton_Pin_G4 = new NewButton();
      this.newButton_Pin_G3 = new NewButton();
      this.newButton_Pin_G2 = new NewButton();
      this.tabControl_Pin_Group = new TabControl();
      this.tabPage_Pin_Group1 = new TabPage();
      this.label_Pin_Gate_G1 = new Label();
      this.label_Pin_Runner_G1 = new Label();
      this.label_Pin_GateDim1_G1 = new Label();
      this.label_Pin_RunnerLength_G1 = new Label();
      this.label_Pin_RunnerDim1_G1 = new Label();
      this.label_Pin_GateDim2_G1 = new Label();
      this.label_Pin_RunnerDim2_G1 = new Label();
      this.newTextBox_Pin_GateDim1_G1 = new NewTextBox();
      this.newTextBox_Pin_GateDim2_G1 = new NewTextBox();
      this.newTextBox_Pin_RunnerLength_G1 = new NewTextBox();
      this.newTextBox_Pin_RunnerDim1_G1 = new NewTextBox();
      this.newTextBox_Pin_RunnerDim2_G1 = new NewTextBox();
      this.tabPage_Pin_Group2 = new TabPage();
      this.label_Pin_Gate_G2 = new Label();
      this.label_Pin_Runner_G2 = new Label();
      this.label_Pin_GateDim1_G2 = new Label();
      this.label_Pin_RunnerLength_G2 = new Label();
      this.label_Pin_RunnerDim1_G2 = new Label();
      this.label_Pin_GateDim2_G2 = new Label();
      this.label_Pin_RunnerDim2_G2 = new Label();
      this.newTextBox_Pin_GateDim1_G2 = new NewTextBox();
      this.newTextBox_Pin_GateDim2_G2 = new NewTextBox();
      this.newTextBox_Pin_RunnerLength_G2 = new NewTextBox();
      this.newTextBox_Pin_RunnerDim1_G2 = new NewTextBox();
      this.newTextBox_Pin_RunnerDim2_G2 = new NewTextBox();
      this.tabPage_Pin_Group3 = new TabPage();
      this.label_Pin_Gate_G3 = new Label();
      this.label_Pin_Runner_G3 = new Label();
      this.label_Pin_GateDim1_G3 = new Label();
      this.label_Pin_RunnerLength_G3 = new Label();
      this.label_Pin_RunnerDim1_G3 = new Label();
      this.label_Pin_GateDim2_G3 = new Label();
      this.label_Pin_RunnerDim2_G3 = new Label();
      this.newTextBox_Pin_GateDim1_G3 = new NewTextBox();
      this.newTextBox_Pin_GateDim2_G3 = new NewTextBox();
      this.newTextBox_Pin_RunnerLength_G3 = new NewTextBox();
      this.newTextBox_Pin_RunnerDim1_G3 = new NewTextBox();
      this.newTextBox_Pin_RunnerDim2_G3 = new NewTextBox();
      this.tabPage_Pin_Group4 = new TabPage();
      this.label_Pin_Gate_G4 = new Label();
      this.label_Pin_Runner_G4 = new Label();
      this.label_Pin_GateDim1_G4 = new Label();
      this.label_Pin_RunnerLength_G4 = new Label();
      this.label_Pin_RunnerDim1_G4 = new Label();
      this.label_Pin_GateDim2_G4 = new Label();
      this.label_Pin_RunnerDim2_G4 = new Label();
      this.newTextBox_Pin_GateDim1_G4 = new NewTextBox();
      this.newTextBox_Pin_GateDim2_G4 = new NewTextBox();
      this.newTextBox_Pin_RunnerLength_G4 = new NewTextBox();
      this.newTextBox_Pin_RunnerDim1_G4 = new NewTextBox();
      this.newTextBox_Pin_RunnerDim2_G4 = new NewTextBox();
      this.tabControl_Side_Group = new TabControl();
      this.tabPage_Side_Group1 = new TabPage();
      this.panel9 = new Panel();
      this.radioButton_Side_GateCircle_G1 = new RadioButton();
      this.radioButton_Side_GateRect_G1 = new RadioButton();
      this.panel_Side_GateType2_G1 = new Panel();
      this.radioButton_Side_GateSubMarine_G1 = new RadioButton();
      this.radioButton_Side_GateNormal_G1 = new RadioButton();
      this.panel11 = new Panel();
      this.radioButton_Side_RunnerRectangle_G1 = new RadioButton();
      this.radioButton_Side_RunnerCircle_G1 = new RadioButton();
      this.radioButton_Side_RunnerTrepezoidal_G1 = new RadioButton();
      this.label_Side_Gate_G1 = new Label();
      this.label_Side_Runner_G1 = new Label();
      this.label_Side_GateLength_G1 = new Label();
      this.label_Side_GateDim1_G1 = new Label();
      this.label_Side_RunnerLength_G1 = new Label();
      this.label_Side_GateDim3_G1 = new Label();
      this.newTextBox_Side_RunnerDim4_G1 = new NewTextBox();
      this.label_Side_RunnerDim2_G1 = new Label();
      this.label_Side_RunnerDim4_G1 = new Label();
      this.label_Side_GateDim2_G1 = new Label();
      this.label_Side_RunnerDim1_G1 = new Label();
      this.label_Side_GateDim4_G1 = new Label();
      this.label_Side_RunnerDim3_G1 = new Label();
      this.newTextBox_Side_GateLength_G1 = new NewTextBox();
      this.newTextBox_Side_GateDim1_G1 = new NewTextBox();
      this.newTextBox_Side_GateDim2_G1 = new NewTextBox();
      this.newTextBox_Side_GateDim3_G1 = new NewTextBox();
      this.newTextBox_Side_GateDim4_G1 = new NewTextBox();
      this.newTextBox_Side_RunnerLength_G1 = new NewTextBox();
      this.newTextBox_Side_RunnerDim1_G1 = new NewTextBox();
      this.newTextBox_Side_RunnerDim2_G1 = new NewTextBox();
      this.newTextBox_Side_RunnerDim3_G1 = new NewTextBox();
      this.tabPage_Side_Group2 = new TabPage();
      this.panel12 = new Panel();
      this.radioButton_Side_GateCircle_G2 = new RadioButton();
      this.radioButton_Side_GateRect_G2 = new RadioButton();
      this.panel_Side_GateType2_G2 = new Panel();
      this.radioButton_Side_GateSubMarine_G2 = new RadioButton();
      this.radioButton_Side_GateNormal_G2 = new RadioButton();
      this.panel14 = new Panel();
      this.radioButton_Side_RunnerRectangle_G2 = new RadioButton();
      this.radioButton_Side_RunnerCircle_G2 = new RadioButton();
      this.radioButton_Side_RunnerTrepezoidal_G2 = new RadioButton();
      this.label_Side_Gate_G2 = new Label();
      this.label_Side_Runner_G2 = new Label();
      this.label_Side_GateLength_G2 = new Label();
      this.label_Side_GateDim1_G2 = new Label();
      this.label_Side_RunnerLength_G2 = new Label();
      this.label_Side_GateDim3_G2 = new Label();
      this.newTextBox_Side_RunnerDim4_G2 = new NewTextBox();
      this.label_Side_RunnerDim2_G2 = new Label();
      this.label_Side_RunnerDim4_G2 = new Label();
      this.label_Side_GateDim2_G2 = new Label();
      this.label_Side_RunnerDim1_G2 = new Label();
      this.label_Side_GateDim4_G2 = new Label();
      this.label_Side_RunnerDim3_G2 = new Label();
      this.newTextBox_Side_GateLength_G2 = new NewTextBox();
      this.newTextBox_Side_GateDim1_G2 = new NewTextBox();
      this.newTextBox_Side_GateDim2_G2 = new NewTextBox();
      this.newTextBox_Side_GateDim3_G2 = new NewTextBox();
      this.newTextBox_Side_GateDim4_G2 = new NewTextBox();
      this.newTextBox_Side_RunnerLength_G2 = new NewTextBox();
      this.newTextBox_Side_RunnerDim1_G2 = new NewTextBox();
      this.newTextBox_Side_RunnerDim2_G2 = new NewTextBox();
      this.newTextBox_Side_RunnerDim3_G2 = new NewTextBox();
      this.tabPage_Side_Group3 = new TabPage();
      this.panel6 = new Panel();
      this.radioButton_Side_GateCircle_G3 = new RadioButton();
      this.radioButton_Side_GateRect_G3 = new RadioButton();
      this.panel_Side_GateType2_G3 = new Panel();
      this.radioButton_Side_GateSubMarine_G3 = new RadioButton();
      this.radioButton_Side_GateNormal_G3 = new RadioButton();
      this.panel8 = new Panel();
      this.radioButton_Side_RunnerRectangle_G3 = new RadioButton();
      this.radioButton_Side_RunnerCircle_G3 = new RadioButton();
      this.radioButton_Side_RunnerTrepezoidal_G3 = new RadioButton();
      this.label_Side_Gate_G3 = new Label();
      this.label_Side_Runner_G3 = new Label();
      this.label_Side_GateLength_G3 = new Label();
      this.label_Side_GateDim1_G3 = new Label();
      this.label_Side_RunnerLength_G3 = new Label();
      this.label_Side_GateDim3_G3 = new Label();
      this.newTextBox_Side_RunnerDim4_G3 = new NewTextBox();
      this.label_Side_RunnerDim2_G3 = new Label();
      this.label_Side_RunnerDim4_G3 = new Label();
      this.label_Side_GateDim2_G3 = new Label();
      this.label_Side_RunnerDim1_G3 = new Label();
      this.label_Side_GateDim4_G3 = new Label();
      this.label_Side_RunnerDim3_G3 = new Label();
      this.newTextBox_Side_GateLength_G3 = new NewTextBox();
      this.newTextBox_Side_GateDim1_G3 = new NewTextBox();
      this.newTextBox_Side_GateDim2_G3 = new NewTextBox();
      this.newTextBox_Side_GateDim3_G3 = new NewTextBox();
      this.newTextBox_Side_GateDim4_G3 = new NewTextBox();
      this.newTextBox_Side_RunnerLength_G3 = new NewTextBox();
      this.newTextBox_Side_RunnerDim1_G3 = new NewTextBox();
      this.newTextBox_Side_RunnerDim2_G3 = new NewTextBox();
      this.newTextBox_Side_RunnerDim3_G3 = new NewTextBox();
      this.tabPage_Side_Group4 = new TabPage();
      this.label_Side_Gate_G4 = new Label();
      this.label_Side_Runner_G4 = new Label();
      this.panel18 = new Panel();
      this.radioButton_Side_GateCircle_G4 = new RadioButton();
      this.radioButton_Side_GateRect_G4 = new RadioButton();
      this.panel_Side_GateType2_G4 = new Panel();
      this.radioButton_Side_GateSubMarine_G4 = new RadioButton();
      this.radioButton_Side_GateNormal_G4 = new RadioButton();
      this.panel20 = new Panel();
      this.radioButton_Side_RunnerRectangle_G4 = new RadioButton();
      this.radioButton_Side_RunnerCircle_G4 = new RadioButton();
      this.radioButton_Side_RunnerTrepezoidal_G4 = new RadioButton();
      this.label_Side_GateLength_G4 = new Label();
      this.label_Side_GateDim1_G4 = new Label();
      this.label_Side_RunnerLength_G4 = new Label();
      this.label_Side_GateDim3_G4 = new Label();
      this.newTextBox_Side_RunnerDim4_G4 = new NewTextBox();
      this.label_Side_RunnerDim2_G4 = new Label();
      this.label_Side_RunnerDim4_G4 = new Label();
      this.label_Side_GateDim2_G4 = new Label();
      this.label_Side_RunnerDim1_G4 = new Label();
      this.label_Side_GateDim4_G4 = new Label();
      this.label_Side_RunnerDim3_G4 = new Label();
      this.newTextBox_Side_GateLength_G4 = new NewTextBox();
      this.newTextBox_Side_GateDim1_G4 = new NewTextBox();
      this.newTextBox_Side_GateDim2_G4 = new NewTextBox();
      this.newTextBox_Side_GateDim3_G4 = new NewTextBox();
      this.newTextBox_Side_GateDim4_G4 = new NewTextBox();
      this.newTextBox_Side_RunnerLength_G4 = new NewTextBox();
      this.newTextBox_Side_RunnerDim1_G4 = new NewTextBox();
      this.newTextBox_Side_RunnerDim2_G4 = new NewTextBox();
      this.newTextBox_Side_RunnerDim3_G4 = new NewTextBox();
      this.newButton_Side_G3 = new NewButton();
      this.newButton_Side_G2 = new NewButton();
      this.newButton_Side_G4 = new NewButton();
      this.newButton_Side_G1 = new NewButton();
      this.panel_Side_Group = new Panel();
      this.panel_Hor_RunnerType = new Panel();
      this.newButton_Hor_Runner_TwoStage = new NewButton();
      this.newButton_Hor_Runner_ThreeStage = new NewButton();
      this.tabControl_Hor_RunnerType = new TabControl();
      this.tabPage_Hor_TwoStage = new TabPage();
      this.newTextBox_Hor_Two_CenterTol = new NewTextBox();
      this.label_Hor_Two_CenterTol = new Label();
      this.checkBox_Hor_Two_Inter = new CheckBox();
      this.tabPage_Hor_ThreeStage = new TabPage();
      this.label_Hor_Three_Length = new Label();
      this.newTextBox_Hor_Three_Length = new NewTextBox();
      this.panel_Quadrant = new Panel();
      this.radioButton_Quadrant4 = new RadioButton();
      this.radioButton_Quadrant2 = new RadioButton();
      this.newTextBox_Hor_Three_CenterTol = new NewTextBox();
      this.panel_Hor_Runner_ThreeType = new Panel();
      this.radioButton_Hor_Three_Type2 = new RadioButton();
      this.radioButton_Hor_Three_Type1 = new RadioButton();
      this.label_Hor_Three_Dia = new Label();
      this.label_Hor_Three_CenterTol = new Label();
      this.newTextBox_Hor_Three_Dia = new NewTextBox();
      this.panel1.SuspendLayout();
      this.panel2.SuspendLayout();
      this.panel5.SuspendLayout();
      this.panel_Pin_Group.SuspendLayout();
      this.tabControl_Pin_Group.SuspendLayout();
      this.tabPage_Pin_Group1.SuspendLayout();
      this.tabPage_Pin_Group2.SuspendLayout();
      this.tabPage_Pin_Group3.SuspendLayout();
      this.tabPage_Pin_Group4.SuspendLayout();
      this.tabControl_Side_Group.SuspendLayout();
      this.tabPage_Side_Group1.SuspendLayout();
      this.panel9.SuspendLayout();
      this.panel_Side_GateType2_G1.SuspendLayout();
      this.panel11.SuspendLayout();
      this.tabPage_Side_Group2.SuspendLayout();
      this.panel12.SuspendLayout();
      this.panel_Side_GateType2_G2.SuspendLayout();
      this.panel14.SuspendLayout();
      this.tabPage_Side_Group3.SuspendLayout();
      this.panel6.SuspendLayout();
      this.panel_Side_GateType2_G3.SuspendLayout();
      this.panel8.SuspendLayout();
      this.tabPage_Side_Group4.SuspendLayout();
      this.panel18.SuspendLayout();
      this.panel_Side_GateType2_G4.SuspendLayout();
      this.panel20.SuspendLayout();
      this.panel_Side_Group.SuspendLayout();
      this.panel_Hor_RunnerType.SuspendLayout();
      this.tabControl_Hor_RunnerType.SuspendLayout();
      this.tabPage_Hor_TwoStage.SuspendLayout();
      this.tabPage_Hor_ThreeStage.SuspendLayout();
      this.panel_Quadrant.SuspendLayout();
      this.panel_Hor_Runner_ThreeType.SuspendLayout();
      this.SuspendLayout();
      this.label_DB_List.BackColor = Color.FromArgb(229, 238, 248);
      this.label_DB_List.BorderStyle = BorderStyle.FixedSingle;
      this.label_DB_List.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_DB_List.ForeColor = Color.MidnightBlue;
      this.label_DB_List.Location = new Point(5, 5);
      this.label_DB_List.Name = "label_DB_List";
      this.label_DB_List.Size = new Size(301, 20);
      this.label_DB_List.TabIndex = 7;
      this.label_DB_List.Text = "DB 리스트";
      this.label_DB_List.TextAlign = ContentAlignment.MiddleCenter;
      this.listBox_DB.FormattingEnabled = true;
      this.listBox_DB.ItemHeight = 15;
      this.listBox_DB.Location = new Point(5, 68);
      this.listBox_DB.Name = "listBox_DB";
      this.listBox_DB.SelectionMode = SelectionMode.MultiExtended;
      this.listBox_DB.Size = new Size(301, 364);
      this.listBox_DB.TabIndex = 3;
      this.listBox_DB.TabStop = false;
      this.listBox_DB.SelectedIndexChanged += new EventHandler(this.listBox_DB_SelectedIndexChanged);
      this.label_Pin.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Pin.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Pin.ForeColor = Color.MidnightBlue;
      this.label_Pin.Location = new Point(310, 5);
      this.label_Pin.Name = "label_Pin";
      this.label_Pin.Size = new Size(228, 20);
      this.label_Pin.TabIndex = 7;
      this.label_Pin.Text = "[핀]";
      this.label_Pin.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Side.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Side.ForeColor = Color.MidnightBlue;
      this.label_Side.Location = new Point(542, 5);
      this.label_Side.Name = "label_Side";
      this.label_Side.Size = new Size(228, 20);
      this.label_Side.TabIndex = 7;
      this.label_Side.Text = "[사이드]";
      this.label_Side.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Hor_Runner.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Hor_Runner.BorderStyle = BorderStyle.FixedSingle;
      this.label_Hor_Runner.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Hor_Runner.ForeColor = Color.MidnightBlue;
      this.label_Hor_Runner.Location = new Point(310, 271);
      this.label_Hor_Runner.Name = "label_Hor_Runner";
      this.label_Hor_Runner.Size = new Size(228, 20);
      this.label_Hor_Runner.TabIndex = 7;
      this.label_Hor_Runner.Text = "[수평런너]";
      this.label_Hor_Runner.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Sprue.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Sprue.BorderStyle = BorderStyle.FixedSingle;
      this.label_Sprue.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Sprue.ForeColor = Color.MidnightBlue;
      this.label_Sprue.Location = new Point(542, 403);
      this.label_Sprue.Name = "label_Sprue";
      this.label_Sprue.Size = new Size(228, 20);
      this.label_Sprue.TabIndex = 7;
      this.label_Sprue.Text = "[스프루]";
      this.label_Sprue.TextAlign = ContentAlignment.MiddleCenter;
      this.panel1.BackColor = Color.White;
      this.panel1.BorderStyle = BorderStyle.FixedSingle;
      this.panel1.Controls.Add((Control) this.radioButton_Pin_HotSystem);
      this.panel1.Controls.Add((Control) this.radioButton_Pin_ColdSystem);
      this.panel1.Location = new Point(310, 24);
      this.panel1.Name = "panel1";
      this.panel1.Size = new Size(228, 25);
      this.panel1.TabIndex = 12;
      this.radioButton_Pin_HotSystem.Location = new Point(128, 2);
      this.radioButton_Pin_HotSystem.Name = "radioButton_Pin_HotSystem";
      this.radioButton_Pin_HotSystem.Size = new Size(76, 19);
      this.radioButton_Pin_HotSystem.TabIndex = 14;
      this.radioButton_Pin_HotSystem.Text = "핫 시스템";
      this.radioButton_Pin_HotSystem.UseVisualStyleBackColor = true;
      this.radioButton_Pin_HotSystem.CheckedChanged += new EventHandler(this.radioButton_Pin_HotSystem_CheckedChanged);
      this.radioButton_Pin_ColdSystem.Location = new Point(28, 2);
      this.radioButton_Pin_ColdSystem.Name = "radioButton_Pin_ColdSystem";
      this.radioButton_Pin_ColdSystem.Size = new Size(89, 19);
      this.radioButton_Pin_ColdSystem.TabIndex = 13;
      this.radioButton_Pin_ColdSystem.Text = "콜드 시스템";
      this.radioButton_Pin_ColdSystem.UseVisualStyleBackColor = true;
      this.radioButton_Pin_ColdSystem.CheckedChanged += new EventHandler(this.radioButton_Pin_ColdSystem_CheckedChanged);
      this.panel2.BackColor = Color.White;
      this.panel2.BorderStyle = BorderStyle.FixedSingle;
      this.panel2.Controls.Add((Control) this.radioButton_Hor_Two_Type4);
      this.panel2.Controls.Add((Control) this.radioButton_Hor_Two_Type3);
      this.panel2.Controls.Add((Control) this.radioButton_Hor_Two_Type2);
      this.panel2.Controls.Add((Control) this.radioButton_Hor_Two_Type1);
      this.panel2.Location = new Point(1, 0);
      this.panel2.Name = "panel2";
      this.panel2.Size = new Size(228, 49);
      this.panel2.TabIndex = 42;
      this.radioButton_Hor_Two_Type4.Location = new Point(128, 27);
      this.radioButton_Hor_Two_Type4.Name = "radioButton_Hor_Two_Type4";
      this.radioButton_Hor_Two_Type4.Size = new Size(81, 19);
      this.radioButton_Hor_Two_Type4.TabIndex = 46;
      this.radioButton_Hor_Two_Type4.Text = "☆ - x 타입";
      this.radioButton_Hor_Two_Type4.UseVisualStyleBackColor = true;
      this.radioButton_Hor_Two_Type4.CheckedChanged += new EventHandler(this.radioButton_Hor_Two_Type4_CheckedChanged);
      this.radioButton_Hor_Two_Type3.Location = new Point(26, 27);
      this.radioButton_Hor_Two_Type3.Name = "radioButton_Hor_Two_Type3";
      this.radioButton_Hor_Two_Type3.Size = new Size(81, 19);
      this.radioButton_Hor_Two_Type3.TabIndex = 45;
      this.radioButton_Hor_Two_Type3.Text = "☆ - + 타입";
      this.radioButton_Hor_Two_Type3.UseVisualStyleBackColor = true;
      this.radioButton_Hor_Two_Type3.CheckedChanged += new EventHandler(this.radioButton_Hor_Two_Type3_CheckedChanged);
      this.radioButton_Hor_Two_Type2.Location = new Point(128, 4);
      this.radioButton_Hor_Two_Type2.Name = "radioButton_Hor_Two_Type2";
      this.radioButton_Hor_Two_Type2.Size = new Size(68, 19);
      this.radioButton_Hor_Two_Type2.TabIndex = 44;
      this.radioButton_Hor_Two_Type2.Text = "H 타입";
      this.radioButton_Hor_Two_Type2.UseVisualStyleBackColor = true;
      this.radioButton_Hor_Two_Type2.CheckedChanged += new EventHandler(this.radioButton_Hor_Two_Type2_CheckedChanged);
      this.radioButton_Hor_Two_Type1.Location = new Point(26, 4);
      this.radioButton_Hor_Two_Type1.Name = "radioButton_Hor_Two_Type1";
      this.radioButton_Hor_Two_Type1.Size = new Size(55, 19);
      this.radioButton_Hor_Two_Type1.TabIndex = 43;
      this.radioButton_Hor_Two_Type1.Text = "I 타입";
      this.radioButton_Hor_Two_Type1.UseVisualStyleBackColor = true;
      this.radioButton_Hor_Two_Type1.CheckedChanged += new EventHandler(this.radioButton_Hor_Two_Type_CheckedChanged);
      this.label_Hor_Two_Dia.BackColor = Color.Lavender;
      this.label_Hor_Two_Dia.BorderStyle = BorderStyle.FixedSingle;
      this.label_Hor_Two_Dia.Location = new Point(1, 48);
      this.label_Hor_Two_Dia.Name = "label_Hor_Two_Dia";
      this.label_Hor_Two_Dia.Size = new Size(153, 23);
      this.label_Hor_Two_Dia.TabIndex = 10;
      this.label_Hor_Two_Dia.Text = "수평 런너 직경";
      this.label_Hor_Two_Dia.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Hor_Two_Length.BackColor = Color.Lavender;
      this.label_Hor_Two_Length.BorderStyle = BorderStyle.FixedSingle;
      this.label_Hor_Two_Length.Location = new Point(1, 70);
      this.label_Hor_Two_Length.Name = "label_Hor_Two_Length";
      this.label_Hor_Two_Length.Size = new Size(153, 23);
      this.label_Hor_Two_Length.TabIndex = 10;
      this.label_Hor_Two_Length.Text = "수평 런너 길이";
      this.label_Hor_Two_Length.TextAlign = ContentAlignment.MiddleCenter;
      this.panel5.BackColor = Color.White;
      this.panel5.BorderStyle = BorderStyle.FixedSingle;
      this.panel5.Controls.Add((Control) this.radioButton_Sprue_Hot);
      this.panel5.Controls.Add((Control) this.radioButton_Sprue_Cold);
      this.panel5.Location = new Point(542, 422);
      this.panel5.Name = "panel5";
      this.panel5.Size = new Size(228, 25);
      this.panel5.TabIndex = 78;
      this.radioButton_Sprue_Hot.Location = new Point(128, 2);
      this.radioButton_Sprue_Hot.Name = "radioButton_Sprue_Hot";
      this.radioButton_Sprue_Hot.Size = new Size(78, 19);
      this.radioButton_Sprue_Hot.TabIndex = 80;
      this.radioButton_Sprue_Hot.Text = "핫 스프루";
      this.radioButton_Sprue_Hot.UseVisualStyleBackColor = true;
      this.radioButton_Sprue_Cold.Location = new Point(29, 2);
      this.radioButton_Sprue_Cold.Name = "radioButton_Sprue_Cold";
      this.radioButton_Sprue_Cold.Size = new Size(89, 19);
      this.radioButton_Sprue_Cold.TabIndex = 79;
      this.radioButton_Sprue_Cold.Text = "콜드 스프루";
      this.radioButton_Sprue_Cold.UseVisualStyleBackColor = true;
      this.label_Sprue_Dim1.BackColor = Color.Lavender;
      this.label_Sprue_Dim1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Sprue_Dim1.Location = new Point(542, 467);
      this.label_Sprue_Dim1.Name = "label_Sprue_Dim1";
      this.label_Sprue_Dim1.Size = new Size(153, 23);
      this.label_Sprue_Dim1.TabIndex = 10;
      this.label_Sprue_Dim1.Text = "Start Dimension";
      this.label_Sprue_Dim1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Sprue_Length.BackColor = Color.Lavender;
      this.label_Sprue_Length.BorderStyle = BorderStyle.FixedSingle;
      this.label_Sprue_Length.Location = new Point(542, 446);
      this.label_Sprue_Length.Name = "label_Sprue_Length";
      this.label_Sprue_Length.Size = new Size(153, 23);
      this.label_Sprue_Length.TabIndex = 10;
      this.label_Sprue_Length.Text = "스프루 길이";
      this.label_Sprue_Length.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Sprue_Dim2.BackColor = Color.Lavender;
      this.label_Sprue_Dim2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Sprue_Dim2.Location = new Point(542, 489);
      this.label_Sprue_Dim2.Name = "label_Sprue_Dim2";
      this.label_Sprue_Dim2.Size = new Size(153, 23);
      this.label_Sprue_Dim2.TabIndex = 10;
      this.label_Sprue_Dim2.Text = "End Dimension";
      this.label_Sprue_Dim2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Cavity.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Cavity.BorderStyle = BorderStyle.FixedSingle;
      this.label_Cavity.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Cavity.ForeColor = Color.MidnightBlue;
      this.label_Cavity.Location = new Point(310, 226);
      this.label_Cavity.Name = "label_Cavity";
      this.label_Cavity.Size = new Size(228, 20);
      this.label_Cavity.TabIndex = 7;
      this.label_Cavity.Text = "[캐비티]";
      this.label_Cavity.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Cavity_Occ.BackColor = Color.Lavender;
      this.label_Cavity_Occ.BorderStyle = BorderStyle.FixedSingle;
      this.label_Cavity_Occ.Location = new Point(310, 245);
      this.label_Cavity_Occ.Name = "label_Cavity_Occ";
      this.label_Cavity_Occ.Size = new Size(153, 23);
      this.label_Cavity_Occ.TabIndex = 10;
      this.label_Cavity_Occ.Text = "Occurence Number";
      this.label_Cavity_Occ.TextAlign = ContentAlignment.MiddleCenter;
      this.label_DB_Company.BackColor = Color.Lavender;
      this.label_DB_Company.BorderStyle = BorderStyle.FixedSingle;
      this.label_DB_Company.Location = new Point(5, 24);
      this.label_DB_Company.Name = "label_DB_Company";
      this.label_DB_Company.Size = new Size(151, 23);
      this.label_DB_Company.TabIndex = 10;
      this.label_DB_Company.Text = "회사명";
      this.label_DB_Company.TextAlign = ContentAlignment.MiddleCenter;
      this.label_DB_Item.BackColor = Color.Lavender;
      this.label_DB_Item.BorderStyle = BorderStyle.FixedSingle;
      this.label_DB_Item.Location = new Point(155, 24);
      this.label_DB_Item.Name = "label_DB_Item";
      this.label_DB_Item.Size = new Size(151, 23);
      this.label_DB_Item.TabIndex = 10;
      this.label_DB_Item.Text = "아이템명";
      this.label_DB_Item.TextAlign = ContentAlignment.MiddleCenter;
      this.label_DB_Option_W.BackColor = Color.Lavender;
      this.label_DB_Option_W.BorderStyle = BorderStyle.FixedSingle;
      this.label_DB_Option_W.Location = new Point(205, 434);
      this.label_DB_Option_W.Name = "label_DB_Option_W";
      this.label_DB_Option_W.Size = new Size(101, 23);
      this.label_DB_Option_W.TabIndex = 10;
      this.label_DB_Option_W.Text = "옵션";
      this.label_DB_Option_W.TextAlign = ContentAlignment.MiddleCenter;
      this.label_DB_Company_W.BackColor = Color.Lavender;
      this.label_DB_Company_W.BorderStyle = BorderStyle.FixedSingle;
      this.label_DB_Company_W.Location = new Point(5, 434);
      this.label_DB_Company_W.Name = "label_DB_Company_W";
      this.label_DB_Company_W.Size = new Size(101, 23);
      this.label_DB_Company_W.TabIndex = 10;
      this.label_DB_Company_W.Text = "회사명";
      this.label_DB_Company_W.TextAlign = ContentAlignment.MiddleCenter;
      this.label_DB_Item_W.BackColor = Color.Lavender;
      this.label_DB_Item_W.BorderStyle = BorderStyle.FixedSingle;
      this.label_DB_Item_W.Location = new Point(105, 434);
      this.label_DB_Item_W.Name = "label_DB_Item_W";
      this.label_DB_Item_W.Size = new Size(101, 23);
      this.label_DB_Item_W.TabIndex = 10;
      this.label_DB_Item_W.Text = "아이템명";
      this.label_DB_Item_W.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Export.ButtonBackColor = Color.White;
      this.newButton_Export.ButtonText = "출력";
      this.newButton_Export.FlatBorderSize = 1;
      this.newButton_Export.FlatStyle = FlatStyle.Flat;
      this.newButton_Export.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Export.Image = (Image) Resources.Export;
      this.newButton_Export.ImageAlign = ContentAlignment.MiddleLeft;
      this.newButton_Export.Location = new Point(246, 480);
      this.newButton_Export.Name = "newButton_Export";
      this.newButton_Export.Size = new Size(60, 32);
      this.newButton_Export.TabIndex = 11;
      this.newButton_Export.TabStop = false;
      this.newButton_Export.TextAlign = ContentAlignment.MiddleRight;
      this.newButton_Export.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Export.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_Import.ButtonBackColor = Color.White;
      this.newButton_Import.ButtonText = "삽입";
      this.newButton_Import.FlatBorderSize = 1;
      this.newButton_Import.FlatStyle = FlatStyle.Flat;
      this.newButton_Import.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Import.Image = (Image) Resources.Import;
      this.newButton_Import.ImageAlign = ContentAlignment.MiddleLeft;
      this.newButton_Import.Location = new Point(187, 480);
      this.newButton_Import.Name = "newButton_Import";
      this.newButton_Import.Size = new Size(60, 32);
      this.newButton_Import.TabIndex = 10;
      this.newButton_Import.TabStop = false;
      this.newButton_Import.TextAlign = ContentAlignment.MiddleRight;
      this.newButton_Import.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Import.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_Del.ButtonBackColor = Color.White;
      this.newButton_Del.ButtonText = "삭제";
      this.newButton_Del.FlatBorderSize = 1;
      this.newButton_Del.FlatStyle = FlatStyle.Flat;
      this.newButton_Del.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Del.Image = (Image) Resources.Del;
      this.newButton_Del.ImageAlign = ContentAlignment.MiddleLeft;
      this.newButton_Del.Location = new Point(123, 480);
      this.newButton_Del.Name = "newButton_Del";
      this.newButton_Del.Size = new Size(60, 32);
      this.newButton_Del.TabIndex = 9;
      this.newButton_Del.TabStop = false;
      this.newButton_Del.TextAlign = ContentAlignment.MiddleRight;
      this.newButton_Del.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Del.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_Edit.ButtonBackColor = Color.White;
      this.newButton_Edit.ButtonText = "수정";
      this.newButton_Edit.FlatBorderSize = 1;
      this.newButton_Edit.FlatStyle = FlatStyle.Flat;
      this.newButton_Edit.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Edit.Image = (Image) Resources.Edit;
      this.newButton_Edit.ImageAlign = ContentAlignment.MiddleLeft;
      this.newButton_Edit.Location = new Point(64, 480);
      this.newButton_Edit.Name = "newButton_Edit";
      this.newButton_Edit.Size = new Size(60, 32);
      this.newButton_Edit.TabIndex = 8;
      this.newButton_Edit.TabStop = false;
      this.newButton_Edit.TextAlign = ContentAlignment.MiddleRight;
      this.newButton_Edit.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Edit.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_Add.ButtonBackColor = Color.White;
      this.newButton_Add.ButtonText = "추가";
      this.newButton_Add.FlatBorderSize = 1;
      this.newButton_Add.FlatStyle = FlatStyle.Flat;
      this.newButton_Add.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Add.Image = (Image) Resources.Add;
      this.newButton_Add.ImageAlign = ContentAlignment.MiddleLeft;
      this.newButton_Add.Location = new Point(5, 480);
      this.newButton_Add.Name = "newButton_Add";
      this.newButton_Add.Size = new Size(60, 32);
      this.newButton_Add.TabIndex = 7;
      this.newButton_Add.TabStop = false;
      this.newButton_Add.TextAlign = ContentAlignment.MiddleRight;
      this.newButton_Add.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Add.NewClick += new EventHandler(this.newButton_NewClick);
      this.newTextBox_Sprue_Length.BackColor = SystemColors.Window;
      this.newTextBox_Sprue_Length.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Sprue_Length.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Sprue_Length.IsDigit = true;
      this.newTextBox_Sprue_Length.Lines = new string[0];
      this.newTextBox_Sprue_Length.Location = new Point(694, 446);
      this.newTextBox_Sprue_Length.MultiLine = false;
      this.newTextBox_Sprue_Length.Name = "newTextBox_Sprue_Length";
      this.newTextBox_Sprue_Length.ReadOnly = false;
      this.newTextBox_Sprue_Length.Size = new Size(76, 23);
      this.newTextBox_Sprue_Length.TabIndex = 81;
      this.newTextBox_Sprue_Length.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Sprue_Length.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Sprue_Length.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Sprue_Length.Value = "";
      this.newTextBox_Sprue_Dim1.BackColor = SystemColors.Window;
      this.newTextBox_Sprue_Dim1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Sprue_Dim1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Sprue_Dim1.IsDigit = true;
      this.newTextBox_Sprue_Dim1.Lines = new string[0];
      this.newTextBox_Sprue_Dim1.Location = new Point(694, 467);
      this.newTextBox_Sprue_Dim1.MultiLine = false;
      this.newTextBox_Sprue_Dim1.Name = "newTextBox_Sprue_Dim1";
      this.newTextBox_Sprue_Dim1.ReadOnly = false;
      this.newTextBox_Sprue_Dim1.Size = new Size(76, 23);
      this.newTextBox_Sprue_Dim1.TabIndex = 82;
      this.newTextBox_Sprue_Dim1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Sprue_Dim1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Sprue_Dim1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Sprue_Dim1.Value = "";
      this.newTextBox_Sprue_Dim2.BackColor = SystemColors.Window;
      this.newTextBox_Sprue_Dim2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Sprue_Dim2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Sprue_Dim2.IsDigit = true;
      this.newTextBox_Sprue_Dim2.Lines = new string[0];
      this.newTextBox_Sprue_Dim2.Location = new Point(694, 489);
      this.newTextBox_Sprue_Dim2.MultiLine = false;
      this.newTextBox_Sprue_Dim2.Name = "newTextBox_Sprue_Dim2";
      this.newTextBox_Sprue_Dim2.ReadOnly = false;
      this.newTextBox_Sprue_Dim2.Size = new Size(76, 23);
      this.newTextBox_Sprue_Dim2.TabIndex = 83;
      this.newTextBox_Sprue_Dim2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Sprue_Dim2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Sprue_Dim2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Sprue_Dim2.Value = "";
      this.newTextBox_Hor_Two_Dia.BackColor = SystemColors.Window;
      this.newTextBox_Hor_Two_Dia.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Hor_Two_Dia.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Hor_Two_Dia.IsDigit = true;
      this.newTextBox_Hor_Two_Dia.Lines = new string[0];
      this.newTextBox_Hor_Two_Dia.Location = new Point(153, 48);
      this.newTextBox_Hor_Two_Dia.MultiLine = false;
      this.newTextBox_Hor_Two_Dia.Name = "newTextBox_Hor_Two_Dia";
      this.newTextBox_Hor_Two_Dia.ReadOnly = false;
      this.newTextBox_Hor_Two_Dia.Size = new Size(76, 23);
      this.newTextBox_Hor_Two_Dia.TabIndex = 47;
      this.newTextBox_Hor_Two_Dia.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Hor_Two_Dia.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Hor_Two_Dia.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Hor_Two_Dia.Value = "";
      this.newTextBox_Hor_Two_Length.BackColor = SystemColors.Window;
      this.newTextBox_Hor_Two_Length.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Hor_Two_Length.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Hor_Two_Length.IsDigit = true;
      this.newTextBox_Hor_Two_Length.Lines = new string[0];
      this.newTextBox_Hor_Two_Length.Location = new Point(153, 70);
      this.newTextBox_Hor_Two_Length.MultiLine = false;
      this.newTextBox_Hor_Two_Length.Name = "newTextBox_Hor_Two_Length";
      this.newTextBox_Hor_Two_Length.ReadOnly = false;
      this.newTextBox_Hor_Two_Length.Size = new Size(76, 23);
      this.newTextBox_Hor_Two_Length.TabIndex = 48;
      this.newTextBox_Hor_Two_Length.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Hor_Two_Length.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Hor_Two_Length.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Hor_Two_Length.Value = "";
      this.newTextBox_Cavity_Occ.BackColor = SystemColors.Window;
      this.newTextBox_Cavity_Occ.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Cavity_Occ.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Cavity_Occ.IsDigit = true;
      this.newTextBox_Cavity_Occ.Lines = new string[0];
      this.newTextBox_Cavity_Occ.Location = new Point(462, 245);
      this.newTextBox_Cavity_Occ.MultiLine = false;
      this.newTextBox_Cavity_Occ.Name = "newTextBox_Cavity_Occ";
      this.newTextBox_Cavity_Occ.ReadOnly = false;
      this.newTextBox_Cavity_Occ.Size = new Size(76, 23);
      this.newTextBox_Cavity_Occ.TabIndex = 41;
      this.newTextBox_Cavity_Occ.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Cavity_Occ.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Cavity_Occ.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Cavity_Occ.Value = "";
      this.newTextBox_Option.BackColor = SystemColors.Window;
      this.newTextBox_Option.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Option.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Option.IsDigit = false;
      this.newTextBox_Option.Lines = new string[0];
      this.newTextBox_Option.Location = new Point(205, 456);
      this.newTextBox_Option.MultiLine = false;
      this.newTextBox_Option.Name = "newTextBox_Option";
      this.newTextBox_Option.ReadOnly = false;
      this.newTextBox_Option.Size = new Size(101, 23);
      this.newTextBox_Option.TabIndex = 6;
      this.newTextBox_Option.TextAlign = HorizontalAlignment.Left;
      this.newTextBox_Option.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Option.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Option.Value = "";
      this.newTextBox_Item.BackColor = SystemColors.Window;
      this.newTextBox_Item.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Item.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Item.IsDigit = false;
      this.newTextBox_Item.Lines = new string[0];
      this.newTextBox_Item.Location = new Point(105, 456);
      this.newTextBox_Item.MultiLine = false;
      this.newTextBox_Item.Name = "newTextBox_Item";
      this.newTextBox_Item.ReadOnly = false;
      this.newTextBox_Item.Size = new Size(101, 23);
      this.newTextBox_Item.TabIndex = 5;
      this.newTextBox_Item.TextAlign = HorizontalAlignment.Left;
      this.newTextBox_Item.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Item.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Item.Value = "";
      this.newTextBox_Company.BackColor = SystemColors.Window;
      this.newTextBox_Company.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Company.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Company.IsDigit = false;
      this.newTextBox_Company.Lines = new string[0];
      this.newTextBox_Company.Location = new Point(5, 456);
      this.newTextBox_Company.MultiLine = false;
      this.newTextBox_Company.Name = "newTextBox_Company";
      this.newTextBox_Company.ReadOnly = false;
      this.newTextBox_Company.Size = new Size(101, 23);
      this.newTextBox_Company.TabIndex = 4;
      this.newTextBox_Company.TextAlign = HorizontalAlignment.Left;
      this.newTextBox_Company.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Company.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Company.Value = "";
      this.newComboBox_Item.BackColor = Color.White;
      this.newComboBox_Item.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Item.ComboBoxBackColor = Color.White;
      this.newComboBox_Item.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Item.isSameSelect = false;
      this.newComboBox_Item.Location = new Point(155, 46);
      this.newComboBox_Item.Name = "newComboBox_Item";
      this.newComboBox_Item.SelectedIndex = -1;
      this.newComboBox_Item.Size = new Size(151, 23);
      this.newComboBox_Item.TabIndex = 2;
      this.newComboBox_Item.TabStop = false;
      this.newComboBox_Item.TextAlign = HorizontalAlignment.Center;
      this.newComboBox_Item.Value = "";
      this.newComboBox_Item.SelectedIndexChanged += new EventHandler(this.newComboBox_Item_SelectedIndexChanged);
      this.newComboBox_Company.BackColor = Color.White;
      this.newComboBox_Company.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Company.ComboBoxBackColor = Color.White;
      this.newComboBox_Company.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Company.isSameSelect = false;
      this.newComboBox_Company.Location = new Point(5, 46);
      this.newComboBox_Company.Name = "newComboBox_Company";
      this.newComboBox_Company.SelectedIndex = -1;
      this.newComboBox_Company.Size = new Size(151, 23);
      this.newComboBox_Company.TabIndex = 1;
      this.newComboBox_Company.TabStop = false;
      this.newComboBox_Company.TextAlign = HorizontalAlignment.Center;
      this.newComboBox_Company.Value = "";
      this.newComboBox_Company.SelectedIndexChanged += new EventHandler(this.newComboBox_Company_SelectedIndexChanged);
      this.newTextBox_Hor_Two_Dim1.BackColor = SystemColors.Window;
      this.newTextBox_Hor_Two_Dim1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Hor_Two_Dim1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Hor_Two_Dim1.IsDigit = true;
      this.newTextBox_Hor_Two_Dim1.Lines = new string[0];
      this.newTextBox_Hor_Two_Dim1.Location = new Point(153, 136);
      this.newTextBox_Hor_Two_Dim1.MultiLine = false;
      this.newTextBox_Hor_Two_Dim1.Name = "newTextBox_Hor_Two_Dim1";
      this.newTextBox_Hor_Two_Dim1.ReadOnly = false;
      this.newTextBox_Hor_Two_Dim1.Size = new Size(76, 23);
      this.newTextBox_Hor_Two_Dim1.TabIndex = 50;
      this.newTextBox_Hor_Two_Dim1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Hor_Two_Dim1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Hor_Two_Dim1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Hor_Two_Dim1.Value = "";
      this.newTextBox_Hor_Two_Dim2.BackColor = SystemColors.Window;
      this.newTextBox_Hor_Two_Dim2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Hor_Two_Dim2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Hor_Two_Dim2.IsDigit = true;
      this.newTextBox_Hor_Two_Dim2.Lines = new string[0];
      this.newTextBox_Hor_Two_Dim2.Location = new Point(153, 158);
      this.newTextBox_Hor_Two_Dim2.MultiLine = false;
      this.newTextBox_Hor_Two_Dim2.Name = "newTextBox_Hor_Two_Dim2";
      this.newTextBox_Hor_Two_Dim2.ReadOnly = false;
      this.newTextBox_Hor_Two_Dim2.Size = new Size(76, 23);
      this.newTextBox_Hor_Two_Dim2.TabIndex = 51;
      this.newTextBox_Hor_Two_Dim2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Hor_Two_Dim2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Hor_Two_Dim2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Hor_Two_Dim2.Value = "";
      this.label_Hor_Two_Dim2.BackColor = Color.Lavender;
      this.label_Hor_Two_Dim2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Hor_Two_Dim2.Location = new Point(1, 158);
      this.label_Hor_Two_Dim2.Name = "label_Hor_Two_Dim2";
      this.label_Hor_Two_Dim2.Size = new Size(153, 23);
      this.label_Hor_Two_Dim2.TabIndex = 40;
      this.label_Hor_Two_Dim2.Text = "[ Y ] 길이";
      this.label_Hor_Two_Dim2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Hor_Two_Dim1.BackColor = Color.Lavender;
      this.label_Hor_Two_Dim1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Hor_Two_Dim1.Location = new Point(1, 136);
      this.label_Hor_Two_Dim1.Name = "label_Hor_Two_Dim1";
      this.label_Hor_Two_Dim1.Size = new Size(153, 23);
      this.label_Hor_Two_Dim1.TabIndex = 41;
      this.label_Hor_Two_Dim1.Text = "[ X ] 길이";
      this.label_Hor_Two_Dim1.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Hor_Two_Angle.BackColor = SystemColors.Window;
      this.newTextBox_Hor_Two_Angle.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Hor_Two_Angle.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Hor_Two_Angle.IsDigit = true;
      this.newTextBox_Hor_Two_Angle.Lines = new string[0];
      this.newTextBox_Hor_Two_Angle.Location = new Point(153, 180);
      this.newTextBox_Hor_Two_Angle.MultiLine = false;
      this.newTextBox_Hor_Two_Angle.Name = "newTextBox_Hor_Two_Angle";
      this.newTextBox_Hor_Two_Angle.ReadOnly = false;
      this.newTextBox_Hor_Two_Angle.Size = new Size(76, 23);
      this.newTextBox_Hor_Two_Angle.TabIndex = 52;
      this.newTextBox_Hor_Two_Angle.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Hor_Two_Angle.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Hor_Two_Angle.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Hor_Two_Angle.Value = "";
      this.label_Hor_Two_Angle.BackColor = Color.Lavender;
      this.label_Hor_Two_Angle.BorderStyle = BorderStyle.FixedSingle;
      this.label_Hor_Two_Angle.Location = new Point(1, 180);
      this.label_Hor_Two_Angle.Name = "label_Hor_Two_Angle";
      this.label_Hor_Two_Angle.Size = new Size(153, 23);
      this.label_Hor_Two_Angle.TabIndex = 44;
      this.label_Hor_Two_Angle.Text = "∠ 각도";
      this.label_Hor_Two_Angle.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Hor_Two_Direction.BackColor = Color.Lavender;
      this.label_Hor_Two_Direction.BorderStyle = BorderStyle.FixedSingle;
      this.label_Hor_Two_Direction.Location = new Point(1, 92);
      this.label_Hor_Two_Direction.Name = "label_Hor_Two_Direction";
      this.label_Hor_Two_Direction.Size = new Size(153, 23);
      this.label_Hor_Two_Direction.TabIndex = 46;
      this.label_Hor_Two_Direction.Text = "수평 런너 방향";
      this.label_Hor_Two_Direction.TextAlign = ContentAlignment.MiddleCenter;
      this.newComboBox_Hor_Two_Direction.BackColor = Color.White;
      this.newComboBox_Hor_Two_Direction.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Hor_Two_Direction.ComboBoxBackColor = Color.White;
      this.newComboBox_Hor_Two_Direction.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Hor_Two_Direction.isSameSelect = false;
      this.newComboBox_Hor_Two_Direction.Location = new Point(153, 92);
      this.newComboBox_Hor_Two_Direction.Name = "newComboBox_Hor_Two_Direction";
      this.newComboBox_Hor_Two_Direction.SelectedIndex = -1;
      this.newComboBox_Hor_Two_Direction.Size = new Size(76, 23);
      this.newComboBox_Hor_Two_Direction.TabIndex = 49;
      this.newComboBox_Hor_Two_Direction.TabStop = false;
      this.newComboBox_Hor_Two_Direction.TextAlign = HorizontalAlignment.Center;
      this.newComboBox_Hor_Two_Direction.Value = "";
      this.panel_Pin_Group.Controls.Add((Control) this.newButton_Pin_G1);
      this.panel_Pin_Group.Controls.Add((Control) this.newButton_Pin_G4);
      this.panel_Pin_Group.Controls.Add((Control) this.newButton_Pin_G3);
      this.panel_Pin_Group.Controls.Add((Control) this.newButton_Pin_G2);
      this.panel_Pin_Group.Controls.Add((Control) this.tabControl_Pin_Group);
      this.panel_Pin_Group.Location = new Point(310, 52);
      this.panel_Pin_Group.Name = "panel_Pin_Group";
      this.panel_Pin_Group.Size = new Size(228, 172);
      this.panel_Pin_Group.TabIndex = 15;
      this.newButton_Pin_G1.ButtonBackColor = Color.LightSteelBlue;
      this.newButton_Pin_G1.ButtonText = "그룹1";
      this.newButton_Pin_G1.FlatBorderSize = 1;
      this.newButton_Pin_G1.FlatStyle = FlatStyle.Flat;
      this.newButton_Pin_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Pin_G1.Image = (Image) null;
      this.newButton_Pin_G1.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Pin_G1.Location = new Point(0, 0);
      this.newButton_Pin_G1.Name = "newButton_Pin_G1";
      this.newButton_Pin_G1.Size = new Size(58, 23);
      this.newButton_Pin_G1.TabIndex = 16;
      this.newButton_Pin_G1.TabStop = false;
      this.newButton_Pin_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Pin_G1.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Pin_G1.NewClick += new EventHandler(this.newButton_Group_NewClick);
      this.newButton_Pin_G4.ButtonBackColor = Color.White;
      this.newButton_Pin_G4.ButtonText = "그룹4";
      this.newButton_Pin_G4.FlatBorderSize = 1;
      this.newButton_Pin_G4.FlatStyle = FlatStyle.Flat;
      this.newButton_Pin_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Pin_G4.Image = (Image) null;
      this.newButton_Pin_G4.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Pin_G4.Location = new Point(170, 0);
      this.newButton_Pin_G4.Name = "newButton_Pin_G4";
      this.newButton_Pin_G4.Size = new Size(58, 23);
      this.newButton_Pin_G4.TabIndex = 19;
      this.newButton_Pin_G4.TabStop = false;
      this.newButton_Pin_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Pin_G4.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Pin_G4.NewClick += new EventHandler(this.newButton_Group_NewClick);
      this.newButton_Pin_G3.ButtonBackColor = Color.White;
      this.newButton_Pin_G3.ButtonText = "그룹3";
      this.newButton_Pin_G3.FlatBorderSize = 1;
      this.newButton_Pin_G3.FlatStyle = FlatStyle.Flat;
      this.newButton_Pin_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Pin_G3.Image = (Image) null;
      this.newButton_Pin_G3.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Pin_G3.Location = new Point(113, 0);
      this.newButton_Pin_G3.Name = "newButton_Pin_G3";
      this.newButton_Pin_G3.Size = new Size(58, 23);
      this.newButton_Pin_G3.TabIndex = 18;
      this.newButton_Pin_G3.TabStop = false;
      this.newButton_Pin_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Pin_G3.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Pin_G3.NewClick += new EventHandler(this.newButton_Group_NewClick);
      this.newButton_Pin_G2.ButtonBackColor = Color.White;
      this.newButton_Pin_G2.ButtonText = "그룹2";
      this.newButton_Pin_G2.FlatBorderSize = 1;
      this.newButton_Pin_G2.FlatStyle = FlatStyle.Flat;
      this.newButton_Pin_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Pin_G2.Image = (Image) null;
      this.newButton_Pin_G2.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Pin_G2.Location = new Point(57, 0);
      this.newButton_Pin_G2.Name = "newButton_Pin_G2";
      this.newButton_Pin_G2.Size = new Size(58, 23);
      this.newButton_Pin_G2.TabIndex = 17;
      this.newButton_Pin_G2.TabStop = false;
      this.newButton_Pin_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Pin_G2.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Pin_G2.NewClick += new EventHandler(this.newButton_Group_NewClick);
      this.tabControl_Pin_Group.Controls.Add((Control) this.tabPage_Pin_Group1);
      this.tabControl_Pin_Group.Controls.Add((Control) this.tabPage_Pin_Group2);
      this.tabControl_Pin_Group.Controls.Add((Control) this.tabPage_Pin_Group3);
      this.tabControl_Pin_Group.Controls.Add((Control) this.tabPage_Pin_Group4);
      this.tabControl_Pin_Group.Location = new Point(-5, -5);
      this.tabControl_Pin_Group.Name = "tabControl_Pin_Group";
      this.tabControl_Pin_Group.SelectedIndex = 0;
      this.tabControl_Pin_Group.Size = new Size(237, 180);
      this.tabControl_Pin_Group.TabIndex = 20;
      this.tabPage_Pin_Group1.Controls.Add((Control) this.label_Pin_Gate_G1);
      this.tabPage_Pin_Group1.Controls.Add((Control) this.label_Pin_Runner_G1);
      this.tabPage_Pin_Group1.Controls.Add((Control) this.label_Pin_GateDim1_G1);
      this.tabPage_Pin_Group1.Controls.Add((Control) this.label_Pin_RunnerLength_G1);
      this.tabPage_Pin_Group1.Controls.Add((Control) this.label_Pin_RunnerDim1_G1);
      this.tabPage_Pin_Group1.Controls.Add((Control) this.label_Pin_GateDim2_G1);
      this.tabPage_Pin_Group1.Controls.Add((Control) this.label_Pin_RunnerDim2_G1);
      this.tabPage_Pin_Group1.Controls.Add((Control) this.newTextBox_Pin_GateDim1_G1);
      this.tabPage_Pin_Group1.Controls.Add((Control) this.newTextBox_Pin_GateDim2_G1);
      this.tabPage_Pin_Group1.Controls.Add((Control) this.newTextBox_Pin_RunnerLength_G1);
      this.tabPage_Pin_Group1.Controls.Add((Control) this.newTextBox_Pin_RunnerDim1_G1);
      this.tabPage_Pin_Group1.Controls.Add((Control) this.newTextBox_Pin_RunnerDim2_G1);
      this.tabPage_Pin_Group1.Location = new Point(4, 24);
      this.tabPage_Pin_Group1.Name = "tabPage_Pin_Group1";
      this.tabPage_Pin_Group1.Padding = new Padding(3);
      this.tabPage_Pin_Group1.Size = new Size(229, 152);
      this.tabPage_Pin_Group1.TabIndex = 0;
      this.tabPage_Pin_Group1.UseVisualStyleBackColor = true;
      this.label_Pin_Gate_G1.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Pin_Gate_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_Gate_G1.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Pin_Gate_G1.ForeColor = Color.MidnightBlue;
      this.label_Pin_Gate_G1.Location = new Point(1, 3);
      this.label_Pin_Gate_G1.Name = "label_Pin_Gate_G1";
      this.label_Pin_Gate_G1.Size = new Size(228, 20);
      this.label_Pin_Gate_G1.TabIndex = 100;
      this.label_Pin_Gate_G1.Text = "<게이트>";
      this.label_Pin_Gate_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_Runner_G1.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Pin_Runner_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_Runner_G1.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Pin_Runner_G1.ForeColor = Color.MidnightBlue;
      this.label_Pin_Runner_G1.Location = new Point(1, 66);
      this.label_Pin_Runner_G1.Name = "label_Pin_Runner_G1";
      this.label_Pin_Runner_G1.Size = new Size(228, 20);
      this.label_Pin_Runner_G1.TabIndex = 99;
      this.label_Pin_Runner_G1.Text = "<런너>";
      this.label_Pin_Runner_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_GateDim1_G1.BackColor = Color.Lavender;
      this.label_Pin_GateDim1_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_GateDim1_G1.Location = new Point(1, 22);
      this.label_Pin_GateDim1_G1.Name = "label_Pin_GateDim1_G1";
      this.label_Pin_GateDim1_G1.Size = new Size(153, 23);
      this.label_Pin_GateDim1_G1.TabIndex = 104;
      this.label_Pin_GateDim1_G1.Text = "Start Dimension";
      this.label_Pin_GateDim1_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_RunnerLength_G1.BackColor = Color.Lavender;
      this.label_Pin_RunnerLength_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_RunnerLength_G1.Location = new Point(1, 85);
      this.label_Pin_RunnerLength_G1.Name = "label_Pin_RunnerLength_G1";
      this.label_Pin_RunnerLength_G1.Size = new Size(153, 23);
      this.label_Pin_RunnerLength_G1.TabIndex = 102;
      this.label_Pin_RunnerLength_G1.Text = "런너 길이";
      this.label_Pin_RunnerLength_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_RunnerDim1_G1.BackColor = Color.Lavender;
      this.label_Pin_RunnerDim1_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_RunnerDim1_G1.Location = new Point(1, 107);
      this.label_Pin_RunnerDim1_G1.Name = "label_Pin_RunnerDim1_G1";
      this.label_Pin_RunnerDim1_G1.Size = new Size(153, 23);
      this.label_Pin_RunnerDim1_G1.TabIndex = 103;
      this.label_Pin_RunnerDim1_G1.Text = "Inner Dimension";
      this.label_Pin_RunnerDim1_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_GateDim2_G1.BackColor = Color.Lavender;
      this.label_Pin_GateDim2_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_GateDim2_G1.Location = new Point(1, 44);
      this.label_Pin_GateDim2_G1.Name = "label_Pin_GateDim2_G1";
      this.label_Pin_GateDim2_G1.Size = new Size(153, 23);
      this.label_Pin_GateDim2_G1.TabIndex = 101;
      this.label_Pin_GateDim2_G1.Text = "End Dimension";
      this.label_Pin_GateDim2_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_RunnerDim2_G1.BackColor = Color.Lavender;
      this.label_Pin_RunnerDim2_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_RunnerDim2_G1.Location = new Point(1, 129);
      this.label_Pin_RunnerDim2_G1.Name = "label_Pin_RunnerDim2_G1";
      this.label_Pin_RunnerDim2_G1.Size = new Size(153, 23);
      this.label_Pin_RunnerDim2_G1.TabIndex = 105;
      this.label_Pin_RunnerDim2_G1.Text = "Outer Dimension";
      this.label_Pin_RunnerDim2_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Pin_GateDim1_G1.BackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim1_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_GateDim1_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_GateDim1_G1.IsDigit = true;
      this.newTextBox_Pin_GateDim1_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_GateDim1_G1.Location = new Point(153, 22);
      this.newTextBox_Pin_GateDim1_G1.MultiLine = false;
      this.newTextBox_Pin_GateDim1_G1.Name = "newTextBox_Pin_GateDim1_G1";
      this.newTextBox_Pin_GateDim1_G1.ReadOnly = false;
      this.newTextBox_Pin_GateDim1_G1.Size = new Size(76, 23);
      this.newTextBox_Pin_GateDim1_G1.TabIndex = 21;
      this.newTextBox_Pin_GateDim1_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_GateDim1_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim1_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_GateDim1_G1.Value = "0";
      this.newTextBox_Pin_GateDim2_G1.BackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim2_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_GateDim2_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_GateDim2_G1.IsDigit = true;
      this.newTextBox_Pin_GateDim2_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_GateDim2_G1.Location = new Point(153, 44);
      this.newTextBox_Pin_GateDim2_G1.MultiLine = false;
      this.newTextBox_Pin_GateDim2_G1.Name = "newTextBox_Pin_GateDim2_G1";
      this.newTextBox_Pin_GateDim2_G1.ReadOnly = false;
      this.newTextBox_Pin_GateDim2_G1.Size = new Size(76, 23);
      this.newTextBox_Pin_GateDim2_G1.TabIndex = 22;
      this.newTextBox_Pin_GateDim2_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_GateDim2_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim2_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_GateDim2_G1.Value = "0";
      this.newTextBox_Pin_RunnerLength_G1.BackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerLength_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_RunnerLength_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_RunnerLength_G1.IsDigit = true;
      this.newTextBox_Pin_RunnerLength_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_RunnerLength_G1.Location = new Point(153, 85);
      this.newTextBox_Pin_RunnerLength_G1.MultiLine = false;
      this.newTextBox_Pin_RunnerLength_G1.Name = "newTextBox_Pin_RunnerLength_G1";
      this.newTextBox_Pin_RunnerLength_G1.ReadOnly = false;
      this.newTextBox_Pin_RunnerLength_G1.Size = new Size(76, 23);
      this.newTextBox_Pin_RunnerLength_G1.TabIndex = 23;
      this.newTextBox_Pin_RunnerLength_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_RunnerLength_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerLength_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_RunnerLength_G1.Value = "0";
      this.newTextBox_Pin_RunnerDim1_G1.BackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim1_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_RunnerDim1_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_RunnerDim1_G1.IsDigit = true;
      this.newTextBox_Pin_RunnerDim1_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_RunnerDim1_G1.Location = new Point(153, 107);
      this.newTextBox_Pin_RunnerDim1_G1.MultiLine = false;
      this.newTextBox_Pin_RunnerDim1_G1.Name = "newTextBox_Pin_RunnerDim1_G1";
      this.newTextBox_Pin_RunnerDim1_G1.ReadOnly = false;
      this.newTextBox_Pin_RunnerDim1_G1.Size = new Size(76, 23);
      this.newTextBox_Pin_RunnerDim1_G1.TabIndex = 24;
      this.newTextBox_Pin_RunnerDim1_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_RunnerDim1_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim1_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_RunnerDim1_G1.Value = "0";
      this.newTextBox_Pin_RunnerDim2_G1.BackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim2_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_RunnerDim2_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_RunnerDim2_G1.IsDigit = true;
      this.newTextBox_Pin_RunnerDim2_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_RunnerDim2_G1.Location = new Point(153, 129);
      this.newTextBox_Pin_RunnerDim2_G1.MultiLine = false;
      this.newTextBox_Pin_RunnerDim2_G1.Name = "newTextBox_Pin_RunnerDim2_G1";
      this.newTextBox_Pin_RunnerDim2_G1.ReadOnly = false;
      this.newTextBox_Pin_RunnerDim2_G1.Size = new Size(76, 23);
      this.newTextBox_Pin_RunnerDim2_G1.TabIndex = 25;
      this.newTextBox_Pin_RunnerDim2_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_RunnerDim2_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim2_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_RunnerDim2_G1.Value = "0";
      this.tabPage_Pin_Group2.Controls.Add((Control) this.label_Pin_Gate_G2);
      this.tabPage_Pin_Group2.Controls.Add((Control) this.label_Pin_Runner_G2);
      this.tabPage_Pin_Group2.Controls.Add((Control) this.label_Pin_GateDim1_G2);
      this.tabPage_Pin_Group2.Controls.Add((Control) this.label_Pin_RunnerLength_G2);
      this.tabPage_Pin_Group2.Controls.Add((Control) this.label_Pin_RunnerDim1_G2);
      this.tabPage_Pin_Group2.Controls.Add((Control) this.label_Pin_GateDim2_G2);
      this.tabPage_Pin_Group2.Controls.Add((Control) this.label_Pin_RunnerDim2_G2);
      this.tabPage_Pin_Group2.Controls.Add((Control) this.newTextBox_Pin_GateDim1_G2);
      this.tabPage_Pin_Group2.Controls.Add((Control) this.newTextBox_Pin_GateDim2_G2);
      this.tabPage_Pin_Group2.Controls.Add((Control) this.newTextBox_Pin_RunnerLength_G2);
      this.tabPage_Pin_Group2.Controls.Add((Control) this.newTextBox_Pin_RunnerDim1_G2);
      this.tabPage_Pin_Group2.Controls.Add((Control) this.newTextBox_Pin_RunnerDim2_G2);
      this.tabPage_Pin_Group2.Location = new Point(4, 24);
      this.tabPage_Pin_Group2.Name = "tabPage_Pin_Group2";
      this.tabPage_Pin_Group2.Padding = new Padding(3);
      this.tabPage_Pin_Group2.Size = new Size(229, 152);
      this.tabPage_Pin_Group2.TabIndex = 1;
      this.tabPage_Pin_Group2.UseVisualStyleBackColor = true;
      this.label_Pin_Gate_G2.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Pin_Gate_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_Gate_G2.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Pin_Gate_G2.ForeColor = Color.MidnightBlue;
      this.label_Pin_Gate_G2.Location = new Point(1, 3);
      this.label_Pin_Gate_G2.Name = "label_Pin_Gate_G2";
      this.label_Pin_Gate_G2.Size = new Size(228, 20);
      this.label_Pin_Gate_G2.TabIndex = 114;
      this.label_Pin_Gate_G2.Text = "<게이트>";
      this.label_Pin_Gate_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_Runner_G2.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Pin_Runner_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_Runner_G2.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Pin_Runner_G2.ForeColor = Color.MidnightBlue;
      this.label_Pin_Runner_G2.Location = new Point(1, 66);
      this.label_Pin_Runner_G2.Name = "label_Pin_Runner_G2";
      this.label_Pin_Runner_G2.Size = new Size(228, 20);
      this.label_Pin_Runner_G2.TabIndex = 113;
      this.label_Pin_Runner_G2.Text = "<런너>";
      this.label_Pin_Runner_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_GateDim1_G2.BackColor = Color.Lavender;
      this.label_Pin_GateDim1_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_GateDim1_G2.Location = new Point(1, 22);
      this.label_Pin_GateDim1_G2.Name = "label_Pin_GateDim1_G2";
      this.label_Pin_GateDim1_G2.Size = new Size(153, 23);
      this.label_Pin_GateDim1_G2.TabIndex = 118;
      this.label_Pin_GateDim1_G2.Text = "Start Dimension";
      this.label_Pin_GateDim1_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_RunnerLength_G2.BackColor = Color.Lavender;
      this.label_Pin_RunnerLength_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_RunnerLength_G2.Location = new Point(1, 85);
      this.label_Pin_RunnerLength_G2.Name = "label_Pin_RunnerLength_G2";
      this.label_Pin_RunnerLength_G2.Size = new Size(153, 23);
      this.label_Pin_RunnerLength_G2.TabIndex = 116;
      this.label_Pin_RunnerLength_G2.Text = "런너 길이";
      this.label_Pin_RunnerLength_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_RunnerDim1_G2.BackColor = Color.Lavender;
      this.label_Pin_RunnerDim1_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_RunnerDim1_G2.Location = new Point(1, 107);
      this.label_Pin_RunnerDim1_G2.Name = "label_Pin_RunnerDim1_G2";
      this.label_Pin_RunnerDim1_G2.Size = new Size(153, 23);
      this.label_Pin_RunnerDim1_G2.TabIndex = 117;
      this.label_Pin_RunnerDim1_G2.Text = "Inner Dimension";
      this.label_Pin_RunnerDim1_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_GateDim2_G2.BackColor = Color.Lavender;
      this.label_Pin_GateDim2_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_GateDim2_G2.Location = new Point(1, 44);
      this.label_Pin_GateDim2_G2.Name = "label_Pin_GateDim2_G2";
      this.label_Pin_GateDim2_G2.Size = new Size(153, 23);
      this.label_Pin_GateDim2_G2.TabIndex = 115;
      this.label_Pin_GateDim2_G2.Text = "End Dimension";
      this.label_Pin_GateDim2_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_RunnerDim2_G2.BackColor = Color.Lavender;
      this.label_Pin_RunnerDim2_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_RunnerDim2_G2.Location = new Point(1, 129);
      this.label_Pin_RunnerDim2_G2.Name = "label_Pin_RunnerDim2_G2";
      this.label_Pin_RunnerDim2_G2.Size = new Size(153, 23);
      this.label_Pin_RunnerDim2_G2.TabIndex = 119;
      this.label_Pin_RunnerDim2_G2.Text = "Outer Dimension";
      this.label_Pin_RunnerDim2_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Pin_GateDim1_G2.BackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim1_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_GateDim1_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_GateDim1_G2.IsDigit = true;
      this.newTextBox_Pin_GateDim1_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_GateDim1_G2.Location = new Point(153, 22);
      this.newTextBox_Pin_GateDim1_G2.MultiLine = false;
      this.newTextBox_Pin_GateDim1_G2.Name = "newTextBox_Pin_GateDim1_G2";
      this.newTextBox_Pin_GateDim1_G2.ReadOnly = false;
      this.newTextBox_Pin_GateDim1_G2.Size = new Size(76, 23);
      this.newTextBox_Pin_GateDim1_G2.TabIndex = 26;
      this.newTextBox_Pin_GateDim1_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_GateDim1_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim1_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_GateDim1_G2.Value = "0";
      this.newTextBox_Pin_GateDim2_G2.BackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim2_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_GateDim2_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_GateDim2_G2.IsDigit = true;
      this.newTextBox_Pin_GateDim2_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_GateDim2_G2.Location = new Point(153, 44);
      this.newTextBox_Pin_GateDim2_G2.MultiLine = false;
      this.newTextBox_Pin_GateDim2_G2.Name = "newTextBox_Pin_GateDim2_G2";
      this.newTextBox_Pin_GateDim2_G2.ReadOnly = false;
      this.newTextBox_Pin_GateDim2_G2.Size = new Size(76, 23);
      this.newTextBox_Pin_GateDim2_G2.TabIndex = 27;
      this.newTextBox_Pin_GateDim2_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_GateDim2_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim2_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_GateDim2_G2.Value = "0";
      this.newTextBox_Pin_RunnerLength_G2.BackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerLength_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_RunnerLength_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_RunnerLength_G2.IsDigit = true;
      this.newTextBox_Pin_RunnerLength_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_RunnerLength_G2.Location = new Point(153, 85);
      this.newTextBox_Pin_RunnerLength_G2.MultiLine = false;
      this.newTextBox_Pin_RunnerLength_G2.Name = "newTextBox_Pin_RunnerLength_G2";
      this.newTextBox_Pin_RunnerLength_G2.ReadOnly = false;
      this.newTextBox_Pin_RunnerLength_G2.Size = new Size(76, 23);
      this.newTextBox_Pin_RunnerLength_G2.TabIndex = 28;
      this.newTextBox_Pin_RunnerLength_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_RunnerLength_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerLength_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_RunnerLength_G2.Value = "0";
      this.newTextBox_Pin_RunnerDim1_G2.BackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim1_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_RunnerDim1_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_RunnerDim1_G2.IsDigit = true;
      this.newTextBox_Pin_RunnerDim1_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_RunnerDim1_G2.Location = new Point(153, 107);
      this.newTextBox_Pin_RunnerDim1_G2.MultiLine = false;
      this.newTextBox_Pin_RunnerDim1_G2.Name = "newTextBox_Pin_RunnerDim1_G2";
      this.newTextBox_Pin_RunnerDim1_G2.ReadOnly = false;
      this.newTextBox_Pin_RunnerDim1_G2.Size = new Size(76, 23);
      this.newTextBox_Pin_RunnerDim1_G2.TabIndex = 29;
      this.newTextBox_Pin_RunnerDim1_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_RunnerDim1_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim1_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_RunnerDim1_G2.Value = "0";
      this.newTextBox_Pin_RunnerDim2_G2.BackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim2_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_RunnerDim2_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_RunnerDim2_G2.IsDigit = true;
      this.newTextBox_Pin_RunnerDim2_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_RunnerDim2_G2.Location = new Point(153, 129);
      this.newTextBox_Pin_RunnerDim2_G2.MultiLine = false;
      this.newTextBox_Pin_RunnerDim2_G2.Name = "newTextBox_Pin_RunnerDim2_G2";
      this.newTextBox_Pin_RunnerDim2_G2.ReadOnly = false;
      this.newTextBox_Pin_RunnerDim2_G2.Size = new Size(76, 23);
      this.newTextBox_Pin_RunnerDim2_G2.TabIndex = 30;
      this.newTextBox_Pin_RunnerDim2_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_RunnerDim2_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim2_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_RunnerDim2_G2.Value = "0";
      this.tabPage_Pin_Group3.Controls.Add((Control) this.label_Pin_Gate_G3);
      this.tabPage_Pin_Group3.Controls.Add((Control) this.label_Pin_Runner_G3);
      this.tabPage_Pin_Group3.Controls.Add((Control) this.label_Pin_GateDim1_G3);
      this.tabPage_Pin_Group3.Controls.Add((Control) this.label_Pin_RunnerLength_G3);
      this.tabPage_Pin_Group3.Controls.Add((Control) this.label_Pin_RunnerDim1_G3);
      this.tabPage_Pin_Group3.Controls.Add((Control) this.label_Pin_GateDim2_G3);
      this.tabPage_Pin_Group3.Controls.Add((Control) this.label_Pin_RunnerDim2_G3);
      this.tabPage_Pin_Group3.Controls.Add((Control) this.newTextBox_Pin_GateDim1_G3);
      this.tabPage_Pin_Group3.Controls.Add((Control) this.newTextBox_Pin_GateDim2_G3);
      this.tabPage_Pin_Group3.Controls.Add((Control) this.newTextBox_Pin_RunnerLength_G3);
      this.tabPage_Pin_Group3.Controls.Add((Control) this.newTextBox_Pin_RunnerDim1_G3);
      this.tabPage_Pin_Group3.Controls.Add((Control) this.newTextBox_Pin_RunnerDim2_G3);
      this.tabPage_Pin_Group3.Location = new Point(4, 24);
      this.tabPage_Pin_Group3.Name = "tabPage_Pin_Group3";
      this.tabPage_Pin_Group3.Padding = new Padding(3);
      this.tabPage_Pin_Group3.Size = new Size(229, 152);
      this.tabPage_Pin_Group3.TabIndex = 2;
      this.tabPage_Pin_Group3.UseVisualStyleBackColor = true;
      this.label_Pin_Gate_G3.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Pin_Gate_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_Gate_G3.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Pin_Gate_G3.ForeColor = Color.MidnightBlue;
      this.label_Pin_Gate_G3.Location = new Point(1, 3);
      this.label_Pin_Gate_G3.Name = "label_Pin_Gate_G3";
      this.label_Pin_Gate_G3.Size = new Size(228, 20);
      this.label_Pin_Gate_G3.TabIndex = 114;
      this.label_Pin_Gate_G3.Text = "<게이트>";
      this.label_Pin_Gate_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_Runner_G3.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Pin_Runner_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_Runner_G3.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Pin_Runner_G3.ForeColor = Color.MidnightBlue;
      this.label_Pin_Runner_G3.Location = new Point(1, 66);
      this.label_Pin_Runner_G3.Name = "label_Pin_Runner_G3";
      this.label_Pin_Runner_G3.Size = new Size(228, 20);
      this.label_Pin_Runner_G3.TabIndex = 113;
      this.label_Pin_Runner_G3.Text = "<런너>";
      this.label_Pin_Runner_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_GateDim1_G3.BackColor = Color.Lavender;
      this.label_Pin_GateDim1_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_GateDim1_G3.Location = new Point(1, 22);
      this.label_Pin_GateDim1_G3.Name = "label_Pin_GateDim1_G3";
      this.label_Pin_GateDim1_G3.Size = new Size(153, 23);
      this.label_Pin_GateDim1_G3.TabIndex = 118;
      this.label_Pin_GateDim1_G3.Text = "Start Dimension";
      this.label_Pin_GateDim1_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_RunnerLength_G3.BackColor = Color.Lavender;
      this.label_Pin_RunnerLength_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_RunnerLength_G3.Location = new Point(1, 85);
      this.label_Pin_RunnerLength_G3.Name = "label_Pin_RunnerLength_G3";
      this.label_Pin_RunnerLength_G3.Size = new Size(153, 23);
      this.label_Pin_RunnerLength_G3.TabIndex = 116;
      this.label_Pin_RunnerLength_G3.Text = "런너 길이";
      this.label_Pin_RunnerLength_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_RunnerDim1_G3.BackColor = Color.Lavender;
      this.label_Pin_RunnerDim1_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_RunnerDim1_G3.Location = new Point(1, 107);
      this.label_Pin_RunnerDim1_G3.Name = "label_Pin_RunnerDim1_G3";
      this.label_Pin_RunnerDim1_G3.Size = new Size(153, 23);
      this.label_Pin_RunnerDim1_G3.TabIndex = 117;
      this.label_Pin_RunnerDim1_G3.Text = "Inner Dimension";
      this.label_Pin_RunnerDim1_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_GateDim2_G3.BackColor = Color.Lavender;
      this.label_Pin_GateDim2_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_GateDim2_G3.Location = new Point(1, 44);
      this.label_Pin_GateDim2_G3.Name = "label_Pin_GateDim2_G3";
      this.label_Pin_GateDim2_G3.Size = new Size(153, 23);
      this.label_Pin_GateDim2_G3.TabIndex = 115;
      this.label_Pin_GateDim2_G3.Text = "End Dimension";
      this.label_Pin_GateDim2_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_RunnerDim2_G3.BackColor = Color.Lavender;
      this.label_Pin_RunnerDim2_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_RunnerDim2_G3.Location = new Point(1, 129);
      this.label_Pin_RunnerDim2_G3.Name = "label_Pin_RunnerDim2_G3";
      this.label_Pin_RunnerDim2_G3.Size = new Size(153, 23);
      this.label_Pin_RunnerDim2_G3.TabIndex = 119;
      this.label_Pin_RunnerDim2_G3.Text = "Outer Dimension";
      this.label_Pin_RunnerDim2_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Pin_GateDim1_G3.BackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim1_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_GateDim1_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_GateDim1_G3.IsDigit = true;
      this.newTextBox_Pin_GateDim1_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_GateDim1_G3.Location = new Point(153, 22);
      this.newTextBox_Pin_GateDim1_G3.MultiLine = false;
      this.newTextBox_Pin_GateDim1_G3.Name = "newTextBox_Pin_GateDim1_G3";
      this.newTextBox_Pin_GateDim1_G3.ReadOnly = false;
      this.newTextBox_Pin_GateDim1_G3.Size = new Size(76, 23);
      this.newTextBox_Pin_GateDim1_G3.TabIndex = 31;
      this.newTextBox_Pin_GateDim1_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_GateDim1_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim1_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_GateDim1_G3.Value = "0";
      this.newTextBox_Pin_GateDim2_G3.BackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim2_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_GateDim2_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_GateDim2_G3.IsDigit = true;
      this.newTextBox_Pin_GateDim2_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_GateDim2_G3.Location = new Point(153, 44);
      this.newTextBox_Pin_GateDim2_G3.MultiLine = false;
      this.newTextBox_Pin_GateDim2_G3.Name = "newTextBox_Pin_GateDim2_G3";
      this.newTextBox_Pin_GateDim2_G3.ReadOnly = false;
      this.newTextBox_Pin_GateDim2_G3.Size = new Size(76, 23);
      this.newTextBox_Pin_GateDim2_G3.TabIndex = 32;
      this.newTextBox_Pin_GateDim2_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_GateDim2_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim2_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_GateDim2_G3.Value = "0";
      this.newTextBox_Pin_RunnerLength_G3.BackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerLength_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_RunnerLength_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_RunnerLength_G3.IsDigit = true;
      this.newTextBox_Pin_RunnerLength_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_RunnerLength_G3.Location = new Point(153, 85);
      this.newTextBox_Pin_RunnerLength_G3.MultiLine = false;
      this.newTextBox_Pin_RunnerLength_G3.Name = "newTextBox_Pin_RunnerLength_G3";
      this.newTextBox_Pin_RunnerLength_G3.ReadOnly = false;
      this.newTextBox_Pin_RunnerLength_G3.Size = new Size(76, 23);
      this.newTextBox_Pin_RunnerLength_G3.TabIndex = 33;
      this.newTextBox_Pin_RunnerLength_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_RunnerLength_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerLength_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_RunnerLength_G3.Value = "0";
      this.newTextBox_Pin_RunnerDim1_G3.BackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim1_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_RunnerDim1_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_RunnerDim1_G3.IsDigit = true;
      this.newTextBox_Pin_RunnerDim1_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_RunnerDim1_G3.Location = new Point(153, 107);
      this.newTextBox_Pin_RunnerDim1_G3.MultiLine = false;
      this.newTextBox_Pin_RunnerDim1_G3.Name = "newTextBox_Pin_RunnerDim1_G3";
      this.newTextBox_Pin_RunnerDim1_G3.ReadOnly = false;
      this.newTextBox_Pin_RunnerDim1_G3.Size = new Size(76, 23);
      this.newTextBox_Pin_RunnerDim1_G3.TabIndex = 34;
      this.newTextBox_Pin_RunnerDim1_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_RunnerDim1_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim1_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_RunnerDim1_G3.Value = "0";
      this.newTextBox_Pin_RunnerDim2_G3.BackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim2_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_RunnerDim2_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_RunnerDim2_G3.IsDigit = true;
      this.newTextBox_Pin_RunnerDim2_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_RunnerDim2_G3.Location = new Point(153, 129);
      this.newTextBox_Pin_RunnerDim2_G3.MultiLine = false;
      this.newTextBox_Pin_RunnerDim2_G3.Name = "newTextBox_Pin_RunnerDim2_G3";
      this.newTextBox_Pin_RunnerDim2_G3.ReadOnly = false;
      this.newTextBox_Pin_RunnerDim2_G3.Size = new Size(76, 23);
      this.newTextBox_Pin_RunnerDim2_G3.TabIndex = 35;
      this.newTextBox_Pin_RunnerDim2_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_RunnerDim2_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim2_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_RunnerDim2_G3.Value = "0";
      this.tabPage_Pin_Group4.Controls.Add((Control) this.label_Pin_Gate_G4);
      this.tabPage_Pin_Group4.Controls.Add((Control) this.label_Pin_Runner_G4);
      this.tabPage_Pin_Group4.Controls.Add((Control) this.label_Pin_GateDim1_G4);
      this.tabPage_Pin_Group4.Controls.Add((Control) this.label_Pin_RunnerLength_G4);
      this.tabPage_Pin_Group4.Controls.Add((Control) this.label_Pin_RunnerDim1_G4);
      this.tabPage_Pin_Group4.Controls.Add((Control) this.label_Pin_GateDim2_G4);
      this.tabPage_Pin_Group4.Controls.Add((Control) this.label_Pin_RunnerDim2_G4);
      this.tabPage_Pin_Group4.Controls.Add((Control) this.newTextBox_Pin_GateDim1_G4);
      this.tabPage_Pin_Group4.Controls.Add((Control) this.newTextBox_Pin_GateDim2_G4);
      this.tabPage_Pin_Group4.Controls.Add((Control) this.newTextBox_Pin_RunnerLength_G4);
      this.tabPage_Pin_Group4.Controls.Add((Control) this.newTextBox_Pin_RunnerDim1_G4);
      this.tabPage_Pin_Group4.Controls.Add((Control) this.newTextBox_Pin_RunnerDim2_G4);
      this.tabPage_Pin_Group4.Location = new Point(4, 24);
      this.tabPage_Pin_Group4.Name = "tabPage_Pin_Group4";
      this.tabPage_Pin_Group4.Padding = new Padding(3);
      this.tabPage_Pin_Group4.Size = new Size(229, 152);
      this.tabPage_Pin_Group4.TabIndex = 3;
      this.tabPage_Pin_Group4.UseVisualStyleBackColor = true;
      this.label_Pin_Gate_G4.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Pin_Gate_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_Gate_G4.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Pin_Gate_G4.ForeColor = Color.MidnightBlue;
      this.label_Pin_Gate_G4.Location = new Point(1, 3);
      this.label_Pin_Gate_G4.Name = "label_Pin_Gate_G4";
      this.label_Pin_Gate_G4.Size = new Size(228, 20);
      this.label_Pin_Gate_G4.TabIndex = 114;
      this.label_Pin_Gate_G4.Text = "<게이트>";
      this.label_Pin_Gate_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_Runner_G4.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Pin_Runner_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_Runner_G4.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Pin_Runner_G4.ForeColor = Color.MidnightBlue;
      this.label_Pin_Runner_G4.Location = new Point(1, 66);
      this.label_Pin_Runner_G4.Name = "label_Pin_Runner_G4";
      this.label_Pin_Runner_G4.Size = new Size(228, 20);
      this.label_Pin_Runner_G4.TabIndex = 113;
      this.label_Pin_Runner_G4.Text = "<런너>";
      this.label_Pin_Runner_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_GateDim1_G4.BackColor = Color.Lavender;
      this.label_Pin_GateDim1_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_GateDim1_G4.Location = new Point(1, 22);
      this.label_Pin_GateDim1_G4.Name = "label_Pin_GateDim1_G4";
      this.label_Pin_GateDim1_G4.Size = new Size(153, 23);
      this.label_Pin_GateDim1_G4.TabIndex = 118;
      this.label_Pin_GateDim1_G4.Text = "Start Dimension";
      this.label_Pin_GateDim1_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_RunnerLength_G4.BackColor = Color.Lavender;
      this.label_Pin_RunnerLength_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_RunnerLength_G4.Location = new Point(1, 85);
      this.label_Pin_RunnerLength_G4.Name = "label_Pin_RunnerLength_G4";
      this.label_Pin_RunnerLength_G4.Size = new Size(153, 23);
      this.label_Pin_RunnerLength_G4.TabIndex = 116;
      this.label_Pin_RunnerLength_G4.Text = "런너 길이";
      this.label_Pin_RunnerLength_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_RunnerDim1_G4.BackColor = Color.Lavender;
      this.label_Pin_RunnerDim1_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_RunnerDim1_G4.Location = new Point(1, 107);
      this.label_Pin_RunnerDim1_G4.Name = "label_Pin_RunnerDim1_G4";
      this.label_Pin_RunnerDim1_G4.Size = new Size(153, 23);
      this.label_Pin_RunnerDim1_G4.TabIndex = 117;
      this.label_Pin_RunnerDim1_G4.Text = "Inner Dimension";
      this.label_Pin_RunnerDim1_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_GateDim2_G4.BackColor = Color.Lavender;
      this.label_Pin_GateDim2_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_GateDim2_G4.Location = new Point(1, 44);
      this.label_Pin_GateDim2_G4.Name = "label_Pin_GateDim2_G4";
      this.label_Pin_GateDim2_G4.Size = new Size(153, 23);
      this.label_Pin_GateDim2_G4.TabIndex = 115;
      this.label_Pin_GateDim2_G4.Text = "End Dimension";
      this.label_Pin_GateDim2_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Pin_RunnerDim2_G4.BackColor = Color.Lavender;
      this.label_Pin_RunnerDim2_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Pin_RunnerDim2_G4.Location = new Point(1, 129);
      this.label_Pin_RunnerDim2_G4.Name = "label_Pin_RunnerDim2_G4";
      this.label_Pin_RunnerDim2_G4.Size = new Size(153, 23);
      this.label_Pin_RunnerDim2_G4.TabIndex = 119;
      this.label_Pin_RunnerDim2_G4.Text = "Outer Dimension";
      this.label_Pin_RunnerDim2_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Pin_GateDim1_G4.BackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim1_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_GateDim1_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_GateDim1_G4.IsDigit = true;
      this.newTextBox_Pin_GateDim1_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_GateDim1_G4.Location = new Point(153, 22);
      this.newTextBox_Pin_GateDim1_G4.MultiLine = false;
      this.newTextBox_Pin_GateDim1_G4.Name = "newTextBox_Pin_GateDim1_G4";
      this.newTextBox_Pin_GateDim1_G4.ReadOnly = false;
      this.newTextBox_Pin_GateDim1_G4.Size = new Size(76, 23);
      this.newTextBox_Pin_GateDim1_G4.TabIndex = 36;
      this.newTextBox_Pin_GateDim1_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_GateDim1_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim1_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_GateDim1_G4.Value = "0";
      this.newTextBox_Pin_GateDim2_G4.BackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim2_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_GateDim2_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_GateDim2_G4.IsDigit = true;
      this.newTextBox_Pin_GateDim2_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_GateDim2_G4.Location = new Point(153, 44);
      this.newTextBox_Pin_GateDim2_G4.MultiLine = false;
      this.newTextBox_Pin_GateDim2_G4.Name = "newTextBox_Pin_GateDim2_G4";
      this.newTextBox_Pin_GateDim2_G4.ReadOnly = false;
      this.newTextBox_Pin_GateDim2_G4.Size = new Size(76, 23);
      this.newTextBox_Pin_GateDim2_G4.TabIndex = 37;
      this.newTextBox_Pin_GateDim2_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_GateDim2_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_GateDim2_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_GateDim2_G4.Value = "0";
      this.newTextBox_Pin_RunnerLength_G4.BackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerLength_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_RunnerLength_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_RunnerLength_G4.IsDigit = true;
      this.newTextBox_Pin_RunnerLength_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_RunnerLength_G4.Location = new Point(153, 85);
      this.newTextBox_Pin_RunnerLength_G4.MultiLine = false;
      this.newTextBox_Pin_RunnerLength_G4.Name = "newTextBox_Pin_RunnerLength_G4";
      this.newTextBox_Pin_RunnerLength_G4.ReadOnly = false;
      this.newTextBox_Pin_RunnerLength_G4.Size = new Size(76, 23);
      this.newTextBox_Pin_RunnerLength_G4.TabIndex = 38;
      this.newTextBox_Pin_RunnerLength_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_RunnerLength_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerLength_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_RunnerLength_G4.Value = "0";
      this.newTextBox_Pin_RunnerDim1_G4.BackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim1_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_RunnerDim1_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_RunnerDim1_G4.IsDigit = true;
      this.newTextBox_Pin_RunnerDim1_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_RunnerDim1_G4.Location = new Point(153, 107);
      this.newTextBox_Pin_RunnerDim1_G4.MultiLine = false;
      this.newTextBox_Pin_RunnerDim1_G4.Name = "newTextBox_Pin_RunnerDim1_G4";
      this.newTextBox_Pin_RunnerDim1_G4.ReadOnly = false;
      this.newTextBox_Pin_RunnerDim1_G4.Size = new Size(76, 23);
      this.newTextBox_Pin_RunnerDim1_G4.TabIndex = 39;
      this.newTextBox_Pin_RunnerDim1_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_RunnerDim1_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim1_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_RunnerDim1_G4.Value = "0";
      this.newTextBox_Pin_RunnerDim2_G4.BackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim2_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Pin_RunnerDim2_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Pin_RunnerDim2_G4.IsDigit = true;
      this.newTextBox_Pin_RunnerDim2_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Pin_RunnerDim2_G4.Location = new Point(153, 129);
      this.newTextBox_Pin_RunnerDim2_G4.MultiLine = false;
      this.newTextBox_Pin_RunnerDim2_G4.Name = "newTextBox_Pin_RunnerDim2_G4";
      this.newTextBox_Pin_RunnerDim2_G4.ReadOnly = false;
      this.newTextBox_Pin_RunnerDim2_G4.Size = new Size(76, 23);
      this.newTextBox_Pin_RunnerDim2_G4.TabIndex = 40;
      this.newTextBox_Pin_RunnerDim2_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Pin_RunnerDim2_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Pin_RunnerDim2_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Pin_RunnerDim2_G4.Value = "0";
      this.tabControl_Side_Group.Controls.Add((Control) this.tabPage_Side_Group1);
      this.tabControl_Side_Group.Controls.Add((Control) this.tabPage_Side_Group2);
      this.tabControl_Side_Group.Controls.Add((Control) this.tabPage_Side_Group3);
      this.tabControl_Side_Group.Controls.Add((Control) this.tabPage_Side_Group4);
      this.tabControl_Side_Group.Location = new Point(-6, -4);
      this.tabControl_Side_Group.Name = "tabControl_Side_Group";
      this.tabControl_Side_Group.SelectedIndex = 0;
      this.tabControl_Side_Group.Size = new Size(238, 361);
      this.tabControl_Side_Group.TabIndex = 93;
      this.tabPage_Side_Group1.Controls.Add((Control) this.panel9);
      this.tabPage_Side_Group1.Controls.Add((Control) this.panel_Side_GateType2_G1);
      this.tabPage_Side_Group1.Controls.Add((Control) this.panel11);
      this.tabPage_Side_Group1.Controls.Add((Control) this.label_Side_Gate_G1);
      this.tabPage_Side_Group1.Controls.Add((Control) this.label_Side_Runner_G1);
      this.tabPage_Side_Group1.Controls.Add((Control) this.label_Side_GateLength_G1);
      this.tabPage_Side_Group1.Controls.Add((Control) this.label_Side_GateDim1_G1);
      this.tabPage_Side_Group1.Controls.Add((Control) this.label_Side_RunnerLength_G1);
      this.tabPage_Side_Group1.Controls.Add((Control) this.label_Side_GateDim3_G1);
      this.tabPage_Side_Group1.Controls.Add((Control) this.newTextBox_Side_RunnerDim4_G1);
      this.tabPage_Side_Group1.Controls.Add((Control) this.label_Side_RunnerDim2_G1);
      this.tabPage_Side_Group1.Controls.Add((Control) this.label_Side_RunnerDim4_G1);
      this.tabPage_Side_Group1.Controls.Add((Control) this.label_Side_GateDim2_G1);
      this.tabPage_Side_Group1.Controls.Add((Control) this.label_Side_RunnerDim1_G1);
      this.tabPage_Side_Group1.Controls.Add((Control) this.label_Side_GateDim4_G1);
      this.tabPage_Side_Group1.Controls.Add((Control) this.label_Side_RunnerDim3_G1);
      this.tabPage_Side_Group1.Controls.Add((Control) this.newTextBox_Side_GateLength_G1);
      this.tabPage_Side_Group1.Controls.Add((Control) this.newTextBox_Side_GateDim1_G1);
      this.tabPage_Side_Group1.Controls.Add((Control) this.newTextBox_Side_GateDim2_G1);
      this.tabPage_Side_Group1.Controls.Add((Control) this.newTextBox_Side_GateDim3_G1);
      this.tabPage_Side_Group1.Controls.Add((Control) this.newTextBox_Side_GateDim4_G1);
      this.tabPage_Side_Group1.Controls.Add((Control) this.newTextBox_Side_RunnerLength_G1);
      this.tabPage_Side_Group1.Controls.Add((Control) this.newTextBox_Side_RunnerDim1_G1);
      this.tabPage_Side_Group1.Controls.Add((Control) this.newTextBox_Side_RunnerDim2_G1);
      this.tabPage_Side_Group1.Controls.Add((Control) this.newTextBox_Side_RunnerDim3_G1);
      this.tabPage_Side_Group1.Location = new Point(4, 24);
      this.tabPage_Side_Group1.Name = "tabPage_Side_Group1";
      this.tabPage_Side_Group1.Padding = new Padding(3);
      this.tabPage_Side_Group1.Size = new Size(230, 333);
      this.tabPage_Side_Group1.TabIndex = 0;
      this.tabPage_Side_Group1.UseVisualStyleBackColor = true;
      this.panel9.BackColor = Color.White;
      this.panel9.BorderStyle = BorderStyle.FixedSingle;
      this.panel9.Controls.Add((Control) this.radioButton_Side_GateCircle_G1);
      this.panel9.Controls.Add((Control) this.radioButton_Side_GateRect_G1);
      this.panel9.Location = new Point(2, 21);
      this.panel9.Name = "panel9";
      this.panel9.Size = new Size(228, 25);
      this.panel9.TabIndex = 58;
      this.radioButton_Side_GateCircle_G1.AutoSize = true;
      this.radioButton_Side_GateCircle_G1.Location = new Point(128, 2);
      this.radioButton_Side_GateCircle_G1.Name = "radioButton_Side_GateCircle_G1";
      this.radioButton_Side_GateCircle_G1.Size = new Size(55, 19);
      this.radioButton_Side_GateCircle_G1.TabIndex = 60;
      this.radioButton_Side_GateCircle_G1.Text = "Circle";
      this.radioButton_Side_GateCircle_G1.UseVisualStyleBackColor = true;
      this.radioButton_Side_GateCircle_G1.CheckedChanged += new EventHandler(this.radioButton_Side_GateCircle_CheckedChanged);
      this.radioButton_Side_GateRect_G1.AutoSize = true;
      this.radioButton_Side_GateRect_G1.Location = new Point(28, 2);
      this.radioButton_Side_GateRect_G1.Name = "radioButton_Side_GateRect_G1";
      this.radioButton_Side_GateRect_G1.Size = new Size(77, 19);
      this.radioButton_Side_GateRect_G1.TabIndex = 59;
      this.radioButton_Side_GateRect_G1.Text = "Rectangle";
      this.radioButton_Side_GateRect_G1.UseVisualStyleBackColor = true;
      this.radioButton_Side_GateRect_G1.CheckedChanged += new EventHandler(this.radioButton_Side_GateRect_CheckedChanged);
      this.panel_Side_GateType2_G1.BackColor = Color.White;
      this.panel_Side_GateType2_G1.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Side_GateType2_G1.Controls.Add((Control) this.radioButton_Side_GateSubMarine_G1);
      this.panel_Side_GateType2_G1.Controls.Add((Control) this.radioButton_Side_GateNormal_G1);
      this.panel_Side_GateType2_G1.Location = new Point(2, 45);
      this.panel_Side_GateType2_G1.Name = "panel_Side_GateType2_G1";
      this.panel_Side_GateType2_G1.Size = new Size(228, 25);
      this.panel_Side_GateType2_G1.TabIndex = 61;
      this.radioButton_Side_GateSubMarine_G1.AutoSize = true;
      this.radioButton_Side_GateSubMarine_G1.Location = new Point(128, 2);
      this.radioButton_Side_GateSubMarine_G1.Name = "radioButton_Side_GateSubMarine_G1";
      this.radioButton_Side_GateSubMarine_G1.Size = new Size(73, 19);
      this.radioButton_Side_GateSubMarine_G1.TabIndex = 63;
      this.radioButton_Side_GateSubMarine_G1.Text = "서브마린";
      this.radioButton_Side_GateSubMarine_G1.UseVisualStyleBackColor = true;
      this.radioButton_Side_GateNormal_G1.AutoSize = true;
      this.radioButton_Side_GateNormal_G1.Location = new Point(29, 2);
      this.radioButton_Side_GateNormal_G1.Name = "radioButton_Side_GateNormal_G1";
      this.radioButton_Side_GateNormal_G1.Size = new Size(49, 19);
      this.radioButton_Side_GateNormal_G1.TabIndex = 62;
      this.radioButton_Side_GateNormal_G1.Text = "일반";
      this.radioButton_Side_GateNormal_G1.UseVisualStyleBackColor = true;
      this.panel11.BackColor = Color.White;
      this.panel11.BorderStyle = BorderStyle.FixedSingle;
      this.panel11.Controls.Add((Control) this.radioButton_Side_RunnerRectangle_G1);
      this.panel11.Controls.Add((Control) this.radioButton_Side_RunnerCircle_G1);
      this.panel11.Controls.Add((Control) this.radioButton_Side_RunnerTrepezoidal_G1);
      this.panel11.Location = new Point(2, 198);
      this.panel11.Name = "panel11";
      this.panel11.Size = new Size(228, 25);
      this.panel11.TabIndex = 69;
      this.radioButton_Side_RunnerRectangle_G1.AutoSize = true;
      this.radioButton_Side_RunnerRectangle_G1.Location = new Point(146, 2);
      this.radioButton_Side_RunnerRectangle_G1.Name = "radioButton_Side_RunnerRectangle_G1";
      this.radioButton_Side_RunnerRectangle_G1.Size = new Size(77, 19);
      this.radioButton_Side_RunnerRectangle_G1.TabIndex = 72;
      this.radioButton_Side_RunnerRectangle_G1.Text = "Rectangle";
      this.radioButton_Side_RunnerRectangle_G1.UseVisualStyleBackColor = true;
      this.radioButton_Side_RunnerRectangle_G1.CheckedChanged += new EventHandler(this.radioButton_Side_RunnerRectangle_CheckedChanged);
      this.radioButton_Side_RunnerCircle_G1.AutoSize = true;
      this.radioButton_Side_RunnerCircle_G1.Location = new Point(88, 2);
      this.radioButton_Side_RunnerCircle_G1.Name = "radioButton_Side_RunnerCircle_G1";
      this.radioButton_Side_RunnerCircle_G1.Size = new Size(55, 19);
      this.radioButton_Side_RunnerCircle_G1.TabIndex = 71;
      this.radioButton_Side_RunnerCircle_G1.Text = "Circle";
      this.radioButton_Side_RunnerCircle_G1.UseVisualStyleBackColor = true;
      this.radioButton_Side_RunnerCircle_G1.CheckedChanged += new EventHandler(this.radioButton_Side_RunnerCircle_CheckedChanged);
      this.radioButton_Side_RunnerTrepezoidal_G1.AutoSize = true;
      this.radioButton_Side_RunnerTrepezoidal_G1.Location = new Point(3, 2);
      this.radioButton_Side_RunnerTrepezoidal_G1.Name = "radioButton_Side_RunnerTrepezoidal_G1";
      this.radioButton_Side_RunnerTrepezoidal_G1.Size = new Size(84, 19);
      this.radioButton_Side_RunnerTrepezoidal_G1.TabIndex = 70;
      this.radioButton_Side_RunnerTrepezoidal_G1.Text = "Trepezoidal";
      this.radioButton_Side_RunnerTrepezoidal_G1.UseVisualStyleBackColor = true;
      this.radioButton_Side_RunnerTrepezoidal_G1.CheckedChanged += new EventHandler(this.radioButton_Side_RunnerTrepezoidal_CheckedChanged);
      this.label_Side_Gate_G1.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Side_Gate_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_Gate_G1.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Side_Gate_G1.ForeColor = Color.MidnightBlue;
      this.label_Side_Gate_G1.Location = new Point(2, 2);
      this.label_Side_Gate_G1.Name = "label_Side_Gate_G1";
      this.label_Side_Gate_G1.Size = new Size(228, 20);
      this.label_Side_Gate_G1.TabIndex = 138;
      this.label_Side_Gate_G1.Text = "<게이트>";
      this.label_Side_Gate_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_Runner_G1.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Side_Runner_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_Runner_G1.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Side_Runner_G1.ForeColor = Color.MidnightBlue;
      this.label_Side_Runner_G1.Location = new Point(2, 179);
      this.label_Side_Runner_G1.Name = "label_Side_Runner_G1";
      this.label_Side_Runner_G1.Size = new Size(228, 20);
      this.label_Side_Runner_G1.TabIndex = 137;
      this.label_Side_Runner_G1.Text = "<런너>";
      this.label_Side_Runner_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateLength_G1.BackColor = Color.Lavender;
      this.label_Side_GateLength_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateLength_G1.Location = new Point(2, 69);
      this.label_Side_GateLength_G1.Name = "label_Side_GateLength_G1";
      this.label_Side_GateLength_G1.Size = new Size(153, 23);
      this.label_Side_GateLength_G1.TabIndex = 148;
      this.label_Side_GateLength_G1.Text = "게이트 길이";
      this.label_Side_GateLength_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim1_G1.BackColor = Color.Lavender;
      this.label_Side_GateDim1_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim1_G1.Location = new Point(2, 91);
      this.label_Side_GateDim1_G1.Name = "label_Side_GateDim1_G1";
      this.label_Side_GateDim1_G1.Size = new Size(153, 23);
      this.label_Side_GateDim1_G1.TabIndex = 146;
      this.label_Side_GateDim1_G1.Text = "Start Width";
      this.label_Side_GateDim1_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerLength_G1.BackColor = Color.Lavender;
      this.label_Side_RunnerLength_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerLength_G1.Location = new Point(2, 222);
      this.label_Side_RunnerLength_G1.Name = "label_Side_RunnerLength_G1";
      this.label_Side_RunnerLength_G1.Size = new Size(153, 23);
      this.label_Side_RunnerLength_G1.TabIndex = 145;
      this.label_Side_RunnerLength_G1.Text = "런너 길이";
      this.label_Side_RunnerLength_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim3_G1.BackColor = Color.Lavender;
      this.label_Side_GateDim3_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim3_G1.Location = new Point(2, 135);
      this.label_Side_GateDim3_G1.Name = "label_Side_GateDim3_G1";
      this.label_Side_GateDim3_G1.Size = new Size(153, 23);
      this.label_Side_GateDim3_G1.TabIndex = 144;
      this.label_Side_GateDim3_G1.Text = "End Width";
      this.label_Side_GateDim3_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Side_RunnerDim4_G1.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim4_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim4_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim4_G1.IsDigit = true;
      this.newTextBox_Side_RunnerDim4_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim4_G1.Location = new Point(154, 310);
      this.newTextBox_Side_RunnerDim4_G1.MultiLine = false;
      this.newTextBox_Side_RunnerDim4_G1.Name = "newTextBox_Side_RunnerDim4_G1";
      this.newTextBox_Side_RunnerDim4_G1.ReadOnly = false;
      this.newTextBox_Side_RunnerDim4_G1.Size = new Size(76, 23);
      this.newTextBox_Side_RunnerDim4_G1.TabIndex = 77;
      this.newTextBox_Side_RunnerDim4_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim4_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim4_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim4_G1.Value = "0";
      this.label_Side_RunnerDim2_G1.BackColor = Color.Lavender;
      this.label_Side_RunnerDim2_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim2_G1.Location = new Point(2, 266);
      this.label_Side_RunnerDim2_G1.Name = "label_Side_RunnerDim2_G1";
      this.label_Side_RunnerDim2_G1.Size = new Size(153, 23);
      this.label_Side_RunnerDim2_G1.TabIndex = 143;
      this.label_Side_RunnerDim2_G1.Text = "Bottom Width";
      this.label_Side_RunnerDim2_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim4_G1.BackColor = Color.Lavender;
      this.label_Side_RunnerDim4_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim4_G1.Location = new Point(2, 310);
      this.label_Side_RunnerDim4_G1.Name = "label_Side_RunnerDim4_G1";
      this.label_Side_RunnerDim4_G1.Size = new Size(153, 23);
      this.label_Side_RunnerDim4_G1.TabIndex = 160;
      this.label_Side_RunnerDim4_G1.Text = "End Height";
      this.label_Side_RunnerDim4_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim2_G1.BackColor = Color.Lavender;
      this.label_Side_GateDim2_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim2_G1.Location = new Point(2, 113);
      this.label_Side_GateDim2_G1.Name = "label_Side_GateDim2_G1";
      this.label_Side_GateDim2_G1.Size = new Size(153, 23);
      this.label_Side_GateDim2_G1.TabIndex = 142;
      this.label_Side_GateDim2_G1.Text = "Start Height";
      this.label_Side_GateDim2_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim1_G1.BackColor = Color.Lavender;
      this.label_Side_RunnerDim1_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim1_G1.Location = new Point(2, 244);
      this.label_Side_RunnerDim1_G1.Name = "label_Side_RunnerDim1_G1";
      this.label_Side_RunnerDim1_G1.Size = new Size(153, 23);
      this.label_Side_RunnerDim1_G1.TabIndex = 147;
      this.label_Side_RunnerDim1_G1.Text = "Top Width";
      this.label_Side_RunnerDim1_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim4_G1.BackColor = Color.Lavender;
      this.label_Side_GateDim4_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim4_G1.Location = new Point(2, 157);
      this.label_Side_GateDim4_G1.Name = "label_Side_GateDim4_G1";
      this.label_Side_GateDim4_G1.Size = new Size(153, 23);
      this.label_Side_GateDim4_G1.TabIndex = 150;
      this.label_Side_GateDim4_G1.Text = "End Height";
      this.label_Side_GateDim4_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim3_G1.BackColor = Color.Lavender;
      this.label_Side_RunnerDim3_G1.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim3_G1.Location = new Point(2, 288);
      this.label_Side_RunnerDim3_G1.Name = "label_Side_RunnerDim3_G1";
      this.label_Side_RunnerDim3_G1.Size = new Size(153, 23);
      this.label_Side_RunnerDim3_G1.TabIndex = 149;
      this.label_Side_RunnerDim3_G1.Text = "Height";
      this.label_Side_RunnerDim3_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Side_GateLength_G1.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateLength_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateLength_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateLength_G1.IsDigit = true;
      this.newTextBox_Side_GateLength_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateLength_G1.Location = new Point(154, 69);
      this.newTextBox_Side_GateLength_G1.MultiLine = false;
      this.newTextBox_Side_GateLength_G1.Name = "newTextBox_Side_GateLength_G1";
      this.newTextBox_Side_GateLength_G1.ReadOnly = false;
      this.newTextBox_Side_GateLength_G1.Size = new Size(76, 23);
      this.newTextBox_Side_GateLength_G1.TabIndex = 64;
      this.newTextBox_Side_GateLength_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateLength_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateLength_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateLength_G1.Value = "0";
      this.newTextBox_Side_GateDim1_G1.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim1_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim1_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim1_G1.IsDigit = true;
      this.newTextBox_Side_GateDim1_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim1_G1.Location = new Point(154, 91);
      this.newTextBox_Side_GateDim1_G1.MultiLine = false;
      this.newTextBox_Side_GateDim1_G1.Name = "newTextBox_Side_GateDim1_G1";
      this.newTextBox_Side_GateDim1_G1.ReadOnly = false;
      this.newTextBox_Side_GateDim1_G1.Size = new Size(76, 23);
      this.newTextBox_Side_GateDim1_G1.TabIndex = 65;
      this.newTextBox_Side_GateDim1_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim1_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim1_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim1_G1.Value = "0";
      this.newTextBox_Side_GateDim2_G1.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim2_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim2_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim2_G1.IsDigit = true;
      this.newTextBox_Side_GateDim2_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim2_G1.Location = new Point(154, 113);
      this.newTextBox_Side_GateDim2_G1.MultiLine = false;
      this.newTextBox_Side_GateDim2_G1.Name = "newTextBox_Side_GateDim2_G1";
      this.newTextBox_Side_GateDim2_G1.ReadOnly = false;
      this.newTextBox_Side_GateDim2_G1.Size = new Size(76, 23);
      this.newTextBox_Side_GateDim2_G1.TabIndex = 66;
      this.newTextBox_Side_GateDim2_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim2_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim2_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim2_G1.Value = "0";
      this.newTextBox_Side_GateDim3_G1.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim3_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim3_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim3_G1.IsDigit = true;
      this.newTextBox_Side_GateDim3_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim3_G1.Location = new Point(154, 135);
      this.newTextBox_Side_GateDim3_G1.MultiLine = false;
      this.newTextBox_Side_GateDim3_G1.Name = "newTextBox_Side_GateDim3_G1";
      this.newTextBox_Side_GateDim3_G1.ReadOnly = false;
      this.newTextBox_Side_GateDim3_G1.Size = new Size(76, 23);
      this.newTextBox_Side_GateDim3_G1.TabIndex = 67;
      this.newTextBox_Side_GateDim3_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim3_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim3_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim3_G1.Value = "0";
      this.newTextBox_Side_GateDim4_G1.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim4_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim4_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim4_G1.IsDigit = true;
      this.newTextBox_Side_GateDim4_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim4_G1.Location = new Point(154, 157);
      this.newTextBox_Side_GateDim4_G1.MultiLine = false;
      this.newTextBox_Side_GateDim4_G1.Name = "newTextBox_Side_GateDim4_G1";
      this.newTextBox_Side_GateDim4_G1.ReadOnly = false;
      this.newTextBox_Side_GateDim4_G1.Size = new Size(76, 23);
      this.newTextBox_Side_GateDim4_G1.TabIndex = 68;
      this.newTextBox_Side_GateDim4_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim4_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim4_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim4_G1.Value = "0";
      this.newTextBox_Side_RunnerLength_G1.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerLength_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerLength_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerLength_G1.IsDigit = true;
      this.newTextBox_Side_RunnerLength_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerLength_G1.Location = new Point(154, 222);
      this.newTextBox_Side_RunnerLength_G1.MultiLine = false;
      this.newTextBox_Side_RunnerLength_G1.Name = "newTextBox_Side_RunnerLength_G1";
      this.newTextBox_Side_RunnerLength_G1.ReadOnly = false;
      this.newTextBox_Side_RunnerLength_G1.Size = new Size(76, 23);
      this.newTextBox_Side_RunnerLength_G1.TabIndex = 73;
      this.newTextBox_Side_RunnerLength_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerLength_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerLength_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerLength_G1.Value = "0";
      this.newTextBox_Side_RunnerDim1_G1.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim1_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim1_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim1_G1.IsDigit = true;
      this.newTextBox_Side_RunnerDim1_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim1_G1.Location = new Point(154, 244);
      this.newTextBox_Side_RunnerDim1_G1.MultiLine = false;
      this.newTextBox_Side_RunnerDim1_G1.Name = "newTextBox_Side_RunnerDim1_G1";
      this.newTextBox_Side_RunnerDim1_G1.ReadOnly = false;
      this.newTextBox_Side_RunnerDim1_G1.Size = new Size(76, 23);
      this.newTextBox_Side_RunnerDim1_G1.TabIndex = 74;
      this.newTextBox_Side_RunnerDim1_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim1_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim1_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim1_G1.Value = "0";
      this.newTextBox_Side_RunnerDim2_G1.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim2_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim2_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim2_G1.IsDigit = true;
      this.newTextBox_Side_RunnerDim2_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim2_G1.Location = new Point(154, 266);
      this.newTextBox_Side_RunnerDim2_G1.MultiLine = false;
      this.newTextBox_Side_RunnerDim2_G1.Name = "newTextBox_Side_RunnerDim2_G1";
      this.newTextBox_Side_RunnerDim2_G1.ReadOnly = false;
      this.newTextBox_Side_RunnerDim2_G1.Size = new Size(76, 23);
      this.newTextBox_Side_RunnerDim2_G1.TabIndex = 75;
      this.newTextBox_Side_RunnerDim2_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim2_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim2_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim2_G1.Value = "0";
      this.newTextBox_Side_RunnerDim3_G1.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim3_G1.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim3_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim3_G1.IsDigit = true;
      this.newTextBox_Side_RunnerDim3_G1.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim3_G1.Location = new Point(154, 288);
      this.newTextBox_Side_RunnerDim3_G1.MultiLine = false;
      this.newTextBox_Side_RunnerDim3_G1.Name = "newTextBox_Side_RunnerDim3_G1";
      this.newTextBox_Side_RunnerDim3_G1.ReadOnly = false;
      this.newTextBox_Side_RunnerDim3_G1.Size = new Size(76, 23);
      this.newTextBox_Side_RunnerDim3_G1.TabIndex = 76;
      this.newTextBox_Side_RunnerDim3_G1.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim3_G1.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim3_G1.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim3_G1.Value = "0";
      this.tabPage_Side_Group2.Controls.Add((Control) this.panel12);
      this.tabPage_Side_Group2.Controls.Add((Control) this.panel_Side_GateType2_G2);
      this.tabPage_Side_Group2.Controls.Add((Control) this.panel14);
      this.tabPage_Side_Group2.Controls.Add((Control) this.label_Side_Gate_G2);
      this.tabPage_Side_Group2.Controls.Add((Control) this.label_Side_Runner_G2);
      this.tabPage_Side_Group2.Controls.Add((Control) this.label_Side_GateLength_G2);
      this.tabPage_Side_Group2.Controls.Add((Control) this.label_Side_GateDim1_G2);
      this.tabPage_Side_Group2.Controls.Add((Control) this.label_Side_RunnerLength_G2);
      this.tabPage_Side_Group2.Controls.Add((Control) this.label_Side_GateDim3_G2);
      this.tabPage_Side_Group2.Controls.Add((Control) this.newTextBox_Side_RunnerDim4_G2);
      this.tabPage_Side_Group2.Controls.Add((Control) this.label_Side_RunnerDim2_G2);
      this.tabPage_Side_Group2.Controls.Add((Control) this.label_Side_RunnerDim4_G2);
      this.tabPage_Side_Group2.Controls.Add((Control) this.label_Side_GateDim2_G2);
      this.tabPage_Side_Group2.Controls.Add((Control) this.label_Side_RunnerDim1_G2);
      this.tabPage_Side_Group2.Controls.Add((Control) this.label_Side_GateDim4_G2);
      this.tabPage_Side_Group2.Controls.Add((Control) this.label_Side_RunnerDim3_G2);
      this.tabPage_Side_Group2.Controls.Add((Control) this.newTextBox_Side_GateLength_G2);
      this.tabPage_Side_Group2.Controls.Add((Control) this.newTextBox_Side_GateDim1_G2);
      this.tabPage_Side_Group2.Controls.Add((Control) this.newTextBox_Side_GateDim2_G2);
      this.tabPage_Side_Group2.Controls.Add((Control) this.newTextBox_Side_GateDim3_G2);
      this.tabPage_Side_Group2.Controls.Add((Control) this.newTextBox_Side_GateDim4_G2);
      this.tabPage_Side_Group2.Controls.Add((Control) this.newTextBox_Side_RunnerLength_G2);
      this.tabPage_Side_Group2.Controls.Add((Control) this.newTextBox_Side_RunnerDim1_G2);
      this.tabPage_Side_Group2.Controls.Add((Control) this.newTextBox_Side_RunnerDim2_G2);
      this.tabPage_Side_Group2.Controls.Add((Control) this.newTextBox_Side_RunnerDim3_G2);
      this.tabPage_Side_Group2.Location = new Point(4, 24);
      this.tabPage_Side_Group2.Name = "tabPage_Side_Group2";
      this.tabPage_Side_Group2.Padding = new Padding(3);
      this.tabPage_Side_Group2.Size = new Size(230, 333);
      this.tabPage_Side_Group2.TabIndex = 1;
      this.tabPage_Side_Group2.UseVisualStyleBackColor = true;
      this.panel12.BackColor = Color.White;
      this.panel12.BorderStyle = BorderStyle.FixedSingle;
      this.panel12.Controls.Add((Control) this.radioButton_Side_GateCircle_G2);
      this.panel12.Controls.Add((Control) this.radioButton_Side_GateRect_G2);
      this.panel12.Location = new Point(2, 21);
      this.panel12.Name = "panel12";
      this.panel12.Size = new Size(228, 25);
      this.panel12.TabIndex = 162;
      this.radioButton_Side_GateCircle_G2.AutoSize = true;
      this.radioButton_Side_GateCircle_G2.Location = new Point(128, 2);
      this.radioButton_Side_GateCircle_G2.Name = "radioButton_Side_GateCircle_G2";
      this.radioButton_Side_GateCircle_G2.Size = new Size(55, 19);
      this.radioButton_Side_GateCircle_G2.TabIndex = 0;
      this.radioButton_Side_GateCircle_G2.Text = "Circle";
      this.radioButton_Side_GateCircle_G2.UseVisualStyleBackColor = true;
      this.radioButton_Side_GateCircle_G2.CheckedChanged += new EventHandler(this.radioButton_Side_GateCircle_CheckedChanged);
      this.radioButton_Side_GateRect_G2.AutoSize = true;
      this.radioButton_Side_GateRect_G2.Location = new Point(28, 2);
      this.radioButton_Side_GateRect_G2.Name = "radioButton_Side_GateRect_G2";
      this.radioButton_Side_GateRect_G2.Size = new Size(77, 19);
      this.radioButton_Side_GateRect_G2.TabIndex = 0;
      this.radioButton_Side_GateRect_G2.Text = "Rectangle";
      this.radioButton_Side_GateRect_G2.UseVisualStyleBackColor = true;
      this.radioButton_Side_GateRect_G2.CheckedChanged += new EventHandler(this.radioButton_Side_GateRect_CheckedChanged);
      this.panel_Side_GateType2_G2.BackColor = Color.White;
      this.panel_Side_GateType2_G2.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Side_GateType2_G2.Controls.Add((Control) this.radioButton_Side_GateSubMarine_G2);
      this.panel_Side_GateType2_G2.Controls.Add((Control) this.radioButton_Side_GateNormal_G2);
      this.panel_Side_GateType2_G2.Location = new Point(2, 45);
      this.panel_Side_GateType2_G2.Name = "panel_Side_GateType2_G2";
      this.panel_Side_GateType2_G2.Size = new Size(228, 25);
      this.panel_Side_GateType2_G2.TabIndex = 163;
      this.radioButton_Side_GateSubMarine_G2.AutoSize = true;
      this.radioButton_Side_GateSubMarine_G2.Location = new Point(128, 2);
      this.radioButton_Side_GateSubMarine_G2.Name = "radioButton_Side_GateSubMarine_G2";
      this.radioButton_Side_GateSubMarine_G2.Size = new Size(73, 19);
      this.radioButton_Side_GateSubMarine_G2.TabIndex = 0;
      this.radioButton_Side_GateSubMarine_G2.Text = "서브마린";
      this.radioButton_Side_GateSubMarine_G2.UseVisualStyleBackColor = true;
      this.radioButton_Side_GateNormal_G2.AutoSize = true;
      this.radioButton_Side_GateNormal_G2.Location = new Point(29, 2);
      this.radioButton_Side_GateNormal_G2.Name = "radioButton_Side_GateNormal_G2";
      this.radioButton_Side_GateNormal_G2.Size = new Size(49, 19);
      this.radioButton_Side_GateNormal_G2.TabIndex = 0;
      this.radioButton_Side_GateNormal_G2.Text = "일반";
      this.radioButton_Side_GateNormal_G2.UseVisualStyleBackColor = true;
      this.panel14.BackColor = Color.White;
      this.panel14.BorderStyle = BorderStyle.FixedSingle;
      this.panel14.Controls.Add((Control) this.radioButton_Side_RunnerRectangle_G2);
      this.panel14.Controls.Add((Control) this.radioButton_Side_RunnerCircle_G2);
      this.panel14.Controls.Add((Control) this.radioButton_Side_RunnerTrepezoidal_G2);
      this.panel14.Location = new Point(2, 198);
      this.panel14.Name = "panel14";
      this.panel14.Size = new Size(228, 25);
      this.panel14.TabIndex = 164;
      this.radioButton_Side_RunnerRectangle_G2.AutoSize = true;
      this.radioButton_Side_RunnerRectangle_G2.Location = new Point(146, 2);
      this.radioButton_Side_RunnerRectangle_G2.Name = "radioButton_Side_RunnerRectangle_G2";
      this.radioButton_Side_RunnerRectangle_G2.Size = new Size(77, 19);
      this.radioButton_Side_RunnerRectangle_G2.TabIndex = 1;
      this.radioButton_Side_RunnerRectangle_G2.Text = "Rectangle";
      this.radioButton_Side_RunnerRectangle_G2.UseVisualStyleBackColor = true;
      this.radioButton_Side_RunnerRectangle_G2.CheckedChanged += new EventHandler(this.radioButton_Side_RunnerRectangle_CheckedChanged);
      this.radioButton_Side_RunnerCircle_G2.AutoSize = true;
      this.radioButton_Side_RunnerCircle_G2.Location = new Point(88, 2);
      this.radioButton_Side_RunnerCircle_G2.Name = "radioButton_Side_RunnerCircle_G2";
      this.radioButton_Side_RunnerCircle_G2.Size = new Size(55, 19);
      this.radioButton_Side_RunnerCircle_G2.TabIndex = 0;
      this.radioButton_Side_RunnerCircle_G2.Text = "Circle";
      this.radioButton_Side_RunnerCircle_G2.UseVisualStyleBackColor = true;
      this.radioButton_Side_RunnerCircle_G2.CheckedChanged += new EventHandler(this.radioButton_Side_RunnerCircle_CheckedChanged);
      this.radioButton_Side_RunnerTrepezoidal_G2.AutoSize = true;
      this.radioButton_Side_RunnerTrepezoidal_G2.Location = new Point(3, 2);
      this.radioButton_Side_RunnerTrepezoidal_G2.Name = "radioButton_Side_RunnerTrepezoidal_G2";
      this.radioButton_Side_RunnerTrepezoidal_G2.Size = new Size(84, 19);
      this.radioButton_Side_RunnerTrepezoidal_G2.TabIndex = 0;
      this.radioButton_Side_RunnerTrepezoidal_G2.Text = "Trepezoidal";
      this.radioButton_Side_RunnerTrepezoidal_G2.UseVisualStyleBackColor = true;
      this.radioButton_Side_RunnerTrepezoidal_G2.CheckedChanged += new EventHandler(this.radioButton_Side_RunnerTrepezoidal_CheckedChanged);
      this.label_Side_Gate_G2.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Side_Gate_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_Gate_G2.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Side_Gate_G2.ForeColor = Color.MidnightBlue;
      this.label_Side_Gate_G2.Location = new Point(2, 2);
      this.label_Side_Gate_G2.Name = "label_Side_Gate_G2";
      this.label_Side_Gate_G2.Size = new Size(228, 20);
      this.label_Side_Gate_G2.TabIndex = 138;
      this.label_Side_Gate_G2.Text = "<게이트>";
      this.label_Side_Gate_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_Runner_G2.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Side_Runner_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_Runner_G2.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Side_Runner_G2.ForeColor = Color.MidnightBlue;
      this.label_Side_Runner_G2.Location = new Point(2, 179);
      this.label_Side_Runner_G2.Name = "label_Side_Runner_G2";
      this.label_Side_Runner_G2.Size = new Size(228, 20);
      this.label_Side_Runner_G2.TabIndex = 137;
      this.label_Side_Runner_G2.Text = "<런너>";
      this.label_Side_Runner_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateLength_G2.BackColor = Color.Lavender;
      this.label_Side_GateLength_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateLength_G2.Location = new Point(2, 69);
      this.label_Side_GateLength_G2.Name = "label_Side_GateLength_G2";
      this.label_Side_GateLength_G2.Size = new Size(153, 23);
      this.label_Side_GateLength_G2.TabIndex = 148;
      this.label_Side_GateLength_G2.Text = "게이트 길이";
      this.label_Side_GateLength_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim1_G2.BackColor = Color.Lavender;
      this.label_Side_GateDim1_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim1_G2.Location = new Point(2, 91);
      this.label_Side_GateDim1_G2.Name = "label_Side_GateDim1_G2";
      this.label_Side_GateDim1_G2.Size = new Size(153, 23);
      this.label_Side_GateDim1_G2.TabIndex = 146;
      this.label_Side_GateDim1_G2.Text = "Start Width";
      this.label_Side_GateDim1_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerLength_G2.BackColor = Color.Lavender;
      this.label_Side_RunnerLength_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerLength_G2.Location = new Point(2, 222);
      this.label_Side_RunnerLength_G2.Name = "label_Side_RunnerLength_G2";
      this.label_Side_RunnerLength_G2.Size = new Size(153, 23);
      this.label_Side_RunnerLength_G2.TabIndex = 145;
      this.label_Side_RunnerLength_G2.Text = "런너 길이";
      this.label_Side_RunnerLength_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim3_G2.BackColor = Color.Lavender;
      this.label_Side_GateDim3_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim3_G2.Location = new Point(2, 135);
      this.label_Side_GateDim3_G2.Name = "label_Side_GateDim3_G2";
      this.label_Side_GateDim3_G2.Size = new Size(153, 23);
      this.label_Side_GateDim3_G2.TabIndex = 144;
      this.label_Side_GateDim3_G2.Text = "End Width";
      this.label_Side_GateDim3_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Side_RunnerDim4_G2.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim4_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim4_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim4_G2.IsDigit = true;
      this.newTextBox_Side_RunnerDim4_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim4_G2.Location = new Point(154, 310);
      this.newTextBox_Side_RunnerDim4_G2.MultiLine = false;
      this.newTextBox_Side_RunnerDim4_G2.Name = "newTextBox_Side_RunnerDim4_G2";
      this.newTextBox_Side_RunnerDim4_G2.ReadOnly = false;
      this.newTextBox_Side_RunnerDim4_G2.Size = new Size(76, 23);
      this.newTextBox_Side_RunnerDim4_G2.TabIndex = 161;
      this.newTextBox_Side_RunnerDim4_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim4_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim4_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim4_G2.Value = "0";
      this.label_Side_RunnerDim2_G2.BackColor = Color.Lavender;
      this.label_Side_RunnerDim2_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim2_G2.Location = new Point(2, 266);
      this.label_Side_RunnerDim2_G2.Name = "label_Side_RunnerDim2_G2";
      this.label_Side_RunnerDim2_G2.Size = new Size(153, 23);
      this.label_Side_RunnerDim2_G2.TabIndex = 143;
      this.label_Side_RunnerDim2_G2.Text = "Bottom Width";
      this.label_Side_RunnerDim2_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim4_G2.BackColor = Color.Lavender;
      this.label_Side_RunnerDim4_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim4_G2.Location = new Point(2, 310);
      this.label_Side_RunnerDim4_G2.Name = "label_Side_RunnerDim4_G2";
      this.label_Side_RunnerDim4_G2.Size = new Size(153, 23);
      this.label_Side_RunnerDim4_G2.TabIndex = 160;
      this.label_Side_RunnerDim4_G2.Text = "End Height";
      this.label_Side_RunnerDim4_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim2_G2.BackColor = Color.Lavender;
      this.label_Side_GateDim2_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim2_G2.Location = new Point(2, 113);
      this.label_Side_GateDim2_G2.Name = "label_Side_GateDim2_G2";
      this.label_Side_GateDim2_G2.Size = new Size(153, 23);
      this.label_Side_GateDim2_G2.TabIndex = 142;
      this.label_Side_GateDim2_G2.Text = "Start Height";
      this.label_Side_GateDim2_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim1_G2.BackColor = Color.Lavender;
      this.label_Side_RunnerDim1_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim1_G2.Location = new Point(2, 244);
      this.label_Side_RunnerDim1_G2.Name = "label_Side_RunnerDim1_G2";
      this.label_Side_RunnerDim1_G2.Size = new Size(153, 23);
      this.label_Side_RunnerDim1_G2.TabIndex = 147;
      this.label_Side_RunnerDim1_G2.Text = "Top Width";
      this.label_Side_RunnerDim1_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim4_G2.BackColor = Color.Lavender;
      this.label_Side_GateDim4_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim4_G2.Location = new Point(2, 157);
      this.label_Side_GateDim4_G2.Name = "label_Side_GateDim4_G2";
      this.label_Side_GateDim4_G2.Size = new Size(153, 23);
      this.label_Side_GateDim4_G2.TabIndex = 150;
      this.label_Side_GateDim4_G2.Text = "End Height";
      this.label_Side_GateDim4_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim3_G2.BackColor = Color.Lavender;
      this.label_Side_RunnerDim3_G2.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim3_G2.Location = new Point(2, 288);
      this.label_Side_RunnerDim3_G2.Name = "label_Side_RunnerDim3_G2";
      this.label_Side_RunnerDim3_G2.Size = new Size(153, 23);
      this.label_Side_RunnerDim3_G2.TabIndex = 149;
      this.label_Side_RunnerDim3_G2.Text = "Height";
      this.label_Side_RunnerDim3_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Side_GateLength_G2.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateLength_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateLength_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateLength_G2.IsDigit = true;
      this.newTextBox_Side_GateLength_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateLength_G2.Location = new Point(154, 69);
      this.newTextBox_Side_GateLength_G2.MultiLine = false;
      this.newTextBox_Side_GateLength_G2.Name = "newTextBox_Side_GateLength_G2";
      this.newTextBox_Side_GateLength_G2.ReadOnly = false;
      this.newTextBox_Side_GateLength_G2.Size = new Size(76, 23);
      this.newTextBox_Side_GateLength_G2.TabIndex = 153;
      this.newTextBox_Side_GateLength_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateLength_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateLength_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateLength_G2.Value = "0";
      this.newTextBox_Side_GateDim1_G2.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim1_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim1_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim1_G2.IsDigit = true;
      this.newTextBox_Side_GateDim1_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim1_G2.Location = new Point(154, 91);
      this.newTextBox_Side_GateDim1_G2.MultiLine = false;
      this.newTextBox_Side_GateDim1_G2.Name = "newTextBox_Side_GateDim1_G2";
      this.newTextBox_Side_GateDim1_G2.ReadOnly = false;
      this.newTextBox_Side_GateDim1_G2.Size = new Size(76, 23);
      this.newTextBox_Side_GateDim1_G2.TabIndex = 152;
      this.newTextBox_Side_GateDim1_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim1_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim1_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim1_G2.Value = "0";
      this.newTextBox_Side_GateDim2_G2.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim2_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim2_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim2_G2.IsDigit = true;
      this.newTextBox_Side_GateDim2_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim2_G2.Location = new Point(154, 113);
      this.newTextBox_Side_GateDim2_G2.MultiLine = false;
      this.newTextBox_Side_GateDim2_G2.Name = "newTextBox_Side_GateDim2_G2";
      this.newTextBox_Side_GateDim2_G2.ReadOnly = false;
      this.newTextBox_Side_GateDim2_G2.Size = new Size(76, 23);
      this.newTextBox_Side_GateDim2_G2.TabIndex = 151;
      this.newTextBox_Side_GateDim2_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim2_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim2_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim2_G2.Value = "0";
      this.newTextBox_Side_GateDim3_G2.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim3_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim3_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim3_G2.IsDigit = true;
      this.newTextBox_Side_GateDim3_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim3_G2.Location = new Point(154, 135);
      this.newTextBox_Side_GateDim3_G2.MultiLine = false;
      this.newTextBox_Side_GateDim3_G2.Name = "newTextBox_Side_GateDim3_G2";
      this.newTextBox_Side_GateDim3_G2.ReadOnly = false;
      this.newTextBox_Side_GateDim3_G2.Size = new Size(76, 23);
      this.newTextBox_Side_GateDim3_G2.TabIndex = 155;
      this.newTextBox_Side_GateDim3_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim3_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim3_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim3_G2.Value = "0";
      this.newTextBox_Side_GateDim4_G2.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim4_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim4_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim4_G2.IsDigit = true;
      this.newTextBox_Side_GateDim4_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim4_G2.Location = new Point(154, 157);
      this.newTextBox_Side_GateDim4_G2.MultiLine = false;
      this.newTextBox_Side_GateDim4_G2.Name = "newTextBox_Side_GateDim4_G2";
      this.newTextBox_Side_GateDim4_G2.ReadOnly = false;
      this.newTextBox_Side_GateDim4_G2.Size = new Size(76, 23);
      this.newTextBox_Side_GateDim4_G2.TabIndex = 154;
      this.newTextBox_Side_GateDim4_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim4_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim4_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim4_G2.Value = "0";
      this.newTextBox_Side_RunnerLength_G2.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerLength_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerLength_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerLength_G2.IsDigit = true;
      this.newTextBox_Side_RunnerLength_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerLength_G2.Location = new Point(154, 222);
      this.newTextBox_Side_RunnerLength_G2.MultiLine = false;
      this.newTextBox_Side_RunnerLength_G2.Name = "newTextBox_Side_RunnerLength_G2";
      this.newTextBox_Side_RunnerLength_G2.ReadOnly = false;
      this.newTextBox_Side_RunnerLength_G2.Size = new Size(76, 23);
      this.newTextBox_Side_RunnerLength_G2.TabIndex = 158;
      this.newTextBox_Side_RunnerLength_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerLength_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerLength_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerLength_G2.Value = "0";
      this.newTextBox_Side_RunnerDim1_G2.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim1_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim1_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim1_G2.IsDigit = true;
      this.newTextBox_Side_RunnerDim1_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim1_G2.Location = new Point(154, 244);
      this.newTextBox_Side_RunnerDim1_G2.MultiLine = false;
      this.newTextBox_Side_RunnerDim1_G2.Name = "newTextBox_Side_RunnerDim1_G2";
      this.newTextBox_Side_RunnerDim1_G2.ReadOnly = false;
      this.newTextBox_Side_RunnerDim1_G2.Size = new Size(76, 23);
      this.newTextBox_Side_RunnerDim1_G2.TabIndex = 157;
      this.newTextBox_Side_RunnerDim1_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim1_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim1_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim1_G2.Value = "0";
      this.newTextBox_Side_RunnerDim2_G2.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim2_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim2_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim2_G2.IsDigit = true;
      this.newTextBox_Side_RunnerDim2_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim2_G2.Location = new Point(154, 266);
      this.newTextBox_Side_RunnerDim2_G2.MultiLine = false;
      this.newTextBox_Side_RunnerDim2_G2.Name = "newTextBox_Side_RunnerDim2_G2";
      this.newTextBox_Side_RunnerDim2_G2.ReadOnly = false;
      this.newTextBox_Side_RunnerDim2_G2.Size = new Size(76, 23);
      this.newTextBox_Side_RunnerDim2_G2.TabIndex = 156;
      this.newTextBox_Side_RunnerDim2_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim2_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim2_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim2_G2.Value = "0";
      this.newTextBox_Side_RunnerDim3_G2.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim3_G2.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim3_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim3_G2.IsDigit = true;
      this.newTextBox_Side_RunnerDim3_G2.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim3_G2.Location = new Point(154, 288);
      this.newTextBox_Side_RunnerDim3_G2.MultiLine = false;
      this.newTextBox_Side_RunnerDim3_G2.Name = "newTextBox_Side_RunnerDim3_G2";
      this.newTextBox_Side_RunnerDim3_G2.ReadOnly = false;
      this.newTextBox_Side_RunnerDim3_G2.Size = new Size(76, 23);
      this.newTextBox_Side_RunnerDim3_G2.TabIndex = 159;
      this.newTextBox_Side_RunnerDim3_G2.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim3_G2.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim3_G2.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim3_G2.Value = "0";
      this.tabPage_Side_Group3.Controls.Add((Control) this.panel6);
      this.tabPage_Side_Group3.Controls.Add((Control) this.panel_Side_GateType2_G3);
      this.tabPage_Side_Group3.Controls.Add((Control) this.panel8);
      this.tabPage_Side_Group3.Controls.Add((Control) this.label_Side_Gate_G3);
      this.tabPage_Side_Group3.Controls.Add((Control) this.label_Side_Runner_G3);
      this.tabPage_Side_Group3.Controls.Add((Control) this.label_Side_GateLength_G3);
      this.tabPage_Side_Group3.Controls.Add((Control) this.label_Side_GateDim1_G3);
      this.tabPage_Side_Group3.Controls.Add((Control) this.label_Side_RunnerLength_G3);
      this.tabPage_Side_Group3.Controls.Add((Control) this.label_Side_GateDim3_G3);
      this.tabPage_Side_Group3.Controls.Add((Control) this.newTextBox_Side_RunnerDim4_G3);
      this.tabPage_Side_Group3.Controls.Add((Control) this.label_Side_RunnerDim2_G3);
      this.tabPage_Side_Group3.Controls.Add((Control) this.label_Side_RunnerDim4_G3);
      this.tabPage_Side_Group3.Controls.Add((Control) this.label_Side_GateDim2_G3);
      this.tabPage_Side_Group3.Controls.Add((Control) this.label_Side_RunnerDim1_G3);
      this.tabPage_Side_Group3.Controls.Add((Control) this.label_Side_GateDim4_G3);
      this.tabPage_Side_Group3.Controls.Add((Control) this.label_Side_RunnerDim3_G3);
      this.tabPage_Side_Group3.Controls.Add((Control) this.newTextBox_Side_GateLength_G3);
      this.tabPage_Side_Group3.Controls.Add((Control) this.newTextBox_Side_GateDim1_G3);
      this.tabPage_Side_Group3.Controls.Add((Control) this.newTextBox_Side_GateDim2_G3);
      this.tabPage_Side_Group3.Controls.Add((Control) this.newTextBox_Side_GateDim3_G3);
      this.tabPage_Side_Group3.Controls.Add((Control) this.newTextBox_Side_GateDim4_G3);
      this.tabPage_Side_Group3.Controls.Add((Control) this.newTextBox_Side_RunnerLength_G3);
      this.tabPage_Side_Group3.Controls.Add((Control) this.newTextBox_Side_RunnerDim1_G3);
      this.tabPage_Side_Group3.Controls.Add((Control) this.newTextBox_Side_RunnerDim2_G3);
      this.tabPage_Side_Group3.Controls.Add((Control) this.newTextBox_Side_RunnerDim3_G3);
      this.tabPage_Side_Group3.Location = new Point(4, 24);
      this.tabPage_Side_Group3.Name = "tabPage_Side_Group3";
      this.tabPage_Side_Group3.Size = new Size(230, 333);
      this.tabPage_Side_Group3.TabIndex = 2;
      this.tabPage_Side_Group3.UseVisualStyleBackColor = true;
      this.panel6.BackColor = Color.White;
      this.panel6.BorderStyle = BorderStyle.FixedSingle;
      this.panel6.Controls.Add((Control) this.radioButton_Side_GateCircle_G3);
      this.panel6.Controls.Add((Control) this.radioButton_Side_GateRect_G3);
      this.panel6.Location = new Point(2, 21);
      this.panel6.Name = "panel6";
      this.panel6.Size = new Size(228, 25);
      this.panel6.TabIndex = 162;
      this.radioButton_Side_GateCircle_G3.AutoSize = true;
      this.radioButton_Side_GateCircle_G3.Location = new Point(128, 2);
      this.radioButton_Side_GateCircle_G3.Name = "radioButton_Side_GateCircle_G3";
      this.radioButton_Side_GateCircle_G3.Size = new Size(55, 19);
      this.radioButton_Side_GateCircle_G3.TabIndex = 0;
      this.radioButton_Side_GateCircle_G3.Text = "Circle";
      this.radioButton_Side_GateCircle_G3.UseVisualStyleBackColor = true;
      this.radioButton_Side_GateCircle_G3.CheckedChanged += new EventHandler(this.radioButton_Side_GateCircle_CheckedChanged);
      this.radioButton_Side_GateRect_G3.AutoSize = true;
      this.radioButton_Side_GateRect_G3.Location = new Point(28, 2);
      this.radioButton_Side_GateRect_G3.Name = "radioButton_Side_GateRect_G3";
      this.radioButton_Side_GateRect_G3.Size = new Size(77, 19);
      this.radioButton_Side_GateRect_G3.TabIndex = 0;
      this.radioButton_Side_GateRect_G3.Text = "Rectangle";
      this.radioButton_Side_GateRect_G3.UseVisualStyleBackColor = true;
      this.radioButton_Side_GateRect_G3.CheckedChanged += new EventHandler(this.radioButton_Side_GateRect_CheckedChanged);
      this.panel_Side_GateType2_G3.BackColor = Color.White;
      this.panel_Side_GateType2_G3.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Side_GateType2_G3.Controls.Add((Control) this.radioButton_Side_GateSubMarine_G3);
      this.panel_Side_GateType2_G3.Controls.Add((Control) this.radioButton_Side_GateNormal_G3);
      this.panel_Side_GateType2_G3.Location = new Point(2, 45);
      this.panel_Side_GateType2_G3.Name = "panel_Side_GateType2_G3";
      this.panel_Side_GateType2_G3.Size = new Size(228, 25);
      this.panel_Side_GateType2_G3.TabIndex = 163;
      this.radioButton_Side_GateSubMarine_G3.AutoSize = true;
      this.radioButton_Side_GateSubMarine_G3.Location = new Point(128, 2);
      this.radioButton_Side_GateSubMarine_G3.Name = "radioButton_Side_GateSubMarine_G3";
      this.radioButton_Side_GateSubMarine_G3.Size = new Size(73, 19);
      this.radioButton_Side_GateSubMarine_G3.TabIndex = 0;
      this.radioButton_Side_GateSubMarine_G3.Text = "서브마린";
      this.radioButton_Side_GateSubMarine_G3.UseVisualStyleBackColor = true;
      this.radioButton_Side_GateNormal_G3.AutoSize = true;
      this.radioButton_Side_GateNormal_G3.Location = new Point(29, 2);
      this.radioButton_Side_GateNormal_G3.Name = "radioButton_Side_GateNormal_G3";
      this.radioButton_Side_GateNormal_G3.Size = new Size(49, 19);
      this.radioButton_Side_GateNormal_G3.TabIndex = 0;
      this.radioButton_Side_GateNormal_G3.Text = "일반";
      this.radioButton_Side_GateNormal_G3.UseVisualStyleBackColor = true;
      this.panel8.BackColor = Color.White;
      this.panel8.BorderStyle = BorderStyle.FixedSingle;
      this.panel8.Controls.Add((Control) this.radioButton_Side_RunnerRectangle_G3);
      this.panel8.Controls.Add((Control) this.radioButton_Side_RunnerCircle_G3);
      this.panel8.Controls.Add((Control) this.radioButton_Side_RunnerTrepezoidal_G3);
      this.panel8.Location = new Point(2, 198);
      this.panel8.Name = "panel8";
      this.panel8.Size = new Size(228, 25);
      this.panel8.TabIndex = 164;
      this.radioButton_Side_RunnerRectangle_G3.AutoSize = true;
      this.radioButton_Side_RunnerRectangle_G3.Location = new Point(146, 2);
      this.radioButton_Side_RunnerRectangle_G3.Name = "radioButton_Side_RunnerRectangle_G3";
      this.radioButton_Side_RunnerRectangle_G3.Size = new Size(77, 19);
      this.radioButton_Side_RunnerRectangle_G3.TabIndex = 1;
      this.radioButton_Side_RunnerRectangle_G3.Text = "Rectangle";
      this.radioButton_Side_RunnerRectangle_G3.UseVisualStyleBackColor = true;
      this.radioButton_Side_RunnerRectangle_G3.CheckedChanged += new EventHandler(this.radioButton_Side_RunnerRectangle_CheckedChanged);
      this.radioButton_Side_RunnerCircle_G3.AutoSize = true;
      this.radioButton_Side_RunnerCircle_G3.Location = new Point(88, 2);
      this.radioButton_Side_RunnerCircle_G3.Name = "radioButton_Side_RunnerCircle_G3";
      this.radioButton_Side_RunnerCircle_G3.Size = new Size(55, 19);
      this.radioButton_Side_RunnerCircle_G3.TabIndex = 0;
      this.radioButton_Side_RunnerCircle_G3.Text = "Circle";
      this.radioButton_Side_RunnerCircle_G3.UseVisualStyleBackColor = true;
      this.radioButton_Side_RunnerCircle_G3.CheckedChanged += new EventHandler(this.radioButton_Side_RunnerCircle_CheckedChanged);
      this.radioButton_Side_RunnerTrepezoidal_G3.AutoSize = true;
      this.radioButton_Side_RunnerTrepezoidal_G3.Location = new Point(3, 2);
      this.radioButton_Side_RunnerTrepezoidal_G3.Name = "radioButton_Side_RunnerTrepezoidal_G3";
      this.radioButton_Side_RunnerTrepezoidal_G3.Size = new Size(84, 19);
      this.radioButton_Side_RunnerTrepezoidal_G3.TabIndex = 0;
      this.radioButton_Side_RunnerTrepezoidal_G3.Text = "Trepezoidal";
      this.radioButton_Side_RunnerTrepezoidal_G3.UseVisualStyleBackColor = true;
      this.radioButton_Side_RunnerTrepezoidal_G3.CheckedChanged += new EventHandler(this.radioButton_Side_RunnerTrepezoidal_CheckedChanged);
      this.label_Side_Gate_G3.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Side_Gate_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_Gate_G3.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Side_Gate_G3.ForeColor = Color.MidnightBlue;
      this.label_Side_Gate_G3.Location = new Point(2, 2);
      this.label_Side_Gate_G3.Name = "label_Side_Gate_G3";
      this.label_Side_Gate_G3.Size = new Size(228, 20);
      this.label_Side_Gate_G3.TabIndex = 138;
      this.label_Side_Gate_G3.Text = "<게이트>";
      this.label_Side_Gate_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_Runner_G3.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Side_Runner_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_Runner_G3.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Side_Runner_G3.ForeColor = Color.MidnightBlue;
      this.label_Side_Runner_G3.Location = new Point(2, 179);
      this.label_Side_Runner_G3.Name = "label_Side_Runner_G3";
      this.label_Side_Runner_G3.Size = new Size(228, 20);
      this.label_Side_Runner_G3.TabIndex = 137;
      this.label_Side_Runner_G3.Text = "<런너>";
      this.label_Side_Runner_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateLength_G3.BackColor = Color.Lavender;
      this.label_Side_GateLength_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateLength_G3.Location = new Point(2, 69);
      this.label_Side_GateLength_G3.Name = "label_Side_GateLength_G3";
      this.label_Side_GateLength_G3.Size = new Size(153, 23);
      this.label_Side_GateLength_G3.TabIndex = 148;
      this.label_Side_GateLength_G3.Text = "게이트 길이";
      this.label_Side_GateLength_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim1_G3.BackColor = Color.Lavender;
      this.label_Side_GateDim1_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim1_G3.Location = new Point(2, 91);
      this.label_Side_GateDim1_G3.Name = "label_Side_GateDim1_G3";
      this.label_Side_GateDim1_G3.Size = new Size(153, 23);
      this.label_Side_GateDim1_G3.TabIndex = 146;
      this.label_Side_GateDim1_G3.Text = "Start Width";
      this.label_Side_GateDim1_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerLength_G3.BackColor = Color.Lavender;
      this.label_Side_RunnerLength_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerLength_G3.Location = new Point(2, 222);
      this.label_Side_RunnerLength_G3.Name = "label_Side_RunnerLength_G3";
      this.label_Side_RunnerLength_G3.Size = new Size(153, 23);
      this.label_Side_RunnerLength_G3.TabIndex = 145;
      this.label_Side_RunnerLength_G3.Text = "런너 길이";
      this.label_Side_RunnerLength_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim3_G3.BackColor = Color.Lavender;
      this.label_Side_GateDim3_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim3_G3.Location = new Point(2, 135);
      this.label_Side_GateDim3_G3.Name = "label_Side_GateDim3_G3";
      this.label_Side_GateDim3_G3.Size = new Size(153, 23);
      this.label_Side_GateDim3_G3.TabIndex = 144;
      this.label_Side_GateDim3_G3.Text = "End Width";
      this.label_Side_GateDim3_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Side_RunnerDim4_G3.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim4_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim4_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim4_G3.IsDigit = true;
      this.newTextBox_Side_RunnerDim4_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim4_G3.Location = new Point(154, 310);
      this.newTextBox_Side_RunnerDim4_G3.MultiLine = false;
      this.newTextBox_Side_RunnerDim4_G3.Name = "newTextBox_Side_RunnerDim4_G3";
      this.newTextBox_Side_RunnerDim4_G3.ReadOnly = false;
      this.newTextBox_Side_RunnerDim4_G3.Size = new Size(76, 23);
      this.newTextBox_Side_RunnerDim4_G3.TabIndex = 161;
      this.newTextBox_Side_RunnerDim4_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim4_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim4_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim4_G3.Value = "0";
      this.label_Side_RunnerDim2_G3.BackColor = Color.Lavender;
      this.label_Side_RunnerDim2_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim2_G3.Location = new Point(2, 266);
      this.label_Side_RunnerDim2_G3.Name = "label_Side_RunnerDim2_G3";
      this.label_Side_RunnerDim2_G3.Size = new Size(153, 23);
      this.label_Side_RunnerDim2_G3.TabIndex = 143;
      this.label_Side_RunnerDim2_G3.Text = "Bottom Width";
      this.label_Side_RunnerDim2_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim4_G3.BackColor = Color.Lavender;
      this.label_Side_RunnerDim4_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim4_G3.Location = new Point(2, 310);
      this.label_Side_RunnerDim4_G3.Name = "label_Side_RunnerDim4_G3";
      this.label_Side_RunnerDim4_G3.Size = new Size(153, 23);
      this.label_Side_RunnerDim4_G3.TabIndex = 160;
      this.label_Side_RunnerDim4_G3.Text = "End Height";
      this.label_Side_RunnerDim4_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim2_G3.BackColor = Color.Lavender;
      this.label_Side_GateDim2_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim2_G3.Location = new Point(2, 113);
      this.label_Side_GateDim2_G3.Name = "label_Side_GateDim2_G3";
      this.label_Side_GateDim2_G3.Size = new Size(153, 23);
      this.label_Side_GateDim2_G3.TabIndex = 142;
      this.label_Side_GateDim2_G3.Text = "Start Height";
      this.label_Side_GateDim2_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim1_G3.BackColor = Color.Lavender;
      this.label_Side_RunnerDim1_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim1_G3.Location = new Point(2, 244);
      this.label_Side_RunnerDim1_G3.Name = "label_Side_RunnerDim1_G3";
      this.label_Side_RunnerDim1_G3.Size = new Size(153, 23);
      this.label_Side_RunnerDim1_G3.TabIndex = 147;
      this.label_Side_RunnerDim1_G3.Text = "Top Width";
      this.label_Side_RunnerDim1_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim4_G3.BackColor = Color.Lavender;
      this.label_Side_GateDim4_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim4_G3.Location = new Point(2, 157);
      this.label_Side_GateDim4_G3.Name = "label_Side_GateDim4_G3";
      this.label_Side_GateDim4_G3.Size = new Size(153, 23);
      this.label_Side_GateDim4_G3.TabIndex = 150;
      this.label_Side_GateDim4_G3.Text = "End Height";
      this.label_Side_GateDim4_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim3_G3.BackColor = Color.Lavender;
      this.label_Side_RunnerDim3_G3.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim3_G3.Location = new Point(2, 288);
      this.label_Side_RunnerDim3_G3.Name = "label_Side_RunnerDim3_G3";
      this.label_Side_RunnerDim3_G3.Size = new Size(153, 23);
      this.label_Side_RunnerDim3_G3.TabIndex = 149;
      this.label_Side_RunnerDim3_G3.Text = "Height";
      this.label_Side_RunnerDim3_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Side_GateLength_G3.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateLength_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateLength_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateLength_G3.IsDigit = true;
      this.newTextBox_Side_GateLength_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateLength_G3.Location = new Point(154, 69);
      this.newTextBox_Side_GateLength_G3.MultiLine = false;
      this.newTextBox_Side_GateLength_G3.Name = "newTextBox_Side_GateLength_G3";
      this.newTextBox_Side_GateLength_G3.ReadOnly = false;
      this.newTextBox_Side_GateLength_G3.Size = new Size(76, 23);
      this.newTextBox_Side_GateLength_G3.TabIndex = 153;
      this.newTextBox_Side_GateLength_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateLength_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateLength_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateLength_G3.Value = "0";
      this.newTextBox_Side_GateDim1_G3.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim1_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim1_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim1_G3.IsDigit = true;
      this.newTextBox_Side_GateDim1_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim1_G3.Location = new Point(154, 91);
      this.newTextBox_Side_GateDim1_G3.MultiLine = false;
      this.newTextBox_Side_GateDim1_G3.Name = "newTextBox_Side_GateDim1_G3";
      this.newTextBox_Side_GateDim1_G3.ReadOnly = false;
      this.newTextBox_Side_GateDim1_G3.Size = new Size(76, 23);
      this.newTextBox_Side_GateDim1_G3.TabIndex = 152;
      this.newTextBox_Side_GateDim1_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim1_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim1_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim1_G3.Value = "0";
      this.newTextBox_Side_GateDim2_G3.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim2_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim2_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim2_G3.IsDigit = true;
      this.newTextBox_Side_GateDim2_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim2_G3.Location = new Point(154, 113);
      this.newTextBox_Side_GateDim2_G3.MultiLine = false;
      this.newTextBox_Side_GateDim2_G3.Name = "newTextBox_Side_GateDim2_G3";
      this.newTextBox_Side_GateDim2_G3.ReadOnly = false;
      this.newTextBox_Side_GateDim2_G3.Size = new Size(76, 23);
      this.newTextBox_Side_GateDim2_G3.TabIndex = 151;
      this.newTextBox_Side_GateDim2_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim2_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim2_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim2_G3.Value = "0";
      this.newTextBox_Side_GateDim3_G3.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim3_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim3_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim3_G3.IsDigit = true;
      this.newTextBox_Side_GateDim3_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim3_G3.Location = new Point(154, 135);
      this.newTextBox_Side_GateDim3_G3.MultiLine = false;
      this.newTextBox_Side_GateDim3_G3.Name = "newTextBox_Side_GateDim3_G3";
      this.newTextBox_Side_GateDim3_G3.ReadOnly = false;
      this.newTextBox_Side_GateDim3_G3.Size = new Size(76, 23);
      this.newTextBox_Side_GateDim3_G3.TabIndex = 155;
      this.newTextBox_Side_GateDim3_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim3_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim3_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim3_G3.Value = "0";
      this.newTextBox_Side_GateDim4_G3.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim4_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim4_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim4_G3.IsDigit = true;
      this.newTextBox_Side_GateDim4_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim4_G3.Location = new Point(154, 157);
      this.newTextBox_Side_GateDim4_G3.MultiLine = false;
      this.newTextBox_Side_GateDim4_G3.Name = "newTextBox_Side_GateDim4_G3";
      this.newTextBox_Side_GateDim4_G3.ReadOnly = false;
      this.newTextBox_Side_GateDim4_G3.Size = new Size(76, 23);
      this.newTextBox_Side_GateDim4_G3.TabIndex = 154;
      this.newTextBox_Side_GateDim4_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim4_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim4_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim4_G3.Value = "0";
      this.newTextBox_Side_RunnerLength_G3.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerLength_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerLength_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerLength_G3.IsDigit = true;
      this.newTextBox_Side_RunnerLength_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerLength_G3.Location = new Point(154, 222);
      this.newTextBox_Side_RunnerLength_G3.MultiLine = false;
      this.newTextBox_Side_RunnerLength_G3.Name = "newTextBox_Side_RunnerLength_G3";
      this.newTextBox_Side_RunnerLength_G3.ReadOnly = false;
      this.newTextBox_Side_RunnerLength_G3.Size = new Size(76, 23);
      this.newTextBox_Side_RunnerLength_G3.TabIndex = 158;
      this.newTextBox_Side_RunnerLength_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerLength_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerLength_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerLength_G3.Value = "0";
      this.newTextBox_Side_RunnerDim1_G3.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim1_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim1_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim1_G3.IsDigit = true;
      this.newTextBox_Side_RunnerDim1_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim1_G3.Location = new Point(154, 244);
      this.newTextBox_Side_RunnerDim1_G3.MultiLine = false;
      this.newTextBox_Side_RunnerDim1_G3.Name = "newTextBox_Side_RunnerDim1_G3";
      this.newTextBox_Side_RunnerDim1_G3.ReadOnly = false;
      this.newTextBox_Side_RunnerDim1_G3.Size = new Size(76, 23);
      this.newTextBox_Side_RunnerDim1_G3.TabIndex = 157;
      this.newTextBox_Side_RunnerDim1_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim1_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim1_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim1_G3.Value = "0";
      this.newTextBox_Side_RunnerDim2_G3.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim2_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim2_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim2_G3.IsDigit = true;
      this.newTextBox_Side_RunnerDim2_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim2_G3.Location = new Point(154, 266);
      this.newTextBox_Side_RunnerDim2_G3.MultiLine = false;
      this.newTextBox_Side_RunnerDim2_G3.Name = "newTextBox_Side_RunnerDim2_G3";
      this.newTextBox_Side_RunnerDim2_G3.ReadOnly = false;
      this.newTextBox_Side_RunnerDim2_G3.Size = new Size(76, 23);
      this.newTextBox_Side_RunnerDim2_G3.TabIndex = 156;
      this.newTextBox_Side_RunnerDim2_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim2_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim2_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim2_G3.Value = "0";
      this.newTextBox_Side_RunnerDim3_G3.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim3_G3.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim3_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim3_G3.IsDigit = true;
      this.newTextBox_Side_RunnerDim3_G3.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim3_G3.Location = new Point(154, 288);
      this.newTextBox_Side_RunnerDim3_G3.MultiLine = false;
      this.newTextBox_Side_RunnerDim3_G3.Name = "newTextBox_Side_RunnerDim3_G3";
      this.newTextBox_Side_RunnerDim3_G3.ReadOnly = false;
      this.newTextBox_Side_RunnerDim3_G3.Size = new Size(76, 23);
      this.newTextBox_Side_RunnerDim3_G3.TabIndex = 159;
      this.newTextBox_Side_RunnerDim3_G3.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim3_G3.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim3_G3.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim3_G3.Value = "0";
      this.tabPage_Side_Group4.Controls.Add((Control) this.label_Side_Gate_G4);
      this.tabPage_Side_Group4.Controls.Add((Control) this.label_Side_Runner_G4);
      this.tabPage_Side_Group4.Controls.Add((Control) this.panel18);
      this.tabPage_Side_Group4.Controls.Add((Control) this.panel_Side_GateType2_G4);
      this.tabPage_Side_Group4.Controls.Add((Control) this.panel20);
      this.tabPage_Side_Group4.Controls.Add((Control) this.label_Side_GateLength_G4);
      this.tabPage_Side_Group4.Controls.Add((Control) this.label_Side_GateDim1_G4);
      this.tabPage_Side_Group4.Controls.Add((Control) this.label_Side_RunnerLength_G4);
      this.tabPage_Side_Group4.Controls.Add((Control) this.label_Side_GateDim3_G4);
      this.tabPage_Side_Group4.Controls.Add((Control) this.newTextBox_Side_RunnerDim4_G4);
      this.tabPage_Side_Group4.Controls.Add((Control) this.label_Side_RunnerDim2_G4);
      this.tabPage_Side_Group4.Controls.Add((Control) this.label_Side_RunnerDim4_G4);
      this.tabPage_Side_Group4.Controls.Add((Control) this.label_Side_GateDim2_G4);
      this.tabPage_Side_Group4.Controls.Add((Control) this.label_Side_RunnerDim1_G4);
      this.tabPage_Side_Group4.Controls.Add((Control) this.label_Side_GateDim4_G4);
      this.tabPage_Side_Group4.Controls.Add((Control) this.label_Side_RunnerDim3_G4);
      this.tabPage_Side_Group4.Controls.Add((Control) this.newTextBox_Side_GateLength_G4);
      this.tabPage_Side_Group4.Controls.Add((Control) this.newTextBox_Side_GateDim1_G4);
      this.tabPage_Side_Group4.Controls.Add((Control) this.newTextBox_Side_GateDim2_G4);
      this.tabPage_Side_Group4.Controls.Add((Control) this.newTextBox_Side_GateDim3_G4);
      this.tabPage_Side_Group4.Controls.Add((Control) this.newTextBox_Side_GateDim4_G4);
      this.tabPage_Side_Group4.Controls.Add((Control) this.newTextBox_Side_RunnerLength_G4);
      this.tabPage_Side_Group4.Controls.Add((Control) this.newTextBox_Side_RunnerDim1_G4);
      this.tabPage_Side_Group4.Controls.Add((Control) this.newTextBox_Side_RunnerDim2_G4);
      this.tabPage_Side_Group4.Controls.Add((Control) this.newTextBox_Side_RunnerDim3_G4);
      this.tabPage_Side_Group4.Location = new Point(4, 24);
      this.tabPage_Side_Group4.Name = "tabPage_Side_Group4";
      this.tabPage_Side_Group4.Size = new Size(230, 333);
      this.tabPage_Side_Group4.TabIndex = 3;
      this.tabPage_Side_Group4.UseVisualStyleBackColor = true;
      this.label_Side_Gate_G4.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Side_Gate_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_Gate_G4.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Side_Gate_G4.ForeColor = Color.MidnightBlue;
      this.label_Side_Gate_G4.Location = new Point(2, 2);
      this.label_Side_Gate_G4.Name = "label_Side_Gate_G4";
      this.label_Side_Gate_G4.Size = new Size(228, 20);
      this.label_Side_Gate_G4.TabIndex = 138;
      this.label_Side_Gate_G4.Text = "<게이트>";
      this.label_Side_Gate_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_Runner_G4.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Side_Runner_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_Runner_G4.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Side_Runner_G4.ForeColor = Color.MidnightBlue;
      this.label_Side_Runner_G4.Location = new Point(2, 179);
      this.label_Side_Runner_G4.Name = "label_Side_Runner_G4";
      this.label_Side_Runner_G4.Size = new Size(228, 20);
      this.label_Side_Runner_G4.TabIndex = 137;
      this.label_Side_Runner_G4.Text = "<런너>";
      this.label_Side_Runner_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.panel18.BackColor = Color.White;
      this.panel18.BorderStyle = BorderStyle.FixedSingle;
      this.panel18.Controls.Add((Control) this.radioButton_Side_GateCircle_G4);
      this.panel18.Controls.Add((Control) this.radioButton_Side_GateRect_G4);
      this.panel18.Location = new Point(2, 21);
      this.panel18.Name = "panel18";
      this.panel18.Size = new Size(228, 25);
      this.panel18.TabIndex = 139;
      this.radioButton_Side_GateCircle_G4.AutoSize = true;
      this.radioButton_Side_GateCircle_G4.Location = new Point(128, 2);
      this.radioButton_Side_GateCircle_G4.Name = "radioButton_Side_GateCircle_G4";
      this.radioButton_Side_GateCircle_G4.Size = new Size(55, 19);
      this.radioButton_Side_GateCircle_G4.TabIndex = 0;
      this.radioButton_Side_GateCircle_G4.Text = "Circle";
      this.radioButton_Side_GateCircle_G4.UseVisualStyleBackColor = true;
      this.radioButton_Side_GateCircle_G4.CheckedChanged += new EventHandler(this.radioButton_Side_GateCircle_CheckedChanged);
      this.radioButton_Side_GateRect_G4.AutoSize = true;
      this.radioButton_Side_GateRect_G4.Location = new Point(28, 2);
      this.radioButton_Side_GateRect_G4.Name = "radioButton_Side_GateRect_G4";
      this.radioButton_Side_GateRect_G4.Size = new Size(77, 19);
      this.radioButton_Side_GateRect_G4.TabIndex = 0;
      this.radioButton_Side_GateRect_G4.Text = "Rectangle";
      this.radioButton_Side_GateRect_G4.UseVisualStyleBackColor = true;
      this.radioButton_Side_GateRect_G4.CheckedChanged += new EventHandler(this.radioButton_Side_GateRect_CheckedChanged);
      this.panel_Side_GateType2_G4.BackColor = Color.White;
      this.panel_Side_GateType2_G4.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Side_GateType2_G4.Controls.Add((Control) this.radioButton_Side_GateSubMarine_G4);
      this.panel_Side_GateType2_G4.Controls.Add((Control) this.radioButton_Side_GateNormal_G4);
      this.panel_Side_GateType2_G4.Location = new Point(2, 45);
      this.panel_Side_GateType2_G4.Name = "panel_Side_GateType2_G4";
      this.panel_Side_GateType2_G4.Size = new Size(228, 25);
      this.panel_Side_GateType2_G4.TabIndex = 140;
      this.radioButton_Side_GateSubMarine_G4.AutoSize = true;
      this.radioButton_Side_GateSubMarine_G4.Location = new Point(128, 2);
      this.radioButton_Side_GateSubMarine_G4.Name = "radioButton_Side_GateSubMarine_G4";
      this.radioButton_Side_GateSubMarine_G4.Size = new Size(73, 19);
      this.radioButton_Side_GateSubMarine_G4.TabIndex = 0;
      this.radioButton_Side_GateSubMarine_G4.Text = "서브마린";
      this.radioButton_Side_GateSubMarine_G4.UseVisualStyleBackColor = true;
      this.radioButton_Side_GateNormal_G4.AutoSize = true;
      this.radioButton_Side_GateNormal_G4.Location = new Point(29, 2);
      this.radioButton_Side_GateNormal_G4.Name = "radioButton_Side_GateNormal_G4";
      this.radioButton_Side_GateNormal_G4.Size = new Size(49, 19);
      this.radioButton_Side_GateNormal_G4.TabIndex = 0;
      this.radioButton_Side_GateNormal_G4.Text = "일반";
      this.radioButton_Side_GateNormal_G4.UseVisualStyleBackColor = true;
      this.panel20.BackColor = Color.White;
      this.panel20.BorderStyle = BorderStyle.FixedSingle;
      this.panel20.Controls.Add((Control) this.radioButton_Side_RunnerRectangle_G4);
      this.panel20.Controls.Add((Control) this.radioButton_Side_RunnerCircle_G4);
      this.panel20.Controls.Add((Control) this.radioButton_Side_RunnerTrepezoidal_G4);
      this.panel20.Location = new Point(2, 198);
      this.panel20.Name = "panel20";
      this.panel20.Size = new Size(228, 25);
      this.panel20.TabIndex = 141;
      this.radioButton_Side_RunnerRectangle_G4.AutoSize = true;
      this.radioButton_Side_RunnerRectangle_G4.Location = new Point(146, 2);
      this.radioButton_Side_RunnerRectangle_G4.Name = "radioButton_Side_RunnerRectangle_G4";
      this.radioButton_Side_RunnerRectangle_G4.Size = new Size(77, 19);
      this.radioButton_Side_RunnerRectangle_G4.TabIndex = 1;
      this.radioButton_Side_RunnerRectangle_G4.Text = "Rectangle";
      this.radioButton_Side_RunnerRectangle_G4.UseVisualStyleBackColor = true;
      this.radioButton_Side_RunnerRectangle_G4.CheckedChanged += new EventHandler(this.radioButton_Side_RunnerRectangle_CheckedChanged);
      this.radioButton_Side_RunnerCircle_G4.AutoSize = true;
      this.radioButton_Side_RunnerCircle_G4.Location = new Point(88, 2);
      this.radioButton_Side_RunnerCircle_G4.Name = "radioButton_Side_RunnerCircle_G4";
      this.radioButton_Side_RunnerCircle_G4.Size = new Size(55, 19);
      this.radioButton_Side_RunnerCircle_G4.TabIndex = 0;
      this.radioButton_Side_RunnerCircle_G4.Text = "Circle";
      this.radioButton_Side_RunnerCircle_G4.UseVisualStyleBackColor = true;
      this.radioButton_Side_RunnerCircle_G4.CheckedChanged += new EventHandler(this.radioButton_Side_RunnerCircle_CheckedChanged);
      this.radioButton_Side_RunnerTrepezoidal_G4.AutoSize = true;
      this.radioButton_Side_RunnerTrepezoidal_G4.Location = new Point(3, 2);
      this.radioButton_Side_RunnerTrepezoidal_G4.Name = "radioButton_Side_RunnerTrepezoidal_G4";
      this.radioButton_Side_RunnerTrepezoidal_G4.Size = new Size(84, 19);
      this.radioButton_Side_RunnerTrepezoidal_G4.TabIndex = 0;
      this.radioButton_Side_RunnerTrepezoidal_G4.Text = "Trepezoidal";
      this.radioButton_Side_RunnerTrepezoidal_G4.UseVisualStyleBackColor = true;
      this.radioButton_Side_RunnerTrepezoidal_G4.CheckedChanged += new EventHandler(this.radioButton_Side_RunnerTrepezoidal_CheckedChanged);
      this.label_Side_GateLength_G4.BackColor = Color.Lavender;
      this.label_Side_GateLength_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateLength_G4.Location = new Point(2, 69);
      this.label_Side_GateLength_G4.Name = "label_Side_GateLength_G4";
      this.label_Side_GateLength_G4.Size = new Size(153, 23);
      this.label_Side_GateLength_G4.TabIndex = 148;
      this.label_Side_GateLength_G4.Text = "게이트 길이";
      this.label_Side_GateLength_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim1_G4.BackColor = Color.Lavender;
      this.label_Side_GateDim1_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim1_G4.Location = new Point(2, 91);
      this.label_Side_GateDim1_G4.Name = "label_Side_GateDim1_G4";
      this.label_Side_GateDim1_G4.Size = new Size(153, 23);
      this.label_Side_GateDim1_G4.TabIndex = 146;
      this.label_Side_GateDim1_G4.Text = "Start Width";
      this.label_Side_GateDim1_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerLength_G4.BackColor = Color.Lavender;
      this.label_Side_RunnerLength_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerLength_G4.Location = new Point(2, 222);
      this.label_Side_RunnerLength_G4.Name = "label_Side_RunnerLength_G4";
      this.label_Side_RunnerLength_G4.Size = new Size(153, 23);
      this.label_Side_RunnerLength_G4.TabIndex = 145;
      this.label_Side_RunnerLength_G4.Text = "런너 길이";
      this.label_Side_RunnerLength_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim3_G4.BackColor = Color.Lavender;
      this.label_Side_GateDim3_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim3_G4.Location = new Point(2, 135);
      this.label_Side_GateDim3_G4.Name = "label_Side_GateDim3_G4";
      this.label_Side_GateDim3_G4.Size = new Size(153, 23);
      this.label_Side_GateDim3_G4.TabIndex = 144;
      this.label_Side_GateDim3_G4.Text = "End Width";
      this.label_Side_GateDim3_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Side_RunnerDim4_G4.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim4_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim4_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim4_G4.IsDigit = true;
      this.newTextBox_Side_RunnerDim4_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim4_G4.Location = new Point(154, 310);
      this.newTextBox_Side_RunnerDim4_G4.MultiLine = false;
      this.newTextBox_Side_RunnerDim4_G4.Name = "newTextBox_Side_RunnerDim4_G4";
      this.newTextBox_Side_RunnerDim4_G4.ReadOnly = false;
      this.newTextBox_Side_RunnerDim4_G4.Size = new Size(76, 23);
      this.newTextBox_Side_RunnerDim4_G4.TabIndex = 161;
      this.newTextBox_Side_RunnerDim4_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim4_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim4_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim4_G4.Value = "0";
      this.label_Side_RunnerDim2_G4.BackColor = Color.Lavender;
      this.label_Side_RunnerDim2_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim2_G4.Location = new Point(2, 266);
      this.label_Side_RunnerDim2_G4.Name = "label_Side_RunnerDim2_G4";
      this.label_Side_RunnerDim2_G4.Size = new Size(153, 23);
      this.label_Side_RunnerDim2_G4.TabIndex = 143;
      this.label_Side_RunnerDim2_G4.Text = "Bottom Width";
      this.label_Side_RunnerDim2_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim4_G4.BackColor = Color.Lavender;
      this.label_Side_RunnerDim4_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim4_G4.Location = new Point(2, 310);
      this.label_Side_RunnerDim4_G4.Name = "label_Side_RunnerDim4_G4";
      this.label_Side_RunnerDim4_G4.Size = new Size(153, 23);
      this.label_Side_RunnerDim4_G4.TabIndex = 160;
      this.label_Side_RunnerDim4_G4.Text = "End Height";
      this.label_Side_RunnerDim4_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim2_G4.BackColor = Color.Lavender;
      this.label_Side_GateDim2_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim2_G4.Location = new Point(2, 113);
      this.label_Side_GateDim2_G4.Name = "label_Side_GateDim2_G4";
      this.label_Side_GateDim2_G4.Size = new Size(153, 23);
      this.label_Side_GateDim2_G4.TabIndex = 142;
      this.label_Side_GateDim2_G4.Text = "Start Height";
      this.label_Side_GateDim2_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim1_G4.BackColor = Color.Lavender;
      this.label_Side_RunnerDim1_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim1_G4.Location = new Point(2, 244);
      this.label_Side_RunnerDim1_G4.Name = "label_Side_RunnerDim1_G4";
      this.label_Side_RunnerDim1_G4.Size = new Size(153, 23);
      this.label_Side_RunnerDim1_G4.TabIndex = 147;
      this.label_Side_RunnerDim1_G4.Text = "Top Width";
      this.label_Side_RunnerDim1_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_GateDim4_G4.BackColor = Color.Lavender;
      this.label_Side_GateDim4_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_GateDim4_G4.Location = new Point(2, 157);
      this.label_Side_GateDim4_G4.Name = "label_Side_GateDim4_G4";
      this.label_Side_GateDim4_G4.Size = new Size(153, 23);
      this.label_Side_GateDim4_G4.TabIndex = 150;
      this.label_Side_GateDim4_G4.Text = "End Height";
      this.label_Side_GateDim4_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Side_RunnerDim3_G4.BackColor = Color.Lavender;
      this.label_Side_RunnerDim3_G4.BorderStyle = BorderStyle.FixedSingle;
      this.label_Side_RunnerDim3_G4.Location = new Point(2, 288);
      this.label_Side_RunnerDim3_G4.Name = "label_Side_RunnerDim3_G4";
      this.label_Side_RunnerDim3_G4.Size = new Size(153, 23);
      this.label_Side_RunnerDim3_G4.TabIndex = 149;
      this.label_Side_RunnerDim3_G4.Text = "Height";
      this.label_Side_RunnerDim3_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Side_GateLength_G4.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateLength_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateLength_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateLength_G4.IsDigit = true;
      this.newTextBox_Side_GateLength_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateLength_G4.Location = new Point(154, 69);
      this.newTextBox_Side_GateLength_G4.MultiLine = false;
      this.newTextBox_Side_GateLength_G4.Name = "newTextBox_Side_GateLength_G4";
      this.newTextBox_Side_GateLength_G4.ReadOnly = false;
      this.newTextBox_Side_GateLength_G4.Size = new Size(76, 23);
      this.newTextBox_Side_GateLength_G4.TabIndex = 153;
      this.newTextBox_Side_GateLength_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateLength_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateLength_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateLength_G4.Value = "0";
      this.newTextBox_Side_GateDim1_G4.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim1_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim1_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim1_G4.IsDigit = true;
      this.newTextBox_Side_GateDim1_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim1_G4.Location = new Point(154, 91);
      this.newTextBox_Side_GateDim1_G4.MultiLine = false;
      this.newTextBox_Side_GateDim1_G4.Name = "newTextBox_Side_GateDim1_G4";
      this.newTextBox_Side_GateDim1_G4.ReadOnly = false;
      this.newTextBox_Side_GateDim1_G4.Size = new Size(76, 23);
      this.newTextBox_Side_GateDim1_G4.TabIndex = 152;
      this.newTextBox_Side_GateDim1_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim1_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim1_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim1_G4.Value = "0";
      this.newTextBox_Side_GateDim2_G4.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim2_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim2_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim2_G4.IsDigit = true;
      this.newTextBox_Side_GateDim2_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim2_G4.Location = new Point(154, 113);
      this.newTextBox_Side_GateDim2_G4.MultiLine = false;
      this.newTextBox_Side_GateDim2_G4.Name = "newTextBox_Side_GateDim2_G4";
      this.newTextBox_Side_GateDim2_G4.ReadOnly = false;
      this.newTextBox_Side_GateDim2_G4.Size = new Size(76, 23);
      this.newTextBox_Side_GateDim2_G4.TabIndex = 151;
      this.newTextBox_Side_GateDim2_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim2_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim2_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim2_G4.Value = "0";
      this.newTextBox_Side_GateDim3_G4.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim3_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim3_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim3_G4.IsDigit = true;
      this.newTextBox_Side_GateDim3_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim3_G4.Location = new Point(154, 135);
      this.newTextBox_Side_GateDim3_G4.MultiLine = false;
      this.newTextBox_Side_GateDim3_G4.Name = "newTextBox_Side_GateDim3_G4";
      this.newTextBox_Side_GateDim3_G4.ReadOnly = false;
      this.newTextBox_Side_GateDim3_G4.Size = new Size(76, 23);
      this.newTextBox_Side_GateDim3_G4.TabIndex = 155;
      this.newTextBox_Side_GateDim3_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim3_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim3_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim3_G4.Value = "0";
      this.newTextBox_Side_GateDim4_G4.BackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim4_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_GateDim4_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_GateDim4_G4.IsDigit = true;
      this.newTextBox_Side_GateDim4_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_GateDim4_G4.Location = new Point(154, 157);
      this.newTextBox_Side_GateDim4_G4.MultiLine = false;
      this.newTextBox_Side_GateDim4_G4.Name = "newTextBox_Side_GateDim4_G4";
      this.newTextBox_Side_GateDim4_G4.ReadOnly = false;
      this.newTextBox_Side_GateDim4_G4.Size = new Size(76, 23);
      this.newTextBox_Side_GateDim4_G4.TabIndex = 154;
      this.newTextBox_Side_GateDim4_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_GateDim4_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_GateDim4_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_GateDim4_G4.Value = "0";
      this.newTextBox_Side_RunnerLength_G4.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerLength_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerLength_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerLength_G4.IsDigit = true;
      this.newTextBox_Side_RunnerLength_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerLength_G4.Location = new Point(154, 222);
      this.newTextBox_Side_RunnerLength_G4.MultiLine = false;
      this.newTextBox_Side_RunnerLength_G4.Name = "newTextBox_Side_RunnerLength_G4";
      this.newTextBox_Side_RunnerLength_G4.ReadOnly = false;
      this.newTextBox_Side_RunnerLength_G4.Size = new Size(76, 23);
      this.newTextBox_Side_RunnerLength_G4.TabIndex = 158;
      this.newTextBox_Side_RunnerLength_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerLength_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerLength_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerLength_G4.Value = "0";
      this.newTextBox_Side_RunnerDim1_G4.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim1_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim1_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim1_G4.IsDigit = true;
      this.newTextBox_Side_RunnerDim1_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim1_G4.Location = new Point(154, 244);
      this.newTextBox_Side_RunnerDim1_G4.MultiLine = false;
      this.newTextBox_Side_RunnerDim1_G4.Name = "newTextBox_Side_RunnerDim1_G4";
      this.newTextBox_Side_RunnerDim1_G4.ReadOnly = false;
      this.newTextBox_Side_RunnerDim1_G4.Size = new Size(76, 23);
      this.newTextBox_Side_RunnerDim1_G4.TabIndex = 157;
      this.newTextBox_Side_RunnerDim1_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim1_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim1_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim1_G4.Value = "0";
      this.newTextBox_Side_RunnerDim2_G4.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim2_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim2_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim2_G4.IsDigit = true;
      this.newTextBox_Side_RunnerDim2_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim2_G4.Location = new Point(154, 266);
      this.newTextBox_Side_RunnerDim2_G4.MultiLine = false;
      this.newTextBox_Side_RunnerDim2_G4.Name = "newTextBox_Side_RunnerDim2_G4";
      this.newTextBox_Side_RunnerDim2_G4.ReadOnly = false;
      this.newTextBox_Side_RunnerDim2_G4.Size = new Size(76, 23);
      this.newTextBox_Side_RunnerDim2_G4.TabIndex = 156;
      this.newTextBox_Side_RunnerDim2_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim2_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim2_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim2_G4.Value = "0";
      this.newTextBox_Side_RunnerDim3_G4.BackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim3_G4.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Side_RunnerDim3_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Side_RunnerDim3_G4.IsDigit = true;
      this.newTextBox_Side_RunnerDim3_G4.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Side_RunnerDim3_G4.Location = new Point(154, 288);
      this.newTextBox_Side_RunnerDim3_G4.MultiLine = false;
      this.newTextBox_Side_RunnerDim3_G4.Name = "newTextBox_Side_RunnerDim3_G4";
      this.newTextBox_Side_RunnerDim3_G4.ReadOnly = false;
      this.newTextBox_Side_RunnerDim3_G4.Size = new Size(76, 23);
      this.newTextBox_Side_RunnerDim3_G4.TabIndex = 159;
      this.newTextBox_Side_RunnerDim3_G4.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Side_RunnerDim3_G4.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Side_RunnerDim3_G4.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Side_RunnerDim3_G4.Value = "0";
      this.newButton_Side_G3.ButtonBackColor = Color.White;
      this.newButton_Side_G3.ButtonText = "그룹3";
      this.newButton_Side_G3.FlatBorderSize = 1;
      this.newButton_Side_G3.FlatStyle = FlatStyle.Flat;
      this.newButton_Side_G3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Side_G3.Image = (Image) null;
      this.newButton_Side_G3.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Side_G3.Location = new Point(114, 0);
      this.newButton_Side_G3.Name = "newButton_Side_G3";
      this.newButton_Side_G3.Size = new Size(58, 23);
      this.newButton_Side_G3.TabIndex = 56;
      this.newButton_Side_G3.TabStop = false;
      this.newButton_Side_G3.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Side_G3.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Side_G3.NewClick += new EventHandler(this.newButton_Group_NewClick);
      this.newButton_Side_G2.ButtonBackColor = Color.White;
      this.newButton_Side_G2.ButtonText = "그룹2";
      this.newButton_Side_G2.FlatBorderSize = 1;
      this.newButton_Side_G2.FlatStyle = FlatStyle.Flat;
      this.newButton_Side_G2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Side_G2.Image = (Image) null;
      this.newButton_Side_G2.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Side_G2.Location = new Point(57, 0);
      this.newButton_Side_G2.Name = "newButton_Side_G2";
      this.newButton_Side_G2.Size = new Size(58, 23);
      this.newButton_Side_G2.TabIndex = 55;
      this.newButton_Side_G2.TabStop = false;
      this.newButton_Side_G2.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Side_G2.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Side_G2.NewClick += new EventHandler(this.newButton_Group_NewClick);
      this.newButton_Side_G4.ButtonBackColor = Color.White;
      this.newButton_Side_G4.ButtonText = "그룹4";
      this.newButton_Side_G4.FlatBorderSize = 1;
      this.newButton_Side_G4.FlatStyle = FlatStyle.Flat;
      this.newButton_Side_G4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Side_G4.Image = (Image) null;
      this.newButton_Side_G4.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Side_G4.Location = new Point(170, 0);
      this.newButton_Side_G4.Name = "newButton_Side_G4";
      this.newButton_Side_G4.Size = new Size(58, 23);
      this.newButton_Side_G4.TabIndex = 57;
      this.newButton_Side_G4.TabStop = false;
      this.newButton_Side_G4.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Side_G4.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Side_G4.NewClick += new EventHandler(this.newButton_Group_NewClick);
      this.newButton_Side_G1.ButtonBackColor = Color.LightSteelBlue;
      this.newButton_Side_G1.ButtonText = "그룹1";
      this.newButton_Side_G1.FlatBorderSize = 1;
      this.newButton_Side_G1.FlatStyle = FlatStyle.Flat;
      this.newButton_Side_G1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Side_G1.Image = (Image) null;
      this.newButton_Side_G1.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Side_G1.Location = new Point(0, 0);
      this.newButton_Side_G1.Name = "newButton_Side_G1";
      this.newButton_Side_G1.Size = new Size(58, 23);
      this.newButton_Side_G1.TabIndex = 54;
      this.newButton_Side_G1.TabStop = false;
      this.newButton_Side_G1.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Side_G1.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Side_G1.NewClick += new EventHandler(this.newButton_Group_NewClick);
      this.panel_Side_Group.Controls.Add((Control) this.newButton_Side_G1);
      this.panel_Side_Group.Controls.Add((Control) this.newButton_Side_G4);
      this.panel_Side_Group.Controls.Add((Control) this.newButton_Side_G2);
      this.panel_Side_Group.Controls.Add((Control) this.newButton_Side_G3);
      this.panel_Side_Group.Controls.Add((Control) this.tabControl_Side_Group);
      this.panel_Side_Group.Location = new Point(542, 24);
      this.panel_Side_Group.Name = "panel_Side_Group";
      this.panel_Side_Group.Size = new Size(228, 353);
      this.panel_Side_Group.TabIndex = 53;
      this.panel_Hor_RunnerType.Controls.Add((Control) this.newButton_Hor_Runner_TwoStage);
      this.panel_Hor_RunnerType.Controls.Add((Control) this.newButton_Hor_Runner_ThreeStage);
      this.panel_Hor_RunnerType.Controls.Add((Control) this.tabControl_Hor_RunnerType);
      this.panel_Hor_RunnerType.Location = new Point(310, 290);
      this.panel_Hor_RunnerType.Name = "panel_Hor_RunnerType";
      this.panel_Hor_RunnerType.Size = new Size(228, 222);
      this.panel_Hor_RunnerType.TabIndex = 84;
      this.newButton_Hor_Runner_TwoStage.ButtonBackColor = Color.LightSteelBlue;
      this.newButton_Hor_Runner_TwoStage.ButtonText = "2단 매니폴드";
      this.newButton_Hor_Runner_TwoStage.FlatBorderSize = 1;
      this.newButton_Hor_Runner_TwoStage.FlatStyle = FlatStyle.Flat;
      this.newButton_Hor_Runner_TwoStage.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Hor_Runner_TwoStage.Image = (Image) null;
      this.newButton_Hor_Runner_TwoStage.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Hor_Runner_TwoStage.Location = new Point(0, 0);
      this.newButton_Hor_Runner_TwoStage.Name = "newButton_Hor_Runner_TwoStage";
      this.newButton_Hor_Runner_TwoStage.Size = new Size(115, 23);
      this.newButton_Hor_Runner_TwoStage.TabIndex = 16;
      this.newButton_Hor_Runner_TwoStage.TabStop = false;
      this.newButton_Hor_Runner_TwoStage.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Hor_Runner_TwoStage.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Hor_Runner_TwoStage.NewClick += new EventHandler(this.newButton_Hor_RunnerType_NewClick);
      this.newButton_Hor_Runner_ThreeStage.ButtonBackColor = Color.White;
      this.newButton_Hor_Runner_ThreeStage.ButtonText = "3단 매니폴드";
      this.newButton_Hor_Runner_ThreeStage.FlatBorderSize = 1;
      this.newButton_Hor_Runner_ThreeStage.FlatStyle = FlatStyle.Flat;
      this.newButton_Hor_Runner_ThreeStage.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Hor_Runner_ThreeStage.Image = (Image) null;
      this.newButton_Hor_Runner_ThreeStage.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Hor_Runner_ThreeStage.Location = new Point(113, 0);
      this.newButton_Hor_Runner_ThreeStage.Name = "newButton_Hor_Runner_ThreeStage";
      this.newButton_Hor_Runner_ThreeStage.Size = new Size(115, 23);
      this.newButton_Hor_Runner_ThreeStage.TabIndex = 17;
      this.newButton_Hor_Runner_ThreeStage.TabStop = false;
      this.newButton_Hor_Runner_ThreeStage.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Hor_Runner_ThreeStage.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Hor_Runner_ThreeStage.NewClick += new EventHandler(this.newButton_Hor_RunnerType_NewClick);
      this.tabControl_Hor_RunnerType.Controls.Add((Control) this.tabPage_Hor_TwoStage);
      this.tabControl_Hor_RunnerType.Controls.Add((Control) this.tabPage_Hor_ThreeStage);
      this.tabControl_Hor_RunnerType.Location = new Point(-5, -5);
      this.tabControl_Hor_RunnerType.Name = "tabControl_Hor_RunnerType";
      this.tabControl_Hor_RunnerType.SelectedIndex = 0;
      this.tabControl_Hor_RunnerType.Size = new Size(237, 231);
      this.tabControl_Hor_RunnerType.TabIndex = 20;
      this.tabControl_Hor_RunnerType.SelectedIndexChanged += new EventHandler(this.tabControl_Hor_RunnerType_SelectedIndexChanged);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.newTextBox_Hor_Two_CenterTol);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.panel2);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.label_Hor_Two_Dia);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.label_Hor_Two_Length);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.label_Hor_Two_CenterTol);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.newComboBox_Hor_Two_Direction);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.newTextBox_Hor_Two_Length);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.label_Hor_Two_Direction);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.newTextBox_Hor_Two_Dia);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.newTextBox_Hor_Two_Angle);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.label_Hor_Two_Dim1);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.label_Hor_Two_Angle);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.label_Hor_Two_Dim2);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.newTextBox_Hor_Two_Dim1);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.newTextBox_Hor_Two_Dim2);
      this.tabPage_Hor_TwoStage.Controls.Add((Control) this.checkBox_Hor_Two_Inter);
      this.tabPage_Hor_TwoStage.Location = new Point(4, 24);
      this.tabPage_Hor_TwoStage.Name = "tabPage_Hor_TwoStage";
      this.tabPage_Hor_TwoStage.Padding = new Padding(3);
      this.tabPage_Hor_TwoStage.Size = new Size(229, 203);
      this.tabPage_Hor_TwoStage.TabIndex = 0;
      this.tabPage_Hor_TwoStage.UseVisualStyleBackColor = true;
      this.newTextBox_Hor_Two_CenterTol.BackColor = SystemColors.Window;
      this.newTextBox_Hor_Two_CenterTol.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Hor_Two_CenterTol.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Hor_Two_CenterTol.IsDigit = true;
      this.newTextBox_Hor_Two_CenterTol.Lines = new string[0];
      this.newTextBox_Hor_Two_CenterTol.Location = new Point(153, 114);
      this.newTextBox_Hor_Two_CenterTol.MultiLine = false;
      this.newTextBox_Hor_Two_CenterTol.Name = "newTextBox_Hor_Two_CenterTol";
      this.newTextBox_Hor_Two_CenterTol.ReadOnly = false;
      this.newTextBox_Hor_Two_CenterTol.Size = new Size(76, 23);
      this.newTextBox_Hor_Two_CenterTol.TabIndex = 136;
      this.newTextBox_Hor_Two_CenterTol.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Hor_Two_CenterTol.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Hor_Two_CenterTol.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Hor_Two_CenterTol.Value = "";
      this.label_Hor_Two_CenterTol.BackColor = Color.Lavender;
      this.label_Hor_Two_CenterTol.BorderStyle = BorderStyle.FixedSingle;
      this.label_Hor_Two_CenterTol.Location = new Point(1, 114);
      this.label_Hor_Two_CenterTol.Name = "label_Hor_Two_CenterTol";
      this.label_Hor_Two_CenterTol.Size = new Size(153, 23);
      this.label_Hor_Two_CenterTol.TabIndex = 137;
      this.label_Hor_Two_CenterTol.Text = "Center 공차";
      this.label_Hor_Two_CenterTol.TextAlign = ContentAlignment.MiddleCenter;
      this.checkBox_Hor_Two_Inter.BackColor = SystemColors.Window;
      this.checkBox_Hor_Two_Inter.CheckAlign = ContentAlignment.MiddleCenter;
      this.checkBox_Hor_Two_Inter.Checked = true;
      this.checkBox_Hor_Two_Inter.CheckState = CheckState.Checked;
      this.checkBox_Hor_Two_Inter.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.checkBox_Hor_Two_Inter.Location = new Point(173, 138);
      this.checkBox_Hor_Two_Inter.Name = "checkBox_Hor_Two_Inter";
      this.checkBox_Hor_Two_Inter.Size = new Size(40, 20);
      this.checkBox_Hor_Two_Inter.TabIndex = 138;
      this.checkBox_Hor_Two_Inter.UseVisualStyleBackColor = false;
      this.checkBox_Hor_Two_Inter.Visible = false;
      this.tabPage_Hor_ThreeStage.Controls.Add((Control) this.label_Hor_Three_Length);
      this.tabPage_Hor_ThreeStage.Controls.Add((Control) this.newTextBox_Hor_Three_Length);
      this.tabPage_Hor_ThreeStage.Controls.Add((Control) this.panel_Quadrant);
      this.tabPage_Hor_ThreeStage.Controls.Add((Control) this.newTextBox_Hor_Three_CenterTol);
      this.tabPage_Hor_ThreeStage.Controls.Add((Control) this.panel_Hor_Runner_ThreeType);
      this.tabPage_Hor_ThreeStage.Controls.Add((Control) this.label_Hor_Three_Dia);
      this.tabPage_Hor_ThreeStage.Controls.Add((Control) this.label_Hor_Three_CenterTol);
      this.tabPage_Hor_ThreeStage.Controls.Add((Control) this.newTextBox_Hor_Three_Dia);
      this.tabPage_Hor_ThreeStage.Location = new Point(4, 24);
      this.tabPage_Hor_ThreeStage.Name = "tabPage_Hor_ThreeStage";
      this.tabPage_Hor_ThreeStage.Padding = new Padding(3);
      this.tabPage_Hor_ThreeStage.Size = new Size(229, 203);
      this.tabPage_Hor_ThreeStage.TabIndex = 1;
      this.tabPage_Hor_ThreeStage.UseVisualStyleBackColor = true;
      this.label_Hor_Three_Length.BackColor = Color.Lavender;
      this.label_Hor_Three_Length.BorderStyle = BorderStyle.FixedSingle;
      this.label_Hor_Three_Length.Location = new Point(1, 47);
      this.label_Hor_Three_Length.Name = "label_Hor_Three_Length";
      this.label_Hor_Three_Length.Size = new Size(153, 23);
      this.label_Hor_Three_Length.TabIndex = 144;
      this.label_Hor_Three_Length.Text = "3단 런너 길이";
      this.label_Hor_Three_Length.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Hor_Three_Length.BackColor = SystemColors.Window;
      this.newTextBox_Hor_Three_Length.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Hor_Three_Length.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Hor_Three_Length.IsDigit = true;
      this.newTextBox_Hor_Three_Length.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Hor_Three_Length.Location = new Point(153, 47);
      this.newTextBox_Hor_Three_Length.MultiLine = false;
      this.newTextBox_Hor_Three_Length.Name = "newTextBox_Hor_Three_Length";
      this.newTextBox_Hor_Three_Length.ReadOnly = false;
      this.newTextBox_Hor_Three_Length.Size = new Size(76, 23);
      this.newTextBox_Hor_Three_Length.TabIndex = 145;
      this.newTextBox_Hor_Three_Length.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Hor_Three_Length.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Hor_Three_Length.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Hor_Three_Length.Value = "0";
      this.panel_Quadrant.BackColor = Color.White;
      this.panel_Quadrant.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Quadrant.Controls.Add((Control) this.radioButton_Quadrant4);
      this.panel_Quadrant.Controls.Add((Control) this.radioButton_Quadrant2);
      this.panel_Quadrant.Location = new Point(1, 3);
      this.panel_Quadrant.Name = "panel_Quadrant";
      this.panel_Quadrant.Size = new Size(228, 23);
      this.panel_Quadrant.TabIndex = 139;
      this.radioButton_Quadrant4.BackColor = Color.White;
      this.radioButton_Quadrant4.Location = new Point(123, 2);
      this.radioButton_Quadrant4.Name = "radioButton_Quadrant4";
      this.radioButton_Quadrant4.Size = new Size(85, 19);
      this.radioButton_Quadrant4.TabIndex = 57;
      this.radioButton_Quadrant4.Text = "4분면";
      this.radioButton_Quadrant4.UseVisualStyleBackColor = false;
      this.radioButton_Quadrant4.CheckedChanged += new EventHandler(this.radioButton_Quadrant4_CheckedChanged);
      this.radioButton_Quadrant2.BackColor = Color.White;
      this.radioButton_Quadrant2.Location = new Point(38, 2);
      this.radioButton_Quadrant2.Name = "radioButton_Quadrant2";
      this.radioButton_Quadrant2.Size = new Size(68, 19);
      this.radioButton_Quadrant2.TabIndex = 56;
      this.radioButton_Quadrant2.Text = "2분면";
      this.radioButton_Quadrant2.UseVisualStyleBackColor = false;
      this.radioButton_Quadrant2.CheckedChanged += new EventHandler(this.radioButton_Quadrant2_CheckedChanged);
      this.newTextBox_Hor_Three_CenterTol.BackColor = SystemColors.Window;
      this.newTextBox_Hor_Three_CenterTol.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Hor_Three_CenterTol.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Hor_Three_CenterTol.IsDigit = true;
      this.newTextBox_Hor_Three_CenterTol.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Hor_Three_CenterTol.Location = new Point(153, 91);
      this.newTextBox_Hor_Three_CenterTol.MultiLine = false;
      this.newTextBox_Hor_Three_CenterTol.Name = "newTextBox_Hor_Three_CenterTol";
      this.newTextBox_Hor_Three_CenterTol.ReadOnly = false;
      this.newTextBox_Hor_Three_CenterTol.Size = new Size(76, 23);
      this.newTextBox_Hor_Three_CenterTol.TabIndex = 142;
      this.newTextBox_Hor_Three_CenterTol.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Hor_Three_CenterTol.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Hor_Three_CenterTol.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Hor_Three_CenterTol.Value = "0";
      this.panel_Hor_Runner_ThreeType.BackColor = Color.White;
      this.panel_Hor_Runner_ThreeType.BorderStyle = BorderStyle.FixedSingle;
      this.panel_Hor_Runner_ThreeType.Controls.Add((Control) this.radioButton_Hor_Three_Type2);
      this.panel_Hor_Runner_ThreeType.Controls.Add((Control) this.radioButton_Hor_Three_Type1);
      this.panel_Hor_Runner_ThreeType.Location = new Point(1, 25);
      this.panel_Hor_Runner_ThreeType.Name = "panel_Hor_Runner_ThreeType";
      this.panel_Hor_Runner_ThreeType.Size = new Size(228, 23);
      this.panel_Hor_Runner_ThreeType.TabIndex = 138;
      this.radioButton_Hor_Three_Type2.BackColor = Color.White;
      this.radioButton_Hor_Three_Type2.Location = new Point(123, 1);
      this.radioButton_Hor_Three_Type2.Name = "radioButton_Hor_Three_Type2";
      this.radioButton_Hor_Three_Type2.Size = new Size(85, 19);
      this.radioButton_Hor_Three_Type2.TabIndex = 57;
      this.radioButton_Hor_Three_Type2.Text = "☆ - + 타입";
      this.radioButton_Hor_Three_Type2.UseVisualStyleBackColor = false;
      this.radioButton_Hor_Three_Type2.CheckedChanged += new EventHandler(this.radioButton_Hor_Three_Type2_CheckedChanged);
      this.radioButton_Hor_Three_Type1.BackColor = Color.White;
      this.radioButton_Hor_Three_Type1.Location = new Point(38, 1);
      this.radioButton_Hor_Three_Type1.Name = "radioButton_Hor_Three_Type1";
      this.radioButton_Hor_Three_Type1.Size = new Size(68, 19);
      this.radioButton_Hor_Three_Type1.TabIndex = 56;
      this.radioButton_Hor_Three_Type1.Text = "H 타입";
      this.radioButton_Hor_Three_Type1.UseVisualStyleBackColor = false;
      this.radioButton_Hor_Three_Type1.CheckedChanged += new EventHandler(this.radioButton_Hor_Three_Type1_CheckedChanged);
      this.label_Hor_Three_Dia.BackColor = Color.Lavender;
      this.label_Hor_Three_Dia.BorderStyle = BorderStyle.FixedSingle;
      this.label_Hor_Three_Dia.Location = new Point(1, 69);
      this.label_Hor_Three_Dia.Name = "label_Hor_Three_Dia";
      this.label_Hor_Three_Dia.Size = new Size(153, 23);
      this.label_Hor_Three_Dia.TabIndex = 140;
      this.label_Hor_Three_Dia.Text = "수평 런너 직경";
      this.label_Hor_Three_Dia.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Hor_Three_CenterTol.BackColor = Color.Lavender;
      this.label_Hor_Three_CenterTol.BorderStyle = BorderStyle.FixedSingle;
      this.label_Hor_Three_CenterTol.Location = new Point(1, 91);
      this.label_Hor_Three_CenterTol.Name = "label_Hor_Three_CenterTol";
      this.label_Hor_Three_CenterTol.Size = new Size(153, 23);
      this.label_Hor_Three_CenterTol.TabIndex = 143;
      this.label_Hor_Three_CenterTol.Text = "Center 공차";
      this.label_Hor_Three_CenterTol.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Hor_Three_Dia.BackColor = SystemColors.Window;
      this.newTextBox_Hor_Three_Dia.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Hor_Three_Dia.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Hor_Three_Dia.IsDigit = true;
      this.newTextBox_Hor_Three_Dia.Lines = new string[1]
      {
        "0"
      };
      this.newTextBox_Hor_Three_Dia.Location = new Point(153, 69);
      this.newTextBox_Hor_Three_Dia.MultiLine = false;
      this.newTextBox_Hor_Three_Dia.Name = "newTextBox_Hor_Three_Dia";
      this.newTextBox_Hor_Three_Dia.ReadOnly = false;
      this.newTextBox_Hor_Three_Dia.Size = new Size(76, 23);
      this.newTextBox_Hor_Three_Dia.TabIndex = 141;
      this.newTextBox_Hor_Three_Dia.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Hor_Three_Dia.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Hor_Three_Dia.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Hor_Three_Dia.Value = "0";
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(774, 515);
      this.Controls.Add((Control) this.panel_Hor_RunnerType);
      this.Controls.Add((Control) this.panel_Side_Group);
      this.Controls.Add((Control) this.panel_Pin_Group);
      this.Controls.Add((Control) this.newButton_Export);
      this.Controls.Add((Control) this.newButton_Import);
      this.Controls.Add((Control) this.newButton_Del);
      this.Controls.Add((Control) this.newButton_Edit);
      this.Controls.Add((Control) this.newButton_Add);
      this.Controls.Add((Control) this.newTextBox_Sprue_Length);
      this.Controls.Add((Control) this.newTextBox_Sprue_Dim1);
      this.Controls.Add((Control) this.newTextBox_Sprue_Dim2);
      this.Controls.Add((Control) this.newTextBox_Cavity_Occ);
      this.Controls.Add((Control) this.newTextBox_Option);
      this.Controls.Add((Control) this.newTextBox_Item);
      this.Controls.Add((Control) this.newTextBox_Company);
      this.Controls.Add((Control) this.newComboBox_Item);
      this.Controls.Add((Control) this.newComboBox_Company);
      this.Controls.Add((Control) this.label_Sprue_Dim2);
      this.Controls.Add((Control) this.label_Sprue_Length);
      this.Controls.Add((Control) this.label_Cavity_Occ);
      this.Controls.Add((Control) this.label_DB_Option_W);
      this.Controls.Add((Control) this.label_DB_Item_W);
      this.Controls.Add((Control) this.label_DB_Item);
      this.Controls.Add((Control) this.label_DB_Company_W);
      this.Controls.Add((Control) this.label_DB_Company);
      this.Controls.Add((Control) this.label_Sprue_Dim1);
      this.Controls.Add((Control) this.panel5);
      this.Controls.Add((Control) this.panel1);
      this.Controls.Add((Control) this.listBox_DB);
      this.Controls.Add((Control) this.label_Side);
      this.Controls.Add((Control) this.label_Sprue);
      this.Controls.Add((Control) this.label_Cavity);
      this.Controls.Add((Control) this.label_Hor_Runner);
      this.Controls.Add((Control) this.label_Pin);
      this.Controls.Add((Control) this.label_DB_List);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmRunnerDB);
      this.ShowInTaskbar = false;
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "런너 DB";
      this.Load += new EventHandler(this.frmRunnerDB_Load);
      this.KeyDown += new KeyEventHandler(this.frmRunnerDB_KeyDown);
      this.panel1.ResumeLayout(false);
      this.panel2.ResumeLayout(false);
      this.panel5.ResumeLayout(false);
      this.panel_Pin_Group.ResumeLayout(false);
      this.tabControl_Pin_Group.ResumeLayout(false);
      this.tabPage_Pin_Group1.ResumeLayout(false);
      this.tabPage_Pin_Group2.ResumeLayout(false);
      this.tabPage_Pin_Group3.ResumeLayout(false);
      this.tabPage_Pin_Group4.ResumeLayout(false);
      this.tabControl_Side_Group.ResumeLayout(false);
      this.tabPage_Side_Group1.ResumeLayout(false);
      this.panel9.ResumeLayout(false);
      this.panel9.PerformLayout();
      this.panel_Side_GateType2_G1.ResumeLayout(false);
      this.panel_Side_GateType2_G1.PerformLayout();
      this.panel11.ResumeLayout(false);
      this.panel11.PerformLayout();
      this.tabPage_Side_Group2.ResumeLayout(false);
      this.panel12.ResumeLayout(false);
      this.panel12.PerformLayout();
      this.panel_Side_GateType2_G2.ResumeLayout(false);
      this.panel_Side_GateType2_G2.PerformLayout();
      this.panel14.ResumeLayout(false);
      this.panel14.PerformLayout();
      this.tabPage_Side_Group3.ResumeLayout(false);
      this.panel6.ResumeLayout(false);
      this.panel6.PerformLayout();
      this.panel_Side_GateType2_G3.ResumeLayout(false);
      this.panel_Side_GateType2_G3.PerformLayout();
      this.panel8.ResumeLayout(false);
      this.panel8.PerformLayout();
      this.tabPage_Side_Group4.ResumeLayout(false);
      this.panel18.ResumeLayout(false);
      this.panel18.PerformLayout();
      this.panel_Side_GateType2_G4.ResumeLayout(false);
      this.panel_Side_GateType2_G4.PerformLayout();
      this.panel20.ResumeLayout(false);
      this.panel20.PerformLayout();
      this.panel_Side_Group.ResumeLayout(false);
      this.panel_Hor_RunnerType.ResumeLayout(false);
      this.tabControl_Hor_RunnerType.ResumeLayout(false);
      this.tabPage_Hor_TwoStage.ResumeLayout(false);
      this.tabPage_Hor_ThreeStage.ResumeLayout(false);
      this.panel_Quadrant.ResumeLayout(false);
      this.panel_Hor_Runner_ThreeType.ResumeLayout(false);
      this.ResumeLayout(false);
    }
  }
}
