﻿// Decompiled with JetBrains decompiler
// Type: HDMFSummary.clsSummaryDefine
// Assembly: HDMFSummary, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: ********-3B9F-45E9-9723-0DEF72C057B2
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFSummary.dll

using System.Collections.Generic;
using System.IO;

namespace HDMFSummary
{
  public class clsSummaryDefine
  {
    public static DirectoryInfo g_diTemplate;
    public static DirectoryInfo g_diTmpReport;
    public static FileInfo g_fiProject;
    public static Dictionary<string, string> g_dicExtension;

    public static string g_strSaveFileName { get; set; }
  }
}
