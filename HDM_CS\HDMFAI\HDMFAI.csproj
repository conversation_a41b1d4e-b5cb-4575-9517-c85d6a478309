﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!--Project was exported from assembly: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFAI.dll-->
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{596E8D3F-84A8-47FD-89E7-F8D99DDF9905}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AssemblyName>HDMFAI</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <ApplicationVersion>1.0.0.0</ApplicationVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ArSetting.Net4">
      <HintPath>lib\ArSetting.Net4.dll</HintPath>
    </Reference>
    <Reference Include="HDLog4Net">
      <HintPath>lib\HDLog4Net.dll</HintPath>
    </Reference>
    <Reference Include="HDMoldFlowLibrary">
      <HintPath>lib\HDMoldFlowLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Newtonsoft.Json">
      <HintPath>lib\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Web" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="clsAISolution.cs" />
    <Compile Include="clsBigData.cs" />
    <Compile Include="clsClasses.cs" />
    <Compile Include="1clsClasses.cs" />
    <Compile Include="2clsClasses.cs" />
    <Compile Include="clsAIDefine.cs" />
    <Compile Include="clsExcel.cs" />
    <Compile Include="clsInputData.cs" />
    <Compile Include="clsUtill.cs" />
    <Compile Include="clsWeb.cs" />
    <Compile Include="Properties\Resources.Designer.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\AppEvents.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\AppEvents_Event.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\Application.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\DocEvents.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\DocEvents_Event.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\Name.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\Names.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\Range.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\Sheets.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\Workbook.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\WorkbookEvents.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\WorkbookEvents_Event.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\Workbooks.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\Worksheet.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\XlFileFormat.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\XlSaveAsAccessMode.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\_Application.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\_Workbook.cs" />
    <Compile Include="Microsoft\Office\Interop\Excel\_Worksheet.cs" />
    <Compile Include="AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="HDMFAI\Properties\Resources.resx" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>