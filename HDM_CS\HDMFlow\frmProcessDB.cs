﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.frmProcessDB
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLocale;
using HDLog4Net;
using HDMFUserControl;
using HDMoldFlow.Properties;
using HDMoldFlowLibrary;
using Microsoft.WindowsAPICodePack.Dialogs;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using System.Xml;

namespace HDMoldFlow
{
  public class frmProcessDB : Form
  {
    private const int WS_EX_COMPOSITED = 33554432;
    private IContainer components = (IContainer) null;
    private NewComboBox newComboBox_Item;
    private NewComboBox newComboBox_Company;
    private Label label_DB_Opt_W;
    private Label label_DB_Item_W;
    private Label label_DB_Item;
    private Label label_DB_Company_W;
    private Label label_DB_Company;
    private Label label_Mat_TradeName;
    private Label label_Mat_Manufacturer;
    private ListBox listBox_DB;
    private Label label_Suji;
    private Label label_DB_List;
    private NewTextBox newTextBox_Company;
    private NewTextBox newTextBox_Item;
    private NewTextBox newTextBox_Option;
    private NewTextBox newTextBox_Mat_UDB;
    private NewButton newButton_Mat_UDB;
    private Label label_Mat_FamilyAbbreviation;
    private Label label_Mat_MaterialID;
    private NewTextBox newTextBox_Mat_FamilyAbbreviation;
    private Label label4;
    private NewComboBox newComboBox_FC;
    private Label label_Mat_Sequence;
    private Label label_Mat_CTime;
    private Label label9;
    private Label label10;
    private NewComboBox newComboBox_Sequence;
    private UnitTextBox unitTextBox_CTime;
    private UnitTextBox unitTextBox_MetlTemp;
    private UnitTextBox unitTextBox_MoldTemp;
    private Label label_FC1;
    private Label label_FC2;
    private DataGridView dataGridView_FC1;
    private Label label_FC3;
    private DataGridView dataGridView_FC3;
    private Label label_FC4;
    private Label label15;
    private NewComboBox newComboBox_VP;
    private Label label16;
    private NewComboBox newComboBox_PHC;
    private Label label_PHC1;
    private Label label_PHC2;
    private Panel panel1;
    private DataGridViewTextBoxColumn Column1;
    private DataGridViewTextBoxColumn Column2;
    private Panel panel2;
    private DataGridView dataGridView_FC2;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn1;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn2;
    private Panel panel3;
    private DataGridViewTextBoxColumn Column5;
    private DataGridViewTextBoxColumn Column7;
    private Panel panel4;
    private DataGridView dataGridView_FC4;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn3;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn4;
    private Panel panel5;
    private DataGridView dataGridView_VP;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn5;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn6;
    private Panel panel6;
    private DataGridView dataGridView_PHC1;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn7;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn8;
    private Panel panel7;
    private DataGridView dataGridView_PHC2;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn9;
    private DataGridViewTextBoxColumn dataGridViewTextBoxColumn10;
    private NewButton newButton_Add;
    private NewButton newButton_Edit;
    private NewButton newButton_Del;
    private NewButton newButton_Import;
    private NewButton newButton_Export;
    private Panel panel8;
    private RadioButton radioButton_Mat_User;
    private RadioButton radioButton_Mat_System;
    private Label label_Mat_Material;
    private Label label_Suji_SearchCond;
    private Label label12;
    private NewTextBox newTextBox_Mat_Search;
    private NewComboBox newComboBox_Mat_Manufacturer;
    private NewComboBox newComboBox_Mat_TradeName;
    private NewTextBox newTextBox_Mat_MaterialID;

    protected override CreateParams CreateParams
    {
      get
      {
        CreateParams createParams = base.CreateParams;
        createParams.ExStyle |= 33554432;
        return createParams;
      }
    }

    public frmProcessDB()
    {
      this.InitializeComponent();
      this.newButton_Add.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Add.Image);
      this.newButton_Edit.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Edit.Image);
      this.newButton_Del.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Del.Image);
      this.newButton_Import.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Import.Image);
      this.newButton_Export.Image = clsUtill.ChangeSyncBackgroundImage(this.newButton_Export.Image);
      this.Text = LocaleControl.getInstance().GetString("IDS_SET_PROCESS") + " DB";
      this.label_DB_List.Text = "DB " + LocaleControl.getInstance().GetString("IDS_LIST");
      this.label_DB_Company.Text = LocaleControl.getInstance().GetString("IDS_COMPANY_NAME");
      this.label_DB_Item.Text = LocaleControl.getInstance().GetString("IDS_ITEM_NAME");
      this.label_DB_Company_W.Text = LocaleControl.getInstance().GetString("IDS_COMPANY_NAME");
      this.label_DB_Item_W.Text = LocaleControl.getInstance().GetString("IDS_ITEM_NAME");
      this.label_DB_Opt_W.Text = LocaleControl.getInstance().GetString("IDS_OPTION");
      this.label_Suji.Text = LocaleControl.getInstance().GetString("IDS_SUJI_DATA");
      this.label_Suji_SearchCond.Text = LocaleControl.getInstance().GetString("IDS_SEARCH_CONDITION");
      this.label_Mat_Material.Text = LocaleControl.getInstance().GetString("IDS_MATERIAL_INFO");
      this.radioButton_Mat_System.Text = LocaleControl.getInstance().GetString("IDS_SYSTEM");
      this.radioButton_Mat_User.Text = LocaleControl.getInstance().GetString("IDS_USER");
      this.newButton_Mat_UDB.ButtonText = LocaleControl.getInstance().GetString("IDS_IMPORT_USER_UDB");
      this.newButton_Add.ButtonText = LocaleControl.getInstance().GetString("IDS_ADD");
      this.newButton_Edit.ButtonText = LocaleControl.getInstance().GetString("IDS_EDIT");
      this.newButton_Del.ButtonText = LocaleControl.getInstance().GetString("IDS_DELETE");
      this.newButton_Import.ButtonText = LocaleControl.getInstance().GetString("IDS_IMPORT");
      this.newButton_Export.ButtonText = LocaleControl.getInstance().GetString("IDS_EXPORT");
    }

    private void frmProcessDB_Load(object sender, EventArgs e)
    {
      this.ActiveControl = (Control) this.label_DB_List;
      string str1 = LocaleControl.getInstance().GetString("IDS_COOL");
      string str2 = LocaleControl.getInstance().GetString("IDS_FLOW");
      string str3 = LocaleControl.getInstance().GetString("IDS_WARP");
      this.newComboBox_Sequence.Items.Add((object) str1);
      this.newComboBox_Sequence.Items.Add((object) str2);
      this.newComboBox_Sequence.Items.Add((object) (str2 + "+" + str3));
      this.newComboBox_Sequence.Items.Add((object) (str1 + "+" + str2 + "+" + str3));
      this.newComboBox_Sequence.Items.Add((object) (str1 + "+" + str2));
      this.newComboBox_Sequence.SelectedIndex = 0;
      this.newComboBox_FC.Items.Add((object) "Automatic");
      this.newComboBox_FC.Items.Add((object) "Injection time");
      this.newComboBox_FC.Items.Add((object) "Flow rate");
      this.newComboBox_FC.Items.Add((object) "Ram speed vs ram position");
      this.newComboBox_FC.Items.Add((object) "%Flow rate vs %shot volume");
      this.newComboBox_FC.SelectedIndex = 0;
      this.newComboBox_VP.Items.Add((object) "Automatic");
      this.newComboBox_VP.Items.Add((object) "By %volume filled");
      this.newComboBox_VP.Items.Add((object) "By ram position");
      this.newComboBox_VP.SelectedIndex = 0;
      this.newComboBox_PHC.Items.Add((object) "Automatic");
      this.newComboBox_PHC.Items.Add((object) "%Filling pressure vs time");
      this.newComboBox_PHC.Items.Add((object) "Packing pressure vs time");
      this.newComboBox_PHC.SelectedIndex = 0;
      this.RefreshDBList();
      this.RefreshCompanyUI();
      this.RefreshItemUI();
      this.radioButton_Mat_System.Checked = true;
    }

    public void RefreshDBList(string p_strCompany = null, string p_strItem = null)
    {
      this.listBox_DB.Items.Clear();
      if (p_strCompany == null)
        p_strCompany = LocaleControl.getInstance().GetString("IDS_ALL");
      if (p_strItem == null)
        p_strItem = LocaleControl.getInstance().GetString("IDS_ALL");
      try
      {
        DataRow[] array = clsDefine.g_dtProcessDB.AsEnumerable().ToArray<DataRow>();
        if (p_strCompany != LocaleControl.getInstance().GetString("IDS_ALL"))
          array = ((IEnumerable<DataRow>) array).Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Company"].ToString() == p_strCompany)).ToArray<DataRow>();
        if (array.Length != 0)
        {
          if (p_strItem != LocaleControl.getInstance().GetString("IDS_ALL"))
            array = ((IEnumerable<DataRow>) array).Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Item"].ToString() == p_strItem)).ToArray<DataRow>();
          if (array.Length != 0)
          {
            List<string> stringList = new List<string>();
            foreach (DataRow dataRow in array)
              stringList.Add(dataRow["Name"].ToString());
            stringList.Sort();
            this.listBox_DB.Items.AddRange((object[]) stringList.ToArray());
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcessDB]RefreshDBList):" + ex.Message));
      }
      this.newTextBox_Company.Text = "";
      this.newTextBox_Item.Text = "";
      this.newTextBox_Option.Text = "";
    }

    public void RefreshCompanyUI(string p_strCompany = null)
    {
      this.newComboBox_Company.Items.Clear();
      if (p_strCompany == null)
        p_strCompany = LocaleControl.getInstance().GetString("IDS_ALL");
      try
      {
        this.newComboBox_Company.Items.Add((object) LocaleControl.getInstance().GetString("IDS_ALL"));
        DataRow[] array = clsDefine.g_dtProcessDB.AsEnumerable().ToArray<DataRow>();
        List<string> stringList = new List<string>();
        foreach (DataRow dataRow in array)
        {
          if (!stringList.Contains(dataRow["Company"].ToString()))
            stringList.Add(dataRow["Company"].ToString());
        }
        stringList.Sort();
        this.newComboBox_Company.Items.AddRange((object[]) stringList.ToArray());
        this.newComboBox_Company.SelectedIndex = this.newComboBox_Company.Items.IndexOf((object) p_strCompany);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcessDB]RefreshCompanyUI):" + ex.Message));
      }
    }

    public void RefreshItemUI(string p_strCompany = null)
    {
      this.newComboBox_Item.Items.Clear();
      if (p_strCompany == null)
        p_strCompany = LocaleControl.getInstance().GetString("IDS_ALL");
      try
      {
        this.newComboBox_Item.Items.Add((object) LocaleControl.getInstance().GetString("IDS_ALL"));
        DataRow[] array = clsDefine.g_dtProcessDB.AsEnumerable().ToArray<DataRow>();
        if (p_strCompany != LocaleControl.getInstance().GetString("IDS_ALL"))
          array = ((IEnumerable<DataRow>) array).Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Company"].ToString() == p_strCompany)).ToArray<DataRow>();
        if (array.Length != 0)
        {
          List<string> stringList = new List<string>();
          foreach (DataRow dataRow in array)
          {
            if (!stringList.Contains(dataRow["Item"].ToString()))
              stringList.Add(dataRow["Item"].ToString());
          }
          stringList.Sort();
          this.newComboBox_Item.Items.AddRange((object[]) stringList.ToArray());
          this.newComboBox_Item.SelectedIndex = 0;
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcessDB]RefreshItemUI):" + ex.Message));
      }
      if (this.newComboBox_Item.Items.Count == 1)
      {
        this.newComboBox_Item.Items.Clear();
        this.newComboBox_Item.Enabled = false;
      }
      else
        this.newComboBox_Item.Enabled = true;
    }

    private void RefreshMaterial()
    {
      this.newComboBox_Mat_Manufacturer.Items.Clear();
      this.newComboBox_Mat_Manufacturer.Enabled = false;
      this.newComboBox_Mat_TradeName.Items.Clear();
      this.newComboBox_Mat_TradeName.Enabled = false;
      this.newTextBox_Mat_FamilyAbbreviation.Value = "";
      this.newTextBox_Mat_MaterialID.Value = "";
      string strQuery = this.newTextBox_Mat_Search.Value;
      try
      {
        DataTable source = !this.radioButton_Mat_System.Checked ? clsDefine.g_dsMaterial.Tables["User"] : clsDefine.g_dsMaterial.Tables["System"];
        if (source == null)
          return;
        DataRow[] dataRowArray = strQuery != null && !(strQuery == "") ? source.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["TradeName"].ToString().ToUpper().Contains(strQuery.ToUpper()))).ToArray<DataRow>() : source.AsEnumerable().ToArray<DataRow>();
        if (dataRowArray.Length == 0)
          return;
        List<string> stringList = new List<string>();
        foreach (DataRow dataRow in dataRowArray)
        {
          if (!stringList.Contains(dataRow["Manufacturer"].ToString()))
            stringList.Add(dataRow["Manufacturer"].ToString());
        }
        stringList.Sort();
        this.newComboBox_Mat_Manufacturer.Items.AddRange((object[]) stringList.ToArray());
        this.newComboBox_Mat_Manufacturer.Enabled = true;
        this.newComboBox_Mat_Manufacturer.SelectedIndex = 0;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcessDB]RefreshMaterial):" + ex.Message));
      }
    }

    private void newComboBox_Sequence_SelectedIndexChanged(object sender, EventArgs e)
    {
      switch (this.newComboBox_Sequence.SelectedIndex)
      {
        case 0:
          this.label_Mat_Sequence.Text = "Cool";
          this.label_Mat_CTime.Text = "Inj+Pack+Cooling time:Specified";
          break;
        case 1:
          this.label_Mat_Sequence.Text = "Flow";
          this.label_Mat_CTime.Text = "Cooling Time:Specified";
          break;
        case 2:
          this.label_Mat_Sequence.Text = "Flow|Warp";
          this.label_Mat_CTime.Text = "Cooling Time:Specified";
          break;
        case 3:
          this.label_Mat_Sequence.Text = "Cool|Flow|Warp";
          this.label_Mat_CTime.Text = "Inj+Pack+Cooling time:Specified";
          break;
        case 4:
          this.label_Mat_Sequence.Text = "Cool|Flow";
          this.label_Mat_CTime.Text = "Inj+Pack+Cooling time:Specified";
          break;
      }
    }

    private void newComboBox_FC_SelectedIndexChanged(object sender, EventArgs e)
    {
      this.label_FC1.Text = "";
      this.label_FC1.BackColor = Color.WhiteSmoke;
      this.dataGridView_FC1.AllowUserToAddRows = false;
      this.dataGridView_FC1.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_FC1.RowHeadersVisible = false;
      this.dataGridView_FC1.RowPostPaint -= new DataGridViewRowPostPaintEventHandler(this.dataGridView_FC_RowPostPaint);
      this.dataGridView_FC1.Rows.Clear();
      this.label_FC2.Text = "";
      this.label_FC2.BackColor = Color.WhiteSmoke;
      this.dataGridView_FC2.AllowUserToAddRows = false;
      this.dataGridView_FC2.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_FC2.RowHeadersVisible = false;
      this.dataGridView_FC2.RowPostPaint -= new DataGridViewRowPostPaintEventHandler(this.dataGridView_FC_RowPostPaint);
      this.dataGridView_FC2.Rows.Clear();
      this.label_FC3.Text = "";
      this.label_FC3.BackColor = Color.WhiteSmoke;
      this.dataGridView_FC3.AllowUserToAddRows = false;
      this.dataGridView_FC3.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_FC3.Rows.Clear();
      this.label_FC4.Text = "";
      this.label_FC4.BackColor = Color.WhiteSmoke;
      this.dataGridView_FC4.AllowUserToAddRows = false;
      this.dataGridView_FC4.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_FC4.Rows.Clear();
      switch (this.newComboBox_FC.SelectedIndex)
      {
        case 1:
          this.label_FC1.BackColor = Color.Lavender;
          this.label_FC1.Text = LocaleControl.getInstance().GetString("IDS_TIME") + "(S)";
          this.dataGridView_FC1.BackgroundColor = Color.White;
          this.dataGridView_FC1.Rows.Add();
          this.dataGridView_FC1.ClearSelection();
          break;
        case 2:
          this.label_FC1.BackColor = Color.Lavender;
          this.label_FC1.Text = LocaleControl.getInstance().GetString("IDS_FLOW_RATE") + "(cm3/s)";
          this.dataGridView_FC1.BackgroundColor = Color.White;
          this.dataGridView_FC1.Rows.Add();
          this.dataGridView_FC1.ClearSelection();
          break;
        case 3:
          this.label_FC1.BackColor = Color.Lavender;
          this.label_FC1.Text = "Ram Position(mm)";
          this.dataGridView_FC1.AllowUserToAddRows = true;
          this.dataGridView_FC1.BackgroundColor = Color.White;
          this.dataGridView_FC1.RowHeadersVisible = true;
          this.dataGridView_FC1.RowPostPaint += new DataGridViewRowPostPaintEventHandler(this.dataGridView_FC_RowPostPaint);
          this.dataGridView_FC1.ClearSelection();
          this.label_FC2.BackColor = Color.Lavender;
          this.label_FC2.Text = "Ram Speed(mm/s)";
          this.dataGridView_FC2.AllowUserToAddRows = true;
          this.dataGridView_FC2.BackgroundColor = Color.White;
          this.dataGridView_FC2.RowHeadersVisible = true;
          this.dataGridView_FC2.RowPostPaint += new DataGridViewRowPostPaintEventHandler(this.dataGridView_FC_RowPostPaint);
          this.dataGridView_FC2.ClearSelection();
          this.label_FC3.BackColor = Color.Lavender;
          this.label_FC3.Text = "Cushion warning limit";
          this.dataGridView_FC3.BackgroundColor = Color.White;
          this.dataGridView_FC3.Rows.Add();
          this.dataGridView_FC3.ClearSelection();
          this.label_FC4.BackColor = Color.Lavender;
          this.label_FC4.Text = "Starting ram position";
          this.dataGridView_FC4.BackgroundColor = Color.White;
          this.dataGridView_FC4.Rows.Add();
          this.dataGridView_FC4.ClearSelection();
          break;
        case 4:
          this.label_FC1.BackColor = Color.Lavender;
          this.label_FC1.Text = "%Shot Volume(%)";
          this.dataGridView_FC1.AllowUserToAddRows = true;
          this.dataGridView_FC1.BackgroundColor = Color.White;
          this.dataGridView_FC1.RowHeadersVisible = true;
          this.dataGridView_FC1.RowPostPaint += new DataGridViewRowPostPaintEventHandler(this.dataGridView_FC_RowPostPaint);
          this.dataGridView_FC1.ClearSelection();
          this.label_FC2.BackColor = Color.Lavender;
          this.label_FC2.Text = "%Flow rate(%)";
          this.dataGridView_FC2.AllowUserToAddRows = true;
          this.dataGridView_FC2.BackgroundColor = Color.White;
          this.dataGridView_FC2.RowHeadersVisible = true;
          this.dataGridView_FC2.RowPostPaint += new DataGridViewRowPostPaintEventHandler(this.dataGridView_FC_RowPostPaint);
          this.dataGridView_FC2.ClearSelection();
          this.label_FC3.BackColor = Color.Lavender;
          this.label_FC3.Text = "Normal flow rate";
          this.dataGridView_FC3.BackgroundColor = Color.White;
          this.dataGridView_FC3.Rows.Add();
          this.dataGridView_FC3.ClearSelection();
          break;
      }
    }

    private void newComboBox_VP_SelectedIndexChanged(object sender, EventArgs e)
    {
      this.dataGridView_VP.AllowUserToAddRows = false;
      this.dataGridView_VP.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_VP.Rows.Clear();
      switch (this.newComboBox_VP.SelectedIndex)
      {
        case 1:
          this.dataGridView_VP.BackgroundColor = Color.White;
          this.dataGridView_VP.Rows.Add();
          this.dataGridView_VP.ClearSelection();
          break;
        case 2:
          this.dataGridView_VP.BackgroundColor = Color.White;
          this.dataGridView_VP.Rows.Add();
          this.dataGridView_VP.ClearSelection();
          break;
      }
    }

    private void newComboBox_PHC_SelectedIndexChanged(object sender, EventArgs e)
    {
      this.label_PHC1.BackColor = Color.WhiteSmoke;
      this.label_PHC1.Text = "";
      this.dataGridView_PHC1.AllowUserToAddRows = false;
      this.dataGridView_PHC1.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_PHC1.Rows.Clear();
      this.label_PHC2.BackColor = Color.WhiteSmoke;
      this.label_PHC2.Text = "";
      this.dataGridView_PHC2.AllowUserToAddRows = false;
      this.dataGridView_PHC2.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_PHC2.Rows.Clear();
      if (this.newComboBox_PHC.SelectedIndex <= 0)
        return;
      this.label_PHC1.BackColor = Color.Lavender;
      this.label_PHC1.Text = "Duration";
      this.dataGridView_PHC1.AllowUserToAddRows = true;
      this.dataGridView_PHC1.BackgroundColor = Color.White;
      this.label_PHC2.BackColor = Color.Lavender;
      this.label_PHC2.Text = "Pressure";
      this.dataGridView_PHC2.AllowUserToAddRows = true;
      this.dataGridView_PHC2.BackgroundColor = Color.White;
    }

    private void dataGridView_PHC1_UserAddedRow(object sender, DataGridViewRowEventArgs e)
    {
      if (this.dataGridView_PHC1.Rows.Count >= 4)
        this.dataGridView_PHC1.AllowUserToAddRows = false;
      else
        this.dataGridView_PHC1.AllowUserToAddRows = true;
    }

    private void dataGridView_PHC1_RowsAdded(object sender, DataGridViewRowsAddedEventArgs e)
    {
      for (int index = 0; index < this.dataGridView_PHC1.Rows.Count; ++index)
        this.dataGridView_PHC1.Rows[index].Cells[1].Value = (object) "s";
    }

    private void dataGridView_PHC2_UserAddedRow(object sender, DataGridViewRowEventArgs e)
    {
      if (this.dataGridView_PHC2.Rows.Count >= 4)
        this.dataGridView_PHC2.AllowUserToAddRows = false;
      else
        this.dataGridView_PHC2.AllowUserToAddRows = true;
    }

    private void dataGridView_PHC2_RowsAdded(object sender, DataGridViewRowsAddedEventArgs e)
    {
      for (int index = 0; index < this.dataGridView_PHC2.Rows.Count; ++index)
        this.dataGridView_PHC2.Rows[index].Cells[1].Value = this.newComboBox_PHC.SelectedIndex != 1 ? (object) "MPa" : (object) "%";
    }

    private void dataGridView_FC1_RowsAdded(object sender, DataGridViewRowsAddedEventArgs e)
    {
      for (int index = 0; index < this.dataGridView_FC1.Rows.Count; ++index)
      {
        switch (this.newComboBox_FC.SelectedIndex)
        {
          case 1:
            this.dataGridView_FC1.Rows[index].Cells[1].Value = (object) "s";
            break;
          case 2:
            this.dataGridView_FC1.Rows[index].Cells[1].Value = (object) "cm3/s";
            break;
          case 3:
            this.dataGridView_FC1.Rows[index].Cells[1].Value = (object) "mm";
            break;
          case 4:
            this.dataGridView_FC1.Rows[index].Cells[1].Value = (object) "%";
            break;
        }
      }
    }

    private void dataGridView_FC2_RowsAdded(object sender, DataGridViewRowsAddedEventArgs e)
    {
      for (int index = 0; index < this.dataGridView_FC2.Rows.Count; ++index)
      {
        switch (this.newComboBox_FC.SelectedIndex)
        {
          case 3:
            this.dataGridView_FC2.Rows[index].Cells[1].Value = (object) "mm";
            break;
          case 4:
            this.dataGridView_FC2.Rows[index].Cells[1].Value = (object) "%";
            break;
        }
      }
    }

    private void dataGridView_FC3_RowsAdded(object sender, DataGridViewRowsAddedEventArgs e)
    {
      for (int index = 0; index < this.dataGridView_FC3.Rows.Count; ++index)
      {
        switch (this.newComboBox_FC.SelectedIndex)
        {
          case 3:
            this.dataGridView_FC3.Rows[index].Cells[1].Value = (object) "mm";
            break;
          case 4:
            this.dataGridView_FC3.Rows[index].Cells[1].Value = (object) "cm3/s";
            break;
        }
      }
    }

    private void dataGridView_FC4_RowsAdded(object sender, DataGridViewRowsAddedEventArgs e)
    {
      for (int index = 0; index < this.dataGridView_FC4.Rows.Count; ++index)
      {
        if (this.newComboBox_FC.SelectedIndex == 3)
          this.dataGridView_FC4.Rows[index].Cells[1].Value = (object) "mm";
      }
    }

    private void dataGridView_VP_RowsAdded(object sender, DataGridViewRowsAddedEventArgs e)
    {
      for (int index = 0; index < this.dataGridView_VP.Rows.Count; ++index)
      {
        switch (this.newComboBox_VP.SelectedIndex)
        {
          case 1:
            this.dataGridView_VP.Rows[index].Cells[1].Value = (object) "%";
            break;
          case 2:
            this.dataGridView_VP.Rows[index].Cells[1].Value = (object) "mm";
            break;
        }
      }
    }

    private void listBox_DB_SelectedIndexChanged(object sender, EventArgs e)
    {
      int num = -1;
      try
      {
        DataRow drProcessDB = clsDefine.g_dtProcessDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == this.listBox_DB.Text)).FirstOrDefault<DataRow>();
        if (drProcessDB == null)
          return;
        this.newTextBox_Company.Value = drProcessDB["Company"].ToString();
        this.newTextBox_Item.Value = drProcessDB["Item"].ToString();
        this.newTextBox_Option.Value = drProcessDB["Option"].ToString();
        this.newTextBox_Mat_UDB.Value = string.Empty;
        this.newTextBox_Mat_Search.Value = string.Empty;
        DataTable table = clsDefine.g_dsMaterial.Tables.Cast<DataTable>().Where<DataTable>((System.Func<DataTable, bool>) (Temp => Temp.TableName == "User")).FirstOrDefault<DataTable>();
        if (table != null)
          clsDefine.g_dsMaterial.Tables.Remove(table);
        FileInfo fiUserUDB = new FileInfo(clsDefine.g_diProcessUDBCfg.ToString() + "\\" + drProcessDB["Mat_UDB"] + ".21000.udb");
        if (fiUserUDB.Exists)
        {
          if (!((IEnumerable<FileInfo>) clsDefine.g_diUserUDB.GetFiles("*.21000.udb")).Any<FileInfo>((System.Func<FileInfo, bool>) (Temp => Temp.Name == fiUserUDB.Name)))
            fiUserUDB.CopyTo(clsDefine.g_diUserUDB.FullName + "\\" + fiUserUDB.Name);
          DataTable materialsFromName = clsHDMFLib.GetUserMaterialsFromName(drProcessDB["Mat_UDB"].ToString());
          if (materialsFromName != null)
            clsDefine.g_dsMaterial.Tables.Add(materialsFromName);
          this.newTextBox_Mat_Search.Value = "";
          this.radioButton_Mat_User.Checked = true;
          this.newTextBox_Mat_UDB.Value = drProcessDB["Mat_UDB"].ToString();
        }
        else
        {
          clsDefine.g_dsMaterial.Tables.Cast<DataTable>().Where<DataTable>((System.Func<DataTable, bool>) (Temp => Temp.TableName == "System")).FirstOrDefault<DataTable>();
          this.radioButton_Mat_System.Checked = true;
        }
        this.RefreshMaterial();
        if (this.newComboBox_Mat_Manufacturer.Items.Contains((object) drProcessDB["Mat_Manufacturer"].ToString()))
          this.newComboBox_Mat_Manufacturer.SelectedIndex = this.newComboBox_Mat_Manufacturer.Items.IndexOf((object) drProcessDB["Mat_Manufacturer"].ToString());
        if (this.newComboBox_Mat_TradeName.Items.Cast<string>().Any<string>((System.Func<string, bool>) (Temp => Temp.Split('[')[0].Trim() == drProcessDB["Mat_TradeName"].ToString())))
          this.newComboBox_Mat_TradeName.SelectedIndex = this.newComboBox_Mat_TradeName.Items.IndexOf((object) this.newComboBox_Mat_TradeName.Items.Cast<string>().Where<string>((System.Func<string, bool>) (Temp => Temp.Split('[')[0].Trim() == drProcessDB["Mat_TradeName"].ToString())).FirstOrDefault<string>());
        if (this.newComboBox_Mat_TradeName.Items.Contains((object) drProcessDB["Mat_TradeName"].ToString()))
          this.newComboBox_Mat_TradeName.SelectedIndex = this.newComboBox_Mat_TradeName.Items.IndexOf((object) drProcessDB["Mat_TradeName"].ToString());
        this.newTextBox_Mat_FamilyAbbreviation.Value = drProcessDB["Mat_Familyabbreviation"].ToString();
        this.newTextBox_Mat_MaterialID.Value = drProcessDB["Mat_MaterialID"].ToString();
        this.newComboBox_Sequence.SelectedIndex = clsUtill.ConvertToInt(drProcessDB["Mat_Sequence"].ToString().Replace(" ", ""));
        this.unitTextBox_CTime.Value = drProcessDB["Mat_CTime"].ToString();
        this.unitTextBox_MetlTemp.Value = drProcessDB["Mat_MeltTemp"].ToString();
        this.unitTextBox_MoldTemp.Value = drProcessDB["Mat_MoldTemp"].ToString();
        this.newComboBox_FC.SelectedIndex = this.newComboBox_FC.Items.IndexOf((object) drProcessDB["FC_Type"].ToString());
        if (this.dataGridView_FC1.BackgroundColor == Color.White)
        {
          if (!this.dataGridView_FC1.AllowUserToAddRows)
          {
            this.dataGridView_FC1.Rows[0].Cells[0].Value = (object) drProcessDB["FC1_1"].ToString();
          }
          else
          {
            this.dataGridView_FC1.Rows.Clear();
            for (int index = 0; index < 14; ++index)
            {
              if (!(drProcessDB["FC1_" + (object) (index + 1)].ToString() == ""))
                this.dataGridView_FC1.Rows[this.dataGridView_FC1.Rows.Add()].Cells[0].Value = (object) drProcessDB["FC1_" + (object) (index + 1)].ToString();
            }
          }
          this.dataGridView_FC1.ClearSelection();
        }
        if (this.dataGridView_FC2.BackgroundColor == Color.White)
        {
          if (!this.dataGridView_FC2.AllowUserToAddRows)
          {
            this.dataGridView_FC2.Rows[0].Cells[0].Value = (object) drProcessDB["FC2_1"].ToString();
          }
          else
          {
            this.dataGridView_FC2.Rows.Clear();
            for (int index = 0; index < 14; ++index)
            {
              if (!(drProcessDB["FC2_" + (object) (index + 1)].ToString() == ""))
                this.dataGridView_FC2.Rows[this.dataGridView_FC2.Rows.Add()].Cells[0].Value = (object) drProcessDB["FC2_" + (object) (index + 1)].ToString();
            }
          }
          this.dataGridView_FC2.ClearSelection();
        }
        if (this.dataGridView_FC3.BackgroundColor == Color.White)
          this.dataGridView_FC3.Rows[0].Cells[0].Value = (object) drProcessDB["FC3"].ToString();
        if (this.dataGridView_FC4.BackgroundColor == Color.White)
          this.dataGridView_FC4.Rows[0].Cells[0].Value = (object) drProcessDB["FC4"].ToString();
        this.newComboBox_VP.SelectedIndex = this.newComboBox_VP.Items.IndexOf((object) drProcessDB["VP_Type"].ToString());
        if (this.dataGridView_VP.BackgroundColor == Color.White)
          this.dataGridView_VP.Rows[0].Cells[0].Value = (object) drProcessDB["VP_Value"].ToString();
        this.newComboBox_PHC.SelectedIndex = this.newComboBox_PHC.Items.IndexOf((object) drProcessDB["PHC_Type"].ToString());
        num = -1;
        this.dataGridView_PHC1.Rows.Clear();
        if (this.dataGridView_PHC1.BackgroundColor == Color.White)
        {
          for (int index = 0; index < 3; ++index)
          {
            if (!(drProcessDB["PHC1_" + (object) (index + 1)].ToString() == ""))
              this.dataGridView_PHC1.Rows[this.dataGridView_PHC1.Rows.Add()].Cells[0].Value = (object) drProcessDB["PHC1_" + (object) (index + 1)].ToString();
          }
          this.dataGridView_PHC1.ClearSelection();
        }
        num = -1;
        this.dataGridView_PHC2.Rows.Clear();
        if (!(this.dataGridView_PHC2.BackgroundColor == Color.White))
          return;
        for (int index = 0; index < 3; ++index)
        {
          if (!(drProcessDB["PHC2_" + (object) (index + 1)].ToString() == ""))
            this.dataGridView_PHC2.Rows[this.dataGridView_PHC2.Rows.Add()].Cells[0].Value = (object) drProcessDB["PHC2_" + (object) (index + 1)].ToString();
        }
        this.dataGridView_PHC2.ClearSelection();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcessDB]listBox_DB_SelectedIndexChanged):" + ex.Message));
      }
    }

    private void newComboBox_Company_SelectedIndexChanged(object sender, EventArgs e)
    {
      this.RefreshDBList(this.newComboBox_Company.Value);
      this.RefreshItemUI(this.newComboBox_Company.Value);
    }

    private void newComboBox_Item_SelectedIndexChanged(object sender, EventArgs e) => this.RefreshDBList(this.newComboBox_Company.Value, this.newComboBox_Item.Value);

    private void newComboBox_Mat_Manufacturer_SelectedIndexChanged(object sender, EventArgs e)
    {
      this.newComboBox_Mat_TradeName.Items.Clear();
      this.newComboBox_Mat_TradeName.Enabled = false;
      this.newTextBox_Mat_FamilyAbbreviation.Value = "";
      this.newTextBox_Mat_MaterialID.Value = "";
      string strQuery = this.newTextBox_Mat_Search.Value;
      string strManufacturer = this.newComboBox_Mat_Manufacturer.Value;
      try
      {
        DataTable source = !this.radioButton_Mat_System.Checked ? clsDefine.g_dsMaterial.Tables["User"] : clsDefine.g_dsMaterial.Tables["System"];
        DataRow[] dataRowArray = strQuery != null && !(strQuery == "") ? source.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Manufacturer"].ToString() == strManufacturer && Temp["TradeName"].ToString().ToUpper().Contains(strQuery.ToUpper()))).ToArray<DataRow>() : source.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Manufacturer"].ToString() == strManufacturer)).ToArray<DataRow>();
        if (dataRowArray.Length == 0)
          return;
        List<string> stringList = new List<string>();
        foreach (DataRow dataRow in dataRowArray)
        {
          string str = dataRow["TradeName"].ToString() + " [ID:" + dataRow["ID"].ToString() + "]";
          if (!stringList.Contains(str))
            stringList.Add(str);
        }
        stringList.Sort();
        this.newComboBox_Mat_TradeName.Items.AddRange((object[]) stringList.ToArray());
        this.newComboBox_Mat_TradeName.Enabled = true;
        this.newComboBox_Mat_TradeName.SelectedIndex = 0;
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcessDB]newComboBox_Mat_Manufacturer_SelectedIndexChanged):" + ex.Message));
      }
    }

    private void newComboBox_Mat_TradeName_SelectedIndexChanged(object sender, EventArgs e)
    {
      this.newTextBox_Mat_FamilyAbbreviation.Value = "";
      this.newTextBox_Mat_MaterialID.Value = "";
      try
      {
        string strManufacturer = this.newComboBox_Mat_Manufacturer.Value;
        string strTradeName = "";
        string strMaterialID = "";
        string[] strArray = this.newComboBox_Mat_TradeName.Value.Split('[');
        strTradeName = strArray[0].TrimEnd();
        strMaterialID = strArray[1].Replace("]", "").Replace("ID:", "");
        DataRow dataRow = (!this.radioButton_Mat_System.Checked ? clsDefine.g_dsMaterial.Tables["User"] : clsDefine.g_dsMaterial.Tables["System"]).AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Manufacturer"].ToString() == strManufacturer && Temp["TradeName"].ToString() == strTradeName && Temp["ID"].ToString() == strMaterialID)).FirstOrDefault<DataRow>();
        if (dataRow == null)
          return;
        this.newTextBox_Mat_FamilyAbbreviation.Value = dataRow["Familyabbreviation"].ToString();
        this.newTextBox_Mat_MaterialID.Value = dataRow["MaterialID"].ToString();
        if (dataRow.Table.Columns.Contains("MoldTemp"))
          this.unitTextBox_MoldTemp.Value = dataRow["MoldTemp"].ToString();
        if (!dataRow.Table.Columns.Contains("MeltTemp"))
          return;
        this.unitTextBox_MetlTemp.Value = dataRow["MeltTemp"].ToString();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcessDB]newComboBox_Mat_TradeName_SelectedIndexChanged):" + ex.Message));
      }
    }

    private void newButton_NewClick(object sender, EventArgs e)
    {
      NewButton newButton = sender as NewButton;
      if (newButton == this.newButton_Add)
        this.AddDB();
      else if (newButton == this.newButton_Edit)
        this.EditDB();
      else if (newButton == this.newButton_Del)
        this.DeleteDB();
      else if (newButton == this.newButton_Import)
        this.ImportDB();
      else
        this.ExportDB();
    }

    private void AddDB()
    {
      if (this.newTextBox_Company.Value == "" || this.newTextBox_Item.Value == "" || this.newTextBox_Option.Value == "")
        return;
      try
      {
        if (((IEnumerable<DataRow>) clsDefine.g_dtProcessDB.AsEnumerable().ToArray<DataRow>()).Any<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Company"].ToString() == this.newTextBox_Company.Value && Temp["Item"].ToString() == this.newTextBox_Item.Value && Temp["Option"].ToString() == this.newTextBox_Option.Value)))
          return;
        DataRow p_drProcessDB = clsDefine.g_dtProcessDB.Rows.Add();
        p_drProcessDB["Name"] = (object) (this.newTextBox_Company.Value + "_" + this.newTextBox_Item.Value + "_" + this.newTextBox_Option.Value);
        p_drProcessDB["Company"] = (object) this.newTextBox_Company.Value;
        p_drProcessDB["Item"] = (object) this.newTextBox_Item.Value;
        p_drProcessDB["Option"] = (object) this.newTextBox_Option.Value;
        this.UpdateDB(p_drProcessDB);
        this.CreateDBFile(p_drProcessDB);
        this.RefreshDBList();
        this.RefreshCompanyUI();
        this.RefreshItemUI();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcessDB]AddDB):" + ex.Message));
      }
    }

    private void EditDB()
    {
      if (this.listBox_DB.SelectedIndex == -1)
        return;
      try
      {
        string strProcessDB = this.listBox_DB.Text;
        string p_strCompany = this.newComboBox_Company.Value;
        string p_strItem = this.newComboBox_Item.Value;
        DataRow p_drProcessDB = clsDefine.g_dtProcessDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == strProcessDB)).FirstOrDefault<DataRow>();
        if (p_drProcessDB == null)
          return;
        this.UpdateDB(p_drProcessDB);
        FileInfo fileInfo = new FileInfo(clsDefine.g_diProcessDBCfg.ToString() + "\\" + p_drProcessDB["Name"] + ".xml");
        if (fileInfo.Exists)
          fileInfo.Delete();
        p_drProcessDB["Name"] = (object) (this.newTextBox_Company.Value + "_" + this.newTextBox_Item.Value + "_" + this.newTextBox_Option.Value);
        p_drProcessDB["Company"] = (object) this.newTextBox_Company.Value;
        p_drProcessDB["Item"] = (object) this.newTextBox_Item.Value;
        p_drProcessDB["Option"] = (object) this.newTextBox_Option.Value;
        this.CreateDBFile(p_drProcessDB);
        this.RefreshDBList(p_strCompany, p_strItem);
        this.RefreshCompanyUI(p_strCompany);
        this.RefreshItemUI(p_strCompany);
        this.listBox_DB.SelectedIndex = this.listBox_DB.Items.IndexOf(p_drProcessDB["Name"]);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcessDB]EditDB):" + ex.Message));
      }
    }

    private void DeleteDB()
    {
      bool flag = false;
      if (this.listBox_DB.SelectedItems.Count == 0)
        return;
      try
      {
        if (clsUtill.ShowMessageBox((Form) this, LocaleControl.getInstance().GetString("IDS_WANT_DELETE"), this.Text, MessageBoxButtons.YesNo, MessageBoxIcon.Asterisk) != DialogResult.Yes)
          return;
        foreach (string selectedItem in this.listBox_DB.SelectedItems)
        {
          string strProcessDB = selectedItem;
          DataRow row = clsDefine.g_dtProcessDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == strProcessDB)).FirstOrDefault<DataRow>();
          if (row != null)
          {
            FileInfo fileInfo = new FileInfo(clsDefine.g_diProcessDBCfg.ToString() + "\\" + row["Name"] + ".xml");
            if (fileInfo.Exists)
              fileInfo.Delete();
            clsDefine.g_dtProcessDB.Rows.Remove(row);
            flag = true;
          }
        }
        if (flag)
        {
          this.RefreshDBList();
          this.RefreshCompanyUI();
          this.RefreshItemUI();
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcessDB]DeleteDB):" + ex.Message));
      }
    }

    private void ImportDB()
    {
      string strProcessDB = "";
      bool flag = false;
      try
      {
        OpenFileDialog openFileDialog = new OpenFileDialog();
        openFileDialog.Filter = "DB Files(*.xml)|*.xml";
        openFileDialog.Multiselect = true;
        if (openFileDialog.ShowDialog((IWin32Window) this) != DialogResult.OK)
          return;
        foreach (string fileName in openFileDialog.FileNames)
        {
          strProcessDB = Path.GetFileNameWithoutExtension(fileName);
          if (!clsDefine.g_dtProcessDB.AsEnumerable().Any<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == strProcessDB)))
          {
            if (strProcessDB.Split('_').Length == 3)
            {
              FileInfo fileInfo = new FileInfo(clsDefine.g_diProcessDBCfg.FullName + "\\" + strProcessDB + ".xml");
              File.Copy(fileName, fileInfo.FullName, true);
              flag = true;
            }
          }
        }
        if (flag)
        {
          clsData.LoadProcessDB();
          this.RefreshDBList();
          this.RefreshCompanyUI();
          this.RefreshItemUI();
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcessDB]ImportDB):" + ex.Message));
      }
    }

    private void ExportDB()
    {
      if (this.listBox_DB.SelectedItems.Count == 0)
        return;
      try
      {
        CommonOpenFileDialog commonOpenFileDialog = new CommonOpenFileDialog()
        {
          IsFolderPicker = true
        };
        if (commonOpenFileDialog.ShowDialog() != CommonFileDialogResult.Ok)
          return;
        foreach (string selectedItem in this.listBox_DB.SelectedItems)
        {
          string strProcessDB = selectedItem;
          DataRow dataRow = clsDefine.g_dtProcessDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == strProcessDB)).FirstOrDefault<DataRow>();
          if (dataRow != null)
          {
            FileInfo fileInfo = new FileInfo(clsDefine.g_diProcessDBCfg.FullName + "\\" + dataRow["Name"].ToString() + ".xml");
            if (fileInfo.Exists)
              fileInfo.CopyTo(commonOpenFileDialog.FileName + "\\" + fileInfo.Name, true);
          }
        }
        Process.Start(commonOpenFileDialog.FileName);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcessDB]ExportDB):" + ex.Message));
      }
    }

    private void UpdateDB(DataRow p_drProcessDB)
    {
      try
      {
        string str = this.newComboBox_Mat_TradeName.Value.Split('[')[0].TrimEnd();
        p_drProcessDB["Mat_UDB"] = (object) this.newTextBox_Mat_UDB.Value;
        p_drProcessDB["Mat_Manufacturer"] = (object) this.newComboBox_Mat_Manufacturer.Value;
        p_drProcessDB["Mat_TradeName"] = (object) str;
        p_drProcessDB["Mat_Familyabbreviation"] = (object) this.newTextBox_Mat_FamilyAbbreviation.Value;
        p_drProcessDB["Mat_MaterialID"] = (object) this.newTextBox_Mat_MaterialID.Value;
        p_drProcessDB["Mat_Sequence"] = (object) this.newComboBox_Sequence.SelectedIndex;
        p_drProcessDB["Mat_CTime"] = (object) this.unitTextBox_CTime.Value;
        p_drProcessDB["Mat_MeltTemp"] = (object) this.unitTextBox_MetlTemp.Value;
        p_drProcessDB["Mat_MoldTemp"] = (object) this.unitTextBox_MoldTemp.Value;
        p_drProcessDB["FC_Type"] = (object) this.newComboBox_FC.Value;
        for (int index = 0; index < 14; ++index)
        {
          if (index < this.dataGridView_FC1.Rows.Count)
          {
            if (this.dataGridView_FC1.Rows[index].Cells[0].Value == null)
              p_drProcessDB["FC1_" + (object) (index + 1)] = (object) "";
            else
              p_drProcessDB["FC1_" + (object) (index + 1)] = this.dataGridView_FC1.Rows[index].Cells[0].Value;
          }
          else
            p_drProcessDB["FC1_" + (object) (index + 1)] = (object) "";
        }
        for (int index = 0; index < 14; ++index)
        {
          if (index < this.dataGridView_FC2.Rows.Count)
          {
            if (this.dataGridView_FC2.Rows[index].Cells[0].Value == null)
              p_drProcessDB["FC2_" + (object) (index + 1)] = (object) "";
            else
              p_drProcessDB["FC2_" + (object) (index + 1)] = this.dataGridView_FC2.Rows[index].Cells[0].Value;
          }
          else
            p_drProcessDB["FC2_" + (object) (index + 1)] = (object) "";
        }
        int num1 = this.dataGridView_FC3.Rows.Count == 0 ? 1 : (this.dataGridView_FC3.Rows[0].Cells[0].Value == null ? 1 : 0);
        p_drProcessDB["FC3"] = num1 == 0 ? this.dataGridView_FC3.Rows[0].Cells[0].Value : (object) "";
        int num2 = this.dataGridView_FC4.Rows.Count == 0 ? 1 : (this.dataGridView_FC4.Rows[0].Cells[0].Value == null ? 1 : 0);
        p_drProcessDB["FC4"] = num2 == 0 ? this.dataGridView_FC4.Rows[0].Cells[0].Value : (object) "";
        p_drProcessDB["VP_Type"] = (object) this.newComboBox_VP.Value;
        int num3 = this.dataGridView_VP.Rows.Count == 0 ? 1 : (this.dataGridView_VP.Rows[0].Cells[0].Value == null ? 1 : 0);
        p_drProcessDB["VP_Value"] = num3 == 0 ? this.dataGridView_VP.Rows[0].Cells[0].Value : (object) "";
        p_drProcessDB["PHC_Type"] = (object) this.newComboBox_PHC.Value;
        for (int index = 0; index < 3; ++index)
        {
          if (index < this.dataGridView_PHC1.Rows.Count)
          {
            if (this.dataGridView_PHC1.Rows[index].Cells[0].Value == null)
              p_drProcessDB["PHC1_" + (object) (index + 1)] = (object) "";
            else
              p_drProcessDB["PHC1_" + (object) (index + 1)] = this.dataGridView_PHC1.Rows[index].Cells[0].Value;
          }
          else
            p_drProcessDB["PHC1_" + (object) (index + 1)] = (object) "";
        }
        for (int index = 0; index < 3; ++index)
        {
          if (index < this.dataGridView_PHC2.Rows.Count)
          {
            if (this.dataGridView_PHC2.Rows[index].Cells[0].Value == null)
              p_drProcessDB["PHC2_" + (object) (index + 1)] = (object) "";
            else
              p_drProcessDB["PHC2_" + (object) (index + 1)] = this.dataGridView_PHC2.Rows[index].Cells[0].Value;
          }
          else
            p_drProcessDB["PHC2_" + (object) (index + 1)] = (object) "";
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcessDB]UpdateDB):" + ex.Message));
      }
    }

    private void CreateDBFile(DataRow p_drProcessDB)
    {
      try
      {
        FileInfo fileInfo = new FileInfo(clsDefine.g_diProcessDBCfg.ToString() + "\\" + p_drProcessDB["Name"].ToString() + ".xml");
        if (!fileInfo.Directory.Exists)
          fileInfo.Directory.Create();
        using (XmlWriter xmlWriter = XmlWriter.Create(fileInfo.FullName, new XmlWriterSettings()
        {
          Indent = true,
          OmitXmlDeclaration = true,
          NewLineOnAttributes = true
        }))
        {
          xmlWriter.WriteStartDocument();
          xmlWriter.WriteStartElement("DB");
          xmlWriter.WriteEndElement();
          xmlWriter.Flush();
        }
        using (XmlReader reader = XmlReader.Create(fileInfo.FullName))
        {
          XmlDocument xmlDocument = new XmlDocument();
          xmlDocument.Load(reader);
          XmlElement documentElement = xmlDocument.DocumentElement;
          documentElement.RemoveAll();
          XmlNode element1 = (XmlNode) xmlDocument.CreateElement("Material");
          XmlNode element2 = (XmlNode) xmlDocument.CreateElement("UDB");
          element2.InnerText = p_drProcessDB["Mat_UDB"].ToString();
          element1.AppendChild(element2);
          XmlNode element3 = (XmlNode) xmlDocument.CreateElement("Manufacturer");
          element3.InnerText = p_drProcessDB["Mat_Manufacturer"].ToString();
          element1.AppendChild(element3);
          XmlNode element4 = (XmlNode) xmlDocument.CreateElement("TradeName");
          element4.InnerText = p_drProcessDB["Mat_TradeName"].ToString();
          element1.AppendChild(element4);
          XmlNode element5 = (XmlNode) xmlDocument.CreateElement("FamilyAbbreviation");
          element5.InnerText = p_drProcessDB["Mat_FamilyAbbreviation"].ToString();
          element1.AppendChild(element5);
          XmlNode element6 = (XmlNode) xmlDocument.CreateElement("MaterialID");
          element6.InnerText = p_drProcessDB["Mat_MaterialID"].ToString();
          element1.AppendChild(element6);
          XmlNode element7 = (XmlNode) xmlDocument.CreateElement("Sequence");
          element7.InnerText = p_drProcessDB["Mat_Sequence"].ToString();
          element1.AppendChild(element7);
          XmlNode element8 = (XmlNode) xmlDocument.CreateElement("CoolingTime");
          element8.InnerText = p_drProcessDB["Mat_CTime"].ToString();
          element1.AppendChild(element8);
          XmlNode element9 = (XmlNode) xmlDocument.CreateElement("MeltTemperature");
          element9.InnerText = p_drProcessDB["Mat_MeltTemp"].ToString();
          element1.AppendChild(element9);
          XmlNode element10 = (XmlNode) xmlDocument.CreateElement("MoldTemperature");
          element10.InnerText = p_drProcessDB["Mat_MoldTemp"].ToString();
          element1.AppendChild(element10);
          documentElement.AppendChild(element1);
          XmlNode element11 = (XmlNode) xmlDocument.CreateElement("FillingControl");
          XmlNode element12 = (XmlNode) xmlDocument.CreateElement("Type");
          element12.InnerText = p_drProcessDB["FC_Type"].ToString();
          element11.AppendChild(element12);
          XmlNode element13 = (XmlNode) xmlDocument.CreateElement("FC1");
          for (int index = 0; index < 14; ++index)
          {
            XmlAttribute attribute = xmlDocument.CreateAttribute("V" + (object) (index + 1));
            attribute.Value = p_drProcessDB["FC1_" + (object) (index + 1)].ToString();
            element13.Attributes.Append(attribute);
          }
          element11.AppendChild(element13);
          XmlNode element14 = (XmlNode) xmlDocument.CreateElement("FC2");
          for (int index = 0; index < 14; ++index)
          {
            XmlAttribute attribute = xmlDocument.CreateAttribute("V" + (object) (index + 1));
            attribute.Value = p_drProcessDB["FC2_" + (object) (index + 1)].ToString();
            element14.Attributes.Append(attribute);
          }
          element11.AppendChild(element14);
          XmlNode element15 = (XmlNode) xmlDocument.CreateElement("FC3");
          XmlAttribute attribute1 = xmlDocument.CreateAttribute("V1");
          attribute1.Value = p_drProcessDB["FC3"].ToString();
          element15.Attributes.Append(attribute1);
          element11.AppendChild(element15);
          XmlNode element16 = (XmlNode) xmlDocument.CreateElement("FC4");
          XmlAttribute attribute2 = xmlDocument.CreateAttribute("V1");
          attribute2.Value = p_drProcessDB["FC4"].ToString();
          element16.Attributes.Append(attribute2);
          element11.AppendChild(element16);
          documentElement.AppendChild(element11);
          XmlNode element17 = (XmlNode) xmlDocument.CreateElement("VPSwitchOver");
          XmlNode element18 = (XmlNode) xmlDocument.CreateElement("Type");
          element18.InnerText = p_drProcessDB["VP_Type"].ToString();
          element17.AppendChild(element18);
          XmlNode element19 = (XmlNode) xmlDocument.CreateElement("Value");
          element19.InnerText = p_drProcessDB["VP_Value"].ToString();
          element17.AppendChild(element19);
          documentElement.AppendChild(element17);
          XmlNode element20 = (XmlNode) xmlDocument.CreateElement("PackHoldingControl");
          XmlNode element21 = (XmlNode) xmlDocument.CreateElement("Type");
          element21.InnerText = p_drProcessDB["PHC_Type"].ToString();
          element20.AppendChild(element21);
          XmlNode element22 = (XmlNode) xmlDocument.CreateElement("PHC1");
          for (int index = 0; index < 3; ++index)
          {
            XmlAttribute attribute3 = xmlDocument.CreateAttribute("V" + (object) (index + 1));
            attribute3.Value = p_drProcessDB["PHC1_" + (object) (index + 1)].ToString();
            element22.Attributes.Append(attribute3);
          }
          element20.AppendChild(element22);
          XmlNode element23 = (XmlNode) xmlDocument.CreateElement("PHC2");
          for (int index = 0; index < 3; ++index)
          {
            XmlAttribute attribute4 = xmlDocument.CreateAttribute("V" + (object) (index + 1));
            attribute4.Value = p_drProcessDB["PHC2_" + (object) (index + 1)].ToString();
            element23.Attributes.Append(attribute4);
          }
          element20.AppendChild(element23);
          documentElement.AppendChild(element20);
          reader.Close();
          xmlDocument.Save(fileInfo.FullName);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcessDB]CreateDBFile):" + ex.Message));
      }
    }

    private void frmProcessDB_KeyDown(object sender, KeyEventArgs e)
    {
      if (e.KeyCode != Keys.Escape)
        return;
      this.Close();
    }

    private void newButton_Mat_UDB_NewClick(object sender, EventArgs e)
    {
      try
      {
        OpenFileDialog openFileDialog = new OpenFileDialog();
        if (this.newTextBox_Mat_UDB.Value != null || this.newTextBox_Mat_UDB.Value != "")
          openFileDialog.InitialDirectory = clsDefine.g_diProcessUDBCfg.FullName;
        openFileDialog.Filter = "Autodesk Moldflow Material Files (*.21000.udb)|*.21000.udb";
        if (openFileDialog.ShowDialog() != DialogResult.OK)
          return;
        FileInfo fiUserUDB = new FileInfo(openFileDialog.FileName);
        string p_strUserUDB = Path.GetFileName(openFileDialog.FileName).Replace(".21000.udb", "");
        if (!clsDefine.g_diProcessUDBCfg.Exists)
          clsDefine.g_diProcessUDBCfg.Create();
        FileInfo fileInfo = new FileInfo(clsDefine.g_diProcessUDBCfg.FullName + "\\" + Path.GetFileName(openFileDialog.FileName));
        fiUserUDB.CopyTo(fileInfo.FullName, true);
        if (!((IEnumerable<FileInfo>) clsDefine.g_diUserUDB.GetFiles("*.21000.udb")).Any<FileInfo>((System.Func<FileInfo, bool>) (Temp => Temp.Name == fiUserUDB.Name)))
          fiUserUDB.CopyTo(clsDefine.g_diUserUDB.FullName + "\\" + fiUserUDB.Name);
        DataTable materialsFromName = clsHDMFLib.GetUserMaterialsFromName(p_strUserUDB);
        if (materialsFromName != null)
          clsDefine.g_dsMaterial.Tables.Add(materialsFromName);
        this.newTextBox_Mat_Search.Value = "";
        this.newTextBox_Mat_UDB.Value = p_strUserUDB;
        this.RefreshMaterial();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmProcessDB]newButton_Mat_UDB_NewClick):" + ex.Message));
      }
    }

    private void radioButton_Mat_System_CheckedChanged(object sender, EventArgs e)
    {
      if (!this.radioButton_Mat_System.Checked)
        return;
      this.newButton_Mat_UDB.Enabled = false;
      this.newTextBox_Mat_UDB.Value = string.Empty;
      this.RefreshMaterial();
    }

    private void radioButton_Mat_User_CheckedChanged(object sender, EventArgs e)
    {
      if (!this.radioButton_Mat_User.Checked)
        return;
      this.newButton_Mat_UDB.Enabled = true;
      DataRow dataRow = clsDefine.g_dtProcessDB.AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Name"].ToString() == this.listBox_DB.Text)).FirstOrDefault<DataRow>();
      if (dataRow != null)
        this.newTextBox_Mat_UDB.Value = dataRow["Mat_UDB"].ToString();
      this.RefreshMaterial();
    }

    private void newTextBox_Mat_Search_TextBoxKeyUp(object sender, KeyEventArgs e)
    {
      if (e.KeyCode != Keys.Return)
        return;
      this.RefreshMaterial();
    }

    private void dataGridView_FC_RowPostPaint(object sender, DataGridViewRowPostPaintEventArgs e)
    {
      DataGridView dataGridView = sender as DataGridView;
      string s = (e.RowIndex + 1).ToString();
      StringFormat format = new StringFormat()
      {
        Alignment = StringAlignment.Center,
        LineAlignment = StringAlignment.Center
      };
      Rectangle layoutRectangle;
      ref Rectangle local = ref layoutRectangle;
      Rectangle rowBounds = e.RowBounds;
      int left = rowBounds.Left;
      rowBounds = e.RowBounds;
      int top = rowBounds.Top;
      int rowHeadersWidth = dataGridView.RowHeadersWidth;
      rowBounds = e.RowBounds;
      int height = rowBounds.Height;
      local = new Rectangle(left, top, rowHeadersWidth, height);
      e.Graphics.DrawString(s, this.Font, SystemBrushes.ControlText, (RectangleF) layoutRectangle, format);
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      DataGridViewCellStyle gridViewCellStyle1 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle2 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle3 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle4 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle5 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle6 = new DataGridViewCellStyle();
      DataGridViewCellStyle gridViewCellStyle7 = new DataGridViewCellStyle();
      this.label_DB_Opt_W = new Label();
      this.label_DB_Item_W = new Label();
      this.label_DB_Item = new Label();
      this.label_DB_Company_W = new Label();
      this.label_DB_Company = new Label();
      this.label_Mat_TradeName = new Label();
      this.label_Mat_Manufacturer = new Label();
      this.listBox_DB = new ListBox();
      this.label_Suji = new Label();
      this.label_DB_List = new Label();
      this.label_Mat_FamilyAbbreviation = new Label();
      this.label_Mat_MaterialID = new Label();
      this.label4 = new Label();
      this.label_Mat_Sequence = new Label();
      this.label_Mat_CTime = new Label();
      this.label9 = new Label();
      this.label10 = new Label();
      this.label_FC1 = new Label();
      this.label_FC2 = new Label();
      this.dataGridView_FC1 = new DataGridView();
      this.Column1 = new DataGridViewTextBoxColumn();
      this.Column2 = new DataGridViewTextBoxColumn();
      this.label_FC3 = new Label();
      this.dataGridView_FC3 = new DataGridView();
      this.Column5 = new DataGridViewTextBoxColumn();
      this.Column7 = new DataGridViewTextBoxColumn();
      this.label_FC4 = new Label();
      this.label15 = new Label();
      this.label16 = new Label();
      this.label_PHC1 = new Label();
      this.label_PHC2 = new Label();
      this.panel1 = new Panel();
      this.panel2 = new Panel();
      this.dataGridView_FC2 = new DataGridView();
      this.dataGridViewTextBoxColumn1 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn2 = new DataGridViewTextBoxColumn();
      this.panel3 = new Panel();
      this.panel4 = new Panel();
      this.dataGridView_FC4 = new DataGridView();
      this.dataGridViewTextBoxColumn3 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn4 = new DataGridViewTextBoxColumn();
      this.panel5 = new Panel();
      this.dataGridView_VP = new DataGridView();
      this.dataGridViewTextBoxColumn5 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn6 = new DataGridViewTextBoxColumn();
      this.panel6 = new Panel();
      this.dataGridView_PHC1 = new DataGridView();
      this.dataGridViewTextBoxColumn7 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn8 = new DataGridViewTextBoxColumn();
      this.panel7 = new Panel();
      this.dataGridView_PHC2 = new DataGridView();
      this.dataGridViewTextBoxColumn9 = new DataGridViewTextBoxColumn();
      this.dataGridViewTextBoxColumn10 = new DataGridViewTextBoxColumn();
      this.newButton_Export = new NewButton();
      this.newButton_Import = new NewButton();
      this.newButton_Del = new NewButton();
      this.newButton_Edit = new NewButton();
      this.newButton_Add = new NewButton();
      this.unitTextBox_MoldTemp = new UnitTextBox();
      this.unitTextBox_MetlTemp = new UnitTextBox();
      this.unitTextBox_CTime = new UnitTextBox();
      this.newComboBox_Sequence = new NewComboBox();
      this.newComboBox_PHC = new NewComboBox();
      this.newComboBox_VP = new NewComboBox();
      this.newComboBox_FC = new NewComboBox();
      this.newButton_Mat_UDB = new NewButton();
      this.newTextBox_Mat_FamilyAbbreviation = new NewTextBox();
      this.newTextBox_Mat_UDB = new NewTextBox();
      this.newTextBox_Option = new NewTextBox();
      this.newTextBox_Item = new NewTextBox();
      this.newTextBox_Company = new NewTextBox();
      this.newComboBox_Item = new NewComboBox();
      this.newComboBox_Company = new NewComboBox();
      this.panel8 = new Panel();
      this.radioButton_Mat_User = new RadioButton();
      this.radioButton_Mat_System = new RadioButton();
      this.label_Mat_Material = new Label();
      this.label_Suji_SearchCond = new Label();
      this.label12 = new Label();
      this.newTextBox_Mat_Search = new NewTextBox();
      this.newComboBox_Mat_Manufacturer = new NewComboBox();
      this.newComboBox_Mat_TradeName = new NewComboBox();
      this.newTextBox_Mat_MaterialID = new NewTextBox();
      ((ISupportInitialize) this.dataGridView_FC1).BeginInit();
      ((ISupportInitialize) this.dataGridView_FC3).BeginInit();
      this.panel1.SuspendLayout();
      this.panel2.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_FC2).BeginInit();
      this.panel3.SuspendLayout();
      this.panel4.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_FC4).BeginInit();
      this.panel5.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_VP).BeginInit();
      this.panel6.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_PHC1).BeginInit();
      this.panel7.SuspendLayout();
      ((ISupportInitialize) this.dataGridView_PHC2).BeginInit();
      this.panel8.SuspendLayout();
      this.SuspendLayout();
      this.label_DB_Opt_W.BackColor = Color.Lavender;
      this.label_DB_Opt_W.BorderStyle = BorderStyle.FixedSingle;
      this.label_DB_Opt_W.Location = new Point(205, 566);
      this.label_DB_Opt_W.Name = "label_DB_Opt_W";
      this.label_DB_Opt_W.Size = new Size(101, 23);
      this.label_DB_Opt_W.TabIndex = 39;
      this.label_DB_Opt_W.Text = "옵션";
      this.label_DB_Opt_W.TextAlign = ContentAlignment.MiddleCenter;
      this.label_DB_Item_W.BackColor = Color.Lavender;
      this.label_DB_Item_W.BorderStyle = BorderStyle.FixedSingle;
      this.label_DB_Item_W.Location = new Point(105, 566);
      this.label_DB_Item_W.Name = "label_DB_Item_W";
      this.label_DB_Item_W.Size = new Size(101, 23);
      this.label_DB_Item_W.TabIndex = 34;
      this.label_DB_Item_W.Text = "아이템명";
      this.label_DB_Item_W.TextAlign = ContentAlignment.MiddleCenter;
      this.label_DB_Item.BackColor = Color.Lavender;
      this.label_DB_Item.BorderStyle = BorderStyle.FixedSingle;
      this.label_DB_Item.Location = new Point(155, 24);
      this.label_DB_Item.Name = "label_DB_Item";
      this.label_DB_Item.Size = new Size(151, 23);
      this.label_DB_Item.TabIndex = 33;
      this.label_DB_Item.Text = "아이템명";
      this.label_DB_Item.TextAlign = ContentAlignment.MiddleCenter;
      this.label_DB_Company_W.BackColor = Color.Lavender;
      this.label_DB_Company_W.BorderStyle = BorderStyle.FixedSingle;
      this.label_DB_Company_W.Location = new Point(5, 566);
      this.label_DB_Company_W.Name = "label_DB_Company_W";
      this.label_DB_Company_W.Size = new Size(101, 23);
      this.label_DB_Company_W.TabIndex = 32;
      this.label_DB_Company_W.Text = "회사명";
      this.label_DB_Company_W.TextAlign = ContentAlignment.MiddleCenter;
      this.label_DB_Company.BackColor = Color.Lavender;
      this.label_DB_Company.BorderStyle = BorderStyle.FixedSingle;
      this.label_DB_Company.Location = new Point(5, 24);
      this.label_DB_Company.Name = "label_DB_Company";
      this.label_DB_Company.Size = new Size(151, 23);
      this.label_DB_Company.TabIndex = 38;
      this.label_DB_Company.Text = "회사명";
      this.label_DB_Company.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Mat_TradeName.BackColor = Color.Lavender;
      this.label_Mat_TradeName.BorderStyle = BorderStyle.FixedSingle;
      this.label_Mat_TradeName.Location = new Point(557, 135);
      this.label_Mat_TradeName.Name = "label_Mat_TradeName";
      this.label_Mat_TradeName.Size = new Size(248, 23);
      this.label_Mat_TradeName.TabIndex = 48;
      this.label_Mat_TradeName.Text = "Trade Name";
      this.label_Mat_TradeName.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Mat_Manufacturer.BackColor = Color.Lavender;
      this.label_Mat_Manufacturer.BorderStyle = BorderStyle.FixedSingle;
      this.label_Mat_Manufacturer.Location = new Point(310, 135);
      this.label_Mat_Manufacturer.Name = "label_Mat_Manufacturer";
      this.label_Mat_Manufacturer.Size = new Size(248, 23);
      this.label_Mat_Manufacturer.TabIndex = 49;
      this.label_Mat_Manufacturer.Text = "Manufacturer";
      this.label_Mat_Manufacturer.TextAlign = ContentAlignment.MiddleCenter;
      this.listBox_DB.FormattingEnabled = true;
      this.listBox_DB.ItemHeight = 15;
      this.listBox_DB.Location = new Point(5, 68);
      this.listBox_DB.Name = "listBox_DB";
      this.listBox_DB.SelectionMode = SelectionMode.MultiExtended;
      this.listBox_DB.Size = new Size(301, 499);
      this.listBox_DB.TabIndex = 3;
      this.listBox_DB.TabStop = false;
      this.listBox_DB.SelectedIndexChanged += new EventHandler(this.listBox_DB_SelectedIndexChanged);
      this.label_Suji.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Suji.BorderStyle = BorderStyle.FixedSingle;
      this.label_Suji.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Suji.ForeColor = Color.MidnightBlue;
      this.label_Suji.Location = new Point(310, 53);
      this.label_Suji.Name = "label_Suji";
      this.label_Suji.Size = new Size(495, 20);
      this.label_Suji.TabIndex = 21;
      this.label_Suji.Text = "수지 데이터";
      this.label_Suji.TextAlign = ContentAlignment.MiddleCenter;
      this.label_DB_List.BackColor = Color.FromArgb(229, 238, 248);
      this.label_DB_List.BorderStyle = BorderStyle.FixedSingle;
      this.label_DB_List.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_DB_List.ForeColor = Color.MidnightBlue;
      this.label_DB_List.Location = new Point(5, 5);
      this.label_DB_List.Name = "label_DB_List";
      this.label_DB_List.Size = new Size(301, 20);
      this.label_DB_List.TabIndex = 16;
      this.label_DB_List.Text = "DB 리스트";
      this.label_DB_List.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Mat_FamilyAbbreviation.BackColor = Color.Lavender;
      this.label_Mat_FamilyAbbreviation.BorderStyle = BorderStyle.FixedSingle;
      this.label_Mat_FamilyAbbreviation.Location = new Point(310, 179);
      this.label_Mat_FamilyAbbreviation.Name = "label_Mat_FamilyAbbreviation";
      this.label_Mat_FamilyAbbreviation.Size = new Size(248, 23);
      this.label_Mat_FamilyAbbreviation.TabIndex = 48;
      this.label_Mat_FamilyAbbreviation.Text = "Family abbreviation";
      this.label_Mat_FamilyAbbreviation.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Mat_MaterialID.BackColor = Color.Lavender;
      this.label_Mat_MaterialID.BorderStyle = BorderStyle.FixedSingle;
      this.label_Mat_MaterialID.Location = new Point(557, 179);
      this.label_Mat_MaterialID.Name = "label_Mat_MaterialID";
      this.label_Mat_MaterialID.Size = new Size(248, 23);
      this.label_Mat_MaterialID.TabIndex = 48;
      this.label_Mat_MaterialID.Text = "Material ID";
      this.label_Mat_MaterialID.TextAlign = ContentAlignment.MiddleCenter;
      this.label4.BackColor = Color.FromArgb(229, 238, 248);
      this.label4.BorderStyle = BorderStyle.FixedSingle;
      this.label4.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label4.ForeColor = Color.MidnightBlue;
      this.label4.Location = new Point(310, 315);
      this.label4.Name = "label4";
      this.label4.Size = new Size(495, 20);
      this.label4.TabIndex = 21;
      this.label4.Text = "Filling Control";
      this.label4.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Mat_Sequence.BackColor = Color.Lavender;
      this.label_Mat_Sequence.BorderStyle = BorderStyle.FixedSingle;
      this.label_Mat_Sequence.Location = new Point(310, 223);
      this.label_Mat_Sequence.Name = "label_Mat_Sequence";
      this.label_Mat_Sequence.Size = new Size(248, 23);
      this.label_Mat_Sequence.TabIndex = 49;
      this.label_Mat_Sequence.TextAlign = ContentAlignment.MiddleCenter;
      this.label_Mat_CTime.BackColor = Color.Lavender;
      this.label_Mat_CTime.BorderStyle = BorderStyle.FixedSingle;
      this.label_Mat_CTime.Location = new Point(557, 223);
      this.label_Mat_CTime.Name = "label_Mat_CTime";
      this.label_Mat_CTime.Size = new Size(248, 23);
      this.label_Mat_CTime.TabIndex = 48;
      this.label_Mat_CTime.TextAlign = ContentAlignment.MiddleCenter;
      this.label9.BackColor = Color.Lavender;
      this.label9.BorderStyle = BorderStyle.FixedSingle;
      this.label9.Location = new Point(310, 267);
      this.label9.Name = "label9";
      this.label9.Size = new Size(248, 23);
      this.label9.TabIndex = 48;
      this.label9.Text = "Melt Temperature";
      this.label9.TextAlign = ContentAlignment.MiddleCenter;
      this.label10.BackColor = Color.Lavender;
      this.label10.BorderStyle = BorderStyle.FixedSingle;
      this.label10.Location = new Point(557, 267);
      this.label10.Name = "label10";
      this.label10.Size = new Size(248, 23);
      this.label10.TabIndex = 48;
      this.label10.Text = "Mold Temperature";
      this.label10.TextAlign = ContentAlignment.MiddleCenter;
      this.label_FC1.BackColor = Color.WhiteSmoke;
      this.label_FC1.BorderStyle = BorderStyle.FixedSingle;
      this.label_FC1.Location = new Point(517, 334);
      this.label_FC1.Name = "label_FC1";
      this.label_FC1.Size = new Size(142, 23);
      this.label_FC1.TabIndex = 26;
      this.label_FC1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_FC2.BackColor = Color.WhiteSmoke;
      this.label_FC2.BorderStyle = BorderStyle.FixedSingle;
      this.label_FC2.Location = new Point(663, 334);
      this.label_FC2.Name = "label_FC2";
      this.label_FC2.Size = new Size(142, 23);
      this.label_FC2.TabIndex = 30;
      this.label_FC2.TextAlign = ContentAlignment.MiddleCenter;
      this.dataGridView_FC1.AllowUserToAddRows = false;
      this.dataGridView_FC1.AllowUserToOrderColumns = true;
      this.dataGridView_FC1.AllowUserToResizeColumns = false;
      this.dataGridView_FC1.AllowUserToResizeRows = false;
      this.dataGridView_FC1.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_FC1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_FC1.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_FC1.BorderStyle = BorderStyle.None;
      this.dataGridView_FC1.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_FC1.ColumnHeadersVisible = false;
      this.dataGridView_FC1.Columns.AddRange((DataGridViewColumn) this.Column1, (DataGridViewColumn) this.Column2);
      gridViewCellStyle1.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle1.BackColor = Color.White;
      gridViewCellStyle1.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle1.ForeColor = SystemColors.ControlText;
      gridViewCellStyle1.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle1.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle1.WrapMode = DataGridViewTriState.False;
      this.dataGridView_FC1.DefaultCellStyle = gridViewCellStyle1;
      this.dataGridView_FC1.Location = new Point(-1, -1);
      this.dataGridView_FC1.Name = "dataGridView_FC1";
      this.dataGridView_FC1.RowHeadersVisible = false;
      this.dataGridView_FC1.RowTemplate.Height = 23;
      this.dataGridView_FC1.Size = new Size(144, 135);
      this.dataGridView_FC1.TabIndex = 27;
      this.dataGridView_FC1.RowsAdded += new DataGridViewRowsAddedEventHandler(this.dataGridView_FC1_RowsAdded);
      this.Column1.HeaderText = "";
      this.Column1.Name = "Column1";
      this.Column2.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      this.Column2.HeaderText = "";
      this.Column2.Name = "Column2";
      this.Column2.ReadOnly = true;
      this.Column2.Width = 45;
      this.label_FC3.BackColor = Color.WhiteSmoke;
      this.label_FC3.BorderStyle = BorderStyle.FixedSingle;
      this.label_FC3.Location = new Point(517, 493);
      this.label_FC3.Name = "label_FC3";
      this.label_FC3.Size = new Size(142, 23);
      this.label_FC3.TabIndex = 28;
      this.label_FC3.TextAlign = ContentAlignment.MiddleCenter;
      this.dataGridView_FC3.AllowUserToAddRows = false;
      this.dataGridView_FC3.AllowUserToOrderColumns = true;
      this.dataGridView_FC3.AllowUserToResizeColumns = false;
      this.dataGridView_FC3.AllowUserToResizeRows = false;
      this.dataGridView_FC3.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_FC3.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_FC3.BorderStyle = BorderStyle.None;
      this.dataGridView_FC3.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_FC3.ColumnHeadersVisible = false;
      this.dataGridView_FC3.Columns.AddRange((DataGridViewColumn) this.Column5, (DataGridViewColumn) this.Column7);
      gridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle2.BackColor = Color.White;
      gridViewCellStyle2.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle2.ForeColor = SystemColors.ControlText;
      gridViewCellStyle2.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle2.WrapMode = DataGridViewTriState.False;
      this.dataGridView_FC3.DefaultCellStyle = gridViewCellStyle2;
      this.dataGridView_FC3.Location = new Point(-1, -1);
      this.dataGridView_FC3.Name = "dataGridView_FC3";
      this.dataGridView_FC3.RowHeadersVisible = false;
      this.dataGridView_FC3.RowTemplate.Height = 23;
      this.dataGridView_FC3.Size = new Size(142, 25);
      this.dataGridView_FC3.TabIndex = 29;
      this.dataGridView_FC3.RowsAdded += new DataGridViewRowsAddedEventHandler(this.dataGridView_FC3_RowsAdded);
      this.Column5.HeaderText = "";
      this.Column5.Name = "Column5";
      this.Column7.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      this.Column7.HeaderText = "";
      this.Column7.Name = "Column7";
      this.Column7.ReadOnly = true;
      this.Column7.Width = 45;
      this.label_FC4.BackColor = Color.WhiteSmoke;
      this.label_FC4.BorderStyle = BorderStyle.FixedSingle;
      this.label_FC4.Location = new Point(663, 493);
      this.label_FC4.Name = "label_FC4";
      this.label_FC4.Size = new Size(142, 23);
      this.label_FC4.TabIndex = 32;
      this.label_FC4.TextAlign = ContentAlignment.MiddleCenter;
      this.label15.BackColor = Color.FromArgb(229, 238, 248);
      this.label15.BorderStyle = BorderStyle.FixedSingle;
      this.label15.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label15.ForeColor = Color.MidnightBlue;
      this.label15.Location = new Point(310, 541);
      this.label15.Name = "label15";
      this.label15.Size = new Size(495, 20);
      this.label15.TabIndex = 21;
      this.label15.Text = "V/P Switch-Over";
      this.label15.TextAlign = ContentAlignment.MiddleCenter;
      this.label16.BackColor = Color.FromArgb(229, 238, 248);
      this.label16.BorderStyle = BorderStyle.FixedSingle;
      this.label16.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label16.ForeColor = Color.MidnightBlue;
      this.label16.Location = new Point(310, 586);
      this.label16.Name = "label16";
      this.label16.Size = new Size(495, 20);
      this.label16.TabIndex = 21;
      this.label16.Text = "Pack/Holding Control";
      this.label16.TextAlign = ContentAlignment.MiddleCenter;
      this.label_PHC1.BackColor = Color.WhiteSmoke;
      this.label_PHC1.BorderStyle = BorderStyle.FixedSingle;
      this.label_PHC1.Location = new Point(517, 605);
      this.label_PHC1.Name = "label_PHC1";
      this.label_PHC1.Size = new Size(142, 23);
      this.label_PHC1.TabIndex = 37;
      this.label_PHC1.TextAlign = ContentAlignment.MiddleCenter;
      this.label_PHC2.BackColor = Color.WhiteSmoke;
      this.label_PHC2.BorderStyle = BorderStyle.FixedSingle;
      this.label_PHC2.Location = new Point(663, 605);
      this.label_PHC2.Name = "label_PHC2";
      this.label_PHC2.Size = new Size(142, 23);
      this.label_PHC2.TabIndex = 39;
      this.label_PHC2.TextAlign = ContentAlignment.MiddleCenter;
      this.panel1.BorderStyle = BorderStyle.FixedSingle;
      this.panel1.Controls.Add((Control) this.dataGridView_FC1);
      this.panel1.Location = new Point(517, 356);
      this.panel1.Name = "panel1";
      this.panel1.Size = new Size(142, 134);
      this.panel1.TabIndex = 27;
      this.panel2.BorderStyle = BorderStyle.FixedSingle;
      this.panel2.Controls.Add((Control) this.dataGridView_FC2);
      this.panel2.Location = new Point(663, 356);
      this.panel2.Name = "panel2";
      this.panel2.Size = new Size(142, 134);
      this.panel2.TabIndex = 31;
      this.dataGridView_FC2.AllowUserToAddRows = false;
      this.dataGridView_FC2.AllowUserToOrderColumns = true;
      this.dataGridView_FC2.AllowUserToResizeColumns = false;
      this.dataGridView_FC2.AllowUserToResizeRows = false;
      this.dataGridView_FC2.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_FC2.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_FC2.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_FC2.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_FC2.ColumnHeadersVisible = false;
      this.dataGridView_FC2.Columns.AddRange((DataGridViewColumn) this.dataGridViewTextBoxColumn1, (DataGridViewColumn) this.dataGridViewTextBoxColumn2);
      gridViewCellStyle3.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle3.BackColor = Color.White;
      gridViewCellStyle3.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle3.ForeColor = SystemColors.ControlText;
      gridViewCellStyle3.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle3.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle3.WrapMode = DataGridViewTriState.False;
      this.dataGridView_FC2.DefaultCellStyle = gridViewCellStyle3;
      this.dataGridView_FC2.Location = new Point(-2, -2);
      this.dataGridView_FC2.Name = "dataGridView_FC2";
      this.dataGridView_FC2.RowHeadersVisible = false;
      this.dataGridView_FC2.RowTemplate.Height = 23;
      this.dataGridView_FC2.Size = new Size(144, 135);
      this.dataGridView_FC2.TabIndex = 31;
      this.dataGridView_FC2.RowsAdded += new DataGridViewRowsAddedEventHandler(this.dataGridView_FC2_RowsAdded);
      this.dataGridViewTextBoxColumn1.HeaderText = "";
      this.dataGridViewTextBoxColumn1.Name = "dataGridViewTextBoxColumn1";
      this.dataGridViewTextBoxColumn2.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      this.dataGridViewTextBoxColumn2.HeaderText = "";
      this.dataGridViewTextBoxColumn2.Name = "dataGridViewTextBoxColumn2";
      this.dataGridViewTextBoxColumn2.ReadOnly = true;
      this.dataGridViewTextBoxColumn2.Width = 45;
      this.panel3.BorderStyle = BorderStyle.FixedSingle;
      this.panel3.Controls.Add((Control) this.dataGridView_FC3);
      this.panel3.Location = new Point(517, 515);
      this.panel3.Name = "panel3";
      this.panel3.Size = new Size(142, 24);
      this.panel3.TabIndex = 95;
      this.panel4.BorderStyle = BorderStyle.FixedSingle;
      this.panel4.Controls.Add((Control) this.dataGridView_FC4);
      this.panel4.Location = new Point(663, 515);
      this.panel4.Name = "panel4";
      this.panel4.Size = new Size(142, 24);
      this.panel4.TabIndex = 95;
      this.dataGridView_FC4.AllowUserToAddRows = false;
      this.dataGridView_FC4.AllowUserToOrderColumns = true;
      this.dataGridView_FC4.AllowUserToResizeColumns = false;
      this.dataGridView_FC4.AllowUserToResizeRows = false;
      this.dataGridView_FC4.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_FC4.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_FC4.BorderStyle = BorderStyle.None;
      this.dataGridView_FC4.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_FC4.ColumnHeadersVisible = false;
      this.dataGridView_FC4.Columns.AddRange((DataGridViewColumn) this.dataGridViewTextBoxColumn3, (DataGridViewColumn) this.dataGridViewTextBoxColumn4);
      gridViewCellStyle4.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle4.BackColor = Color.White;
      gridViewCellStyle4.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle4.ForeColor = SystemColors.ControlText;
      gridViewCellStyle4.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle4.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle4.WrapMode = DataGridViewTriState.False;
      this.dataGridView_FC4.DefaultCellStyle = gridViewCellStyle4;
      this.dataGridView_FC4.Location = new Point(-1, -1);
      this.dataGridView_FC4.Name = "dataGridView_FC4";
      this.dataGridView_FC4.RowHeadersVisible = false;
      this.dataGridView_FC4.RowTemplate.Height = 23;
      this.dataGridView_FC4.Size = new Size(142, 25);
      this.dataGridView_FC4.TabIndex = 33;
      this.dataGridView_FC4.RowsAdded += new DataGridViewRowsAddedEventHandler(this.dataGridView_FC4_RowsAdded);
      this.dataGridViewTextBoxColumn3.HeaderText = "";
      this.dataGridViewTextBoxColumn3.Name = "dataGridViewTextBoxColumn3";
      this.dataGridViewTextBoxColumn4.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      this.dataGridViewTextBoxColumn4.HeaderText = "";
      this.dataGridViewTextBoxColumn4.Name = "dataGridViewTextBoxColumn4";
      this.dataGridViewTextBoxColumn4.ReadOnly = true;
      this.dataGridViewTextBoxColumn4.Width = 45;
      this.panel5.BorderStyle = BorderStyle.FixedSingle;
      this.panel5.Controls.Add((Control) this.dataGridView_VP);
      this.panel5.Location = new Point(517, 560);
      this.panel5.Name = "panel5";
      this.panel5.Size = new Size(142, 24);
      this.panel5.TabIndex = 95;
      this.dataGridView_VP.AllowUserToAddRows = false;
      this.dataGridView_VP.AllowUserToOrderColumns = true;
      this.dataGridView_VP.AllowUserToResizeColumns = false;
      this.dataGridView_VP.AllowUserToResizeRows = false;
      this.dataGridView_VP.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_VP.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_VP.BorderStyle = BorderStyle.None;
      this.dataGridView_VP.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_VP.ColumnHeadersVisible = false;
      this.dataGridView_VP.Columns.AddRange((DataGridViewColumn) this.dataGridViewTextBoxColumn5, (DataGridViewColumn) this.dataGridViewTextBoxColumn6);
      gridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle5.BackColor = Color.White;
      gridViewCellStyle5.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle5.ForeColor = SystemColors.ControlText;
      gridViewCellStyle5.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle5.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle5.WrapMode = DataGridViewTriState.False;
      this.dataGridView_VP.DefaultCellStyle = gridViewCellStyle5;
      this.dataGridView_VP.Location = new Point(-1, -1);
      this.dataGridView_VP.Name = "dataGridView_VP";
      this.dataGridView_VP.RowHeadersVisible = false;
      this.dataGridView_VP.RowTemplate.Height = 23;
      this.dataGridView_VP.Size = new Size(142, 25);
      this.dataGridView_VP.TabIndex = 35;
      this.dataGridView_VP.RowsAdded += new DataGridViewRowsAddedEventHandler(this.dataGridView_VP_RowsAdded);
      this.dataGridViewTextBoxColumn5.HeaderText = "";
      this.dataGridViewTextBoxColumn5.Name = "dataGridViewTextBoxColumn5";
      this.dataGridViewTextBoxColumn6.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      this.dataGridViewTextBoxColumn6.HeaderText = "";
      this.dataGridViewTextBoxColumn6.Name = "dataGridViewTextBoxColumn6";
      this.dataGridViewTextBoxColumn6.ReadOnly = true;
      this.dataGridViewTextBoxColumn6.Width = 45;
      this.panel6.BorderStyle = BorderStyle.FixedSingle;
      this.panel6.Controls.Add((Control) this.dataGridView_PHC1);
      this.panel6.Location = new Point(517, 627);
      this.panel6.Name = "panel6";
      this.panel6.Size = new Size(142, 86);
      this.panel6.TabIndex = 38;
      this.dataGridView_PHC1.AllowUserToAddRows = false;
      this.dataGridView_PHC1.AllowUserToOrderColumns = true;
      this.dataGridView_PHC1.AllowUserToResizeColumns = false;
      this.dataGridView_PHC1.AllowUserToResizeRows = false;
      this.dataGridView_PHC1.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_PHC1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_PHC1.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_PHC1.BorderStyle = BorderStyle.None;
      this.dataGridView_PHC1.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_PHC1.ColumnHeadersVisible = false;
      this.dataGridView_PHC1.Columns.AddRange((DataGridViewColumn) this.dataGridViewTextBoxColumn7, (DataGridViewColumn) this.dataGridViewTextBoxColumn8);
      gridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle6.BackColor = Color.White;
      gridViewCellStyle6.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle6.ForeColor = SystemColors.ControlText;
      gridViewCellStyle6.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle6.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle6.WrapMode = DataGridViewTriState.False;
      this.dataGridView_PHC1.DefaultCellStyle = gridViewCellStyle6;
      this.dataGridView_PHC1.Location = new Point(-1, -1);
      this.dataGridView_PHC1.Name = "dataGridView_PHC1";
      this.dataGridView_PHC1.RowHeadersVisible = false;
      this.dataGridView_PHC1.RowTemplate.Height = 23;
      this.dataGridView_PHC1.Size = new Size(144, 87);
      this.dataGridView_PHC1.TabIndex = 38;
      this.dataGridView_PHC1.RowsAdded += new DataGridViewRowsAddedEventHandler(this.dataGridView_PHC1_RowsAdded);
      this.dataGridView_PHC1.UserAddedRow += new DataGridViewRowEventHandler(this.dataGridView_PHC1_UserAddedRow);
      this.dataGridViewTextBoxColumn7.HeaderText = "";
      this.dataGridViewTextBoxColumn7.Name = "dataGridViewTextBoxColumn7";
      this.dataGridViewTextBoxColumn8.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      this.dataGridViewTextBoxColumn8.HeaderText = "";
      this.dataGridViewTextBoxColumn8.Name = "dataGridViewTextBoxColumn8";
      this.dataGridViewTextBoxColumn8.ReadOnly = true;
      this.dataGridViewTextBoxColumn8.Width = 45;
      this.panel7.BorderStyle = BorderStyle.FixedSingle;
      this.panel7.Controls.Add((Control) this.dataGridView_PHC2);
      this.panel7.Location = new Point(663, 627);
      this.panel7.Name = "panel7";
      this.panel7.Size = new Size(142, 86);
      this.panel7.TabIndex = 40;
      this.dataGridView_PHC2.AllowUserToAddRows = false;
      this.dataGridView_PHC2.AllowUserToOrderColumns = true;
      this.dataGridView_PHC2.AllowUserToResizeColumns = false;
      this.dataGridView_PHC2.AllowUserToResizeRows = false;
      this.dataGridView_PHC2.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView_PHC2.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
      this.dataGridView_PHC2.BackgroundColor = Color.WhiteSmoke;
      this.dataGridView_PHC2.BorderStyle = BorderStyle.None;
      this.dataGridView_PHC2.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView_PHC2.ColumnHeadersVisible = false;
      this.dataGridView_PHC2.Columns.AddRange((DataGridViewColumn) this.dataGridViewTextBoxColumn9, (DataGridViewColumn) this.dataGridViewTextBoxColumn10);
      gridViewCellStyle7.Alignment = DataGridViewContentAlignment.MiddleCenter;
      gridViewCellStyle7.BackColor = Color.White;
      gridViewCellStyle7.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      gridViewCellStyle7.ForeColor = SystemColors.ControlText;
      gridViewCellStyle7.SelectionBackColor = SystemColors.Highlight;
      gridViewCellStyle7.SelectionForeColor = SystemColors.HighlightText;
      gridViewCellStyle7.WrapMode = DataGridViewTriState.False;
      this.dataGridView_PHC2.DefaultCellStyle = gridViewCellStyle7;
      this.dataGridView_PHC2.Location = new Point(-1, -1);
      this.dataGridView_PHC2.Name = "dataGridView_PHC2";
      this.dataGridView_PHC2.RowHeadersVisible = false;
      this.dataGridView_PHC2.RowTemplate.Height = 23;
      this.dataGridView_PHC2.Size = new Size(144, 87);
      this.dataGridView_PHC2.TabIndex = 40;
      this.dataGridView_PHC2.RowsAdded += new DataGridViewRowsAddedEventHandler(this.dataGridView_PHC2_RowsAdded);
      this.dataGridView_PHC2.UserAddedRow += new DataGridViewRowEventHandler(this.dataGridView_PHC2_UserAddedRow);
      this.dataGridViewTextBoxColumn9.HeaderText = "";
      this.dataGridViewTextBoxColumn9.Name = "dataGridViewTextBoxColumn9";
      this.dataGridViewTextBoxColumn10.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
      this.dataGridViewTextBoxColumn10.HeaderText = "";
      this.dataGridViewTextBoxColumn10.Name = "dataGridViewTextBoxColumn10";
      this.dataGridViewTextBoxColumn10.ReadOnly = true;
      this.dataGridViewTextBoxColumn10.Width = 45;
      this.newButton_Export.ButtonBackColor = Color.White;
      this.newButton_Export.ButtonText = "출력";
      this.newButton_Export.FlatBorderSize = 1;
      this.newButton_Export.FlatStyle = FlatStyle.Flat;
      this.newButton_Export.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Export.ForeColor = Color.Navy;
      this.newButton_Export.Image = (Image) Resources.Export;
      this.newButton_Export.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Export.Location = new Point(246, 610);
      this.newButton_Export.Name = "newButton_Export";
      this.newButton_Export.Size = new Size(60, 32);
      this.newButton_Export.TabIndex = 11;
      this.newButton_Export.TabStop = false;
      this.newButton_Export.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Export.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Export.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_Import.ButtonBackColor = Color.White;
      this.newButton_Import.ButtonText = "삽입";
      this.newButton_Import.FlatBorderSize = 1;
      this.newButton_Import.FlatStyle = FlatStyle.Flat;
      this.newButton_Import.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Import.ForeColor = Color.Navy;
      this.newButton_Import.Image = (Image) Resources.Import;
      this.newButton_Import.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Import.Location = new Point(187, 610);
      this.newButton_Import.Name = "newButton_Import";
      this.newButton_Import.Size = new Size(60, 32);
      this.newButton_Import.TabIndex = 10;
      this.newButton_Import.TabStop = false;
      this.newButton_Import.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Import.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Import.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_Del.ButtonBackColor = Color.White;
      this.newButton_Del.ButtonText = "삭제";
      this.newButton_Del.FlatBorderSize = 1;
      this.newButton_Del.FlatStyle = FlatStyle.Flat;
      this.newButton_Del.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Del.ForeColor = Color.Navy;
      this.newButton_Del.Image = (Image) Resources.Del;
      this.newButton_Del.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Del.Location = new Point(123, 610);
      this.newButton_Del.Name = "newButton_Del";
      this.newButton_Del.Size = new Size(60, 32);
      this.newButton_Del.TabIndex = 9;
      this.newButton_Del.TabStop = false;
      this.newButton_Del.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Del.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Del.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_Edit.ButtonBackColor = Color.White;
      this.newButton_Edit.ButtonText = "수정";
      this.newButton_Edit.FlatBorderSize = 1;
      this.newButton_Edit.FlatStyle = FlatStyle.Flat;
      this.newButton_Edit.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Edit.ForeColor = Color.Navy;
      this.newButton_Edit.Image = (Image) Resources.Edit;
      this.newButton_Edit.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Edit.Location = new Point(64, 610);
      this.newButton_Edit.Name = "newButton_Edit";
      this.newButton_Edit.Size = new Size(60, 32);
      this.newButton_Edit.TabIndex = 8;
      this.newButton_Edit.TabStop = false;
      this.newButton_Edit.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Edit.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Edit.NewClick += new EventHandler(this.newButton_NewClick);
      this.newButton_Add.ButtonBackColor = Color.White;
      this.newButton_Add.ButtonText = "추가";
      this.newButton_Add.FlatBorderSize = 1;
      this.newButton_Add.FlatStyle = FlatStyle.Flat;
      this.newButton_Add.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Add.ForeColor = Color.Navy;
      this.newButton_Add.Image = (Image) Resources.Add;
      this.newButton_Add.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Add.Location = new Point(5, 610);
      this.newButton_Add.Name = "newButton_Add";
      this.newButton_Add.Size = new Size(60, 32);
      this.newButton_Add.TabIndex = 7;
      this.newButton_Add.TabStop = false;
      this.newButton_Add.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Add.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Add.NewClick += new EventHandler(this.newButton_NewClick);
      this.unitTextBox_MoldTemp.BackColor = Color.White;
      this.unitTextBox_MoldTemp.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_MoldTemp.ControlBackColor = Color.White;
      this.unitTextBox_MoldTemp.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_MoldTemp.IsDigit = false;
      this.unitTextBox_MoldTemp.Location = new Point(557, 289);
      this.unitTextBox_MoldTemp.Name = "unitTextBox_MoldTemp";
      this.unitTextBox_MoldTemp.Size = new Size(248, 23);
      this.unitTextBox_MoldTemp.TabIndex = 24;
      this.unitTextBox_MoldTemp.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_MoldTemp.Unit = "℃";
      this.unitTextBox_MoldTemp.Value = "";
      this.unitTextBox_MetlTemp.BackColor = Color.White;
      this.unitTextBox_MetlTemp.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_MetlTemp.ControlBackColor = Color.White;
      this.unitTextBox_MetlTemp.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_MetlTemp.IsDigit = false;
      this.unitTextBox_MetlTemp.Location = new Point(310, 289);
      this.unitTextBox_MetlTemp.Name = "unitTextBox_MetlTemp";
      this.unitTextBox_MetlTemp.Size = new Size(248, 23);
      this.unitTextBox_MetlTemp.TabIndex = 23;
      this.unitTextBox_MetlTemp.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_MetlTemp.Unit = "℃";
      this.unitTextBox_MetlTemp.Value = "";
      this.unitTextBox_CTime.BackColor = Color.White;
      this.unitTextBox_CTime.BorderStyle = BorderStyle.FixedSingle;
      this.unitTextBox_CTime.ControlBackColor = Color.White;
      this.unitTextBox_CTime.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.unitTextBox_CTime.IsDigit = false;
      this.unitTextBox_CTime.Location = new Point(557, 245);
      this.unitTextBox_CTime.Name = "unitTextBox_CTime";
      this.unitTextBox_CTime.Size = new Size(248, 23);
      this.unitTextBox_CTime.TabIndex = 22;
      this.unitTextBox_CTime.TextAlign = HorizontalAlignment.Center;
      this.unitTextBox_CTime.Unit = "S";
      this.unitTextBox_CTime.Value = "";
      this.newComboBox_Sequence.BackColor = Color.White;
      this.newComboBox_Sequence.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Sequence.ComboBoxBackColor = Color.White;
      this.newComboBox_Sequence.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Sequence.isSameSelect = false;
      this.newComboBox_Sequence.Location = new Point(310, 245);
      this.newComboBox_Sequence.Name = "newComboBox_Sequence";
      this.newComboBox_Sequence.SelectedIndex = -1;
      this.newComboBox_Sequence.Size = new Size(248, 23);
      this.newComboBox_Sequence.TabIndex = 21;
      this.newComboBox_Sequence.TabStop = false;
      this.newComboBox_Sequence.TextAlign = HorizontalAlignment.Center;
      this.newComboBox_Sequence.Value = "";
      this.newComboBox_Sequence.SelectedIndexChanged += new EventHandler(this.newComboBox_Sequence_SelectedIndexChanged);
      this.newComboBox_PHC.BackColor = Color.White;
      this.newComboBox_PHC.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_PHC.ComboBoxBackColor = Color.White;
      this.newComboBox_PHC.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_PHC.isSameSelect = false;
      this.newComboBox_PHC.Location = new Point(310, 605);
      this.newComboBox_PHC.Name = "newComboBox_PHC";
      this.newComboBox_PHC.SelectedIndex = -1;
      this.newComboBox_PHC.Size = new Size(190, 23);
      this.newComboBox_PHC.TabIndex = 36;
      this.newComboBox_PHC.TabStop = false;
      this.newComboBox_PHC.TextAlign = HorizontalAlignment.Center;
      this.newComboBox_PHC.Value = "";
      this.newComboBox_PHC.SelectedIndexChanged += new EventHandler(this.newComboBox_PHC_SelectedIndexChanged);
      this.newComboBox_VP.BackColor = Color.White;
      this.newComboBox_VP.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_VP.ComboBoxBackColor = Color.White;
      this.newComboBox_VP.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_VP.isSameSelect = false;
      this.newComboBox_VP.Location = new Point(310, 560);
      this.newComboBox_VP.Name = "newComboBox_VP";
      this.newComboBox_VP.SelectedIndex = -1;
      this.newComboBox_VP.Size = new Size(190, 23);
      this.newComboBox_VP.TabIndex = 34;
      this.newComboBox_VP.TabStop = false;
      this.newComboBox_VP.TextAlign = HorizontalAlignment.Center;
      this.newComboBox_VP.Value = "";
      this.newComboBox_VP.SelectedIndexChanged += new EventHandler(this.newComboBox_VP_SelectedIndexChanged);
      this.newComboBox_FC.BackColor = Color.White;
      this.newComboBox_FC.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_FC.ComboBoxBackColor = Color.White;
      this.newComboBox_FC.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_FC.isSameSelect = false;
      this.newComboBox_FC.Location = new Point(310, 334);
      this.newComboBox_FC.Name = "newComboBox_FC";
      this.newComboBox_FC.SelectedIndex = -1;
      this.newComboBox_FC.Size = new Size(190, 23);
      this.newComboBox_FC.TabIndex = 25;
      this.newComboBox_FC.TabStop = false;
      this.newComboBox_FC.TextAlign = HorizontalAlignment.Center;
      this.newComboBox_FC.Value = "";
      this.newComboBox_FC.SelectedIndexChanged += new EventHandler(this.newComboBox_FC_SelectedIndexChanged);
      this.newButton_Mat_UDB.ButtonBackColor = Color.White;
      this.newButton_Mat_UDB.ButtonText = "사용자 UDB 삽입";
      this.newButton_Mat_UDB.FlatBorderSize = 1;
      this.newButton_Mat_UDB.FlatStyle = FlatStyle.Flat;
      this.newButton_Mat_UDB.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Mat_UDB.Image = (Image) null;
      this.newButton_Mat_UDB.ImageAlign = ContentAlignment.MiddleCenter;
      this.newButton_Mat_UDB.Location = new Point(695, 72);
      this.newButton_Mat_UDB.Name = "newButton_Mat_UDB";
      this.newButton_Mat_UDB.Size = new Size(110, 24);
      this.newButton_Mat_UDB.TabIndex = 15;
      this.newButton_Mat_UDB.TabStop = false;
      this.newButton_Mat_UDB.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Mat_UDB.TextImageRelocation = TextImageRelation.Overlay;
      this.newButton_Mat_UDB.NewClick += new EventHandler(this.newButton_Mat_UDB_NewClick);
      this.newTextBox_Mat_FamilyAbbreviation.BackColor = Color.White;
      this.newTextBox_Mat_FamilyAbbreviation.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Mat_FamilyAbbreviation.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Mat_FamilyAbbreviation.IsDigit = false;
      this.newTextBox_Mat_FamilyAbbreviation.Lines = new string[0];
      this.newTextBox_Mat_FamilyAbbreviation.Location = new Point(310, 201);
      this.newTextBox_Mat_FamilyAbbreviation.MultiLine = false;
      this.newTextBox_Mat_FamilyAbbreviation.Name = "newTextBox_Mat_FamilyAbbreviation";
      this.newTextBox_Mat_FamilyAbbreviation.ReadOnly = false;
      this.newTextBox_Mat_FamilyAbbreviation.Size = new Size(248, 23);
      this.newTextBox_Mat_FamilyAbbreviation.TabIndex = 19;
      this.newTextBox_Mat_FamilyAbbreviation.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Mat_FamilyAbbreviation.TextBoxBackColor = Color.White;
      this.newTextBox_Mat_FamilyAbbreviation.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Mat_FamilyAbbreviation.Value = "";
      this.newTextBox_Mat_UDB.BackColor = Color.White;
      this.newTextBox_Mat_UDB.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Mat_UDB.Enabled = false;
      this.newTextBox_Mat_UDB.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Mat_UDB.IsDigit = false;
      this.newTextBox_Mat_UDB.Lines = new string[0];
      this.newTextBox_Mat_UDB.Location = new Point(310, 72);
      this.newTextBox_Mat_UDB.MultiLine = false;
      this.newTextBox_Mat_UDB.Name = "newTextBox_Mat_UDB";
      this.newTextBox_Mat_UDB.ReadOnly = true;
      this.newTextBox_Mat_UDB.Size = new Size(386, 23);
      this.newTextBox_Mat_UDB.TabIndex = 14;
      this.newTextBox_Mat_UDB.TabStop = false;
      this.newTextBox_Mat_UDB.TextAlign = HorizontalAlignment.Left;
      this.newTextBox_Mat_UDB.TextBoxBackColor = Color.White;
      this.newTextBox_Mat_UDB.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Mat_UDB.Value = "";
      this.newTextBox_Option.BackColor = Color.White;
      this.newTextBox_Option.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Option.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Option.IsDigit = false;
      this.newTextBox_Option.Lines = new string[0];
      this.newTextBox_Option.Location = new Point(205, 588);
      this.newTextBox_Option.MultiLine = false;
      this.newTextBox_Option.Name = "newTextBox_Option";
      this.newTextBox_Option.ReadOnly = false;
      this.newTextBox_Option.Size = new Size(101, 23);
      this.newTextBox_Option.TabIndex = 6;
      this.newTextBox_Option.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Option.TextBoxBackColor = Color.White;
      this.newTextBox_Option.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Option.Value = "";
      this.newTextBox_Item.BackColor = Color.White;
      this.newTextBox_Item.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Item.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Item.IsDigit = false;
      this.newTextBox_Item.Lines = new string[0];
      this.newTextBox_Item.Location = new Point(105, 588);
      this.newTextBox_Item.MultiLine = false;
      this.newTextBox_Item.Name = "newTextBox_Item";
      this.newTextBox_Item.ReadOnly = false;
      this.newTextBox_Item.Size = new Size(101, 23);
      this.newTextBox_Item.TabIndex = 5;
      this.newTextBox_Item.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Item.TextBoxBackColor = Color.White;
      this.newTextBox_Item.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Item.Value = "";
      this.newTextBox_Company.BackColor = Color.White;
      this.newTextBox_Company.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Company.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Company.IsDigit = false;
      this.newTextBox_Company.Lines = new string[0];
      this.newTextBox_Company.Location = new Point(5, 588);
      this.newTextBox_Company.MultiLine = false;
      this.newTextBox_Company.Name = "newTextBox_Company";
      this.newTextBox_Company.ReadOnly = false;
      this.newTextBox_Company.Size = new Size(101, 23);
      this.newTextBox_Company.TabIndex = 4;
      this.newTextBox_Company.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Company.TextBoxBackColor = Color.White;
      this.newTextBox_Company.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Company.Value = "";
      this.newComboBox_Item.BackColor = Color.White;
      this.newComboBox_Item.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Item.ComboBoxBackColor = Color.White;
      this.newComboBox_Item.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Item.isSameSelect = false;
      this.newComboBox_Item.Location = new Point(155, 46);
      this.newComboBox_Item.Name = "newComboBox_Item";
      this.newComboBox_Item.SelectedIndex = -1;
      this.newComboBox_Item.Size = new Size(151, 23);
      this.newComboBox_Item.TabIndex = 2;
      this.newComboBox_Item.TabStop = false;
      this.newComboBox_Item.TextAlign = HorizontalAlignment.Center;
      this.newComboBox_Item.Value = "";
      this.newComboBox_Item.SelectedIndexChanged += new EventHandler(this.newComboBox_Item_SelectedIndexChanged);
      this.newComboBox_Company.BackColor = Color.White;
      this.newComboBox_Company.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Company.ComboBoxBackColor = Color.White;
      this.newComboBox_Company.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Company.isSameSelect = false;
      this.newComboBox_Company.Location = new Point(5, 46);
      this.newComboBox_Company.Name = "newComboBox_Company";
      this.newComboBox_Company.SelectedIndex = -1;
      this.newComboBox_Company.Size = new Size(151, 23);
      this.newComboBox_Company.TabIndex = 1;
      this.newComboBox_Company.TabStop = false;
      this.newComboBox_Company.TextAlign = HorizontalAlignment.Center;
      this.newComboBox_Company.Value = "";
      this.newComboBox_Company.SelectedIndexChanged += new EventHandler(this.newComboBox_Company_SelectedIndexChanged);
      this.panel8.BorderStyle = BorderStyle.FixedSingle;
      this.panel8.Controls.Add((Control) this.radioButton_Mat_User);
      this.panel8.Controls.Add((Control) this.radioButton_Mat_System);
      this.panel8.Location = new Point(310, 24);
      this.panel8.Name = "panel8";
      this.panel8.Size = new Size(495, 30);
      this.panel8.TabIndex = 101;
      this.radioButton_Mat_User.Location = new Point(73, 6);
      this.radioButton_Mat_User.Name = "radioButton_Mat_User";
      this.radioButton_Mat_User.Size = new Size(61, 18);
      this.radioButton_Mat_User.TabIndex = 13;
      this.radioButton_Mat_User.Text = "사용자";
      this.radioButton_Mat_User.UseVisualStyleBackColor = true;
      this.radioButton_Mat_User.CheckedChanged += new EventHandler(this.radioButton_Mat_User_CheckedChanged);
      this.radioButton_Mat_System.Location = new Point(6, 6);
      this.radioButton_Mat_System.Name = "radioButton_Mat_System";
      this.radioButton_Mat_System.Size = new Size(61, 18);
      this.radioButton_Mat_System.TabIndex = 12;
      this.radioButton_Mat_System.Text = "시스템";
      this.radioButton_Mat_System.UseVisualStyleBackColor = true;
      this.radioButton_Mat_System.CheckedChanged += new EventHandler(this.radioButton_Mat_System_CheckedChanged);
      this.label_Mat_Material.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Mat_Material.BorderStyle = BorderStyle.FixedSingle;
      this.label_Mat_Material.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Mat_Material.ForeColor = Color.MidnightBlue;
      this.label_Mat_Material.Location = new Point(310, 5);
      this.label_Mat_Material.Name = "label_Mat_Material";
      this.label_Mat_Material.Size = new Size(495, 20);
      this.label_Mat_Material.TabIndex = 97;
      this.label_Mat_Material.Text = "재질 정보";
      this.label_Mat_Material.TextAlign = ContentAlignment.MiddleLeft;
      this.label_Suji_SearchCond.BackColor = Color.FromArgb(229, 238, 248);
      this.label_Suji_SearchCond.BorderStyle = BorderStyle.FixedSingle;
      this.label_Suji_SearchCond.Font = new Font("Segoe UI", 9f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
      this.label_Suji_SearchCond.ForeColor = Color.MidnightBlue;
      this.label_Suji_SearchCond.Location = new Point(310, 94);
      this.label_Suji_SearchCond.Name = "label_Suji_SearchCond";
      this.label_Suji_SearchCond.Size = new Size(495, 20);
      this.label_Suji_SearchCond.TabIndex = 98;
      this.label_Suji_SearchCond.Text = "검색 조건";
      this.label_Suji_SearchCond.TextAlign = ContentAlignment.MiddleLeft;
      this.label12.BackColor = Color.Lavender;
      this.label12.BorderStyle = BorderStyle.FixedSingle;
      this.label12.Location = new Point(310, 113);
      this.label12.Name = "label12";
      this.label12.Size = new Size(100, 23);
      this.label12.TabIndex = 99;
      this.label12.Text = "Trade Name";
      this.label12.TextAlign = ContentAlignment.MiddleCenter;
      this.newTextBox_Mat_Search.BackColor = SystemColors.Window;
      this.newTextBox_Mat_Search.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Mat_Search.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Mat_Search.IsDigit = false;
      this.newTextBox_Mat_Search.Lines = new string[0];
      this.newTextBox_Mat_Search.Location = new Point(409, 113);
      this.newTextBox_Mat_Search.MultiLine = false;
      this.newTextBox_Mat_Search.Name = "newTextBox_Mat_Search";
      this.newTextBox_Mat_Search.ReadOnly = false;
      this.newTextBox_Mat_Search.Size = new Size(396, 23);
      this.newTextBox_Mat_Search.TabIndex = 16;
      this.newTextBox_Mat_Search.TextAlign = HorizontalAlignment.Left;
      this.newTextBox_Mat_Search.TextBoxBackColor = SystemColors.Window;
      this.newTextBox_Mat_Search.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Mat_Search.Value = "";
      this.newTextBox_Mat_Search.TextBoxKeyUp += new KeyEventHandler(this.newTextBox_Mat_Search_TextBoxKeyUp);
      this.newComboBox_Mat_Manufacturer.BackColor = Color.White;
      this.newComboBox_Mat_Manufacturer.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Mat_Manufacturer.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_Mat_Manufacturer.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Mat_Manufacturer.isSameSelect = false;
      this.newComboBox_Mat_Manufacturer.Location = new Point(310, 157);
      this.newComboBox_Mat_Manufacturer.Name = "newComboBox_Mat_Manufacturer";
      this.newComboBox_Mat_Manufacturer.SelectedIndex = -1;
      this.newComboBox_Mat_Manufacturer.Size = new Size(248, 23);
      this.newComboBox_Mat_Manufacturer.TabIndex = 17;
      this.newComboBox_Mat_Manufacturer.TabStop = false;
      this.newComboBox_Mat_Manufacturer.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Mat_Manufacturer.Value = "";
      this.newComboBox_Mat_Manufacturer.SelectedIndexChanged += new EventHandler(this.newComboBox_Mat_Manufacturer_SelectedIndexChanged);
      this.newComboBox_Mat_TradeName.BackColor = Color.White;
      this.newComboBox_Mat_TradeName.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Mat_TradeName.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_Mat_TradeName.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Mat_TradeName.isSameSelect = false;
      this.newComboBox_Mat_TradeName.Location = new Point(557, 157);
      this.newComboBox_Mat_TradeName.Name = "newComboBox_Mat_TradeName";
      this.newComboBox_Mat_TradeName.SelectedIndex = -1;
      this.newComboBox_Mat_TradeName.Size = new Size(248, 23);
      this.newComboBox_Mat_TradeName.TabIndex = 18;
      this.newComboBox_Mat_TradeName.TabStop = false;
      this.newComboBox_Mat_TradeName.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Mat_TradeName.Value = "";
      this.newComboBox_Mat_TradeName.SelectedIndexChanged += new EventHandler(this.newComboBox_Mat_TradeName_SelectedIndexChanged);
      this.newTextBox_Mat_MaterialID.BackColor = Color.White;
      this.newTextBox_Mat_MaterialID.BorderStyle = BorderStyle.FixedSingle;
      this.newTextBox_Mat_MaterialID.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newTextBox_Mat_MaterialID.IsDigit = false;
      this.newTextBox_Mat_MaterialID.Lines = new string[0];
      this.newTextBox_Mat_MaterialID.Location = new Point(557, 201);
      this.newTextBox_Mat_MaterialID.MultiLine = false;
      this.newTextBox_Mat_MaterialID.Name = "newTextBox_Mat_MaterialID";
      this.newTextBox_Mat_MaterialID.ReadOnly = false;
      this.newTextBox_Mat_MaterialID.Size = new Size(248, 23);
      this.newTextBox_Mat_MaterialID.TabIndex = 20;
      this.newTextBox_Mat_MaterialID.TextAlign = HorizontalAlignment.Center;
      this.newTextBox_Mat_MaterialID.TextBoxBackColor = Color.White;
      this.newTextBox_Mat_MaterialID.TextForeColor = SystemColors.WindowText;
      this.newTextBox_Mat_MaterialID.Value = "";
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(809, 717);
      this.Controls.Add((Control) this.newComboBox_Mat_TradeName);
      this.Controls.Add((Control) this.newComboBox_Mat_Manufacturer);
      this.Controls.Add((Control) this.panel8);
      this.Controls.Add((Control) this.label_Mat_Material);
      this.Controls.Add((Control) this.label_Suji_SearchCond);
      this.Controls.Add((Control) this.label12);
      this.Controls.Add((Control) this.newTextBox_Mat_Search);
      this.Controls.Add((Control) this.newButton_Export);
      this.Controls.Add((Control) this.newButton_Import);
      this.Controls.Add((Control) this.newButton_Del);
      this.Controls.Add((Control) this.newButton_Edit);
      this.Controls.Add((Control) this.newButton_Add);
      this.Controls.Add((Control) this.panel4);
      this.Controls.Add((Control) this.panel5);
      this.Controls.Add((Control) this.panel3);
      this.Controls.Add((Control) this.panel2);
      this.Controls.Add((Control) this.panel7);
      this.Controls.Add((Control) this.panel6);
      this.Controls.Add((Control) this.panel1);
      this.Controls.Add((Control) this.label16);
      this.Controls.Add((Control) this.unitTextBox_MoldTemp);
      this.Controls.Add((Control) this.unitTextBox_MetlTemp);
      this.Controls.Add((Control) this.unitTextBox_CTime);
      this.Controls.Add((Control) this.newComboBox_Sequence);
      this.Controls.Add((Control) this.newComboBox_PHC);
      this.Controls.Add((Control) this.newComboBox_VP);
      this.Controls.Add((Control) this.newComboBox_FC);
      this.Controls.Add((Control) this.newTextBox_Mat_MaterialID);
      this.Controls.Add((Control) this.newTextBox_Mat_FamilyAbbreviation);
      this.Controls.Add((Control) this.newTextBox_Mat_UDB);
      this.Controls.Add((Control) this.newTextBox_Option);
      this.Controls.Add((Control) this.newTextBox_Item);
      this.Controls.Add((Control) this.newTextBox_Company);
      this.Controls.Add((Control) this.newComboBox_Item);
      this.Controls.Add((Control) this.newComboBox_Company);
      this.Controls.Add((Control) this.label_DB_Opt_W);
      this.Controls.Add((Control) this.label_DB_Item_W);
      this.Controls.Add((Control) this.label_DB_Item);
      this.Controls.Add((Control) this.label_DB_Company_W);
      this.Controls.Add((Control) this.label_DB_Company);
      this.Controls.Add((Control) this.label10);
      this.Controls.Add((Control) this.label_PHC2);
      this.Controls.Add((Control) this.label9);
      this.Controls.Add((Control) this.label_FC2);
      this.Controls.Add((Control) this.label_FC4);
      this.Controls.Add((Control) this.label_PHC1);
      this.Controls.Add((Control) this.label_FC3);
      this.Controls.Add((Control) this.label_FC1);
      this.Controls.Add((Control) this.label_Mat_MaterialID);
      this.Controls.Add((Control) this.label_Mat_CTime);
      this.Controls.Add((Control) this.label_Mat_FamilyAbbreviation);
      this.Controls.Add((Control) this.label_Mat_Sequence);
      this.Controls.Add((Control) this.label_Mat_TradeName);
      this.Controls.Add((Control) this.label_Mat_Manufacturer);
      this.Controls.Add((Control) this.listBox_DB);
      this.Controls.Add((Control) this.label15);
      this.Controls.Add((Control) this.label4);
      this.Controls.Add((Control) this.label_Suji);
      this.Controls.Add((Control) this.label_DB_List);
      this.Controls.Add((Control) this.newButton_Mat_UDB);
      this.DoubleBuffered = true;
      this.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmProcessDB);
      this.ShowInTaskbar = false;
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "프로세스 세팅 DB";
      this.Load += new EventHandler(this.frmProcessDB_Load);
      this.KeyDown += new KeyEventHandler(this.frmProcessDB_KeyDown);
      ((ISupportInitialize) this.dataGridView_FC1).EndInit();
      ((ISupportInitialize) this.dataGridView_FC3).EndInit();
      this.panel1.ResumeLayout(false);
      this.panel2.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_FC2).EndInit();
      this.panel3.ResumeLayout(false);
      this.panel4.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_FC4).EndInit();
      this.panel5.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_VP).EndInit();
      this.panel6.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_PHC1).EndInit();
      this.panel7.ResumeLayout(false);
      ((ISupportInitialize) this.dataGridView_PHC2).EndInit();
      this.panel8.ResumeLayout(false);
      this.ResumeLayout(false);
    }
  }
}
