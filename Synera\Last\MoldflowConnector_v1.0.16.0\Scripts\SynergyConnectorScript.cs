// Este script C# se conectará a una instancia existente de Autodesk Moldflow Synergy a través de COM.
// Asegúrate de que Moldflow Synergy esté abierto antes de ejecutar este script.

using System;
using System.Runtime.InteropServices;
using System.Diagnostics;
using System.IO;
using System.Threading;
using Microsoft.VisualBasic;

public class SynergyConnector
{
    [DllImport("ole32.dll")]
    public static extern int GetActiveObject(ref Guid rclsid, IntPtr pvReserved, out object ppunk);

    [DllImport("oleaut32.dll")]
    public static extern int Release(IntPtr pv);

    public static object GetSynergyApplication(string scriptPath, string tempFilePath)
    {
        // Ejecutar el script VBScript para iniciar Moldflow y obtener el ID de instancia.
        ProcessStartInfo startInfo = new ProcessStartInfo();
        startInfo.FileName = "wscript.exe"; // O cscript.exe
        startInfo.Arguments = string.Format("\"{0}\"", scriptPath);
        startInfo.UseShellExecute = false;
        startInfo.RedirectStandardOutput = true;
        startInfo.CreateNoWindow = true;

        try
        {
            using (Process process = Process.Start(startInfo))
            {
                // Esperar a que el script VBScript se ejecute y cree el archivo temporal, con un tiempo de espera.
                int maxWaitTimeMs = 40000; // 40 segundos
                int waitIntervalMs = 100; // Verificar cada 100 ms
                int elapsedWaitTimeMs = 0;

                while (!File.Exists(tempFilePath) && elapsedWaitTimeMs < maxWaitTimeMs)
                {
                    Thread.Sleep(waitIntervalMs);
                    elapsedWaitTimeMs += waitIntervalMs;
                }

                if (File.Exists(tempFilePath))
                {
                    string instanceId = File.ReadAllText(tempFilePath).Trim();
                    File.Delete(tempFilePath); // Limpiar el archivo temporal

                    if (!string.IsNullOrEmpty(instanceId))
                    {
                        Console.WriteLine("ID de instancia de Moldflow obtenido: " + instanceId);
                        try
                        {
                            // Intentar obtener el objeto COM usando el ID de instancia
                            // El script VBScript ya inicia Synergy y escribe el SAInstance ID.
                            // Usamos GetObject para conectar a esa instancia.
                            object synergyApp = Microsoft.VisualBasic.Interaction.GetObject(instanceId);
                            Console.WriteLine("Conexión a Moldflow Synergy exitosa.");
                            return synergyApp;
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine(string.Format("Error al conectar a la instancia de Moldflow Synergy con ID '{0}': {1}", instanceId, ex.Message));
                            return null;
                        }
                    }
                    else
                    {
                        Console.WriteLine("Error: El archivo temporal no contiene un ID de instancia válido.");
                        return null;
                    }
                }
                else
                {
                    Console.WriteLine("Error: El archivo temporal InstID.tmp no fue creado por el script VBScript.");
                    return null;
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine(string.Format("Ocurrió un error al ejecutar el script VBScript: {0}", ex.Message));
            return null;
        }
    }

    public static void Main(string[] args)
    {
        string scriptPath = @"c:\Moldflow\Synera\Last\MoldflowConnector_v1.0.16.0\Scripts\mfcom__start_mf.vbs";
        string tempFilePath = Path.Combine(Path.GetTempPath(), "InstID.tmp");

        object synergyApplication = GetSynergyApplication(scriptPath, tempFilePath);

        if (synergyApplication != null)
        {
            // Aquí puedes interactuar con el objeto COM de Synergy.
            // La interacción directa con objetos COM dinámicos puede ser compleja sin las interfaces de interop.
            // Para una interacción más robusta, necesitarías generar o referenciar los ensamblados de interop de Moldflow.
            // Como ejemplo simple, intentaremos acceder a una propiedad común si es posible.
            try
            {
                // Nota: Acceder a miembros COM dinámicamente requiere System.Dynamic y Microsoft.CSharp
                // Para este script simple, asumimos que el objeto devuelto tiene miembros accesibles.
                // En un proyecto real, usarías interfaces de interop.

                // Ejemplo (puede requerir ajustes o ensamblados de interop):
                // dynamic synergy = synergyApplication;
                // Console.WriteLine(string.Format("Versión de Synergy: {0}", synergy.Application.Version));

                Console.WriteLine("Objeto de aplicación de Synergy obtenido. Puedes agregar lógica para interactuar con él aquí.");

                // Liberar el objeto COM cuando hayas terminado
                // Marshal.ReleaseComObject(synergyApplication);
                // Console.WriteLine("Objeto COM de Synergy liberado.");
            }
            catch (Exception ex)
            {
                Console.WriteLine(string.Format("Error al interactuar con el objeto Synergy: {0}", ex.Message));
            }
        }

        Console.WriteLine("Presiona cualquier tecla para salir.");
        Console.ReadKey();
    }
}