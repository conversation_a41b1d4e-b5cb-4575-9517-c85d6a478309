﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFrameworks>netcoreapp3.1;net47</TargetFrameworks>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <AssemblyTitle>Benchmark</AssemblyTitle>
    <Product>Benchmark</Product>
    <Copyright>Copyright © 2020</Copyright>
    <OutputPath>bin\$(Configuration)\</OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\SevenZipExtractor\SevenZipExtractor.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="BenchmarkDotNet" Version="0.12.0" />
  </ItemGroup>
  <ItemGroup>
    <None Update="Resources\7z.7z">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>