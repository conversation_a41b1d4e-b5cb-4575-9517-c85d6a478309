﻿// Decompiled with JetBrains decompiler
// Type: Microsoft.Office.Interop.PowerPoint.PpSaveAsFileType
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

namespace Microsoft.Office.Interop.PowerPoint
{
  [CompilerGenerated]
  [Guid("01F8F37D-78D4-4920-B2A2-227B23A7ED66")]
  [TypeIdentifier("91493440-5a91-11cf-8700-00aa0060263b", "Microsoft.Office.Interop.PowerPoint.PpSaveAsFileType")]
  public enum PpSaveAsFileType
  {
    ppSaveAsPresentation = 1,
    ppSaveAsPowerPoint7 = 2,
    ppSaveAsPowerPoint4 = 3,
    ppSaveAsPowerPoint3 = 4,
    ppSaveAsTemplate = 5,
    ppSaveAsRTF = 6,
    ppSaveAsShow = 7,
    ppSaveAsAddIn = 8,
    ppSaveAsPowerPoint4FarEast = 10, // 0x0000000A
    ppSaveAsDefault = 11, // 0x0000000B
    ppSaveAsHTML = 12, // 0x0000000C
    ppSaveAsHTMLv3 = 13, // 0x0000000D
    ppSaveAsHTMLDual = 14, // 0x0000000E
    ppSaveAsMetaFile = 15, // 0x0000000F
    ppSaveAsGIF = 16, // 0x00000010
    ppSaveAsJPG = 17, // 0x00000011
    ppSaveAsPNG = 18, // 0x00000012
    ppSaveAsBMP = 19, // 0x00000013
    ppSaveAsWebArchive = 20, // 0x00000014
    ppSaveAsTIF = 21, // 0x00000015
    ppSaveAsPresForReview = 22, // 0x00000016
    ppSaveAsEMF = 23, // 0x00000017
    ppSaveAsOpenXMLPresentation = 24, // 0x00000018
    ppSaveAsOpenXMLPresentationMacroEnabled = 25, // 0x00000019
    ppSaveAsOpenXMLTemplate = 26, // 0x0000001A
    ppSaveAsOpenXMLTemplateMacroEnabled = 27, // 0x0000001B
    ppSaveAsOpenXMLShow = 28, // 0x0000001C
    ppSaveAsOpenXMLShowMacroEnabled = 29, // 0x0000001D
    ppSaveAsOpenXMLAddin = 30, // 0x0000001E
    ppSaveAsOpenXMLTheme = 31, // 0x0000001F
    ppSaveAsPDF = 32, // 0x00000020
    ppSaveAsXPS = 33, // 0x00000021
    ppSaveAsXMLPresentation = 34, // 0x00000022
    ppSaveAsOpenDocumentPresentation = 35, // 0x00000023
    ppSaveAsOpenXMLPicturePresentation = 36, // 0x00000024
    ppSaveAsWMV = 37, // 0x00000025
    ppSaveAsStrictOpenXMLPresentation = 38, // 0x00000026
    ppSaveAsMP4 = 39, // 0x00000027
    ppSaveAsExternalConverter = 64000, // 0x0000FA00
  }
}
