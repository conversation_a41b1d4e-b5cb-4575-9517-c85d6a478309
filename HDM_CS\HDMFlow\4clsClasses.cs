﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.DataGridViewDisableButtonCell
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using System.Drawing;
using System.Windows.Forms;
using System.Windows.Forms.VisualStyles;

namespace HDMoldFlow
{
  public class DataGridViewDisableButtonCell : DataGridViewButtonCell
  {
    private bool enabledValue;

    public bool Enabled
    {
      get => this.enabledValue;
      set => this.enabledValue = value;
    }

    public override object Clone()
    {
      DataGridViewDisableButtonCell disableButtonCell = (DataGridViewDisableButtonCell) base.Clone();
      disableButtonCell.Enabled = this.Enabled;
      return (object) disableButtonCell;
    }

    public DataGridViewDisableButtonCell() => this.enabledValue = true;

    protected override void Paint(
      Graphics graphics,
      Rectangle clipBounds,
      Rectangle cellBounds,
      int rowIndex,
      DataGridViewElementStates elementState,
      object value,
      object formattedValue,
      string errorText,
      DataGridViewCellStyle cellStyle,
      DataGridViewAdvancedBorderStyle advancedBorderStyle,
      DataGridViewPaintParts paintParts)
    {
      if (!this.enabledValue)
      {
        if ((paintParts & DataGridViewPaintParts.Background) == DataGridViewPaintParts.Background)
        {
          SolidBrush solidBrush = new SolidBrush(cellStyle.BackColor);
          graphics.FillRectangle((Brush) solidBrush, cellBounds);
          solidBrush.Dispose();
        }
        if ((paintParts & DataGridViewPaintParts.Border) == DataGridViewPaintParts.Border)
          this.PaintBorder(graphics, clipBounds, cellBounds, cellStyle, advancedBorderStyle);
        Rectangle bounds = cellBounds;
        Rectangle rectangle = this.BorderWidths(advancedBorderStyle);
        bounds.X += rectangle.X;
        bounds.Y += rectangle.Y;
        bounds.Height -= rectangle.Height;
        bounds.Width -= rectangle.Width;
        ButtonRenderer.DrawButton(graphics, bounds, PushButtonState.Disabled);
        if (!(this.FormattedValue is string))
          return;
        TextRenderer.DrawText((IDeviceContext) graphics, (string) this.FormattedValue, new Font("Segoe UI", 9f, FontStyle.Regular), bounds, SystemColors.GrayText);
      }
      else
        base.Paint(graphics, clipBounds, cellBounds, rowIndex, elementState, value, formattedValue, errorText, cellStyle, advancedBorderStyle, paintParts);
    }
  }
}
