﻿// Decompiled with JetBrains decompiler
// Type: HDMFAI.CenterWinDialog
// Assembly: HDMFAI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: A66A47CE-D63B-4ACB-B9A4-A4F118252C4E
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFAI.dll

using System;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;

namespace HDMFAI
{
  internal class CenterWinDialog : IDisposable
  {
    private int mTries;
    private Form mOwner;

    [DllImport("user32.dll")]
    private static extern bool EnumThreadWindows(
      int tid,
      CenterWinDialog.EnumThreadWndProc callback,
      IntPtr lp);

    [DllImport("kernel32.dll")]
    private static extern int GetCurrentThreadId();

    [DllImport("user32.dll")]
    private static extern int GetClassName(IntPtr hWnd, StringBuilder buffer, int buflen);

    [DllImport("user32.dll")]
    private static extern bool GetWindowRect(IntPtr hWnd, out CenterWinDialog.RECT rc);

    [DllImport("user32.dll")]
    private static extern bool MoveWindow(IntPtr hWnd, int x, int y, int w, int h, bool repaint);

    public CenterWinDialog(Form owner)
    {
      this.mOwner = owner;
      owner.BeginInvoke((Delegate) new MethodInvoker(this.findDialog));
    }

    private void findDialog()
    {
      if (this.mTries < 0)
        return;
      CenterWinDialog.EnumThreadWndProc callback = new CenterWinDialog.EnumThreadWndProc(this.checkWindow);
      if (!CenterWinDialog.EnumThreadWindows(CenterWinDialog.GetCurrentThreadId(), callback, IntPtr.Zero) || ++this.mTries >= 10)
        return;
      this.mOwner.BeginInvoke((Delegate) new MethodInvoker(this.findDialog));
    }

    private bool checkWindow(IntPtr hWnd, IntPtr lp)
    {
      StringBuilder buffer = new StringBuilder(260);
      CenterWinDialog.GetClassName(hWnd, buffer, buffer.Capacity);
      if (buffer.ToString() != "#32770")
        return true;
      Rectangle rectangle = new Rectangle(this.mOwner.Location, this.mOwner.Size);
      CenterWinDialog.RECT rc;
      CenterWinDialog.GetWindowRect(hWnd, out rc);
      CenterWinDialog.MoveWindow(hWnd, rectangle.Left + (rectangle.Width - rc.Right + rc.Left) / 2, rectangle.Top + (rectangle.Height - rc.Bottom + rc.Top) / 2, rc.Right - rc.Left, rc.Bottom - rc.Top, true);
      return false;
    }

    public void Dispose() => this.mTries = -1;

    private delegate bool EnumThreadWndProc(IntPtr hWnd, IntPtr lp);

    private struct RECT
    {
      public int Left;
      public int Top;
      public int Right;
      public int Bottom;
    }
  }
}
