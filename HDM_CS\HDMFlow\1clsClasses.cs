﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.ControlExtensions
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using System;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public static class ControlExtensions
  {
    public static void InvokeIFNeeded(this Control p_Control, Action p_Action)
    {
      if (p_Control.InvokeRequired)
        p_Control.Invoke((Delegate) p_Action);
      else
        p_Action();
    }
  }
}
