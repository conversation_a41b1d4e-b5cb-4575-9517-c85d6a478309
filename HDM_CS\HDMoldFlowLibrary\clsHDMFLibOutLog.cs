﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlowLibrary.clsHDMFLibOutLog
// Assembly: HDMoldFlowLibrary, Version=2.24.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 16E5D508-B892-4B5D-A02C-2C1930D27867
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMoldFlowLibrary.dll

using HDLog4Net;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;

#nullable disable
namespace HDMoldFlowLibrary
{
  [StandardModule]
  public sealed class clsHDMFLibOutLog
  {
    public static string GetMeshStatus(string p_strMeshFile)
    {
      string str = string.Empty;
      string meshStatus;
      try
      {
        if (!File.Exists(p_strMeshFile))
        {
          meshStatus = str;
          goto label_11;
        }
        else
        {
          string[] strArray = File.ReadAllLines(p_strMeshFile);
          if (Array.IndexOf<string>(strArray, "1910007") > -1)
            str = "COMPLETE";
          else if (Array.IndexOf<string>(strArray, "1900303") > -1)
          {
            str = "FAILED";
          }
          else
          {
            int p_strDataIndex = Array.LastIndexOf<string>(strArray, "1910005");
            if (p_strDataIndex > -1)
              str = "INPROGRESS(" + clsHDMFLibOutLog.GetDataFromOutData(p_strDataIndex, ((IEnumerable<string>) strArray).ToList<string>()) + ")";
          }
        }
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLibOutLog]GetMeshStatus):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      meshStatus = str;
label_11:
      return meshStatus;
    }

    public static List<string> GetLogDataFromOutFiles(string p_strPath, string p_strStudy)
    {
      // ISSUE: variable of a compiler-generated type
      clsHDMFLibOutLog._Closure\u0024__1\u002D0 closure10_1;
      // ISSUE: object of a compiler-generated type is created
      // ISSUE: variable of a compiler-generated type
      clsHDMFLibOutLog._Closure\u0024__1\u002D0 closure10_2 = new clsHDMFLibOutLog._Closure\u0024__1\u002D0(closure10_1);
      // ISSUE: reference to a compiler-generated field
      closure10_2.\u0024VB\u0024Local_p_strStudy = p_strStudy;
      bool flag1 = false;
      bool flag2 = false;
      bool flag3 = false;
      List<string> dataFromOutFiles = new List<string>();
      // ISSUE: reference to a compiler-generated method
      string[] array1 = ((IEnumerable<string>) Directory.GetFiles(p_strPath)).Where<string>(new System.Func<string, bool>(closure10_2._Lambda\u0024__0)).ToArray<string>();
      System.Func<string, int> keySelector;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I1\u002D1 != null)
      {
        // ISSUE: reference to a compiler-generated field
        keySelector = clsHDMFLibOutLog._Closure\u0024__.\u0024I1\u002D1;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I1\u002D1 = keySelector = (System.Func<string, int>) ([SpecialName] (Temp) => clsHDMFLibUtil.ConvertToInt32(Path.GetFileNameWithoutExtension(Temp).Split('~')[1]));
      }
      string[] array2 = ((IEnumerable<string>) array1).OrderBy<string, int>(keySelector).ToArray<string>();
      int num = 0;
      string[] strArray1 = array2;
      int index = 0;
      while (index < strArray1.Length)
      {
        string path = strArray1[index];
        bool flag4 = false;
        if (num <= 2)
        {
          string[] strArray2 = File.ReadAllLines(path);
          if (Array.IndexOf<string>(strArray2, "700000") > -1 & !flag1)
          {
            flag4 = true;
            flag1 = true;
          }
          else if ((Array.IndexOf<string>(strArray2, "304134") > -1 | Array.IndexOf<string>(strArray2, "90017") > -1) & !flag2)
          {
            flag4 = true;
            flag2 = true;
          }
          else if (Array.IndexOf<string>(strArray2, "200001") > -1 & !flag3)
          {
            flag4 = true;
            flag3 = true;
          }
          if (flag4)
            dataFromOutFiles.AddRange((IEnumerable<string>) strArray2);
          checked { ++num; }
          checked { ++index; }
        }
        else
          break;
      }
      return dataFromOutFiles;
    }

    public static string GetDataFromOutData(
      string p_strDataID,
      List<string> p_lst_strOutData,
      string p_strParentID = "")
    {
      // ISSUE: variable of a compiler-generated type
      clsHDMFLibOutLog._Closure\u0024__2\u002D0 closure20_1;
      // ISSUE: object of a compiler-generated type is created
      // ISSUE: variable of a compiler-generated type
      clsHDMFLibOutLog._Closure\u0024__2\u002D0 closure20_2 = new clsHDMFLibOutLog._Closure\u0024__2\u002D0(closure20_1);
      // ISSUE: reference to a compiler-generated field
      closure20_2.\u0024VB\u0024Local_p_strDataID = p_strDataID;
      // ISSUE: reference to a compiler-generated field
      closure20_2.\u0024VB\u0024Local_p_strParentID = p_strParentID;
      StringBuilder stringBuilder = new StringBuilder();
      try
      {
        int num1;
        // ISSUE: reference to a compiler-generated field
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(closure20_2.\u0024VB\u0024Local_p_strParentID, "", false) == 0)
        {
          // ISSUE: reference to a compiler-generated method
          num1 = p_lst_strOutData.FindIndex(new Predicate<string>(closure20_2._Lambda\u0024__0));
        }
        else
        {
          // ISSUE: variable of a compiler-generated type
          clsHDMFLibOutLog._Closure\u0024__2\u002D1 closure21_1;
          // ISSUE: object of a compiler-generated type is created
          // ISSUE: variable of a compiler-generated type
          clsHDMFLibOutLog._Closure\u0024__2\u002D1 closure21_2 = new clsHDMFLibOutLog._Closure\u0024__2\u002D1(closure21_1);
          // ISSUE: reference to a compiler-generated field
          // ISSUE: reference to a compiler-generated method
          closure21_2.\u0024VB\u0024Local_iParentIndex = p_lst_strOutData.FindIndex(new Predicate<string>(closure20_2._Lambda\u0024__1));
          List<string> source1 = p_lst_strOutData;
          Func<string, int, VB\u0024AnonymousType_1<string, int>> selector1;
          // ISSUE: reference to a compiler-generated field
          if (clsHDMFLibOutLog._Closure\u0024__.\u0024I2\u002D2 != null)
          {
            // ISSUE: reference to a compiler-generated field
            selector1 = clsHDMFLibOutLog._Closure\u0024__.\u0024I2\u002D2;
          }
          else
          {
            // ISSUE: reference to a compiler-generated field
            clsHDMFLibOutLog._Closure\u0024__.\u0024I2\u002D2 = selector1 = [SpecialName] (strData, iIDIndex) => new
            {
              strData = strData,
              iIndex = iIDIndex
            };
          }
          // ISSUE: reference to a compiler-generated method
          IEnumerable<VB\u0024AnonymousType_1<string, int>> source2 = source1.Select(selector1).Where(new System.Func<VB\u0024AnonymousType_1<string, int>, bool>(closure20_2._Lambda\u0024__3));
          System.Func<VB\u0024AnonymousType_1<string, int>, int> selector2;
          // ISSUE: reference to a compiler-generated field
          if (clsHDMFLibOutLog._Closure\u0024__.\u0024I2\u002D4 != null)
          {
            // ISSUE: reference to a compiler-generated field
            selector2 = clsHDMFLibOutLog._Closure\u0024__.\u0024I2\u002D4;
          }
          else
          {
            // ISSUE: reference to a compiler-generated field
            clsHDMFLibOutLog._Closure\u0024__.\u0024I2\u002D4 = selector2 = [SpecialName] (Temp) => Temp.iIndex;
          }
          // ISSUE: reference to a compiler-generated method
          num1 = source2.Select(selector2).ToList<int>().Where<int>(new System.Func<int, bool>(closure21_2._Lambda\u0024__5)).FirstOrDefault<int>();
        }
        int index1 = checked (num1 + 1);
        int int32_1 = clsHDMFLibUtil.ConvertToInt32(p_lst_strOutData[index1]);
        int index2 = checked (index1 + int32_1 + 1);
        int int32_2 = clsHDMFLibUtil.ConvertToInt32(p_lst_strOutData[index2]);
        int num2 = 0;
        do
        {
          int num3;
          int num4;
          if (num2 == 0)
          {
            num3 = index1;
            num4 = int32_1;
          }
          else
          {
            num3 = index2;
            num4 = int32_2;
          }
          if (stringBuilder.Length != 0 & num4 != 0)
            stringBuilder.Append("|");
          int num5 = checked (num4 - 1);
          int num6 = 0;
          while (num6 <= num5)
          {
            stringBuilder.Append(p_lst_strOutData[checked (num3 + num6 + 1)]);
            if (num6 != checked (num4 - 1))
              stringBuilder.Append(",");
            checked { ++num6; }
          }
          checked { ++num2; }
        }
        while (num2 <= 1);
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLibOutLog]GetDataFromOutData):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      return stringBuilder.ToString();
    }

    public static string GetDataFromOutData(int p_strDataIndex, List<string> p_lst_strOutData)
    {
      StringBuilder stringBuilder = new StringBuilder();
      int index1 = checked (p_strDataIndex + 1);
      int int32_1 = clsHDMFLibUtil.ConvertToInt32(p_lst_strOutData[index1]);
      int index2 = checked (index1 + int32_1 + 1);
      int int32_2 = clsHDMFLibUtil.ConvertToInt32(p_lst_strOutData[index2]);
      try
      {
        int num1 = 0;
        do
        {
          int num2;
          int num3;
          if (num1 == 0)
          {
            num2 = index1;
            num3 = int32_1;
          }
          else
          {
            num2 = index2;
            num3 = int32_2;
          }
          if (stringBuilder.Length != 0 & num3 != 0)
            stringBuilder.Append("|");
          int num4 = checked (num3 - 1);
          int num5 = 0;
          while (num5 <= num4)
          {
            stringBuilder.Append(p_lst_strOutData[checked (num2 + num5 + 1)]);
            if (num5 != checked (num3 - 1))
              stringBuilder.Append(",");
            checked { ++num5; }
          }
          checked { ++num1; }
        }
        while (num1 <= 1);
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLibOutLog]GetDataFromOLog(2)):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      return stringBuilder.ToString();
    }

    public static DataTable GetFillingPhaseDataFromOutData(List<string> p_lst_strOutData)
    {
      // ISSUE: variable of a compiler-generated type
      clsHDMFLibOutLog._Closure\u0024__4\u002D0 closure40_1;
      // ISSUE: object of a compiler-generated type is created
      // ISSUE: variable of a compiler-generated type
      clsHDMFLibOutLog._Closure\u0024__4\u002D0 closure40_2 = new clsHDMFLibOutLog._Closure\u0024__4\u002D0(closure40_1);
      DataTable phaseDataFromOutData = new DataTable();
      string[] strArray1 = new string[0];
      // ISSUE: reference to a compiler-generated field
      closure40_2.\u0024VB\u0024Local_strStartID = "304001";
      // ISSUE: reference to a compiler-generated field
      closure40_2.\u0024VB\u0024Local_strEndID = "304002";
      string[] source1 = new string[4]
      {
        "304004",
        "304005",
        "304006",
        "304007"
      };
      try
      {
        phaseDataFromOutData.TableName = "FillPhase";
        List<string> source2 = p_lst_strOutData;
        System.Func<string, bool> predicate;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I4\u002D0 != null)
        {
          // ISSUE: reference to a compiler-generated field
          predicate = clsHDMFLibOutLog._Closure\u0024__.\u0024I4\u002D0;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I4\u002D0 = predicate = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("304001"));
        }
        if (source2.Any<string>(predicate))
        {
          phaseDataFromOutData.Columns.Add("Time");
          phaseDataFromOutData.Columns.Add("Fill Vol");
          phaseDataFromOutData.Columns.Add("Inj Press");
          phaseDataFromOutData.Columns.Add("Clamp F");
          phaseDataFromOutData.Columns.Add("Flow Rate");
          phaseDataFromOutData.Columns.Add("Frozen");
          phaseDataFromOutData.Columns.Add("Status");
        }
        else
        {
          phaseDataFromOutData.Columns.Add("Time");
          phaseDataFromOutData.Columns.Add("Pressure");
          phaseDataFromOutData.Columns.Add("Clamp force");
          phaseDataFromOutData.Columns.Add("Volume");
          phaseDataFromOutData.Columns.Add("Flow Rate");
          phaseDataFromOutData.Columns.Add("Status");
          source1 = new string[4]
          {
            "93004",
            "93154",
            "93010",
            "93300"
          };
          // ISSUE: reference to a compiler-generated field
          closure40_2.\u0024VB\u0024Local_strStartID = "90120";
          // ISSUE: reference to a compiler-generated field
          closure40_2.\u0024VB\u0024Local_strEndID = "90125";
        }
        // ISSUE: reference to a compiler-generated method
        int index1 = p_lst_strOutData.FindIndex(new Predicate<string>(closure40_2._Lambda\u0024__1));
        // ISSUE: reference to a compiler-generated method
        int index2 = p_lst_strOutData.FindIndex(new Predicate<string>(closure40_2._Lambda\u0024__2));
        int num1 = checked (p_lst_strOutData.Count - 1);
        int num2 = index1;
        while (num2 <= num1)
        {
          if (num2 != index2)
          {
            if (((IEnumerable<string>) source1).Contains<string>(p_lst_strOutData[num2]))
            {
              string[] strArray2 = clsHDMFLibOutLog.GetDataFromOutData(num2, p_lst_strOutData).Split(',');
              DataRow dataRow = phaseDataFromOutData.Rows.Add();
              int num3 = checked (phaseDataFromOutData.Columns.Count - 1);
              int index3 = 0;
              while (index3 <= num3)
              {
                if (index3 == checked (phaseDataFromOutData.Columns.Count - 1))
                {
                  string Left = p_lst_strOutData[num2];
                  if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Left, source1[0], false) == 0)
                    dataRow[index3] = (object) "V";
                  else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Left, source1[1], false) == 0)
                    dataRow[index3] = (object) "V/P";
                  else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Left, source1[2], false) == 0)
                    dataRow[index3] = (object) "P";
                  else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Left, source1[3], false) == 0)
                    dataRow[index3] = (object) "Filled";
                }
                else
                {
                  string p_strValue = strArray2[index3].Trim();
                  if (p_strValue.Contains("e-") | p_strValue.Contains("e+"))
                    p_strValue = Conversions.ToString(clsHDMFLib.ParsingAnalysisValue(p_strValue));
                  double num4 = clsHDMFLibUtil.ConvertToDouble(p_strValue);
                  if (phaseDataFromOutData.Columns[index3].ColumnName.Contains("Press"))
                    num4 *= Math.Pow(10.0, -6.0);
                  else if (phaseDataFromOutData.Columns[index3].ColumnName.Contains("Flow"))
                    num4 *= Math.Pow(10.0, 6.0);
                  else if (phaseDataFromOutData.Columns[index3].ColumnName.Contains("Clamp"))
                    num4 = num4 * 1.019399 / Math.Pow(10.0, 4.0);
                  dataRow[index3] = (object) Math.Round(num4, 2).ToString();
                }
                checked { ++index3; }
              }
            }
            checked { ++num2; }
          }
          else
            break;
        }
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLibOutLog]GetFillingPhaseDataFromOutData):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      return phaseDataFromOutData;
    }

    public static DataTable GetPackingPhaseDataFromOutData(List<string> p_lst_strOutData)
    {
      // ISSUE: variable of a compiler-generated type
      clsHDMFLibOutLog._Closure\u0024__5\u002D0 closure50_1;
      // ISSUE: object of a compiler-generated type is created
      // ISSUE: variable of a compiler-generated type
      clsHDMFLibOutLog._Closure\u0024__5\u002D0 closure50_2 = new clsHDMFLibOutLog._Closure\u0024__5\u002D0(closure50_1);
      DataTable phaseDataFromOutData = new DataTable();
      string[] strArray1 = new string[0];
      // ISSUE: reference to a compiler-generated field
      closure50_2.\u0024VB\u0024Local_strStartID = "304034";
      // ISSUE: reference to a compiler-generated field
      closure50_2.\u0024VB\u0024Local_strEndID = "304032";
      string Left = "304033";
      string Right = "93410";
      try
      {
        bool flag = false;
        phaseDataFromOutData.TableName = "PackPhase";
        List<string> source = p_lst_strOutData;
        System.Func<string, bool> predicate;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I5\u002D0 != null)
        {
          // ISSUE: reference to a compiler-generated field
          predicate = clsHDMFLibOutLog._Closure\u0024__.\u0024I5\u002D0;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I5\u002D0 = predicate = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Contains("304034"));
        }
        if (source.Any<string>(predicate))
        {
          phaseDataFromOutData.Columns.Add("Time");
          phaseDataFromOutData.Columns.Add("Packing");
          phaseDataFromOutData.Columns.Add("Inj Press");
          phaseDataFromOutData.Columns.Add("Clamp F");
          phaseDataFromOutData.Columns.Add("Part Mass");
          phaseDataFromOutData.Columns.Add("Frozen");
          phaseDataFromOutData.Columns.Add("Status");
        }
        else
        {
          flag = true;
          phaseDataFromOutData.Columns.Add("Time");
          phaseDataFromOutData.Columns.Add("Pressure");
          phaseDataFromOutData.Columns.Add("Clamp force");
          phaseDataFromOutData.Columns.Add("Packing");
          phaseDataFromOutData.Columns.Add("Status");
          // ISSUE: reference to a compiler-generated field
          closure50_2.\u0024VB\u0024Local_strStartID = "90125";
          // ISSUE: reference to a compiler-generated field
          closure50_2.\u0024VB\u0024Local_strEndID = "90099";
          Left = "93400";
        }
        // ISSUE: reference to a compiler-generated method
        int index1 = p_lst_strOutData.FindIndex(new Predicate<string>(closure50_2._Lambda\u0024__1));
        // ISSUE: reference to a compiler-generated method
        int index2 = p_lst_strOutData.FindIndex(new Predicate<string>(closure50_2._Lambda\u0024__2));
        int num1 = checked (p_lst_strOutData.Count - 1);
        int num2 = index1;
        while (num2 <= num1)
        {
          if (num2 != index2)
          {
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Left, p_lst_strOutData[num2], false) == 0)
            {
              string[] strArray2 = clsHDMFLibOutLog.GetDataFromOutData(num2, p_lst_strOutData).Split(',');
              DataRow dataRow = phaseDataFromOutData.Rows.Add();
              int num3 = checked (phaseDataFromOutData.Columns.Count - 1);
              int index3 = 0;
              while (index3 <= num3)
              {
                if (index3 == checked (phaseDataFromOutData.Columns.Count - 1))
                {
                  dataRow[index3] = (object) "P";
                }
                else
                {
                  string p_strValue = strArray2[index3].Trim();
                  if (p_strValue.Contains("e-") | p_strValue.Contains("e+"))
                    p_strValue = Conversions.ToString(clsHDMFLib.ParsingAnalysisValue(p_strValue));
                  double num4 = clsHDMFLibUtil.ConvertToDouble(p_strValue);
                  if (phaseDataFromOutData.Columns[index3].ColumnName.Contains("Press"))
                    num4 *= Math.Pow(10.0, -6.0);
                  else if (phaseDataFromOutData.Columns[index3].ColumnName.Contains("Flow"))
                    num4 *= Math.Pow(10.0, 6.0);
                  else if (phaseDataFromOutData.Columns[index3].ColumnName.Contains("Clamp"))
                    num4 = num4 * 1.019399 / Math.Pow(10.0, 4.0);
                  dataRow[index3] = (object) Math.Round(num4, 2).ToString();
                }
                checked { ++index3; }
              }
            }
            else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(p_lst_strOutData[num2], Right, false) == 0 & flag)
            {
              string dataFromOutData = clsHDMFLibOutLog.GetDataFromOutData(p_lst_strOutData[num2], p_lst_strOutData);
              DataRow dataRow = phaseDataFromOutData.Rows.Add();
              dataRow["Time"] = (object) Math.Round(clsHDMFLibUtil.ConvertToDouble(dataFromOutData));
              dataRow["Status"] = (object) "Pressure released";
            }
            checked { ++num2; }
          }
          else
            break;
        }
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLibOutLog]GetPackingPhaseDataFromOutData):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      return phaseDataFromOutData;
    }

    public static string GetMeshTypeFromOutData(List<string> p_lst_strLogData)
    {
      string meshTypeFromOutData = string.Empty;
      List<string> source1 = p_lst_strLogData;
      System.Func<string, bool> predicate1;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I6\u002D0 != null)
      {
        // ISSUE: reference to a compiler-generated field
        predicate1 = clsHDMFLibOutLog._Closure\u0024__.\u0024I6\u002D0;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I6\u002D0 = predicate1 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("300020"));
      }
      if (source1.Any<string>(predicate1))
      {
        meshTypeFromOutData = "3D";
      }
      else
      {
        List<string> source2 = p_lst_strLogData;
        System.Func<string, bool> predicate2;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I6\u002D1 != null)
        {
          // ISSUE: reference to a compiler-generated field
          predicate2 = clsHDMFLibOutLog._Closure\u0024__.\u0024I6\u002D1;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I6\u002D1 = predicate2 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("700220"));
        }
        if (source2.Any<string>(predicate2))
          meshTypeFromOutData = "Dual";
      }
      return meshTypeFromOutData;
    }

    public static string GetMeshCountFromOutData(
      List<string> p_lst_strLogData,
      string p_strMeshType)
    {
      string countFromOutData = string.Empty;
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(p_strMeshType, "3D", false) == 0)
        countFromOutData = clsHDMFLibOutLog.GetDataFromOutData("300120", p_lst_strLogData);
      else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(p_strMeshType, "Dual", false) == 0)
        countFromOutData = clsHDMFLibOutLog.GetDataFromOutData("39052", p_lst_strLogData);
      return countFromOutData;
    }

    public static string GetSequenceFromOutData(List<string> p_lst_strLogData)
    {
      string empty = string.Empty;
      StringBuilder stringBuilder = new StringBuilder();
      List<string> source1 = p_lst_strLogData;
      System.Func<string, bool> predicate1;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I8\u002D0 != null)
      {
        // ISSUE: reference to a compiler-generated field
        predicate1 = clsHDMFLibOutLog._Closure\u0024__.\u0024I8\u002D0;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I8\u002D0 = predicate1 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("700000"));
      }
      if (source1.Any<string>(predicate1))
        stringBuilder.Append("Cool");
      List<string> source2 = p_lst_strLogData;
      System.Func<string, bool> predicate2;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I8\u002D1 != null)
      {
        // ISSUE: reference to a compiler-generated field
        predicate2 = clsHDMFLibOutLog._Closure\u0024__.\u0024I8\u002D1;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I8\u002D1 = predicate2 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("300001"));
      }
      int num1 = source2.Any<string>(predicate2) ? 1 : 0;
      List<string> source3 = p_lst_strLogData;
      System.Func<string, bool> predicate3;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I8\u002D2 != null)
      {
        // ISSUE: reference to a compiler-generated field
        predicate3 = clsHDMFLibOutLog._Closure\u0024__.\u0024I8\u002D2;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I8\u002D2 = predicate3 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("90100"));
      }
      int num2 = source3.Any<string>(predicate3) ? 1 : 0;
      if ((num1 | num2) != 0)
      {
        if (stringBuilder.Length != 0)
          stringBuilder.Append(",");
        stringBuilder.Append("Fill");
      }
      List<string> source4 = p_lst_strLogData;
      System.Func<string, bool> predicate4;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I8\u002D3 != null)
      {
        // ISSUE: reference to a compiler-generated field
        predicate4 = clsHDMFLibOutLog._Closure\u0024__.\u0024I8\u002D3;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I8\u002D3 = predicate4 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("304033"));
      }
      int num3 = source4.Any<string>(predicate4) ? 1 : 0;
      List<string> source5 = p_lst_strLogData;
      System.Func<string, bool> predicate5;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I8\u002D4 != null)
      {
        // ISSUE: reference to a compiler-generated field
        predicate5 = clsHDMFLibOutLog._Closure\u0024__.\u0024I8\u002D4;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I8\u002D4 = predicate5 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("90103"));
      }
      int num4 = source5.Any<string>(predicate5) ? 1 : 0;
      if ((num3 | num4) != 0)
      {
        if (stringBuilder.Length != 0)
          stringBuilder.Append(",");
        stringBuilder.Append("Pack");
      }
      List<string> source6 = p_lst_strLogData;
      System.Func<string, bool> predicate6;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I8\u002D5 != null)
      {
        // ISSUE: reference to a compiler-generated field
        predicate6 = clsHDMFLibOutLog._Closure\u0024__.\u0024I8\u002D5;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I8\u002D5 = predicate6 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("200001"));
      }
      if (source6.Any<string>(predicate6))
      {
        if (stringBuilder.Length != 0)
          stringBuilder.Append(",");
        stringBuilder.Append("Warp");
      }
      return stringBuilder.ToString();
    }

    public static string GetPartTempFromOutData(List<string> p_lst_strLogData)
    {
      StringBuilder stringBuilder = new StringBuilder();
      string empty = string.Empty;
      List<string> source1 = p_lst_strLogData;
      System.Func<string, bool> predicate1;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I9\u002D0 != null)
      {
        // ISSUE: reference to a compiler-generated field
        predicate1 = clsHDMFLibOutLog._Closure\u0024__.\u0024I9\u002D0;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I9\u002D0 = predicate1 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("702181"));
      }
      if (source1.Any<string>(predicate1))
      {
        string kelvin = clsHDMFLibUtil.ConvertToKelvin(clsHDMFLibOutLog.GetDataFromOutData("702181", p_lst_strLogData));
        stringBuilder.Append(kelvin);
        if (stringBuilder.Length != 0)
          stringBuilder.Append("|");
      }
      List<string> source2 = p_lst_strLogData;
      System.Func<string, bool> predicate2;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I9\u002D1 != null)
      {
        // ISSUE: reference to a compiler-generated field
        predicate2 = clsHDMFLibOutLog._Closure\u0024__.\u0024I9\u002D1;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I9\u002D1 = predicate2 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("702182"));
      }
      if (source2.Any<string>(predicate2))
      {
        string kelvin = clsHDMFLibUtil.ConvertToKelvin(clsHDMFLibOutLog.GetDataFromOutData("702182", p_lst_strLogData));
        stringBuilder.Append(kelvin);
        if (stringBuilder.Length != 0)
          stringBuilder.Append("|");
      }
      List<string> source3 = p_lst_strLogData;
      System.Func<string, bool> predicate3;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I9\u002D2 != null)
      {
        // ISSUE: reference to a compiler-generated field
        predicate3 = clsHDMFLibOutLog._Closure\u0024__.\u0024I9\u002D2;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I9\u002D2 = predicate3 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("702183"));
      }
      if (source3.Any<string>(predicate3))
      {
        string kelvin = clsHDMFLibUtil.ConvertToKelvin(clsHDMFLibOutLog.GetDataFromOutData("702183", p_lst_strLogData));
        stringBuilder.Append(kelvin);
      }
      return stringBuilder.ToString();
    }

    public static string GetCavityTempFromOutData(List<string> p_lst_strLogData)
    {
      StringBuilder stringBuilder = new StringBuilder();
      string empty = string.Empty;
      List<string> source1 = p_lst_strLogData;
      System.Func<string, bool> predicate1;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I10\u002D0 != null)
      {
        // ISSUE: reference to a compiler-generated field
        predicate1 = clsHDMFLibOutLog._Closure\u0024__.\u0024I10\u002D0;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I10\u002D0 = predicate1 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("702190"));
      }
      if (source1.Any<string>(predicate1))
      {
        string kelvin = clsHDMFLibUtil.ConvertToKelvin(clsHDMFLibOutLog.GetDataFromOutData("702190", p_lst_strLogData));
        stringBuilder.Append(kelvin);
        if (stringBuilder.Length != 0)
          stringBuilder.Append("|");
      }
      List<string> source2 = p_lst_strLogData;
      System.Func<string, bool> predicate2;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I10\u002D1 != null)
      {
        // ISSUE: reference to a compiler-generated field
        predicate2 = clsHDMFLibOutLog._Closure\u0024__.\u0024I10\u002D1;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I10\u002D1 = predicate2 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("702200"));
      }
      if (source2.Any<string>(predicate2))
      {
        string kelvin = clsHDMFLibUtil.ConvertToKelvin(clsHDMFLibOutLog.GetDataFromOutData("702200", p_lst_strLogData));
        stringBuilder.Append(kelvin);
        if (stringBuilder.Length != 0)
          stringBuilder.Append("|");
      }
      List<string> source3 = p_lst_strLogData;
      System.Func<string, bool> predicate3;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I10\u002D2 != null)
      {
        // ISSUE: reference to a compiler-generated field
        predicate3 = clsHDMFLibOutLog._Closure\u0024__.\u0024I10\u002D2;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I10\u002D2 = predicate3 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("702210"));
      }
      if (source3.Any<string>(predicate3))
      {
        string kelvin = clsHDMFLibUtil.ConvertToKelvin(clsHDMFLibOutLog.GetDataFromOutData("702210", p_lst_strLogData));
        stringBuilder.Append(kelvin);
      }
      return stringBuilder.ToString();
    }

    public static string GetManufactureAndTradeFromOutData(List<string> p_lst_strLogData)
    {
      string empty = string.Empty;
      StringBuilder stringBuilder = new StringBuilder();
      try
      {
        List<string> source1 = p_lst_strLogData;
        System.Func<string, bool> predicate1;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I11\u002D0 != null)
        {
          // ISSUE: reference to a compiler-generated field
          predicate1 = clsHDMFLibOutLog._Closure\u0024__.\u0024I11\u002D0;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I11\u002D0 = predicate1 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("10012"));
        }
        if (source1.Any<string>(predicate1))
        {
          string dataFromOutData1 = clsHDMFLibOutLog.GetDataFromOutData("10012", p_lst_strLogData);
          stringBuilder.Append(dataFromOutData1);
          List<string> source2 = p_lst_strLogData;
          System.Func<string, bool> predicate2;
          // ISSUE: reference to a compiler-generated field
          if (clsHDMFLibOutLog._Closure\u0024__.\u0024I11\u002D1 != null)
          {
            // ISSUE: reference to a compiler-generated field
            predicate2 = clsHDMFLibOutLog._Closure\u0024__.\u0024I11\u002D1;
          }
          else
          {
            // ISSUE: reference to a compiler-generated field
            clsHDMFLibOutLog._Closure\u0024__.\u0024I11\u002D1 = predicate2 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("10011"));
          }
          if (source2.Any<string>(predicate2))
          {
            string dataFromOutData2 = clsHDMFLibOutLog.GetDataFromOutData("10011", p_lst_strLogData);
            stringBuilder.Append("|");
            stringBuilder.Append(dataFromOutData2);
          }
        }
        else
        {
          List<string> source3 = p_lst_strLogData;
          System.Func<string, bool> predicate3;
          // ISSUE: reference to a compiler-generated field
          if (clsHDMFLibOutLog._Closure\u0024__.\u0024I11\u002D2 != null)
          {
            // ISSUE: reference to a compiler-generated field
            predicate3 = clsHDMFLibOutLog._Closure\u0024__.\u0024I11\u002D2;
          }
          else
          {
            // ISSUE: reference to a compiler-generated field
            clsHDMFLibOutLog._Closure\u0024__.\u0024I11\u002D2 = predicate3 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("1999"));
          }
          if (source3.Any<string>(predicate3))
          {
            string[] strArray = clsHDMFLibOutLog.GetDataFromOutData("1999", p_lst_strLogData).Split('|')[0].Split(':');
            stringBuilder.Append(strArray[1].Trim());
            stringBuilder.Append("|");
            stringBuilder.Append(strArray[0].Trim());
          }
        }
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLibOutLog]GetManufactureAndTradeFromOutData):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      return stringBuilder.ToString();
    }

    public static string GetFamilyNameFromOutData(List<string> p_lst_strLogData)
    {
      string familyNameFromOutData = string.Empty;
      List<string> source = p_lst_strLogData;
      System.Func<string, bool> predicate;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I12\u002D0 != null)
      {
        // ISSUE: reference to a compiler-generated field
        predicate = clsHDMFLibOutLog._Closure\u0024__.\u0024I12\u002D0;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I12\u002D0 = predicate = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("304105"));
      }
      if (source.Any<string>(predicate))
        familyNameFromOutData = clsHDMFLibOutLog.GetDataFromOutData("304105", p_lst_strLogData);
      return familyNameFromOutData;
    }

    public static string GetTransitionTempFromOutData(List<string> p_lst_strLogData)
    {
      string transitionTempFromOutData = string.Empty;
      List<string> source = p_lst_strLogData;
      System.Func<string, bool> predicate;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I13\u002D0 != null)
      {
        // ISSUE: reference to a compiler-generated field
        predicate = clsHDMFLibOutLog._Closure\u0024__.\u0024I13\u002D0;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I13\u002D0 = predicate = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("1500"));
      }
      if (source.Any<string>(predicate))
        transitionTempFromOutData = clsHDMFLibUtil.ConvertToKelvin(clsHDMFLibOutLog.GetDataFromOutData("1500", p_lst_strLogData));
      return transitionTempFromOutData;
    }

    public static string GetViscosityFromOutData(List<string> p_lst_strLogData)
    {
      string empty = string.Empty;
      StringBuilder stringBuilder = new StringBuilder();
      try
      {
        List<string> source = p_lst_strLogData;
        System.Func<string, bool> predicate;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I14\u002D0 != null)
        {
          // ISSUE: reference to a compiler-generated field
          predicate = clsHDMFLibOutLog._Closure\u0024__.\u0024I14\u002D0;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I14\u002D0 = predicate = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("1313"));
        }
        if (source.Any<string>(predicate))
        {
          string[] strArray = clsHDMFLibOutLog.GetDataFromOutData("1313", p_lst_strLogData).Split(',');
          string str1 = Math.Round(clsHDMFLibUtil.ConvertToDouble(strArray[0]), 4).ToString();
          stringBuilder.Append(str1 + "|");
          string str2 = Conversions.ToString(clsHDMFLib.ParsingAnalysisValue(strArray[4]));
          stringBuilder.Append(str2);
        }
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLibOutLog]GetViscosityFromOutData):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      return stringBuilder.ToString();
    }

    public static string GetFillerPropertiesFromOutData(List<string> p_lst_strLogData)
    {
      string propertiesFromOutData = string.Empty;
      List<string> source = p_lst_strLogData;
      System.Func<string, bool> predicate;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I15\u002D0 != null)
      {
        // ISSUE: reference to a compiler-generated field
        predicate = clsHDMFLibOutLog._Closure\u0024__.\u0024I15\u002D0;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I15\u002D0 = predicate = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("3999"));
      }
      if (source.Any<string>(predicate))
        propertiesFromOutData = clsHDMFLibOutLog.GetDataFromOutData("3999", p_lst_strLogData);
      return propertiesFromOutData;
    }

    public static string GetFillerWeightFromOutData(
      List<string> p_lst_strLogData,
      string p_strMeshType)
    {
      string p_strValue = string.Empty;
      List<string> source = p_lst_strLogData;
      System.Func<string, bool> predicate;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I16\u002D0 != null)
      {
        // ISSUE: reference to a compiler-generated field
        predicate = clsHDMFLibOutLog._Closure\u0024__.\u0024I16\u002D0;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I16\u002D0 = predicate = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("3900"));
      }
      if (source.Any<string>(predicate))
        p_strValue = clsHDMFLibOutLog.GetDataFromOutData("3900", p_lst_strLogData);
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(p_strMeshType, "Dual", false) == 0)
        p_strValue = Conversions.ToString(clsHDMFLibUtil.ConvertToDouble(p_strValue) * 100.0);
      return p_strValue;
    }

    public static string GetTotalVolumeFromOutData(List<string> p_lst_strLogData)
    {
      string volumeFromOutData = string.Empty;
      List<string> source1 = p_lst_strLogData;
      System.Func<string, bool> predicate1;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I17\u002D0 != null)
      {
        // ISSUE: reference to a compiler-generated field
        predicate1 = clsHDMFLibOutLog._Closure\u0024__.\u0024I17\u002D0;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I17\u002D0 = predicate1 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("300150"));
      }
      if (source1.Any<string>(predicate1))
      {
        volumeFromOutData = clsHDMFLibOutLog.GetDataFromOutData("300150", p_lst_strLogData);
      }
      else
      {
        List<string> source2 = p_lst_strLogData;
        System.Func<string, bool> predicate2;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I17\u002D1 != null)
        {
          // ISSUE: reference to a compiler-generated field
          predicate2 = clsHDMFLibOutLog._Closure\u0024__.\u0024I17\u002D1;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I17\u002D1 = predicate2 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("39300"));
        }
        if (source2.Any<string>(predicate2))
          volumeFromOutData = clsHDMFLibOutLog.GetDataFromOutData("39300", p_lst_strLogData);
      }
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(volumeFromOutData, string.Empty, false) != 0)
        volumeFromOutData = Conversions.ToString(Math.Round(clsHDMFLibUtil.ConvertToDouble(volumeFromOutData) * 1000000.0, 4));
      return volumeFromOutData;
    }

    public static string GetPartVolumeToBeFilledFromOutData(List<string> p_lst_strLogData)
    {
      string filledFromOutData = string.Empty;
      List<string> source1 = p_lst_strLogData;
      System.Func<string, bool> predicate1;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I18\u002D0 != null)
      {
        // ISSUE: reference to a compiler-generated field
        predicate1 = clsHDMFLibOutLog._Closure\u0024__.\u0024I18\u002D0;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I18\u002D0 = predicate1 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("300320"));
      }
      if (source1.Any<string>(predicate1))
      {
        filledFromOutData = clsHDMFLibOutLog.GetDataFromOutData("300320", p_lst_strLogData);
      }
      else
      {
        List<string> source2 = p_lst_strLogData;
        System.Func<string, bool> predicate2;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I18\u002D1 != null)
        {
          // ISSUE: reference to a compiler-generated field
          predicate2 = clsHDMFLibOutLog._Closure\u0024__.\u0024I18\u002D1;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I18\u002D1 = predicate2 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("39322"));
        }
        if (source2.Any<string>(predicate2))
          filledFromOutData = clsHDMFLibOutLog.GetDataFromOutData("39322", p_lst_strLogData);
      }
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(filledFromOutData, string.Empty, false) != 0)
        filledFromOutData = Conversions.ToString(Math.Round(clsHDMFLibUtil.ConvertToDouble(filledFromOutData) * 1000000.0, 4));
      return filledFromOutData;
    }

    public static string GetTotalProjectedAreaFromOutData(List<string> p_lst_strLogData)
    {
      string projectedAreaFromOutData = string.Empty;
      List<string> source1 = p_lst_strLogData;
      System.Func<string, bool> predicate1;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I19\u002D0 != null)
      {
        // ISSUE: reference to a compiler-generated field
        predicate1 = clsHDMFLibOutLog._Closure\u0024__.\u0024I19\u002D0;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I19\u002D0 = predicate1 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("300350"));
      }
      if (source1.Any<string>(predicate1))
      {
        projectedAreaFromOutData = clsHDMFLibOutLog.GetDataFromOutData("300350", p_lst_strLogData);
      }
      else
      {
        List<string> source2 = p_lst_strLogData;
        System.Func<string, bool> predicate2;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I19\u002D1 != null)
        {
          // ISSUE: reference to a compiler-generated field
          predicate2 = clsHDMFLibOutLog._Closure\u0024__.\u0024I19\u002D1;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I19\u002D1 = predicate2 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("39410"));
        }
        if (source2.Any<string>(predicate2))
          projectedAreaFromOutData = clsHDMFLibOutLog.GetDataFromOutData("39410", p_lst_strLogData);
      }
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(projectedAreaFromOutData, string.Empty, false) != 0)
        projectedAreaFromOutData = Conversions.ToString(Math.Round(clsHDMFLibUtil.ConvertToDouble(projectedAreaFromOutData) * 1000000.0, 4));
      return projectedAreaFromOutData;
    }

    public static string GetMachineInjectionPFromOutData(List<string> p_lst_strLogData)
    {
      string p_strValue = string.Empty;
      List<string> source = p_lst_strLogData;
      System.Func<string, bool> predicate;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I20\u002D0 != null)
      {
        // ISSUE: reference to a compiler-generated field
        predicate = clsHDMFLibOutLog._Closure\u0024__.\u0024I20\u002D0;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I20\u002D0 = predicate = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("10002"));
      }
      if (source.Any<string>(predicate))
      {
        p_strValue = clsHDMFLibOutLog.GetDataFromOutData("10002", p_lst_strLogData, "302000");
        if (p_strValue.ToLower().Contains("e-") | p_strValue.ToLower().Contains("e+"))
          p_strValue = (clsHDMFLib.ParsingAnalysisValue(p_strValue) / 1000000.0).ToString();
      }
      return p_strValue;
    }

    public static string GetMachineClampFFromOutData(List<string> p_lst_strLogData)
    {
      string empty = string.Empty;
      List<string> source = p_lst_strLogData;
      System.Func<string, bool> predicate;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I21\u002D0 != null)
      {
        // ISSUE: reference to a compiler-generated field
        predicate = clsHDMFLibOutLog._Closure\u0024__.\u0024I21\u002D0;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I21\u002D0 = predicate = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("10000"));
      }
      if (source.Any<string>(predicate))
        empty = Conversions.ToString(Math.Round(clsHDMFLibUtil.ConvertToDouble(clsHDMFLibOutLog.GetDataFromOutData("10000", p_lst_strLogData, "302000")) * 1.019399 / 10000.0, 0));
      return empty;
    }

    public static string GetProcessMeltTempFromOutData(List<string> p_lst_strLogData)
    {
      string meltTempFromOutData = string.Empty;
      try
      {
        List<string> source1 = p_lst_strLogData;
        System.Func<string, bool> predicate1;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I22\u002D0 != null)
        {
          // ISSUE: reference to a compiler-generated field
          predicate1 = clsHDMFLibOutLog._Closure\u0024__.\u0024I22\u002D0;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I22\u002D0 = predicate1 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("102101"));
        }
        if (source1.Any<string>(predicate1))
        {
          meltTempFromOutData = clsHDMFLibOutLog.GetDataFromOutData("102101", p_lst_strLogData);
        }
        else
        {
          List<string> source2 = p_lst_strLogData;
          System.Func<string, bool> predicate2;
          // ISSUE: reference to a compiler-generated field
          if (clsHDMFLibOutLog._Closure\u0024__.\u0024I22\u002D1 != null)
          {
            // ISSUE: reference to a compiler-generated field
            predicate2 = clsHDMFLibOutLog._Closure\u0024__.\u0024I22\u002D1;
          }
          else
          {
            // ISSUE: reference to a compiler-generated field
            clsHDMFLibOutLog._Closure\u0024__.\u0024I22\u002D1 = predicate2 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("11002"));
          }
          if (source2.Any<string>(predicate2))
            meltTempFromOutData = clsHDMFLibOutLog.GetDataFromOutData("11002", p_lst_strLogData);
        }
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(meltTempFromOutData, string.Empty, false) != 0)
          meltTempFromOutData = clsHDMFLibUtil.ConvertToKelvin(meltTempFromOutData);
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLibOutLog]GetProcessMeltTempFromOutData):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      return meltTempFromOutData;
    }

    public static string GetProcessMoldTempFromOutData(List<string> p_lst_strLogData)
    {
      string moldTempFromOutData = string.Empty;
      try
      {
        List<string> source1 = p_lst_strLogData;
        System.Func<string, bool> predicate1;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I23\u002D0 != null)
        {
          // ISSUE: reference to a compiler-generated field
          predicate1 = clsHDMFLibOutLog._Closure\u0024__.\u0024I23\u002D0;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I23\u002D0 = predicate1 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("220124"));
        }
        if (source1.Any<string>(predicate1))
        {
          moldTempFromOutData = clsHDMFLibOutLog.GetDataFromOutData("220124", p_lst_strLogData);
        }
        else
        {
          List<string> source2 = p_lst_strLogData;
          System.Func<string, bool> predicate2;
          // ISSUE: reference to a compiler-generated field
          if (clsHDMFLibOutLog._Closure\u0024__.\u0024I23\u002D1 != null)
          {
            // ISSUE: reference to a compiler-generated field
            predicate2 = clsHDMFLibOutLog._Closure\u0024__.\u0024I23\u002D1;
          }
          else
          {
            // ISSUE: reference to a compiler-generated field
            clsHDMFLibOutLog._Closure\u0024__.\u0024I23\u002D1 = predicate2 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("11004"));
          }
          if (source2.Any<string>(predicate2))
            moldTempFromOutData = clsHDMFLibOutLog.GetDataFromOutData("11004", p_lst_strLogData);
        }
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(moldTempFromOutData, string.Empty, false) != 0)
          moldTempFromOutData = clsHDMFLibUtil.ConvertToKelvin(moldTempFromOutData);
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLibOutLog]GetProcessMeltTempFromOutData):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      return moldTempFromOutData;
    }

    public static string GetFillingTypeFromOutData(List<string> p_lst_strLogData)
    {
      string empty = string.Empty;
      StringBuilder stringBuilder = new StringBuilder();
      try
      {
        List<string> source1 = p_lst_strLogData;
        System.Func<string, bool> predicate1;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I24\u002D0 != null)
        {
          // ISSUE: reference to a compiler-generated field
          predicate1 = clsHDMFLibOutLog._Closure\u0024__.\u0024I24\u002D0;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I24\u002D0 = predicate1 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("302004"));
        }
        if (source1.Any<string>(predicate1))
        {
          stringBuilder.Append("Injection time");
          string dataFromOutData = clsHDMFLibOutLog.GetDataFromOutData("102102", p_lst_strLogData);
          stringBuilder.Append("|" + dataFromOutData);
        }
        else
        {
          List<string> source2 = p_lst_strLogData;
          System.Func<string, bool> predicate2;
          // ISSUE: reference to a compiler-generated field
          if (clsHDMFLibOutLog._Closure\u0024__.\u0024I24\u002D1 != null)
          {
            // ISSUE: reference to a compiler-generated field
            predicate2 = clsHDMFLibOutLog._Closure\u0024__.\u0024I24\u002D1;
          }
          else
          {
            // ISSUE: reference to a compiler-generated field
            clsHDMFLibOutLog._Closure\u0024__.\u0024I24\u002D1 = predicate2 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("302007"));
          }
          if (source2.Any<string>(predicate2))
          {
            stringBuilder.Append("% Flow rate vs % shot volume");
            string dataFromOutData = clsHDMFLibOutLog.GetDataFromOutData("302034", p_lst_strLogData);
            if (dataFromOutData.ToLower().Contains("e-") | dataFromOutData.ToLower().Contains("e+"))
              dataFromOutData = Conversions.ToString(clsHDMFLib.ParsingAnalysisValue(dataFromOutData));
            string str = Conversions.ToString(Math.Round(clsHDMFLibUtil.ConvertToDouble(dataFromOutData) * 1000000.0, 0));
            stringBuilder.Append("|" + str);
          }
          else
          {
            List<string> source3 = p_lst_strLogData;
            System.Func<string, bool> predicate3;
            // ISSUE: reference to a compiler-generated field
            if (clsHDMFLibOutLog._Closure\u0024__.\u0024I24\u002D2 != null)
            {
              // ISSUE: reference to a compiler-generated field
              predicate3 = clsHDMFLibOutLog._Closure\u0024__.\u0024I24\u002D2;
            }
            else
            {
              // ISSUE: reference to a compiler-generated field
              clsHDMFLibOutLog._Closure\u0024__.\u0024I24\u002D2 = predicate3 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("10100"));
            }
            if (source3.Any<string>(predicate3))
            {
              stringBuilder.Append("Injection time");
              string dataFromOutData = clsHDMFLibOutLog.GetDataFromOutData("10100", p_lst_strLogData);
              stringBuilder.Append("|" + dataFromOutData);
            }
            else
            {
              List<string> source4 = p_lst_strLogData;
              System.Func<string, bool> predicate4;
              // ISSUE: reference to a compiler-generated field
              if (clsHDMFLibOutLog._Closure\u0024__.\u0024I24\u002D3 != null)
              {
                // ISSUE: reference to a compiler-generated field
                predicate4 = clsHDMFLibOutLog._Closure\u0024__.\u0024I24\u002D3;
              }
              else
              {
                // ISSUE: reference to a compiler-generated field
                clsHDMFLibOutLog._Closure\u0024__.\u0024I24\u002D3 = predicate4 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("10101"));
              }
              if (source4.Any<string>(predicate4))
              {
                stringBuilder.Append("Flow rate");
                string dataFromOutData = clsHDMFLibOutLog.GetDataFromOutData("10101", p_lst_strLogData);
                if (dataFromOutData.ToLower().Contains("e-") | dataFromOutData.ToLower().Contains("e+"))
                  dataFromOutData = Conversions.ToString(clsHDMFLib.ParsingAnalysisValue(dataFromOutData));
                string str = Conversions.ToString(Math.Round(clsHDMFLibUtil.ConvertToDouble(dataFromOutData) * 1000000.0, 0));
                stringBuilder.Append("|" + str);
              }
            }
          }
        }
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLibOutLog]GetFillingTypeFromOutData):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      return stringBuilder.ToString();
    }

    public static string GetProcessVPTypeFromOutData(List<string> p_lst_strLogData)
    {
      string empty = string.Empty;
      StringBuilder stringBuilder = new StringBuilder();
      try
      {
        List<string> source1 = p_lst_strLogData;
        System.Func<string, bool> predicate1;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I25\u002D0 != null)
        {
          // ISSUE: reference to a compiler-generated field
          predicate1 = clsHDMFLibOutLog._Closure\u0024__.\u0024I25\u002D0;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I25\u002D0 = predicate1 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("302042"));
        }
        if (source1.Any<string>(predicate1))
        {
          stringBuilder.Append("By % volume filled");
          string dataFromOutData = clsHDMFLibOutLog.GetDataFromOutData("220011", p_lst_strLogData);
          stringBuilder.Append("|" + dataFromOutData);
        }
        else
        {
          List<string> source2 = p_lst_strLogData;
          System.Func<string, bool> predicate2;
          // ISSUE: reference to a compiler-generated field
          if (clsHDMFLibOutLog._Closure\u0024__.\u0024I25\u002D1 != null)
          {
            // ISSUE: reference to a compiler-generated field
            predicate2 = clsHDMFLibOutLog._Closure\u0024__.\u0024I25\u002D1;
          }
          else
          {
            // ISSUE: reference to a compiler-generated field
            clsHDMFLibOutLog._Closure\u0024__.\u0024I25\u002D1 = predicate2 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("10300"));
          }
          if (source2.Any<string>(predicate2))
          {
            stringBuilder.Append("By % volume filled");
            string dataFromOutData = clsHDMFLibOutLog.GetDataFromOutData("10300", p_lst_strLogData);
            stringBuilder.Append("|" + dataFromOutData);
          }
        }
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLibOutLog]GetProcessVPTypeFromOutData):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      return stringBuilder.ToString();
    }

    public static string GetPackDataFromOutData(List<string> p_lst_strLogData)
    {
      string str1 = string.Empty;
      string Left = string.Empty;
      StringBuilder stringBuilder = new StringBuilder();
      try
      {
        List<string> source1 = p_lst_strLogData;
        System.Func<string, bool> predicate1;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I26\u002D0 != null)
        {
          // ISSUE: reference to a compiler-generated field
          predicate1 = clsHDMFLibOutLog._Closure\u0024__.\u0024I26\u002D0;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I26\u002D0 = predicate1 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("302055"));
        }
        if (source1.Any<string>(predicate1))
        {
          Left = "%";
          str1 = clsHDMFLibOutLog.GetDataFromOutData("302055", p_lst_strLogData);
        }
        else
        {
          List<string> source2 = p_lst_strLogData;
          System.Func<string, bool> predicate2;
          // ISSUE: reference to a compiler-generated field
          if (clsHDMFLibOutLog._Closure\u0024__.\u0024I26\u002D1 != null)
          {
            // ISSUE: reference to a compiler-generated field
            predicate2 = clsHDMFLibOutLog._Closure\u0024__.\u0024I26\u002D1;
          }
          else
          {
            // ISSUE: reference to a compiler-generated field
            clsHDMFLibOutLog._Closure\u0024__.\u0024I26\u002D1 = predicate2 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("302056"));
          }
          if (source2.Any<string>(predicate2))
          {
            Left = "MPa";
            str1 = clsHDMFLibOutLog.GetDataFromOutData("302056", p_lst_strLogData);
          }
          else
          {
            List<string> source3 = p_lst_strLogData;
            System.Func<string, bool> predicate3;
            // ISSUE: reference to a compiler-generated field
            if (clsHDMFLibOutLog._Closure\u0024__.\u0024I26\u002D2 != null)
            {
              // ISSUE: reference to a compiler-generated field
              predicate3 = clsHDMFLibOutLog._Closure\u0024__.\u0024I26\u002D2;
            }
            else
            {
              // ISSUE: reference to a compiler-generated field
              clsHDMFLibOutLog._Closure\u0024__.\u0024I26\u002D2 = predicate3 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("126114"));
            }
            if (source3.Any<string>(predicate3))
            {
              Left = "%";
              str1 = clsHDMFLibOutLog.GetDataFromOutData("126114", p_lst_strLogData);
            }
            else
            {
              List<string> source4 = p_lst_strLogData;
              System.Func<string, bool> predicate4;
              // ISSUE: reference to a compiler-generated field
              if (clsHDMFLibOutLog._Closure\u0024__.\u0024I26\u002D3 != null)
              {
                // ISSUE: reference to a compiler-generated field
                predicate4 = clsHDMFLibOutLog._Closure\u0024__.\u0024I26\u002D3;
              }
              else
              {
                // ISSUE: reference to a compiler-generated field
                clsHDMFLibOutLog._Closure\u0024__.\u0024I26\u002D3 = predicate4 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("126112"));
              }
              if (source4.Any<string>(predicate4))
              {
                Left = "MPa";
                str1 = clsHDMFLibOutLog.GetDataFromOutData("126112", p_lst_strLogData);
              }
            }
          }
        }
        stringBuilder.Append(Left);
        string[] strArray = str1.Split(',');
        int num1 = checked ((int) Math.Round(unchecked ((double) strArray.Length / 2.0)) - 1);
        int num2 = 0;
        while (num2 <= num1)
        {
          if (num2 % 2 == 1)
          {
            if (stringBuilder.Length != 0)
              stringBuilder.Append("|");
            string str2 = Conversions.ToString(Math.Round(clsHDMFLibUtil.ConvertToDouble(strArray[checked (num2 * 2)]), 2));
            stringBuilder.Append(str2 + ",");
            string p_strValue = strArray[checked (num2 * 2 + 1)];
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Left, "MPa", false) == 0)
              p_strValue = Conversions.ToString(clsHDMFLibUtil.ConvertToDouble(p_strValue) / 1000000.0);
            stringBuilder.Append(p_strValue);
          }
          checked { ++num2; }
        }
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLibOutLog]GetPackDataFromOutData):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      return stringBuilder.ToString();
    }

    public static string GetCoolingTimeFromOutData(
      List<string> p_lst_strLogData,
      List<DataRow> p_lst_drPackPhase)
    {
      string empty = string.Empty;
      string p_strValue = string.Empty;
      try
      {
        List<string> source1 = p_lst_strLogData;
        System.Func<string, bool> predicate1;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I27\u002D0 != null)
        {
          // ISSUE: reference to a compiler-generated field
          predicate1 = clsHDMFLibOutLog._Closure\u0024__.\u0024I27\u002D0;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I27\u002D0 = predicate1 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("304460"));
        }
        if (source1.Any<string>(predicate1))
        {
          p_strValue = clsHDMFLibOutLog.GetDataFromOutData("304460", p_lst_strLogData);
          string pressFromOutData = clsHDMFLibOutLog.GetTimeOfInjPressFromOutData(p_lst_drPackPhase);
          p_strValue = Conversions.ToString(clsHDMFLibUtil.ConvertToDouble(p_strValue) - clsHDMFLibUtil.ConvertToDouble(pressFromOutData));
        }
        else
        {
          List<string> source2 = p_lst_strLogData;
          System.Func<string, bool> predicate2;
          // ISSUE: reference to a compiler-generated field
          if (clsHDMFLibOutLog._Closure\u0024__.\u0024I27\u002D1 != null)
          {
            // ISSUE: reference to a compiler-generated field
            predicate2 = clsHDMFLibOutLog._Closure\u0024__.\u0024I27\u002D1;
          }
          else
          {
            // ISSUE: reference to a compiler-generated field
            clsHDMFLibOutLog._Closure\u0024__.\u0024I27\u002D1 = predicate2 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("126109"));
          }
          if (source2.Any<string>(predicate2))
          {
            p_strValue = clsHDMFLibOutLog.GetDataFromOutData("126109", p_lst_strLogData);
            string pressFromOutData = clsHDMFLibOutLog.GetTimeOfInjPressFromOutData(p_lst_drPackPhase);
            p_strValue = Conversions.ToString(clsHDMFLibUtil.ConvertToDouble(p_strValue) - clsHDMFLibUtil.ConvertToDouble(pressFromOutData));
          }
          else
          {
            List<string> source3 = p_lst_strLogData;
            System.Func<string, bool> predicate3;
            // ISSUE: reference to a compiler-generated field
            if (clsHDMFLibOutLog._Closure\u0024__.\u0024I27\u002D2 != null)
            {
              // ISSUE: reference to a compiler-generated field
              predicate3 = clsHDMFLibOutLog._Closure\u0024__.\u0024I27\u002D2;
            }
            else
            {
              // ISSUE: reference to a compiler-generated field
              clsHDMFLibOutLog._Closure\u0024__.\u0024I27\u002D2 = predicate3 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("126108"));
            }
            if (source3.Any<string>(predicate3))
              p_strValue = clsHDMFLibOutLog.GetDataFromOutData("126108", p_lst_strLogData);
          }
        }
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLibOutLog]GetCoolingTimeFromOutData):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      return p_strValue;
    }

    public static string GetTimeOfInjPressFromOutData(List<DataRow> p_lst_drPackPhase)
    {
      string pressFromOutData = "";
      string str = "Inj Press";
      string columnName = "Time";
      try
      {
        if (p_lst_drPackPhase.Count != 0)
          str = !p_lst_drPackPhase[0].Table.Columns.Contains(str) ? "Pressure" : "Inj Press";
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(str, "", false) != 0)
        {
          try
          {
            foreach (DataRow dataRow in p_lst_drPackPhase)
            {
              if (clsHDMFLibUtil.ConvertToDouble(dataRow[str].ToString()) == 0.0)
              {
                pressFromOutData = Math.Round(clsHDMFLibUtil.ConvertToDouble(Conversions.ToString(dataRow[columnName])), 1).ToString();
                break;
              }
            }
          }
          finally
          {
            List<DataRow>.Enumerator enumerator;
            enumerator.Dispose();
          }
        }
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLibOutLog]GetTimeOfInjPressFromOutData):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      return pressFromOutData;
    }

    public static string GetValveDataFromOutData(
      List<string> p_lst_strLogData,
      string p_strMeshType)
    {
      // ISSUE: variable of a compiler-generated type
      clsHDMFLibOutLog._Closure\u0024__29\u002D0 closure290_1;
      // ISSUE: object of a compiler-generated type is created
      // ISSUE: variable of a compiler-generated type
      clsHDMFLibOutLog._Closure\u0024__29\u002D0 closure290_2 = new clsHDMFLibOutLog._Closure\u0024__29\u002D0(closure290_1);
      string empty = string.Empty;
      // ISSUE: reference to a compiler-generated field
      closure290_2.\u0024VB\u0024Local_strNode = string.Empty;
      StringBuilder stringBuilder1 = new StringBuilder();
      List<string> stringList1 = p_lst_strLogData;
      Predicate<string> match1;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I29\u002D0 != null)
      {
        // ISSUE: reference to a compiler-generated field
        match1 = clsHDMFLibOutLog._Closure\u0024__.\u0024I29\u002D0;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I29\u002D0 = match1 = (Predicate<string>) ([SpecialName] (Temp) => Temp.Equals("300001"));
      }
      int lastIndex1 = stringList1.FindLastIndex(match1);
      List<string> stringList2 = p_lst_strLogData;
      Predicate<string> match2;
      // ISSUE: reference to a compiler-generated field
      if (clsHDMFLibOutLog._Closure\u0024__.\u0024I29\u002D1 != null)
      {
        // ISSUE: reference to a compiler-generated field
        match2 = clsHDMFLibOutLog._Closure\u0024__.\u0024I29\u002D1;
      }
      else
      {
        // ISSUE: reference to a compiler-generated field
        clsHDMFLibOutLog._Closure\u0024__.\u0024I29\u002D1 = match2 = (Predicate<string>) ([SpecialName] (Temp) => Temp.Equals("304034"));
      }
      int lastIndex2 = stringList2.FindLastIndex(match2);
      List<int> intList = new List<int>();
      try
      {
        List<int> list;
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(p_strMeshType, "3D", false) == 0)
        {
          List<string> source1 = p_lst_strLogData;
          Func<string, int, VB\u0024AnonymousType_1<string, int>> selector1;
          // ISSUE: reference to a compiler-generated field
          if (clsHDMFLibOutLog._Closure\u0024__.\u0024I29\u002D2 != null)
          {
            // ISSUE: reference to a compiler-generated field
            selector1 = clsHDMFLibOutLog._Closure\u0024__.\u0024I29\u002D2;
          }
          else
          {
            // ISSUE: reference to a compiler-generated field
            clsHDMFLibOutLog._Closure\u0024__.\u0024I29\u002D2 = selector1 = [SpecialName] (strData, iIndex) => new
            {
              strData = strData,
              iIndex = iIndex
            };
          }
          IEnumerable<VB\u0024AnonymousType_1<string, int>> source2 = source1.Select(selector1);
          System.Func<VB\u0024AnonymousType_1<string, int>, bool> predicate;
          // ISSUE: reference to a compiler-generated field
          if (clsHDMFLibOutLog._Closure\u0024__.\u0024I29\u002D3 != null)
          {
            // ISSUE: reference to a compiler-generated field
            predicate = clsHDMFLibOutLog._Closure\u0024__.\u0024I29\u002D3;
          }
          else
          {
            // ISSUE: reference to a compiler-generated field
            clsHDMFLibOutLog._Closure\u0024__.\u0024I29\u002D3 = predicate = [SpecialName] (Temp) => Temp.strData.Equals("304137");
          }
          IEnumerable<VB\u0024AnonymousType_1<string, int>> source3 = source2.Where(predicate);
          System.Func<VB\u0024AnonymousType_1<string, int>, int> selector2;
          // ISSUE: reference to a compiler-generated field
          if (clsHDMFLibOutLog._Closure\u0024__.\u0024I29\u002D4 != null)
          {
            // ISSUE: reference to a compiler-generated field
            selector2 = clsHDMFLibOutLog._Closure\u0024__.\u0024I29\u002D4;
          }
          else
          {
            // ISSUE: reference to a compiler-generated field
            clsHDMFLibOutLog._Closure\u0024__.\u0024I29\u002D4 = selector2 = [SpecialName] (Temp) => Temp.iIndex;
          }
          list = source3.Select(selector2).ToList<int>();
        }
        else
        {
          List<string> source4 = p_lst_strLogData;
          Func<string, int, VB\u0024AnonymousType_1<string, int>> selector3;
          // ISSUE: reference to a compiler-generated field
          if (clsHDMFLibOutLog._Closure\u0024__.\u0024I29\u002D5 != null)
          {
            // ISSUE: reference to a compiler-generated field
            selector3 = clsHDMFLibOutLog._Closure\u0024__.\u0024I29\u002D5;
          }
          else
          {
            // ISSUE: reference to a compiler-generated field
            clsHDMFLibOutLog._Closure\u0024__.\u0024I29\u002D5 = selector3 = [SpecialName] (strData, iIndex) => new
            {
              strData = strData,
              iIndex = iIndex
            };
          }
          IEnumerable<VB\u0024AnonymousType_1<string, int>> source5 = source4.Select(selector3);
          System.Func<VB\u0024AnonymousType_1<string, int>, bool> predicate;
          // ISSUE: reference to a compiler-generated field
          if (clsHDMFLibOutLog._Closure\u0024__.\u0024I29\u002D6 != null)
          {
            // ISSUE: reference to a compiler-generated field
            predicate = clsHDMFLibOutLog._Closure\u0024__.\u0024I29\u002D6;
          }
          else
          {
            // ISSUE: reference to a compiler-generated field
            clsHDMFLibOutLog._Closure\u0024__.\u0024I29\u002D6 = predicate = [SpecialName] (Temp) => Temp.strData.Equals("93200");
          }
          IEnumerable<VB\u0024AnonymousType_1<string, int>> source6 = source5.Where(predicate);
          System.Func<VB\u0024AnonymousType_1<string, int>, int> selector4;
          // ISSUE: reference to a compiler-generated field
          if (clsHDMFLibOutLog._Closure\u0024__.\u0024I29\u002D7 != null)
          {
            // ISSUE: reference to a compiler-generated field
            selector4 = clsHDMFLibOutLog._Closure\u0024__.\u0024I29\u002D7;
          }
          else
          {
            // ISSUE: reference to a compiler-generated field
            clsHDMFLibOutLog._Closure\u0024__.\u0024I29\u002D7 = selector4 = [SpecialName] (Temp) => Temp.iIndex;
          }
          list = source6.Select(selector4).ToList<int>();
        }
        int num1 = checked (list.Count - 1);
        int index = 0;
        while (index <= num1)
        {
          // ISSUE: reference to a compiler-generated field
          closure290_2.\u0024VB\u0024Local_strNode = clsHDMFLibOutLog.GetDataFromOutData(list[index], p_lst_strLogData);
          if (stringBuilder1.Length != 0)
            stringBuilder1.Append("|");
          double num2;
          if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(p_strMeshType, "3D", false) == 0)
          {
            // ISSUE: reference to a compiler-generated field
            stringBuilder1.Append(closure290_2.\u0024VB\u0024Local_strNode);
            stringBuilder1.Append(",");
            if (lastIndex1 > list[index])
            {
              stringBuilder1.Append("0");
            }
            else
            {
              int num3 = list[index];
              int num4 = lastIndex1;
              int num5 = num3;
              while (num5 >= num4)
              {
                if (p_lst_strLogData[num5].Equals("304004"))
                {
                  string[] strArray = clsHDMFLibOutLog.GetDataFromOutData(num5, p_lst_strLogData).Split(',');
                  StringBuilder stringBuilder2 = stringBuilder1;
                  num2 = Math.Round(clsHDMFLibUtil.ConvertToDouble(strArray[0]), 3);
                  string str = num2.ToString();
                  stringBuilder2.Append(str);
                  break;
                }
                checked { num5 += -1; }
              }
            }
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated method
            // ISSUE: reference to a compiler-generated field
            int lastIndex3 = p_lst_strLogData.FindLastIndex(closure290_2.\u0024I8 == null ? (closure290_2.\u0024I8 = new Predicate<string>(closure290_2._Lambda\u0024__8)) : closure290_2.\u0024I8);
            stringBuilder1.Append(",");
            int num6 = lastIndex3;
            int num7 = lastIndex2;
            int num8 = num6;
            while (num8 >= num7)
            {
              if (p_lst_strLogData[num8].Equals("304033"))
              {
                string[] strArray = clsHDMFLibOutLog.GetDataFromOutData(num8, p_lst_strLogData).Split(',');
                StringBuilder stringBuilder3 = stringBuilder1;
                num2 = Math.Round(clsHDMFLibUtil.ConvertToDouble(strArray[0]), 3);
                string str = num2.ToString();
                stringBuilder3.Append(str);
                break;
              }
              checked { num8 += -1; }
            }
          }
          else
          {
            // ISSUE: reference to a compiler-generated field
            string[] strArray1 = closure290_2.\u0024VB\u0024Local_strNode.Split('|');
            // ISSUE: reference to a compiler-generated field
            closure290_2.\u0024VB\u0024Local_strNode = strArray1[0];
            // ISSUE: reference to a compiler-generated field
            stringBuilder1.Append(closure290_2.\u0024VB\u0024Local_strNode);
            string[] strArray2 = strArray1[1].Split(',');
            stringBuilder1.Append(",");
            StringBuilder stringBuilder4 = stringBuilder1;
            num2 = Math.Round(clsHDMFLibUtil.ConvertToDouble(strArray2[0]), 3);
            string str1 = num2.ToString();
            stringBuilder4.Append(str1);
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated field
            // ISSUE: reference to a compiler-generated method
            // ISSUE: reference to a compiler-generated field
            int lastIndex4 = p_lst_strLogData.FindLastIndex(closure290_2.\u0024I9 == null ? (closure290_2.\u0024I9 = new Predicate<string>(closure290_2._Lambda\u0024__9)) : closure290_2.\u0024I9);
            string p_strValue = p_lst_strLogData[checked (lastIndex4 + 2)];
            stringBuilder1.Append(",");
            StringBuilder stringBuilder5 = stringBuilder1;
            num2 = Math.Round(clsHDMFLibUtil.ConvertToDouble(p_strValue), 3);
            string str2 = num2.ToString();
            stringBuilder5.Append(str2);
          }
          checked { ++index; }
        }
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLibOutLog]GetValveDataFromOutData):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      return stringBuilder1.ToString();
    }

    public static string GetVPDataFromOutData(List<string> p_lst_strLogData)
    {
      string str = string.Empty;
      StringBuilder stringBuilder = new StringBuilder();
      try
      {
        List<string> source1 = p_lst_strLogData;
        System.Func<string, bool> predicate1;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I30\u002D0 != null)
        {
          // ISSUE: reference to a compiler-generated field
          predicate1 = clsHDMFLibOutLog._Closure\u0024__.\u0024I30\u002D0;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I30\u002D0 = predicate1 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("304005"));
        }
        if (source1.Any<string>(predicate1))
        {
          str = clsHDMFLibOutLog.GetDataFromOutData("304005", p_lst_strLogData);
        }
        else
        {
          List<string> source2 = p_lst_strLogData;
          System.Func<string, bool> predicate2;
          // ISSUE: reference to a compiler-generated field
          if (clsHDMFLibOutLog._Closure\u0024__.\u0024I30\u002D1 != null)
          {
            // ISSUE: reference to a compiler-generated field
            predicate2 = clsHDMFLibOutLog._Closure\u0024__.\u0024I30\u002D1;
          }
          else
          {
            // ISSUE: reference to a compiler-generated field
            clsHDMFLibOutLog._Closure\u0024__.\u0024I30\u002D1 = predicate2 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("93154"));
          }
          if (source2.Any<string>(predicate2))
            str = clsHDMFLibOutLog.GetDataFromOutData("93154", p_lst_strLogData);
        }
        string[] strArray = str.Split(',');
        if (strArray.Length > 2)
        {
          stringBuilder.Append(Math.Round(clsHDMFLibUtil.ConvertToDouble(strArray[0]), 1));
          stringBuilder.Append(",");
          stringBuilder.Append(Math.Round(clsHDMFLibUtil.ConvertToDouble(strArray[2]) / 1000000.0, 1));
        }
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLibOutLog]GetVPDataFromOutData):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      return stringBuilder.ToString();
    }

    public static string GetFilledDataFromOutData(
      List<string> p_lst_strLogData,
      string p_strMeshType)
    {
      string str = string.Empty;
      StringBuilder stringBuilder = new StringBuilder();
      try
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(p_strMeshType, "3D", false) == 0)
        {
          List<string> source = p_lst_strLogData;
          System.Func<string, bool> predicate;
          // ISSUE: reference to a compiler-generated field
          if (clsHDMFLibOutLog._Closure\u0024__.\u0024I31\u002D0 != null)
          {
            // ISSUE: reference to a compiler-generated field
            predicate = clsHDMFLibOutLog._Closure\u0024__.\u0024I31\u002D0;
          }
          else
          {
            // ISSUE: reference to a compiler-generated field
            clsHDMFLibOutLog._Closure\u0024__.\u0024I31\u002D0 = predicate = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("304007"));
          }
          string dataFromOutData;
          if (source.Any<string>(predicate))
          {
            dataFromOutData = clsHDMFLibOutLog.GetDataFromOutData("304007", p_lst_strLogData);
          }
          else
          {
            List<string> stringList = p_lst_strLogData;
            Predicate<string> match;
            // ISSUE: reference to a compiler-generated field
            if (clsHDMFLibOutLog._Closure\u0024__.\u0024I31\u002D1 != null)
            {
              // ISSUE: reference to a compiler-generated field
              match = clsHDMFLibOutLog._Closure\u0024__.\u0024I31\u002D1;
            }
            else
            {
              // ISSUE: reference to a compiler-generated field
              clsHDMFLibOutLog._Closure\u0024__.\u0024I31\u002D1 = match = (Predicate<string>) ([SpecialName] (Temp) => Temp.Equals("304006"));
            }
            dataFromOutData = clsHDMFLibOutLog.GetDataFromOutData(stringList.FindLastIndex(match), p_lst_strLogData);
          }
          string[] strArray = dataFromOutData.Split(',');
          if (strArray.Length > 1)
          {
            stringBuilder.Append(strArray[0]);
            stringBuilder.Append(",");
            stringBuilder.Append(strArray[1]);
          }
        }
        else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(p_strMeshType, "Dual", false) == 0)
        {
          List<string> source1 = p_lst_strLogData;
          System.Func<string, bool> predicate1;
          // ISSUE: reference to a compiler-generated field
          if (clsHDMFLibOutLog._Closure\u0024__.\u0024I31\u002D2 != null)
          {
            // ISSUE: reference to a compiler-generated field
            predicate1 = clsHDMFLibOutLog._Closure\u0024__.\u0024I31\u002D2;
          }
          else
          {
            // ISSUE: reference to a compiler-generated field
            clsHDMFLibOutLog._Closure\u0024__.\u0024I31\u002D2 = predicate1 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("93300"));
          }
          if (source1.Any<string>(predicate1))
          {
            str = clsHDMFLibOutLog.GetDataFromOutData("93300", p_lst_strLogData);
          }
          else
          {
            List<string> source2 = p_lst_strLogData;
            System.Func<string, bool> predicate2;
            // ISSUE: reference to a compiler-generated field
            if (clsHDMFLibOutLog._Closure\u0024__.\u0024I31\u002D3 != null)
            {
              // ISSUE: reference to a compiler-generated field
              predicate2 = clsHDMFLibOutLog._Closure\u0024__.\u0024I31\u002D3;
            }
            else
            {
              // ISSUE: reference to a compiler-generated field
              clsHDMFLibOutLog._Closure\u0024__.\u0024I31\u002D3 = predicate2 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("128271"));
            }
            if (source2.Any<string>(predicate2))
            {
              List<string> stringList1 = p_lst_strLogData;
              Predicate<string> match1;
              // ISSUE: reference to a compiler-generated field
              if (clsHDMFLibOutLog._Closure\u0024__.\u0024I31\u002D4 != null)
              {
                // ISSUE: reference to a compiler-generated field
                match1 = clsHDMFLibOutLog._Closure\u0024__.\u0024I31\u002D4;
              }
              else
              {
                // ISSUE: reference to a compiler-generated field
                clsHDMFLibOutLog._Closure\u0024__.\u0024I31\u002D4 = match1 = (Predicate<string>) ([SpecialName] (Temp) => Temp.Equals("90120"));
              }
              int lastIndex1 = stringList1.FindLastIndex(match1);
              List<string> stringList2 = p_lst_strLogData;
              Predicate<string> match2;
              // ISSUE: reference to a compiler-generated field
              if (clsHDMFLibOutLog._Closure\u0024__.\u0024I31\u002D5 != null)
              {
                // ISSUE: reference to a compiler-generated field
                match2 = clsHDMFLibOutLog._Closure\u0024__.\u0024I31\u002D5;
              }
              else
              {
                // ISSUE: reference to a compiler-generated field
                clsHDMFLibOutLog._Closure\u0024__.\u0024I31\u002D5 = match2 = (Predicate<string>) ([SpecialName] (Temp) => Temp.Equals("128271"));
              }
              int lastIndex2 = stringList2.FindLastIndex(match2);
              int num = checked (lastIndex1 - 1);
              int index = lastIndex2;
              while (index >= num)
              {
                if (p_lst_strLogData[index].Equals("93010"))
                {
                  str = clsHDMFLibOutLog.GetDataFromOutData("93010", p_lst_strLogData);
                  break;
                }
                checked { index += -1; }
              }
            }
          }
          string[] strArray = str.Split(',');
          if (strArray.Length > 3)
          {
            stringBuilder.Append(strArray[0]);
            stringBuilder.Append(",");
            stringBuilder.Append(strArray[3]);
          }
        }
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLibOutLog]GetFilledDataFromOutData):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      return stringBuilder.ToString();
    }

    public static string GetFillingClampFMaxFromOutData(List<string> p_lst_strLogData)
    {
      string p_strValue = string.Empty;
      try
      {
        List<string> source = p_lst_strLogData;
        System.Func<string, bool> predicate;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I32\u002D0 != null)
        {
          // ISSUE: reference to a compiler-generated field
          predicate = clsHDMFLibOutLog._Closure\u0024__.\u0024I32\u002D0;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I32\u002D0 = predicate = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("41300"));
        }
        if (source.Any<string>(predicate))
        {
          p_strValue = clsHDMFLibOutLog.GetDataFromOutData("41300", p_lst_strLogData);
          p_strValue = Conversions.ToString(Math.Round(clsHDMFLibUtil.ConvertToDouble(p_strValue) * 1.019399 / 10000.0, 0));
        }
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLibOutLog]GetFillingClampFMaxFromOutData):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      return p_strValue;
    }

    public static string GetPackingClampFMaxFromOutData(List<string> p_lst_strLogData)
    {
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      try
      {
        List<string> source = p_lst_strLogData;
        System.Func<string, bool> predicate;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I33\u002D0 != null)
        {
          // ISSUE: reference to a compiler-generated field
          predicate = clsHDMFLibOutLog._Closure\u0024__.\u0024I33\u002D0;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I33\u002D0 = predicate = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("41310"));
        }
        if (source.Any<string>(predicate))
          empty2 = Conversions.ToString(Math.Round(clsHDMFLibUtil.ConvertToDouble(clsHDMFLibOutLog.GetDataFromOutData("41310", p_lst_strLogData).Split(',')[0]) * 1.019399 / 10000.0, 1));
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLibOutLog]GetPackingClampFMaxFromOutData):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      return empty2;
    }

    public static string GetPackingPartMassFromOutData(List<string> p_lst_strLogData)
    {
      string partMassFromOutData = string.Empty;
      try
      {
        List<string> source1 = p_lst_strLogData;
        System.Func<string, bool> predicate1;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I34\u002D0 != null)
        {
          // ISSUE: reference to a compiler-generated field
          predicate1 = clsHDMFLibOutLog._Closure\u0024__.\u0024I34\u002D0;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I34\u002D0 = predicate1 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("304139"));
        }
        if (source1.Any<string>(predicate1))
        {
          partMassFromOutData = clsHDMFLibOutLog.GetDataFromOutData("304139", p_lst_strLogData);
        }
        else
        {
          List<string> source2 = p_lst_strLogData;
          System.Func<string, bool> predicate2;
          // ISSUE: reference to a compiler-generated field
          if (clsHDMFLibOutLog._Closure\u0024__.\u0024I34\u002D1 != null)
          {
            // ISSUE: reference to a compiler-generated field
            predicate2 = clsHDMFLibOutLog._Closure\u0024__.\u0024I34\u002D1;
          }
          else
          {
            // ISSUE: reference to a compiler-generated field
            clsHDMFLibOutLog._Closure\u0024__.\u0024I34\u002D1 = predicate2 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("300370"));
          }
          if (source2.Any<string>(predicate2))
          {
            partMassFromOutData = clsHDMFLibOutLog.GetDataFromOutData("300370", p_lst_strLogData);
          }
          else
          {
            List<string> source3 = p_lst_strLogData;
            System.Func<string, bool> predicate3;
            // ISSUE: reference to a compiler-generated field
            if (clsHDMFLibOutLog._Closure\u0024__.\u0024I34\u002D2 != null)
            {
              // ISSUE: reference to a compiler-generated field
              predicate3 = clsHDMFLibOutLog._Closure\u0024__.\u0024I34\u002D2;
            }
            else
            {
              // ISSUE: reference to a compiler-generated field
              clsHDMFLibOutLog._Closure\u0024__.\u0024I34\u002D2 = predicate3 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("40220"));
            }
            if (source3.Any<string>(predicate3))
              partMassFromOutData = clsHDMFLibOutLog.GetDataFromOutData("40220", p_lst_strLogData);
          }
        }
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(partMassFromOutData, string.Empty, false) != 0)
          partMassFromOutData = Conversions.ToString(Math.Round(clsHDMFLibUtil.ConvertToDouble(partMassFromOutData) * 1000.0, 1));
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLibOutLog]GetPackingPartMassFromOutData):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      return partMassFromOutData;
    }

    public static string GetPackingVolShkDataFromOutData(List<string> p_lst_strLogData)
    {
      StringBuilder stringBuilder = new StringBuilder();
      string empty = string.Empty;
      try
      {
        List<string> source1 = p_lst_strLogData;
        System.Func<string, bool> predicate1;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I35\u002D0 != null)
        {
          // ISSUE: reference to a compiler-generated field
          predicate1 = clsHDMFLibOutLog._Closure\u0024__.\u0024I35\u002D0;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I35\u002D0 = predicate1 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("40700"));
        }
        if (source1.Any<string>(predicate1))
        {
          string dataFromOutData = clsHDMFLibOutLog.GetDataFromOutData("40700", p_lst_strLogData);
          stringBuilder.Append(Math.Round(clsHDMFLibUtil.ConvertToDouble(dataFromOutData), 1));
        }
        List<string> source2 = p_lst_strLogData;
        System.Func<string, bool> predicate2;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I35\u002D1 != null)
        {
          // ISSUE: reference to a compiler-generated field
          predicate2 = clsHDMFLibOutLog._Closure\u0024__.\u0024I35\u002D1;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I35\u002D1 = predicate2 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("40702"));
        }
        if (source2.Any<string>(predicate2))
        {
          if (stringBuilder.Length != 0)
            stringBuilder.Append("|");
          string dataFromOutData = clsHDMFLibOutLog.GetDataFromOutData("40702", p_lst_strLogData);
          stringBuilder.Append(Math.Round(clsHDMFLibUtil.ConvertToDouble(dataFromOutData), 1));
        }
        List<string> source3 = p_lst_strLogData;
        System.Func<string, bool> predicate3;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I35\u002D2 != null)
        {
          // ISSUE: reference to a compiler-generated field
          predicate3 = clsHDMFLibOutLog._Closure\u0024__.\u0024I35\u002D2;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I35\u002D2 = predicate3 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("40704"));
        }
        if (source3.Any<string>(predicate3))
        {
          if (stringBuilder.Length != 0)
            stringBuilder.Append("|");
          string dataFromOutData = clsHDMFLibOutLog.GetDataFromOutData("40704", p_lst_strLogData);
          stringBuilder.Append(Math.Round(clsHDMFLibUtil.ConvertToDouble(dataFromOutData), 1));
        }
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLibOutLog]GetPackingVolShkDataFromOutData):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      return stringBuilder.ToString();
    }

    public static string GetDeflectionDataFromOutData(List<string> p_lst_strLogData)
    {
      string empty = string.Empty;
      StringBuilder stringBuilder = new StringBuilder();
      List<int> intList = new List<int>();
      try
      {
        List<string> source1 = p_lst_strLogData;
        Func<string, int, VB\u0024AnonymousType_1<string, int>> selector1;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I36\u002D0 != null)
        {
          // ISSUE: reference to a compiler-generated field
          selector1 = clsHDMFLibOutLog._Closure\u0024__.\u0024I36\u002D0;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I36\u002D0 = selector1 = [SpecialName] (strData, iIndex) => new
          {
            strData = strData,
            iIndex = iIndex
          };
        }
        IEnumerable<VB\u0024AnonymousType_1<string, int>> source2 = source1.Select(selector1);
        System.Func<VB\u0024AnonymousType_1<string, int>, bool> predicate;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I36\u002D1 != null)
        {
          // ISSUE: reference to a compiler-generated field
          predicate = clsHDMFLibOutLog._Closure\u0024__.\u0024I36\u002D1;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I36\u002D1 = predicate = [SpecialName] (Temp) => Temp.strData.Equals("201040");
        }
        IEnumerable<VB\u0024AnonymousType_1<string, int>> source3 = source2.Where(predicate);
        System.Func<VB\u0024AnonymousType_1<string, int>, int> selector2;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I36\u002D2 != null)
        {
          // ISSUE: reference to a compiler-generated field
          selector2 = clsHDMFLibOutLog._Closure\u0024__.\u0024I36\u002D2;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I36\u002D2 = selector2 = [SpecialName] (Temp) => Temp.iIndex;
        }
        List<int> list = source3.Select(selector2).ToList<int>();
        int num = checked (list.Count - 1);
        int index = 0;
        while (index <= num)
        {
          if (index % 3 == 0 & stringBuilder.Length != 0)
            stringBuilder.Append("|");
          string[] strArray = clsHDMFLibOutLog.GetDataFromOutData(list[index], p_lst_strLogData).Split('|')[1].Split(',');
          stringBuilder.Append(Math.Round(clsHDMFLibUtil.ConvertToDouble(strArray[1]), 4));
          stringBuilder.Append(",");
          stringBuilder.Append(Math.Round(clsHDMFLibUtil.ConvertToDouble(strArray[3]), 4));
          stringBuilder.Append(",");
          checked { ++index; }
        }
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLibOutLog]GetPackingPartMassFromOutData):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      return stringBuilder.ToString();
    }

    public static string GetCycleTimeFromOutData(List<DataRow> p_lst_drAllPhase)
    {
      string empty = string.Empty;
      double num = 0.0;
      string cycleTimeFromOutData;
      try
      {
        if (!p_lst_drAllPhase[0].Table.Columns.Contains("Frozen"))
        {
          cycleTimeFromOutData = empty;
          goto label_13;
        }
        else
        {
          try
          {
            foreach (DataRow dataRow in p_lst_drAllPhase)
            {
              if (Convert.ToDouble(RuntimeHelpers.GetObjectValue(dataRow["Frozen"])) == 100.0)
              {
                num = Conversions.ToDouble(dataRow["Time"]);
                break;
              }
            }
          }
          finally
          {
            List<DataRow>.Enumerator enumerator;
            enumerator.Dispose();
          }
          if (num == 0.0)
            num = Conversions.ToDouble(p_lst_drAllPhase.Last<DataRow>()["Time"]);
          empty = Conversions.ToString(Math.Round(num * 0.9, 1));
        }
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLib]GetCycleTimeFromOutData):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      cycleTimeFromOutData = empty;
label_13:
      return cycleTimeFromOutData;
    }

    public static string GetPartVolumeFromOutData(List<string> p_lst_strLogData)
    {
      string p_strValue = string.Empty;
      try
      {
        List<string> source1 = p_lst_strLogData;
        System.Func<string, bool> predicate1;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I38\u002D0 != null)
        {
          // ISSUE: reference to a compiler-generated field
          predicate1 = clsHDMFLibOutLog._Closure\u0024__.\u0024I38\u002D0;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I38\u002D0 = predicate1 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("300320"));
        }
        if (source1.Any<string>(predicate1))
        {
          p_strValue = clsHDMFLibOutLog.GetDataFromOutData("300320", p_lst_strLogData);
        }
        else
        {
          List<string> source2 = p_lst_strLogData;
          System.Func<string, bool> predicate2;
          // ISSUE: reference to a compiler-generated field
          if (clsHDMFLibOutLog._Closure\u0024__.\u0024I38\u002D1 != null)
          {
            // ISSUE: reference to a compiler-generated field
            predicate2 = clsHDMFLibOutLog._Closure\u0024__.\u0024I38\u002D1;
          }
          else
          {
            // ISSUE: reference to a compiler-generated field
            clsHDMFLibOutLog._Closure\u0024__.\u0024I38\u002D1 = predicate2 = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("39322"));
          }
          if (source2.Any<string>(predicate2))
            p_strValue = clsHDMFLibOutLog.GetDataFromOutData("39322", p_lst_strLogData);
        }
        if (p_strValue.ToLower().Contains("e-") | p_strValue.ToLower().Contains("e+"))
          p_strValue = clsHDMFLib.ParsingAnalysisValue(p_strValue).ToString();
        p_strValue = Conversions.ToString(Math.Round(clsHDMFLibUtil.ConvertToDouble(p_strValue) * 1000000.0, 4));
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLibOutLog]GetPartVolumeFromOutData):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      return p_strValue;
    }

    public static string GetPressureMaxFromOutData(List<string> p_lst_strLogData)
    {
      string pressureMaxFromOutData = string.Empty;
      try
      {
        List<string> source = p_lst_strLogData;
        System.Func<string, bool> predicate;
        // ISSUE: reference to a compiler-generated field
        if (clsHDMFLibOutLog._Closure\u0024__.\u0024I39\u002D0 != null)
        {
          // ISSUE: reference to a compiler-generated field
          predicate = clsHDMFLibOutLog._Closure\u0024__.\u0024I39\u002D0;
        }
        else
        {
          // ISSUE: reference to a compiler-generated field
          clsHDMFLibOutLog._Closure\u0024__.\u0024I39\u002D0 = predicate = (System.Func<string, bool>) ([SpecialName] (Temp) => Temp.Equals("41400"));
        }
        if (source.Any<string>(predicate))
        {
          pressureMaxFromOutData = clsHDMFLibOutLog.GetDataFromOutData("41400", p_lst_strLogData);
          pressureMaxFromOutData = Conversions.ToString(Math.Round(clsHDMFLibUtil.ConvertToDouble(pressureMaxFromOutData.Split(',')[0]) / 1000000.0, 1));
        }
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        HDLog.Instance().Error((object) ("Exception([clsHDMFLibOutLog]GetPressureMaxFromOutData):" + exception.Message));
        ProjectData.ClearProjectError();
      }
      return pressureMaxFromOutData;
    }
  }
}
