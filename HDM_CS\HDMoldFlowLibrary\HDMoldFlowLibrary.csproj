<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!--Project was exported from assembly: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMoldFlowLibrary.dll-->
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{03592564-B403-480B-A7E0-11ED67313D67}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AssemblyName>HDMoldFlowLibrary</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <ApplicationVersion>2.24.0.0</ApplicationVersion>
    <FileAlignment>512</FileAlignment>
    <RootNamespace>HDMoldFlowLibrary</RootNamespace>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="HDLog4Net">
      <HintPath>lib\HDLog4Net.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="clsHDMFLib.cs" />
    <Compile Include="clsHDMFLibParams.cs" />
    <Compile Include="clsPlotOption.cs" />
    <Compile Include="PlotColorType.cs" />
    <Compile Include="PlotColor.cs" />
    <Compile Include="Scaling.cs" />
    <Compile Include="clsHDMFLibDefine.cs" />
    <Compile Include="clsHDMFLibOutLog.cs" />
    <Compile Include="clsHDMFLibSL.cs" />
    <Compile Include="clsHDMFLibUtil.cs" />
    <Compile Include="My\MyApplication.cs" />
    <Compile Include="My\MyComputer.cs" />
    <Compile Include="My\MyProject.cs" />
    <Compile Include="My\MySettings.cs" />
    <Compile Include="My\MySettingsProperty.cs" />
    <Compile Include="My\Resources\Resources.cs" />
    <Compile Include="AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Resources.resx" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>