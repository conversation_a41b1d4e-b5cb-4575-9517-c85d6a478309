{"version": 3, "targets": {".NETFramework,Version=v4.5": {"SevenZipExtractor/1.0.19": {"type": "project", "framework": ".NETFramework,Version=v4.5", "compile": {"bin/placeholder/SevenZipExtractor.dll": {}}, "runtime": {"bin/placeholder/SevenZipExtractor.dll": {}}}}, ".NETFramework,Version=v4.5/win-x86": {"SevenZipExtractor/1.0.19": {"type": "project", "framework": ".NETFramework,Version=v4.5", "compile": {"bin/placeholder/SevenZipExtractor.dll": {}}, "runtime": {"bin/placeholder/SevenZipExtractor.dll": {}}}}}, "libraries": {"SevenZipExtractor/1.0.19": {"type": "project", "path": "../SevenZipExtractor/SevenZipExtractor.csproj", "msbuildProject": "../SevenZipExtractor/SevenZipExtractor.csproj"}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.5": ["SevenZipExtractor >= 1.0.19"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Moldflow\\SevenZipExtractor\\Example\\Example.csproj", "projectName": "ConsoleApplication86", "projectPath": "C:\\Moldflow\\SevenZipExtractor\\Example\\Example.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Moldflow\\SevenZipExtractor\\Example\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net45"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net45": {"targetAlias": "net45", "projectReferences": {"C:\\Moldflow\\SevenZipExtractor\\SevenZipExtractor\\SevenZipExtractor.csproj": {"projectPath": "C:\\Moldflow\\SevenZipExtractor\\SevenZipExtractor\\SevenZipExtractor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net45": {"targetAlias": "net45", "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.204\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}}