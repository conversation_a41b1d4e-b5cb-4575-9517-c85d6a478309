#!/usr/bin/env python3
"""
Test different COM Program IDs for Moldflow Synergy.
"""

import win32com.client
import pythoncom

print("Testing different COM Program IDs for Moldflow Synergy...")

# List of possible Program IDs to try
prog_ids = [
    "Synergy.Synergy",
    "Scandium.Synergy", 
    "Synergy.Application",
    "Scandium.Application",
    "Moldflow.Synergy",
    "Moldflow.Application",
    "Autodesk.Synergy",
    "Autodesk.Moldflow.Synergy",
    "MoldflowSynergy.Application",
    "Synergy2025.Application",
    "Synergy2024.Application",
]

successful_connections = []

for prog_id in prog_ids:
    try:
        print(f"Trying {prog_id}...", end=" ")
        synergy = win32com.client.Dispatch(prog_id)
        print("✅ SUCCESS!")
        successful_connections.append((prog_id, synergy))
        
        # Try to get some basic info
        try:
            version = synergy.Version
            print(f"   Version: {version}")
        except:
            print("   Could not get version")
            
        try:
            edition = synergy.Edition
            print(f"   Edition: {edition}")
        except:
            print("   Could not get edition")
            
    except Exception as e:
        print(f"❌ Failed: {str(e)}")

print(f"\nFound {len(successful_connections)} working connections:")
for prog_id, synergy in successful_connections:
    print(f"  - {prog_id}")

# Try to find running COM objects
print("\nLooking for running COM objects...")
try:
    rot = pythoncom.GetRunningObjectTable()
    enum_moniker = rot.EnumRunning()
    
    moldflow_objects = []
    for moniker in enum_moniker:
        try:
            display_name = moniker.GetDisplayName(None, None)
            if any(keyword in display_name.lower() for keyword in ['moldflow', 'synergy', 'scandium']):
                moldflow_objects.append(display_name)
        except:
            continue
    
    if moldflow_objects:
        print("Found Moldflow-related COM objects:")
        for obj in moldflow_objects:
            print(f"  - {obj}")
    else:
        print("No Moldflow-related COM objects found in ROT")
        
except Exception as e:
    print(f"Error accessing Running Object Table: {e}")

print("\nTest completed!")
