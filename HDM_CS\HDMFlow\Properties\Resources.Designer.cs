﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.Properties.Resources
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;

namespace HDMoldFlow.Properties
{
  [GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
  [DebuggerNonUserCode]
  [CompilerGenerated]
  internal class Resources
  {
    private static ResourceManager resourceMan;
    private static CultureInfo resourceCulture;

    internal Resources()
    {
    }

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    internal static ResourceManager ResourceManager
    {
      get
      {
        if (HDMoldFlow.Properties.Resources.resourceMan == null)
          HDMoldFlow.Properties.Resources.resourceMan = new ResourceManager("HDMoldFlow.Properties.Resources", typeof (HDMoldFlow.Properties.Resources).Assembly);
        return HDMoldFlow.Properties.Resources.resourceMan;
      }
    }

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    internal static CultureInfo Culture
    {
      get => HDMoldFlow.Properties.Resources.resourceCulture;
      set => HDMoldFlow.Properties.Resources.resourceCulture = value;
    }

    internal static Bitmap Add => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (Add), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap AllProduct => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (AllProduct), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap Analysis => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (Analysis), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap Backup => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (Backup), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap Close => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (Close), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap Copy => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (Copy), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap Datas => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (Datas), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap Del => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (Del), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap Edit => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (Edit), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap Export => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (Export), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap Gate1 => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (Gate1), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap Gate2 => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (Gate2), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap HDMFlow => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (HDMFlow), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap HDSolutions => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (HDSolutions), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap Help => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (Help), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap Import => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (Import), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap Loading => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (Loading), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap NewProject => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (NewProject), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap OK => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (OK), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap OpenProject => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (OpenProject), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap Product => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (Product), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap Rotate => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (Rotate), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap Setting => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (Setting), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap STD3D => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (STD3D), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap STDDual => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (STDDual), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap Update => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (Update), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap User => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (User), HDMoldFlow.Properties.Resources.resourceCulture);

    internal static Bitmap Write => (Bitmap) HDMoldFlow.Properties.Resources.ResourceManager.GetObject(nameof (Write), HDMoldFlow.Properties.Resources.resourceCulture);
  }
}
