﻿// Decompiled with JetBrains decompiler
// Type: HDMFAI.Properties.Resources
// Assembly: HDMFAI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: A66A47CE-D63B-4ACB-B9A4-A4F118252C4E
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFAI.dll

using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;

namespace HDMFAI.Properties
{
  [GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
  [DebuggerNonUserCode]
  [CompilerGenerated]
  internal class Resources
  {
    private static ResourceManager resourceMan;
    private static CultureInfo resourceCulture;

    internal Resources()
    {
    }

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    internal static ResourceManager ResourceManager
    {
      get
      {
        if (HDMFAI.Properties.Resources.resourceMan == null)
          HDMFAI.Properties.Resources.resourceMan = new ResourceManager("HDMFAI.Properties.Resources", typeof (HDMFAI.Properties.Resources).Assembly);
        return HDMFAI.Properties.Resources.resourceMan;
      }
    }

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    internal static CultureInfo Culture
    {
      get => HDMFAI.Properties.Resources.resourceCulture;
      set => HDMFAI.Properties.Resources.resourceCulture = value;
    }
  }
}
