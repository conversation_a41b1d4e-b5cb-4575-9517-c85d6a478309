﻿// Decompiled with JetBrains decompiler
// Type: HDMoldFlow.clsMesh
// Assembly: HDMFlow, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 93A48DE9-AA41-426F-88C8-6A81E6E37BA9
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFlow.exe

using HDLog4Net;
using HDMoldFlowLibrary;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading;
using System.Windows.Forms;

namespace HDMoldFlow
{
  public class clsMesh
  {
    private static frmUpdateMesh m_frmUpdateMesh;
    public static BackgroundWorker m_bwProgress;
    private static Form m_frmParent;
    private static string m_strMesh;
    private static DataRow[] m_arr_drStudy;

    public static void ShowUpdateMesh(DataRow[] p_arr_drStudy, Form p_frmParent, string p_strMesh)
    {
      clsMesh.m_frmParent = p_frmParent;
      clsMesh.m_arr_drStudy = p_arr_drStudy;
      clsMesh.m_strMesh = p_strMesh;
      clsMesh.m_bwProgress = new BackgroundWorker();
      clsMesh.m_bwProgress.WorkerReportsProgress = true;
      clsMesh.m_bwProgress.WorkerSupportsCancellation = true;
      clsMesh.m_bwProgress.DoWork += new DoWorkEventHandler(clsMesh.M_bcw_DoWork);
      clsMesh.m_bwProgress.RunWorkerAsync();
      bool isDisposed = false;
      while (true)
      {
        if (clsMesh.m_frmUpdateMesh != null)
        {
          clsMesh.m_frmUpdateMesh.InvokeIFNeeded((Action) (() => isDisposed = clsMesh.m_frmUpdateMesh.IsDisposed));
          if (!isDisposed)
            break;
        }
        Thread.Sleep(10);
      }
    }

    private static void M_bcw_DoWork(object sender, DoWorkEventArgs e)
    {
      clsMesh.m_frmUpdateMesh = new frmUpdateMesh();
      clsMesh.m_frmUpdateMesh.m_frmParent = clsMesh.m_frmParent;
      clsMesh.m_frmUpdateMesh.m_strMesh = clsMesh.m_strMesh;
      clsMesh.m_frmUpdateMesh.m_arr_drStudy = clsMesh.m_arr_drStudy;
      Application.Run((Form) clsMesh.m_frmUpdateMesh);
    }

    public static void CloseUpdateMesh()
    {
      if (clsMesh.m_bwProgress != null)
      {
        clsMesh.m_bwProgress.CancelAsync();
        clsMesh.m_frmUpdateMesh.InvokeIFNeeded((Action) (() => clsMesh.m_frmUpdateMesh.Close()));
        clsMesh.m_frmUpdateMesh.Dispose();
      }
      clsMesh.m_bwProgress = (BackgroundWorker) null;
    }

    public static void UpdateDualMesh(DataRow[] p_arr_drStudy)
    {
      List<string> stringList1 = new List<string>();
      try
      {
        List<string> stringList2 = new List<string>();
        List<Thread> source = new List<Thread>();
        for (int index = 0; index < p_arr_drStudy.Length; ++index)
        {
          string str = "";
          clsHDMFLib.OpenStudy(p_arr_drStudy[index]["Name"].ToString());
          if (!clsHDMFLib.ExistDualDomainMesh())
          {
            JToken t1 = clsSCM.GetJobData().FirstOrDefault<JToken>();
            double globalEdgeLength = clsHDMFLib.GetGlobalEdgeLength();
            double p_dblSolidEdgeLength = globalEdgeLength + clsUtill.ConvertToDouble(clsDefine.g_dicMesh["SolidLength"]);
            double p_dblSurfaceEdgeLength = globalEdgeLength + clsUtill.ConvertToDouble(clsDefine.g_dicMesh["SurfaceLength"]);
            clsHDMFLib.CreateDualMesh(p_arr_drStudy[index]["Name"].ToString(), p_dblSolidEdgeLength, p_dblSurfaceEdgeLength, clsUtill.ConvertToDouble(clsDefine.g_dicMesh["SolidScale"]), "Ignore contact ", true);
            string studyName = clsHDMFLib.GetStudyName();
            JToken jtoken = (JToken) null;
            JToken t2;
            while (true)
            {
              t2 = clsSCM.GetJobData().FirstOrDefault<JToken>();
              if (t2 != null)
              {
                if (JToken.DeepEquals(t1, t2) || !(t2[(object) "payload"][(object) "name"].ToString() == studyName))
                  Thread.Sleep(100);
                else
                  break;
              }
              else
                goto label_8;
            }
            jtoken = t2;
label_8:
            if (jtoken != null)
              str = jtoken[(object) "payload"][(object) "xref"].ToString();
            if (t2 != null)
            {
              Thread thread = new Thread(new ParameterizedThreadStart(clsMesh.ThdWork));
              thread.IsBackground = true;
              thread.Start((object) (p_arr_drStudy[index]["Name"].ToString() + "|" + str));
              source.Add(thread);
            }
            else
            {
              clsHDMFLib.GetMeshOutFile(clsDefine.g_diProject.FullName, p_arr_drStudy[index]["FName"].ToString());
              Thread thread = new Thread(new ParameterizedThreadStart(clsMesh.ThdMeshCheck));
              thread.IsBackground = true;
              thread.Start((object) (p_arr_drStudy[index]["Name"].ToString() + "|" + p_arr_drStudy[index]["FName"].ToString()));
              source.Add(thread);
            }
          }
        }
        do
        {
          Thread.Sleep(1000);
        }
        while (source.Cast<Thread>().Any<Thread>((System.Func<Thread, bool>) (Temp => Temp.IsAlive)));
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsMesh]UpdateDualMesh):" + ex.Message));
      }
    }

    public static void Update3DMesh(DataRow[] p_arr_drStudy, string[] p_arr_strGlobalEdgeLength)
    {
      List<DataRow> dataRowList = new List<DataRow>();
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      string empty3 = string.Empty;
      try
      {
        List<string> stringList = new List<string>();
        List<Thread> source = new List<Thread>();
        for (int index = 0; index < p_arr_drStudy.Length; ++index)
        {
          string empty4 = string.Empty;
          DataRow dataRow = p_arr_drStudy[index];
          clsHDMFLib.OpenStudy(dataRow["Name"].ToString());
          JToken t1 = clsSCM.GetJobData().FirstOrDefault<JToken>();
          clsHDMFLib.Create3DMesh(clsUtill.ConvertToDouble(p_arr_strGlobalEdgeLength[index]), clsUtill.ConvertToDouble(clsDefine.g_dicMesh["TetraLayer"]), true);
          Thread.Sleep(1000);
          string studyName = clsHDMFLib.GetStudyName();
          JToken jtoken = (JToken) null;
          JToken t2;
          while (true)
          {
            t2 = clsSCM.GetJobData().FirstOrDefault<JToken>();
            if (t2 != null)
            {
              if (JToken.DeepEquals(t1, t2) || !(t2[(object) "payload"][(object) "name"].ToString() == studyName))
                Thread.Sleep(100);
              else
                break;
            }
            else
              goto label_7;
          }
          jtoken = t2;
label_7:
          if (jtoken != null)
          {
            empty4 = jtoken[(object) "payload"][(object) "xref"].ToString();
            Thread thread = new Thread(new ParameterizedThreadStart(clsMesh.ThdWork));
            thread.IsBackground = true;
            thread.Start((object) (dataRow["Name"].ToString() + "|" + empty4));
            source.Add(thread);
          }
          else
          {
            Thread thread = new Thread(new ParameterizedThreadStart(clsMesh.ThdMeshCheck));
            thread.IsBackground = true;
            thread.Start((object) (dataRow["Name"].ToString() + "|" + dataRow["FName"].ToString()));
            source.Add(thread);
          }
          stringList.Add(p_arr_drStudy[index]["Name"].ToString() + "|" + empty4);
        }
        do
        {
          Thread.Sleep(1000);
        }
        while (source.Cast<Thread>().Any<Thread>((System.Func<Thread, bool>) (Temp => Temp.IsAlive)));
        for (int index = 0; index < stringList.Count; ++index)
        {
          DataRow dataRow = p_arr_drStudy[index];
          string p_strJobID = stringList[index].Split('|')[1];
          string str = !(p_strJobID != string.Empty) ? clsHDMFLibOutLog.GetMeshStatus(clsHDMFLib.GetMeshOutFile(clsDefine.g_diProject.FullName, dataRow["FName"].ToString())) : clsSCM.GetJobStatus(p_strJobID);
          dataRow["Check"] = (object) str.Contains("COMPLETE");
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsMesh]Update3DMesh):" + ex.Message));
      }
    }

    public static void ThdWork(object p_objStudy)
    {
      string strStudy = p_objStudy.ToString().Split('|')[0];
      string p_strJobID = p_objStudy.ToString().Split('|')[1];
      string strProg = "";
      string strJobStatus = "";
      while (true)
      {
        try
        {
          if (strJobStatus.Contains("FAILED") || strJobStatus.Contains("CANCELED") || strJobStatus.Contains("COMPLETE") || p_strJobID == "")
          {
            if (p_strJobID == "")
            {
              clsMesh.m_frmUpdateMesh.InvokeIFNeeded((Action) (() => clsMesh.m_frmUpdateMesh.UpdateStatus(strStudy, "COMPLETED", "100")));
              break;
            }
            if (!strJobStatus.Contains("FAILED") && !strJobStatus.Contains("CANCELED"))
              break;
            clsMesh.m_frmUpdateMesh.InvokeIFNeeded((Action) (() => clsMesh.m_frmUpdateMesh.ChangeProgressColor(strStudy, clsUtill.ProgressBarColor.Red)));
            break;
          }
          strJobStatus = clsSCM.GetJobStatus(p_strJobID);
          if (strJobStatus != null && strJobStatus != "")
          {
            strProg = "";
            if (strJobStatus.Contains("INPROGRESS"))
            {
              strProg = strJobStatus.Replace("INPROGRESS(", "");
              strProg = strProg.Replace(")", "");
            }
            clsMesh.m_frmUpdateMesh.InvokeIFNeeded((Action) (() => clsMesh.m_frmUpdateMesh.UpdateStatus(strStudy, strJobStatus, strProg)));
          }
        }
        catch
        {
          Thread.Sleep(1000);
        }
        Thread.Sleep(1000);
      }
    }

    public static void ThdMeshCheck(object p_objStudy)
    {
      int num = 0;
      string strStudy = p_objStudy.ToString().Split('|')[0];
      string p_strStudyFileName = p_objStudy.ToString().Split('|')[1];
      string strJobStatus = string.Empty;
      string str = string.Empty;
      string p_strMeshFile = string.Empty;
      string strProg = string.Empty;
      while (true)
      {
        try
        {
          if (string.IsNullOrEmpty(p_strMeshFile))
            p_strMeshFile = clsHDMFLib.GetMeshOutFile(clsDefine.g_diProject.FullName, p_strStudyFileName);
          if (strJobStatus.Contains("COMPLETE"))
          {
            clsMesh.m_frmUpdateMesh.InvokeIFNeeded((Action) (() => clsMesh.m_frmUpdateMesh.UpdateStatus(strStudy, "COMPLETED", "100")));
            break;
          }
          if (num > 30 || strJobStatus.Contains("FAILED"))
          {
            clsMesh.m_frmUpdateMesh.InvokeIFNeeded((Action) (() => clsMesh.m_frmUpdateMesh.ChangeProgressColor(strStudy, clsUtill.ProgressBarColor.Red)));
            break;
          }
          strJobStatus = clsHDMFLibOutLog.GetMeshStatus(p_strMeshFile);
          if (strJobStatus != null && strJobStatus != "")
          {
            strProg = "";
            if (strJobStatus.Contains("INPROGRESS"))
            {
              strProg = strJobStatus.Replace("INPROGRESS(", "");
              strProg = strProg.Replace(")", "");
            }
            if (strProg != str)
            {
              clsMesh.m_frmUpdateMesh.InvokeIFNeeded((Action) (() => clsMesh.m_frmUpdateMesh.UpdateStatus(strStudy, strJobStatus, strProg)));
              str = strProg;
              num = 0;
            }
            else
              ++num;
          }
        }
        catch
        {
          Thread.Sleep(1000);
        }
        Thread.Sleep(1000);
      }
    }

    public static void ApplyProcessDBToStudy(DataRow p_drProcessDB)
    {
      DataRow p_drMaterial = (DataRow) null;
      try
      {
        if (p_drProcessDB["Mat_UDB"] == DBNull.Value || p_drProcessDB["Mat_UDB"].ToString() == "")
        {
          DataRow[] array = clsDefine.g_dsMaterial.Tables["System"].AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Manufacturer"].ToString() == p_drProcessDB["Mat_Manufacturer"].ToString() && Temp["TradeName"].ToString() == p_drProcessDB["Mat_TradeName"].ToString())).ToArray<DataRow>();
          for (int index = 0; index < array.Length; ++index)
          {
            if (clsHDMFLib.CheckSuji(array[index], array[index]["Manufacturer"].ToString() + ".21000.udb"))
            {
              p_drMaterial = array[index];
              break;
            }
          }
        }
        else
        {
          FileInfo fiUserUDB = new FileInfo(clsDefine.g_diProcessUDBCfg.ToString() + "\\" + p_drProcessDB["Mat_UDB"] + ".21000.udb");
          if (fiUserUDB.Exists)
          {
            if (!((IEnumerable<FileInfo>) clsDefine.g_diUserUDB.GetFiles("*.21000.udb")).Any<FileInfo>((System.Func<FileInfo, bool>) (Temp => Temp.Name == fiUserUDB.Name)))
            {
              fiUserUDB.CopyTo(clsDefine.g_diUserUDB.FullName + "\\" + fiUserUDB.Name);
              DataTable table = clsDefine.g_dsMaterial.Tables.Cast<DataTable>().Where<DataTable>((System.Func<DataTable, bool>) (Temp => Temp.TableName == "User")).FirstOrDefault<DataTable>();
              if (table != null)
                clsDefine.g_dsMaterial.Tables.Remove(table);
              DataTable userMaterial = clsHDMFLib.GetUserMaterial();
              if (userMaterial != null)
                clsDefine.g_dsMaterial.Tables.Add(userMaterial);
            }
            DataRow[] array = clsDefine.g_dsMaterial.Tables["User"].AsEnumerable().Where<DataRow>((System.Func<DataRow, bool>) (Temp => Temp["Manufacturer"].ToString() == p_drProcessDB["Mat_Manufacturer"].ToString() && Temp["TradeName"].ToString() == p_drProcessDB["Mat_TradeName"].ToString())).ToArray<DataRow>();
            for (int index = 0; index < array.Length; ++index)
            {
              if (clsHDMFLib.CheckSuji(array[index], fiUserUDB.Name))
              {
                p_drMaterial = array[index];
                break;
              }
            }
          }
        }
        clsHDMFLib.SelectSuji(p_drMaterial);
        clsHDMFLib.SetProcessSet(p_drProcessDB);
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([frmMain]ApplyProcessDB):" + ex.Message));
      }
    }
  }
}
