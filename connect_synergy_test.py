# Python: Conexión y extracción del tiempo de llenado en Synergy Moldflow 2025
import win32com.client

try:
    synergy = win32com.client.Dispatch("synergy.Synergy")
except Exception as e:
    print("No se pudo iniciar Synergy:", e)
    exit(1)

version = synergy.Version
if "2025" not in version:
    print(f"Advertencia: No es la versión 2025. Versión detectada: {version}")
else:
    print(f"Conectado a Synergy Moldflow versión: {version}")

try:
    study_doc = synergy.StudyDoc()
except Exception as e:
    print("No hay estudio abierto en Synergy:", e)
    exit(1)

try:
    fill_time = study_doc.GetResultValue("Fill Time")
    print(f"Tiempo de llenado del estudio activo: {fill_time} s")
except Exception as e:
    print("No se pudo obtener el tiempo de llenado (Fill Time):", e)
