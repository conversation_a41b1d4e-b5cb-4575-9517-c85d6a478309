C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\x64\7z.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\x86\7z.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\Resources\7z.7z
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\Benchmark.exe
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\Benchmark.dll.config
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\Benchmark.deps.json
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\Benchmark.runtimeconfig.json
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\Benchmark.runtimeconfig.dev.json
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\Benchmark.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\Benchmark.pdb
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\BenchmarkDotNet.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\BenchmarkDotNet.Annotations.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\CommandLine.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\Microsoft.CodeAnalysis.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\Microsoft.CodeAnalysis.CSharp.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\Microsoft.DotNet.PlatformAbstractions.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\System.CodeDom.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\System.Management.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\cs\Microsoft.CodeAnalysis.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\de\Microsoft.CodeAnalysis.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\es\Microsoft.CodeAnalysis.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\fr\Microsoft.CodeAnalysis.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\it\Microsoft.CodeAnalysis.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\ja\Microsoft.CodeAnalysis.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\ko\Microsoft.CodeAnalysis.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\pl\Microsoft.CodeAnalysis.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\pt-BR\Microsoft.CodeAnalysis.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\ru\Microsoft.CodeAnalysis.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\tr\Microsoft.CodeAnalysis.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\zh-Hans\Microsoft.CodeAnalysis.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\zh-Hant\Microsoft.CodeAnalysis.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\cs\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\de\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\es\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\fr\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\it\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\ja\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\ko\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\pl\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\pt-BR\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\ru\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\tr\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\zh-Hans\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\zh-Hant\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\runtimes\win\lib\netcoreapp2.0\System.Management.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\SevenZipExtractor.dll
C:\Moldflow\SevenZipExtractor\Benchmark\bin\Debug\netcoreapp3.1\SevenZipExtractor.pdb
C:\Moldflow\SevenZipExtractor\Benchmark\obj\Debug\netcoreapp3.1\Benchmark.csproj.AssemblyReference.cache
C:\Moldflow\SevenZipExtractor\Benchmark\obj\Debug\netcoreapp3.1\Benchmark.csproj.SuggestedBindingRedirects.cache
C:\Moldflow\SevenZipExtractor\Benchmark\obj\Debug\netcoreapp3.1\Benchmark.dll.config
C:\Moldflow\SevenZipExtractor\Benchmark\obj\Debug\netcoreapp3.1\Benchmark.GeneratedMSBuildEditorConfig.editorconfig
C:\Moldflow\SevenZipExtractor\Benchmark\obj\Debug\netcoreapp3.1\Benchmark.AssemblyInfoInputs.cache
C:\Moldflow\SevenZipExtractor\Benchmark\obj\Debug\netcoreapp3.1\Benchmark.AssemblyInfo.cs
C:\Moldflow\SevenZipExtractor\Benchmark\obj\Debug\netcoreapp3.1\Benchmark.csproj.CoreCompileInputs.cache
C:\Moldflow\SevenZipExtractor\Benchmark\obj\Debug\netcoreapp3.1\Benchmark.sourcelink.json
C:\Moldflow\SevenZipExtractor\Benchmark\obj\Debug\netcoreapp3.1\Benchmark.csproj.Up2Date
C:\Moldflow\SevenZipExtractor\Benchmark\obj\Debug\netcoreapp3.1\Benchmark.dll
C:\Moldflow\SevenZipExtractor\Benchmark\obj\Debug\netcoreapp3.1\Benchmark.pdb
C:\Moldflow\SevenZipExtractor\Benchmark\obj\Debug\netcoreapp3.1\Benchmark.genruntimeconfig.cache
