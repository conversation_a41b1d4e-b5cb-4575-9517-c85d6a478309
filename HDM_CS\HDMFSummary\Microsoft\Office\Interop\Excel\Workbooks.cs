﻿// Decompiled with JetBrains decompiler
// Type: Microsoft.Office.Interop.Excel.Workbooks
// Assembly: HDMFSummary, Version=2.3.0.0, Culture=neutral, PublicKeyToken=null
// MVID: ********-3B9F-45E9-9723-0DEF72C057B2
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFSummary.dll

using System.Collections;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

namespace Microsoft.Office.Interop.Excel
{
  [CompilerGenerated]
  [Guid("000208DB-0000-0000-C000-000000000046")]
  [DefaultMember("_Default")]
  [TypeIdentifier]
  [ComImport]
  public interface Workbooks : IEnumerable
  {
    [SpecialName]
    [MethodImpl(MethodCodeType = MethodCodeType.Runtime)]
    sealed extern void _VtblGap1_12();

    [DispId(1923)]
    [LCIDConversion(15)]
    [MethodImpl(MethodImplOptions.InternalCall, MethodCodeType = MethodCodeType.Runtime)]
    [return: MarshalAs(UnmanagedType.Interface)]
    Workbook Open(
      [MarshalAs(UnmanagedType.BStr), In] string Filename,
      [MarshalAs(UnmanagedType.Struct), In, Optional] object UpdateLinks,
      [MarshalAs(UnmanagedType.Struct), In, Optional] object ReadOnly,
      [MarshalAs(UnmanagedType.Struct), In, Optional] object Format,
      [MarshalAs(UnmanagedType.Struct), In, Optional] object Password,
      [MarshalAs(UnmanagedType.Struct), In, Optional] object WriteResPassword,
      [MarshalAs(UnmanagedType.Struct), In, Optional] object IgnoreReadOnlyRecommended,
      [MarshalAs(UnmanagedType.Struct), In, Optional] object Origin,
      [MarshalAs(UnmanagedType.Struct), In, Optional] object Delimiter,
      [MarshalAs(UnmanagedType.Struct), In, Optional] object Editable,
      [MarshalAs(UnmanagedType.Struct), In, Optional] object Notify,
      [MarshalAs(UnmanagedType.Struct), In, Optional] object Converter,
      [MarshalAs(UnmanagedType.Struct), In, Optional] object AddToMru,
      [MarshalAs(UnmanagedType.Struct), In, Optional] object Local,
      [MarshalAs(UnmanagedType.Struct), In, Optional] object CorruptLoad);
  }
}
