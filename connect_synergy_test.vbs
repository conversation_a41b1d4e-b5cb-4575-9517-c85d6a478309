
Option Explicit
SetLocale("en-us")
Dim Synergy, SynergyGetter
On Error Resume Next

' --- Conexión robusta a Synergy Moldflow (patrón FICOSA) ---
Dim SynergyProgIDs, i, connected, errorMsg
connected = False
errorMsg = ""

Set Synergy = Nothing
Set SynergyGetter = Nothing

On Error Resume Next
Set SynergyGetter = GetObject(CreateObject("WScript.Shell").ExpandEnvironmentStrings("%SAInstance%"))
On Error GoTo 0
If (Not IsEmpty(SynergyGetter)) And (Not SynergyGetter Is Nothing) Then
    Set Synergy = SynergyGetter.GetSASynergy
    connected = True
    WScript.Echo "Conectado a instancia activa de Synergy (por %SAInstance%)."
End If

If Not connected Then
    On Error Resume Next
    Set Synergy = CreateObject("amiws.Synergy")
    On Error GoTo 0
    If Not Synergy Is Nothing Then
        connected = True
        WScript.Echo "Conectado a Synergy usando ProgID 'amiws.Synergy'."
    End If
End If

If Not connected Then
    On Error Resume Next
    Set Synergy = CreateObject("synergy.Synergy")
    On Error GoTo 0
    If Not Synergy Is Nothing Then
        connected = True
        WScript.Echo "Conectado a Synergy usando ProgID 'synergy.Synergy'."
    End If
End If

If Not connected Or Synergy Is Nothing Then
    WScript.Echo "No se pudo iniciar ni conectar a Synergy (ni por instancia activa, ni por 'amiws.Synergy', ni por 'synergy.Synergy')."
    WScript.Quit 1
End If

Dim version
version = Synergy.Version
If InStr(version, "2025") = 0 Then
    WScript.Echo "Advertencia: No es la versión 2025. Versión detectada: " & version
Else
    WScript.Echo "Conectado a Synergy Moldflow versión: " & version
End If

Dim StudyDoc
Set StudyDoc = Synergy.StudyDoc()
If StudyDoc Is Nothing Then
    WScript.Echo "No hay estudio abierto en Synergy."
    WScript.Quit 1
End If

' Obtener el tiempo de llenado desde el .out usando ScreenOutput y Message
Dim lName, lOutName, lMessages, MM, fillTime
lName = StudyDoc.GetResultPrefix("Flow")
lOutName = lName & ".out"
Set lMessages = New ScreenOutput
lMessages.LoadOutputFile(lOutName)
Set MM = lMessages.GetMessage(300400,1) ' MSCD 300400: Cavity Fill Time
If Not MM Is Nothing Then
    fillTime = MM.GetFloat(0)
    WScript.Echo "Tiempo de llenado del estudio activo: " & fillTime & " s"
Else
    WScript.Echo "No se pudo obtener el tiempo de llenado (Fill Time) desde el archivo .out."
End If

' --- Clases necesarias para leer el .out ---
Class Message
  Private mMSCD, mNumString, mNumFloat, mStrings(), mFloats()
  Public Sub SetMSCD(aMSCD): mMSCD = aMSCD: End Sub
  Public Sub SetNumString(aNumString): mNumString = aNumString: End Sub
  Public Sub SetNumFloat(aNumFloat): mNumFloat = aNumFloat: End Sub
  Public Sub AddFloat(aFloat): mNumFloat = mNumFloat + 1: ReDim Preserve mFloats(mNumFloat): mFloats(mNumFloat-1) = aFloat: End Sub
  Public Sub AddString(aString): mNumString = mNumString + 1: ReDim Preserve mStrings(mNumString): mStrings(mNumString-1) = aString: End Sub
  Public Function GetMSCD(): GetMSCD = mMSCD: End Function
  Public Function GetString(aIndex): GetString = "": If aIndex >= 0 And aIndex < mNumString Then GetString = mStrings(aIndex): End if: End Function
  Public Function GetFloat(aIndex): GetFloat = "": If aIndex >= 0 And aIndex < mNumFloat Then GetFloat = mFloats(aIndex): End if: End Function
  Public Function GetNumString(): GetNumString = mNumString: End Function
  Public Function GetNumFloat(): GetNumFloat = mNumFloat: End Function
  Private Sub Class_Initialize: mMSCD = -1: mNumString = 0: mNumFloat = 0: End Sub
End Class

Class ScreenOutput
 Private mMessages(), mNumMessages
  Public Function LoadOutputFile(aFile)
   Const ForReading = 1
   Dim FS: Set FS = CreateObject("Scripting.FileSystemObject")
   Dim File: Set File = FS.OpenTextFile(aFile, ForReading)
        While Not File.AtEndOfStream
     Dim ID: ID = -1
        Dim Line,lenLine
        Line = File.ReadLine
        lenLine = len(Line)
        If Not File.AtEndOfStream or lenLine >= 1 Then
            ID = Line
            Dim curMessage: Set curMessage = New Message
            curMessage.SetMSCD(ID)
            Line = File.ReadLine: lenLine = len(Line)
            If Not File.AtEndOfStream or lenLine >= 1 Then
                Dim numString: numString = Line
                Dim i
                For i = 1 To numString
                    Line = File.ReadLine: lenLine = len(Line)
                    If Not File.AtEndOfStream or lenLine >= 1 Then curMessage.AddString(Line)
                Next
            End if
            Line = File.ReadLine: lenLine = len(Line)
            If Not File.AtEndOfStream or lenLine >= 1 Then
                Dim numFloat: numFloat = Line
                For i = 1 To numFloat
                    Line = File.ReadLine: lenLine = len(Line)
                    If Not File.AtEndOfStream or lenLine >= 1 Then curMessage.AddFloat(Line)
                Next
            End If
            AddMessage(curMessage)
        End If
    Wend
    File.Close
  End Function
  Public Sub AddMessage(aMessage): mNumMessages = mNumMessages + 1: ReDim Preserve mMessages(mNumMessages): Set mMessages(mNumMessages-1) = aMessage: End Sub
  Public Function GetNumMessages(): GetNumMessages = mNumMessages: End Function
  Public Function GetMessage(aMSCD,aOccur)
   Set GetMessage = Nothing
   Dim j, lFindInstance: lFindInstance = aOccur
   If aOccur < 0 Then lFindInstance = 0
   Dim Count: Count = 0
   For j = 0 To mNumMessages-1
       If CStr(mMessages(j).GetMSCD) = CStr(aMSCD) Then
          Count = Count + 1
          If Count >= lFindInstance Then Set GetMessage = mMessages(j): Exit Function
       End if
   Next
  End Function
  Private Sub Class_Initialize: mNumMessages = 0: End Sub
End Class
