﻿// Decompiled with JetBrains decompiler
// Type: HDMFReport.frmReportSetting
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using HDLocale;
using HDMFReport.Properties;
using HDMFUserControl;
using HDMoldFlowLibrary;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace HDMFReport
{
  internal class frmReportSetting : Form
  {
    private const int WS_EX_COMPOSITED = 33554432;
    public clsHDMFLibDefine.Company m_enumCompany = clsHDMFLibDefine.Company.HDSolutions;
    public string m_strStudyName = string.Empty;
    private IContainer components;
    private NewButton newButton_Apply;
    private RadioButton radioButton_Company;
    private RadioButton radioButton_HDSolutions;
    private NewComboBox newComboBox_Company;

    protected override CreateParams CreateParams
    {
      get
      {
        CreateParams createParams = base.CreateParams;
        createParams.ExStyle |= 33554432;
        return createParams;
      }
    }

    public frmReportSetting()
    {
      this.InitializeComponent();
      this.Text = LocaleControl.getInstance().GetString("IDS_SETTING_REPORT");
      this.newButton_Apply.ButtonText = LocaleControl.getInstance().GetString("IDS_APPLY");
    }

    private void frmReportSetting_Load(object sender, EventArgs e)
    {
      clsHDMFLibDefine.Company enumCompany = this.m_enumCompany;
      if (enumCompany == clsHDMFLibDefine.Company.admin)
      {
        this.newComboBox_Company.Items.AddRange((object[]) ((IEnumerable<string>) Enum.GetNames(typeof (clsHDMFLibDefine.Company))).Where<string>((Func<string, bool>) (Temp => Temp != Enum.GetName(typeof (clsHDMFLibDefine.Company), (object) clsHDMFLibDefine.Company.admin))).ToArray<string>());
        this.newComboBox_Company.SelectedIndex = 0;
        this.newComboBox_Company.Visible = true;
        this.radioButton_Company.Visible = false;
        this.radioButton_HDSolutions.Visible = false;
      }
      else
      {
        this.newComboBox_Company.Visible = false;
        this.radioButton_Company.Visible = true;
        this.radioButton_HDSolutions.Visible = true;
        this.radioButton_Company.Text = this.m_enumCompany.ToString();
        FileInfo fileInfo = new FileInfo(clsReportDefine.g_diTmpReport.FullName + "\\" + this.m_strStudyName + "\\Report.ini");
        if (fileInfo.Exists)
          enumCompany = clsReportUtill.ConvertToEnumCompany(clsReportUtill.ReadINI("Template", "Company", fileInfo.FullName));
        if (enumCompany == clsHDMFLibDefine.Company.HDSolutions)
          this.radioButton_HDSolutions.Checked = true;
        else
          this.radioButton_Company.Checked = true;
      }
    }

    private void newButton_Apply_NewClick(object sender, EventArgs e)
    {
      clsReportDefine.g_enumSelectedCompany = !this.newComboBox_Company.Visible ? (!this.radioButton_HDSolutions.Checked ? this.m_enumCompany : clsHDMFLibDefine.Company.HDSolutions) : clsReportUtill.ConvertToEnumCompany(this.newComboBox_Company.Value);
      this.Close();
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.newButton_Apply = new NewButton();
      this.radioButton_Company = new RadioButton();
      this.radioButton_HDSolutions = new RadioButton();
      this.newComboBox_Company = new NewComboBox();
      this.SuspendLayout();
      this.newButton_Apply.ButtonBackColor = Color.White;
      this.newButton_Apply.ButtonText = "적용";
      this.newButton_Apply.FlatBorderSize = 1;
      this.newButton_Apply.FlatStyle = FlatStyle.Flat;
      this.newButton_Apply.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newButton_Apply.Image = (Image) Resources.OK;
      this.newButton_Apply.ImageAlign = ContentAlignment.MiddleRight;
      this.newButton_Apply.Location = new Point(3, 42);
      this.newButton_Apply.Name = "newButton_Apply";
      this.newButton_Apply.Size = new Size(208, 23);
      this.newButton_Apply.TabIndex = 22;
      this.newButton_Apply.TextAlign = ContentAlignment.MiddleCenter;
      this.newButton_Apply.TextImageRelocation = TextImageRelation.ImageBeforeText;
      this.newButton_Apply.NewClick += new EventHandler(this.newButton_Apply_NewClick);
      this.radioButton_Company.Location = new Point(116, 12);
      this.radioButton_Company.Name = "radioButton_Company";
      this.radioButton_Company.Size = new Size(92, 19);
      this.radioButton_Company.TabIndex = 23;
      this.radioButton_Company.Text = "Company";
      this.radioButton_Company.UseVisualStyleBackColor = true;
      this.radioButton_HDSolutions.Location = new Point(7, 13);
      this.radioButton_HDSolutions.Name = "radioButton_HDSolutions";
      this.radioButton_HDSolutions.Size = new Size(100, 18);
      this.radioButton_HDSolutions.TabIndex = 24;
      this.radioButton_HDSolutions.Text = "HDSolutions";
      this.radioButton_HDSolutions.TextAlign = ContentAlignment.MiddleCenter;
      this.radioButton_HDSolutions.UseVisualStyleBackColor = true;
      this.newComboBox_Company.BackColor = Color.White;
      this.newComboBox_Company.BorderStyle = BorderStyle.FixedSingle;
      this.newComboBox_Company.ComboBoxBackColor = SystemColors.Window;
      this.newComboBox_Company.Font = new Font("Segoe UI", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
      this.newComboBox_Company.isSameSelect = false;
      this.newComboBox_Company.Location = new Point(3, 9);
      this.newComboBox_Company.Name = "newComboBox_Company";
      this.newComboBox_Company.SelectedIndex = -1;
      this.newComboBox_Company.Size = new Size(208, 24);
      this.newComboBox_Company.TabIndex = 25;
      this.newComboBox_Company.TextAlign = HorizontalAlignment.Left;
      this.newComboBox_Company.Value = "";
      this.newComboBox_Company.Visible = false;
      this.AutoScaleMode = AutoScaleMode.None;
      this.BackColor = Color.White;
      this.ClientSize = new Size(216, 67);
      this.Controls.Add((Control) this.radioButton_Company);
      this.Controls.Add((Control) this.radioButton_HDSolutions);
      this.Controls.Add((Control) this.newButton_Apply);
      this.Controls.Add((Control) this.newComboBox_Company);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.KeyPreview = true;
      this.MaximizeBox = false;
      this.MinimizeBox = false;
      this.Name = nameof (frmReportSetting);
      this.StartPosition = FormStartPosition.CenterParent;
      this.Text = "Report 설정";
      this.Load += new EventHandler(this.frmReportSetting_Load);
      this.ResumeLayout(false);
    }
  }
}
