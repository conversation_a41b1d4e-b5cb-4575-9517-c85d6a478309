﻿// Decompiled with JetBrains decompiler
// Type: HDMFReport.CMPRData
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using System.Collections.Generic;
using System.Data;

namespace HDMFReport
{
  internal class CMPRData
  {
    internal DataRow drStudy { get; set; }

    internal Dictionary<string, string> dicValue { get; set; }

    internal Dictionary<string, string> dicView { get; set; }

    internal Dictionary<string, string> dicUse { get; set; }

    internal CMPRData()
    {
      this.drStudy = (DataRow) null;
      this.dicValue = new Dictionary<string, string>();
      this.dicView = new Dictionary<string, string>();
      this.dicUse = new Dictionary<string, string>();
    }
  }
}
