﻿// Decompiled with JetBrains decompiler
// Type: HDMFReport.clsSLData
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using HDLog4Net;
using HDMoldFlowLibrary;
using Microsoft.CSharp.RuntimeBinder;
using Microsoft.Office.Core;
using Microsoft.Office.Interop.Excel;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;

namespace HDMFReport
{
  internal class clsSLData
  {
    public static void GetReportUser(
      DataRow p_drStudy,
      out Dictionary<string, string> p_dicValue,
      out Dictionary<string, string> p_dicView,
      out Dictionary<string, string> p_dicUse)
    {
      bool flag = false;
      p_dicValue = new Dictionary<string, string>();
      p_dicView = new Dictionary<string, string>();
      p_dicUse = new Dictionary<string, string>();
      try
      {
        if (p_drStudy["Sequence"].ToString().Contains("Cool"))
          flag = true;
        if (p_drStudy["Summary"] != DBNull.Value && p_drStudy["Summary"].ToString() != "")
          JsonConvert.DeserializeObject<Dictionary<string, string>>(p_drStudy["Summary"].ToString());
        clsHDMFLib.GetFamilyAbbreviation();
        clsHDMFLib.GetFibersFillers();
        p_dicValue.Add("InjCond", "0");
        Dictionary<string, string> injectionData = clsHDMFLib.GetInjectionData();
        p_dicValue.Add("InjMaxStroke", injectionData["MaxStroke"]);
        p_dicValue.Add("InjMaxRate", injectionData["MaxRate"]);
        p_dicValue.Add("InjScrewDia", injectionData["ScrewDia"]);
        p_dicValue.Add("InjMaxPressure", injectionData["MaxPressure"]);
        p_dicValue.Add("InjMaxClamp", injectionData["MaxClamp"]);
        p_dicValue.Add("InjPreRatio", "0");
        p_dicValue.Add("InjType", "1");
        p_dicValue.Add("InjRange1", "5");
        p_dicValue.Add("InjRange2", "15");
        p_dicValue.Add("InjRange3", "65");
        p_dicValue.Add("InjRange4", "97");
        p_dicValue.Add("InjRangeVP", "9");
        p_dicValue.Add("Report_Item", p_drStudy.Table.TableName);
        p_dicValue.Add("Report_Step", "");
        p_dicValue.Add("Report_Engineer", "");
        p_dicValue.Add("Report_Manager", "");
        p_dicValue.Add("Report_WeldLine", "OK|");
        p_dicValue.Add("Report_AirTrap", "DISCUSSION(Gas vent)|");
        p_dicValue.Add("Report_Shrinkage/Sink", "DISCUSSION(Gas vent)|");
        if (flag)
          p_dicValue.Add("Report_Cooling", "OK|");
        p_dicValue.Add("Report_Countermeasure", string.Empty);
        p_dicValue.Add("Report_ManifoldSize", "16");
        p_dicValue.Add("Report_NozzleSize", "16,8");
        p_dicValue.Add("Report_ValvePinSize", "n/a");
        p_dicValue.Add("Report_NozzleGateSize", "4");
        p_dicValue.Add("Report_ManifoldVolume", "1.2");
        p_dicValue.Add("Report_HeatDiameter", "");
        p_dicValue.Add("Report_HeatPipeEffectiveness", "");
        p_dicValue.Add("Report_Filling", "25/50/80/95");
        p_dicValue.Add("Report_AnimationFrame", "150");
        p_dicValue.Add("Report_VolumePercentage", "0");
        p_dicValue.Add("Report_Time", "0");
        p_dicValue.Add("Report_FlowTemp", "0");
        p_dicValue.Add("Report_MoldTemp", "0");
        p_dicValue.Add("Report_DeflectionAll", "Best fit/");
        p_dicValue.Add("Report_DeflectionX", "Best fit/");
        p_dicValue.Add("Report_DeflectionY", "Best fit/");
        p_dicValue.Add("Report_DeflectionZ", "Best fit/");
        p_dicValue.Add("Report_SCOption", "Isotropic");
        p_dicValue.Add("Report_SCValue", "0.6");
        p_dicValue.Add("Report_MoldTemp1", "");
        p_dicValue.Add("Report_MoldTemp2", "");
        p_dicValue.Add("Report_MoldGoal", "");
        FileInfo fileInfo1 = new FileInfo(clsReportDefine.g_diCfg.FullName + "\\Report.ini");
        FileInfo fileInfo2 = new FileInfo(clsReportDefine.g_diTmpReport.ToString() + "\\" + p_drStudy["Name"].ToString() + "\\Report.ini");
        if (!fileInfo2.Exists)
          fileInfo2 = fileInfo1;
        foreach (string iniAllKey in clsReportUtill.GetINIAllKeys(fileInfo2.FullName, "Input"))
        {
          string str = clsReportUtill.ReadINI("Input", iniAllKey, fileInfo2.FullName);
          p_dicValue[iniAllKey] = str;
        }
        foreach (string iniAllKey in clsReportUtill.GetINIAllKeys(fileInfo2.FullName, "View"))
        {
          if (!(iniAllKey == "ViewType"))
          {
            string str = clsReportUtill.ReadINI("View", iniAllKey, fileInfo2.FullName);
            if (str == string.Empty)
              str = "-135/-145/30";
            p_dicView.Add(iniAllKey, str);
          }
        }
        foreach (string iniAllKey in clsReportUtill.GetINIAllKeys(fileInfo2.FullName, "Use"))
        {
          string str = clsReportUtill.ReadINI("Use", iniAllKey, fileInfo2.FullName);
          if (str != "")
            p_dicUse[iniAllKey] = str;
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsReport]GetReportUser):" + ex.Message));
      }
    }

    public static Dictionary<string, string> GetAnalysisData(
      DataRow p_drStudy,
      Dictionary<string, string> p_dicValue,
      Dictionary<string, string> p_dicView)
    {
      bool flag = false;
      bool p_isCool = false;
      string str1 = "";
      string p_strSNNode = "";
      List<string> stringList = new List<string>();
      Dictionary<string, string> p_dicData = new Dictionary<string, string>();
      DataTable p_dtDet = new DataTable();
      try
      {
        DirectoryInfo directoryInfo = new DirectoryInfo(clsReportDefine.g_diTmpReport.ToString() + "\\" + p_drStudy["Name"]);
        if (!directoryInfo.Exists)
          directoryInfo.Create();
        if (p_drStudy["Sequence"].ToString().Contains("Cool"))
          p_isCool = true;
        if (p_drStudy["Sequence"].ToString().Contains("Warp"))
          flag = true;
        FileInfo fileInfo = new FileInfo(clsReportDefine.g_diTmpReport.ToString() + "\\" + p_drStudy["Name"].ToString() + "\\ALog.log");
        if (!fileInfo.Exists)
          stringList = clsHDMFLib.ExportAnalysisLog(fileInfo.FullName);
        else
          stringList.AddRange((IEnumerable<string>) File.ReadAllLines(fileInfo.FullName, Encoding.Default));
        List<DataRow> p_lst_drFillPhase = new List<DataRow>((IEnumerable<DataRow>) clsHDMFLib.GetFillingPhaseData(stringList).Rows.Cast<DataRow>().ToArray<DataRow>());
        List<DataRow> dataRowList1 = new List<DataRow>((IEnumerable<DataRow>) clsHDMFLib.GetPackingPhaseData(stringList).Rows.Cast<DataRow>().ToArray<DataRow>());
        List<DataRow> dataRowList2 = new List<DataRow>();
        dataRowList2.AddRange((IEnumerable<DataRow>) p_lst_drFillPhase.ToArray());
        dataRowList2.AddRange((IEnumerable<DataRow>) dataRowList1.ToArray());
        Dictionary<string, string> allNodes = clsHDMFLib.GetAllNodes(ref p_dtDet);
        string str2 = clsHDMFLib.GetGateNodeFromStudyNote();
        if (str2 == string.Empty)
        {
          str2 = string.Join(" ", clsHDMFLib.GetGateNodes(allNodes, p_dtDet));
          clsHDMFLib.WriteGateNodeToStudyNote(str2);
        }
        string[] strArray1 = clsHDMFLib.GetCoreAndCavityNodeFromStudyNote();
        if (strArray1.Length == 0)
        {
          string[] strArray2 = str2.Split(' ');
          strArray1 = clsHDMFLib.GetCavityCoreNodes(allNodes, stringList, strArray2[0]);
          if (strArray1.Length > 1)
          {
            clsHDMFLib.WriteCoreToStudyNote(strArray1[0]);
            clsHDMFLib.WriteCavityToStudyNote(strArray1[1]);
          }
        }
        if (strArray1.Length == 2)
        {
          str1 = strArray1[0];
          p_strSNNode = strArray1[1];
        }
        DataTable allLayers = clsHDMFLib.GetAllLayers();
        p_dicData.Add("Item[User]", p_dicValue["Report_Item"]);
        p_dicData.Add("Step[User]", p_dicValue["Report_Step"]);
        p_dicData.Add("Engineer[User]", p_dicValue["Report_Engineer"]);
        p_dicData.Add("Manager[User]", p_dicValue["Report_Manager"]);
        p_dicData.Add("Countermeasure[User]", p_dicValue["Report_Countermeasure"]);
        p_dicData.Add("ManifoldSize[User]", p_dicValue["Report_ManifoldSize"]);
        p_dicData.Add("NozzleSize[User]", p_dicValue["Report_NozzleSize"]);
        p_dicData.Add("ValvePinSize[User]", p_dicValue["Report_ValvePinSize"]);
        p_dicData.Add("NozzleGateSize[User]", p_dicValue["Report_NozzleGateSize"]);
        p_dicData.Add("ManifoldVolume[User]", p_dicValue["Report_ManifoldVolume"]);
        p_dicData.Add("HeatDiameter[User]", p_dicValue["Report_HeatDiameter"]);
        p_dicData.Add("HeatPipeEffectiveness[User]", p_dicValue["Report_HeatPipeEffectiveness"]);
        if (!p_isCool)
          p_dicData.Add("ShrinkageValue[User]", p_dicValue["Report_SCValue"]);
        if (flag)
        {
          p_dicData.Add("DeflectionAll[User]", p_dicValue["Report_DeflectionAll"]);
          p_dicData.Add("DeflectionX[User]", p_dicValue["Report_DeflectionX"]);
          p_dicData.Add("DeflectionY[User]", p_dicValue["Report_DeflectionY"]);
          p_dicData.Add("DeflectionZ[User]", p_dicValue["Report_DeflectionZ"]);
        }
        p_dicData.Add("WeldLine[User]", p_dicValue["Report_WeldLine"]);
        p_dicData.Add("AirTrap[User]", p_dicValue["Report_AirTrap"]);
        p_dicData.Add("Shrinkage/Sink[User]", p_dicValue["Report_Shrinkage/Sink"]);
        if (p_dicValue.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "Report_Cooling")) & p_isCool)
        {
          string[] strArray3 = p_dicValue["Report_Cooling"].Split('|');
          string str3 = strArray3[1];
          if (str3 == string.Empty)
            str3 = !(strArray3[0].ToUpper() == "OK") ? (!(strArray3[0].ToUpper() == "DISCUSSION") ? "Problem" : "Need to discussion") : "To reduce mold temperature area";
          p_dicData.Add("Cooling[User]", strArray3[0] + "|" + str3);
        }
        p_dicData.Add("ProductVolume[Log]", clsHDMFLib.GetProductVolumeFromLog(stringList));
        p_dicData.Add("ColdFeedVolume[Log]", clsHDMFLib.GetColdFeedVolumeFromLog(stringList));
        p_dicData.Add("ColdMass[Log]", clsHDMFLib.GetColdMassFromLog(stringList));
        p_dicData.Add("PartMass[Log]", clsHDMFLib.GetPartMassFromLog(stringList));
        p_dicData.Add("FillTime[Log]", clsHDMFLib.GetFillLastTimeFromLog(p_lst_drFillPhase));
        p_dicData.Add("CycleTime[Log]", clsHDMFLib.GetCycleTimeFromLog());
        p_dicData.Add("TotalMass(FillPhase)[Log]", clsHDMFLib.GetFillPhaseTotalMassFromLog(stringList));
        p_dicData.Add("TotalMass(PackPhase)[Log]", clsHDMFLib.GetPackPhaseTotalMassFromLog(stringList));
        p_dicData.Add("Totalweight(FillPhase)[Log]", clsHDMFLib.GetFillPhaseTotalweightFromLog(stringList));
        p_dicData.Add("Totalweight(PackPhase)[Log]", clsHDMFLib.GetPackPhaseTotalweightFromLog(stringList));
        p_dicData.Add("CircuitCoolantTemperature[Log]", clsHDMFLib.GetCircuitCoolantTemperatureFromLog(stringList, str1));
        p_dicData.Add("FillPattern[Log]", clsHDMFLibSL.GetFillpatternFromLog(stringList));
        p_dicData.Add("InjPressure[Log]", clsHDMFLibSL.GetInjPressureFromLog(dataRowList2));
        p_dicData.Add("VolumetricShrinkage[Log]", clsHDMFLibSL.GetVolumetricShrinkageFromLog(stringList));
        p_dicData.Add("ProjectionArea[Log]", clsHDMFLib.GetProjectedAreaFromLog(stringList));
        p_dicData.Add("ElementNo[Log]", clsHDMFLibSL.GetElementNoFromLog(stringList));
        p_dicData.Add("NumberOfLayerThroughThickness[Log]", clsHDMFLib.GetNumberofLayerThroughThicknessFromLog(stringList));
        p_dicData.Add("CoolingTime[Log]", clsHDMFLib.GetCoolingTimeFromLog(dataRowList2));
        p_dicData.Add("PressureAtInjectionLocation[Log]", clsHDMFLib.GetPressureAtInjectionLocationFromLog(dataRowList2));
        p_dicData.Add("VolumeByComponents[Log]", clsHDMFLibSL.GetVolumeByComponentsFromLog(stringList));
        p_dicData.Add("HRSToPartVolumeRatio[Log]", clsHDMFLibSL.GetHRSToPartVolumeRatioFromLog(stringList));
        p_dicData.Add("ColdRunnerVolume[Log]", clsHDMFLibSL.GetColdRunnerVolumeForSlide(stringList));
        p_dicData.Add("SpruePressure[Log]", clsHDMFLib.GetSpruePressureFromLog(dataRowList2));
        p_dicData.Add("ClampForce[Log]", clsHDMFLib.GetClampForceFromLog2(dataRowList2));
        if (p_isCool)
        {
          p_dicData.Add("CircuitReynoldsNumber[Log]", clsHDMFLibSL.GetCircuitReynoldNumberFromLog(1, stringList));
          p_dicData.Add("CircuitCoolantTemperature(Core)[Log]", clsHDMFLib.GetCircuitCoolantTemperatureCavityCoreFromLog(stringList, str1));
          p_dicData.Add("CircuitCoolantTemperature(Cavity)[Log]", clsHDMFLib.GetCircuitCoolantTemperatureCavityCoreFromLog(stringList, p_strSNNode));
          p_dicData.Add("CircuitFlowRate[Log]", clsHDMFLibSL.GetCircuitFlowRateFromLog(stringList));
          p_dicData.Add("CoolantInletTemp[Log]", clsHDMFLibSL.GetCoolantInletTemperatureFromLog(stringList));
          p_dicData.Add("CoolantControlFlowRate[Log]", clsHDMFLibSL.GetCoolantControlFlowRateFromLog(stringList));
        }
        string[] strArray4 = clsHDMFLibSL.GetClampForceFromLog(dataRowList2).Split('/');
        p_dicData.Add("ClampForce_Fill[Log]", strArray4[0]);
        p_dicData.Add("ClampForce_Pack[Log]", strArray4[1]);
        p_dicData.Add("ClampForce_More[Log]", strArray4[2]);
        p_dicData.Add("Sequence[MF]", p_drStudy["Sequence"].ToString().Replace("|", " + "));
        p_dicData.Add("ShearRate[MF]", clsHDMFLib.GetShearRate());
        p_dicData.Add("ShearStress[MF]", clsHDMFLib.GetShearStress());
        p_dicData.Add("Mesh[MF]", p_drStudy["Mesh"].ToString());
        p_dicData.Add("ColdRunnerCrossSectionIs[MF]", clsHDMFLibSL.GetColdRunnerCrossSectionIsForSlide());
        p_dicData.Add("ColdRunnerSize[MF]", clsHDMFLibSL.GetColdRunnerSizeForSlide());
        p_dicData.Add("ColdGateType[MF]", clsHDMFLibSL.GetColdGateTypeForSlide(str2));
        p_dicData.Add("ColdGateSize[MF]", clsHDMFLibSL.GetColdGateSizeForSlide(str2));
        p_dicData.Add("CoolingLineDiameter[MF]", clsHDMFLibSL.GetCoolingLineDiameterForSlide());
        p_dicData.Add("Viscosity[MF]", clsHDMFLib.GetMaterialPlotToImage(1310));
        p_dicData.Add("SpecifiedVolume[MF]", clsHDMFLib.GetMaterialPlotToImage(1004));
        p_dicData.Add("Manufacturer[MF]", clsHDMFLib.GetManufacturer());
        p_dicData.Add("TradeName[MF]", clsHDMFLib.GetTradeName());
        p_dicData.Add("MaterialID[MF]", clsHDMFLib.GetMaterialID());
        p_dicData.Add("FamilyAbbreviation[MF]", clsHDMFLib.GetFamilyAbbreviation());
        p_dicData.Add("FibersFillers[MF]", clsHDMFLib.GetFibersFillers());
        p_dicData.Add("MaterialIndicator[MF]", clsHDMFLibSL.GetMaterialIndicator());
        p_dicData.Add("DateTested[MF]", clsHDMFLibSL.GetDateTested());
        p_dicData.Add("MeltTempRange[MF]", clsHDMFLibSL.GetMeltTempRange());
        p_dicData.Add("MoldTempRange[MF]", clsHDMFLibSL.GetMoldTempRange());
        p_dicData.Add("EjectionTemp[MF]", clsHDMFLib.GetEjectionTemperature());
        p_dicData.Add("TransitionTemp[MF]", clsHDMFLib.GetTransitionTemperature());
        p_dicData.Add("MeltTemp[MF]", clsHDMFLib.GetMeltTemperatureDataFromProcessSet());
        p_dicData.Add("MoldTemp[MF]", clsHDMFLib.GetMoldTemperatureDataFromProcessSet());
        p_dicData.Add("FillTime[MF]", clsHDMFLib.GetFillingControlDataForCase());
        p_dicData.Add("FillTimeType[MF]", clsHDMFLib.GetFillingControlType());
        p_dicData.Add("VPSwitchover[MF]", clsHDMFLibSL.GetVPSwitchOver());
        p_dicData.Add("PackingProfile[MF]", clsHDMFLibSL.GetPackingProfile());
        p_dicData.Add("CoolantControl[MF]", clsHDMFLibSL.GetCoolantControl());
        p_dicData.Add("CoolingTime[MF]", clsHDMFLib.GetCoolingTimeFromProcess());
        p_dicData.Add("MeltDensity[MF]", clsHDMFLib.GetMaterialMeltDensity());
        p_dicData.Add("SolidDensity[MF]", clsHDMFLib.GetMaterialSolidDensity());
        if (p_isCool)
        {
          string efficiencyAtTheSurface = clsHDMFLibSL.GetCoolingEfficiencyAtTheSurface(clsReportUtill.ConvertToDouble(p_dicValue["Report_MoldTemp1"]), clsReportUtill.ConvertToDouble(p_dicValue["Report_MoldTemp2"]));
          if (efficiencyAtTheSurface != "")
          {
            string str4 = Convert.ToDouble(efficiencyAtTheSurface) >= clsReportUtill.ConvertToDouble(p_dicValue["Report_MoldGoal"]) ? efficiencyAtTheSurface + "|OK" : efficiencyAtTheSurface + "|NG";
            p_dicData.Add("CoolingEfficiencyAtTheSurface[MF]", str4);
          }
          if (p_isCool)
            p_dicData.Add("BaffleDiameter[MF]", clsHDMFLibSL.GetBaffleDiameterForSlide());
        }
        clsHDMFLibSL.ChangeLayerForImage(0, allLayers);
        clsHDMFLibSL.GetModelImageForSlide("Model", directoryInfo.FullName, p_dicView["Model"], 900, 529, ref p_dicData);
        p_dicData.Add("PressureAtInjectionLocation_Inj[IMG]", clsHDMFLib.GetPlotImage("PressureAtInjectionLocation", "Pressure at injection location:XY Plot", "", "", directoryInfo.FullName, "", 900, 600));
        p_dicData.Add("TemperatureAtFlowFront_Inj[IMG]", clsHDMFLib.GetPlotImage("TemperatureAtFlowFront_Inj", "Temperature at flow front", "", "", directoryInfo.FullName, "", 900, 600));
        clsHDMFLibSL.ChangeLayerForImage(1, allLayers);
        clsHDMFLibSL.GetPlotForSlide("Air traps", false, true, "", "AirTrap", directoryInfo.FullName, p_dicView["Air trap"], 900, 600, ref p_dicData);
        clsHDMFLibSL.GetPlotForSlide("Average volumetric shrinkage", false, true, "", "AverageVolumetricShrinkage", directoryInfo.FullName, p_dicView["Volumetric shrinkage"], 900, 600, ref p_dicData);
        string p_strKey = p_dicData["FamilyAbbreviation[MF]"].Replace(" ", string.Empty);
        if (p_strKey.Contains("ABS") && p_strKey.Contains("PC"))
          p_strKey = "PCABS";
        double p_dblMin = clsReportUtill.ConvertToDouble(clsReportUtill.ReadINI("Value", p_strKey, clsReportDefine.g_diCfg.FullName + "\\SLReport\\ShrinkageConfig.ini"));
        clsHDMFLibSL.GetVolumetricScaleImageForSlide("", directoryInfo.FullName, p_dblMin, p_dicView["Volumetric shrinkage"], 900, 600, ref p_dicData);
        clsHDMFLibSL.GetFillTimePlotForSlide("", directoryInfo.FullName, p_dicValue, p_dicView, ref p_dicData);
        clsHDMFLibSL.GetPlotForSlide("Grow from", false, true, "", "GrowFrom", directoryInfo.FullName, p_dicView["Grow from"], 900, 600, ref p_dicData);
        p_dicData.Add("GrowFrom[Node]", clsHDMFLibSL.GetGrowDataFromPlot("", str2));
        clsHDMFLibSL.GetXYPlotFromPlotBySettingRange("Pressure at injection location:XY Plot", "", true, true, "PressureAtInjectionLocation", "PressureAtInjectionLocation", directoryInfo.FullName, "", p_dicView["Injection pressure"], 900, 600, "PackPhase", 3.0, false, dataRowList2, ref p_dicData);
        clsHDMFLibSL.GetPlotImageForSlide("Ram speed, recommended:XY Plot", false, "", "RamSpeedRecommended", "", "", directoryInfo.FullName, "", 900, 600, ref p_dicData);
        clsHDMFLibSL.GetPlotForSlide("Shear stress at wall", false, true, "", "ShearStress", directoryInfo.FullName, p_dicView["Shear stress at wall at 95%"], 900, 600, ref p_dicData);
        clsHDMFLibSL.GetPlotForSlide("Shear rate", true, true, "ShearRate", "ShearRate", directoryInfo.FullName, p_dicView["Shear rate at 95%, Maximum"], 900, 600, ref p_dicData);
        clsHDMFLibSL.GetXYPlotFromPlotBySettingRange("Shear stress at wall:XY Plot", "7050", true, true, "ShearStress", "ShearStressXY", directoryInfo.FullName, str2, p_dicView["Shear stress at wall at 95%"], 900, 720, "FillPhase", 3.0, false, dataRowList2, ref p_dicData);
        clsHDMFLibSL.GetPlotForSlide("Temperature at flow front", true, true, true, "TemperatureAtFlowFront", "TemperatureAtFlowFront", directoryInfo.FullName, p_dicView["Temperature at flow front"], 900, 600, ref p_dicData);
        clsHDMFLibSL.GetTemperaturePlotForSlide("", dataRowList2, directoryInfo.FullName, p_dicView["Volumetric shrinkage"], ref p_dicData);
        clsHDMFLibSL.GetWeldlinePlotForSlide(directoryInfo.FullName, p_dicView["Weld line"], ref p_dicData);
        p_dicData.Add("FrozenLayerFractionAtGate[Plot]", clsHDMFLibSL.GetXYPlotData2("Frozen layer fraction:XY Plot", "1491", "FrozenLayerFractionAtGate", 1.0, directoryInfo.FullName, str2));
        if (p_isCool)
        {
          clsHDMFLibSL.GetTemperatureMoldForSlide("Temperature, mold", "TemperatureMold", directoryInfo.FullName, p_dicValue, p_dicView["Determine process settings"], false, 900, 600, ref p_dicData);
          clsHDMFLibSL.GetTemperatureMoldForSlide("Temperature, mold", "TemperatureMold_Front", directoryInfo.FullName, p_dicValue, p_dicView["Mold Temperature(Front)"], true, 900, 600, ref p_dicData);
          clsHDMFLibSL.GetTemperatureMoldForSlide("Temperature, mold", "TemperatureMold_Back", directoryInfo.FullName, p_dicValue, p_dicView["Mold Temperature(Back)"], true, 900, 600, ref p_dicData);
          clsHDMFLibSL.GetPlotImageForSlide("Time to reach ejection temperature, part", false, "", "TimeToReachEjectionTemperaturePart", "", "", directoryInfo.FullName, p_dicView["Time to reach ejection temperature"], 900, 600, ref p_dicData);
        }
        if (flag)
        {
          clsHDMFLibSL.GetDeflectionAllPlotForSlide(directoryInfo.FullName, p_dicValue, p_isCool, p_dicView, ref p_dicData);
          clsHDMFLibSL.GetDeflectionXYZPlotForSlide(directoryInfo.FullName, p_dicValue, p_isCool, p_dicView, ref p_dicData);
        }
        clsHDMFLibSL.ChangeLayerForImage(2, allLayers);
        clsHDMFLibSL.GetModelImageForSlide("RunnerDetailAndGatePosition", directoryInfo.FullName, p_dicView["Runner detail and gate positions"], 900, 840, ref p_dicData);
        clsHDMFLibSL.GetXYPlotFromPlotBySettingRange("Clamp force:XY Plot", "", false, true, "ClampForce", "ClampForce", directoryInfo.FullName, "", p_dicView["Clamp force"], 900, 600, "PackPhase", 3.0, false, dataRowList2, ref p_dicData);
        clsHDMFLibSL.GetPlotImageForSlide("Frozen layer fraction:XY Plot", false, "", "FrozenLayerFractionAtGate", str2, "1491", directoryInfo.FullName, p_dicView["Frozen Layer fraction at gate"], 900, 600, ref p_dicData);
        clsHDMFLibSL.GetPlotForSlide("Cavity weight", true, false, "PartWeight", "", directoryInfo.FullName, "", 900, 600, ref p_dicData);
        clsHDMFLibSL.GetXYPlotFromPlotBySettingRange("Pressure:XY Plot", "1180", true, true, "PressureXY", "PressureXY", directoryInfo.FullName, str2, p_dicView["Injection pressure"], 900, 600, "PackPhase", 3.0, false, dataRowList2, ref p_dicData);
        clsHDMFLibSL.GetPlotImageForSlide("Pressure at V/P switchover", false, "", "PressureAtVPSwitchover", "", "", directoryInfo.FullName, p_dicView["Pressure at V/P switchover"], 900, 600, ref p_dicData);
        clsHDMFLibSL.GetXYPlotFromPlotBySettingRange("Shear rate:XY Plot", "1584", false, true, "ShearRateXY", "ShearRateXY", directoryInfo.FullName, str2, p_dicView["Shear rate at 95%, Maximum"], 900, 720, "FillPhase", 3.0, false, dataRowList2, ref p_dicData);
        clsHDMFLibSL.GetVelocityOverlayFillTime("Velocity", "Velocity", "Fill time", directoryInfo.FullName, dataRowList2, p_dicView["Velocity"], 900, 600, ref p_dicData);
        clsHDMFLibSL.ChangeLayerForImage(3, allLayers);
        if (p_isCool)
        {
          clsHDMFLibSL.GetPlotForSlide("Circuit coolant temperature", true, true, false, "CircuitCoolantTemperature", "", directoryInfo.FullName, "", 900, 600, ref p_dicData);
          clsHDMFLibSL.GetPlotImageForSlide("Circuit coolant temperature", false, "", "CircuitCoolantTemperature_Front", "", "", directoryInfo.FullName, p_dicView["Circuit coolant temperature(Front)"], 900, 600, ref p_dicData);
          clsHDMFLibSL.GetPlotImageForSlide("Circuit coolant temperature", false, "", "CircuitCoolantTemperature_Side", "", "", directoryInfo.FullName, p_dicView["Circuit coolant temperature(Side)"], 900, 600, ref p_dicData);
          clsHDMFLibSL.GetPlotImageForSlide("Circuit flow rate", false, "", "CircuitFlowRate", "", "", directoryInfo.FullName, p_dicView["Circuit Flow rate"], 900, 600, ref p_dicData);
          clsHDMFLibSL.GetPlotForSlide("Circuit pressure", true, false, "CircuitPressure", "", directoryInfo.FullName, "", 900, 600, ref p_dicData);
          clsHDMFLibSL.GetPlotForSlide("Circuit Reynolds number", true, true, true, "CircuitReynoldsNumber", "CircuitReynoldsNumber", directoryInfo.FullName, p_dicView["Circuit Reynolds number"], 900, 600, ref p_dicData);
        }
        clsHDMFLibSL.ChangeLayerForImage(4, allLayers);
        clsHDMFLibSL.GetModelImageForSlide("CoolingChannelLayout", directoryInfo.FullName, p_dicView["Cooling channel Layout"], 900, 600, ref p_dicData);
        clsHDMFLibSL.ChangeLayerForImage(5, allLayers);
        Dictionary<string, string> p_dicThickness = new Dictionary<string, string>();
        string str5 = clsReportUtill.ReadINI("Thickness", "Min", clsReportDefine.g_diCfg.ToString() + "\\SLReport\\ThicknessConfig.ini");
        p_dicThickness.Add("Min", str5);
        string str6 = clsReportUtill.ReadINI("Thickness", "Max", clsReportDefine.g_diCfg.ToString() + "\\SLReport\\ThicknessConfig.ini");
        p_dicThickness.Add("Max", str6);
        clsHDMFLibSL.GetPartModelAndMeshImageForSlide(directoryInfo.FullName, p_dicView, p_dicThickness, ref p_dicData);
        clsHDMFLibSL.GetTimeToReachEjectionTemperaturePlot(directoryInfo.FullName, dataRowList2, p_dicView, allLayers, ref p_dicData);
        clsHDMFLibSL.GetPressureAtEndOfFillForSlide("", directoryInfo.FullName, p_dicView, ref p_dicData, allLayers);
        double num = clsReportUtill.ConvertToDouble(p_dicData["InjPressure[Log]"]);
        double p_dblCavityPressure = clsReportUtill.ConvertToDouble(p_dicData["PressureXY[Plot]"]);
        string pressureTableToImage = clsSLData.GetPressureTableToImage(directoryInfo.FullName, num - p_dblCavityPressure, p_dblCavityPressure);
        if (pressureTableToImage != "")
          p_dicData.Add("PressureTable[IMG]", pressureTableToImage);
        if (p_dicValue.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == "InjCond")))
        {
          if (p_dicValue["InjCond"] == "1")
            clsInjCondTable.ExportInjCondTable(p_drStudy, dataRowList2, p_dicValue, p_dicData);
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsSLData]GetAnalysisData):" + ex.Message));
      }
      return p_dicData;
    }

    private static string GetPressureTableToImage(
      string p_strReportPath,
      double p_dblInjPressure,
      double p_dblCavityPressure)
    {
      uint lpdwProcessId = 0;
      string pressureTableToImage = "";
      string Filename = System.Windows.Forms.Application.StartupPath + "\\Config\\SLReport\\Pressure table.xlsx";
      string str = p_strReportPath + "\\PressureTable.jpg";
      try
      {
        // ISSUE: variable of a compiler-generated type
        Microsoft.Office.Interop.Excel.Application instance = (Microsoft.Office.Interop.Excel.Application) Activator.CreateInstance(Marshal.GetTypeFromCLSID(new Guid("00024500-0000-0000-C000-000000000046")));
        // ISSUE: reference to a compiler-generated method
        // ISSUE: variable of a compiler-generated type
        Workbook workbook = instance.Workbooks.Open(Filename, System.Type.Missing, System.Type.Missing, System.Type.Missing, System.Type.Missing, System.Type.Missing, System.Type.Missing, System.Type.Missing, System.Type.Missing, System.Type.Missing, System.Type.Missing, System.Type.Missing, System.Type.Missing, System.Type.Missing, System.Type.Missing);
        // ISSUE: reference to a compiler-generated field
        if (clsSLData.\u003C\u003Eo__2.\u003C\u003Ep__0 == null)
        {
          // ISSUE: reference to a compiler-generated field
          clsSLData.\u003C\u003Eo__2.\u003C\u003Ep__0 = CallSite<Func<CallSite, object, Worksheet>>.Create(Binder.Convert(CSharpBinderFlags.ConvertExplicit, typeof (Worksheet), typeof (clsSLData)));
        }
        // ISSUE: reference to a compiler-generated field
        // ISSUE: reference to a compiler-generated field
        // ISSUE: variable of a compiler-generated type
        Worksheet worksheet = clsSLData.\u003C\u003Eo__2.\u003C\u003Ep__0.Target((CallSite) clsSLData.\u003C\u003Eo__2.\u003C\u003Ep__0, workbook.Sheets[(object) 1]);
        worksheet.Cells[(object) 6, (object) 6] = (object) p_dblInjPressure;
        worksheet.Cells[(object) 7, (object) 6] = (object) p_dblCavityPressure;
        // ISSUE: variable of a compiler-generated type
        Shape shape = worksheet.Shapes.Cast<Shape>().Where<Shape>((System.Func<Shape, bool>) (Temp => Temp.Type == MsoShapeType.msoGroup && Temp.Name == "Group 1")).FirstOrDefault<Shape>();
        if (shape != null)
        {
          // ISSUE: reference to a compiler-generated method
          shape.CopyPicture((object) XlPictureAppearance.xlScreen, (object) XlCopyPictureFormat.xlBitmap);
          if (Clipboard.ContainsImage())
            Clipboard.GetImage().Save(str);
        }
        if (File.Exists(str))
          pressureTableToImage = str;
        // ISSUE: reference to a compiler-generated method
        workbook.Close((object) false, System.Type.Missing, System.Type.Missing);
        int windowThreadProcessId = (int) clsReportUtill.GetWindowThreadProcessId(new IntPtr(instance.Hwnd), out lpdwProcessId);
        // ISSUE: reference to a compiler-generated method
        instance.Quit();
        clsReportUtill.ReleaseComObject((object) instance);
        if (lpdwProcessId != 0U)
        {
          Process processById = Process.GetProcessById((int) lpdwProcessId);
          processById.CloseMainWindow();
          processById.Refresh();
          processById.Kill();
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsAutoReport]GetPressureTableToImage):" + ex.Message));
      }
      return pressureTableToImage;
    }
  }
}
