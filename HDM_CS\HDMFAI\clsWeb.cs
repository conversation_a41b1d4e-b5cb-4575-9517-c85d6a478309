﻿// Decompiled with JetBrains decompiler
// Type: HDMFAI.clsWeb
// Assembly: HDMFAI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: A66A47CE-D63B-4ACB-B9A4-A4F118252C4E
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFAI.dll

using HDLog4Net;
using Newtonsoft.Json.Linq;
using System;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Web;

namespace HDMFAI
{
  internal class clsWeb
  {
    internal static string SendDataToWeb(string p_strJsonData, string p_strSendURL)
    {
      string str = string.Empty;
      string empty = string.Empty;
      string web = string.Empty;
      try
      {
        if (clsAIDefine.g_fiAICfg != null)
          str = clsUtill.ReadINI("Main", "URL", clsAIDefine.g_fiAICfg.FullName);
        if (string.IsNullOrEmpty(str))
          str = "http://api.hdsol.co.kr";
        JObject jobject = JObject.Parse(new HttpClient()
        {
          Timeout = TimeSpan.FromSeconds(20.0)
        }.PostAsync(str + p_strSendURL, (HttpContent) new StringContent(p_strJsonData, Encoding.UTF8, "application/json")).Result.Content.ReadAsStringAsync().Result);
        if (jobject["code"].ToString() != "1")
          web = "Send Data Error : " + jobject["msg"].ToString();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsWeb]SendDataToWeb):" + ex.Message + "/" + (object) ex.InnerException));
        web = "Error: " + ex.Message;
      }
      return web;
    }

    internal static string ReceiveDataFromWeb(
      string p_strReceiveURL,
      string p_strStudy,
      ref JObject p_joAIData)
    {
      int num = 10000;
      string p_strValue = string.Empty;
      string str = string.Empty;
      string empty = string.Empty;
      string dataFromWeb = string.Empty;
      try
      {
        if (clsAIDefine.g_fiAICfg != null)
        {
          str = clsUtill.ReadINI("Main", "URL", clsAIDefine.g_fiAICfg.FullName);
          p_strValue = clsUtill.ReadINI("Timeout", "Receive", clsAIDefine.g_fiAICfg.FullName);
        }
        if (!string.IsNullOrEmpty(p_strValue))
          num = clsUtill.ConvertToInt(p_strValue);
        if (string.IsNullOrEmpty(str))
          str = "http://api.hdsol.co.kr";
        p_strStudy = HttpUtility.UrlEncode(p_strStudy);
        WebRequest webRequest = WebRequest.Create(str + p_strReceiveURL + "?study=" + p_strStudy);
        webRequest.Method = "POST";
        webRequest.Timeout = num;
        webRequest.ContentType = "application/json";
        using (WebResponse response = webRequest.GetResponse())
        {
          if (((HttpWebResponse) response).StatusCode != HttpStatusCode.OK)
          {
            dataFromWeb = "REST-RESPONSE: X";
          }
          else
          {
            using (Stream responseStream = response.GetResponseStream())
            {
              using (StreamReader streamReader = new StreamReader(responseStream))
              {
                JObject jobject = JObject.Parse(streamReader.ReadToEnd());
                JValue jvalue1 = (JValue) jobject["code"];
                JValue jvalue2 = (JValue) jobject["status"];
                if (jvalue1.ToString() != "1" || jvalue2.ToString().ToLower() == "failed")
                {
                  dataFromWeb = jobject["msg"].ToString();
                  return dataFromWeb;
                }
                if (jvalue2.ToString().ToLower() == "waiting")
                  return dataFromWeb;
                p_joAIData = jobject;
              }
            }
          }
        }
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([clsWeb]ReceiveDataFromWeb):" + ex.Message));
        dataFromWeb = "Exception: " + ex.Message;
      }
      if (!string.IsNullOrEmpty(dataFromWeb))
        HDLog.Instance().Error((object) ("Error ([clsWeb]ReceiveDataFromWeb):" + dataFromWeb));
      return dataFromWeb;
    }
  }
}
