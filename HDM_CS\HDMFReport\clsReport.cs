﻿// Decompiled with JetBrains decompiler
// Type: HDMFReport.clsReport
// Assembly: HDMFReport, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 354DA238-01AA-45A5-AC75-3C7078691488
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFReport.dll

using HDMoldFlowLibrary;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Windows.Forms;

namespace HDMFReport
{
  public class clsReport
  {
    public static void ShowReportForm(
      Form p_formParent,
      DataRow p_drStudy,
      string p_strLangType,
      clsHDMFLibDefine.Company p_enumCompany)
    {
      clsHDMFLibDefine.Company p_enumCompany1 = p_enumCompany;
      Dictionary<string, string> dictionary1 = new Dictionary<string, string>();
      if (p_enumCompany1 != clsHDMFLibDefine.Company.HDSolutions)
      {
        int num = (int) new frmReportSetting()
        {
          m_enumCompany = p_enumCompany,
          m_strStudyName = p_drStudy["Name"].ToString()
        }.ShowDialog();
        p_enumCompany1 = clsReportDefine.g_enumSelectedCompany;
      }
      frmBase frmBase = p_enumCompany1 == clsHDMFLibDefine.Company.Hyundai ? (frmBase) new frmHyundai() : (p_enumCompany1 == clsHDMFLibDefine.Company.SL ? (frmBase) new frmSL() : (frmBase) new frmHDSolutions());
      DataTable reportViewDataFromIni = clsReportData.GetReportViewDataFromIni(p_enumCompany1, p_strLangType);
      Dictionary<string, string> dictionary2 = p_enumCompany1 != clsHDMFLibDefine.Company.SL ? clsReportData.GetReportData(p_drStudy) : clsReportData.GetReportDataSL(p_drStudy);
      frmBase.m_drStudy = p_drStudy;
      frmBase.m_dtReportView = reportViewDataFromIni;
      frmBase.m_enumCompany = p_enumCompany1;
      frmBase.m_dicReport = dictionary2;
      frmBase.Icon = p_formParent.Icon;
      int num1 = (int) frmBase.ShowDialog((IWin32Window) p_formParent);
    }

    public static void ExportReport(
      List<DataRow> p_lst_drStudy,
      string p_strLangType,
      clsHDMFLibDefine.Company p_enumCompany)
    {
      clsBase clsBase1 = (clsBase) new clsHDSolutions();
      clsHDMFLibDefine.Company p_enumCompany1 = p_enumCompany;
      for (int index = 0; index < p_lst_drStudy.Count; ++index)
      {
        DataRow p_drStudy = p_lst_drStudy[index];
        FileInfo fileInfo = new FileInfo(clsReportDefine.g_diTmpReport.FullName + "\\" + p_drStudy["Name"].ToString() + "\\Report.ini");
        if (fileInfo.Exists)
          p_enumCompany1 = clsReportUtill.ConvertToEnumCompany(clsReportUtill.ReadINI("Template", "Company", fileInfo.FullName));
        clsBase clsBase2;
        switch (p_enumCompany1)
        {
          case clsHDMFLibDefine.Company.Hyundai:
            clsBase2 = (clsBase) new clsHyundai();
            break;
          case clsHDMFLibDefine.Company.Kumnung:
            clsBase2 = (clsBase) new clsKumnung();
            break;
          case clsHDMFLibDefine.Company.Changsung:
            clsBase2 = (clsBase) new clsChangsung();
            break;
          case clsHDMFLibDefine.Company.SL:
            clsBase2 = (clsBase) new clsSL();
            break;
          default:
            clsBase2 = (clsBase) new clsHDSolutions();
            break;
        }
        if (!clsHDMFLib.OpenStudy(p_drStudy["Name"].ToString()))
          break;
        clsHDMFLib.SetRemoveUnused();
        clsBase2.ExportReport(p_drStudy, p_strLangType, p_enumCompany1);
        clsHDMFLib.CloseStudy(false);
      }
    }

    public static void ExportReportForComparison(List<DataRow> p_lst_drStudy, string p_strLangType)
    {
      clsBase clsBase = (clsBase) new clsHDSolutions_CMPR();
      clsHDMFLibDefine.Company enumCompany = clsReportUtill.ConvertToEnumCompany(clsReportUtill.ReadINI("Template", "Company", new FileInfo(clsReportDefine.g_diTmpReport.FullName + "\\" + p_lst_drStudy[0]["Name"].ToString() + "\\Report.ini").FullName));
      (enumCompany != clsHDMFLibDefine.Company.Hyundai ? (clsBase) new clsHDSolutions_CMPR() : (clsBase) new clsHyundai_CMPR()).ExportCMPRReport(p_lst_drStudy, p_strLangType, enumCompany);
    }
  }
}
