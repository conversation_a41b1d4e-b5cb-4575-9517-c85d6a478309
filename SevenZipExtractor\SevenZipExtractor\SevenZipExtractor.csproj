﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFrameworks>net45;netstandard2.0</TargetFrameworks>
    <AssemblyTitle>SevenZipWrapper</AssemblyTitle>
    <Product>SevenZipWrapper</Product>
    <Description>C# wrapper for 7z.dll (included)</Description>
    <Copyright>Copyright 2025</Copyright>
    <Version>1.0.19</Version>
    <OutputPath>bin\$(Configuration)\</OutputPath>
    <Authors><PERSON>, <PERSON>, @matorthee<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON></Authors>
    <PackageId>SevenZipExtractor</PackageId>
    <PackageProjectUrl>https://github.com/adoconnection/SevenZipExtractor</PackageProjectUrl>
    <PackageTags>7Zip APM Arj BZip2 Cab Chm Compound Cpio CramFS Deb Dll Dmg Exe Fat Flv GZip Hfs Iso Lzh Lzma Lzma86 Mach-O Mbr Mub Nsis Ntfs Ppmd Rar Rar5 Rpm Split SquashFS Swf Swfc Tar TE Udf UEFIc UEFIs Vhd Wim Xar XZ Z Zip</PackageTags>
    <PackageVersion>1.0.19</PackageVersion>
  </PropertyGroup>

  <ItemGroup>
    <None Include="$(MSBuildThisFileDirectory)x64\7z.dll">
      <PackagePath>build\x64\</PackagePath>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>

      <Pack>true</Pack>
    </None>
    <None Include="$(MSBuildThisFileDirectory)x86\7z.dll">
      <PackagePath>build\x86\</PackagePath>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Pack>true</Pack>
    </None>

    <None Include="SevenZipExtractor.targets" Pack="true" PackagePath="build\">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>