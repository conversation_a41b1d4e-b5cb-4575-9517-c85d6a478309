﻿// Decompiled with JetBrains decompiler
// Type: Moldflow_Esay_Tool_Kit.My.MySettings
// Assembly: Moldflow Esay Tool Kit, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: EE4F1197-F36A-4D67-AE33-DA541A327629
// Assembly location: C:\Users\<USER>\Documents\20210315_Moldflow Esay Tool Kit_V3.exe

using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Configuration;
using System.Diagnostics;
using System.Runtime.CompilerServices;

#nullable disable
namespace Moldflow_Esay_Tool_Kit.My
{
  [CompilerGenerated]
  [GeneratedCode("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "12.0.0.0")]
  [EditorBrowsable(EditorBrowsableState.Advanced)]
  internal sealed class MySettings : ApplicationSettingsBase
  {
    private static MySettings defaultInstance = (MySettings) SettingsBase.Synchronized((SettingsBase) new MySettings());

    [DebuggerNonUserCode]
    public MySettings()
    {
    }

    public static MySettings Default
    {
      get
      {
        MySettings defaultInstance = MySettings.defaultInstance;
        return defaultInstance;
      }
    }
  }
}
