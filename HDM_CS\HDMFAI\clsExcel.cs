﻿// Decompiled with JetBrains decompiler
// Type: HDMFAI.clsExcel
// Assembly: HDMFAI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: A66A47CE-D63B-4ACB-B9A4-A4F118252C4E
// Assembly location: F:\Moldflow\Moldflow HDFlow\HDSolutions\HDMFlow\HDMFAI.dll

using HDLog4Net;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;

namespace HDMFAI
{
  internal class clsExcel
  {
    internal static void ExportAIData(
      string p_strStudyName,
      Dictionary<string, string> p_dicSummary,
      FileInfo p_fiExport)
    {
      bool flag = false;
      uint lpdwProcessId = 0;
      DataTable dtExcel = (DataTable) null;
      List<string> stringList = new List<string>();
      try
      {
        FileInfo fileInfo1 = new FileInfo(clsAIDefine.g_diTemplate.ToString() + "\\AIData.hde");
        if (!fileInfo1.Exists)
          return;
        FileInfo fileInfo2 = new FileInfo(clsAIDefine.g_diTemplate.ToString() + "\\AIData.xlsx");
        if (!fileInfo2.Exists)
        {
          fileInfo1.CopyTo(fileInfo2.FullName);
          fileInfo2.Refresh();
          if (!fileInfo2.Exists)
            return;
        }
        if (!p_fiExport.Directory.Exists)
          p_fiExport.Directory.Create();
        p_fiExport.Refresh();
        if (p_fiExport.Exists)
        {
          if (!p_fiExport.Extension.Contains("xl"))
          {
            fileInfo2 = new FileInfo(p_fiExport.FullName.Replace(p_fiExport.Extension, ".xlsx"));
            p_fiExport.CopyTo(fileInfo2.FullName);
          }
          else
            fileInfo2 = new FileInfo(p_fiExport.FullName);
        }
        fileInfo2.Refresh();
        if (fileInfo2.Exists)
        {
          // ISSUE: variable of a compiler-generated type
          Application instance = (Application) Activator.CreateInstance(Marshal.GetTypeFromCLSID(new Guid("00024500-0000-0000-C000-000000000046")));
          instance.Visible = false;
          // ISSUE: variable of a compiler-generated type
          Application p_obj = instance;
          try
          {
            // ISSUE: reference to a compiler-generated method
            p_obj.Workbooks.Open(fileInfo2.FullName, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing);
            // ISSUE: variable of a compiler-generated type
            Worksheet p_wSheet = p_obj.Sheets.Cast<Worksheet>().Where<Worksheet>((System.Func<Worksheet, bool>) (Temp => Temp.Name == "AIData")).FirstOrDefault<Worksheet>();
            dtExcel = clsUtill.GetWorkSheetDataForAI(p_wSheet);
          }
          catch (Exception ex)
          {
            HDLog.Instance().Error((object) ("Exception([HDMFSummary][clsSummary]ExportSummaryForAI(1)):" + ex.Message));
          }
          int windowThreadProcessId = (int) clsUtill.GetWindowThreadProcessId(new IntPtr(p_obj.Hwnd), ref lpdwProcessId);
          // ISSUE: reference to a compiler-generated method
          p_obj.Quit();
          clsUtill.ReleaseComObject((object) p_obj);
          if (lpdwProcessId != 0U)
          {
            Process processById = Process.GetProcessById((int) lpdwProcessId);
            processById.CloseMainWindow();
            processById.Refresh();
            processById.Kill();
          }
        }
        // ISSUE: variable of a compiler-generated type
        Application instance1 = (Application) Activator.CreateInstance(Marshal.GetTypeFromCLSID(new Guid("00024500-0000-0000-C000-000000000046")));
        instance1.Visible = false;
        // ISSUE: variable of a compiler-generated type
        Application p_obj1 = instance1;
        try
        {
          // ISSUE: reference to a compiler-generated method
          // ISSUE: variable of a compiler-generated type
          Workbook workbook = p_obj1.Workbooks.Open(fileInfo2.FullName, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing);
          // ISSUE: variable of a compiler-generated type
          Worksheet p_wSheet = workbook.Sheets.Cast<Worksheet>().Where<Worksheet>((System.Func<Worksheet, bool>) (Temp => Temp.Name == "AIData")).FirstOrDefault<Worksheet>();
          if (p_wSheet != null)
          {
            int RowIndex = dtExcel.Rows.Count + 2;
            DataRow dataRow = dtExcel.Rows.Add();
            string strKey = string.Empty;
            for (int i = 0; i < dtExcel.Columns.Count; i++)
            {
              strKey = dtExcel.Columns[i].ColumnName;
              if (strKey.Contains("△"))
                strKey = strKey.Replace("△", "Diff");
              if (p_dicSummary.Any<KeyValuePair<string, string>>((System.Func<KeyValuePair<string, string>, bool>) (Temp => Temp.Key == strKey && !dtExcel.Columns[i].ColumnName.Contains("[mg]"))))
              {
                int columnIndex = clsUtill.GetColumnIndex(p_wSheet, dtExcel.Columns[i].ColumnName);
                if (columnIndex >= 0)
                {
                  p_wSheet.Cells[(object) RowIndex, (object) columnIndex] = (object) p_dicSummary[strKey];
                  dataRow[dtExcel.Columns[i].ColumnName] = (object) p_dicSummary[strKey];
                  if (dtExcel.Columns[i].ColumnName.Contains("Packing_Holding") || dtExcel.Columns[i].ColumnName.Contains("component"))
                    p_wSheet.Cells[(object) RowIndex, (object) (columnIndex + 2)] = (object) p_dicSummary["merge_" + strKey];
                }
              }
            }
            flag = true;
          }
          if (flag)
          {
            if (p_fiExport.Extension == ".xlsm")
            {
              if (fileInfo2.FullName != fileInfo1.FullName.Replace(fileInfo1.Extension, ".xlsm"))
              {
                // ISSUE: reference to a compiler-generated method
                workbook.Save();
              }
              else
              {
                // ISSUE: reference to a compiler-generated method
                workbook.SaveAs((object) p_fiExport.FullName, (object) XlFileFormat.xlOpenXMLWorkbookMacroEnabled, Type.Missing, Type.Missing, Type.Missing, Type.Missing, ConflictResolution: Type.Missing, AddToMru: Type.Missing, TextCodepage: Type.Missing, TextVisualLayout: Type.Missing, Local: Type.Missing);
              }
            }
            else if (fileInfo2.FullName != fileInfo1.FullName.Replace(fileInfo1.Extension, ".xlsx"))
            {
              // ISSUE: reference to a compiler-generated method
              workbook.Save();
            }
            else
            {
              // ISSUE: reference to a compiler-generated method
              workbook.SaveAs((object) p_fiExport.FullName, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, ConflictResolution: Type.Missing, AddToMru: Type.Missing, TextCodepage: Type.Missing, TextVisualLayout: Type.Missing, Local: Type.Missing);
            }
          }
          // ISSUE: reference to a compiler-generated method
          workbook.Close(Type.Missing, Type.Missing, Type.Missing);
        }
        catch (Exception ex)
        {
          HDLog.Instance().Error((object) ("Exception([HDMFSummary][clsSummary_AI]ExportSummaryForAI(2)):" + ex.Message));
        }
        int windowThreadProcessId1 = (int) clsUtill.GetWindowThreadProcessId(new IntPtr(p_obj1.Hwnd), ref lpdwProcessId);
        // ISSUE: reference to a compiler-generated method
        p_obj1.Quit();
        clsUtill.ReleaseComObject((object) p_obj1);
        if (lpdwProcessId != 0U)
        {
          Process processById = Process.GetProcessById((int) lpdwProcessId);
          processById.CloseMainWindow();
          processById.Refresh();
          processById.Kill();
        }
        if (p_fiExport.Extension.Contains(".xl"))
          return;
        if (flag && fileInfo2.FullName != fileInfo1.FullName.Replace(fileInfo1.Extension, ".xlsx"))
        {
          if (p_fiExport.Exists)
            p_fiExport.Delete();
          File.Move(fileInfo2.FullName, p_fiExport.FullName);
        }
        if (!fileInfo2.Exists)
          return;
        fileInfo2.Delete();
      }
      catch (Exception ex)
      {
        HDLog.Instance().Error((object) ("Exception([HDMFSummary][clsSummary_AI]ExportSummary):" + ex.Message));
      }
    }
  }
}
